"use client"

/**
 * 🇨🇳 风口云平台 - 账号信息页面
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 显示所有可用的登录账号信息
 * - 包括总部管理员账号和工厂管理员账号
 * - 提供快速登录链接
 */

import { useEffect, useState } from "react"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button  } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LocalDB } from "@/lib/storage/local-db"
import { Building2, Factory, User, Eye, EyeOff, Copy, ExternalLink } from "lucide-react"

interface AccountInfo {
  id: string
  username: string
  password: string
  name: string
  role: string
  type: 'admin' | 'factory'
  factoryName?: string
  factoryId?: string
  isActive?: boolean
}

export default function AccountsPage() {
  const [accounts, setAccounts] = useState<AccountInfo[]>([])
  const [showPasswords, setShowPasswords] = useState(false)

  useEffect(() => {
    loadAccounts()
  }, [])

  const loadAccounts = () => {
    try {
      const allAccounts: AccountInfo[] = []

      // 获取总部管理员账号
      const admins = LocalDB.admins.getAll()
      admins.forEach(admin => {
        allAccounts.push({
          id: admin.id,
          username: admin.username,
          password: admin.password,
          name: admin.name,
          role: '总部管理员',
          type: 'admin'
        })
      })

      // 获取工厂管理员账号
      const factoryUsers = LocalDB.factoryUsers.getAll()
      const factories = LocalDB.factories.getAll()
      
      factoryUsers.forEach(user => {
        const factory = factories.find(f => f.id === user.factoryId)
        allAccounts.push({
          id: user.id,
          username: user.username,
          password: user.password,
          name: user.name,
          role: user.role === 'owner' ? '工厂管理员' : '工厂员工',
          type: 'factory',
          factoryName: factory?.name || '未知工厂',
          factoryId: user.factoryId,
          isActive: user.isActive
        })
      })

      setAccounts(allAccounts)
    } catch (error) {
      console.error('加载账号信息失败:', error)
    }
  }

  const copyToClipboard = async (text: string) => {
    const { copyToClipboard: copy } = await import('@/utils/clipboard')
    await copy(text)
  }

  const getLoginUrl = (account: AccountInfo) => {
    return account.type === 'admin' ? '/admin/login' : '/factory/login'
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case '总部管理员':
        return 'bg-red-100 text-red-800'
      case '工厂管理员':
        return 'bg-blue-100 text-blue-800'
      case '工厂员工':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const activeAccounts = accounts.filter(acc => acc.type === 'admin' || acc.isActive !== false)
  const inactiveAccounts = accounts.filter(acc => acc.type === 'factory' && acc.isActive === false)

  return (
    <div className="container mx-auto p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">🔐 系统账号信息</h1>
        <p className="text-gray-600 mb-4">
          以下是系统中所有可用的登录账号信息，包括总部管理员和工厂管理员账号。
        </p>
        
        <div className="flex gap-4 mb-6">
          <Button
            variant="outline"
            onClick={() => setShowPasswords(!showPasswords)}
          >
            {showPasswords ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
            {showPasswords ? '隐藏密码' : '显示密码'}
          </Button>
          
          <Button variant="outline" onClick={loadAccounts}>
            刷新账号列表
          </Button>
        </div>
      </div>

      {/* 活跃账号 */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">✅ 可用账号 ({activeAccounts.length})</h2>
        
        <div className="grid gap-4">
          {activeAccounts.map((account) => (
            <Card key={account.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${account.type === 'admin' ? 'bg-red-100' : 'bg-blue-100'}`}>
                      {account.type === 'admin' ? (
                        <Building2 className={`h-5 w-5 ${account.type === 'admin' ? 'text-red-600' : 'text-blue-600'}`} />
                      ) : (
                        <Factory className="h-5 w-5 text-blue-600" />
                      )}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{account.name}</CardTitle>
                      <p className="text-sm text-gray-600">
                        {account.factoryName && `${account.factoryName} - `}
                        用户名: {account.username}
                      </p>
                    </div>
                  </div>
                  <Badge className={getRoleColor(account.role)}>
                    {account.role}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">用户名</label>
                      <div className="flex items-center space-x-2">
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                          {account.username}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(account.username)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-600">密码</label>
                      <div className="flex items-center space-x-2">
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                          {showPasswords ? account.password : '••••••••'}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(account.password)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 pt-2">
                    <Link href={getLoginUrl(account)}>
                      <Button size="sm" className="flex items-center space-x-1">
                        <ExternalLink className="h-3 w-3" />
                        <span>前往登录</span>
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* 非活跃账号 */}
      {inactiveAccounts.length > 0 && (
        <div>
          <h2 className="text-2xl font-semibold mb-4 text-gray-500">❌ 已停用账号 ({inactiveAccounts.length})</h2>
          
          <div className="grid gap-4">
            {inactiveAccounts.map((account) => (
              <Card key={account.id} className="opacity-60">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <User className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-600">{account.name}</p>
                        <p className="text-sm text-gray-500">
                          {account.factoryName} - {account.username}
                        </p>
                      </div>
                    </div>
                    <Badge variant="secondary">已停用</Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* 返回首页 */}
      <div className="text-center mt-8">
        <Link href="/">
          <Button variant="outline">
            <Building2 className="h-4 w-4 mr-2" />
            返回首页
          </Button>
        </Link>
      </div>
    </div>
  )
}
