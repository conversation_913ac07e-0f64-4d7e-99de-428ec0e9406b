<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 项目名称识别修复验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover { background: #1976D2; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .button.success { background: #4CAF50; }
        .button.success:hover { background: #45a049; }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #4CAF50; background: #e8f5e8; }
        .error { border-color: #f44336; background: #ffe8e8; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        .test-input {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 项目名称识别修复验证</h1>
        <p>验证修复后的DeepSeek API能否正确识别项目名称、客户信息和备注</p>
        
        <div style="background: #e8f5e8; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #4CAF50;">
            <strong>✅ 已修复的问题：</strong><br>
            • 改进Prompt以包含项目名称和客户信息<br>
            • 修复Token设置：使用配置的800而不是硬编码的1500<br>
            • 修复性能监控时间计算错误<br>
            • 增强房间名称和备注信息提取
        </div>
        
        <div>
            <label for="testInput"><strong>测试文本：</strong></label>
            <textarea id="testInput" class="test-input" placeholder="输入包含项目名称的风口订单文本...">项目：万科翡翠公园二期
客户：张总
联系方式：13800138000

一楼大厅：
回风口 1200×300 白色 1个 (主要通风)
出风口 2520×110 白色 1个 (装饰性)

二楼办公室：
回风口 1500×300 白色 1个
出风口 3610×110 白色 1个

备注：所有风口需要静音处理，交期2周</textarea>
        </div>
        
        <button class="button success" onclick="testProjectNameRecognition()">🧪 测试项目名称识别</button>
        <button class="button" onclick="testPerformance()">⚡ 测试性能改进</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_KEY = 'sk-c9047196d51d4f6c99dc94f9fbef602c';
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}] ${title}</strong>\n${content}`;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function testProjectNameRecognition() {
            const testText = document.getElementById('testInput').value.trim();
            if (!testText) {
                addResult('❌ 错误', '请输入测试文本', 'error');
                return;
            }
            
            addResult('🧪 项目名称识别测试', '使用改进后的Prompt测试...', 'info');
            
            const improvedPrompt = `解析风口订单为JSON格式。

输入文本：
${testText}

解析规则：
- 提取项目名称和客户信息
- 宽度≤254mm = 出风口 (systemType: "double_white_outlet")
- 宽度≥255mm = 回风口 (systemType: "white_return")
- 数字<100当作厘米，需要×10转换为毫米
- 提取房间名称（如：大厅、办公室、2103房号等）

返回格式（必须是有效的JSON）：
{
  "projects": [{
    "projectName": "项目名称",
    "clientInfo": "客户信息",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间名称",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": "备注信息"
          }
        ]
      }]
    }]
  }]
}

请确保返回完整有效的JSON，不要添加任何解释文字。`;

            const startTime = performance.now();
            
            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [{ role: 'user', content: improvedPrompt }],
                        max_tokens: 800,
                        temperature: 0.2,
                        stream: false
                    }),
                    signal: AbortSignal.timeout(30000)
                });
                
                const duration = performance.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0]?.message?.content;
                    
                    addResult(
                        '✅ API调用成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
Token使用: ${data.usage?.total_tokens || 'N/A'}
响应长度: ${content?.length || 0} 字符`,
                        'success'
                    );
                    
                    // 解析JSON并验证项目信息
                    try {
                        let jsonStr = content.trim();
                        jsonStr = jsonStr.replace(/^```json\s*/i, '');
                        jsonStr = jsonStr.replace(/^```\s*/i, '');
                        jsonStr = jsonStr.replace(/\s*```\s*$/g, '');
                        
                        const firstBrace = jsonStr.indexOf('{');
                        const lastBrace = jsonStr.lastIndexOf('}');
                        
                        if (firstBrace !== -1 && lastBrace !== -1) {
                            jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
                        }
                        
                        const parsed = JSON.parse(jsonStr);
                        const project = parsed.projects?.[0];
                        
                        if (project) {
                            const projectName = project.projectName || '未识别';
                            const clientInfo = project.clientInfo || '未识别';
                            const roomCount = project.floors?.[0]?.rooms?.length || 0;
                            const ventCount = project.floors?.[0]?.rooms?.reduce((total, room) => 
                                total + (room.vents?.length || 0), 0) || 0;
                            
                            // 检查是否有备注信息
                            const hasNotes = project.floors?.[0]?.rooms?.some(room => 
                                room.vents?.some(vent => vent.notes && vent.notes.trim() !== '')) || false;
                            
                            addResult(
                                '🏢 项目信息提取结果',
                                `项目名称: ${projectName} ${projectName !== '未识别' ? '✅' : '❌'}
客户信息: ${clientInfo} ${clientInfo !== '未识别' ? '✅' : '❌'}
房间数量: ${roomCount}
风口数量: ${ventCount}
备注信息: ${hasNotes ? '✅ 已提取' : '❌ 未提取'}

详细数据:
${JSON.stringify(parsed, null, 2)}`,
                                (projectName !== '未识别' && clientInfo !== '未识别') ? 'success' : 'warning'
                            );
                        } else {
                            addResult(
                                '❌ 项目信息提取失败',
                                '无法从响应中提取项目信息',
                                'error'
                            );
                        }
                        
                    } catch (parseError) {
                        addResult(
                            '❌ JSON解析失败',
                            `解析错误: ${parseError.message}
原始响应: ${content}`,
                            'error'
                        );
                    }
                    
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ API调用失败',
                        `状态码: ${response.status}
响应时间: ${duration.toFixed(2)}ms
错误信息: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const duration = performance.now() - startTime;
                addResult(
                    '❌ API调用异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        async function testPerformance() {
            addResult('⚡ 性能测试', '测试修复后的响应速度...', 'info');
            
            const simpleText = `项目：性能测试
一楼：
回风口 1200×300 白色 1个`;

            const startTime = performance.now();
            
            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [{ role: 'user', content: `解析: ${simpleText}` }],
                        max_tokens: 200,
                        temperature: 0.2,
                        stream: false
                    }),
                    signal: AbortSignal.timeout(15000)
                });
                
                const duration = performance.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    
                    let performanceRating = '';
                    if (duration < 5000) {
                        performanceRating = '🟢 优秀 (<5秒)';
                    } else if (duration < 15000) {
                        performanceRating = '🟡 良好 (5-15秒)';
                    } else if (duration < 30000) {
                        performanceRating = '🟠 一般 (15-30秒)';
                    } else {
                        performanceRating = '🔴 较慢 (>30秒)';
                    }
                    
                    addResult(
                        '⚡ 性能测试结果',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
性能评级: ${performanceRating}
Token使用: ${data.usage?.total_tokens || 'N/A'}
改进效果: ${duration < 20000 ? '✅ 显著改善' : '⚠️ 仍需优化'}`,
                        duration < 20000 ? 'success' : 'warning'
                    );
                } else {
                    addResult(
                        '❌ 性能测试失败',
                        `状态码: ${response.status}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const duration = performance.now() - startTime;
                addResult(
                    '❌ 性能测试异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult(
                '📋 项目名称识别修复验证',
                `🎯 目标: 验证项目名称、客户信息、备注识别
🔧 修复内容:
  • 改进Prompt包含项目信息提取
  • 修复Token设置 (1500 → 800)
  • 修复性能监控计算
  • 增强房间名称识别

点击"🧪 测试项目名称识别"开始验证！`,
                'info'
            );
        };
    </script>
</body>
</html>
