#!/usr/bin/env node

/**
 * 🇨🇳 风口云平台 - 初始化轮播图片脚本
 * 为本地开发环境创建默认的轮播图片数据
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 创建一个简单的1x1像素的透明PNG图片的Base64数据
const createPlaceholderImage = (color = 'blue', text = '示例图片') => {
  // 这是一个简单的SVG图片，转换为Base64
  const svg = `
    <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${color}" opacity="0.3"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="24" 
            text-anchor="middle" dominant-baseline="middle" fill="white">
        ${text}
      </text>
    </svg>
  `.trim();
  
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
};

async function initCarouselImages() {
  console.log('🖼️ 开始初始化轮播图片...\n');

  try {
    // 1. 创建产品展示图片
    console.log('📦 创建产品展示图片...');
    
    const productImages = [
      {
        name: '风口产品展示1.svg',
        category: 'product',
        imageData: createPlaceholderImage('#2563eb', '风口产品展示'),
        mimeType: 'image/svg+xml',
        size: 1024,
        width: 800,
        height: 400
      },
      {
        name: '风口产品展示2.svg',
        category: 'product',
        imageData: createPlaceholderImage('#059669', '优质风口产品'),
        mimeType: 'image/svg+xml',
        size: 1024,
        width: 800,
        height: 400
      },
      {
        name: '风口产品展示3.svg',
        category: 'product',
        imageData: createPlaceholderImage('#dc2626', '专业风口制造'),
        mimeType: 'image/svg+xml',
        size: 1024,
        width: 800,
        height: 400
      }
    ];

    for (const imageData of productImages) {
      // 检查是否已存在
      const existing = await prisma.carouselImage.findFirst({
        where: { name: imageData.name }
      });

      if (!existing) {
        await prisma.carouselImage.create({
          data: imageData
        });
      }
    }
    console.log('✅ 产品展示图片创建成功');

    // 2. 创建生产流程图片
    console.log('🏭 创建生产流程图片...');
    
    const processImages = [
      {
        name: '生产流程1.svg',
        category: 'process',
        imageData: createPlaceholderImage('#7c3aed', '设计阶段'),
        mimeType: 'image/svg+xml',
        size: 1024,
        width: 800,
        height: 400
      },
      {
        name: '生产流程2.svg',
        category: 'process',
        imageData: createPlaceholderImage('#ea580c', '生产制造'),
        mimeType: 'image/svg+xml',
        size: 1024,
        width: 800,
        height: 400
      },
      {
        name: '生产流程3.svg',
        category: 'process',
        imageData: createPlaceholderImage('#0891b2', '质量检测'),
        mimeType: 'image/svg+xml',
        size: 1024,
        width: 800,
        height: 400
      }
    ];

    for (const imageData of processImages) {
      // 检查是否已存在
      const existing = await prisma.carouselImage.findFirst({
        where: { name: imageData.name }
      });

      if (!existing) {
        await prisma.carouselImage.create({
          data: imageData
        });
      }
    }
    console.log('✅ 生产流程图片创建成功');

    // 3. 创建其他类型图片
    console.log('🎨 创建其他类型图片...');
    
    const otherImages = [
      {
        name: '公司介绍.svg',
        category: 'other',
        imageData: createPlaceholderImage('#be185d', '风口云平台'),
        mimeType: 'image/svg+xml',
        size: 1024,
        width: 800,
        height: 400
      }
    ];

    for (const imageData of otherImages) {
      // 检查是否已存在
      const existing = await prisma.carouselImage.findFirst({
        where: { name: imageData.name }
      });

      if (!existing) {
        await prisma.carouselImage.create({
          data: imageData
        });
      }
    }
    console.log('✅ 其他类型图片创建成功');

    console.log('\n🎉 轮播图片初始化完成！');
    console.log('\n📊 统计信息：');
    console.log(`- 产品展示图片: ${productImages.length} 张`);
    console.log(`- 生产流程图片: ${processImages.length} 张`);
    console.log(`- 其他类型图片: ${otherImages.length} 张`);
    console.log(`- 总计: ${productImages.length + processImages.length + otherImages.length} 张`);

  } catch (error) {
    console.error('❌ 初始化轮播图片失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行初始化
if (require.main === module) {
  initCarouselImages()
    .then(() => {
      console.log('\n✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { initCarouselImages };
