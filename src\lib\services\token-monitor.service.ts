/**
 * 🇨🇳 风口云平台 - Token 监控服务
 * 
 * 功能说明：
 * - 定期检查 token 状态
 * - 自动刷新即将过期的 token
 * - 处理 token 过期情况
 */

import { AuthService } from './auth.service'
import { useAuthStore } from '@/lib/store/auth'

export class TokenMonitorService {
  private static checkInterval: NodeJS.Timeout | null = null
  private static isChecking = false
  private static readonly CHECK_INTERVAL = 5 * 60 * 1000 // 5分钟检查一次

  /**
   * 启动 Token 监控
   */
  static startTokenMonitoring(): void {
    // 避免重复启动
    if (this.checkInterval) {
      return
    }

    console.log('🔄 启动 Token 监控服务')

    // 立即检查一次
    this.checkTokenStatus()

    // 定期检查
    this.checkInterval = setInterval(async () => {
      await this.checkTokenStatus()
    }, this.CHECK_INTERVAL)
  }

  /**
   * 停止 Token 监控
   */
  static stopTokenMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
      console.log('⏹️ Token 监控服务已停止')
    }
  }

  /**
   * 检查 Token 状态
   */
  static async checkTokenStatus(): Promise<void> {
    // 避免并发检查
    if (this.isChecking) {
      return
    }

    this.isChecking = true

    try {
      const { isAuthenticated } = useAuthStore.getState()
      
      // 如果用户未登录，不需要检查
      if (!isAuthenticated) {
        this.isChecking = false
        return
      }

      console.log('🔍 检查 Token 状态...')

      // 检查并刷新 token
      const refreshSuccess = await AuthService.checkAndRefreshToken()
      
      if (!refreshSuccess) {
        console.log('❌ Token 刷新失败，用户需要重新登录')
        this.handleTokenExpired()
      } else {
        console.log('✅ Token 状态正常')
      }

    } catch (error) {
      console.error('❌ Token 状态检查失败:', error)
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 处理 Token 过期
   */
  static handleTokenExpired(): void {
    console.log('🚪 处理 Token 过期')

    // 停止监控
    this.stopTokenMonitoring()

    // 清除本地状态
    useAuthStore.getState().logout()

    // 显示提示信息
    this.showTokenExpiredNotification()

    // 跳转到登录页面
    this.redirectToLogin()
  }

  /**
   * 显示 Token 过期通知
   */
  static showTokenExpiredNotification(): void {
    if (typeof window !== 'undefined') {
      alert('登录状态已过期，请重新登录')
    }
  }

  /**
   * 跳转到登录页面
   */
  static redirectToLogin(): void {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname
      
      if (currentPath.startsWith('/admin')) {
        window.location.href = '/admin/login'
      } else if (currentPath.startsWith('/factory')) {
        window.location.href = '/factory/login'
      } else {
        window.location.href = '/'
      }
    }
  }

  /**
   * 用户登录后启动监控
   */
  static onUserLogin(): void {
    console.log('👤 用户登录，启动 Token 监控')
    this.startTokenMonitoring()
  }

  /**
   * 用户登出后停止监控
   */
  static onUserLogout(): void {
    console.log('👤 用户登出，停止 Token 监控')
    this.stopTokenMonitoring()
  }

  /**
   * 页面可见性变化时检查 Token
   */
  static onVisibilityChange(): void {
    if (typeof window !== 'undefined' && document.visibilityState === 'visible') {
      console.log('👁️ 页面重新可见，检查 Token 状态')
      this.checkTokenStatus()
    }
  }

  /**
   * 页面焦点变化时检查 Token
   */
  static onFocusChange(): void {
    console.log('🎯 页面重新获得焦点，检查 Token 状态')
    this.checkTokenStatus()
  }
}

// 导出单例
export const tokenMonitorService = TokenMonitorService

// 在浏览器环境中监听页面可见性和焦点变化
if (typeof window !== 'undefined') {
  document.addEventListener('visibilitychange', () => {
    tokenMonitorService.onVisibilityChange()
  })

  window.addEventListener('focus', () => {
    tokenMonitorService.onFocusChange()
  })
}
