'use client'

import { useState } from 'react'

export default function TestLayoutRecognitionPage() {
  const [inputText, setInputText] = useState('')
  const [layoutResult, setLayoutResult] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  // 师傅手写习惯示例
  const masterExamples = [
    {
      name: '分列习惯 - 出风/回风分列',
      description: '师傅习惯左边写出风口尺寸，右边写回风口尺寸',
      text: `出风口          回风口
140x240        300x1600
150x280        350x1800
160x300        400x2000
140x260        320x1700`
    },
    {
      name: '序号习惯 - 按顺序编号',
      description: '师傅习惯用数字序号，从左到右，从上到下排列',
      text: `1. 140x240 出风口 5个
2. 300x1600 回风口 3个
3. 150x280 出风口 4个
4. 350x1800 回风口 2个
5. 160x300 线型风口 6个`
    },
    {
      name: '楼层序号习惯',
      description: '师傅会在序号前加楼层信息',
      text: `3楼1. 140x240 出风口
3楼2. 300x1600 回风口
4楼1. 150x280 出风口
4楼2. 350x1800 回风口
5楼1. 160x300 线型风口`
    },
    {
      name: '您的手写清单',
      description: '实际的手写风口清单',
      text: `1. 140x240 300x1600
2. 140x3480 300x1600
3. 140x3510 300x1600
4. 140x3460 300x1600
5. 150x2460 300x1600
6. 150x3160 300x1600
7. 140x4525 300x1600
8. 140x2600 300x1600
9. 150x2470 300x1600
10. 150x1600 300x1600`
    }
  ]

  const handleAnalyze = async () => {
    if (!inputText.trim()) {
      alert('请输入文本内容')
      return
    }

    setIsProcessing(true)
    setLayoutResult(null)

    try {
      // 调用排版识别API
      const response = await fetch('/api/nlp/layout-analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: inputText
        })
      })

      const data = await response.json()
      setLayoutResult(data)
    } catch (error) {
      setLayoutResult({
        success: false,
        error: '分析失败: ' + (error instanceof Error ? error.message : '未知错误')
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const loadExample = (text: string) => {
    setInputText(text)
    setLayoutResult(null)
  }

  const getLayoutTypeDescription = (layoutType: string) => {
    switch (layoutType) {
      case 'column_based':
        return '分列排版 - 师傅习惯分两列写，左边出风口，右边回风口'
      case 'sequence_based':
        return '序号排版 - 师傅习惯按数字顺序编号，从左到右，从上到下'
      case 'table_based':
        return '表格排版 - 规整的表格形式'
      case 'mixed':
        return '混合排版 - 包含多种排版特征'
      default:
        return '未知排版 - 无法识别明确的排版模式'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            📐 师傅手写习惯智能识别
          </h1>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {/* 输入区域 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                📝 输入师傅手写内容
              </h2>
              
              {/* 师傅习惯示例 */}
              <div className="mb-4">
                <div className="text-sm text-gray-600 mb-3">师傅常见手写习惯示例:</div>
                <div className="space-y-2">
                  {masterExamples.map((example, index) => (
                    <div key={index} className="border border-gray-200 rounded p-3">
                      <button
                        onClick={() => loadExample(example.text)}
                        className="w-full text-left"
                      >
                        <div className="font-medium text-blue-700 mb-1">{example.name}</div>
                        <div className="text-xs text-gray-600">{example.description}</div>
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="请输入师傅手写的风口清单..."
                className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
              />

              <button
                onClick={handleAnalyze}
                disabled={isProcessing || !inputText.trim()}
                className="w-full mt-4 bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 px-6 rounded-lg hover:from-green-600 hover:to-blue-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? '🔄 正在分析师傅手写习惯...' : '📐 分析排版和手写习惯'}
              </button>
            </div>

            {/* 分析结果 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                🔍 排版识别结果
              </h2>

              {!layoutResult ? (
                <div className="bg-gray-100 rounded-lg p-8 text-center text-gray-500">
                  请输入师傅手写内容并点击分析
                </div>
              ) : layoutResult.success ? (
                <div className="space-y-4">
                  {/* 排版类型 */}
                  <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <h3 className="font-medium text-blue-800 mb-2">📋 识别的排版类型</h3>
                    <div className="text-blue-700 font-medium mb-2">
                      {layoutResult.data.layoutType}
                    </div>
                    <div className="text-sm text-blue-600">
                      {getLayoutTypeDescription(layoutResult.data.layoutType)}
                    </div>
                    <div className="mt-2 text-sm">
                      <span className="text-blue-600">置信度: </span>
                      <span className="font-medium text-blue-800">
                        {(layoutResult.data.confidence * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>

                  {/* 排版结构分析 */}
                  <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                    <h3 className="font-medium text-green-800 mb-2">🏗️ 排版结构分析</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-green-600">列头检测: </span>
                        <span className="font-medium">{layoutResult.data.structure.hasHeaders ? '✅ 有' : '❌ 无'}</span>
                      </div>
                      <div>
                        <span className="text-green-600">序号检测: </span>
                        <span className="font-medium">{layoutResult.data.structure.hasSequence ? '✅ 有' : '❌ 无'}</span>
                      </div>
                      <div>
                        <span className="text-green-600">列数: </span>
                        <span className="font-medium">{layoutResult.data.structure.columnCount}</span>
                      </div>
                      <div>
                        <span className="text-green-600">行数: </span>
                        <span className="font-medium">{layoutResult.data.structure.rowCount}</span>
                      </div>
                    </div>
                  </div>

                  {/* 风口分组 */}
                  <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                    <h3 className="font-medium text-purple-800 mb-2">📦 风口分组结果</h3>
                    <div className="space-y-3">
                      {layoutResult.data.ventGroups.map((group: any, index: number) => (
                        <div key={index} className="bg-white rounded p-3">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium text-purple-700">{group.title}</span>
                            <span className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded">
                              {group.type} | {group.position} | {group.items.length}项
                            </span>
                          </div>
                          <div className="space-y-1 max-h-32 overflow-y-auto">
                            {group.items.map((item: any, itemIndex: number) => (
                              <div key={itemIndex} className="text-xs text-gray-600 bg-gray-50 rounded p-2">
                                <div className="flex justify-between">
                                  <span>
                                    {item.sequence && `${item.sequence}. `}
                                    {item.floor && `${item.floor} `}
                                    {item.dimensions}
                                    {item.quantity && ` ×${item.quantity}`}
                                  </span>
                                  <span className="text-purple-600">
                                    {(item.confidence * 100).toFixed(0)}%
                                  </span>
                                </div>
                                <div className="text-gray-500 text-xs mt-1">
                                  原文: {item.originalText.substring(0, 30)}...
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 警告和建议 */}
                  {(layoutResult.data.warnings.length > 0 || layoutResult.data.suggestions.length > 0) && (
                    <div className="space-y-3">
                      {layoutResult.data.warnings.length > 0 && (
                        <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                          <h3 className="font-medium text-yellow-800 mb-2">⚠️ 识别警告</h3>
                          <ul className="text-sm text-yellow-700 space-y-1">
                            {layoutResult.data.warnings.map((warning: string, index: number) => (
                              <li key={index}>• {warning}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {layoutResult.data.suggestions.length > 0 && (
                        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                          <h3 className="font-medium text-blue-800 mb-2">💡 优化建议</h3>
                          <ul className="text-sm text-blue-700 space-y-1">
                            {layoutResult.data.suggestions.map((suggestion: string, index: number) => (
                              <li key={index}>• {suggestion}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                  <h3 className="font-medium text-red-800 mb-2">❌ 分析失败</h3>
                  <div className="text-red-700">{layoutResult.error}</div>
                </div>
              )}
            </div>
          </div>

          {/* 师傅手写习惯说明 */}
          <div className="mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-800 mb-4">
              👨‍🔧 师傅手写习惯智能识别说明
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-green-700 mb-2">🎯 识别的手写习惯</h4>
                <ul className="text-sm text-green-600 space-y-1">
                  <li>• <strong>分列习惯</strong>: 左边出风口，右边回风口</li>
                  <li>• <strong>序号习惯</strong>: 按1、2、3...顺序编号</li>
                  <li>• <strong>楼层标记</strong>: 3楼1、4楼2等楼层序号</li>
                  <li>• <strong>数量标记</strong>: 尺寸后面的个数信息</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-blue-700 mb-2">🔧 智能优化特性</h4>
                <ul className="text-sm text-blue-600 space-y-1">
                  <li>• <strong>异常检测</strong>: 超过50个项目提醒检查</li>
                  <li>• <strong>尺寸验证</strong>: 异常大尺寸自动标记</li>
                  <li>• <strong>智能分组</strong>: 根据排版自动分类</li>
                  <li>• <strong>置信度评估</strong>: 每个识别结果的可信度</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
