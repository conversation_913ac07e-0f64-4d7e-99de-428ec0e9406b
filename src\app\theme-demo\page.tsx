/**
 * 🇨🇳 风口云平台 - 主题演示页面
 * 
 * 功能说明：
 * - 展示不同主题效果
 * - 测试主题切换功能
 * - 验证主题系统完整性
 */

'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ThemeSelector } from '@/components/theme/theme-selector'
import { ThemeToggle, SimpleThemeToggle } from '@/components/theme/theme-toggle'
import { useTheme } from '@/components/theme/theme-provider'
import { 
  Sun, 
  Moon, 
  Monitor, 
  Palette, 
  Check,
  Star,
  Heart,
  Zap,
  Shield,
  Award
} from 'lucide-react'

export default function ThemeDemoPage() {
  const { mode, color, theme } = useTheme()

  return (
    <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">🎨 主题演示页面</h1>
              <p className="text-muted-foreground mt-2">
                测试和预览不同的主题效果
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <SimpleThemeToggle />
              <ThemeToggle showLabel />
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主题选择器 */}
          <div className="lg:col-span-2">
            <ThemeSelector />
          </div>

          {/* 当前主题信息 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Monitor className="h-5 w-5 mr-2" />
                  当前主题状态
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">模式</span>
                    <Badge variant="outline">{mode}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">颜色</span>
                    <Badge variant="outline">{color}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">主题名称</span>
                    <Badge>{theme.name}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 颜色预览 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Palette className="h-5 w-5 mr-2" />
                  颜色预览
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <div className="h-8 bg-primary rounded flex items-center justify-center text-primary-foreground text-xs font-medium">
                      Primary
                    </div>
                    <div className="h-8 bg-secondary rounded flex items-center justify-center text-secondary-foreground text-xs font-medium">
                      Secondary
                    </div>
                    <div className="h-8 bg-accent rounded flex items-center justify-center text-accent-foreground text-xs font-medium">
                      Accent
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-8 bg-muted rounded flex items-center justify-center text-muted-foreground text-xs font-medium">
                      Muted
                    </div>
                    <div className="h-8 bg-destructive rounded flex items-center justify-center text-destructive-foreground text-xs font-medium">
                      Destructive
                    </div>
                    <div className="h-8 border rounded flex items-center justify-center text-xs font-medium">
                      Border
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 组件演示 */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">组件演示</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 按钮演示 */}
            <Card>
              <CardHeader>
                <CardTitle>按钮组件</CardTitle>
                <CardDescription>不同样式的按钮效果</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full">
                  <Star className="h-4 w-4 mr-2" />
                  Primary Button
                </Button>
                <Button variant="secondary" className="w-full">
                  <Heart className="h-4 w-4 mr-2" />
                  Secondary Button
                </Button>
                <Button variant="outline" className="w-full">
                  <Zap className="h-4 w-4 mr-2" />
                  Outline Button
                </Button>
                <Button variant="ghost" className="w-full">
                  <Shield className="h-4 w-4 mr-2" />
                  Ghost Button
                </Button>
              </CardContent>
            </Card>

            {/* 徽章演示 */}
            <Card>
              <CardHeader>
                <CardTitle>徽章组件</CardTitle>
                <CardDescription>不同状态的徽章样式</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex flex-wrap gap-2">
                  <Badge>Default</Badge>
                  <Badge variant="secondary">Secondary</Badge>
                  <Badge variant="outline">Outline</Badge>
                  <Badge variant="destructive">Destructive</Badge>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Badge className="bg-green-500">
                    <Check className="h-3 w-3 mr-1" />
                    Success
                  </Badge>
                  <Badge className="bg-yellow-500">
                    <Award className="h-3 w-3 mr-1" />
                    Warning
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* 卡片演示 */}
            <Card>
              <CardHeader>
                <CardTitle>卡片组件</CardTitle>
                <CardDescription>展示卡片在不同主题下的效果</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">这是一个 muted 背景的内容区域</p>
                  </div>
                  <div className="p-3 bg-accent rounded-lg">
                    <p className="text-sm text-accent-foreground">这是一个 accent 背景的内容区域</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>🔧 主题系统使用说明</CardTitle>
              <CardDescription>如何在您的工厂系统中使用主题功能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">🌅 浅色主题</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• 适合白天或光线充足的环境</li>
                    <li>• 提供清晰明亮的视觉体验</li>
                    <li>• 支持多种主题颜色选择</li>
                    <li>• 减少屏幕反光影响</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">🌙 深色主题</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• 纯黑色背景，极致护眼体验</li>
                    <li>• 适合夜班工作和低光环境</li>
                    <li>• 显著减少眼部疲劳和蓝光伤害</li>
                    <li>• 节省OLED屏幕电池电量</li>
                    <li>• 提供专业沉浸的工作体验</li>
                  </ul>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 dark:bg-gray-800 border border-blue-200 dark:border-gray-700 rounded-lg">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">💡 快速切换</h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  您可以在侧边栏底部找到主题切换按钮，或者在工厂设置页面的"界面设置"中进行详细配置。
                  深色主题采用纯黑色背景设计，特别适合夜班工作环境。系统会自动保存您的主题偏好设置。
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
