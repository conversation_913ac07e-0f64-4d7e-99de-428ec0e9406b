/**
 * 🎁 推荐奖励使用条件验证工具
 * 
 * 验证客户是否满足使用推荐奖励的条件
 */

import { db } from '@/lib/database'
import { safeNumber } from '@/lib/utils/number-utils'
import type { Client, Order } from '@/types'

export interface RewardUsageValidationResult {
  canUse: boolean
  reason?: string
  details: {
    clientInfo: {
      name: string
      availableReward: number
      referralCount: number
    }
    referredClients: Array<{
      id: string
      name: string
      phone: string
      hasUnpaidOrders: boolean
      unpaidAmount: number
      orderCount: number
    }>
    summary: {
      totalReferredClients: number
      clientsWithUnpaidOrders: number
      totalUnpaidAmount: number
    }
  }
}

/**
 * 验证客户是否可以使用推荐奖励（支持按比例使用）
 *
 * 条件：
 * 1. 客户有可用奖励余额
 * 2. 使用金额不超过可用余额（基于已结清客户的奖励）
 * 3. 至少有一个被推荐客户已结清货款
 */
export async function validateRewardUsage(
  clientId: string,
  usageAmount: number
): Promise<RewardUsageValidationResult> {
  try {
    console.log('🔍 验证奖励使用条件（按比例模式）:', { clientId, usageAmount })

    // 1. 获取客户信息
    const client = await db.getClientById(clientId)
    if (!client) {
      return {
        canUse: false,
        reason: '客户不存在',
        details: {
          clientInfo: { name: '', availableReward: 0, referralCount: 0 },
          referredClients: [],
          summary: { totalReferredClients: 0, clientsWithUnpaidOrders: 0, totalUnpaidAmount: 0 }
        }
      }
    }

    // 2. 计算按比例奖励状态
    const { calculateProportionalReward } = await import('@/lib/utils/reward-calculator')
    const rewardStatus = await calculateProportionalReward(clientId, client.factoryId)

    console.log('📊 奖励状态:', {
      总奖励: rewardStatus.totalReward.toFixed(2),
      可用奖励: rewardStatus.availableReward.toFixed(2),
      结清客户: `${rewardStatus.settledClients}/${rewardStatus.totalClients}`
    })

    // 3. 检查可用奖励余额
    if (rewardStatus.availableReward <= 0) {
      return {
        canUse: false,
        reason: rewardStatus.totalClients === 0
          ? '没有推荐其他客户'
          : `没有可用的奖励余额，需要被推荐客户结清货款（已结清：${rewardStatus.settledClients}/${rewardStatus.totalClients}）`,
        details: {
          clientInfo: {
            name: client.name,
            availableReward: rewardStatus.availableReward,
            referralCount: rewardStatus.totalClients
          },
          referredClients: rewardStatus.details.map(detail => ({
            id: detail.clientId,
            name: detail.clientName,
            phone: '', // 这里可以从客户信息中获取
            hasUnpaidOrders: !detail.isSettled,
            unpaidAmount: detail.unpaidAmount,
            orderCount: 1 // 简化处理
          })),
          summary: {
            totalReferredClients: rewardStatus.totalClients,
            clientsWithUnpaidOrders: rewardStatus.totalClients - rewardStatus.settledClients,
            totalUnpaidAmount: rewardStatus.pendingReward
          }
        }
      }
    }

    // 4. 检查使用金额是否超过可用余额
    if (usageAmount > rewardStatus.availableReward) {
      return {
        canUse: false,
        reason: `使用金额 ¥${usageAmount.toFixed(2)} 超过可用余额 ¥${rewardStatus.availableReward.toFixed(2)}`,
        details: {
          clientInfo: {
            name: client.name,
            availableReward: rewardStatus.availableReward,
            referralCount: rewardStatus.totalClients
          },
          referredClients: rewardStatus.details.map(detail => ({
            id: detail.clientId,
            name: detail.clientName,
            phone: '',
            hasUnpaidOrders: !detail.isSettled,
            unpaidAmount: detail.unpaidAmount,
            orderCount: 1
          })),
          summary: {
            totalReferredClients: rewardStatus.totalClients,
            clientsWithUnpaidOrders: rewardStatus.totalClients - rewardStatus.settledClients,
            totalUnpaidAmount: rewardStatus.pendingReward
          }
        }
      }
    }

    // 5. 验证通过，可以使用奖励
    const result: RewardUsageValidationResult = {
      canUse: true,
      reason: undefined,
      details: {
        clientInfo: {
          name: client.name,
          availableReward: rewardStatus.availableReward,
          referralCount: rewardStatus.totalClients
        },
        referredClients: rewardStatus.details.map(detail => ({
          id: detail.clientId,
          name: detail.clientName,
          phone: '',
          hasUnpaidOrders: !detail.isSettled,
          unpaidAmount: detail.unpaidAmount,
          orderCount: 1
        })),
        summary: {
          totalReferredClients: rewardStatus.totalClients,
          clientsWithUnpaidOrders: rewardStatus.totalClients - rewardStatus.settledClients,
          totalUnpaidAmount: rewardStatus.pendingReward
        }
      }
    }

    console.log('✅ 奖励使用条件验证完成（按比例模式）:', {
      canUse: true,
      usageAmount: usageAmount.toFixed(2),
      availableReward: rewardStatus.availableReward.toFixed(2),
      settledClients: `${rewardStatus.settledClients}/${rewardStatus.totalClients}`
    })

    return result

  } catch (error) {
    console.error('❌ 验证奖励使用条件失败:', error)
    return {
      canUse: false,
      reason: '验证过程中发生错误，请重试',
      details: {
        clientInfo: { name: '', availableReward: 0, referralCount: 0 },
        referredClients: [],
        summary: { totalReferredClients: 0, clientsWithUnpaidOrders: 0, totalUnpaidAmount: 0 }
      }
    }
  }
}

/**
 * 快速检查客户是否可以使用奖励（简化版本）
 */
export async function canClientUseReward(clientId: string): Promise<boolean> {
  try {
    const client = await db.getClientById(clientId)
    if (!client) return false

    const availableReward = safeNumber(client.availableReward)
    if (availableReward <= 0) return false

    // 获取被推荐的客户
    const allClients = await db.getClientsByFactoryId(client.factoryId)
    const referredClients = allClients.filter((c: unknown) => c.referrerId === clientId)

    // 检查是否所有被推荐客户都已结清货款
    for (const referredClient of referredClients) {
      const clientOrders = await db.getOrdersByClientId(referredClient.id)
      
      const hasUnpaidOrders = clientOrders.some((order: unknown) => {
        const totalAmount = safeNumber(order.totalAmount)
        const paidAmount = safeNumber(order.paidAmount)
        return totalAmount > paidAmount
      })

      if (hasUnpaidOrders) {
        return false
      }
    }

    return true
  } catch (error) {
    console.error('❌ 快速检查奖励使用条件失败:', error)
    return false
  }
}

/**
 * 获取客户的奖励使用限制信息（使用实时计算的奖励数据）
 */
export async function getRewardUsageLimits(clientId: string): Promise<{
  maxUsableAmount: number
  restrictions: string[]
}> {
  try {
    const client = await db.getClientById(clientId)
    if (!client) {
      return {
        maxUsableAmount: 0,
        restrictions: ['客户不存在']
      }
    }

    // 🔧 修复：使用实时计算的奖励数据，而不是数据库中的旧值
    const { calculateProportionalReward } = await import('@/lib/utils/reward-calculator')
    const rewardStatus = await calculateProportionalReward(clientId, client.factoryId)

    const availableReward = rewardStatus.availableReward
    const restrictions: string[] = []

    console.log('🔍 获取奖励使用限制 - 实时数据:', {
      客户: client.name,
      数据库旧值: safeNumber(client.availableReward),
      实时计算值: availableReward,
      结清客户: `${rewardStatus.settledClients}/${rewardStatus.totalClients}`,
      详细信息: rewardStatus.details.map(detail => ({
        客户: detail.clientName,
        订单金额: detail.orderAmount,
        奖励金额: detail.rewardAmount,
        已付金额: detail.paidAmount,
        未付金额: detail.unpaidAmount,
        是否结清: detail.isSettled
      }))
    })

    if (availableReward <= 0) {
      if (rewardStatus.totalClients === 0) {
        restrictions.push('没有推荐其他客户')
      } else {
        restrictions.push(`没有可用的奖励余额，需要被推荐客户结清货款（已结清：${rewardStatus.settledClients}/${rewardStatus.totalClients}）`)
      }
    }

    // 🔧 简化：实时计算已经包含了所有验证逻辑，无需重复检查
    const result = {
      maxUsableAmount: availableReward,
      restrictions
    }

    console.log('✅ 奖励使用限制计算完成:', {
      客户: client.name,
      最大可用金额: availableReward,
      限制条件: restrictions,
      canUse: availableReward > 0 && restrictions.length === 0
    })

    return result
  } catch (error) {
    console.error('❌ 获取奖励使用限制失败:', error)
    return {
      maxUsableAmount: 0,
      restrictions: ['获取限制信息失败']
    }
  }
}
