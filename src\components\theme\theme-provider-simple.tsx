/**
 * 最简单的主题提供者 - 彻底避免水合错误
 */

'use client'

import { useEffect, useState } from 'react'
import { getUserThemeSettings } from '@/lib/utils/theme-manager'
import { useAuthStore } from '@/lib/store/auth'
import { autoFixThemeData } from '@/lib/utils/theme-cleanup'

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [mounted, setMounted] = useState(false)
  const { isAuthenticated, user } = useAuthStore()

  useEffect(() => {
    setMounted(true)

    // 先清理损坏的主题数据
    autoFixThemeData()

    // 简化的主题初始化
    const initializeTheme = () => {
      try {
        // 获取用户主题设置
        const userThemeSettings = getUserThemeSettings()
        const mode = userThemeSettings?.mode || 'light'

        const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        const isDark = mode === 'dark' || (mode === 'auto' && isSystemDark)

        // 检查当前路径是否为管理界面
        const isAdminPath = window.location.pathname.startsWith('/admin') ||
                           window.location.pathname.startsWith('/factory') ||
                           window.location.pathname.startsWith('/client')

        if (isDark && isAdminPath) {
          document.documentElement.classList.add('dark')
          document.body.classList.add('admin-dark')
          console.log('🎨 管理界面深色主题初始化')
        } else {
          document.documentElement.classList.remove('dark')
          document.body.classList.remove('admin-dark')
          console.log('🎨 浅色主题初始化')
        }
      } catch (error) {
        console.warn('⚠️ 主题初始化失败:', error)
      }
    }

    // 延迟初始化，确保清理完成
    setTimeout(initializeTheme, 100)
  }, [])

  // 监听用户登录状态变化
  useEffect(() => {
    if (mounted) {
      // 用户状态变化时重新初始化主题
      const userThemeSettings = getUserThemeSettings()
      const mode = userThemeSettings?.mode || 'light'

      const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      const isDark = mode === 'dark' || (mode === 'auto' && isSystemDark)

      const isAdminPath = window.location.pathname.startsWith('/admin') ||
                         window.location.pathname.startsWith('/factory') ||
                         window.location.pathname.startsWith('/client')

      if (isDark && isAdminPath) {
        document.documentElement.classList.add('dark')
        document.body.classList.add('admin-dark')
      } else {
        document.documentElement.classList.remove('dark')
        document.body.classList.remove('admin-dark')
      }

      console.log('🎨 用户状态变化，主题已更新:', isDark ? '深色模式' : '浅色模式')
    }
  }, [isAuthenticated, user, mounted])

  // 在客户端挂载前，不应用任何主题
  if (!mounted) {
    return <div suppressHydrationWarning>{children}</div>
  }

  return <>{children}</>
}

// 简化的主题Hook
export function useTheme() {
  const [mode, setMode] = useState<'light' | 'dark' | 'auto'>('light')
  const [mounted, setMounted] = useState(false)

  // 初始化主题
  useEffect(() => {
    setMounted(true)

    if (typeof window !== 'undefined') {
      try {
        // 使用用户特定的主题设置
        const userThemeSettings = getUserThemeSettings()
        const storedMode = userThemeSettings?.mode || 'light'
        setMode(storedMode)

        // 立即应用存储的主题
        const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        const isDark = storedMode === 'dark' || (storedMode === 'auto' && isSystemDark)

        // 检查当前路径是否为管理界面
        const isAdminPath = window.location.pathname.startsWith('/admin') ||
                           window.location.pathname.startsWith('/factory') ||
                           window.location.pathname.startsWith('/client')

        if (isDark && isAdminPath) {
          document.documentElement.classList.add('dark')
          document.body.classList.add('admin-dark')
          console.log('🎨 管理界面深色主题初始化')
        } else {
          document.documentElement.classList.remove('dark')
          document.body.classList.remove('admin-dark')
          console.log('🎨 浅色主题初始化')
        }
      } catch (error) {
        console.warn('⚠️ 主题初始化失败:', error)
      }

      // 监听路径变化，自动切换主题
      const handlePathChange = () => {
        const userThemeSettings = getUserThemeSettings()
        const storedMode = userThemeSettings?.mode || 'light'
        const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        const isDark = storedMode === 'dark' || (storedMode === 'auto' && isSystemDark)

        const isAdminPath = window.location.pathname.startsWith('/admin') ||
                           window.location.pathname.startsWith('/factory') ||
                           window.location.pathname.startsWith('/client')

        if (isDark && isAdminPath) {
          document.documentElement.classList.add('dark')
          document.body.classList.add('admin-dark')
        } else {
          document.documentElement.classList.remove('dark')
          document.body.classList.remove('admin-dark')
        }
      }

      // 监听popstate事件（浏览器前进后退）
      window.addEventListener('popstate', handlePathChange)

      // 监听pushstate和replacestate（程序化导航）
      const originalPushState = history.pushState
      const originalReplaceState = history.replaceState

      history.pushState = function(...args) {
        originalPushState.apply(history, args)
        setTimeout(handlePathChange, 0)
      }

      history.replaceState = function(...args) {
        originalReplaceState.apply(history, args)
        setTimeout(handlePathChange, 0)
      }

      return () => {
        window.removeEventListener('popstate', handlePathChange)
        history.pushState = originalPushState
        history.replaceState = originalReplaceState
      }
    }
  }, [])

  const setTheme = async (newMode: 'light' | 'dark' | 'auto') => {
    setMode(newMode)

    // 立即应用主题
    if (typeof window !== 'undefined') {
      const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      const isDark = newMode === 'dark' || (newMode === 'auto' && isSystemDark)

      // 检查当前路径是否为管理界面
      const isAdminPath = window.location.pathname.startsWith('/admin') ||
                         window.location.pathname.startsWith('/factory') ||
                         window.location.pathname.startsWith('/client')

      if (isDark && isAdminPath) {
        // 只在管理界面应用深色主题
        document.documentElement.classList.add('dark')
        document.body.classList.add('admin-dark')
        console.log('🌙 管理界面深色主题已应用')
      } else {
        // 移除深色主题
        document.documentElement.classList.remove('dark')
        document.body.classList.remove('admin-dark')
        console.log('🌅 浅色主题已应用')
      }

      // 保存到用户特定的localStorage
      try {
        const { saveUserThemeSettings } = await import('@/lib/utils/theme-manager')
        saveUserThemeSettings(newMode)
      } catch (error) {
        console.warn('⚠️ 保存用户主题设置失败:', error)
      }
    }
  }

  const toggleTheme = () => {
    const newMode = mode === 'light' ? 'dark' : 'light'
    setTheme(newMode)
  }

  return {
    mode,
    setTheme,
    toggleTheme,
    mounted
  }
}
