/**
 * 🇨🇳 风口云平台 - 会话状态监控服务
 * 
 * 功能说明：
 * - 监控用户会话状态
 * - 检测会话是否被踢出（单点登录）
 * - 自动处理会话失效情况
 */

import { useAuthStore } from '@/lib/store/auth'
import { api } from '@/lib/api/client'

export interface SessionMonitorConfig {
  immediateCheck?: boolean // 是否立即检查
  checkInterval: number    // 检查间隔（毫秒）
  maxRetries: number       // 最大重试次数
  onSessionInvalid?: (reason: string) => void // 会话失效回调
}

/**
 * 会话状态监控服务
 */
export class SessionMonitorService {
  private static instance: SessionMonitorService | null = null
  private checkTimer: NodeJS.Timeout | null = null
  private isChecking = false
  private retryCount = 0
  private config: SessionMonitorConfig

  private constructor(config: SessionMonitorConfig) {
    this.config = config
  }

  /**
   * 获取单例实例
   */
  static getInstance(config?: SessionMonitorConfig): SessionMonitorService {
    if (!SessionMonitorService.instance) {
      const defaultConfig: SessionMonitorConfig = {
        immediateCheck: false,
        checkInterval: 300000, // 5分钟检查一次，合理的定期检查
        maxRetries: 3,
        onSessionInvalid: (reason: string) => {
          console.log('🚨 会话失效:', reason)
          // 显示通知
          if (typeof window !== 'undefined') {
            alert(`会话已失效：${reason}\n\n可能原因：您的账号在其他地方登录了。`)
          }
        }
      }
      
      SessionMonitorService.instance = new SessionMonitorService({
        ...defaultConfig,
        ...config
      })
    }
    return SessionMonitorService.instance
  }

  /**
   * 开始监控会话状态
   */
  startMonitoring(): void {
    if (this.checkTimer) {
      console.log('⚠️ 会话监控已在运行')
      return
    }

    console.log('🚀 启动会话监控')
    console.log('   立即检查:', this.config.immediateCheck ? '是' : '否')
    console.log('   检查间隔:', this.config.checkInterval / 1000, '秒')
    console.log('   最大重试次数:', this.config.maxRetries)

    // 检查认证状态
    const { isAuthenticated } = useAuthStore.getState()
    console.log('   当前认证状态:', isAuthenticated)

    // 如果配置了立即检查，则立即执行一次
    if (this.config.immediateCheck) {
      console.log('🔍 执行立即检查...')
      this.checkSessionStatus()
    }

    // 设置定时检查
    this.checkTimer = setInterval(() => {
      this.checkSessionStatus()
    }, this.config.checkInterval)

    console.log('✅ 会话监控定时器已设置')
  }

  /**
   * 停止监控会话状态
   */
  stopMonitoring(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
      this.checkTimer = null
      console.log('⏹️ 会话状态监控已停止')
    }
    this.retryCount = 0
  }

  /**
   * 检查会话状态
   */
  private async checkSessionStatus(): Promise<void> {
    // 避免并发检查
    if (this.isChecking) {
      return
    }

    const { isAuthenticated } = useAuthStore.getState()
    console.log('   认证状态检查:', isAuthenticated)

    // 如果用户未登录，停止监控
    if (!isAuthenticated) {
      console.log('❌ 用户未登录，停止会话监控')
      this.stopMonitoring()
      return
    }

    this.isChecking = true

    try {
      console.log('🔍 检查会话状态...')

      const response = await api.get('/api/auth/session')

      if (response.success) {
        console.log('✅ 会话状态正常')
        this.retryCount = 0 // 重置重试计数
      } else {
        console.log('❌ 会话状态异常:', response.error)
        
        // 如果需要登出
        if ((response as any).shouldLogout) {
          await this.handleSessionInvalid(response.error || '会话已失效')
        } else {
          // 增加重试计数
          this.retryCount++
          if (this.retryCount >= this.config.maxRetries) {
            await this.handleSessionInvalid('会话检查失败次数过多')
          }
        }
      }

    } catch (error) {
      console.error('❌ 会话状态检查失败:', error)
      
      // 增加重试计数
      this.retryCount++
      if (this.retryCount >= this.config.maxRetries) {
        await this.handleSessionInvalid('网络连接异常')
      }
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 处理会话失效
   */
  private async handleSessionInvalid(reason: string): Promise<void> {
    console.log('🚨 处理会话失效:', reason)

    // 停止监控
    this.stopMonitoring()

    // 调用回调函数
    if (this.config.onSessionInvalid) {
      this.config.onSessionInvalid(reason)
    }

    // 清除本地状态
    useAuthStore.getState().logout()

    // 跳转到登录页
    if (typeof window !== 'undefined') {
      // 延迟跳转，确保用户看到通知
      setTimeout(() => {
        const currentPath = window.location.pathname
        if (currentPath.startsWith('/admin')) {
          window.location.href = '/admin/login'
        } else {
          window.location.href = '/factory/login'
        }
      }, 2000)
    }
  }

  /**
   * 手动触发会话检查
   */
  async checkNow(): Promise<boolean> {
    try {
      await this.checkSessionStatus()
      return true
    } catch (error) {
      console.error('❌ 手动会话检查失败:', error)
      return false
    }
  }

  /**
   * 更新监控配置
   */
  updateConfig(newConfig: Partial<SessionMonitorConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // 如果正在监控且间隔时间改变，重启监控
    if (this.checkTimer && newConfig.checkInterval) {
      this.stopMonitoring()
      this.startMonitoring()
    }
  }

  /**
   * 获取当前监控状态
   */
  getStatus(): {
    isMonitoring: boolean
    retryCount: number
    config: SessionMonitorConfig
  } {
    return {
      isMonitoring: !!this.checkTimer,
      retryCount: this.retryCount,
      config: this.config
    }
  }
}

/**
 * 全局会话监控实例
 */
export const sessionMonitor = SessionMonitorService.getInstance()

/**
 * 启动会话监控的便捷函数
 */
export function startSessionMonitoring(config?: Partial<SessionMonitorConfig>): void {
  const monitor = SessionMonitorService.getInstance(config)
  monitor.startMonitoring()
}

/**
 * 停止会话监控的便捷函数
 */
export function stopSessionMonitoring(): void {
  sessionMonitor.stopMonitoring()
}
