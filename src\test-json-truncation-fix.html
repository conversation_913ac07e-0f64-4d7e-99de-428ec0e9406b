<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 JSON截断修复验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover { background: #1976D2; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .button.success { background: #4CAF50; }
        .button.success:hover { background: #45a049; }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #4CAF50; background: #e8f5e8; }
        .error { border-color: #f44336; background: #ffe8e8; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JSON截断修复验证</h1>
        <p>验证修复后的DeepSeek API能否正确处理JSON截断问题</p>
        
        <div style="background: #e8f5e8; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #4CAF50;">
            <strong>✅ 已修复的问题：</strong><br>
            • Token数量：800 → 1500（避免截断）<br>
            • 改进激进修复：能从截断JSON中恢复项目信息<br>
            • 增强风口对象提取：支持不完整的JSON片段<br>
            • 智能信息恢复：项目名称、客户信息、尺寸数据
        </div>
        
        <button class="button success" onclick="testFullRecognition()">🧪 测试完整识别</button>
        <button class="button" onclick="testTruncationRecovery()">🔧 测试截断恢复</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_KEY = 'sk-c9047196d51d4f6c99dc94f9fbef602c';
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}] ${title}</strong>\n${content}`;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function testFullRecognition() {
            addResult('🧪 完整识别测试', '使用1500 tokens测试完整识别...', 'info');
            
            const testText = `项目：万科翡翠公园二期
客户：张总

一楼：
2103房号：
回风口 1200×300 白色 1个
出风口 2520×110 白色 1个

大厅：
回风口 1500×300 白色 1个
出风口 3610×110 白色 1个

小房间1：
回风口 990×300 白色 1个
出风口 2650×140 白色 1个

小房间2：
回风口 1000×300 白色 1个
出风口 2390×140 白色 1个

小房间3：
回风口 1000×300 白色 1个
出风口 1720×130 白色 1个

小房间4：
回风口 930×300 白色 1个
出风口 2060×140 白色 1个

主卧室：
回风口 1150×300 白色 1个
出风口 2620×140 白色 1个`;

            const improvedPrompt = `解析风口订单为JSON格式。

输入文本：
${testText}

解析规则：
- 提取项目名称和客户信息
- 宽度≤254mm = 出风口 (systemType: "double_white_outlet")
- 宽度≥255mm = 回风口 (systemType: "white_return")
- 数字<100当作厘米，需要×10转换为毫米
- 提取房间名称（如：大厅、办公室、2103房号等）

返回格式（必须是有效的JSON）：
{
  "projects": [{
    "projectName": "项目名称",
    "clientInfo": "客户信息",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间名称",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": "备注信息"
          }
        ]
      }]
    }]
  }]
}

请确保返回完整有效的JSON，不要添加任何解释文字。`;

            const startTime = performance.now();
            
            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [{ role: 'user', content: improvedPrompt }],
                        max_tokens: 1500,
                        temperature: 0.2,
                        stream: false
                    }),
                    signal: AbortSignal.timeout(30000)
                });
                
                const duration = performance.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0]?.message?.content;
                    
                    addResult(
                        '✅ API调用成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
Token使用: ${data.usage?.total_tokens || 'N/A'}
响应长度: ${content?.length || 0} 字符

原始响应:
${content}`,
                        'success'
                    );
                    
                    // 解析JSON并验证
                    try {
                        let jsonStr = content.trim();
                        jsonStr = jsonStr.replace(/^```json\s*/i, '');
                        jsonStr = jsonStr.replace(/^```\s*/i, '');
                        jsonStr = jsonStr.replace(/\s*```\s*$/g, '');
                        
                        const firstBrace = jsonStr.indexOf('{');
                        const lastBrace = jsonStr.lastIndexOf('}');
                        
                        if (firstBrace !== -1 && lastBrace !== -1) {
                            jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
                        }
                        
                        const parsed = JSON.parse(jsonStr);
                        const project = parsed.projects?.[0];
                        
                        if (project) {
                            const projectName = project.projectName || '未识别';
                            const clientInfo = project.clientInfo || '未识别';
                            const roomCount = project.floors?.[0]?.rooms?.length || 0;
                            const ventCount = project.floors?.[0]?.rooms?.reduce((total, room) => 
                                total + (room.vents?.length || 0), 0) || 0;
                            
                            addResult(
                                '🎉 完整识别结果',
                                `项目名称: ${projectName} ${projectName !== '未识别' ? '✅' : '❌'}
客户信息: ${clientInfo} ${clientInfo !== '未识别' ? '✅' : '❌'}
房间数量: ${roomCount}
风口数量: ${ventCount}
预期风口: 14个 ${ventCount === 14 ? '✅' : '❌'}

详细数据:
${JSON.stringify(parsed, null, 2)}`,
                                (projectName !== '未识别' && clientInfo !== '未识别' && ventCount === 14) ? 'success' : 'warning'
                            );
                        }
                        
                    } catch (parseError) {
                        addResult(
                            '❌ JSON解析失败',
                            `解析错误: ${parseError.message}
这表明JSON仍然被截断或格式错误`,
                            'error'
                        );
                    }
                    
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ API调用失败',
                        `状态码: ${response.status}
错误信息: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const duration = performance.now() - startTime;
                addResult(
                    '❌ API调用异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        function testTruncationRecovery() {
            addResult('🔧 截断恢复测试', '测试改进后的JSON修复逻辑...', 'info');
            
            // 模拟截断的JSON
            const truncatedJson = `{
  "projects": [{
    "projectName": "万科翡翠公园二期",
    "clientInfo": "张总",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "2103房号",
        "vents": [
          {
            "systemType": "white_return",
            "originalType": "回风口",
            "dimensions": {"length": 1200, "width": 300, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          },
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2520, "width": 110, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          }
        ]
      }, {
        "roomName": "大厅",
        "vents": [
          {
            "systemType": "white_return",
            "originalType": "回风口",
            "dimensions": {"length": 1500, "width": 300, "unit": "mm"}`;
            
            addResult(
                '📝 模拟截断JSON',
                `原始长度: ${truncatedJson.length} 字符
截断位置: 在dimensions对象中间
缺失信息: color, quantity, notes等字段

截断内容:
${truncatedJson}`,
                'warning'
            );
            
            // 模拟修复逻辑
            try {
                // 提取项目信息
                const projectNameMatch = truncatedJson.match(/"projectName":\s*"([^"]*)"/);
                const clientMatch = truncatedJson.match(/"clientInfo":\s*"([^"]*)"/);
                
                const projectName = projectNameMatch ? projectNameMatch[1] : "";
                const clientInfo = clientMatch ? clientMatch[1] : "";
                
                // 提取风口信息
                const systemTypeMatches = truncatedJson.match(/"systemType":\s*"([^"]*)"[^}]*}/g) || [];
                const ventObjects = [];
                
                for (const match of systemTypeMatches) {
                    const systemTypeMatch = match.match(/"systemType":\s*"([^"]*)"/);
                    const lengthMatch = match.match(/"length":\s*(\d+)/);
                    const widthMatch = match.match(/"width":\s*(\d+)/);
                    
                    if (systemTypeMatch && lengthMatch && widthMatch) {
                        ventObjects.push({
                            systemType: systemTypeMatch[1],
                            originalType: systemTypeMatch[1].includes('return') ? '回风口' : '出风口',
                            dimensions: {
                                length: parseInt(lengthMatch[1]),
                                width: parseInt(widthMatch[1]),
                                unit: 'mm'
                            },
                            color: 'white',
                            quantity: 1,
                            notes: ''
                        });
                    }
                }
                
                const recovered = {
                    projects: [{
                        projectName: projectName,
                        clientInfo: clientInfo,
                        floors: [{
                            floorName: "一楼",
                            rooms: [{
                                roomName: "房间",
                                vents: ventObjects
                            }]
                        }]
                    }]
                };
                
                addResult(
                    '✅ 截断恢复成功',
                    `恢复的项目名称: ${projectName}
恢复的客户信息: ${clientInfo}
恢复的风口数量: ${ventObjects.length}

恢复的数据:
${JSON.stringify(recovered, null, 2)}`,
                    'success'
                );
                
            } catch (error) {
                addResult(
                    '❌ 截断恢复失败',
                    `错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult(
                '📋 JSON截断修复验证说明',
                `🎯 目标: 验证JSON截断问题修复
🔧 修复内容:
  • Token数量: 800 → 1500
  • 改进激进修复逻辑
  • 增强信息恢复能力
  • 智能风口对象提取

点击"🧪 测试完整识别"验证修复效果！`,
                'info'
            );
        };
    </script>
</body>
</html>
