/**
 * 检查工厂状态
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  console.log('🔍 检查工厂状态')
  
  try {
    // 获取所有工厂
    const factories = await db.getFactories()
    console.log('📊 工厂数量:', factories.length)
    
    // 查找广西风口加工厂
    const gxFactory = factories.find(f => f.code === 'GXSFKC')
    if (gxFactory) {
      console.log('🏭 广西风口加工厂状态:', {
        name: gxFactory.name,
        code: gxFactory.code,
        status: gxFactory.status,
        subscriptionType: gxFactory.subscriptionType,
        subscriptionStartDate: gxFactory.subscriptionStartDate,
        subscriptionEndDate: gxFactory.subscriptionEndDate,
        suspendedReason: gxFactory.suspendedReason
      })
      
      // 检查工厂状态
      const statusCheck = await (db as any).checkFactoryStatus(gxFactory)
      console.log('🔍 状态检查结果:', statusCheck)
      
      return NextResponse.json({
        success: true,
        factory: {
          name: gxFactory.name,
          code: gxFactory.code,
          status: gxFactory.status,
          subscriptionType: gxFactory.subscriptionType,
          subscriptionStartDate: gxFactory.subscriptionStartDate,
          subscriptionEndDate: gxFactory.subscriptionEndDate,
          suspendedReason: gxFactory.suspendedReason
        },
        statusCheck
      })
    } else {
      return NextResponse.json({
        success: false,
        error: '未找到广西风口加工厂'
      })
    }
    
  } catch (error) {
    console.error('❌ 检查工厂状态失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
