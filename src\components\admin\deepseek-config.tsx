"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle, Settings, TestTube, Eye, EyeOff } from 'lucide-react'
import { testDeepSeekConnection, validateAPIKeyFormat } from '@/lib/utils/deepseek-test'

export function DeepSeekConfigPanel() {
  const [apiKey, setApiKey] = useState('')
  const [showApiKey, setShowApiKey] = useState(false)
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)
  const [saving, setSaving] = useState(false)

  // 加载当前配置
  useEffect(() => {
    const savedApiKey = localStorage.getItem('deepseek_api_key')
    if (savedApiKey) {
      setApiKey(savedApiKey)
    }
  }, [])

  // 测试API连接
  const handleTestConnection = async () => {
    if (!apiKey.trim()) {
      setTestResult({
        success: false,
        message: '请先输入API Key'
      })
      return
    }

    const validation = validateAPIKeyFormat(apiKey)
    if (!validation.valid) {
      setTestResult({
        success: false,
        message: validation.message
      })
      return
    }

    setTesting(true)
    setTestResult(null)

    try {
      // 临时保存API Key用于测试
      const originalKey = localStorage.getItem('deepseek_api_key')
      localStorage.setItem('deepseek_api_key', apiKey)

      const result = await testDeepSeekConnection()
      setTestResult(result)

      // 恢复原始配置
      if (originalKey) {
        localStorage.setItem('deepseek_api_key', originalKey)
      } else {
        localStorage.removeItem('deepseek_api_key')
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: error instanceof Error ? error.message : '测试失败'
      })
    } finally {
      setTesting(false)
    }
  }

  // 保存配置
  const handleSaveConfig = async () => {
    if (!apiKey.trim()) {
      alert('请输入API Key')
      return
    }

    const validation = validateAPIKeyFormat(apiKey)
    if (!validation.valid) {
      alert(validation.message)
      return
    }

    setSaving(true)
    try {
      localStorage.setItem('deepseek_api_key', apiKey)
      alert('✅ 配置保存成功！')
    } catch (error) {
      alert('❌ 保存失败：' + (error instanceof Error ? error.message : '未知错误'))
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 配置卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            DeepSeek API 配置
          </CardTitle>
          <CardDescription>
            配置DeepSeek V3 API用于AI风口识别
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* API Key输入 */}
          <div className="space-y-2">
            <Label htmlFor="apiKey">API Key</Label>
            <div className="relative">
              <Input
                id="apiKey"
                type={showApiKey ? "text" : "password"}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            <p className="text-sm text-gray-600">
              从 <a href="https://platform.deepseek.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">DeepSeek平台</a> 获取您的API Key
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-3">
            <Button
              onClick={handleTestConnection}
              disabled={testing || !apiKey.trim()}
              variant="outline"
              className="flex-1"
            >
              <TestTube className="h-4 w-4 mr-2" />
              {testing ? '测试中...' : '测试连接'}
            </Button>
            <Button
              onClick={handleSaveConfig}
              disabled={saving || !apiKey.trim()}
              className="flex-1"
            >
              {saving ? '保存中...' : '保存配置'}
            </Button>
          </div>

          {/* 测试结果 */}
          {testResult && (
            <div className={`p-4 rounded-lg border ${
              testResult.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center mb-2">
                {testResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                )}
                <span className={`font-medium ${
                  testResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {testResult.message}
                </span>
              </div>
              
              {testResult.details && (
                <div className="text-sm text-gray-600 space-y-1">
                  {testResult.success && (
                    <>
                      <div>置信度: {testResult.details.confidence}</div>
                      <div>识别项目数: {testResult.details.projectsCount}</div>
                      {testResult.details.warnings?.length > 0 && (
                        <div>警告: {testResult.details.warnings.join(', ')}</div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Badge variant="outline" className="mt-1">1</Badge>
              <div>
                <p className="font-medium">注册DeepSeek账号</p>
                <p className="text-sm text-gray-600">访问 https://platform.deepseek.com 注册账号</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Badge variant="outline" className="mt-1">2</Badge>
              <div>
                <p className="font-medium">完成实名认证</p>
                <p className="text-sm text-gray-600">上传身份证完成实名认证</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Badge variant="outline" className="mt-1">3</Badge>
              <div>
                <p className="font-medium">获取API Key</p>
                <p className="text-sm text-gray-600">在控制台创建API Key</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Badge variant="outline" className="mt-1">4</Badge>
              <div>
                <p className="font-medium">充值账户</p>
                <p className="text-sm text-gray-600">充值100-200元用于API调用（每次识别约0.01-0.02元）</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">💡 成本预估</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <div>• 单次识别成本：约 ¥0.01-0.02</div>
              <div>• 月处理1000单：约 ¥10-20</div>
              <div>• 推荐充值：¥100-200（可用很久）</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
