/**
 * 🇨🇳 风口云平台 - 用户会话隔离管理器
 * 
 * 功能说明：
 * - 为每个用户创建独立的认证存储空间
 * - 防止不同用户的会话相互覆盖
 * - 支持同一设备多用户登录
 */

import type { AuthRole } from '@/lib/store/auth'

// 生成浏览器会话指纹（包含随机因子）
function generateBrowserSessionFingerprint(): string {
  if (typeof window === 'undefined') return 'server'

  try {
    // 获取或创建浏览器会话唯一标识
    let browserSessionId = sessionStorage.getItem('browser_session_id')
    if (!browserSessionId) {
      browserSessionId = Math.random().toString(36).substring(2, 15) + Date.now().toString(36)
      sessionStorage.setItem('browser_session_id', browserSessionId)
    }

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx?.fillText('fingerprint', 10, 10)
    const canvasFingerprint = canvas.toDataURL()

    const fingerprint = btoa(JSON.stringify({
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvasFingerprint.slice(-50),
      browserSessionId, // 🔧 新增：浏览器会话唯一标识
      tabId: Math.random().toString(36).substring(2, 10) // 🔧 新增：标签页唯一标识
    })).replace(/[^a-zA-Z0-9]/g, '').slice(0, 32)

    return fingerprint
  } catch (error) {
    console.warn('⚠️ 生成浏览器会话指纹失败:', error)
    return Math.random().toString(36).substring(2, 15)
  }
}

// 生成用户会话键（增强版）
function generateUserSessionKey(userId: string, role: AuthRole): string {
  const browserFingerprint = generateBrowserSessionFingerprint()
  const timestamp = Date.now()

  // 🔧 改进：增加时间戳和更多唯一性因素
  return `user_session_${userId}_${role}_${browserFingerprint}_${timestamp}`
}

// 用户会话数据接口
interface UserSessionData {
  userId: string
  username: string
  role: AuthRole
  factoryId?: string
  accessToken: string
  refreshToken: string
  user: any
  loginTime: number
  lastActiveTime: number
  deviceFingerprint: string
}

// 用户会话管理器
export class UserSessionManager {
  private static instance: UserSessionManager
  private currentSessionKey: string | null = null
  private sessionCleanupInterval: NodeJS.Timeout | null = null

  static getInstance(): UserSessionManager {
    if (!UserSessionManager.instance) {
      UserSessionManager.instance = new UserSessionManager()
    }
    return UserSessionManager.instance
  }

  constructor() {
    this.startSessionCleanup()
  }

  /**
   * 创建用户会话
   */
  createUserSession(
    user: any,
    role: AuthRole,
    tokens: { accessToken: string; refreshToken: string },
    factoryId?: string
  ): string {
    if (typeof window === 'undefined') return ''

    try {
      const sessionKey = generateUserSessionKey(user.id, role)
      const sessionData: UserSessionData = {
        userId: user.id,
        username: user.username || user.name,
        role,
        factoryId,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user,
        loginTime: Date.now(),
        lastActiveTime: Date.now(),
        deviceFingerprint: generateBrowserSessionFingerprint()
      }

      // 保存会话数据
      localStorage.setItem(sessionKey, JSON.stringify(sessionData))
      
      // 设置当前活跃会话
      this.setActiveSession(sessionKey)
      
      // 保存到会话列表
      this.addToSessionList(sessionKey, sessionData)
      
      console.log('✅ 用户会话已创建:', {
        sessionKey,
        userId: user.id,
        username: sessionData.username,
        role
      })
      
      return sessionKey
    } catch (error) {
      console.error('❌ 创建用户会话失败:', error)
      return ''
    }
  }

  /**
   * 获取当前活跃会话
   */
  getCurrentSession(): UserSessionData | null {
    if (typeof window === 'undefined') return null

    try {
      const activeSessionKey = localStorage.getItem('active_session_key')
      if (!activeSessionKey) return null

      const sessionData = localStorage.getItem(activeSessionKey)
      if (!sessionData) return null

      const session = JSON.parse(sessionData) as UserSessionData
      
      // 更新最后活跃时间
      session.lastActiveTime = Date.now()
      localStorage.setItem(activeSessionKey, JSON.stringify(session))
      
      return session
    } catch (error) {
      console.error('❌ 获取当前会话失败:', error)
      return null
    }
  }

  /**
   * 设置活跃会话
   */
  setActiveSession(sessionKey: string): void {
    if (typeof window === 'undefined') return

    try {
      this.currentSessionKey = sessionKey
      localStorage.setItem('active_session_key', sessionKey)
      
      console.log('✅ 活跃会话已设置:', sessionKey)
    } catch (error) {
      console.error('❌ 设置活跃会话失败:', error)
    }
  }

  /**
   * 切换到指定用户会话
   */
  switchToUserSession(userId: string, role: AuthRole): boolean {
    if (typeof window === 'undefined') return false

    try {
      const sessionKey = generateUserSessionKey(userId, role)
      const sessionData = localStorage.getItem(sessionKey)
      
      if (sessionData) {
        this.setActiveSession(sessionKey)
        console.log('✅ 已切换到用户会话:', { userId, role })
        return true
      } else {
        console.warn('⚠️ 未找到用户会话:', { userId, role })
        return false
      }
    } catch (error) {
      console.error('❌ 切换用户会话失败:', error)
      return false
    }
  }

  /**
   * 删除用户会话
   */
  removeUserSession(sessionKey?: string): void {
    if (typeof window === 'undefined') return

    try {
      const targetKey = sessionKey || this.currentSessionKey
      if (!targetKey) return

      // 删除会话数据
      localStorage.removeItem(targetKey)
      
      // 从会话列表中移除
      this.removeFromSessionList(targetKey)
      
      // 如果删除的是当前活跃会话，清除活跃会话标记
      if (targetKey === this.currentSessionKey) {
        localStorage.removeItem('active_session_key')
        this.currentSessionKey = null
      }
      
      console.log('✅ 用户会话已删除:', targetKey)
    } catch (error) {
      console.error('❌ 删除用户会话失败:', error)
    }
  }

  /**
   * 获取所有用户会话
   */
  getAllUserSessions(): UserSessionData[] {
    if (typeof window === 'undefined') return []

    try {
      const sessionList = localStorage.getItem('user_session_list')
      if (!sessionList) return []

      const sessionKeys = JSON.parse(sessionList) as string[]
      const sessions: UserSessionData[] = []

      for (const key of sessionKeys) {
        try {
          const sessionData = localStorage.getItem(key)
          if (sessionData) {
            sessions.push(JSON.parse(sessionData))
          }
        } catch (error) {
          console.warn('⚠️ 解析会话数据失败:', key, error)
        }
      }

      return sessions
    } catch (error) {
      console.error('❌ 获取用户会话列表失败:', error)
      return []
    }
  }

  /**
   * 清理过期会话
   */
  cleanupExpiredSessions(): void {
    if (typeof window === 'undefined') return

    try {
      const sessions = this.getAllUserSessions()
      const now = Date.now()
      const maxInactiveTime = 24 * 60 * 60 * 1000 // 24小时

      for (const session of sessions) {
        if (now - session.lastActiveTime > maxInactiveTime) {
          const sessionKey = generateUserSessionKey(session.userId, session.role)
          this.removeUserSession(sessionKey)
          console.log('🧹 已清理过期会话:', sessionKey)
        }
      }
    } catch (error) {
      console.error('❌ 清理过期会话失败:', error)
    }
  }

  /**
   * 添加到会话列表
   */
  private addToSessionList(sessionKey: string, sessionData: UserSessionData): void {
    try {
      const sessionList = localStorage.getItem('user_session_list')
      const sessionKeys = sessionList ? JSON.parse(sessionList) : []
      
      if (!sessionKeys.includes(sessionKey)) {
        sessionKeys.push(sessionKey)
        localStorage.setItem('user_session_list', JSON.stringify(sessionKeys))
      }
    } catch (error) {
      console.error('❌ 添加到会话列表失败:', error)
    }
  }

  /**
   * 从会话列表中移除
   */
  private removeFromSessionList(sessionKey: string): void {
    try {
      const sessionList = localStorage.getItem('user_session_list')
      if (sessionList) {
        const sessionKeys = JSON.parse(sessionList) as string[]
        const updatedKeys = sessionKeys.filter(key => key !== sessionKey)
        localStorage.setItem('user_session_list', JSON.stringify(updatedKeys))
      }
    } catch (error) {
      console.error('❌ 从会话列表移除失败:', error)
    }
  }

  /**
   * 启动会话清理定时器
   */
  private startSessionCleanup(): void {
    if (typeof window === 'undefined') return

    // 每小时清理一次过期会话
    this.sessionCleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions()
    }, 60 * 60 * 1000)
  }

  /**
   * 停止会话清理定时器
   */
  stopSessionCleanup(): void {
    if (this.sessionCleanupInterval) {
      clearInterval(this.sessionCleanupInterval)
      this.sessionCleanupInterval = null
    }
  }
}

// 导出单例实例
export const userSessionManager = UserSessionManager.getInstance()
