/**
 * 🇨🇳 风口云平台 - 登录记录API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 获取查询参数
    const factoryId = searchParams.get('factoryId')
    const userType = searchParams.get('userType') as 'admin' | 'factory_user' | null
    const loginStatus = searchParams.get('loginStatus') as 'success' | 'failed' | 'blocked' | null
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const limit = parseInt(searchParams.get('limit') || '100')
    const offset = parseInt(searchParams.get('offset') || '0')

    // 构建查询选项
    const options: unknown = {
      limit,
      offset
    }

    if (factoryId) options.factoryId = factoryId
    if (userType) options.userType = userType
    if (loginStatus) options.loginStatus = loginStatus
    if (startDate) options.startDate = new Date(startDate)
    if (endDate) options.endDate = new Date(endDate)

    // 获取登录记录
    const records = await db.getLoginRecords(options)

    return NextResponse.json({
      success: true,
      data: records,
      total: records.length
    })

  } catch (error) {
    console.error('❌ 获取登录记录失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: '获取登录记录失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const loginData = await request.json()

    // 获取客户端IP和User-Agent
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // 记录登录信息
    const record = await db.recordLogin({
      ...loginData,
      ipAddress: ip,
      userAgent: userAgent,
      deviceInfo: parseDeviceInfo(userAgent)
    })

    return NextResponse.json({
      success: true,
      data: record
    })

  } catch (error) {
    console.error('❌ 记录登录信息失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: '记录登录信息失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

// 解析设备信息的辅助函数
function parseDeviceInfo(userAgent: string): string {
  if (!userAgent) return 'Unknown Device'
  
  // 简单的设备检测
  if (userAgent.includes('Mobile')) return 'Mobile Device'
  if (userAgent.includes('Tablet')) return 'Tablet'
  if (userAgent.includes('Windows')) return 'Windows PC'
  if (userAgent.includes('Mac')) return 'Mac'
  if (userAgent.includes('Linux')) return 'Linux'
  
  return 'Desktop'
}
