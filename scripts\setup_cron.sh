#!/bin/bash
# setup_cron.sh - 设置定时任务脚本

echo "⏰ 设置风口云平台定时任务..."

# 获取项目根目录
PROJECT_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
SCRIPTS_DIR="$PROJECT_ROOT/scripts"

# 确保脚本有执行权限
chmod +x "$SCRIPTS_DIR"/*.sh

# 创建日志目录
mkdir -p "$PROJECT_ROOT/logs/cron"

# 定时任务配置
CRON_JOBS="
# 风口云平台定时任务
# 每天凌晨2点检查更新
0 2 * * * $SCRIPTS_DIR/auto_update.sh >> $PROJECT_ROOT/logs/cron/auto_update.log 2>&1

# 每小时进行健康检查
0 * * * * $SCRIPTS_DIR/health_check.sh >> $PROJECT_ROOT/logs/cron/health_check.log 2>&1

# 每天凌晨3点清理日志
0 3 * * * find $PROJECT_ROOT/logs -name '*.log' -mtime +7 -delete

# 每周日凌晨4点清理旧备份
0 4 * * 0 find /backups -type d -mtime +30 -exec rm -rf {} \; 2>/dev/null

# 每天凌晨1点创建数据库备份
0 1 * * * $SCRIPTS_DIR/backup.sh >> $PROJECT_ROOT/logs/cron/backup.log 2>&1
"

# 添加到crontab
echo "添加定时任务到crontab..."
(crontab -l 2>/dev/null | grep -v "风口云平台定时任务" | grep -v "$SCRIPTS_DIR"; echo "$CRON_JOBS") | crontab -

echo "✅ 定时任务设置完成"
echo ""
echo "📋 已设置的定时任务:"
echo "  - 每天凌晨2点: 自动检查更新"
echo "  - 每小时: 健康检查"
echo "  - 每天凌晨3点: 清理旧日志"
echo "  - 每周日凌晨4点: 清理旧备份"
echo "  - 每天凌晨1点: 数据库备份"
echo ""
echo "📁 日志位置: $PROJECT_ROOT/logs/cron/"
echo ""
echo "🔍 查看当前crontab: crontab -l"
echo "📝 编辑crontab: crontab -e"
