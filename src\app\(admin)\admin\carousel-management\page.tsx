'use client'

/**
 * 🇨🇳 风口云平台 - 轮播图管理页面（数据库版本）
 * 
 * 功能说明：
 * - 使用数据库持久化存储轮播图片
 * - 支持图片上传、预览、删除
 * - 自动图片压缩和格式验证
 * - 实时预览轮播效果
 */

import React, { useState, useRef, useEffect, useCallback } from 'react'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Upload,
  X,
  Database,
  BarChart3,
  ImageIcon
} from "lucide-react"
import { Carousel } from '@/components/ui/carousel'
import { DashboardLayout } from '@/components/layout/dashboard-layout'

interface CarouselImage {
  id: string
  name: string
  imageData: string
  size: number
  width?: number
  height?: number
  category: 'product' | 'process' | 'other'
  createdAt: string
}

interface StorageStats {
  totalImages: number
  totalSize: number
  categories: {
    product: number
    process: number
    other: number
  }
}

export default function CarouselManagementPage() {
  const [category, setCategory] = useState<'product' | 'process' | 'other'>('product')
  const [images, setImages] = useState<CarouselImage[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [uploadStatus, setUploadStatus] = useState('')
  const [stats, setStats] = useState<StorageStats | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 加载图片列表
  const loadImages = useCallback(async () => {
    try {
      const response = await fetch(`/api/carousel-images?category=${category}`)
      const data = await response.json()

      if (data.success) {
        setImages(data.images)
      } else {
        console.error('加载图片失败:', data.error)
      }
    } catch (error) {
      console.error('加载图片失败:', error)
    }
  }, [category])

  // 加载存储统计
  const loadStats = useCallback(async () => {
    try {
      const response = await fetch('/api/carousel-images/stats')
      const data = await response.json()

      if (data.success) {
        setStats(data.stats)
      }
    } catch (error) {
      console.error('加载统计失败:', error)
    }
  }, [])

  useEffect(() => {
    loadImages()
    loadStats()
  }, [loadImages, loadStats])

  // 图片压缩函数
  const compressImage = (file: File, maxWidth: number = 1200, maxHeight: number = 800, quality: number = 0.8): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        // 如果无法获取canvas context，返回原文件的base64
        const reader = new FileReader()
        reader.onload = () => resolve(reader.result as string)
        reader.readAsDataURL(file)
        return
      }
      const img = new window.Image()
      
      img.onload = () => {
        // 计算新尺寸
        let { width, height } = img
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width
            width = maxWidth
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height
            height = maxHeight
          }
        }
        
        canvas.width = width
        canvas.height = height
        
        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, width, height)
        
        // 转换为Base64
        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality)
        resolve(compressedDataUrl)
      }
      
      img.src = URL.createObjectURL(file)
    })
  }

  // 处理文件上传
  const handleFileUpload = async (files: FileList) => {
    if (!files.length) return

    setIsUploading(true)
    setUploadStatus('正在处理图片...')

    try {
      for (const file of Array.from(files)) {
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
          setUploadStatus(`❌ ${file.name} 不是有效的图片文件`)
          continue
        }

        // 验证文件大小 (5MB)
        if (file.size > 5 * 1024 * 1024) {
          setUploadStatus(`❌ ${file.name} 文件过大，请选择小于5MB的图片`)
          continue
        }

        setUploadStatus(`📷 正在压缩 ${file.name}...`)
        
        // 压缩图片
        const compressedImageData = await compressImage(file)
        
        setUploadStatus(`☁️ 正在上传 ${file.name}...`)
        
        // 上传到服务器
        const response = await fetch('/api/carousel-images', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: file.name,
            imageData: compressedImageData,
            size: file.size,
            category: category
          })
        })

        const result = await response.json()
        
        if (result.success) {
          setUploadStatus(`✅ ${file.name} 上传成功`)
        } else {
          setUploadStatus(`❌ ${file.name} 上传失败: ${result.error}`)
        }
      }

      // 重新加载图片列表和统计
      await loadImages()
      await loadStats()
      
      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }

    } catch (error) {
      console.error('上传失败:', error)
      setUploadStatus(`❌ 上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsUploading(false)
      
      // 3秒后清空状态消息
      setTimeout(() => {
        setUploadStatus('')
      }, 3000)
    }
  }

  // 删除图片
  const handleDeleteImage = async (imageId: string) => {
    if (!confirm('确定要删除这张图片吗？')) return

    try {
      const response = await fetch(`/api/carousel-images/${imageId}`, {
        method: 'DELETE'
      })

      const result = await response.json()
      
      if (result.success) {
        setUploadStatus('✅ 图片删除成功')
        await loadImages()
        await loadStats()
      } else {
        setUploadStatus(`❌ 删除失败: ${result.error}`)
      }
    } catch (error) {
      console.error('删除失败:', error)
      setUploadStatus(`❌ 删除失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }

    // 3秒后清空状态消息
    setTimeout(() => {
      setUploadStatus('')
    }, 3000)
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取分类名称
  const getCategoryName = (cat: string): string => {
    const names = {
      product: '产品展示',
      process: '工艺展示', 
      other: '其他图片'
    }
    return names[cat as keyof typeof names] || cat
  }

  // 获取轮播图片URL列表
  const getCarouselImages = (): string[] => {
    return images.map(img => img.imageData)
  }

  return (
    <DashboardLayout role="admin">
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">轮播图管理</h1>
            <p className="text-gray-600 mt-2">管理首页和客户查询页面的轮播图片（数据库存储版本）</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Database className="w-4 h-4" />
            <span>数据库持久化存储</span>
          </div>
        </div>

        {/* 存储统计 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <ImageIcon className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">总图片数</p>
                    <p className="text-xl font-bold">{stats.totalImages}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600">总存储</p>
                    <p className="text-xl font-bold">{formatFileSize(stats.totalSize)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="text-sm text-gray-600">产品图片</p>
                    <p className="text-xl font-bold">{stats.categories.product}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="text-sm text-gray-600">工艺图片</p>
                    <p className="text-xl font-bold">{stats.categories.process}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 分类选择 */}
        <Tabs value={category} onValueChange={(value) => setCategory(value as 'product' | 'process' | 'other')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="product">产品展示</TabsTrigger>
            <TabsTrigger value="process">工艺展示</TabsTrigger>
            <TabsTrigger value="other">其他图片</TabsTrigger>
          </TabsList>

          <TabsContent value={category} className="space-y-6">
            {/* 上传区域 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Upload className="w-5 h-5" />
                  <span>上传 {getCategoryName(category)}</span>
                </CardTitle>
                <CardDescription>
                  支持 JPG、PNG、GIF 格式，单个文件不超过 5MB，自动压缩优化
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 状态显示 */}
                {uploadStatus && (
                  <div className={`p-3 rounded-lg ${
                    uploadStatus.includes('✅') ? 'bg-green-50 text-green-800' :
                    uploadStatus.includes('❌') ? 'bg-red-50 text-red-800' :
                    'bg-blue-50 text-blue-800'
                  }`}>
                    {uploadStatus}
                  </div>
                )}

                {/* 上传区域 */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      if (e.target.files) {
                        handleFileUpload(e.target.files)
                      }
                    }}
                  />
                  
                  <div className="space-y-4">
                    <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                      <Upload className="w-8 h-8 text-gray-600" />
                    </div>
                    <div>
                      <p className="text-lg font-medium">点击上传图片</p>
                      <p className="text-sm text-gray-600">或拖拽图片到此区域</p>
                    </div>
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isUploading}
                      size="lg"
                    >
                      {isUploading ? '上传中...' : '选择图片'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 图片列表 */}
        <Card>
          <CardHeader>
            <CardTitle>
              {getCategoryName(category)} ({images.length})
            </CardTitle>
            <CardDescription>
              当前分类的所有图片
            </CardDescription>
          </CardHeader>
          <CardContent>
            {images.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <ImageIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>暂无图片，请上传图片</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {images.map((image) => (
                  <div key={image.id} className="border rounded-lg overflow-hidden">
                    <div className="aspect-video bg-gray-100 relative">
                      {image.imageData.startsWith('data:') ? (
                        // Base64图片使用img标签
                        <img
                          src={image.imageData}
                          alt={image.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        // 外部URL使用Next.js Image组件
                        <Image
                          src={image.imageData}
                          alt={image.name}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      )}
                      <div className="absolute top-2 right-2">
                        <Button
                          size="sm"
                          variant="destructive"
                          className="w-8 h-8 p-0"
                          onClick={() => handleDeleteImage(image.id)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-3 space-y-2">
                      <p className="font-medium text-sm truncate">{image.name}</p>
                      <p className="text-gray-500 text-xs">{formatFileSize(image.size)}</p>
                      {image.width && image.height && (
                        <p className="text-gray-500 text-xs">{image.width} × {image.height}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 轮播预览 */}
        {images.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>轮播预览</CardTitle>
              <CardDescription>
                预览当前分类的轮播效果
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="max-w-2xl mx-auto">
                <Carousel
                  images={getCarouselImages()}
                  autoPlay={true}
                  interval={3000}
                  showDots={true}
                  showArrows={true}
                  className="h-64 sm:h-80 md:h-96"
                  imageClassName="object-contain"
                />
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
