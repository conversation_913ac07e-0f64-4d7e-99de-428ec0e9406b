'use client'

/**
 * 🇨🇳 风口云平台 - 主题隔离测试页面
 * 
 * 功能说明：
 * - 测试用户主题隔离功能
 * - 显示当前用户的主题设置
 * - 提供主题切换和测试工具
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useUserTheme } from '@/components/theme/user-theme-provider'
import { useAuthStore } from '@/lib/store/auth'
import {
  useUserThemeStore,
  getUserThemeKey,
  loadUserTheme
} from '@/lib/store/user-theme'
import { getAllUserThemeKeys } from '@/lib/utils/theme-manager'
import { UserSessionSwitcher, UserSessionIndicator } from '@/components/auth/user-session-switcher'
import { userSessionManager } from '@/lib/utils/user-session-manager'

export default function ThemeTestPage() {
  const { mode, setTheme } = useUserTheme()
  const { user, role, factoryId } = useAuthStore()
  const [userThemeKey, setUserThemeKey] = useState<string>('')
  const [allThemeKeys, setAllThemeKeys] = useState<string[]>([])
  const [userThemeSettings, setUserThemeSettings] = useState<any>(null)
  const [userSessions, setUserSessions] = useState<any[]>([])

  // 获取用户主题store状态
  const userThemes = useUserThemeStore(state => state.userThemes)
  const currentUserKey = useUserThemeStore(state => state.currentUserKey)

  // 刷新主题信息
  const refreshThemeInfo = () => {
    const key = getUserThemeKey()
    const settings = loadUserTheme(key)
    const allKeys = getAllUserThemeKeys()
    const sessions = userSessionManager.getAllUserSessions()

    setUserThemeKey(key)
    setUserThemeSettings(settings)
    setAllThemeKeys(allKeys)
    setUserSessions(sessions)
  }

  useEffect(() => {
    refreshThemeInfo()
  }, [user, role, factoryId])

  // 清理所有主题数据
  const clearAllThemeData = () => {
    if (typeof window !== 'undefined') {
      const keys = getAllUserThemeKeys()
      keys.forEach(key => {
        localStorage.removeItem(key)
      })
      // 也清理旧的全局主题数据
      localStorage.removeItem('theme-storage')
      refreshThemeInfo()
      alert('已清理所有主题数据')
    }
  }

  // 模拟用户切换
  const simulateUserSwitch = () => {
    const store = useUserThemeStore.getState()
    store.setCurrentUser(getUserThemeKey())
    refreshThemeInfo()
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">主题隔离测试</h1>
          <p className="text-muted-foreground mt-2">
            测试每个用户独立的主题设置功能和会话管理
          </p>
        </div>
        <div className="flex items-center gap-2">
          <UserSessionIndicator />
          <UserSessionSwitcher />
          <Button onClick={refreshThemeInfo} variant="outline">
            刷新信息
          </Button>
        </div>
      </div>

      {/* 当前用户信息 */}
      <Card>
        <CardHeader>
          <CardTitle>当前用户信息</CardTitle>
          <CardDescription>显示当前登录用户的基本信息</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">用户名</label>
              <p className="text-lg">{user?.name || '未登录'}</p>
            </div>
            <div>
              <label className="text-sm font-medium">用户ID</label>
              <p className="text-lg">{user?.id || '无'}</p>
            </div>
            <div>
              <label className="text-sm font-medium">角色</label>
              <Badge variant="outline">{role || '无'}</Badge>
            </div>
            <div>
              <label className="text-sm font-medium">工厂ID</label>
              <p className="text-lg">{factoryId || '无'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主题设置信息 */}
      <Card>
        <CardHeader>
          <CardTitle>主题设置信息</CardTitle>
          <CardDescription>显示当前用户的主题设置详情</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">当前主题模式</label>
              <Badge variant={mode === 'dark' ? 'default' : 'secondary'}>
                {mode === 'light' ? '浅色' : mode === 'dark' ? '深色' : '自动'}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium">用户主题键</label>
              <p className="text-sm font-mono bg-muted p-2 rounded">
                {userThemeKey}
              </p>
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium">用户主题设置</label>
            <pre className="text-sm bg-muted p-3 rounded mt-2 overflow-auto">
              {JSON.stringify(userThemeSettings, null, 2)}
            </pre>
          </div>

          <div>
            <label className="text-sm font-medium">当前用户主题Store</label>
            <pre className="text-sm bg-muted p-3 rounded mt-2 overflow-auto">
              {JSON.stringify({
                currentUserKey,
                currentTheme: userThemes[currentUserKey],
                allUserThemes: userThemes
              }, null, 2)}
            </pre>
          </div>

          <div>
            <label className="text-sm font-medium">用户会话列表</label>
            <pre className="text-sm bg-muted p-3 rounded mt-2 overflow-auto">
              {JSON.stringify(userSessions.map(session => ({
                userId: session.userId,
                username: session.username,
                role: session.role,
                factoryId: session.factoryId,
                loginTime: new Date(session.loginTime).toLocaleString('zh-CN'),
                lastActiveTime: new Date(session.lastActiveTime).toLocaleString('zh-CN')
              })), null, 2)}
            </pre>
          </div>
        </CardContent>
      </Card>

      {/* 主题切换测试 */}
      <Card>
        <CardHeader>
          <CardTitle>主题切换测试</CardTitle>
          <CardDescription>测试主题切换功能，验证设置是否正确保存</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={() => setTheme('light')}
              variant={mode === 'light' ? 'default' : 'outline'}
            >
              浅色主题
            </Button>
            <Button 
              onClick={() => setTheme('dark')}
              variant={mode === 'dark' ? 'default' : 'outline'}
            >
              深色主题
            </Button>
            <Button 
              onClick={() => setTheme('auto')}
              variant={mode === 'auto' ? 'default' : 'outline'}
            >
              自动主题
            </Button>
          </div>
          
          <p className="text-sm text-muted-foreground">
            切换主题后，设置会自动保存到当前用户的独立存储中
          </p>
        </CardContent>
      </Card>

      {/* 系统主题数据 */}
      <Card>
        <CardHeader>
          <CardTitle>系统主题数据</CardTitle>
          <CardDescription>显示系统中所有用户的主题存储键</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">所有主题存储键 ({allThemeKeys.length})</label>
            <div className="mt-2 space-y-1">
              {allThemeKeys.length > 0 ? (
                allThemeKeys.map((key, index) => (
                  <div key={index} className="text-sm font-mono bg-muted p-2 rounded">
                    {key}
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground">没有找到主题存储数据</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试工具 */}
      <Card>
        <CardHeader>
          <CardTitle>测试工具</CardTitle>
          <CardDescription>提供各种测试功能</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={simulateUserSwitch} variant="outline">
              模拟用户切换
            </Button>
            <Button onClick={clearAllThemeData} variant="destructive">
              清理所有主题数据
            </Button>
          </div>
          
          <div className="text-sm text-muted-foreground space-y-1">
            <p>• <strong>模拟用户切换</strong>：重新初始化当前用户的主题设置</p>
            <p>• <strong>清理所有主题数据</strong>：删除所有用户的主题存储数据（谨慎使用）</p>
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
          <CardDescription>如何测试主题隔离功能</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm space-y-2">
            <p><strong>1. 单用户测试：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>切换不同的主题模式，观察设置是否正确保存</li>
              <li>刷新页面，检查主题设置是否持久化</li>
              <li>查看"用户主题键"，确认使用了用户特定的存储键</li>
            </ul>
            
            <p><strong>2. 多用户测试：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>使用不同账号登录，设置不同的主题</li>
              <li>切换账号，检查主题是否独立保存</li>
              <li>观察"所有主题存储键"，确认每个用户都有独立的存储</li>
            </ul>
            
            <p><strong>3. 验证隔离：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>确认同一工厂的不同用户主题设置互不影响</li>
              <li>确认不同工厂的用户主题设置完全隔离</li>
              <li>确认管理员和工厂用户的主题设置独立</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
