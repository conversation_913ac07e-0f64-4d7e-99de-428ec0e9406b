/**
 * 🇨🇳 风口云平台 - 活跃公告API
 * 
 * 获取工厂活跃公告（排除已读和已关闭的公告）
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')

    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '缺少工厂ID参数'
      }, { status: 400 })
    }

    console.log('📋 API: 获取工厂活跃公告:', factoryId)

    // 获取工厂活跃公告
    const activeAnnouncements = await db.getActiveAnnouncementsForFactory(factoryId)

    console.log('✅ API: 活跃公告获取成功:', activeAnnouncements.length, '条')

    return NextResponse.json({
      success: true,
      data: {
        announcements: activeAnnouncements
      }
    })

  } catch (error) {
    console.error('❌ API: 获取活跃公告失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '获取活跃公告失败'
    }, { status: 500 })
  }
}
