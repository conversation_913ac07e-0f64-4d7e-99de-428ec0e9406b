"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle  } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  User,
  Phone,
  Mail,
  Building,
  MapPin,
  ShoppingCart,
  DollarSign,
  Gift,
  AlertTriangle,
  Calendar,
  TrendingUp,
  Loader2,
  History,
  CheckCircle,
  Clock
} from "lucide-react"
import { db } from "@/lib/database"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import { safeNumber, safeAmountFormat } from "@/lib/utils/number-utils"
import { calculateTotalReferralReward } from "@/lib/utils/reward-calculator"
import { api } from "@/lib/api/client"
import type { Client, Order } from "@/types"
import { RewardUsageDialog } from "@/components/reward/RewardUsageDialog"
import { RewardUsageHistory } from "@/components/reward/RewardUsageHistory"

import { safeAmountSum } from "@/lib/utils/number-utils"
import { useAuthStore } from "@/lib/store/auth"

interface ClientDetailDialogProps {
  client: Client | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ClientDetailDialog({ client, open, onOpenChange }: ClientDetailDialogProps) {
  const [loading, setLoading] = useState(false)
  const [clientOrders, setClientOrders] = useState<Order[]>([])
  const [referredClients, setReferredClients] = useState<Client[]>([])
  const [referredClientsOrders, setReferredClientsOrders] = useState<{[clientId: string]: Order[]}>({})
  const [rewardUsageDialogOpen, setRewardUsageDialogOpen] = useState(false)
  const [currentClient, setCurrentClient] = useState<Client | null>(client)
  const [statistics, setStatistics] = useState({
    totalOrders: 0,
    totalAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0,
    overdueAmount: 0,
    referralReward: 0,
    referralCount: 0
  })

  // 🆕 添加实时计算的推荐奖励状态
  const [rewardStats, setRewardStats] = useState({
    totalReward: 0,
    availableReward: 0,
    pendingReward: 0,
    usedReward: 0
  })

  // 加载客户详细数据
  const loadClientDetails = async () => {
    if (!client) return

    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()

      if (!factoryId || !db) {
        console.warn('无法获取工厂ID或数据库服务不可用')
        return
      }

      // 获取所有订单数据（用于推荐奖励计算）
      const allOrders = await db.getOrdersByFactoryId(factoryId)

      // 使用新的根据客户ID获取订单的方法
      let orders: Order[] = []
      if (typeof db.getOrdersByClientId === 'function') {
        console.log('🔄 使用客户ID获取订单:', client.id)
        orders = await db.getOrdersByClientId(client.id)
      } else {
        // 回退到原来的方法
        console.log('🔄 回退到工厂订单筛选方法')
        orders = allOrders.filter(order => order.clientId === client.id)
      }

      setClientOrders(orders)
      console.log('✅ 客户订单加载成功:', orders.length, '条')

      // 获取该客户推荐的其他客户
      const allClients = await db.getClientsByFactoryId(factoryId)
      const referred = allClients.filter(c => c.referrerId === client.id)
      setReferredClients(referred)

      // 🆕 为每个推荐客户获取订单数据
      const referredOrdersMap: {[clientId: string]: Order[]} = {}
      for (const referredClient of referred) {
        try {
          let referredOrders: Order[] = []
          if (typeof db.getOrdersByClientId === 'function') {
            referredOrders = await db.getOrdersByClientId(referredClient.id)
          } else {
            referredOrders = allOrders.filter(order => order.clientId === referredClient.id)
          }
          referredOrdersMap[referredClient.id] = referredOrders
          console.log(`📋 推荐客户 ${referredClient.name} 的订单:`, referredOrders.length, '条')
        } catch (error) {
          console.error(`获取推荐客户 ${referredClient.name} 的订单失败:`, error)
          referredOrdersMap[referredClient.id] = []
        }
      }
      setReferredClientsOrders(referredOrdersMap)

      // 重新获取最新的客户信息（包含最新的奖励余额）
      const updatedClient = allClients.find(c => c.id === client.id)
      if (updatedClient) {
        setCurrentClient(updatedClient)
      }

      // 🆕 获取实时推荐奖励状态 - 修复：通过API调用避免客户端使用Prisma
      try {
        console.log('🧮 开始获取推荐奖励状态 - 客户作为推荐人:', client.name)

        // 🔧 修复：使用API调用而不是直接在客户端使用Prisma
        const response = await fetch(`/api/clients/${client.id}/reward-status?factoryId=${factoryId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          }
        })

        if (!response.ok) {
          throw new Error(`API调用失败: ${response.status}`)
        }

        const result = await response.json()

        if (!result.success) {
          throw new Error(result.error || '获取奖励状态失败')
        }

        const rewardResult = result.data

        console.log('💰 API推荐奖励计算成功:', {
          totalReward: rewardResult.totalReward,
          availableReward: rewardResult.availableReward,
          pendingReward: rewardResult.pendingReward,
          settledClients: rewardResult.settledClients,
          totalClients: rewardResult.totalClients
        })

        setRewardStats({
          totalReward: rewardResult.totalReward,
          availableReward: rewardResult.availableReward,
          pendingReward: rewardResult.pendingReward,
          usedReward: rewardResult.usedReward // 🔧 使用API返回的已使用奖励
        })

      } catch (error) {
        console.error('❌ 实时推荐奖励计算失败:', error)

        // 备用方案：使用数据库中的数据
        console.log('🔄 使用数据库备用数据:', {
          referralReward: updatedClient?.referralReward,
          availableReward: updatedClient?.availableReward,
          pendingReward: updatedClient?.pendingReward,
          usedReward: updatedClient?.usedReward
        })

        setRewardStats({
          totalReward: (updatedClient?.referralReward as number) || 0,
          availableReward: (updatedClient?.availableReward as number) || 0,
          pendingReward: (updatedClient?.pendingReward as number) || 0,
          usedReward: (updatedClient?.usedReward as number) || 0
        })
      }

      // 计算统计数据
      const totalAmount = orders.reduce((sum, order) => sum + Number(order.totalAmount || 0), 0)
      const paidAmount = orders.reduce((sum, order) => sum + Number(order.paidAmount || 0), 0)
      const unpaidAmount = totalAmount - paidAmount

      // 计算逾期金额（假设30天为逾期）
      const now = new Date()
      const overdueAmount = orders
        .filter(order => {
          if (!order.dueDate) return false
          return new Date(order.dueDate) < now && (order.paymentStatus === 'unpaid' || order.paymentStatus === 'partial')
        })
        .reduce((sum, order) => sum + (Number(order.totalAmount || 0) - Number(order.paidAmount || 0)), 0)

      // 使用最新的客户数据（如果有的话）
      const clientData = updatedClient || client
      const referralReward = safeNumber(clientData.referralReward || 0)

      setStatistics({
        totalOrders: orders.length,
        totalAmount,
        paidAmount,
        unpaidAmount,
        overdueAmount,
        referralReward,
        referralCount: referred.length
      })

    } catch (error) {
      console.error('加载客户详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 当客户或对话框状态改变时加载数据
  useEffect(() => {
    if (open && client) {
      setCurrentClient(client) // 初始化当前客户数据
      loadClientDetails()
    }
  }, [open, client])

  if (!client) return null

  // 获取付款状态颜色
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'partial': return 'bg-yellow-100 text-yellow-800'
      case 'unpaid': return 'bg-red-100 text-red-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // 获取付款状态文本
  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'paid': return '已付清'
      case 'partial': return '部分付款'
      case 'unpaid': return '未付款'
      case 'overdue': return '逾期'
      default: return '未知'
    }
  }

  // 格式化金额显示，去除前导零
  const formatAmount = (amount: unknown): string => {
    return safeAmountFormat(amount, 1)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>{client.name}</span>
          </DialogTitle>
          <DialogDescription>
            客户详细信息、订单汇总和财务状况
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">加载客户详情中...</span>
          </div>
        ) : (
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">概览</TabsTrigger>
              <TabsTrigger value="orders">订单</TabsTrigger>
              <TabsTrigger value="referrals">推荐</TabsTrigger>
              <TabsTrigger value="finance">财务</TabsTrigger>
            </TabsList>

            {/* 概览标签页 */}
            <TabsContent value="overview" className="space-y-4">
              {/* 基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span>{client.phone}</span>
                    </div>
                    {client.email && (
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span>{client.email}</span>
                      </div>
                    )}
                    {client.company && (
                      <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4 text-gray-500" />
                        <span>{client.company}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span>{client.address}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>注册时间：{new Date(client.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 统计卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold text-blue-600">{statistics.totalOrders}</div>
                    <p className="text-sm text-gray-600">总订单数</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold text-green-600">¥{formatAmount(statistics.totalAmount)}</div>
                    <p className="text-sm text-gray-600">总消费金额</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold text-purple-600">{statistics.referralCount}</div>
                    <p className="text-sm text-gray-600">推荐客户数</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold text-orange-600">¥{formatAmount(rewardStats.totalReward)}</div>
                    <p className="text-sm text-gray-600">推荐奖励</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* 订单标签页 */}
            <TabsContent value="orders" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>订单列表</CardTitle>
                  <CardDescription>该客户的所有订单记录</CardDescription>
                </CardHeader>
                <CardContent>
                  {clientOrders.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      暂无订单记录
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {clientOrders.map((order) => {
                        // 🔧 修复：实时计算付款状态，确保显示准确，使用safeNumber处理数字精度
                        const totalAmount = safeNumber(order.totalAmount)
                        const paidAmount = safeNumber(order.paidAmount)
                        const unpaidAmount = totalAmount - paidAmount

                        let realTimePaymentStatus = 'unpaid'
                        // 使用精度处理避免浮点数比较问题
                        if (Math.abs(paidAmount - totalAmount) < 0.01 || paidAmount >= totalAmount) {
                          realTimePaymentStatus = 'paid'
                        } else if (paidAmount > 0) {
                          realTimePaymentStatus = 'partial'
                        }

                        // 检查是否逾期
                        if (order.dueDate && new Date(order.dueDate) < new Date() && realTimePaymentStatus !== 'paid') {
                          realTimePaymentStatus = 'overdue'
                        }

                        return (
                          <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                            <div>
                              <div className="font-medium">{order.orderNumber || order.id}</div>
                              <div className="text-sm text-gray-600">
                                {new Date(order.createdAt).toLocaleDateString()} · {order.items.length} 项产品
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                已付：¥{formatAmount(paidAmount)} / 总额：¥{formatAmount(totalAmount)}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-medium">¥{formatAmount(totalAmount)}</div>
                              <Badge className={getPaymentStatusColor(realTimePaymentStatus)}>
                                {getPaymentStatusText(realTimePaymentStatus)}
                              </Badge>
                              {unpaidAmount > 0 && (
                                <div className="text-xs text-red-600 mt-1">
                                  待付：¥{formatAmount(unpaidAmount)}
                                </div>
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* 推荐标签页 */}
            <TabsContent value="referrals" className="space-y-4">
              {/* 推荐奖励概览 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center">
                      <Gift className="mr-2 h-5 w-5" />
                      推荐奖励
                    </span>
                    {rewardStats.availableReward > 0 && (
                      <Button
                        onClick={() => setRewardUsageDialogOpen(true)}
                        className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold px-6 py-2 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-2 border-green-400"
                        size="lg"
                      >
                        <Gift className="mr-2 h-5 w-5" />
                        <span className="text-base">使用奖励 ¥{formatAmount(rewardStats.availableReward)}</span>
                      </Button>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        ¥{formatAmount(rewardStats.availableReward)}
                      </div>
                      <div className="text-sm text-gray-600">可用奖励</div>
                      <div className="text-xs text-green-600 mt-1">
                        基于已结清客户
                      </div>
                    </div>
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        ¥{formatAmount(rewardStats.totalReward)}
                      </div>
                      <div className="text-sm text-gray-600">总奖励</div>
                      <div className="text-xs text-blue-600 mt-1">
                        所有推荐客户
                      </div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">
                        ¥{formatAmount(rewardStats.pendingReward)}
                      </div>
                      <div className="text-sm text-gray-600">待结算</div>
                      <div className="text-xs text-yellow-600 mt-1">
                        未结清客户
                      </div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-600">
                        ¥{formatAmount(rewardStats.usedReward)}
                      </div>
                      <div className="text-sm text-gray-600">已使用</div>
                      <div className="text-xs text-gray-600 mt-1">
                        历史使用
                      </div>
                    </div>
                  </div>

                  {/* 奖励进度条 */}
                  {rewardStats.totalReward > 0 && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-700">奖励解锁进度</span>
                        <span className="text-sm text-gray-600">
                          {statistics.referralCount > 0
                            ? `${Math.round((rewardStats.availableReward / rewardStats.totalReward) * 100)}%`
                            : '0%'
                          }
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className="bg-green-500 h-3 rounded-full transition-all duration-300"
                          style={{
                            width: `${statistics.referralCount > 0
                              ? Math.min(100, (rewardStats.availableReward / rewardStats.totalReward) * 100)
                              : 0}%`
                          }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>可用: ¥{formatAmount(rewardStats.availableReward)}</span>
                        <span>总计: ¥{formatAmount(rewardStats.totalReward)}</span>
                      </div>
                    </div>
                  )}

                  {/* 提示信息 */}
                  {rewardStats.availableReward === 0 && rewardStats.totalReward > 0 && (
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                      <div className="flex items-center text-blue-800">
                        <AlertTriangle className="mr-2 h-4 w-4" />
                        <span className="text-sm">
                          奖励将在被推荐客户结清货款后逐步解锁，无需等待全部客户付清
                        </span>
                      </div>
                    </div>
                  )}

                  {rewardStats.availableReward > 0 && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                      <div className="flex items-center text-green-800">
                        <CheckCircle className="mr-2 h-4 w-4" />
                        <span className="text-sm">
                          有 ¥{formatAmount(rewardStats.availableReward)} 奖励可立即使用
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 推荐客户列表 */}
              <Card>
                <CardHeader>
                  <CardTitle>推荐客户详情</CardTitle>
                  <CardDescription>该客户推荐的其他客户及其付款状态</CardDescription>
                </CardHeader>
                <CardContent>
                  {referredClients.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      暂无推荐客户
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {referredClients.map((referredClient) => {
                        // 🔧 修复：使用正确的推荐客户订单数据
                        const referredClientOrders = referredClientsOrders[referredClient.id] || []
                        const totalAmount = referredClientOrders.reduce((sum, order) => sum + Number(order.totalAmount || 0), 0)
                        const paidAmount = referredClientOrders.reduce((sum, order) => sum + Number(order.paidAmount || 0), 0)
                        const unpaidAmount = Math.max(0, totalAmount - paidAmount)
                        const isSettled = unpaidAmount <= 0.01

                        // 🆕 使用阶梯式奖励计算，而不是固定2%
                        const factoryId = getCurrentFactoryId()
                        const clientRewardResult = calculateTotalReferralReward(referredClientOrders, factoryId || '')
                        const rewardAmount = clientRewardResult.totalReward

                        return (
                          <div key={referredClient.id} className={`p-4 border rounded-lg ${isSettled ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'}`}>
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2">
                                  <div className="font-medium">{referredClient.name}</div>
                                  {isSettled ? (
                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                      <CheckCircle className="w-3 h-3 mr-1" />
                                      已结清
                                    </span>
                                  ) : (
                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                      <Clock className="w-3 h-3 mr-1" />
                                      未结清
                                    </span>
                                  )}
                                </div>
                                <div className="text-sm text-gray-600">{referredClient.phone}</div>
                                <div className="text-sm text-gray-600">
                                  订单总额：¥{formatAmount(totalAmount)} | 已付：¥{formatAmount(paidAmount)}
                                  {!isSettled && ` | 欠款：¥${formatAmount(unpaidAmount)}`}
                                </div>
                              </div>
                              <div className="text-right">
                                <div className={`text-sm font-medium ${isSettled ? 'text-green-600' : 'text-gray-400'}`}>
                                  奖励：¥{formatAmount(rewardAmount)}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {isSettled ? '可用' : '待结算'}
                                </div>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 奖励使用历史 */}
              <RewardUsageHistory clientId={client.id} factoryId={client.factoryId} />
            </TabsContent>

            {/* 财务标签页 */}
            <TabsContent value="finance" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold text-green-600">¥{formatAmount(statistics.paidAmount)}</div>
                    <p className="text-sm text-gray-600">已付金额</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold text-yellow-600">¥{formatAmount(statistics.unpaidAmount)}</div>
                    <p className="text-sm text-gray-600">未付金额</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold text-red-600">¥{formatAmount(statistics.overdueAmount)}</div>
                    <p className="text-sm text-gray-600">逾期金额</p>
                  </CardContent>
                </Card>
              </div>

              {statistics.overdueAmount > 0 && (
                <Card className="border-red-200 bg-red-50">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-2 text-red-600">
                      <AlertTriangle className="h-5 w-5" />
                      <span className="font-medium">逾期提醒</span>
                    </div>
                    <p className="text-sm text-red-600 mt-2">
                      该客户有 ¥{formatAmount(statistics.overdueAmount)} 的逾期款项，请及时跟进催收。
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        )}

        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </div>
      </DialogContent>

      {/* 奖励使用对话框 */}
      <RewardUsageDialog
        open={rewardUsageDialogOpen}
        onOpenChange={setRewardUsageDialogOpen}
        client={currentClient || client}
        realtimeRewardData={{
          availableReward: rewardStats.availableReward,
          totalReward: rewardStats.totalReward,
          pendingReward: rewardStats.pendingReward
        }}
        onSuccess={() => {
          // 奖励使用成功后重新加载客户详情
          loadClientDetails()
        }}
      />
    </Dialog>
  )
}
