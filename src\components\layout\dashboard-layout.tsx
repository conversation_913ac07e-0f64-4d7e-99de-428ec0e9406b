"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Sidebar } from "./sidebar"
import { useAuthStore } from "@/lib/store/auth"
import { checkDataIsolationStatus } from "@/lib/middleware/data-isolation"
import { getAuthDebugInfo, autoFixAuthIssues, printAuthDebugInfo } from "@/lib/utils/auth-debug"

interface DashboardLayoutProps {
  children: React.ReactNode
  role: 'admin' | 'factory'
}

export function DashboardLayout({ children, role }: DashboardLayoutProps) {
  const router = useRouter()
  const { isAuthenticated, role: userRole, factoryId, accessToken } = useAuthStore()
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    // 等待认证状态完全恢复
    const initializeAuth = async () => {
      // 给Zustand persist一些时间来恢复状态
      await new Promise(resolve => setTimeout(resolve, 100))

      // 检查localStorage中是否有token
      const hasStoredToken = typeof window !== 'undefined' &&
        (localStorage.getItem('accessToken') || localStorage.getItem('auth-storage'))

      console.log('🔍 认证状态检查:', {
        isAuthenticated,
        userRole,
        role,
        hasStoredToken: !!hasStoredToken,
        accessToken: !!accessToken
      })

      // 如果localStorage中有token但Zustand状态还没恢复，使用调试工具诊断和修复
      if (hasStoredToken && !isAuthenticated) {
        console.log('⏳ 等待认证状态恢复...')

        // 打印调试信息
        printAuthDebugInfo()

        // 尝试自动修复
        const fixResult = autoFixAuthIssues()
        if (fixResult) {
          console.log('✅ 认证问题已自动修复')

          // 再等待一下让状态更新
          await new Promise(resolve => setTimeout(resolve, 300))

          // 检查修复结果
          const { isAuthenticated: newAuth, user: newUser } = useAuthStore.getState()
          console.log('🔄 修复后状态:', {
            isAuthenticated: newAuth,
            userName: newUser?.name,
            timestamp: new Date().toISOString()
          })
        } else {
          console.log('❌ 无法自动修复认证问题，需要重新登录')
        }
      }

      setIsInitialized(true)
    }

    initializeAuth()
  }, [])

  useEffect(() => {
    if (!isInitialized) return

    console.log('🔐 执行认证检查:', { isAuthenticated, userRole, role, factoryId })

    if (!isAuthenticated) {
      console.log('❌ 用户未认证，重定向到登录页，工厂登录失败')

      // 清理可能损坏的本地存储
      if (typeof window !== 'undefined') {
        const hasStoredData = localStorage.getItem('accessToken') || localStorage.getItem('auth-storage')
        if (hasStoredData) {
          console.log('🧹 清理损坏的认证数据')
          localStorage.removeItem('accessToken')
          localStorage.removeItem('refreshToken')
          localStorage.removeItem('user')
          localStorage.removeItem('role')
          localStorage.removeItem('factoryId')
          localStorage.removeItem('auth-storage')
        }
      }

      router.push(role === 'admin' ? '/admin/login' : '/factory/login')
      return
    }

    if (userRole !== role) {
      console.log('❌ 角色不匹配，当前角色:', userRole, '需要角色:', role)
      router.push('/')
      return
    }

    // 检查数据隔离状态
    const isolationStatus = checkDataIsolationStatus()
    if (!isolationStatus.isAuthenticated) {
      console.log('❌ 数据隔离状态检查失败')
      router.push('/')
      return
    }

    console.log('✅ 认证检查通过')
  }, [isInitialized, isAuthenticated, userRole, role, router])

  // 单独处理工厂用户的factoryId检查
  useEffect(() => {
    if (!isInitialized || !isAuthenticated || userRole !== 'factory' || role !== 'factory') {
      return
    }

    if (!factoryId) {
      console.log('⚠️ 工厂用户factoryId尚未恢复，等待状态同步...')

      let finalTimer: NodeJS.Timeout | null = null
      let extraTimer: NodeJS.Timeout | null = null

      const timer = setTimeout(() => {
        const currentFactoryId = useAuthStore.getState().factoryId
        if (!currentFactoryId) {
          console.log('⚠️ 工厂用户factoryId第一次检查未恢复，继续等待...')
          // 第二次检查
          finalTimer = setTimeout(() => {
            const secondFactoryId = useAuthStore.getState().factoryId
            if (!secondFactoryId) {
              console.log('⚠️ 工厂用户factoryId第二次检查未恢复，最后等待...')
              // 第三次检查，最后机会
              extraTimer = setTimeout(() => {
                const finalFactoryId = useAuthStore.getState().factoryId
                if (!finalFactoryId) {
                  console.warn('⚠️ 工厂用户factoryId长时间未恢复，可能需要重新登录')
                  router.push('/factory/login')
                } else {
                  console.log('✅ 工厂用户factoryId最终恢复成功:', finalFactoryId)
                }
              }, 5000) // 最后等5秒
            } else {
              console.log('✅ 工厂用户factoryId第二次检查恢复成功:', secondFactoryId)
            }
          }, 3000) // 第二次等3秒
        } else {
          console.log('✅ 工厂用户factoryId第一次检查恢复成功:', currentFactoryId)
        }
      }, 3000) // 首次等待3秒

      return () => {
        clearTimeout(timer)
        if (finalTimer) {
          clearTimeout(finalTimer)
        }
        if (extraTimer) {
          clearTimeout(extraTimer)
        }
      }
    } else {
      console.log('✅ 工厂用户factoryId已恢复:', factoryId)
    }
  }, [isInitialized, isAuthenticated, userRole, role, factoryId, router])

  // 显示加载状态直到认证状态完全初始化
  if (!isInitialized || !isAuthenticated || userRole !== role || (role === 'factory' && !factoryId)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">
            {!isInitialized ? '正在初始化...' :
             !isAuthenticated ? '正在验证身份...' :
             userRole !== role ? '正在验证权限...' :
             role === 'factory' && !factoryId ? '正在加载工厂信息...' :
             '正在验证身份...'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-64 flex-shrink-0">
        <Sidebar role={role} />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden bg-background">
        <main className="flex-1 overflow-y-auto bg-background">
          {children}
        </main>
      </div>
    </div>
  )
}
