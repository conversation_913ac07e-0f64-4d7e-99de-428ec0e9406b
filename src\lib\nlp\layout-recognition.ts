/**
 * 🇨🇳 风口云平台 - 智能排版识别系统
 * 
 * 基于师傅手写习惯的智能排版识别
 */

// 排版类型枚举
export type LayoutType = 'column_based' | 'sequence_based' | 'table_based' | 'mixed' | 'unknown'

// 排版识别结果
export interface LayoutRecognitionResult {
  layoutType: LayoutType
  confidence: number
  structure: {
    hasHeaders: boolean
    hasSequence: boolean
    columnCount: number
    rowCount: number
    sequencePattern: string[]
  }
  ventGroups: VentGroup[]
  warnings: string[]
  suggestions: string[]
}

// 风口分组
export interface VentGroup {
  type: 'outlet' | 'return' | 'mixed'
  title?: string
  items: VentLayoutItem[]
  position: 'left' | 'right' | 'single'
}

// 风口排版项目
export interface VentLayoutItem {
  sequence?: number
  floor?: string
  dimensions: string
  quantity?: number
  originalText: string
  position: { line: number, column: number }
  confidence: number
}

/**
 * 智能排版识别主函数
 */
export function recognizeLayout(text: string): LayoutRecognitionResult {
  console.log('📐 开始智能排版识别...')
  console.log('📝 分析师傅手写习惯...')
  
  const lines = text.split('\n').filter(line => line.trim())
  
  // 步骤1: 检测列头关键词
  const headerAnalysis = analyzeHeaders(lines)
  console.log('📋 列头分析:', headerAnalysis)
  
  // 步骤2: 检测序号模式
  const sequenceAnalysis = analyzeSequence(lines)
  console.log('🔢 序号分析:', sequenceAnalysis)
  
  // 步骤3: 检测表格结构
  const tableAnalysis = analyzeTableStructure(lines)
  console.log('📊 表格分析:', tableAnalysis)
  
  // 步骤4: 确定排版类型
  const layoutType = determineLayoutType(headerAnalysis, sequenceAnalysis, tableAnalysis)
  console.log('🎯 排版类型:', layoutType)
  
  // 步骤5: 解析风口分组
  const ventGroups = parseVentGroups(lines, layoutType, headerAnalysis, sequenceAnalysis)
  console.log('📦 风口分组:', ventGroups.length, '组')
  
  // 步骤6: 生成警告和建议
  const { warnings, suggestions } = generateLayoutAdvice(ventGroups, layoutType)
  
  const result: LayoutRecognitionResult = {
    layoutType,
    confidence: calculateLayoutConfidence(headerAnalysis, sequenceAnalysis, tableAnalysis),
    structure: {
      hasHeaders: headerAnalysis.hasHeaders,
      hasSequence: sequenceAnalysis.hasSequence,
      columnCount: headerAnalysis.columnCount,
      rowCount: lines.length,
      sequencePattern: sequenceAnalysis.patterns
    },
    ventGroups,
    warnings,
    suggestions
  }
  
  console.log('✅ 排版识别完成:', result.layoutType, `置信度: ${(result.confidence * 100).toFixed(1)}%`)
  return result
}

/**
 * 分析列头关键词
 */
function analyzeHeaders(lines: string[]): any {
  console.log('   🔍 检测列头关键词...')
  
  const headerKeywords = ['出风', '回风', '送风', '排风', '新风', '线型', '检修']
  const headerLines: number[] = []
  let columnCount = 1
  
  // 检查前3行是否包含列头
  for (let i = 0; i < Math.min(3, lines.length); i++) {
    const line = lines[i]
    const foundKeywords = headerKeywords.filter(keyword => line.includes(keyword))
    
    if (foundKeywords.length >= 2) {
      headerLines.push(i)
      // 估算列数：如果同一行有多个关键词，可能是分列的
      columnCount = Math.max(columnCount, foundKeywords.length)
      console.log(`     ✅ 第${i + 1}行发现列头: ${foundKeywords.join(', ')}`)
    }
  }
  
  return {
    hasHeaders: headerLines.length > 0,
    headerLines,
    columnCount,
    keywords: headerKeywords.filter(keyword => 
      lines.slice(0, 3).some(line => line.includes(keyword))
    )
  }
}

/**
 * 分析序号模式（优化版：支持手写订单的左右列排版）
 */
function analyzeSequence(lines: string[]): any {
  console.log('   🔢 检测序号模式...')

  const sequencePatterns: string[] = []
  const sequenceLines: number[] = []
  let hasSequence = false
  let sequenceType = 'unknown' // 'floor', 'column', 'simple'

  // 检测数字序号模式
  const numberPattern = /^(\d+)[\.\、\s]/
  const floorNumberPattern = /^(\d+)楼?(\d+)[\.\、\s]/
  const hasFloorKeywords = lines.some(line => /\d+楼|\d+层/i.test(line))

  let consecutiveNumbers = 0
  let lastNumber = 0
  let sequenceNumbers: number[] = []

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()

    // 检测楼层+序号模式
    const floorMatch = line.match(floorNumberPattern)
    if (floorMatch) {
      sequenceLines.push(i)
      sequencePatterns.push(`楼层序号: ${floorMatch[1]}楼${floorMatch[2]}`)
      hasSequence = true
      sequenceType = 'floor'
      console.log(`     🏢 第${i + 1}行发现楼层序号: ${floorMatch[1]}楼${floorMatch[2]}`)
      continue
    }

    // 检测普通序号模式
    const numberMatch = line.match(numberPattern)
    if (numberMatch) {
      const currentNumber = parseInt(numberMatch[1])
      sequenceLines.push(i)
      sequenceNumbers.push(currentNumber)

      if (currentNumber === lastNumber + 1) {
        consecutiveNumbers++
      } else {
        consecutiveNumbers = 1
      }

      lastNumber = currentNumber
      sequencePatterns.push(`序号: ${currentNumber}`)

      // 如果连续序号超过2个，确认为序号模式
      if (consecutiveNumbers >= 2) {
        hasSequence = true
        console.log(`     📝 第${i + 1}行发现连续序号: ${currentNumber}`)
      }
    }
  }

  // 智能判断序号类型
  if (hasSequence && sequenceNumbers.length > 0) {
    // 检查是否为列式排版（如1,8,2,9,3,10...表示左右列）
    const isColumnPattern = detectColumnPattern(sequenceNumbers)
    if (isColumnPattern && !hasFloorKeywords) {
      sequenceType = 'column'
      console.log('     📊 检测到列式排版模式（左右列从上到下）')
    } else if (!hasFloorKeywords) {
      sequenceType = 'simple'
      console.log('     📝 检测到简单序号模式（无楼层信息，序号作为备注）')
    }
  }

  return {
    hasSequence,
    sequenceLines,
    patterns: sequencePatterns,
    consecutiveCount: consecutiveNumbers,
    maxNumber: lastNumber,
    sequenceType,
    sequenceNumbers,
    hasFloorKeywords,
    isColumnLayout: sequenceType === 'column'
  }
}

/**
 * 检测列式排版模式（如1,8,2,9,3,10...）
 */
function detectColumnPattern(numbers: number[]): boolean {
  if (numbers.length < 4) return false

  // 检查是否存在跳跃模式（如1,8,2,9...）
  let jumpPattern = 0
  let consecutivePattern = 0

  for (let i = 1; i < numbers.length; i++) {
    const diff = numbers[i] - numbers[i-1]
    if (Math.abs(diff) > 5) {
      jumpPattern++
    } else if (Math.abs(diff) === 1) {
      consecutivePattern++
    }
  }

  // 如果跳跃模式多于连续模式，可能是列式排版
  const isColumnPattern = jumpPattern > consecutivePattern && jumpPattern >= 2

  if (isColumnPattern) {
    console.log(`     🔍 列式模式分析: 跳跃${jumpPattern}次, 连续${consecutivePattern}次`)
  }

  return isColumnPattern
}

/**
 * 分析表格结构
 */
function analyzeTableStructure(lines: string[]): any {
  console.log('   📊 检测表格结构...')
  
  // 检测表格分隔符
  const separators = ['|', '│', '\t', '  ']
  let tableScore = 0
  let consistentColumns = 0
  
  const columnCounts: number[] = []
  
  for (const line of lines) {
    let maxColumns = 1
    
    for (const sep of separators) {
      const columns = line.split(sep).filter(col => col.trim())
      maxColumns = Math.max(maxColumns, columns.length)
    }
    
    columnCounts.push(maxColumns)
    if (maxColumns > 2) tableScore++
  }
  
  // 检查列数一致性
  const mostCommonColumns = columnCounts.sort((a, b) => 
    columnCounts.filter(v => v === a).length - columnCounts.filter(v => v === b).length
  ).pop() || 1
  
  consistentColumns = columnCounts.filter(count => count === mostCommonColumns).length
  
  const isTable = tableScore > lines.length * 0.3 && consistentColumns > lines.length * 0.5
  
  if (isTable) {
    console.log(`     📋 检测到表格结构: ${mostCommonColumns}列, 一致性: ${(consistentColumns / lines.length * 100).toFixed(1)}%`)
  }
  
  return {
    isTable,
    tableScore,
    consistentColumns,
    mostCommonColumns,
    consistency: consistentColumns / lines.length
  }
}

/**
 * 确定排版类型
 */
function determineLayoutType(headerAnalysis: any, sequenceAnalysis: any, tableAnalysis: any): LayoutType {
  console.log('   🎯 确定排版类型...')
  
  // 表格优先
  if (tableAnalysis.isTable) {
    console.log('     📊 判断为表格排版')
    return 'table_based'
  }
  
  // 列头排版
  if (headerAnalysis.hasHeaders && headerAnalysis.columnCount >= 2) {
    console.log('     📋 判断为列头排版 (师傅分两列写)')
    return 'column_based'
  }
  
  // 序号排版
  if (sequenceAnalysis.hasSequence && sequenceAnalysis.consecutiveCount >= 2) {
    console.log('     🔢 判断为序号排版 (师傅按顺序写)')
    return 'sequence_based'
  }
  
  // 混合排版
  if (headerAnalysis.hasHeaders || sequenceAnalysis.hasSequence) {
    console.log('     🔀 判断为混合排版')
    return 'mixed'
  }
  
  console.log('     ❓ 无法确定排版类型')
  return 'unknown'
}

/**
 * 解析风口分组
 */
function parseVentGroups(lines: string[], layoutType: LayoutType, headerAnalysis: any, sequenceAnalysis: any): VentGroup[] {
  console.log('   📦 解析风口分组...')
  
  const groups: VentGroup[] = []
  
  switch (layoutType) {
    case 'column_based':
      return parseColumnBasedLayout(lines, headerAnalysis)
    case 'sequence_based':
      return parseSequenceBasedLayout(lines, sequenceAnalysis)
    case 'table_based':
      return parseTableBasedLayout(lines)
    case 'mixed':
      return parseMixedLayout(lines, headerAnalysis, sequenceAnalysis)
    default:
      return parseUnknownLayout(lines)
  }
}

/**
 * 解析列头排版（师傅分两列写）
 */
function parseColumnBasedLayout(lines: string[], headerAnalysis: any): VentGroup[] {
  console.log('     📋 解析列头排版...')
  
  const groups: VentGroup[] = []
  const headerLine = headerAnalysis.headerLines[0] || 0
  
  // 创建出风口组和回风口组
  const outletGroup: VentGroup = {
    type: 'outlet',
    title: '出风口',
    items: [],
    position: 'left'
  }
  
  const returnGroup: VentGroup = {
    type: 'return', 
    title: '回风口',
    items: [],
    position: 'right'
  }
  
  // 从列头后开始解析数据
  for (let i = headerLine + 1; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue
    
    // 查找尺寸
    const dimensions = line.match(/\d+\s*[xX×*]\s*\d+/g) || []
    
    for (let j = 0; j < dimensions.length; j++) {
      const dim = dimensions[j]
      const dimensionParts = dim.split(/[xX×*+\-]/).map(s => parseInt(s.trim()))

      // 验证尺寸数据有效性
      if (dimensionParts.length !== 2 || dimensionParts.some(isNaN) || dimensionParts.some(val => val <= 0)) {
        console.log(`     ❌ 跳过无效尺寸: "${dim}" -> [${dimensionParts.join(', ')}]`)
        continue
      }

      const [length, width] = dimensionParts

      // 根据位置和宽度判断类型
      const isReturn = width > 280 || j === 1 // 第二个尺寸通常是回风口
      const targetGroup = isReturn ? returnGroup : outletGroup
      
      targetGroup.items.push({
        dimensions: dim,
        originalText: line,
        position: { line: i, column: j },
        confidence: 0.8
      })
    }
  }
  
  if (outletGroup.items.length > 0) groups.push(outletGroup)
  if (returnGroup.items.length > 0) groups.push(returnGroup)
  
  console.log(`     ✅ 列头排版解析完成: 出风${outletGroup.items.length}个, 回风${returnGroup.items.length}个`)
  return groups
}

/**
 * 解析序号排版（师傅按顺序写）- 优化版
 */
function parseSequenceBasedLayout(lines: string[], sequenceAnalysis: any): VentGroup[] {
  console.log('     🔢 解析序号排版...')
  console.log(`     📊 序号类型: ${sequenceAnalysis.sequenceType}`)

  const group: VentGroup = {
    type: 'mixed',
    title: getSequenceTitle(sequenceAnalysis.sequenceType),
    items: [],
    position: 'single'
  }

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    // 提取序号
    const sequenceMatch = line.match(/^(\d+)[\.\、\s]/) || line.match(/^(\d+)楼?(\d+)[\.\、\s]/)
    const sequence = sequenceMatch ? parseInt(sequenceMatch[1]) : undefined

    // 提取楼层（只有在有楼层关键词时才作为楼层）
    let floor: string | undefined
    if (sequenceAnalysis.hasFloorKeywords) {
      const floorMatch = line.match(/(\d+)楼/)
      floor = floorMatch ? floorMatch[1] + '楼' : undefined
    }

    // 清理文本：移除序号前缀，避免干扰尺寸识别
    let cleanLine = line
    if (sequenceMatch) {
      cleanLine = line.replace(/^(\d+)[\.\、\s]+/, '').trim()
    }

    // 提取尺寸（优化正则，支持更多格式，避免序号干扰）
    const dimensions = cleanLine.match(/(\d{1,4})\s*[xX×*+\-]\s*(\d{1,4})/g) || []

    console.log(`     🔍 处理行: "${line}" -> 清理后: "${cleanLine}" -> 尺寸: [${dimensions.join(', ')}]`)

    // 提取数量
    const quantityMatch = line.match(/(\d+)\s*[个只套]/) || line.match(/数量\s*[:：]\s*(\d+)/)
    const quantity = quantityMatch ? parseInt(quantityMatch[1]) : undefined

    for (const dim of dimensions) {
      // 根据序号类型决定备注内容
      let notes = generateSequenceNotes(sequence, sequenceAnalysis.sequenceType, floor)

      group.items.push({
        sequence,
        floor: sequenceAnalysis.hasFloorKeywords ? floor : undefined,
        dimensions: dim,
        quantity,
        notes,
        originalText: line,
        position: { line: i, column: 0 },
        confidence: 0.85
      })
    }
  }

  console.log(`     ✅ 序号排版解析完成: ${group.items.length}个项目`)
  return [group]
}

/**
 * 根据序号类型生成标题
 */
function getSequenceTitle(sequenceType: string): string {
  switch (sequenceType) {
    case 'floor':
      return '按楼层序号排列'
    case 'column':
      return '按列式排版（左右列从上到下）'
    case 'simple':
      return '按序号排列（序号作备注）'
    default:
      return '按序号排列'
  }
}

/**
 * 生成序号相关的备注
 */
function generateSequenceNotes(sequence: number | undefined, sequenceType: string, floor: string | undefined): string | undefined {
  if (!sequence) return undefined

  switch (sequenceType) {
    case 'floor':
      // 有楼层信息时，序号不作为备注
      return undefined
    case 'column':
      // 列式排版时，序号作为位置备注
      return `序号${sequence}`
    case 'simple':
      // 简单序号时，序号作为备注
      return `序号${sequence}`
    default:
      return `序号${sequence}`
  }
}

/**
 * 解析表格排版
 */
function parseTableBasedLayout(lines: string[]): VentGroup[] {
  console.log('     📊 解析表格排版...')
  
  // 表格解析逻辑
  const group: VentGroup = {
    type: 'mixed',
    title: '表格数据',
    items: [],
    position: 'single'
  }
  
  // 简化的表格解析
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    const dimensions = line.match(/\d+\s*[xX×*]\s*\d+/g) || []
    
    for (const dim of dimensions) {
      group.items.push({
        dimensions: dim,
        originalText: line,
        position: { line: i, column: 0 },
        confidence: 0.75
      })
    }
  }
  
  console.log(`     ✅ 表格排版解析完成: ${group.items.length}个项目`)
  return [group]
}

/**
 * 解析混合排版
 */
function parseMixedLayout(lines: string[], headerAnalysis: any, sequenceAnalysis: any): VentGroup[] {
  console.log('     🔀 解析混合排版...')
  
  // 优先使用序号排版逻辑
  if (sequenceAnalysis.hasSequence) {
    return parseSequenceBasedLayout(lines, sequenceAnalysis)
  }
  
  // 其次使用列头排版逻辑
  if (headerAnalysis.hasHeaders) {
    return parseColumnBasedLayout(lines, headerAnalysis)
  }
  
  return parseUnknownLayout(lines)
}

/**
 * 解析未知排版
 */
function parseUnknownLayout(lines: string[]): VentGroup[] {
  console.log('     ❓ 解析未知排版...')
  
  const group: VentGroup = {
    type: 'mixed',
    title: '未知排版',
    items: [],
    position: 'single'
  }
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    const dimensions = line.match(/\d+\s*[xX×*]\s*\d+/g) || []
    
    for (const dim of dimensions) {
      group.items.push({
        dimensions: dim,
        originalText: line,
        position: { line: i, column: 0 },
        confidence: 0.6
      })
    }
  }
  
  console.log(`     ✅ 未知排版解析完成: ${group.items.length}个项目`)
  return [group]
}

/**
 * 计算排版置信度
 */
function calculateLayoutConfidence(headerAnalysis: any, sequenceAnalysis: any, tableAnalysis: any): number {
  let confidence = 0.5
  
  if (headerAnalysis.hasHeaders) confidence += 0.2
  if (sequenceAnalysis.hasSequence) confidence += 0.2
  if (tableAnalysis.isTable) confidence += 0.3
  if (sequenceAnalysis.consecutiveCount >= 3) confidence += 0.1
  
  return Math.min(confidence, 1.0)
}

/**
 * 生成排版建议
 */
function generateLayoutAdvice(ventGroups: VentGroup[], layoutType: LayoutType): { warnings: string[], suggestions: string[] } {
  const warnings: string[] = []
  const suggestions: string[] = []
  
  // 检查项目数量异常
  const totalItems = ventGroups.reduce((sum, group) => sum + group.items.length, 0)
  if (totalItems > 50) {
    warnings.push('识别到超过50个项目，可能存在识别错误，建议人工检查')
  }
  
  // 检查尺寸异常
  for (const group of ventGroups) {
    for (const item of group.items) {
      const [length, width] = item.dimensions.split(/[xX×*]/).map(s => parseInt(s.trim()))
      if (length > 5000 || width > 5000) {
        warnings.push(`发现异常尺寸 ${item.dimensions}，可能是识别错误`)
      }
    }
  }
  
  // 生成优化建议
  if (layoutType === 'unknown') {
    suggestions.push('无法确定排版类型，建议提供更清晰的图片或标准化格式')
  }
  
  if (layoutType === 'column_based') {
    suggestions.push('检测到分列排版，已按出风口/回风口分组处理')
  }
  
  if (layoutType === 'sequence_based') {
    suggestions.push('检测到序号排版，已按顺序解析，请确认楼层信息')
  }
  
  return { warnings, suggestions }
}
