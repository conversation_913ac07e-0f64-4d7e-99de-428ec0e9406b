/**
 * 🇨🇳 风口云平台 - 楼层提取测试页面
 */

'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

// 复制楼层提取逻辑进行测试
const extractFloorFromProject = (projectInfo: { projectName: string, clientInfo: string }): string => {
  const projectName = projectInfo.projectName || ''
  const clientInfo = projectInfo.clientInfo || ''
  const fullText = `${projectName} ${clientInfo}`.trim()

  console.log(`🏢 智能楼层提取: "${fullText}"`)

  // 1. 优先识别明确的楼层标识
  const explicitFloorMatch = fullText.match(/(\d+)楼|(\d+)层|(\d+)F/i)
  if (explicitFloorMatch) {
    const floor = explicitFloorMatch[1] || explicitFloorMatch[2] || explicitFloorMatch[3]
    console.log(`✅ 明确楼层标识: "${floor}楼"`)
    return `${floor}楼`
  }

  // 2. 🔧 增强：支持"书香华府8-501"格式 → 5楼
  const buildingRoomMatch = fullText.match(/(\d+)-(\d{3,4})/)
  if (buildingRoomMatch) {
    const roomNumber = buildingRoomMatch[2]
    let floor = ''
    if (roomNumber.length === 4) {
      // 4位房号：前2位是楼层（如501 → 5楼，2101 → 21楼）
      floor = roomNumber.substring(0, 2)
      // 去掉前导0（如05 → 5）
      floor = parseInt(floor).toString()
    } else if (roomNumber.length === 3) {
      // 3位房号：前1位是楼层（如501 → 5楼）
      floor = roomNumber.substring(0, 1)
    }
    if (floor && parseInt(floor) >= 1 && parseInt(floor) <= 99) {
      console.log(`✅ 从"栋-房号"格式提取楼层: "${buildingRoomMatch[0]}" → "${floor}楼"`)
      return `${floor}楼`
    }
  }

  // 3. 🔧 增强：支持"9-2101"格式 → 21楼
  const simpleRoomMatch = fullText.match(/(\d+)-(\d{4})/)
  if (simpleRoomMatch) {
    const roomNumber = simpleRoomMatch[2]
    const floor = parseInt(roomNumber.substring(0, 2)).toString()
    if (parseInt(floor) >= 1 && parseInt(floor) <= 99) {
      console.log(`✅ 从"数字-房号"格式提取楼层: "${simpleRoomMatch[0]}" → "${floor}楼"`)
      return `${floor}楼`
    }
  }

  // 4. 🔧 增强：支持"3-1802"格式 → 18楼
  const shortBuildingMatch = fullText.match(/(\d)-(\d{4})/)
  if (shortBuildingMatch) {
    const roomNumber = shortBuildingMatch[2]
    const floor = parseInt(roomNumber.substring(0, 2)).toString()
    if (parseInt(floor) >= 1 && parseInt(floor) <= 99) {
      console.log(`✅ 从"单数字-房号"格式提取楼层: "${shortBuildingMatch[0]}" → "${floor}楼"`)
      return `${floor}楼`
    }
  }

  // 5. 从房号中提取楼层（支持多种格式）
  const roomNumberMatch = fullText.match(/(\d{3,4})号?房/)
  if (roomNumberMatch) {
    const roomNumber = roomNumberMatch[1]
    if (roomNumber.length === 4) {
      const floor = parseInt(roomNumber.substring(0, 2)).toString()
      console.log(`✅ 从4位房号提取楼层: "${roomNumber}" → "${floor}楼"`)
      return `${floor}楼`
    } else if (roomNumber.length === 3) {
      const floor = roomNumber.substring(0, 1)
      console.log(`✅ 从3位房号提取楼层: "${roomNumber}" → "${floor}楼"`)
      return `${floor}楼`
    }
  }

  // 6. 任何4位数字 → 前2位作为楼层
  const anyFourDigitMatch = fullText.match(/(\d{4})/)
  if (anyFourDigitMatch) {
    const roomNumber = anyFourDigitMatch[1]
    const floor = parseInt(roomNumber.substring(0, 2))
    if (floor >= 1 && floor <= 99) {
      console.log(`✅ 从4位数字提取楼层: "${roomNumber}" → "${floor}楼"`)
      return `${floor}楼`
    }
  }

  // 7. 中文数字楼层
  const chineseFloorMatch = fullText.match(/(一|二|三|四|五|六|七|八|九|十)[楼层]/)
  if (chineseFloorMatch) {
    const chineseNum = chineseFloorMatch[1]
    const floorMap: { [key: string]: string } = {
      '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
      '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
    }
    const floor = floorMap[chineseNum] || '1'
    console.log(`✅ 从中文数字提取楼层: "${chineseFloorMatch[0]}" → "${floor}楼"`)
    return `${floor}楼`
  }

  // 8. 默认返回1楼
  console.log(`⚠️ 无法提取楼层，使用默认值: "1楼"`)
  return '1楼'
}

export default function TestFloorExtractionPage() {
  const [projectName, setProjectName] = useState('书香华府8-501')
  const [clientInfo, setClientInfo] = useState('')
  const [result, setResult] = useState<string | null>(null)

  const runTest = () => {
    console.log('🚀 开始楼层提取测试...')
    const extractedFloor = extractFloorFromProject({ projectName, clientInfo })
    setResult(extractedFloor)
  }

  const testCases = [
    { name: '书香华府8-501', expected: '5楼' },
    { name: '9-2101', expected: '21楼' },
    { name: '3-1802', expected: '18楼' },
    { name: '上淮府3-2702', expected: '27楼' },
    { name: '金桂苑1901号房', expected: '19楼' },
    { name: '容桂壹号7栋2单元903', expected: '9楼' },
    { name: '万科城5楼', expected: '5楼' },
    { name: '恒大华府三楼', expected: '3楼' },
    { name: '绿地中心15F', expected: '15楼' },
    { name: '普通项目', expected: '1楼' }
  ]

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-center">🏢 楼层提取测试页面</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 手动测试 */}
          <Card>
            <CardHeader>
              <CardTitle>手动测试</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">项目名称</label>
                <Input
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  placeholder="输入项目名称..."
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">客户信息</label>
                <Input
                  value={clientInfo}
                  onChange={(e) => setClientInfo(e.target.value)}
                  placeholder="输入客户信息..."
                />
              </div>
              
              <Button onClick={runTest} className="w-full">
                🏢 提取楼层
              </Button>
              
              {result && (
                <div className="p-4 bg-green-50 border border-green-200 rounded">
                  <p className="text-green-700 font-bold text-lg">
                    提取结果: {result}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 预设测试用例 */}
          <Card>
            <CardHeader>
              <CardTitle>预设测试用例</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {testCases.map((testCase, index) => (
                <div key={index} className="border rounded p-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium">{testCase.name}</h4>
                      <p className="text-sm text-gray-600">
                        期望结果: {testCase.expected}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setProjectName(testCase.name)
                        setClientInfo('')
                        const extracted = extractFloorFromProject({ 
                          projectName: testCase.name, 
                          clientInfo: '' 
                        })
                        setResult(extracted)
                      }}
                    >
                      测试
                    </Button>
                  </div>
                  {result && projectName === testCase.name && (
                    <div className={`mt-2 p-2 rounded text-sm ${
                      result === testCase.expected 
                        ? 'bg-green-50 text-green-700' 
                        : 'bg-red-50 text-red-700'
                    }`}>
                      实际结果: {result} {result === testCase.expected ? '✅' : '❌'}
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* 支持的格式说明 */}
        <Card>
          <CardHeader>
            <CardTitle>支持的楼层识别格式</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">明确楼层标识</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• 5楼、15楼</li>
                  <li>• 3层、18层</li>
                  <li>• 5F、15F</li>
                  <li>• 三楼、五层</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">房号格式</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• 书香华府8-501 → 5楼</li>
                  <li>• 9-2101 → 21楼</li>
                  <li>• 3-1802 → 18楼</li>
                  <li>• 1901号房 → 19楼</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
