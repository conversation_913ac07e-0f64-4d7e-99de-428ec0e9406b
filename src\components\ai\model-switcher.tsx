"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Brain, Zap, Clock, DollarSign, TrendingUp, AlertTriangle } from 'lucide-react'
import { type ComplexityAnalysis } from '@/lib/services/complexity-detector'

interface ModelSwitcherProps {
  complexityAnalysis?: ComplexityAnalysis
  onModelSelect: (model: 'deepseek-chat' | 'deepseek-reasoner' | 'auto') => void
  selectedModel: 'deepseek-chat' | 'deepseek-reasoner' | 'auto'
}

export function ModelSwitcher({ complexityAnalysis, onModelSelect, selectedModel }: ModelSwitcherProps) {
  const [showDetails, setShowDetails] = useState(false)

  const models = [
    {
      id: 'auto' as const,
      name: '智能选择',
      description: '根据文本复杂度自动选择最适合的模型',
      icon: Brain,
      speed: '快速',
      cost: '优化',
      accuracy: '智能',
      color: 'blue',
      recommended: true
    },
    {
      id: 'deepseek-chat' as const,
      name: 'DeepSeek V3',
      description: '快速响应，适合常规风口识别',
      icon: Zap,
      speed: '2-3秒',
      cost: '¥0.01',
      accuracy: '90-95%',
      color: 'green'
    },
    {
      id: 'deepseek-reasoner' as const,
      name: 'DeepSeek R1',
      description: '深度推理，适合复杂场景',
      icon: Brain,
      speed: '10-30秒',
      cost: '¥0.02',
      accuracy: '95-98%',
      color: 'purple'
    }
  ]

  const getModelBadgeColor = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'green': return 'bg-green-100 text-green-800 border-green-200'
      case 'purple': return 'bg-purple-100 text-purple-800 border-purple-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="space-y-4">
      {/* 复杂度分析显示 */}
      {complexityAnalysis && (
        <Card className="border-amber-200 bg-amber-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center">
              <TrendingUp className="h-4 w-4 mr-2 text-amber-600" />
              文本复杂度分析
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">复杂度评分:</span>
              <Badge variant="outline" className={`${
                complexityAnalysis.score >= 5 ? 'border-red-300 text-red-700' : 'border-green-300 text-green-700'
              }`}>
                {complexityAnalysis.score}/10
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">推荐模型:</span>
              <Badge className={getModelBadgeColor(
                complexityAnalysis.recommendedModel === 'deepseek-reasoner' ? 'purple' : 'green'
              )}>
                {complexityAnalysis.recommendedModel === 'deepseek-reasoner' ? 'R1 推理模型' : 'V3 快速模型'}
              </Badge>
            </div>

            {complexityAnalysis.reasons.length > 0 && (
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">复杂因素:</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowDetails(!showDetails)}
                    className="h-6 px-2 text-xs"
                  >
                    {showDetails ? '收起' : '展开'}
                  </Button>
                </div>
                
                {showDetails && (
                  <div className="space-y-1">
                    {complexityAnalysis.reasons.map((reason, index) => (
                      <div key={index} className="flex items-center text-xs text-amber-700">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {reason}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 模型选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">选择AI模型</CardTitle>
          <CardDescription>
            根据您的需求选择合适的识别模型
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {models.map((model) => {
            const Icon = model.icon
            const isSelected = selectedModel === model.id
            
            return (
              <div
                key={model.id}
                className={`p-3 rounded-lg border cursor-pointer transition-all ${
                  isSelected 
                    ? `border-${model.color}-300 bg-${model.color}-50` 
                    : 'border-gray-200 hover:border-gray-300'
                } ${model.recommended ? 'ring-2 ring-blue-200' : ''}`}
                onClick={() => onModelSelect(model.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <Icon className={`h-5 w-5 mt-0.5 ${
                      isSelected ? `text-${model.color}-600` : 'text-gray-500'
                    }`} />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-sm">{model.name}</h4>
                        {model.recommended && (
                          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                            推荐
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-gray-600 mt-1">{model.description}</p>
                      
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-600">{model.speed}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-600">{model.cost}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-600">{model.accuracy}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className={`w-4 h-4 rounded-full border-2 ${
                    isSelected 
                      ? `border-${model.color}-500 bg-${model.color}-500` 
                      : 'border-gray-300'
                  }`}>
                    {isSelected && (
                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* 使用建议 */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-4">
          <div className="space-y-2 text-sm">
            <h4 className="font-medium text-blue-800">💡 使用建议</h4>
            <div className="space-y-1 text-blue-700">
              <div>• <strong>智能选择</strong>：推荐选项，优先使用V3模型</div>
              <div>• <strong>V3模型</strong>：⭐ 推荐！风口识别准确率高，速度快</div>
              <div>• <strong>R1模型</strong>：推理能力强但在风口识别上可能过度复杂</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
