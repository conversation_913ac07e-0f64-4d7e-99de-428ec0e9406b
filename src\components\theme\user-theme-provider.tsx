'use client'

/**
 * 🇨🇳 风口云平台 - 用户独立主题提供者
 * 
 * 功能说明：
 * - 为每个用户提供完全独立的主题设置
 * - 用户切换时自动加载对应的主题
 * - 主题设置不会在用户间相互影响
 */

import { useEffect, useState, createContext, useContext } from 'react'
import { useAuthStore } from '@/lib/store/auth'
import { 
  useUserThemeStore, 
  initializeUserTheme, 
  switchUserTheme,
  type ThemeMode 
} from '@/lib/store/user-theme'

interface UserThemeContextType {
  mode: ThemeMode
  setTheme: (mode: ThemeMode) => void
  isLoading: boolean
}

const UserThemeContext = createContext<UserThemeContextType | undefined>(undefined)

interface UserThemeProviderProps {
  children: React.ReactNode
}

export function UserThemeProvider({ children }: UserThemeProviderProps) {
  const [mounted, setMounted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const { isAuthenticated, user, role, factoryId } = useAuthStore()
  
  // 订阅主题store的变化
  const currentTheme = useUserThemeStore(state => state.getCurrentTheme())
  const setMode = useUserThemeStore(state => state.setMode)
  const setCurrentUser = useUserThemeStore(state => state.setCurrentUser)

  // 初始化主题系统
  useEffect(() => {
    setMounted(true)
    
    const initialize = async () => {
      try {
        setIsLoading(true)
        
        // 初始化用户主题系统
        initializeUserTheme()
        
        console.log('✅ 用户主题提供者初始化完成')
      } catch (error) {
        console.error('❌ 用户主题提供者初始化失败:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    initialize()
  }, [])

  // 监听用户状态变化
  useEffect(() => {
    if (mounted && !isLoading) {
      try {
        // 当用户状态变化时，切换主题
        switchUserTheme()
        console.log('🔄 用户状态变化，已切换主题:', { 
          isAuthenticated, 
          userId: user?.id, 
          role, 
          factoryId 
        })
      } catch (error) {
        console.error('❌ 用户主题切换失败:', error)
      }
    }
  }, [mounted, isLoading, isAuthenticated, user?.id, role, factoryId])

  // 主题设置函数
  const setTheme = (newMode: ThemeMode) => {
    try {
      setMode(newMode)
      console.log('🎨 用户主题已更新:', { 
        userId: user?.id || 'anonymous', 
        mode: newMode 
      })
    } catch (error) {
      console.error('❌ 设置用户主题失败:', error)
    }
  }

  // 防止水合错误
  if (!mounted) {
    return (
      <UserThemeContext.Provider value={{
        mode: 'light',
        setTheme: () => {},
        isLoading: true
      }}>
        {children}
      </UserThemeContext.Provider>
    )
  }

  return (
    <UserThemeContext.Provider value={{
      mode: currentTheme.mode,
      setTheme,
      isLoading
    }}>
      {children}
    </UserThemeContext.Provider>
  )
}

// Hook for using user theme
export function useUserTheme() {
  const context = useContext(UserThemeContext)
  if (context === undefined) {
    throw new Error('useUserTheme must be used within a UserThemeProvider')
  }
  return context
}

// 兼容性Hook，保持与原有代码的兼容
export function useTheme() {
  return useUserTheme()
}
