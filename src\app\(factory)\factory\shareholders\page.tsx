"use client"

import { useState, useEffect } from "react"
import { Card, CardContent  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { AddShareholderDialog } from "@/components/shareholders/add-shareholder-dialog"
import { ShareholderProfitAnalysis } from "@/components/shareholders/profit-analysis"
import {
  Loader2,
  AlertCircle,
  Users,
  Edit,
  Trash2,
  Plus,
  DollarSign,
  Calendar,
  Phone,
  Mail,
  MapPin
} from "lucide-react"
import { Shareholder } from "@/types"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import { safeNumber, safeAmount } from "@/lib/utils/number-utils"

export default function ShareholdersPage() {
  const [shareholders, setShareholders] = useState<Shareholder[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'shareholders' | 'profit'>('shareholders')

  // 编辑对话框状态
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showInvestmentDialog, setShowInvestmentDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedShareholder, setSelectedShareholder] = useState<Shareholder | null>(null)
  const [editLoading, setEditLoading] = useState(false)

  // 编辑表单数据
  const [editForm, setEditForm] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    notes: ''
  })

  // 追加投资表单数据
  const [investmentForm, setInvestmentForm] = useState({
    amount: 0,
    notes: '',
    adjustmentType: 'auto' as 'auto' | 'manual',  // 新增：调整类型
    newSharePercentage: 0                         // 新增：手动设置的新股权比例
  })

  // 加载股东数据
  const loadShareholders = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        setError("无法获取工厂信息")
        return
      }

      const response = await fetch(`/api/shareholders?factoryId=${factoryId}`)
      const result = await response.json()

      if (result.success) {
        setShareholders(result.shareholders)
      } else {
        setError(result.error || "获取股东数据失败")
      }
    } catch (error) {
      console.error('加载股东数据失败:', error)
      setError("加载股东数据失败")
    } finally {
      setLoading(false)
    }
  }

  // 处理股东添加成功
  const handleShareholderAdded = (newShareholder: Shareholder) => {
    setShareholders(prev => [newShareholder, ...prev])
  }

  // 🆕 打开编辑对话框
  const handleEditShareholder = (shareholder: Shareholder) => {
    setSelectedShareholder(shareholder)
    setEditForm({
      name: shareholder.name,
      phone: shareholder.phone || '',
      email: shareholder.email || '',
      address: shareholder.address || '',
      notes: shareholder.notes || ''
    })
    setShowEditDialog(true)
  }

  // 🆕 打开追加投资对话框
  const handleAddInvestment = (shareholder: Shareholder) => {
    setSelectedShareholder(shareholder)
    setInvestmentForm({
      amount: 0,
      notes: '',
      adjustmentType: 'auto',
      newSharePercentage: shareholder.sharePercentage  // 默认为当前股权比例
    })
    setShowInvestmentDialog(true)
  }

  // 🆕 打开删除确认对话框
  const handleDeleteShareholder = (shareholder: Shareholder) => {
    setSelectedShareholder(shareholder)
    setShowDeleteDialog(true)
  }

  // 🆕 提交编辑
  const handleEditSubmit = async () => {
    if (!selectedShareholder) return

    try {
      setEditLoading(true)

      const response = await fetch(`/api/shareholders/${selectedShareholder.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm)
      })

      const result = await response.json()

      if (result.success) {
        // 更新本地状态
        setShareholders(prev =>
          prev.map(s =>
            s.id === selectedShareholder.id
              ? { ...s, ...editForm, updatedAt: new Date() }
              : s
          )
        )
        setShowEditDialog(false)
        console.log('✅ 股东信息更新成功')
      } else {
        console.error('❌ 更新股东信息失败:', result.error)
        alert(result.error || '更新失败')
      }
    } catch (error) {
      console.error('❌ 更新股东信息失败:', error)
      alert('更新失败，请重试')
    } finally {
      setEditLoading(false)
    }
  }

  // 🆕 提交追加投资
  const handleInvestmentSubmit = async () => {
    if (!selectedShareholder || investmentForm.amount <= 0) {
      alert('请输入有效的投资金额')
      return
    }

    // 如果是手动调整模式，验证新股权比例
    if (investmentForm.adjustmentType === 'manual') {
      if (investmentForm.newSharePercentage <= 0 || investmentForm.newSharePercentage > 100) {
        alert('股权比例必须在0-100之间')
        return
      }
    }

    try {
      setEditLoading(true)

      const requestData = {
        amount: investmentForm.amount,
        notes: investmentForm.notes,
        adjustmentType: investmentForm.adjustmentType,
        ...(investmentForm.adjustmentType === 'manual' && {
          newSharePercentage: investmentForm.newSharePercentage
        })
      }

      const response = await fetch(`/api/shareholders/${selectedShareholder.id}/investment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      const result = await response.json()

      if (result.success) {
        // 重新加载股东数据以获取最新的股权比例
        await loadShareholders()
        setShowInvestmentDialog(false)
        console.log('✅ 追加投资记录成功')
      } else {
        console.error('❌ 追加投资失败:', result.error)
        alert(result.error || '追加投资失败')
      }
    } catch (error) {
      console.error('❌ 追加投资失败:', error)
      alert('追加投资失败，请重试')
    } finally {
      setEditLoading(false)
    }
  }

  // 🆕 确认删除股东
  const handleDeleteConfirm = async () => {
    if (!selectedShareholder) return

    try {
      setEditLoading(true)

      const response = await fetch(`/api/shareholders/${selectedShareholder.id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        // 从本地状态中移除
        setShareholders(prev => prev.filter(s => s.id !== selectedShareholder.id))
        setShowDeleteDialog(false)
        console.log('✅ 股东删除成功')
      } else {
        console.error('❌ 删除股东失败:', result.error)
        alert(result.error || '删除失败')
      }
    } catch (error) {
      console.error('❌ 删除股东失败:', error)
      alert('删除失败，请重试')
    } finally {
      setEditLoading(false)
    }
  }

  // 页面加载时获取数据
  useEffect(() => {
    loadShareholders()
  }, [])

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">股东管理</h1>
            <p className="text-gray-600 mt-2">管理工厂股东信息和股权结构</p>
          </div>
          <div className="flex items-center space-x-4">
            {activeTab === 'shareholders' && (
              <AddShareholderDialog onShareholderAdded={handleShareholderAdded} />
            )}
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('shareholders')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'shareholders'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                股东信息
              </button>
              <button
                onClick={() => setActiveTab('profit')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'profit'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                利润分析
              </button>
            </nav>
          </div>
        </div>

        {/* 内容区域 */}
        {activeTab === 'shareholders' && (
          <>
            {/* Stats Card */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Users className="h-8 w-8 text-blue-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">股东总数</p>
                      <p className="text-2xl font-bold text-gray-900">{shareholders.length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 加载状态 */}
            {loading && (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">加载股东数据中...</span>
              </div>
            )}

            {/* 错误状态 */}
            {error && (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <Button onClick={loadShareholders} variant="outline">
                    重试
                  </Button>
                </div>
              </div>
            )}

            {/* Shareholders List */}
            {!loading && !error && (
              <div className="space-y-4">
                {shareholders.map((shareholder) => (
              <Card key={shareholder.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* 基本信息 */}
                      <div className="flex items-center space-x-3 mb-3">
                        <h3 className="text-lg font-semibold text-gray-900">{shareholder.name}</h3>
                        <Badge
                          className={
                            shareholder.shareholderType === 'FOUNDER' ? 'bg-purple-100 text-purple-800' :
                            shareholder.shareholderType === 'INVESTOR' ? 'bg-blue-100 text-blue-800' :
                            shareholder.shareholderType === 'EMPLOYEE' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }
                        >
                          {shareholder.shareholderType === 'FOUNDER' && '创始人'}
                          {shareholder.shareholderType === 'INVESTOR' && '投资人'}
                          {shareholder.shareholderType === 'EMPLOYEE' && '员工'}
                          {shareholder.shareholderType === 'PARTNER' && '合伙人'}
                        </Badge>
                        <Badge
                          className={
                            shareholder.status === 'active' ? 'bg-green-100 text-green-800' :
                            shareholder.status === 'inactive' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }
                        >
                          {shareholder.status === 'active' && '在职'}
                          {shareholder.status === 'inactive' && '暂停'}
                          {shareholder.status === 'exited' && '已退出'}
                        </Badge>
                      </div>

                      {/* 详细信息网格 */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">{shareholder.phone || '未提供电话'}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Mail className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">{shareholder.email || '未提供邮箱'}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">投资: {safeAmount(shareholder.investmentAmount)}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">
                            入股: {new Date(shareholder.joinDate).toLocaleDateString('zh-CN')}
                          </span>
                        </div>
                      </div>

                      {/* 股权信息 */}
                      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          {/* 🔧 只有当股份数量大于0时才显示 */}
                          {shareholder.shareCount > 0 && (
                            <div>
                              <span className="text-gray-600">持股数量: </span>
                              <span className="font-medium">{shareholder.shareCount.toLocaleString()} 股</span>
                            </div>
                          )}
                          <div className={shareholder.shareCount >0 ? "" : "col-span-2"}>
                            <span className="text-gray-600">持股比例: </span>
                            <span className="font-medium text-blue-600">{shareholder.sharePercentage}%</span>
                          </div>
                        </div>
                      </div>

                      {/* 地址信息 */}
                      {shareholder.address && (
                        <div className="mt-3 flex items-start space-x-2 text-sm">
                          <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                          <span className="text-gray-600">{shareholder.address}</span>
                        </div>
                      )}

                      {/* 备注信息 */}
                      {shareholder.notes && (
                        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                          <span className="text-gray-600">{shareholder.notes}</span>
                        </div>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex flex-col space-y-2 ml-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditShareholder(shareholder)}
                        className="text-blue-600 border-blue-300 hover:bg-blue-50"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        编辑
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleAddInvestment(shareholder)}
                        className="text-green-600 border-green-300 hover:bg-green-50"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        追加投资
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteShareholder(shareholder)}
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        删除
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {shareholders.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">还没有股东</h3>
                <p className="text-gray-600">点击上方"添加股东"按钮开始添加股东</p>
              </div>
            )}
              </div>
            )}
          </>
        )}

        {/* 利润分析标签页 */}
        {activeTab === 'profit' && (
          <ShareholderProfitAnalysis />
        )}

        {/* 🆕 编辑股东对话框 */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Edit className="h-5 w-5 text-blue-600" />
                <span>编辑股东信息</span>
              </DialogTitle>
              <DialogDescription>
                修改股东的基本信息（股权信息需要通过追加投资功能调整）
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-name">姓名 *</Label>
                <Input
                  id="edit-name"
                  value={editForm.name}
                  onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="请输入股东姓名"
                />
              </div>

              <div>
                <Label htmlFor="edit-phone">联系电话</Label>
                <Input
                  id="edit-phone"
                  type="tel"
                  value={editForm.phone}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '') // 只允许数字
                    setEditForm(prev => ({ ...prev, phone: value }))
                  }}
                  placeholder="请输入11位手机号码"
                  maxLength={11}
                  pattern="[0-9]*"
                />
              </div>

              <div>
                <Label htmlFor="edit-email">邮箱地址</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={editForm.email}
                  onChange={(e) => setEditForm(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="请输入邮箱地址"
                />
              </div>

              <div>
                <Label htmlFor="edit-address">联系地址</Label>
                <Input
                  id="edit-address"
                  value={editForm.address}
                  onChange={(e) => setEditForm(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="请输入联系地址"
                />
              </div>

              <div>
                <Label htmlFor="edit-notes">备注信息</Label>
                <Textarea
                  id="edit-notes"
                  value={editForm.notes}
                  onChange={(e) => setEditForm(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="请输入备注信息"
                  rows={3}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowEditDialog(false)}
                disabled={editLoading}
              >
                取消
              </Button>
              <Button
                onClick={handleEditSubmit}
                disabled={editLoading || !editForm.name.trim()}
              >
                {editLoading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                保存修改
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* 🆕 追加投资对话框 */}
        <Dialog open={showInvestmentDialog} onOpenChange={setShowInvestmentDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Plus className="h-5 w-5 text-green-600" />
                <span>追加投资</span>
              </DialogTitle>
              <DialogDescription>
                为股东 {selectedShareholder?.name} 记录追加投资，系统将自动重新计算股权比例
              </DialogDescription>
            </DialogHeader>

            {selectedShareholder && (
              <div className="space-y-4">
                {/* 当前股权信息 */}
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">当前股权信息</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    {/* 🔧 只有当股份数量大于0时才显示 */}
                    {selectedShareholder.shareCount > 0 && (
                      <div>
                        <span className="text-gray-600">持股数量: </span>
                        <span className="font-medium">{selectedShareholder.shareCount.toLocaleString()} 股</span>
                      </div>
                    )}
                    <div className={selectedShareholder.shareCount >0 ? "" : "col-span-2"}>
                      <span className="text-gray-600">持股比例: </span>
                      <span className="font-medium">{selectedShareholder.sharePercentage}%</span>
                    </div>
                    <div className="col-span-2">
                      <span className="text-gray-600">累计投资: </span>
                      <span className="font-medium">{safeAmount(selectedShareholder.investmentAmount)}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="investment-amount">追加投资金额 *</Label>
                  <Input
                    id="investment-amount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={investmentForm.amount || ''}
                    onChange={(e) => setInvestmentForm(prev => ({
                      ...prev,
                      amount: parseFloat(e.target.value) || 0
                    }))}
                    placeholder="请输入追加投资金额"
                  />
                </div>

                {/* 🆕 股权调整方式选择 */}
                <div>
                  <Label>股权调整方式 *</Label>
                  <div className="space-y-3 mt-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="adjustment-auto"
                        name="adjustmentType"
                        value="auto"
                        checked={investmentForm.adjustmentType === 'auto'}
                        onChange={(e) => setInvestmentForm(prev => ({
                          ...prev,
                          adjustmentType: e.target.value as 'auto' | 'manual'
                        }))}
                        className="h-4 w-4 text-blue-600"
                      />
                      <label htmlFor="adjustment-auto" className="text-sm font-medium text-gray-700">
                        自动调整（科学稀释）
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 ml-6">
                      💡 系统会根据所有股东的投资金额重新计算股权比例，确保公平合理
                    </p>

                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="adjustment-manual"
                        name="adjustmentType"
                        value="manual"
                        checked={investmentForm.adjustmentType === 'manual'}
                        onChange={(e) => setInvestmentForm(prev => ({
                          ...prev,
                          adjustmentType: e.target.value as 'auto' | 'manual'
                        }))}
                        className="h-4 w-4 text-blue-600"
                      />
                      <label htmlFor="adjustment-manual" className="text-sm font-medium text-gray-700">
                        手动调整
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 ml-6">
                      🔧 您可以手动指定该股东追加投资后的新股权比例
                    </p>
                  </div>
                </div>

                {/* 🆕 手动调整时的股权比例输入 */}
                {investmentForm.adjustmentType === 'manual' && (
                  <div>
                    <Label htmlFor="new-share-percentage">新股权比例 (%) *</Label>
                    <Input
                      id="new-share-percentage"
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      value={investmentForm.newSharePercentage || ''}
                      onChange={(e) => setInvestmentForm(prev => ({
                        ...prev,
                        newSharePercentage: parseFloat(e.target.value) || 0
                      }))}
                      placeholder="请输入新的股权比例"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      当前股权比例：{selectedShareholder?.sharePercentage}%
                    </p>
                  </div>
                )}

                <div>
                  <Label htmlFor="investment-notes">投资说明</Label>
                  <Textarea
                    id="investment-notes"
                    value={investmentForm.notes}
                    onChange={(e) => setInvestmentForm(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="请输入投资说明或备注"
                    rows={3}
                  />
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowInvestmentDialog(false)}
                disabled={editLoading}
              >
                取消
              </Button>
              <Button
                onClick={handleInvestmentSubmit}
                disabled={editLoading || investmentForm.amount <= 0}
              >
                {editLoading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                确认追加投资
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* 🆕 删除确认对话框 */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Trash2 className="h-5 w-5 text-red-600" />
                <span>删除股东</span>
              </DialogTitle>
              <DialogDescription>
                此操作将永久删除股东信息，无法恢复。请确认是否继续？
              </DialogDescription>
            </DialogHeader>

            {selectedShareholder && (
              <div className="space-y-4">
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="font-medium text-red-900 mb-2">即将删除的股东信息</h4>
                  <div className="space-y-1 text-sm text-red-800">
                    <p><span className="font-medium">姓名:</span> {selectedShareholder.name}</p>
                    <p><span className="font-medium">持股比例:</span> {selectedShareholder.sharePercentage}%</p>
                    <p><span className="font-medium">投资金额:</span> {safeAmount(selectedShareholder.investmentAmount)}</p>
                    <p><span className="font-medium">入股时间:</span> {new Date(selectedShareholder.joinDate).toLocaleDateString('zh-CN')}</p>
                  </div>
                </div>

                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    <strong>注意:</strong> 删除股东后，其他股东的持股比例将自动重新计算。
                  </p>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowDeleteDialog(false)}
                disabled={editLoading}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteConfirm}
                disabled={editLoading}
              >
                {editLoading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                确认删除
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
