'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ExternalLink, Copy, CheckCircle, Clock, DollarSign } from 'lucide-react'

export default function SiliconFlowSetupGuide() {
  const [copiedStep, setCopiedStep] = useState<number | null>(null)

  const copyToClipboard = (text: string, stepNumber: number) => {
    navigator.clipboard.writeText(text)
    setCopiedStep(stepNumber)
    setTimeout(() => setCopiedStep(null), 2000)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl flex items-center gap-2">
            🚀 硅基流动 API 申请指南
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <AlertDescription>
              <strong>为什么选择硅基流动？</strong><br/>
              • 🚀 <strong>速度提升10倍以上</strong>：针对DeepSeek V3模型进行了深度优化<br/>
              • 🇨🇳 <strong>国内访问稳定</strong>：无需担心网络连接问题<br/>
              • 💰 <strong>有免费额度</strong>：新用户可免费试用<br/>
              • 🔌 <strong>兼容OpenAI格式</strong>：无需修改代码，直接替换
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl">📋 申请步骤</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 步骤1 */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline">步骤 1</Badge>
              <span className="font-semibold">注册硅基流动账号</span>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">访问硅基流动官网并注册账号</p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('https://cloud.siliconflow.cn/account/ak', '_blank')}
                  className="flex items-center gap-1"
                >
                  <ExternalLink className="h-3 w-3" />
                  打开注册页面
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard('https://cloud.siliconflow.cn/account/ak', 1)}
                  className="flex items-center gap-1"
                >
                  {copiedStep === 1 ? <CheckCircle className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                  复制链接
                </Button>
              </div>
            </div>
          </div>

          {/* 步骤2 */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline">步骤 2</Badge>
              <span className="font-semibold">获取 API Key</span>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">登录后进入控制台，找到"API密钥"页面</p>
              <ol className="text-sm text-gray-600 list-decimal list-inside space-y-1">
                <li>点击"创建新的API Key"</li>
                <li>设置密钥名称（如：风口识别系统）</li>
                <li>复制生成的API Key（格式：sk-xxxxxx）</li>
                <li>妥善保存API Key，不要泄露给他人</li>
              </ol>
            </div>
          </div>

          {/* 步骤3 */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline">步骤 3</Badge>
              <span className="font-semibold">配置到系统中</span>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">将API Key配置到系统中</p>
              <div className="bg-gray-100 p-3 rounded font-mono text-sm">
                <div className="flex items-center justify-between">
                  <span>src/lib/config/api-keys.ts</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(`// 将你的硅基流动API Key替换到这里
SILICONFLOW: 'sk-your-actual-api-key-here'`, 3)}
                    className="flex items-center gap-1"
                  >
                    {copiedStep === 3 ? <CheckCircle className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                    复制代码
                  </Button>
                </div>
                <div className="mt-2 text-gray-600">
                  // 将你的硅基流动API Key替换到这里<br/>
                  SILICONFLOW: 'sk-your-actual-api-key-here'
                </div>
              </div>
            </div>
          </div>

          {/* 步骤4 */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline">步骤 4</Badge>
              <span className="font-semibold">测试连接</span>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">配置完成后，选择"硅基流动"提供商进行测试</p>
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-green-500" />
                <span>预期识别时间：5-10秒（比官方API快10倍以上）</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            价格对比
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">服务商</th>
                  <th className="text-left p-2">模型</th>
                  <th className="text-left p-2">输入价格</th>
                  <th className="text-left p-2">输出价格</th>
                  <th className="text-left p-2">优势</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="p-2">
                    <Badge variant="secondary">硅基流动</Badge>
                  </td>
                  <td className="p-2">DeepSeek-V3</td>
                  <td className="p-2">¥2.00/M tokens</td>
                  <td className="p-2">¥8.00/M tokens</td>
                  <td className="p-2">
                    <div className="space-y-1">
                      <div className="text-green-600">🚀 10x+ 速度提升</div>
                      <div className="text-blue-600">🇨🇳 国内访问稳定</div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="p-2">
                    <Badge variant="outline">DeepSeek官方</Badge>
                  </td>
                  <td className="p-2">deepseek-chat</td>
                  <td className="p-2">$0.27/M tokens</td>
                  <td className="p-2">$1.10/M tokens</td>
                  <td className="p-2">
                    <div className="text-gray-600">官方原生</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl">❓ 常见问题</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <p className="font-semibold text-sm">Q: 硅基流动是否安全可靠？</p>
            <p className="text-sm text-gray-600">A: 硅基流动是国内知名的AI基础设施提供商，已为众多企业提供服务，安全可靠。</p>
          </div>
          <div>
            <p className="font-semibold text-sm">Q: 免费额度有多少？</p>
            <p className="text-sm text-gray-600">A: 新用户注册后会获得免费额度，具体额度请查看官网最新政策。</p>
          </div>
          <div>
            <p className="font-semibold text-sm">Q: 如何充值？</p>
            <p className="text-sm text-gray-600">A: 在硅基流动控制台中可以通过支付宝、微信等方式充值。</p>
          </div>
          <div>
            <p className="font-semibold text-sm">Q: 识别准确性如何？</p>
            <p className="text-sm text-gray-600">A: 使用相同的DeepSeek V3模型，准确性与官方API一致，但速度更快。</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
