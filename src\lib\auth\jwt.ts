/**
 * 🇨🇳 风口云平台 - JWT认证工具
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - JWT token生成和验证
 * - 用户会话管理
 * - 安全的认证机制
 */

import jwt from 'jsonwebtoken'
import { NextRequest } from 'next/server'

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '8h'  // 8小时，平衡安全性和用户体验
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '30d'

// Token载荷接口
export interface JWTPayload {
  userId: string
  username: string
  name: string  // 用户中文姓名
  userType: 'admin' | 'factory_user'
  factoryId?: string
  role: string
  sessionId?: string // 🔧 新增：会话ID
  iat?: number
  exp?: number
}

// 认证结果接口
export interface AuthResult {
  success: boolean
  user?: JWTPayload
  error?: string
}

/**
 * 生成访问令牌
 */
export function generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  // 🔧 确保包含sessionId
  const tokenPayload = {
    ...payload,
    sessionId: payload.sessionId || require('uuid').v4() // 如果没有sessionId则生成一个
  }

  return jwt.sign(tokenPayload, JWT_SECRET as string, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'factorysystem',
    audience: 'factorysystem-users'
  } as jwt.SignOptions)
}

/**
 * 生成刷新令牌
 */
export function generateRefreshToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  // 🔧 确保包含sessionId
  const tokenPayload = {
    ...payload,
    sessionId: payload.sessionId || require('uuid').v4() // 如果没有sessionId则生成一个
  }

  return jwt.sign(tokenPayload, JWT_SECRET as string, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
    issuer: 'factorysystem',
    audience: 'factorysystem-refresh'
  } as jwt.SignOptions)
}

/**
 * 验证访问令牌
 */
export function verifyAccessToken(token: string): AuthResult {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'factorysystem',
      audience: 'factorysystem-users'
    }) as JWTPayload

    return {
      success: true,
      user: decoded
    }
  } catch (error) {
    const err = error as Error
    console.error('❌ JWT验证失败:', err.message)

    let errorMessage = '令牌无效'
    if (err.name === 'TokenExpiredError') {
      errorMessage = '令牌已过期'
    } else if (err.name === 'JsonWebTokenError') {
      errorMessage = '令牌格式错误'
    }

    return {
      success: false,
      error: errorMessage
    }
  }
}

/**
 * 验证刷新令牌
 */
export function verifyRefreshToken(token: string): AuthResult {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'factorysystem',
      audience: 'factorysystem-refresh'
    }) as JWTPayload

    return {
      success: true,
      user: decoded
    }
  } catch (error) {
    const err = error as Error
    console.error('❌ 刷新令牌验证失败:', err.message)
    return {
      success: false,
      error: '刷新令牌无效'
    }
  }
}

/**
 * 从请求中提取Bearer token
 */
export function extractTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization')

  console.log('🔍 提取令牌 - Authorization头:', authHeader ? '存在' : '不存在')

  if (!authHeader) {
    console.log('❌ 没有Authorization头')
    return null
  }

  const parts = authHeader.split(' ')
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    console.log('❌ Authorization头格式错误:', authHeader)
    return null
  }

  console.log('✅ 成功提取令牌，长度:', parts[1].length)
  return parts[1]
}

/**
 * 从请求中获取用户信息
 */
export function getUserFromRequest(request: NextRequest): AuthResult {
  const token = extractTokenFromRequest(request)
  
  if (!token) {
    return {
      success: false,
      error: '缺少认证令牌'
    }
  }

  return verifyAccessToken(token)
}

/**
 * 检查用户权限
 */
export function hasPermission(user: JWTPayload, requiredRole: string): boolean {
  // 管理员拥有所有权限
  if (user.userType === 'admin') {
    return true
  }

  // 检查角色权限
  return user.role === requiredRole
}

/**
 * 检查工厂数据访问权限
 */
export function canAccessFactory(user: JWTPayload, factoryId: string): boolean {
  // 管理员可以访问所有工厂
  if (user.userType === 'admin') {
    return true
  }

  // 工厂用户只能访问自己的工厂
  return user.factoryId === factoryId
}

/**
 * 安全解码 JWT payload（处理 URL-safe base64）
 */
export function decodeJWTPayload(token: string): any | null {
  try {
    // 验证 JWT 格式
    const parts = token.split('.')
    if (parts.length !== 3) {
      console.error('❌ Token 格式无效 - 部分数量:', parts.length)
      return null
    }

    // 获取 payload 部分
    const [, payload] = parts
    if (!payload) {
      console.error('❌ Token payload 部分为空')
      return null
    }

    // 修复 base64 填充问题和 URL-safe base64 转换
    let fixedPayload = payload
      .replace(/-/g, '+')  // 将 URL-safe base64 的 - 替换为 +
      .replace(/_/g, '/')  // 将 URL-safe base64 的 _ 替换为 /

    // 确保 base64 字符串长度是4的倍数
    while (fixedPayload.length % 4 !== 0) {
      fixedPayload += '='
    }

    // 解码 base64
    const decodedPayload = atob(fixedPayload)
    if (!decodedPayload) {
      console.error('❌ Token payload 解码为空')
      return null
    }

    // 解析 JSON
    return JSON.parse(decodedPayload)
  } catch (error) {
    console.error('❌ JWT payload 解码失败:', error)
    return null
  }
}

/**
 * 检查 JWT 是否过期
 */
export function isJWTExpired(token: string): boolean {
  const payload = decodeJWTPayload(token)
  if (!payload || !payload.exp) {
    return true
  }

  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp < currentTime
}

/**
 * 检查 JWT 是否即将过期（指定分钟内）
 */
export function isJWTExpiringSoon(token: string, minutesThreshold: number = 30): boolean {
  const payload = decodeJWTPayload(token)
  if (!payload || !payload.exp) {
    return true
  }

  const currentTime = Math.floor(Date.now() / 1000)
  const thresholdSeconds = minutesThreshold * 60

  return (payload.exp - currentTime) <= thresholdSeconds
}

/**
 * 生成令牌对
 */
export function generateTokenPair(payload: Omit<JWTPayload, 'iat' | 'exp'>) {
  return {
    accessToken: generateAccessToken(payload),
    refreshToken: generateRefreshToken(payload),
    tokenType: 'Bearer' as const,
    expiresIn: JWT_EXPIRES_IN
  }
}
