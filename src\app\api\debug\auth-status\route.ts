/**
 * 🔧 调试API - 检查认证状态
 */

import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest } from '@/lib/auth/jwt'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 调试：检查认证状态')
    
    // 检查Authorization头
    const authHeader = request.headers.get('authorization')
    console.log('🔍 Authorization头:', authHeader ? '存在' : '不存在')
    
    if (authHeader) {
      console.log('🔍 Authorization头内容:', authHeader.substring(0, 50) + '...')
    }
    
    // 尝试获取用户信息
    const authResult = getUserFromRequest(request)
    
    return NextResponse.json({
      success: true,
      authStatus: {
        hasAuthHeader: !!authHeader,
        authHeaderPreview: authHeader ? authHeader.substring(0, 50) + '...' : null,
        authResult: {
          success: authResult.success,
          error: authResult.error,
          user: authResult.user ? {
            userId: authResult.user.userId,
            username: authResult.user.username,
            userType: authResult.user.userType,
            factoryId: authResult.user.factoryId
          } : null
        }
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('❌ 认证状态检查失败:', error)
    return NextResponse.json({
      success: false,
      error: '认证状态检查失败',
      message: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
