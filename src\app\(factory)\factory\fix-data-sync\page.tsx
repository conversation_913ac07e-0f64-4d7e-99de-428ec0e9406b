"use client"

import { useState } from "react"
import { DashboardLayout  } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { RefreshCw, CheckCircle, AlertCircle, Database, Users } from "lucide-react"
import { useAuthStore } from "@/lib/store/auth"
import { db } from "@/lib/database/client"

export default function FixDataSyncPage() {
  const { factoryId } = useAuthStore()
  const [testing, setTesting] = useState(false)
  const [result, setResult] = useState<unknown>(null)

  const handleFixDataSync = async () => {
    if (!factoryId) {
      alert('请先登录工厂账户')
      return
    }

    try {
      setTesting(true)
      setResult(null)

      console.log('🔧 开始修复数据同步问题...')
      
      // 调用数据同步修复功能
      const syncResult = await db.fixDataSyncIssues(factoryId)
      
      setResult(syncResult)
      console.log('🔧 修复结果:', syncResult)

    } catch (error) {
      console.error('❌ 修复失败:', error)
      setResult({
        success: false,
        message: `修复失败: ${error.message}`,
        details: []
      })
    } finally {
      setTesting(false)
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-8">数据一致性修复工具</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 修复工具 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-5 w-5" />
                  <span>数据一致性修复</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 className="font-medium text-yellow-900 mb-2">问题说明</h4>
                    <p className="text-sm text-yellow-800">
                      订单管理和账单管理页面显示的付款状态可能不一致，这是因为：
                    </p>
                    <ul className="text-sm text-yellow-800 mt-2 space-y-1">
                      <li>• 订单管理：直接显示订单表中的付款状态</li>
                      <li>• 账单管理：使用客户表中的统计数据</li>
                      <li>• 当订单付款状态更新时，客户统计可能未同步</li>
                    </ul>
                  </div>
                  
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">修复功能</h4>
                    <p className="text-sm text-blue-800">
                      此工具会检查所有客户的统计数据，并与实际订单数据进行对比，
                      自动修复不一致的数据。
                    </p>
                  </div>
                  
                  <Button 
                    onClick={handleFixDataSync}
                    disabled={testing || !factoryId}
                    className="w-full"
                    size="lg"
                  >
                    {testing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        正在修复数据...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        开始修复数据一致性
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 修复结果 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  {result ? (
                    result.success ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-600" />
                    )
                  ) : (
                    <Users className="h-5 w-5" />
                  )}
                  <span>修复结果</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!result ? (
                  <div className="text-center py-8 text-gray-500">
                    <Database className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>点击修复按钮开始检查数据一致性</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className={`p-3 rounded-lg ${
                      result.success 
                        ? 'bg-green-50 border border-green-200 text-green-800'
                        : 'bg-red-50 border border-red-200 text-red-800'
                    }`}>
                      <p className="font-medium">{result.message}</p>
                    </div>

                    {result.details && result.details.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3">详细修复记录：</h4>
                        <div className="space-y-2 max-h-96 overflow-y-auto">
                          {result.details.map((detail: unknown, index: number) => (
                            <div 
                              key={index}
                              className={`p-3 rounded border text-sm ${
                                detail.status === 'success' 
                                  ? 'bg-green-50 border-green-200 text-green-700'
                                  : detail.status === 'failed' || detail.status === 'error'
                                  ? 'bg-red-50 border-red-200 text-red-700'
                                  : 'bg-gray-50 border-gray-200 text-gray-700'
                              }`}
                            >
                              <div className="flex items-center justify-between mb-1">
                                <div className="font-medium">{detail.clientName}</div>
                                <div className={`text-xs px-2 py-1 rounded ${
                                  detail.status === 'success' ? 'bg-green-100 text-green-800' :
                                  detail.status === 'failed' || detail.status === 'error' ? 'bg-red-100 text-red-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {detail.status === 'success' ? '已修复' :
                                   detail.status === 'no_change' ? '无需修复' :
                                   detail.status === 'failed' ? '修复失败' : '错误'}
                                </div>
                              </div>
                              
                              {detail.message && (
                                <div className="text-xs mb-2">{detail.message}</div>
                              )}
                              
                              {detail.error && (
                                <div className="text-xs text-red-600 mb-2">错误: {detail.error}</div>
                              )}
                              
                              {detail.before && detail.after && (
                                <div className="text-xs space-y-1">
                                  <div className="grid grid-cols-3 gap-2">
                                    <div>
                                      <div className="font-medium">修复前:</div>
                                      <div>总额: ¥{detail.before.totalAmount}</div>
                                      <div>已付: ¥{detail.before.paidAmount}</div>
                                      <div>未付: ¥{detail.before.unpaidAmount}</div>
                                    </div>
                                    <div>
                                      <div className="font-medium">修复后:</div>
                                      <div>总额: ¥{detail.after.totalAmount}</div>
                                      <div>已付: ¥{detail.after.paidAmount}</div>
                                      <div>未付: ¥{detail.after.unpaidAmount}</div>
                                    </div>
                                    <div>
                                      <div className="font-medium">差异:</div>
                                      <div>总额: ¥{detail.after.totalAmount - detail.before.totalAmount}</div>
                                      <div>已付: ¥{detail.after.paidAmount - detail.before.paidAmount}</div>
                                      <div>未付: ¥{detail.after.unpaidAmount - detail.before.unpaidAmount}</div>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 使用说明 */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>使用说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">何时使用此工具？</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 发现订单管理和账单管理显示的付款状态不一致</li>
                    <li>• 客户的已付金额与实际订单付款记录不符</li>
                    <li>• 系统升级或数据迁移后</li>
                    <li>• 定期数据维护</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">修复过程说明</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 获取工厂所有客户和订单数据</li>
                    <li>• 计算每个客户的实际统计数据</li>
                    <li>• 对比客户表中的统计数据</li>
                    <li>• 自动更新不一致的客户统计信息</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
