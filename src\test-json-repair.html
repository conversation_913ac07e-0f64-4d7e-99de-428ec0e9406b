<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON 修复功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
            padding: 15px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .json-input {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .json-output {
            background: #e8f5e8;
            border: 1px solid #4CAF50;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .error {
            background: #ffe8e8;
            border: 1px solid #f44336;
            color: #d32f2f;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4CAF50;
            color: #2e7d32;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #1976D2;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JSON 修复功能测试</h1>
        <p>测试 DeepSeek API 的 JSON 修复功能，验证各种截断和错误的 JSON 是否能被正确修复。</p>
        
        <button class="button" onclick="runAllTests()">🚀 运行所有测试</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        // 测试用的截断 JSON 样本
        const testCases = [
            {
                name: "对象中间截断",
                json: `{
  "projects": [{
    "projectName": "",
    "clientInfo": "",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          },
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 1525, "width": 255, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          },
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "`
            },
            {
                name: "数组中间截断",
                json: `{
  "projects": [{
    "projectName": "",
    "clientInfo": "",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          }`
            },
            {
                name: "缺少逗号",
                json: `{
  "projects": [{
    "projectName": "",
    "clientInfo": "",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          }
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 1525, "width": 255, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          }
        ]
      }]
    }]
  }]
}`
            }
        ];

        // 模拟 DeepSeek API 的修复函数
        function fixCommonJsonErrors(jsonStr) {
            let fixed = jsonStr;

            try {
                console.log('🔧 开始修复JSON，原始长度:', jsonStr.length);
                
                // 0. 检查并修复截断的JSON
                fixed = fixTruncatedJson(fixed);
                
                // 1. 修复缺失的引号
                fixed = fixed.replace(/(\w+):/g, '"$1":');

                // 2. 修复尾随逗号
                fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

                // 3. 修复缺失的逗号 - 改进版本
                fixed = fixed.replace(/}(\s*){/g, '},$1{');
                fixed = fixed.replace(/}(\s*)"/g, '},$1"');
                fixed = fixed.replace(/](\s*)"/g, '],$1"');
                fixed = fixed.replace(/](\s*){/g, '],$1{');

                // 4. 修复数字后缺失逗号
                fixed = fixed.replace(/(\d)(\s*)"(\w+)":/g, '$1,$2"$3":');
                fixed = fixed.replace(/(\d)(\s*){/g, '$1,$2{');

                // 5. 修复字符串后缺失逗号
                fixed = fixed.replace(/"(\s*)"(\w+)":/g, '"$1,"$2":');
                fixed = fixed.replace(/"(\s*){/g, '"$1,{');

                // 6. 修复对象/数组后缺失逗号
                fixed = fixed.replace(/}(\s*){/g, '},$1{');
                fixed = fixed.replace(/](\s*)\[/g, '],$1[');

                console.log('🔧 JSON修复完成，修复后长度:', fixed.length);
                return fixed;
            } catch (error) {
                console.error('❌ JSON修复失败:', error);
                return jsonStr;
            }
        }

        function fixTruncatedJson(jsonStr) {
            let fixed = jsonStr.trim();
            
            console.log('🚨 开始截断修复');
            
            // 检查是否以不完整的结构结尾
            if (fixed.endsWith('"')) {
                const lastQuoteIndex = fixed.lastIndexOf('"', fixed.length - 2);
                if (lastQuoteIndex !== -1) {
                    const beforeQuote = fixed.substring(0, lastQuoteIndex).trim();
                    if (beforeQuote.endsWith(':') || beforeQuote.endsWith(',')) {
                        fixed = fixed.substring(0, lastQuoteIndex);
                    }
                }
            }
            
            // 检查是否以不完整的对象开始结尾
            if (fixed.endsWith('": {') || fixed.endsWith('": [')) {
                const colonIndex = fixed.lastIndexOf('":');
                if (colonIndex !== -1) {
                    const quoteIndex = fixed.lastIndexOf('"', colonIndex - 1);
                    if (quoteIndex !== -1) {
                        fixed = fixed.substring(0, quoteIndex);
                    }
                }
            }
            
            // 确保JSON结构完整闭合
            let openBraces = 0;
            let openBrackets = 0;
            let inString = false;
            let escapeNext = false;
            
            for (let i = 0; i < fixed.length; i++) {
                const char = fixed[i];
                
                if (escapeNext) {
                    escapeNext = false;
                    continue;
                }
                
                if (char === '\\') {
                    escapeNext = true;
                    continue;
                }
                
                if (char === '"') {
                    inString = !inString;
                    continue;
                }
                
                if (!inString) {
                    if (char === '{') openBraces++;
                    else if (char === '}') openBraces--;
                    else if (char === '[') openBrackets++;
                    else if (char === ']') openBrackets--;
                }
            }
            
            // 添加缺失的闭合符号
            while (openBrackets > 0) {
                fixed += ']';
                openBrackets--;
            }
            while (openBraces > 0) {
                fixed += '}';
                openBraces--;
            }
            
            console.log('🔧 截断修复完成');
            return fixed;
        }

        function testJsonRepair(testCase) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-case';
            
            resultDiv.innerHTML = `
                <h3>📝 ${testCase.name}</h3>
                <div>
                    <strong>原始 JSON:</strong>
                    <div class="json-input">${testCase.json}</div>
                </div>
            `;
            
            try {
                // 尝试解析原始 JSON
                JSON.parse(testCase.json);
                resultDiv.innerHTML += `<div class="status success">✅ 原始 JSON 有效，无需修复</div>`;
            } catch (originalError) {
                resultDiv.innerHTML += `<div class="status error">❌ 原始 JSON 无效: ${originalError.message}</div>`;
                
                try {
                    // 尝试修复
                    const fixed = fixCommonJsonErrors(testCase.json);
                    const parsed = JSON.parse(fixed);
                    
                    resultDiv.innerHTML += `
                        <div class="status success">✅ 修复成功！</div>
                        <div>
                            <strong>修复后的 JSON:</strong>
                            <div class="json-output">${JSON.stringify(parsed, null, 2)}</div>
                        </div>
                    `;
                } catch (fixError) {
                    resultDiv.innerHTML += `<div class="status error">❌ 修复失败: ${fixError.message}</div>`;
                }
            }
            
            return resultDiv;
        }

        function runAllTests() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>🧪 测试结果</h2>';
            
            testCases.forEach(testCase => {
                const result = testJsonRepair(testCase);
                resultsDiv.appendChild(result);
            });
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            console.log('🔧 JSON 修复测试页面已加载');
        };
    </script>
</body>
</html>
