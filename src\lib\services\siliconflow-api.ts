/**
 * 硅基流动 API 服务
 * 提供高速的 DeepSeek V3 模型访问
 */

interface SiliconFlowConfig {
  apiKey: string
  baseURL: string
  timeout: number
}

interface SiliconFlowMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

interface SiliconFlowRequest {
  model: string
  messages: SiliconFlowMessage[]
  max_tokens?: number
  temperature?: number
  stream?: boolean
}

interface SiliconFlowResponse {
  id: string
  choices: Array<{
    message: {
      role: string
      content: string
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
  created: number
  model: string
}

export class SiliconFlowAPI {
  private config: SiliconFlowConfig
  private lastInputText: string = '' // 保存输入文本供备用解析使用

  constructor(apiKey: string) {
    this.config = {
      apiKey,
      baseURL: 'https://api.siliconflow.cn/v1',
      timeout: 60000 // 增加到60秒
    }
  }

  /**
   * 调用硅基流动 API（带重试机制）
   */
  async callAPI(prompt: string, model: string = 'deepseek-ai/DeepSeek-V3', retries: number = 2): Promise<SiliconFlowResponse> {
    const startTime = performance.now()
    console.log('🚀 [硅基流动] 开始API调用:', new Date().toISOString())
    console.log('🚀 [硅基流动] 使用模型:', model)
    console.log('🚀 [硅基流动] Prompt长度:', prompt.length, '字符')

    const requestBody: SiliconFlowRequest = {
      model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 800,
      temperature: 0.2,
      stream: false
    }

    console.log('🚀 [硅基流动] 请求体大小:', JSON.stringify(requestBody).length, '字符')

    let response: Response | undefined

    for (let attempt = 0; attempt <= retries; attempt++) {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

      const fetchStartTime = performance.now()

      try {
        if (attempt > 0) {
          console.log(`🔄 [硅基流动] 第${attempt + 1}次尝试...`)
        }

        response = await fetch(`${this.config.baseURL}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.config.apiKey}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal
        })

        const fetchTime = performance.now() - fetchStartTime
        console.log(`🌐 [硅基流动] Fetch请求耗时: ${fetchTime.toFixed(2)}ms`)
        clearTimeout(timeoutId)

        // 成功获得响应，跳出重试循环
        break

      } catch (error: any) {
        const fetchTime = performance.now() - fetchStartTime
        console.log(`❌ [硅基流动] 第${attempt + 1}次请求失败，耗时: ${fetchTime.toFixed(2)}ms`)
        clearTimeout(timeoutId)

        if (attempt === retries) {
          // 最后一次尝试失败
          if (error.name === 'AbortError') {
            throw new Error(`硅基流动API请求超时（${this.config.timeout/1000}秒），已重试${retries}次，请检查网络连接`)
          }
          throw error
        }

        // 等待一段时间后重试
        const waitTime = Math.pow(2, attempt) * 1000 // 指数退避：1s, 2s, 4s...
        console.log(`⏳ [硅基流动] 等待${waitTime}ms后重试...`)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }

    // 检查是否成功获得响应
    if (!response) {
      throw new Error('硅基流动API请求失败：所有重试都未能获得响应')
    }

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ [硅基流动] API请求失败:', response.status, errorText)
      throw new Error(`硅基流动API请求失败: ${response.status} ${errorText}`)
    }

    const jsonParseStartTime = performance.now()
    const data: SiliconFlowResponse = await response.json()
    console.log(`📊 [硅基流动] JSON解析耗时: ${(performance.now() - jsonParseStartTime).toFixed(2)}ms`)
    console.log(`📊 [硅基流动] 响应数据大小: ${JSON.stringify(data).length} 字符`)

    const totalTime = performance.now() - startTime
    console.log(`🎉 [硅基流动] API调用总耗时: ${totalTime.toFixed(2)}ms (${(totalTime/1000).toFixed(2)}秒)`)

    // 记录token使用情况
    if (data.usage) {
      console.log(`💰 [硅基流动] Token使用情况:`)
      console.log(`   输入: ${data.usage.prompt_tokens} tokens (¥${(data.usage.prompt_tokens * 2.00 / 1000000).toFixed(4)})`)
      console.log(`   输出: ${data.usage.completion_tokens} tokens (¥${(data.usage.completion_tokens * 8.00 / 1000000).toFixed(4)})`)
      console.log(`   总计: ${data.usage.total_tokens} tokens (¥${((data.usage.prompt_tokens * 2.00 + data.usage.completion_tokens * 8.00) / 1000000).toFixed(4)})`)
    }

    return data
  }

  /**
   * 测试API连接（简单快速）
   */
  async testConnection(): Promise<{ success: boolean; duration: number; error?: string }> {
    const startTime = performance.now()

    try {
      console.log('🔧 [硅基流动] 开始连接测试...')

      const requestBody = {
        model: 'deepseek-ai/DeepSeek-V3',
        messages: [{ role: 'user', content: '测试' }],
        max_tokens: 5,
        temperature: 0,
        stream: false
      }

      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(15000) // 15秒超时
      })

      const duration = performance.now() - startTime

      if (response.ok) {
        const data = await response.json()
        console.log('✅ [硅基流动] 连接测试成功')
        return { success: true, duration }
      } else {
        const errorText = await response.text()
        console.error('❌ [硅基流动] 连接测试失败:', response.status, errorText)
        return {
          success: false,
          duration,
          error: `API错误: ${response.status} - ${errorText}`
        }
      }
    } catch (error: any) {
      const duration = performance.now() - startTime
      console.error('❌ [硅基流动] 连接测试异常:', error)

      let errorMessage = '未知错误'
      if (error.name === 'TimeoutError') {
        errorMessage = '连接超时（15秒）'
      } else if (error.message) {
        errorMessage = error.message
      }

      return {
        success: false,
        duration,
        error: errorMessage
      }
    }
  }

  /**
   * 分析风口订单（使用硅基流动的DeepSeek V3）
   */
  async analyzeVentOrder(text: string): Promise<any> {
    const startTime = performance.now()
    console.log('🚀 [硅基流动] 开始风口订单分析...')

    // 保存输入文本供备用解析使用
    this.lastInputText = text;

    // 使用优化的快速Prompt
    const prompt = this.buildFastPrompt(text)

    try {
      const response = await this.callAPI(prompt, 'deepseek-ai/DeepSeek-V3')
      
      const parseStartTime = performance.now()
      const result = this.parseResponse(response)
      console.log(`🔧 [硅基流动] JSON解析耗时: ${(performance.now() - parseStartTime).toFixed(2)}ms`)

      const totalTime = performance.now() - startTime
      console.log(`🎉 [硅基流动] 风口分析总耗时: ${totalTime.toFixed(2)}ms (${(totalTime/1000).toFixed(2)}秒)`)

      return result
    } catch (error) {
      console.error('❌ [硅基流动] 风口分析失败:', error)
      throw error
    }
  }

  /**
   * 构建快速Prompt
   */
  private buildFastPrompt(text: string): string {
    return `你是资深暖通工程师，请智能分析风口订单文本，运用专业知识深度理解内容。

📝 待分析文本：
${text}

🧠 智能分析要求：
- 理解文本结构和业务逻辑
- 识别专业术语和工程规格
- 智能解析尺寸和数量信息
- 判断风口类型和房间归属
- **保守处理楼层**：只有明确看到楼层标识才创建新楼层，否则统一放在1楼

🔍 核心规则：
- 宽度≤254mm = 出风口 (systemType: "double_white_outlet")
- 宽度≥255mm = 回风口 (systemType: "white_return")
- 数字<100当作厘米，需要×10转换为毫米
- **楼层识别（保守原则）**：
  ✅ 只有明确看到"一楼"、"二楼"、"3楼"等独立行才创建新楼层
  ❌ 严禁从房间编号推断楼层（A201≠2楼，A101≠1楼）
  ✅ 无明确楼层时统一使用"1楼"

返回格式（必须是有效的JSON）：
{
  "projects": [{
    "projectName": "项目名称",
    "clientInfo": "客户信息",
    "floors": [{
      "floorName": "楼层",
      "rooms": [{
        "roomName": "房间名称",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": "备注"
          }
        ]
      }]
    }]
  }]
}

🎯 请展现专业的AI理解能力，返回完整有效的JSON！`
  }

  /**
   * 解析API响应
   */
  private parseResponse(response: SiliconFlowResponse): any {
    try {
      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('API响应中没有内容')
      }

      console.log('🔍 [硅基流动] 原始响应长度:', content.length, '字符')
      console.log('🔍 [硅基流动] 原始响应内容:', content)

      // 多种方式清理JSON字符串
      let jsonStr = content.trim()

      // 移除markdown代码块标记
      jsonStr = jsonStr.replace(/^```json\s*/i, '')
      jsonStr = jsonStr.replace(/^```\s*/i, '')
      jsonStr = jsonStr.replace(/\s*```\s*$/g, '')

      // 移除可能的解释文字
      jsonStr = jsonStr.replace(/^[^{]*/, '') // 移除开头的非JSON内容
      jsonStr = jsonStr.replace(/[^}]*$/, '') // 移除结尾的非JSON内容

      // 查找JSON内容
      const firstBrace = jsonStr.indexOf('{')
      const lastBrace = jsonStr.lastIndexOf('}')

      if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
        jsonStr = jsonStr.substring(firstBrace, lastBrace + 1)
      }

      console.log('🧹 [硅基流动] 清理后JSON长度:', jsonStr.length)
      console.log('🧹 [硅基流动] 清理后JSON内容:', jsonStr)

      // 尝试修复常见的JSON错误
      jsonStr = this.fixCommonJSONErrors(jsonStr)

      let parsed
      try {
        parsed = JSON.parse(jsonStr)
      } catch (parseError) {
        // 先尝试备用解析方法
        const fallbackResult = this.fallbackParse(content);
        if (fallbackResult) {
          console.log('✅ [硅基流动] 备用解析成功，跳过JSON错误显示');
          return fallbackResult;
        }

        console.error('❌ [硅基流动] JSON解析失败，尝试修复...')
        console.error('错误详情:', parseError)
        console.error('问题JSON:', jsonStr)

        // 尝试更激进的修复
        jsonStr = this.aggressiveJSONFix(jsonStr)
        console.log('🔧 [硅基流动] 修复后JSON:', jsonStr)

        parsed = JSON.parse(jsonStr) // 再次尝试解析
      }
      
      // 验证和修正风口类型
      const processedProjects = this.validateVentTypes(parsed.projects || [])

      return {
        projects: processedProjects,
        confidence: 0.9,
        warnings: [],
        rawResponse: content,
        provider: 'siliconflow',
        model: 'deepseek-ai/DeepSeek-V3'
      }
    } catch (error) {
      console.error('❌ [硅基流动] 响应解析失败:', error)
      console.log('原始响应:', response.choices[0]?.message?.content)
      throw new Error(`硅基流动响应解析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 验证和修正风口类型
   */
  private validateVentTypes(projects: any[]): any[] {
    return projects.map(project => ({
      ...project,
      floors: project.floors?.map((floor: any) => ({
        ...floor,
        rooms: floor.rooms?.map((room: any) => ({
          ...room,
          vents: room.vents?.map((vent: any) => {
            const width = vent.dimensions?.width || 0
            let systemType = vent.systemType
            let originalType = vent.originalType
            
            // 强制验证风口类型
            if (width >= 255 && systemType === 'double_white_outlet') {
              console.log(`🔧 [硅基流动] 修正风口类型: 宽度${width}mm≥255mm，从出风口改为回风口`)
              systemType = 'white_return'
              originalType = '回风口'
            } else if (width < 255 && systemType === 'white_return') {
              console.log(`🔧 [硅基流动] 修正风口类型: 宽度${width}mm<255mm，从回风口改为出风口`)
              systemType = 'double_white_outlet'
              originalType = '出风口'
            }
            
            return {
              ...vent,
              systemType,
              originalType
            }
          }) || []
        })) || []
      })) || []
    }))
  }

  /**
   * 修复常见的JSON错误
   */
  private fixCommonJSONErrors(jsonStr: string): string {
    // 修复尾随逗号
    jsonStr = jsonStr.replace(/,(\s*[}\]])/g, '$1')

    // 修复缺少逗号的情况
    jsonStr = jsonStr.replace(/}(\s*){/g, '},$1{')
    jsonStr = jsonStr.replace(/](\s*){/g, '],$1{')

    // 修复单引号为双引号
    jsonStr = jsonStr.replace(/'/g, '"')

    // 修复属性名没有引号的情况
    jsonStr = jsonStr.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')

    return jsonStr
  }

  /**
   * 更激进的JSON修复
   */
  private aggressiveJSONFix(jsonStr: string): string {
    try {
      // 如果JSON被截断，尝试补全
      if (!jsonStr.endsWith('}')) {
        // 计算需要补全的括号数量
        const openBraces = (jsonStr.match(/{/g) || []).length
        const closeBraces = (jsonStr.match(/}/g) || []).length
        const openBrackets = (jsonStr.match(/\[/g) || []).length
        const closeBrackets = (jsonStr.match(/]/g) || []).length

        // 补全缺少的括号
        for (let i = 0; i < openBrackets - closeBrackets; i++) {
          jsonStr += ']'
        }
        for (let i = 0; i < openBraces - closeBraces; i++) {
          jsonStr += '}'
        }
      }

      // 移除最后一个逗号（如果存在）
      jsonStr = jsonStr.replace(/,(\s*[}\]])$/, '$1')

      return jsonStr
    } catch (error) {
      console.error('JSON修复失败:', error)
      return jsonStr
    }
  }

  /**
   * 备用解析方法 - 使用正则表达式提取关键信息（复制通义千问的逻辑）
   */
  private fallbackParse(content: string): any | null {
    try {
      console.log('🔄 [硅基流动] 尝试备用解析方法...');

      // 从原始输入文本中解析，而不是从损坏的JSON中解析
      const inputText = this.lastInputText || content;
      const lines = inputText.split('\n').filter(line => line.trim());

      const floors: any[] = [];
      let currentFloor = '1';
      let currentRoom = '房间';
      let projectName = '未指定项目';

      // 提取项目名称（使用通义千问的逻辑）
      if (lines.length > 0) {
        const firstLine = lines[0].trim();
        if (firstLine && !this.hasVentPattern(firstLine) && this.isValidProjectName(firstLine)) {
          projectName = firstLine.replace(/^[；;]/, ''); // 移除开头的分号
        }
      }

      console.log(`🔄 [硅基流动] 开始解析 ${lines.length} 行数据`);

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        console.log(`🔄 [硅基流动] 处理行: "${trimmedLine}"`);

        // 识别楼层（只识别明确的楼层标识）
        const floorMatch = trimmedLine.match(/^([一二三四五六七八九十]楼|[1-9]\d*楼)$/);
        if (floorMatch) {
          const floorText = floorMatch[1];
          if (floorText.includes('一')) currentFloor = '1';
          else if (floorText.includes('二')) currentFloor = '2';
          else if (floorText.includes('三')) currentFloor = '3';
          else currentFloor = floorText.replace(/[楼F]/g, '');
          console.log(`🔄 [硅基流动] 识别楼层: "${trimmedLine}" → "${currentFloor}"`);
          continue;
        }

        // 识别房间
        if (this.isRoomName(trimmedLine)) {
          currentRoom = trimmedLine;
          console.log(`🔄 [硅基流动] 识别房间: "${trimmedLine}"`);
          continue;
        }

        // 识别风口
        if (this.hasVentPattern(trimmedLine)) {
          console.log(`🔄 [硅基流动] 识别风口: "${trimmedLine}" (楼层: ${currentFloor}, 房间: ${currentRoom})`);
          this.addVentToFloors(floors, currentFloor, currentRoom, trimmedLine);
        } else {
          console.log(`🔄 [硅基流动] 跳过行: "${trimmedLine}" (不匹配任何模式)`);
        }
      }

      const result = {
        projects: [{
          projectName,
          clientInfo: projectName,
          floors
        }],
        confidence: 0.8,
        warnings: ['使用备用解析方法'],
        rawResponse: content,
        provider: 'siliconflow',
        model: 'deepseek-ai/DeepSeek-V3'
      };

      console.log('🔄 [硅基流动] 备用解析结果:', JSON.stringify(result, null, 2));
      console.log('🔄 [硅基流动] 解析到的楼层数:', floors.length);
      floors.forEach((floor, i) => {
        console.log(`🔄 [硅基流动] 楼层${i+1}: ${floor.floorName}, 房间数: ${floor.rooms.length}`);
        floor.rooms.forEach((room: any, j: number) => {
          console.log(`🔄 [硅基流动] 房间${j+1}: ${room.roomName}, 风口数: ${room.vents.length}`);
        });
      });

      return result;
    } catch (error) {
      console.error('🔄 [硅基流动] 备用解析失败:', error);
      return null;
    }
  }

  /**
   * 检查是否包含风口模式（复制通义千问的逻辑）
   */
  private hasVentPattern(line: string): boolean {
    // 先标准化符号，将各种分隔符统一为×
    const normalizedLine = line
      .replace(/✖️/g, '×')  // 先处理复合字符
      .replace(/[✖✘✕⨯*xX]/g, '×');  // 再处理其他字符
    return /\d+\s*×\s*\d+/.test(normalizedLine) ||
           /(出风|回风|进风|排风|送风)/.test(line);
  }

  /**
   * 检查是否是房间名称（复制通义千问的逻辑）
   */
  private isRoomName(line: string): boolean {
    const roomPatterns = [
      /^(活动室|客厅|茶室|棋牌室|餐厅|厨房|KTV)$/,
      /^[A-Z]\d{3}$/,  // A201, B302等
      /^(主卧|次卧|卧室|书房|办公室|会议室|大厅|走廊|检修口)$/
    ];
    return roomPatterns.some(pattern => pattern.test(line));
  }

  /**
   * 检查是否是有效的项目名称（复制通义千问的逻辑）
   */
  private isValidProjectName(line: string): boolean {
    // 排除尺寸信息
    if (/\d+\s*[×xX*✖️]\s*\d+/.test(line)) return false;
    // 排除风口关键词
    if (/(出风|回风|进风|排风|送风|风口)/.test(line)) return false;
    // 排除纯数字
    if (/^\d+$/.test(line)) return false;
    // 排除单位信息
    if (/(mm|cm|毫米|厘米)/.test(line)) return false;

    // 包含地址关键词或项目特征
    return /[小区花园广场大厦公寓别墅商城中心大楼写字楼苑城府园庄村院街路巷里弄]/.test(line) ||
           /^.{3,15}[A-Z]?$/.test(line); // 3-15字符，可能带字母后缀
  }

  /**
   * 添加风口到楼层结构（复制通义千问的逻辑）
   */
  private addVentToFloors(floors: any[], floorName: string, roomName: string, ventLine: string): void {
    const vent = this.parseVentLine(ventLine);
    if (!vent) return;

    let floor = floors.find(f => f.floorName === `${floorName}楼`);
    if (!floor) {
      floor = { floorName: `${floorName}楼`, rooms: [] };
      floors.push(floor);
    }

    let room = floor.rooms.find((r: any) => r.roomName === roomName);
    if (!room) {
      room = { roomName, vents: [] };
      floor.rooms.push(room);
    }

    room.vents.push(vent);
  }

  /**
   * 解析风口行（复制通义千问的逻辑）
   */
  private parseVentLine(line: string): any | null {
    console.log(`🔄 [硅基流动] 解析风口行: "${line}"`);

    // 先标准化符号，将各种分隔符统一为×
    const normalizedLine = line
      .replace(/✖️/g, '×')  // 先处理复合字符
      .replace(/[✖✘✕⨯*xX]/g, '×');  // 再处理其他字符
    console.log(`🔄 [硅基流动] 标准化后: "${normalizedLine}"`);

    // 提取尺寸
    const dimensionMatch = normalizedLine.match(/(\d+)\s*×\s*(\d+)/);
    if (!dimensionMatch) {
      console.log(`🔄 [硅基流动] 未找到尺寸匹配: "${normalizedLine}"`);
      return null;
    }

    let length = parseInt(dimensionMatch[1]);
    let width = parseInt(dimensionMatch[2]);
    console.log(`🔄 [硅基流动] 原始尺寸: ${length}×${width}`);

    // 单位转换
    if (length < 100) length *= 10;
    if (width < 100) width *= 10;
    console.log(`🔄 [硅基流动] 转换后尺寸: ${length}×${width}`);

    // 提取数量
    const quantityMatch = line.match(/(\d+)个/);
    const quantity = quantityMatch ? parseInt(quantityMatch[1]) : 1;
    console.log(`🔄 [硅基流动] 数量: ${quantity}`);

    // 判断类型
    const hasReturnKeywords = /(回风|进风|排风)/.test(line);
    const systemType = hasReturnKeywords || width >= 255 ? 'white_return' : 'double_white_outlet';
    console.log(`🔄 [硅基流动] 类型判断: 关键词=${hasReturnKeywords}, 宽度=${width}, 类型=${systemType}`);

    // 提取备注
    const notes = this.extractNotes(line);
    console.log(`🔄 [硅基流动] 备注: "${notes}"`);

    const result = {
      systemType,
      originalType: line,
      dimensions: { length, width, unit: 'mm' },
      quantity,
      notes
    };

    console.log(`🔄 [硅基流动] 风口解析结果:`, result);
    return result;
  }

  /**
   * 提取备注信息（复制通义千问的逻辑）
   */
  private extractNotes(line: string): string {
    // 移除尺寸信息后的剩余部分作为备注
    const cleanLine = line.replace(/\d+\s*[×xX*✖️]\s*\d+/, '').trim();
    const notes = cleanLine.replace(/^(出风|回风|进风|排风|送风)(口)?/, '').trim();
    return notes.replace(/^[，,、]/, '').trim();
  }
}

// 创建硅基流动API实例的工厂函数
export function getSiliconFlowAPI(apiKey: string): SiliconFlowAPI {
  return new SiliconFlowAPI(apiKey)
}
