# 🇨🇳 风口云平台 - MySQL数据库配置指南

## 📋 前置条件
- ✅ MySQL Server 8.0 已安装
- ✅ MySQL Workbench 8.0 已安装

## 🔧 数据库初始化步骤

### 1. 创建数据库
打开MySQL Workbench或命令行，执行以下SQL：

```sql
-- 创建开发环境数据库
CREATE DATABASE factorysystem_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建生产环境数据库（可选）
CREATE DATABASE factorysystem_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建专用用户（推荐）
CREATE USER 'factorysystem'@'localhost' IDENTIFIED BY 'FactorySystem2024!';

-- 授权
GRANT ALL PRIVILEGES ON factorysystem_dev.* TO 'factorysystem'@'localhost';
GRANT ALL PRIVILEGES ON factorysystem_prod.* TO 'factorysystem'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
```

### 2. 更新环境变量
在 `.env` 文件中更新数据库连接字符串：

```env
# 使用专用用户
DATABASE_URL="mysql://factorysystem:FactorySystem2024!@localhost:3306/factorysystem_dev"

# 或使用root用户（需要您的root密码）
# DATABASE_URL="mysql://root:您的root密码@localhost:3306/factorysystem_dev"
```

### 3. 运行数据库迁移
```bash
# 生成Prisma客户端
npx prisma generate

# 推送数据库结构
npx prisma db push

# 查看数据库
npx prisma studio
```

## 🔍 验证安装

### 检查MySQL服务状态
```bash
# Windows服务管理器中查看 MySQL80 服务状态
# 或使用命令行
net start MySQL80
```

### 测试连接
```bash
# 使用MySQL命令行客户端
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -p

# 或使用MySQL Workbench图形界面
```

## 🚨 常见问题

### 问题1：忘记root密码
```bash
# 停止MySQL服务
net stop MySQL80

# 以安全模式启动
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqld.exe" --skip-grant-tables

# 重置密码
mysql -u root
USE mysql;
UPDATE user SET authentication_string=PASSWORD('新密码') WHERE User='root';
FLUSH PRIVILEGES;
```

### 问题2：连接被拒绝
- 检查MySQL服务是否运行
- 确认端口3306未被占用
- 检查防火墙设置

### 问题3：字符编码问题
确保数据库使用UTF8MB4编码以支持中文和emoji。

## 📊 数据库结构预览

我们的数据库将包含以下主要表：
- `factories` - 工厂信息
- `factory_users` - 工厂用户
- `clients` - 客户信息
- `orders` - 订单数据
- `payments` - 付款记录
- `announcements` - 系统公告
- `audit_logs` - 审计日志

## 🔄 下一步
1. 完成数据库创建
2. 更新 `.env` 文件
3. 运行 `npx prisma db push`
4. 开始数据迁移

---
**注意**: 请妥善保管数据库密码，不要提交到版本控制系统中。
