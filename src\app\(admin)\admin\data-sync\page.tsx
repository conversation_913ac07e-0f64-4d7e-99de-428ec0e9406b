"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { dataSyncService, type SyncStatus } from "@/lib/services/data-sync"
import { db } from "@/lib/database"
// import { DataSyncStatus } from "@/components/data-sync/data-sync-status" // 已删除
import type { Factory, Client, Order } from "@/types"
import {
  RefreshCw,
  Database,
  Users,
  ShoppingCart,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Building2,
  Activity
} from "lucide-react"

export default function DataSyncPage() {
  const [loading, setLoading] = useState(false)
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(dataSyncService.getSyncStatus())
  const [factories, setFactories] = useState<Factory[]>([])
  const [allClientsData, setAllClientsData] = useState<{ [factoryId: string]: Client[] }>({})
  const [allOrdersData, setAllOrdersData] = useState<{ [factoryId: string]: Order[] }>({})
  const [statistics, setStatistics] = useState<any>(null)

  // 监听同步状态变化
  useEffect(() => {
    const handleSyncStatusChange = (status: SyncStatus) => {
      setSyncStatus(status)
    }

    dataSyncService.addSyncListener(handleSyncStatusChange)
    
    return () => {
      dataSyncService.removeSyncListener(handleSyncStatusChange)
    }
  }, [])

  // 加载初始数据
  useEffect(() => {
    loadAllData()
  }, [])

  const loadAllData = async () => {
    try {
      setLoading(true)
      
      // 加载工厂列表
      const factoriesData = await db.getFactories()
      setFactories(factoriesData)

      // 加载所有工厂的客户数据
      const clientsData = await dataSyncService.getAllFactoriesClients()
      setAllClientsData(clientsData)

      // 加载所有工厂的订单数据
      const ordersData = await dataSyncService.getAllFactoriesOrders()
      setAllOrdersData(ordersData)

      // 加载统计数据
      const stats = await dataSyncService.getAllFactoriesStatistics()
      setStatistics(stats)

      console.log('✅ 所有数据加载完成')
    } catch (error) {
      console.error('❌ 加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRefreshData = async () => {
    await loadAllData()
  }

  const getSyncStatusIcon = () => {
    if (syncStatus.syncInProgress) {
      return <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
    }
    if (syncStatus.errorCount > 0) {
      return <AlertCircle className="h-4 w-4 text-red-600" />
    }
    return <CheckCircle className="h-4 w-4 text-green-600" />
  }

  const getSyncStatusText = () => {
    if (syncStatus.syncInProgress) return "同步中..."
    if (syncStatus.errorCount >0) return "同步异常"
    return "同步正常"
  }

  const getSyncStatusColor = () => {
    if (syncStatus.syncInProgress) return "bg-blue-100 text-blue-800"
    if (syncStatus.errorCount >0) return "bg-red-100 text-red-800"
    return "bg-green-100 text-green-800"
  }

  return (
    <DashboardLayout role="admin">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">数据同步管理</h1>
            <p className="text-gray-600">总部查看和管理所有工厂的数据同步状态</p>
          </div>
          <div className="flex items-center space-x-4">
            <Badge className={getSyncStatusColor()}>
              {getSyncStatusIcon()}
              <span className="ml-2">{getSyncStatusText()}</span>
            </Badge>
            <Button onClick={handleRefreshData} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新数据
            </Button>
          </div>
        </div>

        {/* 同步状态概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">在线状态</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {syncStatus.isOnline ? "在线" : "离线"}
                  </p>
                </div>
                <Activity className={`h-8 w-8 ${syncStatus.isOnline ? 'text-green-600' : 'text-red-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">最后同步</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {syncStatus.lastSyncTime 
                      ? new Date(syncStatus.lastSyncTime).toLocaleTimeString()
                      : "未同步"
                    }
                  </p>
                </div>
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">错误次数</p>
                  <p className="text-2xl font-bold text-gray-900">{syncStatus.errorCount}</p>
                </div>
                <AlertCircle className={`h-8 w-8 ${syncStatus.errorCount > 0 ? 'text-red-600' : 'text-gray-400'}`} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">工厂总数</p>
                  <p className="text-2xl font-bold text-gray-900">{factories.length}</p>
                </div>
                <Building2 className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 数据详情 */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">数据概览</TabsTrigger>
            <TabsTrigger value="sync-status">同步状态</TabsTrigger>
            <TabsTrigger value="clients">客户数据</TabsTrigger>
            <TabsTrigger value="orders">订单数据</TabsTrigger>
            <TabsTrigger value="statistics">统计分析</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {statistics && (
                <>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">总客户数</p>
                          <p className="text-3xl font-bold text-blue-600">{(statistics as any)?.totalClients || 0}</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">总订单数</p>
                          <p className="text-3xl font-bold text-green-600">{(statistics as any)?.totalOrders || 0}</p>
                        </div>
                        <ShoppingCart className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">总收入</p>
                          <p className="text-3xl font-bold text-purple-600">
                            ¥{((statistics as any)?.totalRevenue / 10000 || 0).toFixed(1)}万
                          </p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-purple-600" />
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="sync-status">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 全局数据同步状态 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Database className="h-5 w-5 mr-2 text-blue-600" />
                    全局同步状态
                  </CardTitle>
                  <CardDescription>系统整体数据同步状态监控</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">同步状态</span>
                      <Badge className={getSyncStatusColor()}>
                        {getSyncStatusIcon()}
                        <span className="ml-2">{getSyncStatusText()}</span>
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">最后同步时间</span>
                      <span className="text-sm text-gray-600">
                        {syncStatus.lastSyncTime
                          ? syncStatus.lastSyncTime.toLocaleString('zh-CN')
                          : '从未同步'
                        }
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">错误次数</span>
                      <span className="text-sm text-gray-600">{syncStatus.errorCount}</span>
                    </div>

                    {syncStatus.lastError && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-800">最后错误: {syncStatus.lastError}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 工厂同步状态列表 */}
              <Card>
                <CardHeader>
                  <CardTitle>工厂同步状态</CardTitle>
                  <CardDescription>各工厂的数据同步状态详情</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {factories.map((factory) => (
                      <div key={factory.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{factory.name}</h4>
                          <Badge variant={factory.status === 'active' ? 'default' : 'secondary'}>
                            {factory.status === 'active' ? '活跃' : '非活跃'}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">客户数</p>
                            <p className="font-medium">{allClientsData[factory.id]?.length || 0}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">订单数</p>
                            <p className="font-medium">{allOrdersData[factory.id]?.length || 0}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">状态</p>
                            <p className="font-medium text-green-600">正常</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="clients">
            <div className="space-y-6">
              {factories.map(factory => (
                <Card key={factory.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{factory.name}</span>
                      <Badge variant="outline">
                        {allClientsData[factory.id]?.length || 0} 个客户
                      </Badge>
                    </CardTitle>
                    <CardDescription>{factory.address}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {allClientsData[factory.id]?.slice(0, 6).map(client => (
                        <div key={client.id} className="p-3 border rounded-lg">
                          <p className="font-medium">{client.name}</p>
                          <p className="text-sm text-gray-600">{client.phone}</p>
                          <p className="text-sm text-gray-600">
                            订单: {client.totalOrders || 0} | 金额: ¥{typeof client.totalAmount === 'object' && client.totalAmount?.toNumber ? client.totalAmount.toNumber().toLocaleString() : (client.totalAmount || 0).toLocaleString()}
                          </p>
                        </div>
                      )) || (
                        <p className="text-gray-500 col-span-3">暂无客户数据</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="orders">
            <div className="space-y-6">
              {factories.map(factory => (
                <Card key={factory.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{factory.name}</span>
                      <Badge variant="outline">
                        {allOrdersData[factory.id]?.length || 0} 个订单
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {allOrdersData[factory.id]?.slice(0, 5).map(order => (
                        <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <p className="font-medium">订单 #{order.id.slice(-8)}</p>
                            <p className="text-sm text-gray-600">
                              {new Date(order.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">¥{typeof order.totalAmount === 'object' && order.totalAmount?.toNumber ? order.totalAmount.toNumber().toLocaleString() : (order.totalAmount || 0).toLocaleString()}</p>
                            <Badge variant={order.status === 'completed' ? 'default' : 'secondary'}>
                              {order.status === 'completed' ? '已完成' : '进行中'}
                            </Badge>
                          </div>
                        </div>
                      )) || (
                        <p className="text-gray-500">暂无订单数据</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="statistics">
            <Card>
              <CardHeader>
                <CardTitle>工厂统计分析</CardTitle>
                <CardDescription>各工厂的详细统计数据</CardDescription>
              </CardHeader>
              <CardContent>
                {statistics?.factoryStats && (
                  <div className="space-y-4">
                    {Object.entries((statistics as any)?.factoryStats || {}).map(([factoryId, stats]: [string, any]) => (
                      <div key={factoryId} className="p-4 border rounded-lg">
                        <h3 className="font-medium mb-3">{stats.factory.name}</h3>
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">客户数</p>
                            <p className="font-medium">{stats.totalClients}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">订单数</p>
                            <p className="font-medium">{stats.totalOrders}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">月收入</p>
                            <p className="font-medium">¥{stats.monthlyRevenue}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">活跃客户</p>
                            <p className="font-medium">{stats.activeClients}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">待处理订单</p>
                            <p className="font-medium">{stats.pendingOrders}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
