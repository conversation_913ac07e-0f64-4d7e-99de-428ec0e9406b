// 工厂设置工具函数

export interface FactorySettings {
  // 基本信息
  factoryName: string
  factoryCode: string
  address: string
  contactPerson: string
  contactPhone: string
  email: string

  // 业务设置
  defaultUnitPrices: {
    // 常规风口 (元/㎡)
    double_white_outlet: number
    double_black_outlet: number
    white_black_bottom_outlet: number
    white_black_bottom_return: number
    white_return: number
    black_return: number
    white_linear: number
    black_linear: number
    white_linear_return: number
    black_linear_return: number
    maintenance: number
    // 高端风口 (元/m) - 统一命名
    high_end_white_outlet: number
    high_end_black_outlet: number
    high_end_white_return: number
    high_end_black_return: number
    arrow_outlet: number
    arrow_return: number
    claw_outlet: number
    claw_return: number
    black_white_dual_outlet: number
    black_white_dual_return: number
    wood_grain_outlet: number
    wood_grain_return: number
    white_putty_outlet: number
    white_putty_return: number
    black_putty_outlet: number
    black_putty_return: number
    white_gypsum_outlet: number
    white_gypsum_return: number
    black_gypsum_outlet: number
    black_gypsum_return: number
  }

  // 客户奖励设置
  rewardSettings: {
    // 普通风口奖励（按比例）
    regularVentRewards: {
      baseRate: number        // 基础奖励比例 (默认2%)
      tier1Amount: number     // 第一档金额门槛 (默认10万)
      tier1Rate: number       // 第一档奖励比例 (默认3%)
      tier2Amount: number     // 第二档金额门槛 (默认20万)
      tier2Rate: number       // 第二档奖励比例 (默认4%)
      maxRate: number         // 最高奖励比例 (默认5%)
    }

    // 高端风口奖励（固定金额）
    premiumVentRewards: {
      baseAmount: number      // 基础奖励金额 (默认30元)
      tier1Amount: number     // 第一档订单门槛 (默认1万)
      tier1Reward: number     // 第一档奖励金额 (默认50元)
      tier2Amount: number     // 第二档订单门槛 (默认2万)
      tier2Reward: number     // 第二档奖励金额 (默认100元)
      tier3Amount: number     // 第三档订单门槛 (默认5万)
      tier3Reward: number     // 第三档奖励金额 (默认500元)
    }

    // 奖励使用规则
    rewardRules: {
      requireFullPayment: boolean  // 是否要求结清货款才能使用奖励
      allowCashOut: boolean        // 是否允许兑现提取
      allowOrderDeduction: boolean // 是否允许抵扣订单
    }
  }

  // 系统设置
  orderNumberPrefix: string
  autoBackup: boolean
  backupFrequency: string

  // 前缀生成配置
  prefixMinLength?: number          // 前缀最小长度 (默认3)
  prefixMaxLength?: number          // 前缀最大长度 (默认8)
  prefixIncludeNumbers?: boolean    // 是否允许数字后缀 (默认false)
  prefixPreferLonger?: boolean      // 是否偏好较长前缀 (默认true)

  // 通知设置
  emailNotifications: boolean
  smsNotifications: boolean
  orderStatusNotifications: boolean

  // 界面设置
  theme: string
  language: string
  dateFormat: string
  currencyFormat: string

  // 元数据
  lastUpdated?: string
  updatedBy?: string
}

// 默认工厂设置
export const DEFAULT_FACTORY_SETTINGS: FactorySettings = {
  factoryName: "",
  factoryCode: "",
  address: "",
  contactPerson: "",
  contactPhone: "",
  email: "",
  defaultUnitPrices: {
    // 常规风口 (元/㎡)
    double_white_outlet: 150,
    double_black_outlet: 150,
    white_black_bottom_outlet: 150,
    white_black_bottom_return: 150,
    white_return: 150,
    black_return: 150,
    white_linear: 150,
    black_linear: 150,
    white_linear_return: 150,
    black_linear_return: 150,
    maintenance: 150,
    // 高端风口 (元/m) - 统一命名
    high_end_white_outlet: 80,
    high_end_black_outlet: 80,
    high_end_white_return: 80,
    high_end_black_return: 80,
    arrow_outlet: 90,                    // 🔧 箭型出风口
    arrow_return: 90,                    // 🔧 箭型回风口
    claw_outlet: 100,                    // 🔧 爪型出风口
    claw_return: 100,                    // 🔧 爪型回风口
    black_white_dual_outlet: 85,         // 🔧 黑白双色出风口
    black_white_dual_return: 85,         // 🔧 黑白双色回风口
    wood_grain_outlet: 120,
    wood_grain_return: 120,
    white_putty_outlet: 110,
    white_putty_return: 110,
    black_putty_outlet: 110,
    black_putty_return: 110,
    white_gypsum_outlet: 130,
    white_gypsum_return: 130,
    black_gypsum_outlet: 130,
    black_gypsum_return: 130
  },
  rewardSettings: {
    regularVentRewards: {
      baseRate: 0.02,        // 2%
      tier1Amount: 100000,   // 10万
      tier1Rate: 0.03,       // 3%
      tier2Amount: 200000,   // 20万
      tier2Rate: 0.04,       // 4%
      maxRate: 0.05          // 5%
    },
    premiumVentRewards: {
      baseAmount: 30,        // 30元
      tier1Amount: 10000,    // 1万
      tier1Reward: 50,       // 50元
      tier2Amount: 20000,    // 2万
      tier2Reward: 100,      // 100元
      tier3Amount: 50000,    // 5万
      tier3Reward: 500       // 500元
    },
    rewardRules: {
      requireFullPayment: true,     // 要求结清货款
      allowCashOut: true,           // 允许兑现
      allowOrderDeduction: true     // 允许抵扣
    }},
  orderNumberPrefix: "ORD",
  autoBackup: true,
  backupFrequency: "daily",
  prefixMinLength: 3,
  prefixMaxLength: 8,
  prefixIncludeNumbers: false,
  prefixPreferLonger: true,
  emailNotifications: true,
  smsNotifications: false,
  orderStatusNotifications: true,
  theme: "light",
  language: "zh-CN",
  dateFormat: "YYYY-MM-DD",
  currencyFormat: "CNY"
}

/**
 * 获取当前工厂的设置（异步版本，从数据库读取）
 * @param factoryId 工厂ID
 * @returns 工厂设置对象
 */
export const getFactorySettings = async (factoryId: string): Promise<FactorySettings> => {
  try {
    // 尝试从数据库获取设置
    const dbSettings = await getFactorySettingsFromDB(factoryId)
    if (dbSettings) {
      return dbSettings
    }

    // 如果数据库中没有设置，返回默认设置
    return getDefaultSettingsForFactory(factoryId)
  } catch (error) {
    console.error('❌ 获取工厂设置失败:', error)
    return getDefaultSettingsForFactory(factoryId)
  }
}

/**
 * 同步版本的获取工厂设置（用于兼容性）
 * @param factoryId 工厂ID
 * @returns 工厂设置对象
 */
export const getFactorySettingsSync = (factoryId: string): FactorySettings => {
  try {
    // 直接返回默认设置，保持简单
    return getDefaultSettingsForFactory(factoryId)
  } catch (error) {
    console.error('❌ 获取工厂设置失败:', error)
    return getDefaultSettingsForFactory(factoryId)
  }
}

/**
 * 获取工厂的默认设置
 * @param factoryId 工厂ID
 * @returns 默认工厂设置
 */
export const getDefaultSettingsForFactory = (factoryId: string): FactorySettings => {
  const baseSettings = { ...DEFAULT_FACTORY_SETTINGS }

  // 根据工厂ID设置特定的默认值
  switch (factoryId) {
    case 'lin001':
      return {
        ...baseSettings,
        factoryName: "南宁加工厂",
        factoryCode: "LIN001",
        address: "广西南宁市",
        contactPerson: "林经理",
        contactPhone: "13800138000",
        email: "<EMAIL>",
        orderNumberPrefix: "NNJGC"  // 南宁加工厂拼音首字母
      }
    case 'lzfxc':
    case 'LZFXC':
      return {
        ...baseSettings,
        factoryName: "柳州风向厂",
        factoryCode: "LZFXC",
        address: "广西柳州市",
        contactPerson: "厂长",
        contactPhone: "13800138000",
        email: "<EMAIL>",
        orderNumberPrefix: "LZFXC"  // 柳州风向厂英文编号
      }
    default:
      // 为其他工厂生成唯一的默认设置
      const uniquePrefix = generateFactoryUniquePrefix(factoryId)
      return {
        ...baseSettings,
        factoryName: `工厂-${factoryId.toUpperCase()}`,
        factoryCode: factoryId.toUpperCase(),
        address: "待完善地址",
        contactPerson: "待完善联系人",
        contactPhone: "13800138000",
        email: `${factoryId.toLowerCase()}@factory.com`,
        orderNumberPrefix: uniquePrefix  // 基于工厂ID生成的唯一前缀
      }
  }
}

/**
 * 为工厂生成唯一的订单号前缀
 * @param factoryId 工厂ID
 * @returns 唯一的订单号前缀
 */
const generateFactoryUniquePrefix = (factoryId: string): string => {
  // 将factoryId转换为大写字母前缀
  const cleanId = factoryId.toUpperCase().replace(/[^A-Z0-9]/g, '')

  if (cleanId.length >= 3) {
    // 如果ID本身就是3位以上字母，直接使用（最多取前6位）
    return cleanId.substring(0, 6)
  } else if (cleanId.length >= 2) {
    // 如果是2位，添加'JGC'后缀
    return cleanId + 'JGC'
  } else {
    // 如果太短，使用哈希方式生成
    const hash = factoryId.split('').reduce((acc, char) => {
      return acc + char.charCodeAt(0)
    }, 0)
    const hashStr = hash.toString(36).toUpperCase().replace(/[^A-Z]/g, '')
    return (hashStr + 'JGC').substring(0, 6)
  }
}

/**
 * 保存工厂设置到数据库
 * @param factoryId 工厂ID
 * @param settings 工厂设置
 * @param username 操作用户名
 */
export const saveFactorySettings = async (factoryId: string, settings: FactorySettings, username?: string): Promise<void> => {
  try {
    const settingsToSave = {
      ...settings,
      lastUpdated: new Date().toISOString(),
      updatedBy: username || 'unknown'
    }

    // 保存到数据库
    await saveFactorySettingsToDB(factoryId, settingsToSave)
    console.log('✅ 工厂设置已保存到数据库:', settingsToSave)
  } catch (error) {
    console.error('❌ 保存工厂设置失败:', error)
    throw error
  }
}

/**
 * 从数据库获取工厂设置
 * @param factoryId 工厂ID
 * @returns 工厂设置或null
 */
export const getFactorySettingsFromDB = async (factoryId: string): Promise<FactorySettings | null> => {
  try {
    if (typeof window === 'undefined') {
      // 服务端环境
      const { PrismaClient } = await import('@prisma/client')
      const prisma = new PrismaClient()

      const factory = await prisma.factory.findUnique({
        where: { id: factoryId }
      })

      await prisma.$disconnect()

      if (factory && factory.settings) {
        // 解析JSON字符串为对象
        let settings: any
        try {
          settings = typeof factory.settings === 'string'
            ? JSON.parse(factory.settings)
            : factory.settings
        } catch (error) {
          console.error('❌ 解析工厂设置JSON失败:', error)
          settings = {}
        }

        // 🔧 数据迁移：处理旧的defaultPrices字段
        if (settings.defaultPrices && !settings.defaultUnitPrices) {
          console.log('🔄 检测到旧的defaultPrices字段，正在迁移到defaultUnitPrices...')
          settings.defaultUnitPrices = migrateOldPrices(settings.defaultPrices)
          delete settings.defaultPrices

          // 保存迁移后的设置
          await saveFactorySettingsToDB(factoryId, settings)
          console.log('✅ 价格数据迁移完成')
        }

        const defaultSettings = getDefaultSettingsForFactory(factoryId)

        // 智能合并默认设置和数据库设置
        const mergedSettings = {
          ...defaultSettings,
          ...settings,
          // 工厂名称：优先使用设置中的名称，如果是合理的中文名称；否则使用默认名称
          factoryName: (settings.factoryName &&
                       settings.factoryName !== "演示工厂" &&
                       /[\u4e00-\u9fa5]/.test(settings.factoryName))
                       ? settings.factoryName
                       : (factory.name && /[\u4e00-\u9fa5]/.test(factory.name))
                       ? factory.name
                       : defaultSettings.factoryName,
          factoryCode: settings.factoryCode || factory.code || defaultSettings.factoryCode,
          address: settings.address || factory.address || defaultSettings.address,
          contactPhone: settings.contactPhone || factory.phone || defaultSettings.contactPhone,
          email: settings.email || factory.email || defaultSettings.email
        }

        console.log(`🏭 工厂设置合并结果 (${factoryId}):`, {
          数据库工厂名: factory.name,
          设置中工厂名: settings.factoryName,
          默认工厂名: defaultSettings.factoryName,
          最终工厂名: mergedSettings.factoryName
        })

        return mergedSettings
      }
    } else {
      // 客户端环境，通过API调用
      const response = await fetch(`/api/factory/${factoryId}/settings`)
      if (response.ok) {
        const data = await response.json()
        return data.settings
      }
    }

    return null
  } catch (error) {
    console.error('❌ 从数据库获取工厂设置失败:', error)
    return null
  }
}

/**
 * 保存工厂设置到数据库
 * @param factoryId 工厂ID
 * @param settings 工厂设置
 * @returns 是否保存成功
 */
export const saveFactorySettingsToDB = async (factoryId: string, settings: FactorySettings): Promise<boolean> => {
  try {
    if (typeof window === 'undefined') {
      // 服务端环境
      const { PrismaClient } = await import('@prisma/client')
      const prisma = new PrismaClient()

      await prisma.factory.upsert({
        where: { id: factoryId },
        update: {
          name: settings.factoryName,
          code: settings.factoryCode,
          address: settings.address,
          phone: settings.contactPhone,
          email: settings.email,
          settings: JSON.stringify(settings)
        },
        create: {
          id: factoryId,
          name: settings.factoryName,
          code: settings.factoryCode,
          address: settings.address,
          phone: settings.contactPhone,
          email: settings.email,
          settings: JSON.stringify(settings)
        }
      })

      await prisma.$disconnect()
      console.log('✅ 工厂设置已保存到数据库')
      return true
    } else {
      // 客户端环境，通过API调用
      const response = await fetch(`/api/factory/${factoryId}/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ settings })
      })

      if (response.ok) {
        console.log('✅ 工厂设置已保存到数据库')
        return true
      } else {
        console.error('❌ 保存工厂设置失败:', await response.text())
        return false
      }
    }
  } catch (error) {
    console.error('❌ 保存工厂设置到数据库失败:', error)
    return false
  }
}

/**
 * 获取当前工厂的默认单价（同步版本，用于兼容性）
 * @param factoryId 工厂ID
 * @returns 默认单价配置
 */
export const getDefaultUnitPrices = (factoryId: string) => {
  const settings = getFactorySettingsSync(factoryId)
  return settings.defaultUnitPrices
}

/**
 * 获取当前工厂的默认单价（异步版本，从数据库读取）
 * @param factoryId 工厂ID
 * @returns 默认单价配置
 */
export const getDefaultUnitPricesAsync = async (factoryId: string) => {
  const settings = await getFactorySettings(factoryId)
  return settings.defaultUnitPrices
}

/**
 * 🔧 数据迁移：将旧的defaultPrices字段迁移到新的defaultUnitPrices字段
 * @param oldPrices 旧的价格配置
 * @returns 新的价格配置
 */
const migrateOldPrices = (oldPrices: any): FactorySettings['defaultUnitPrices'] => {
  console.log('🔄 开始迁移旧价格数据:', oldPrices)

  // 创建完整的默认单价配置
  const newPrices = { ...DEFAULT_FACTORY_SETTINGS.defaultUnitPrices }

  // 映射旧字段名到新字段名
  const fieldMapping: Record<string, string> = {
    // 旧字段名 -> 新字段名
    'black_white_outlet': 'black_white_dual_outlet',
    'black_white_return': 'black_white_dual_return',
    'wood_outlet': 'wood_grain_outlet',
    'wood_return': 'wood_grain_return'
  }

  // 迁移已有的价格数据
  Object.keys(oldPrices).forEach(oldKey => {
    const newKey = fieldMapping[oldKey] || oldKey
    if (newPrices.hasOwnProperty(newKey)) {
      newPrices[newKey as keyof typeof newPrices] = oldPrices[oldKey] || newPrices[newKey as keyof typeof newPrices]
    }
  })

  console.log('✅ 价格数据迁移完成:', newPrices)
  return newPrices
}

/**
 * 获取订单号前缀（同步版本）
 * @param factoryId 工厂ID
 * @returns 订单号前缀
 */
export const getOrderNumberPrefix = (factoryId: string): string => {
  const settings = getFactorySettingsSync(factoryId)
  return settings.orderNumberPrefix || "ORD"
}

/**
 * 获取订单号前缀（异步版本，从数据库读取）
 * @param factoryId 工厂ID
 * @returns 订单号前缀
 */
export const getOrderNumberPrefixAsync = async (factoryId: string): Promise<string> => {
  const settings = await getFactorySettings(factoryId)
  return settings.orderNumberPrefix || "ORD"
}

/**
 * 检查工厂设置是否完整
 * @param settings 工厂设置
 * @returns 是否完整
 */
export const isFactorySettingsComplete = (settings: FactorySettings): boolean => {
  return !!(
    settings.factoryName &&
    settings.factoryCode &&
    settings.address &&
    settings.contactPerson &&
    settings.contactPhone
  )
}

/**
 * 格式化工厂信息显示（同步版本）
 * @param factoryId 工厂ID
 * @returns 格式化的工厂信息
 */
export const getFormattedFactoryInfo = (factoryId: string): string => {
  const settings = getFactorySettingsSync(factoryId)
  return `${settings.factoryName} (${settings.factoryCode})`
}

/**
 * 格式化工厂信息显示（异步版本，从数据库读取）
 * @param factoryId 工厂ID
 * @returns 格式化的工厂信息
 */
export const getFormattedFactoryInfoAsync = async (factoryId: string): Promise<string> => {
  const settings = await getFactorySettings(factoryId)
  return `${settings.factoryName} (${settings.factoryCode})`
}

/**
 * 检查订单号前缀是否与其他工厂冲突
 * @param prefix 要检查的前缀
 * @param currentFactoryId 当前工厂ID（排除自己）
 * @returns 是否存在冲突
 */
export const checkPrefixConflict = (prefix: string, currentFactoryId: string): boolean => {
  // 已知的工厂前缀映射
  const knownPrefixes: Record<string, string> = {
    'lin001': 'NNJGC',
    'lzfxc': 'LZFXC',
    'LZFXC': 'LZFXC'
  }

  // 检查是否与已知前缀冲突
  for (const [factoryId, factoryPrefix] of Object.entries(knownPrefixes)) {
    if (factoryId !== currentFactoryId && factoryPrefix === prefix) {
      return true // 存在冲突
    }
  }

  return false // 无冲突
}

/**
 * 完整的中文转拼音首字母映射表
 */
const PINYIN_MAP: Record<string, string> = {
  // 地名
  '北京': 'BJ', '上海': 'SH', '天津': 'TJ', '重庆': 'CQ',
  '广州': 'GZ', '深圳': 'SZ', '南京': 'NJ', '杭州': 'HZ',
  '武汉': 'WH', '成都': 'CD', '西安': 'XA', '沈阳': 'SY',
  '大连': 'DL', '青岛': 'QD', '厦门': 'XM', '宁波': 'NB',
  '苏州': 'SZ', '无锡': 'WX', '佛山': 'FS', '东莞': 'DG',
  '南宁': 'NN', '柳州': 'LZ', '桂林': 'GL', '梧州': 'WZ',
  '玉林': 'YL', '百色': 'BS', '贵港': 'GG', '钦州': 'QZ',
  '防城港': 'FCG', '崇左': 'CZ', '来宾': 'LB', '河池': 'HC',
  '贺州': 'HZ', '广西': 'GX', '广东': 'GD', '湖南': 'HN',
  '湖北': 'HB', '河南': 'HN', '河北': 'HB', '山东': 'SD',
  '山西': 'SX', '陕西': 'SX', '四川': 'SC', '云南': 'YN',
  '贵州': 'GZ', '福建': 'FJ', '江西': 'JX', '安徽': 'AH',
  '浙江': 'ZJ', '江苏': 'JS', '辽宁': 'LN', '吉林': 'JL',
  '黑龙江': 'HLJ', '内蒙古': 'NMG', '新疆': 'XJ', '西藏': 'XZ',
  '宁夏': 'NX', '青海': 'QH', '甘肃': 'GS', '海南': 'HN',

  // 工厂相关词汇
  '工厂': 'GC', '加工厂': 'JGC', '制造厂': 'ZZC', '生产厂': 'SCC',
  '机械厂': 'JXC', '电子厂': 'DZC', '化工厂': 'HGC', '纺织厂': 'FZC',
  '食品厂': 'SPC', '服装厂': 'FZC', '家具厂': 'JJC', '建材厂': 'JCC',
  '钢铁厂': 'GTC', '水泥厂': 'SNC', '玻璃厂': 'BLC', '陶瓷厂': 'TCC',
  '塑料厂': 'SLC', '橡胶厂': 'XJC', '印刷厂': 'YSC', '包装厂': 'BZC',

  // 行业词汇
  '制造': 'ZZ', '生产': 'SC', '加工': 'JG', '装配': 'ZP',
  '组装': 'ZZ', '焊接': 'HJ', '冲压': 'CY', '铸造': 'ZZ',
  '锻造': 'DZ', '机加': 'JJ', '热处理': 'RCL', '表面处理': 'BMCL',
  '喷涂': 'PT', '电镀': 'DD', '抛光': 'PG', '研磨': 'YM',

  // 产品相关
  '风口': 'FK', '风向': 'FX', '通风': 'TF', '空调': 'KT',
  '暖通': 'NT', '制冷': 'ZL', '换热': 'HR', '过滤': 'GL',
  '净化': 'JH', '除尘': 'CC', '排风': 'PF', '送风': 'SF',
  '回风': 'HF', '新风': 'XF', '排气': 'PQ', '进气': 'JQ',

  // 材料相关
  '不锈钢': 'BXG', '铝合金': 'LHJ', '碳钢': 'TG', '镀锌': 'DX',
  '塑料': 'SL', '橡胶': 'XJ', '玻璃': 'BL', '陶瓷': 'TC',
  '复合': 'FH', '纤维': 'XW', '金属': 'JS', '非金属': 'FJS',

  // 规模词汇
  '大型': 'DX', '中型': 'ZX', '小型': 'XX', '微型': 'WX',
  '特大': 'TD', '超大': 'CD', '巨型': 'JX', '精密': 'JM',
  '高端': 'GD', '中端': 'ZD', '低端': 'DD', '专业': 'ZY',

  // 常用字符
  '有限': 'YX', '公司': 'GS', '企业': 'QY', '集团': 'JT',
  '股份': 'GF', '责任': 'ZR', '合作': 'HZ', '联合': 'LH',
  '国际': 'GJ', '实业': 'SY', '控股': 'KG', '投资': 'TZ'
}

/**
 * 智能提取中文名称的拼音首字母
 * @param chineseName 中文名称
 * @returns 拼音首字母组合
 */
export const extractPinyinInitials = (chineseName: string): string => {
  if (!chineseName || typeof chineseName !== 'string') {
    return ''
  }

  let result = ''
  let remainingName = chineseName

  // 按长度从长到短匹配，优先匹配长词组
  const sortedKeys = Object.keys(PINYIN_MAP).sort((a, b) => b.length - a.length)

  for (const key of sortedKeys) {
    if (remainingName.includes(key)) {
      result += PINYIN_MAP[key]
      remainingName = remainingName.replace(key, '')
    }
  }

  // 如果还有未匹配的字符，尝试单字匹配
  if (remainingName.length > 0) {
    for (const char of remainingName) {
      if (PINYIN_MAP[char]) {
        result += PINYIN_MAP[char]
      }
    }
  }

  // 如果仍然没有结果，使用简单的字符映射
  if (result.length === 0) {
    result = chineseName.substring(0, Math.min(4, chineseName.length))
      .split('')
      .map(char => char.charCodeAt(0).toString(36).toUpperCase())
      .join('')
      .replace(/[^A-Z]/g, '')
  }

  return result.substring(0, 6) // 限制最大长度为6位
}

/**
 * 为工厂建议唯一的订单号前缀
 * @param factoryId 工厂ID
 * @param factoryName 工厂名称（可选）
 * @param options 生成选项
 * @returns 建议的前缀列表
 */
export const suggestOrderPrefixes = (
  factoryId: string,
  factoryName?: string,
  options: {
    minLength?: number
    maxLength?: number
    includeNumbers?: boolean
    preferLonger?: boolean
  } = {}
): string[] => {
  const {
    minLength = 3,
    maxLength = 8,
    includeNumbers = false,
    preferLonger = true
  } = options

  const suggestions: string[] = []

  // 1. 基于工厂名称的智能拼音首字母提取（优先级最高）
  if (factoryName) {
    const pinyinPrefix = extractPinyinInitials(factoryName)
    if (pinyinPrefix.length >= minLength) {
      // 原始提取结果
      suggestions.push(pinyinPrefix.substring(0, maxLength))

      // 如果太短，尝试加长
      if (pinyinPrefix.length < minLength && preferLonger) {
        suggestions.push((pinyinPrefix + 'GC').substring(0, maxLength)) // 加上"工厂"
        suggestions.push((pinyinPrefix + 'JGC').substring(0, maxLength)) // 加上"加工厂"
        suggestions.push((pinyinPrefix + 'ZZ').substring(0, maxLength)) // 加上"制造"
        suggestions.push((pinyinPrefix + 'SC').substring(0, maxLength)) // 加上"生产"
      }
    }
  }

  // 2. 基于工厂ID的建议
  const cleanId = factoryId.toUpperCase().replace(/[^A-Z0-9]/g, '')
  if (cleanId.length >= minLength) {
    suggestions.push(cleanId.substring(0, maxLength))
  }

  // 3. 组合建议：工厂名称首字母 + 通用后缀
  if (factoryName) {
    const nameInitials = extractPinyinInitials(factoryName)
    if (nameInitials.length >= 2) {
      const combinations = [
        nameInitials + 'GC',    // 工厂
        nameInitials + 'JGC',   // 加工厂
        nameInitials + 'ZZC',   // 制造厂
        nameInitials + 'SCC',   // 生产厂
        nameInitials + 'GONGC', // 工厂（全拼）
        nameInitials + 'FAC',   // Factory
        nameInitials + 'MFG',   // Manufacturing
        nameInitials + 'PROD'   // Production
      ]

      combinations.forEach(combo => {
        if (combo.length >= minLength && combo.length <= maxLength) {
          suggestions.push(combo)
        } else if (combo.length > maxLength) {
          suggestions.push(combo.substring(0, maxLength))
        }
      })
    }
  }

  // 4. 基于工厂ID + 通用后缀的建议
  if (cleanId.length >= 2) {
    const combinations = [
      cleanId + 'GC',
      cleanId + 'JGC',
      cleanId + 'FAC',
      cleanId + 'MFG'
    ]

    combinations.forEach(combo => {
      if (combo.length >= minLength && combo.length <= maxLength) {
        suggestions.push(combo)
      } else if (combo.length > maxLength) {
        suggestions.push(combo.substring(0, maxLength))
      }
    })
  }

  // 5. 如果允许数字，添加数字后缀
  if (includeNumbers && factoryName) {
    const nameInitials = extractPinyinInitials(factoryName)
    if (nameInitials.length >= 2) {
      for (let i = 1; i <= 99; i++) {
        const withNumber = nameInitials + i.toString().padStart(2, '0')
        if (withNumber.length >= minLength && withNumber.length <= maxLength) {
          suggestions.push(withNumber)
        }
      }
    }
  }

  // 6. 创意组合（基于地区+行业）
  if (factoryName) {
    const regions = ['NN', 'LZ', 'GX', 'BJ', 'SH', 'SZ', 'GZ', 'CD', 'WH', 'XA']
    const industries = ['FK', 'TF', 'KT', 'NT', 'ZL', 'JH', 'CC']

    regions.forEach(region => {
      if (factoryName.includes(getRegionName(region))) {
        industries.forEach(industry => {
          const creative = region + industry
          if (creative.length >= minLength && creative.length <= maxLength) {
            suggestions.push(creative)
          }
        })
      }
    })
  }

  // 去重并过滤掉冲突的前缀
  const uniqueSuggestions = [...new Set(suggestions)]
  const validSuggestions = uniqueSuggestions.filter(prefix =>
    prefix.length >= minLength &&
    prefix.length <= maxLength &&
    !checkPrefixConflict(prefix, factoryId)
  )

  // 按长度排序（如果偏好更长的前缀）
  if (preferLonger) {
    return validSuggestions.sort((a, b) => b.length - a.length)
  } else {
    return validSuggestions.sort((a, b) => a.length - b.length)
  }
}

/**
 * 根据地区代码获取地区名称
 */
const getRegionName = (code: string): string => {
  const regionMap: Record<string, string> = {
    'NN': '南宁', 'LZ': '柳州', 'GX': '广西', 'BJ': '北京',
    'SH': '上海', 'SZ': '深圳', 'GZ': '广州', 'CD': '成都',
    'WH': '武汉', 'XA': '西安'
  }
  return regionMap[code] || ''
}





/**
 * 智能生成并保存工厂订单号前缀
 * @param factoryId 工厂ID
 * @param factoryName 工厂名称
 * @returns 生成的前缀
 */
export const generateAndSaveOrderPrefix = async (factoryId: string, factoryName: string): Promise<string> => {
  try {
    // 1. 智能提取拼音首字母
    const pinyinPrefix = extractPinyinInitials(factoryName)

    // 2. 检查是否冲突
    let finalPrefix = pinyinPrefix
    if (checkPrefixConflict(pinyinPrefix, factoryId)) {
      // 如果冲突，尝试其他建议
      const suggestions = suggestOrderPrefixes(factoryId, factoryName)
      finalPrefix = suggestions.length > 0 ? suggestions[0] : generateFallbackPrefix(factoryId)
    }

    // 3. 获取当前设置并更新前缀
    const currentSettings = await getFactorySettings(factoryId)
    const updatedSettings = {
      ...currentSettings,
      orderNumberPrefix: finalPrefix,
      factoryName: factoryName,
      lastUpdated: new Date().toISOString(),
      updatedBy: 'system-auto-generate'
    }

    // 4. 记录设置更新
    console.log('✅ 工厂设置已更新:', updatedSettings)

    console.log(`🎯 为工厂 ${factoryName}(${factoryId}) 自动生成前缀: ${finalPrefix}`)
    return finalPrefix
  } catch (error) {
    console.error('❌ 生成并保存订单号前缀失败:', error)
    return generateFallbackPrefix(factoryId)
  }
}

/**
 * 生成备用前缀
 * @param factoryId 工厂ID
 * @returns 备用前缀
 */
const generateFallbackPrefix = (factoryId: string): string => {
  const cleanId = factoryId.toUpperCase().replace(/[^A-Z0-9]/g, '')
  if (cleanId.length >= 3) {
    return cleanId.substring(0, 6)
  }

  // 使用哈希生成
  const hash = factoryId.split('').reduce((acc, char) => {
    return acc + char.charCodeAt(0)
  }, 0)
  const hashStr = hash.toString(36).toUpperCase().replace(/[^A-Z]/g, '')
  return (hashStr + 'AUTO').substring(0, 6)
}
