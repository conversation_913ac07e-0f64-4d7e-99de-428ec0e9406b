"use client"

import { useState } from "react"
import { But<PERSON>  } from "@/components/ui/button"
import { FactoryUser } from "@/types"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Shield,
  Clock
} from "lucide-react"

interface EmployeeActionsProps {
  employee: FactoryUser
  onEdit: (employee: FactoryUser) => void
  onDelete: (employee: FactoryUser) => void
  onToggleStatus: (employee: FactoryUser) => void
  onViewDetails: (employee: FactoryUser) => void
}

export function EmployeeActions({ 
  employee, 
  onEdit, 
  onDelete, 
  onToggleStatus, 
  onViewDetails 
}: EmployeeActionsProps) {
  const [showDropdown, setShowDropdown] = useState(false)

  const handleAction = (action: () => void) => {
    action()
    setShowDropdown(false)
  }

  return (
    <div className="relative">
      {/* 快捷操作按钮 */}
      <div className="flex items-center space-x-2">
        <Button 
          size="sm" 
          variant="outline"
          onClick={() => onViewDetails(employee)}
        >
          <Eye className="h-4 w-4 mr-1" />
          查看
        </Button>
        
        <Button 
          size="sm" 
          variant="outline"
          onClick={() => onEdit(employee)}
        >
          <Edit className="h-4 w-4 mr-1" />
          编辑
        </Button>

        {/* 更多操作下拉菜单 */}
        <div className="relative">
          <Button 
            size="sm" 
            variant="ghost"
            onClick={() => setShowDropdown(!showDropdown)}
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>

          {showDropdown && (
            <>
              {/* 遮罩层 */}
              <div 
                className="fixed inset-0 z-10" 
                onClick={() => setShowDropdown(false)}
              />
              
              {/* 下拉菜单 */}
              <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-20">
                <div className="py-1">
                  {/* 切换状态 */}
                  <button
                    className="w-full px-4 py-2 text-left text-sm text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center space-x-2"
                    onClick={() => handleAction(() => onToggleStatus(employee))}
                  >
                    {employee.isActive ? (
                      <>
                        <UserX className="h-4 w-4 text-orange-500" />
                        <span>停用员工</span>
                      </>
                    ) : (
                      <>
                        <UserCheck className="h-4 w-4 text-green-500" />
                        <span>激活员工</span>
                      </>
                    )}
                  </button>

                  {/* 查看权限 */}
                  <button
                    className="w-full px-4 py-2 text-left text-sm text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center space-x-2"
                    onClick={() => handleAction(() => onViewDetails(employee))}
                  >
                    <Shield className="h-4 w-4 text-blue-500" />
                    <span>查看权限</span>
                  </button>

                  {/* 查看登录记录 */}
                  <button
                    className="w-full px-4 py-2 text-left text-sm text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center space-x-2"
                    onClick={() => handleAction(() => onViewDetails(employee))}
                  >
                    <Clock className="h-4 w-4 text-purple-500" />
                    <span>登录记录</span>
                  </button>

                  {/* 分割线 */}
                  <div className="border-t border-gray-200 dark:border-gray-600 my-1" />

                  {/* 删除员工 */}
                  <button
                    className="w-full px-4 py-2 text-left text-sm hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400 flex items-center space-x-2"
                    onClick={() => handleAction(() => onDelete(employee))}
                  >
                    <Trash2 className="h-4 w-4" />
                    <span>删除员工</span>
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

// 员工详情查看对话框
interface EmployeeDetailsDialogProps {
  isOpen: boolean
  onClose: () => void
  employee: FactoryUser | null
}

export function EmployeeDetailsDialog({ isOpen, onClose, employee }: EmployeeDetailsDialogProps) {
  if (!isOpen || !employee) return null

  const getRoleName = (role: string) => {
    switch (role) {
      case 'owner': return '工厂主'
      case 'manager': return '管理员'
      case 'employee': return '员工'
      default: return role
    }
  }

  const getPermissionName = (permission: string) => {
    const permissionMap: Record<string, string> = {
      'orders:create': '创建订单',
      'orders:read': '查看订单',
      'orders:update': '修改订单',
      'orders:delete': '删除订单',
      'clients:create': '添加客户',
      'clients:read': '查看客户',
      'clients:update': '修改客户',
      'clients:delete': '删除客户',
      'reports:read': '查看报表',
      'analytics:read': '数据分析',
      'employees:manage': '员工管理',
      'settings:manage': '系统设置'
    }
    return permissionMap[permission] || permission
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">员工详情</h2>
          <Button variant="ghost" onClick={onClose}>✕</Button>
        </div>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">基本信息</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">员工姓名</p>
                <p className="font-medium">{employee.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">用户名</p>
                <p className="font-medium">{employee.username}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">角色</p>
                <p className="font-medium">{getRoleName(employee.role)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">状态</p>
                <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                  employee.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {employee.isActive ? '正常' : '停用'}
                </span>
              </div>
              {employee.email && (
                <div>
                  <p className="text-sm text-gray-600">邮箱</p>
                  <p className="font-medium">{employee.email}</p>
                </div>
              )}
              {employee.phone && (
                <div>
                  <p className="text-sm text-gray-600">手机号</p>
                  <p className="font-medium">{employee.phone}</p>
                </div>
              )}
            </div>
          </div>

          {/* 权限信息 */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">权限列表</h3>
            <div className="grid grid-cols-2 gap-2">
              {employee.permissions.map((permission: string, index: number) => (
                <div key={index} className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">{getPermissionName(permission)}</span>
                </div>
              ))}
            </div>
          </div>

          {/* 统计信息 */}
          <div className="bg-green-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">工作统计</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">总订单数</p>
                <p className="text-2xl font-bold text-green-600">{employee.totalOrders || 0}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">本月订单</p>
                <p className="text-2xl font-bold text-blue-600">{employee.thisMonthOrders || 0}</p>
              </div>
            </div>
          </div>

          {/* 时间信息 */}
          <div className="bg-purple-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">时间记录</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">创建时间</p>
                <p className="font-medium">{employee.createdAt.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">最后更新</p>
                <p className="font-medium">{employee.updatedAt.toLocaleString()}</p>
              </div>
              {employee.lastLoginAt && (
                <div>
                  <p className="text-sm text-gray-600">最后登录</p>
                  <p className="font-medium">{employee.lastLoginAt.toLocaleString()}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 关闭按钮 */}
        <div className="flex justify-end mt-6 pt-6 border-t">
          <Button onClick={onClose}>关闭</Button>
        </div>
      </div>
    </div>
  )
}
