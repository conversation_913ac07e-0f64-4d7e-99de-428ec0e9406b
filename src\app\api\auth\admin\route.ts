/**
 * 🇨🇳 风口云平台 - 管理员认证API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { generateTokenPair } from '@/lib/auth/jwt'

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json()

    if (!username || !password) {
      return NextResponse.json(
        { error: '用户名和密码不能为空' },
        { status: 400 }
      )
    }

    // 🔧 修复：获取客户端信息用于认证
    const ip = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // 🔧 新增：单点登录会话管理
    console.log('🔍 检查管理员活跃会话...')
    const { SingleSessionService } = await import('@/lib/services/single-session.service')

    // 先进行基本认证
    const admin = await db.authenticateAdmin(username, password, {
      ipAddress: ip,
      userAgent: userAgent,
      deviceInfo: parseDeviceInfo(userAgent)
    })

    if (!admin) {
      return NextResponse.json(
        { error: '用户名或密码错误' },
        { status: 401 }
      )
    }

    // 生成设备指纹
    const deviceFingerprint = SingleSessionService.generateDeviceFingerprint(
      ip,
      userAgent
    )

    console.log('🔍 设备指纹:', deviceFingerprint)

    // 智能检查活跃会话 - 严格单会话模式
    const activeSessionCheck = await SingleSessionService.checkActiveSession(
      admin.id,
      'admin',
      deviceFingerprint,
      {
        allowMultipleTabsOnSameDevice: false, // 🔧 禁止多标签页
        allowMultipleDevices: false,          // 管理员不允许多设备
        maxConcurrentSessions: 1,             // 管理员最多1个会话
        sessionTimeoutMinutes: 480            // 8小时超时
      }
    )

    if (activeSessionCheck.hasActiveSession) {
      console.log('⚠️ 发现活跃会话，严格单会话模式下立即踢出所有旧会话')

      // 生成临时会话ID用于踢出旧会话
      const tempSessionId = SingleSessionService.generateSessionId()

      // 立即使所有旧会话失效（严格单会话模式）
      await SingleSessionService.invalidateOldSessions(
        admin.id,
        'admin',
        tempSessionId,
        '新登录会话（严格单会话模式）'
      )
      console.log('✅ 已踢出所有旧会话（严格单会话模式）')
    }

    // 生成新的会话ID
    const sessionId = SingleSessionService.generateSessionId()
    console.log('🆔 生成管理员会话ID:', sessionId)

    // 🔧 新增：生成包含sessionId的JWT令牌
    const tokens = generateTokenPair({
      userId: admin.id,
      username: admin.username,
      name: admin.name,  // 包含管理员中文姓名
      userType: 'admin',
      role: admin.role,
      sessionId // 🔧 新增：包含会话ID
    })

    // 🔧 新增：创建管理员会话记录
    console.log('📝 创建管理员会话记录...')
    try {
      await SingleSessionService.createSessionRecord({
        sessionId,
        userId: admin.id,
        userType: 'admin',
        username: admin.username,
        ipAddress: ip,
        userAgent: userAgent,
        deviceInfo: deviceFingerprint,  // 使用设备指纹
        deviceFingerprint: deviceFingerprint
      })
      console.log('✅ 管理员会话记录创建成功')
    } catch (error) {
      console.error('❌ 创建管理员会话记录失败:', error)
      // 不影响登录流程，继续执行
    }

    // 返回管理员信息和令牌（不包含密码）
    const { passwordHash, ...adminInfo } = admin

    return NextResponse.json({
      success: true,
      user: adminInfo,
      ...tokens
    })

  } catch (error) {
    console.error('❌ 管理员认证失败:', error)
    return NextResponse.json(
      { error: '认证服务异常' },
      { status: 500 }
    )
  }
}

// 获取客户端真实IP地址
function getClientIP(request: NextRequest): string {
  // 尝试从各种头部获取真实IP
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip') // Cloudflare
  const xClientIP = request.headers.get('x-client-ip')

  // 优先级顺序处理
  if (cfConnectingIP) return normalizeIP(cfConnectingIP.trim())
  if (realIP) return normalizeIP(realIP.trim())
  if (xClientIP) return normalizeIP(xClientIP.trim())

  if (forwarded) {
    // x-forwarded-for 可能包含多个IP，取第一个
    const ips = forwarded.split(',').map(ip => ip.trim())
    for (const ip of ips) {
      const normalizedIP = normalizeIP(ip)
      // 过滤掉私有IP和本地IP
      if (isPublicIP(normalizedIP)) {
        return normalizedIP
      }
    }
    // 如果没有公网IP，返回第一个（标准化后）
    return normalizeIP(ips[0])
  }

  // 从连接信息获取（开发环境fallback）
  const url = new URL(request.url)
  if (url.hostname !== 'localhost' && url.hostname !== '127.0.0.1') {
    return url.hostname
  }

  // 开发环境默认值
  return '127.0.0.1'
}

// 标准化IP地址格式
function normalizeIP(ip: string): string {
  if (!ip) return '127.0.0.1'

  // 处理IPv6映射的IPv4地址 (::ffff:*********** -> ***********)
  if (ip.startsWith('::ffff:')) {
    return ip.substring(7)
  }

  // 处理IPv6本地地址
  if (ip === '::1') {
    return '127.0.0.1'
  }

  return ip
}

// 判断是否为公网IP
function isPublicIP(ip: string): boolean {
  if (!ip) return false

  // IPv6本地地址
  if (ip === '::1' || ip.startsWith('::ffff:127.') || ip.startsWith('fe80:')) {
    return false
  }

  // IPv4私有地址和本地地址
  if (ip === '127.0.0.1' || ip === 'localhost') return false
  if (ip.startsWith('192.168.')) return false
  if (ip.startsWith('10.')) return false
  if (ip.startsWith('172.')) {
    const parts = ip.split('.')
    if (parts.length >= 2) {
      const second = parseInt(parts[1])
      if (second >= 16 && second <= 31) return false
    }
  }

  return true
}

// 解析设备信息
function parseDeviceInfo(userAgent: string): string {
  if (!userAgent) return 'Unknown Device'

  if (userAgent.includes('Mobile')) return 'Mobile Device'
  if (userAgent.includes('Tablet')) return 'Tablet'
  if (userAgent.includes('Windows')) return 'Windows PC'
  if (userAgent.includes('Mac')) return 'Mac'
  if (userAgent.includes('Linux')) return 'Linux'

  return 'Desktop'
}
