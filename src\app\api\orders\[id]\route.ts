/**
 * 🇨🇳 风口云平台 - 订单管理API (单个订单操作)
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminOrFactoryAuth } from '@/lib/middleware/auth'

// 删除单个订单
export const DELETE = withAdminOrFactoryAuth(async (
  request: NextRequest,
  user
) => {
  try {
    // 从URL路径中提取订单ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const orderId = pathSegments[pathSegments.length - 1]

    if (!orderId) {
      return NextResponse.json(
        { error: '订单ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📦 删除订单请求:', {
      orderId,
      userType: user.userType,
      userId: user.userId
    })

    // 获取订单信息以验证权限
    const order = await db.getOrderById(orderId)
    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 权限检查：只有管理员或订单所属工厂的用户可以删除
    if (user.userType === 'factory_user' && user.factoryId !== order.factoryId) {
      return NextResponse.json(
        { error: '无权限删除此订单' },
        { status: 403 }
      )
    }

    // 删除订单
    const success = await db.deleteOrder(orderId)

    if (!success) {
      return NextResponse.json(
        { error: '删除订单失败' },
        { status: 500 }
      )
    }

    console.log('✅ 订单删除成功')

    return NextResponse.json({
      success: true,
      message: '订单删除成功'
    })

  } catch (error) {
    console.error('❌ 删除订单失败:', error)
    
    return NextResponse.json(
      { error: '删除订单失败' },
      { status: 500 }
    )
  }
})
