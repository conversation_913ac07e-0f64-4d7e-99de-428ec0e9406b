import type { FactoryUser, Client, OrderItem, User, isAdmin, isFactoryUser, isClient } from '@/types'
import { useAuthStore } from '@/lib/store/auth'

/**
 * 数据隔离中间件
 * 确保每个工厂只能访问自己的数据
 */

// 获取当前用户的工厂ID
export function getCurrentFactoryId(): string | null {
  if (typeof window === 'undefined') return null

  try {
    const { factoryId } = useAuthStore.getState()
    return factoryId
  } catch {
    return null
  }
}

// 获取当前用户信息
export function getCurrentUser(): FactoryUser | null {
  if (typeof window === 'undefined') return null

  try {
    const { user } = useAuthStore.getState()
    return user as FactoryUser
  } catch {
    return null
  }
}

// 检查用户是否有权限访问指定工厂的数据
export function hasFactoryAccess(targetFactoryId: string): boolean {
  const currentUser = getCurrentUser()
  const currentFactoryId = getCurrentFactoryId()
  const { role, user } = useAuthStore.getState()

  // 如果是总部管理员，可以访问所有工厂数据
  if ((user as User)?.userType === 'admin' || role === 'admin') {
    return true
  }

  // 工厂用户只能访问自己工厂的数据
  return currentFactoryId === targetFactoryId
}

// 过滤客户数据 - 只返回当前工厂的客户
export function filterClientsByFactory<T extends { factoryId: string }>(clients: T[]): T[] {
  const currentFactoryId = getCurrentFactoryId()
  if (!currentFactoryId) return []
  
  return clients.filter(client => client.factoryId === currentFactoryId)
}

// 过滤订单数据 - 只返回当前工厂的订单
export function filterOrdersByFactory<T extends { factoryId?: string }>(orders: T[]): T[] {
  const currentFactoryId = getCurrentFactoryId()
  if (!currentFactoryId) return []
  
  return orders.filter(order => order.factoryId === currentFactoryId)
}

// 过滤员工数据 - 只返回当前工厂的员工
export function filterEmployeesByFactory<T extends { factoryId: string }>(employees: T[]): T[] {
  const currentFactoryId = getCurrentFactoryId()
  if (!currentFactoryId) return []
  
  return employees.filter(employee => employee.factoryId === currentFactoryId)
}

// 为新创建的数据添加工厂ID
export function addFactoryId<T extends Record<string, any>>(data: T): T & { factoryId: string } {
  const currentFactoryId = getCurrentFactoryId()
  if (!currentFactoryId) {
    throw new Error('无法获取当前工厂ID')
  }
  
  return {
    ...data,
    factoryId: currentFactoryId
  }
}

// 验证数据是否属于当前工厂
export function validateFactoryOwnership(factoryId: string): boolean {
  const currentFactoryId = getCurrentFactoryId()
  const { role, user } = useAuthStore.getState()

  // 总部管理员可以访问所有数据
  if ((user as User)?.userType === 'admin' || role === 'admin') {
    return true
  }

  // 工厂用户只能访问自己工厂的数据
  return currentFactoryId === factoryId
}

// 权限检查装饰器
export function requireFactoryAccess<T extends unknown[], R>(
  fn: (...args: T) => R,
  factoryIdExtractor?: (args: T) => string
) {
  return (...args: T): R => {
    let targetFactoryId: string
    
    if (factoryIdExtractor) {
      targetFactoryId = factoryIdExtractor(args)
    } else {
      // 默认从第一个参数中提取factoryId
      const firstArg = args[0]
      if (typeof firstArg === 'object' && firstArg && 'factoryId' in firstArg) {
        targetFactoryId = firstArg.factoryId
      } else {
        throw new Error('无法提取工厂ID')
      }
    }
    
    if (!hasFactoryAccess(targetFactoryId)) {
      throw new Error('没有权限访问该工厂的数据')
    }
    
    return fn(...args)
  }
}

// API请求拦截器 - 自动添加工厂ID过滤
export function createFactoryApiRequest(baseUrl: string) {
  return {
    get: async (endpoint: string, params?: Record<string, any>) => {
      const currentFactoryId = getCurrentFactoryId()
      const url = new URL(`${baseUrl}${endpoint}`)
      
      // 自动添加工厂ID过滤参数
      if (currentFactoryId) {
        url.searchParams.set('factoryId', currentFactoryId)
      }
      
      // 添加其他参数
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          url.searchParams.set(key, String(value))
        })
      }
      
      const response = await fetch(url.toString())
      return response.json()
    },
    
    post: async (endpoint: string, data: unknown) => {
      const currentFactoryId = getCurrentFactoryId()
      
      // 自动添加工厂ID到请求数据
      const requestData = currentFactoryId ? { ...data, factoryId: currentFactoryId } : data
      
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })
      
      return response.json()
    },
    
    put: async (endpoint: string, data: unknown) => {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })
      
      return response.json()
    },
    
    delete: async (endpoint: string) => {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'DELETE'
      })
      
      return response.json()
    }
  }
}

// 数据验证工具
export const DataIsolationUtils = {
  // 验证客户是否属于当前工厂
  validateClient: (client: { factoryId: string }) => {
    return validateFactoryOwnership(client.factoryId)
  },
  
  // 验证订单是否属于当前工厂
  validateOrder: (order: { factoryId?: string }) => {
    if (!order.factoryId) return false
    return validateFactoryOwnership(order.factoryId)
  },
  
  // 验证员工是否属于当前工厂
  validateEmployee: (employee: { factoryId: string }) => {
    return validateFactoryOwnership(employee.factoryId)
  },
  
  // 获取安全的数据查询参数
  getSecureQueryParams: () => {
    const currentFactoryId = getCurrentFactoryId()
    return currentFactoryId ? { factoryId: currentFactoryId } : {}
  }
}

// 错误类型
export class DataIsolationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'DataIsolationError'
  }
}

// 数据隔离状态检查
export function checkDataIsolationStatus() {
  try {
    const { role, user, isAuthenticated } = useAuthStore.getState()
    const currentUser = getCurrentUser()
    const currentFactoryId = getCurrentFactoryId()

    // 添加更详细的日志
    console.log('🔍 数据隔离状态检查:', {
      isAuthenticated,
      hasUser: !!currentUser,
      hasFactoryId: !!currentFactoryId,
      userRole: role,
      userType: (user as User)?.userType
    })

    // 对于管理员用户，认证状态主要看isAuthenticated和user
    const isAdminUser = (user as User)?.userType === 'admin' || role === 'admin'
    const authStatus = isAdminUser ? (isAuthenticated && !!user) : !!currentUser

    return {
      isAuthenticated: authStatus,
      hasFactoryId: !!currentFactoryId,
      userRole: role,
      factoryId: currentFactoryId,
      canAccessAllFactories: isAdminUser
    }
  } catch (error) {
    console.error('❌ 数据隔离状态检查失败:', error)
    return {
      isAuthenticated: false,
      hasFactoryId: false,
      userRole: null,
      factoryId: null,
      canAccessAllFactories: false
    }
  }
}
