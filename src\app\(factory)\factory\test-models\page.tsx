"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Brain, Zap, Clock, DollarSign, TestTube } from 'lucide-react'
import { deepSeekAPI } from '@/lib/services/deepseek-api'
import { complexityDetector } from '@/lib/services/complexity-detector'

export default function TestModelsPage() {
  const [inputText, setInputText] = useState('')
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<any[]>([])

  const testCases = [
    {
      name: '简单案例',
      text: `金色时代10栋1901号房
大厅出风口：300X1500mm
餐厅出风口：2615X150mm`,
      expectedModel: 'deepseek-chat'
    },
    {
      name: '复杂案例',
      text: `武宣镇天润城16-1905，
大厅出风口:3090✘150分段
餐厅出风口:2615✘150
进风口:3110✘300分段
主卧室 | 回风口 | 300×1200 | 加急
次卧室 | 出风口 | 110×2590 | 定制
客厅左边出风口大概3000×150左右
餐厅右边回风口约300×1500差不多`,
      expectedModel: 'deepseek-reasoner'
    },
    {
      name: '多项目案例',
      text: `项目一：金色时代10栋1901
大厅出风口：300X1500mm

项目二：银河小区5栋2301
客厅出风口：400X200mm
主卧回风口：300X1200mm

项目三：天润城16栋1905
餐厅 | 出风口 | 2615×150 | 分段`,
      expectedModel: 'deepseek-reasoner'
    }
  ]

  const runComparison = async (text: string) => {
    setTesting(true)
    const testResults = []

    try {
      // 1. 复杂度分析
      const complexity = complexityDetector.analyzeComplexity(text)
      
      // 2. 测试V3模型
      const startTimeV3 = Date.now()
      const v3Result = await deepSeekAPI.analyzeVentOrder(text, 'deepseek-chat')
      const v3Time = Date.now() - startTimeV3

      // 3. 测试R1模型
      const startTimeR1 = Date.now()
      const r1Result = await deepSeekAPI.analyzeVentOrder(text, 'deepseek-reasoner')
      const r1Time = Date.now() - startTimeR1

      // 4. 测试智能选择
      const startTimeAuto = Date.now()
      const autoResult = await deepSeekAPI.analyzeVentOrder(text)
      const autoTime = Date.now() - startTimeAuto

      testResults.push({
        complexity,
        v3: { result: v3Result, time: v3Time },
        r1: { result: r1Result, time: r1Time },
        auto: { result: autoResult, time: autoTime }
      })

    } catch (error) {
      console.error('测试失败:', error)
      alert('测试失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }

    setResults(testResults)
    setTesting(false)
  }

  const runAllTests = async () => {
    setTesting(true)
    setResults([])

    for (const testCase of testCases) {
      console.log(`🧪 测试案例: ${testCase.name}`)
      await runComparison(testCase.text)
      // 等待一下避免API限制
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    setTesting(false)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">DeepSeek V3 vs R1 模型对比测试</h1>
          <p className="text-gray-600">测试不同模型在风口识别任务中的表现</p>
        </div>
        <Badge variant="outline" className="bg-blue-50 text-blue-700">
          <TestTube className="h-4 w-4 mr-1" />
          测试工具
        </Badge>
      </div>

      {/* 自定义测试 */}
      <Card>
        <CardHeader>
          <CardTitle>自定义测试</CardTitle>
          <CardDescription>输入您的测试文本，对比不同模型的表现</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="请输入要测试的风口订单文本..."
            className="min-h-[120px]"
          />
          <div className="flex gap-2">
            <Button
              onClick={() => runComparison(inputText)}
              disabled={testing || !inputText.trim()}
              className="flex-1"
            >
              {testing ? '测试中...' : '开始对比测试'}
            </Button>
            <Button
              variant="outline"
              onClick={() => setInputText('')}
              disabled={testing}
            >
              清空
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 预设测试案例 */}
      <Card>
        <CardHeader>
          <CardTitle>预设测试案例</CardTitle>
          <CardDescription>运行预设的测试案例，验证模型选择逻辑</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testCases.map((testCase, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{testCase.name}</h4>
                  <Badge variant="outline">
                    预期: {testCase.expectedModel === 'deepseek-chat' ? 'V3快速' : 'R1推理'}
                  </Badge>
                </div>
                <pre className="text-sm bg-gray-50 p-2 rounded whitespace-pre-wrap">
                  {testCase.text}
                </pre>
                <Button
                  size="sm"
                  variant="outline"
                  className="mt-2"
                  onClick={() => runComparison(testCase.text)}
                  disabled={testing}
                >
                  测试此案例
                </Button>
              </div>
            ))}
            
            <Button
              onClick={runAllTests}
              disabled={testing}
              className="w-full"
            >
              {testing ? '运行中...' : '运行所有测试案例'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>测试结果对比</CardTitle>
          </CardHeader>
          <CardContent>
            {results.map((result, index) => (
              <div key={index} className="space-y-4 border-b pb-4 mb-4 last:border-b-0">
                {/* 复杂度分析 */}
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                  <h4 className="font-medium text-amber-800 mb-2">复杂度分析</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>评分: {result.complexity.score}/10</div>
                    <div>推荐: {result.complexity.recommendedModel === 'deepseek-chat' ? 'V3' : 'R1'}</div>
                  </div>
                  {result.complexity.reasons.length > 0 && (
                    <div className="mt-2 text-xs text-amber-700">
                      原因: {result.complexity.reasons.join(', ')}
                    </div>
                  )}
                </div>

                {/* 模型对比 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* V3结果 */}
                  <div className="border border-green-200 rounded-lg p-3 bg-green-50">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-green-800 flex items-center">
                        <Zap className="h-4 w-4 mr-1" />
                        V3快速模型
                      </h5>
                      <Badge variant="outline" className="text-xs">
                        <Clock className="h-3 w-3 mr-1" />
                        {result.v3.time}ms
                      </Badge>
                    </div>
                    <div className="text-sm space-y-1">
                      <div>置信度: {(result.v3.result.confidence * 100).toFixed(1)}%</div>
                      <div>项目数: {result.v3.result.projects.length}</div>
                      <div>警告数: {result.v3.result.warnings.length}</div>
                    </div>
                  </div>

                  {/* R1结果 */}
                  <div className="border border-purple-200 rounded-lg p-3 bg-purple-50">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-purple-800 flex items-center">
                        <Brain className="h-4 w-4 mr-1" />
                        R1推理模型
                      </h5>
                      <Badge variant="outline" className="text-xs">
                        <Clock className="h-3 w-3 mr-1" />
                        {result.r1.time}ms
                      </Badge>
                    </div>
                    <div className="text-sm space-y-1">
                      <div>置信度: {(result.r1.result.confidence * 100).toFixed(1)}%</div>
                      <div>项目数: {result.r1.result.projects.length}</div>
                      <div>警告数: {result.r1.result.warnings.length}</div>
                      {result.r1.result.reasoningContent && (
                        <div className="text-purple-600">✓ 包含推理过程</div>
                      )}
                    </div>
                  </div>

                  {/* 智能选择结果 */}
                  <div className="border border-blue-200 rounded-lg p-3 bg-blue-50">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-blue-800 flex items-center">
                        <Brain className="h-4 w-4 mr-1" />
                        智能选择
                      </h5>
                      <Badge variant="outline" className="text-xs">
                        <Clock className="h-3 w-3 mr-1" />
                        {result.auto.time}ms
                      </Badge>
                    </div>
                    <div className="text-sm space-y-1">
                      <div>选择模型: {result.auto.result.modelUsed === 'deepseek-chat' ? 'V3' : 'R1'}</div>
                      <div>置信度: {(result.auto.result.confidence * 100).toFixed(1)}%</div>
                      <div>项目数: {result.auto.result.projects.length}</div>
                      <div className={`font-medium ${
                        result.auto.result.modelUsed === result.complexity.recommendedModel 
                          ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {result.auto.result.modelUsed === result.complexity.recommendedModel 
                          ? '✓ 选择正确' : '✗ 选择不符'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
