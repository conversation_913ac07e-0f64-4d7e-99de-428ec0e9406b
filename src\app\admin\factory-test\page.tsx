'use client'

import { useState, useEffect } from 'react'

export default function FactoryTestPage() {
  const [factories, setFactories] = useState<any[]>([])
  const [loginRecords, setLoginRecords] = useState<any[]>([])
  const [selectedFactory, setSelectedFactory] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [adminCredentials, setAdminCredentials] = useState({ username: '', password: '' })

  // 获取工厂列表
  const fetchFactories = async () => {
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch('/api/admin/factories', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 401) {
        alert('需要管理员登录才能访问此功能')
        return
      }

      const data = await response.json()
      if (data.success) {
        setFactories(data.factories)
      } else {
        console.error('获取工厂列表失败:', data.error)
      }
    } catch (error) {
      console.error('获取工厂列表失败:', error)
    }
  }

  // 获取登录记录
  const fetchLoginRecords = async (factoryId?: string) => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (factoryId) params.append('factoryId', factoryId)
      params.append('limit', '20')

      const token = localStorage.getItem('auth_token')
      const response = await fetch(`/api/admin/login-records?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 401) {
        console.log('需要管理员登录才能查看登录记录')
        return
      }

      const data = await response.json()
      if (data.success) {
        setLoginRecords(data.records)
      } else {
        console.error('获取登录记录失败:', data.error)
      }
    } catch (error) {
      console.error('获取登录记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 测试工厂登录
  const testFactoryLogin = async (username: string, password: string) => {
    try {
      const response = await fetch('/api/auth/factory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password })
      })
      
      const data = await response.json()
      
      if (response.ok) {
        alert(`登录成功: ${data.user.name}`)
      } else {
        alert(`登录失败: ${data.error}`)
      }
      
      // 刷新登录记录
      fetchLoginRecords(selectedFactory)
    } catch (error) {
      console.error('测试登录失败:', error)
      alert('测试登录失败')
    }
  }

  // 更新工厂状态
  const updateFactoryStatus = async (factoryId: string, status: string, reason?: string) => {
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch('/api/admin/factories/status', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ factoryId, status, reason })
      })

      if (response.status === 401) {
        alert('需要管理员登录才能更新工厂状态')
        return
      }

      const data = await response.json()

      if (data.success) {
        alert(`状态更新成功: ${data.message}`)
        fetchFactories() // 刷新工厂列表
      } else {
        alert(`状态更新失败: ${data.error}`)
      }
    } catch (error) {
      console.error('更新状态失败:', error)
      alert('更新状态失败')
    }
  }

  // 创建测试管理员账号
  const createTestAdmin = async () => {
    try {
      const response = await fetch('/api/admin/create-test-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const data = await response.json()

      if (data.success) {
        alert('测试管理员账号创建成功！用户名: admin, 密码: admin123')
      } else {
        alert(`创建失败: ${data.message || data.error}`)
      }
    } catch (error) {
      console.error('创建测试管理员失败:', error)
      alert('创建测试管理员失败')
    }
  }

  // 管理员登录
  const adminLogin = async () => {
    try {
      const response = await fetch('/api/auth/admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(adminCredentials)
      })

      const data = await response.json()

      if (response.ok && data.token) {
        localStorage.setItem('auth_token', data.token)
        setIsLoggedIn(true)
        alert('管理员登录成功')
        fetchFactories()
        fetchLoginRecords()
      } else {
        alert(`登录失败: ${data.error}`)
      }
    } catch (error) {
      console.error('管理员登录失败:', error)
      alert('管理员登录失败')
    }
  }

  // 检查登录状态
  const checkLoginStatus = () => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      setIsLoggedIn(true)
      fetchFactories()
      fetchLoginRecords()
    }
  }

  useEffect(() => {
    checkLoginStatus()
  }, [])

  // 如果未登录，显示登录界面
  if (!isLoggedIn) {
    return (
      <div className="p-6 max-w-md mx-auto mt-20">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6 text-center">管理员登录</h1>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">用户名</label>
              <input
                type="text"
                value={adminCredentials.username}
                onChange={(e) => setAdminCredentials({...adminCredentials, username: e.target.value})}
                className="w-full border rounded px-3 py-2"
                placeholder="请输入管理员用户名"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">密码</label>
              <input
                type="password"
                value={adminCredentials.password}
                onChange={(e) => setAdminCredentials({...adminCredentials, password: e.target.value})}
                className="w-full border rounded px-3 py-2"
                placeholder="请输入密码"
              />
            </div>
            <button
              onClick={adminLogin}
              className="w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600"
            >
              登录
            </button>
            <button
              onClick={createTestAdmin}
              className="w-full bg-green-500 text-white py-2 rounded hover:bg-green-600"
            >
              创建测试管理员账号
            </button>
            <div className="text-sm text-gray-600 text-center">
              <p>测试账号: admin / admin123</p>
              <p className="text-xs mt-1">如果登录失败，请先点击"创建测试管理员账号"</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">工厂登录记录和状态管理测试</h1>
        <button
          onClick={() => {
            localStorage.removeItem('auth_token')
            setIsLoggedIn(false)
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          退出登录
        </button>
      </div>
      
      {/* 工厂列表 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3">工厂列表</h2>
        <div className="grid gap-4">
          {factories.map((factory) => (
            <div key={factory.id} className="border p-4 rounded-lg">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-medium">{factory.name} ({factory.code})</h3>
                  <p className="text-sm text-gray-600">
                    状态: <span className={`px-2 py-1 rounded text-xs ${
                      factory.status === 'active' ? 'bg-green-100 text-green-800' :
                      factory.status === 'suspended' ? 'bg-red-100 text-red-800' :
                      factory.status === 'expired' ? 'bg-orange-100 text-orange-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {factory.status}
                    </span>
                  </p>
                  <p className="text-sm text-gray-600">
                    订阅类型: {factory.subscriptionType} | 
                    永久: {factory.isPermanent ? '是' : '否'}
                  </p>
                  {factory.subscriptionEnd && (
                    <p className="text-sm text-gray-600">
                      到期时间: {new Date(factory.subscriptionEnd).toLocaleString()}
                    </p>
                  )}
                  {factory.suspendedReason && (
                    <p className="text-sm text-red-600">
                      暂停原因: {factory.suspendedReason}
                    </p>
                  )}
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => updateFactoryStatus(factory.id, 'active')}
                    className="px-3 py-1 bg-green-500 text-white rounded text-sm"
                  >
                    激活
                  </button>
                  <button
                    onClick={() => updateFactoryStatus(factory.id, 'suspended', '测试暂停')}
                    className="px-3 py-1 bg-red-500 text-white rounded text-sm"
                  >
                    暂停
                  </button>
                  <button
                    onClick={() => {
                      setSelectedFactory(factory.id)
                      fetchLoginRecords(factory.id)
                    }}
                    className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
                  >
                    查看记录
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 测试登录 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3">测试登录</h2>
        <div className="flex gap-4">
          <button
            onClick={() => testFactoryLogin('sh002', 'shh@2025')}
            className="px-4 py-2 bg-blue-500 text-white rounded"
          >
            测试用户登录 (sh002)
          </button>
          <button
            onClick={() => testFactoryLogin('invalid', 'invalid')}
            className="px-4 py-2 bg-gray-500 text-white rounded"
          >
            测试无效登录
          </button>
        </div>
      </div>

      {/* 登录记录 */}
      <div>
        <h2 className="text-lg font-semibold mb-3">
          登录记录 
          {selectedFactory && (
            <span className="text-sm font-normal text-gray-600">
              (工厂: {factories.find(f => f.id === selectedFactory)?.name})
            </span>
          )}
        </h2>
        
        <div className="mb-3">
          <select
            value={selectedFactory}
            onChange={(e) => {
              setSelectedFactory(e.target.value)
              fetchLoginRecords(e.target.value || undefined)
            }}
            className="border rounded px-3 py-2"
          >
            <option value="">所有工厂</option>
            {factories.map((factory) => (
              <option key={factory.id} value={factory.id}>
                {factory.name} ({factory.code})
              </option>
            ))}
          </select>
          <button
            onClick={() => fetchLoginRecords(selectedFactory || undefined)}
            className="ml-2 px-4 py-2 bg-blue-500 text-white rounded"
            disabled={loading}
          >
            {loading ? '加载中...' : '刷新'}
          </button>
        </div>

        <div className="border rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left">时间</th>
                <th className="px-4 py-2 text-left">用户</th>
                <th className="px-4 py-2 text-left">工厂</th>
                <th className="px-4 py-2 text-left">状态</th>
                <th className="px-4 py-2 text-left">IP地址</th>
                <th className="px-4 py-2 text-left">失败原因</th>
              </tr>
            </thead>
            <tbody>
              {loginRecords.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                    {loading ? '加载中...' : '暂无登录记录'}
                  </td>
                </tr>
              ) : (
                loginRecords.map((record, index) => (
                  <tr key={index} className="border-t">
                    <td className="px-4 py-2 text-sm">
                      {new Date(record.loginTime).toLocaleString()}
                    </td>
                    <td className="px-4 py-2 text-sm">
                      {record.userName} ({record.username})
                    </td>
                    <td className="px-4 py-2 text-sm">
                      {record.factoryName || '-'}
                    </td>
                    <td className="px-4 py-2 text-sm">
                      <span className={`px-2 py-1 rounded text-xs ${
                        record.loginStatus === 'success' ? 'bg-green-100 text-green-800' :
                        record.loginStatus === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-orange-100 text-orange-800'
                      }`}>
                        {record.loginStatus}
                      </span>
                    </td>
                    <td className="px-4 py-2 text-sm">
                      {record.ipAddress || '-'}
                    </td>
                    <td className="px-4 py-2 text-sm text-red-600">
                      {record.failReason || '-'}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
