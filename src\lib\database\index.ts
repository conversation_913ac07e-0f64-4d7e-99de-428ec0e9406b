/**
 * 🇨🇳 风口云平台 - MySQL数据库服务
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 提供统一的MySQL数据库服务接口
 * - 使用Prisma ORM进行数据操作
 * - 确保数据持久化和一致性
 */

import { PrismaClient, FactoryStatus, ClientStatus, ShareholderType, ShareholderStatus, DividendStatus } from '@prisma/client'
import { calculateTotalReferralReward, canUseReward  } from "@/lib/utils/reward-calculator"
import type { Order, Factory, FactoryUser, Client, RewardRecord, Announcement, Shareholder, Dividend, DividendRecord } from "@/types"
import { toNumber } from "@/types"
import bcrypt from 'bcryptjs'

// 全局Prisma客户端实例
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

/**
 * PostgreSQL数据库服务
 * 使用Prisma ORM进行所有数据操作
 */
class DatabaseService {

  constructor() {
    // 初始化数据库连接
    this.initializeDatabase()
  }

  /**
   * 初始化数据库连接和基础数据
   */
  private async initializeDatabase() {
    try {
      // 测试数据库连接
      await prisma.$connect()
      console.log('✅ PostgreSQL数据库连接成功')
      
      // 初始化基础数据
      await this.initializeBaseData()
    } catch (error) {
      console.error('❌ 数据库连接失败:', error)
    }
  }

  /**
   * 初始化基础数据（仅在数据库为空时执行）
   */
  private async initializeBaseData() {
    try {
      console.log('🔄 检查数据库状态...')

      // 检查是否已有数据
      const adminCount = await prisma.admin.count()
      const factoryCount = await prisma.factory.count()

      if (adminCount > 0 && factoryCount > 0) {
        console.log('✅ 数据库已有数据，跳过初始化')
        return
      }

      console.log('🔄 数据库为空，开始初始化基础数据...')

      // 创建默认管理员（如果不存在）
      let admin = await prisma.admin.findFirst({
        where: { username: 'admin' }
      })

      if (!admin) {
        const adminPassword = await bcrypt.hash('admin@yhg2025', 12)
        admin = await prisma.admin.create({
          data: {
            username: 'admin',
            passwordHash: adminPassword,
            name: '系统管理员',
            email: '<EMAIL>',
            role: 'admin',
            isActive: true
          }
        })
        console.log('✅ 默认管理员创建成功')
      }

      // 创建默认工厂（如果不存在）
      let factory = await prisma.factory.findFirst({
        where: { code: 'lin001' }
      })

      if (!factory) {
        factory = await prisma.factory.create({
          data: {
            id: 'lin001',
            name: '南宁加工厂',
            code: 'lin001',
            address: '广西南宁市',
            phone: '0771-1234567',
            status: FactoryStatus.active
          }
        })
        console.log('✅ 默认工厂创建成功')
      }

      // 创建默认工厂用户（如果不存在）
      let factoryUser = await prisma.factoryUser.findFirst({
        where: { username: 'lin001' }
      })

      if (!factoryUser) {
        const hashedPassword = await bcrypt.hash('lin001', 12)
        factoryUser = await prisma.factoryUser.create({
          data: {
            factoryId: factory.id,
            username: 'lin001',
            passwordHash: hashedPassword,
            name: '林经理',
            role: 'owner',
            permissions: JSON.stringify(['all']),
            isActive: true
          }
        })
        console.log('✅ 默认工厂用户创建成功')
      }

      console.log('✅ 基础数据初始化完成')

      // 添加测试数据（仅在完全空数据库时）
      const clientCount = await prisma.client.count()
      if (clientCount === 0) {
        await this.addTestData(factory.id, factoryUser.id)
      }
    } catch (error) {
      console.error('❌ 初始化基础数据失败:', error)
    }
  }

  /**
   * 添加测试数据
   */
  private async addTestData(factoryId: string, userId: string): Promise<void> {
    try {
      console.log('📝 开始添加测试数据...')

      // 创建测试客户数据
      const clients = await Promise.all([
        prisma.client.create({
          data: {
            factoryId: factoryId,
            name: '张三',
            phone: '13800138001',
            email: '<EMAIL>',
            company: '广西建筑公司',
            address: '南宁市兴宁区建设路123号',
            status: ClientStatus.active
          }
        }),
        prisma.client.create({
          data: {
            factoryId: factoryId,
            name: '李四',
            phone: '13800138002',
            email: '<EMAIL>',
            company: '南宁装饰工程有限公司',
            address: '南宁市江南区民族大道456号',
            status: ClientStatus.active
          }
        }),
        prisma.client.create({
          data: {
            factoryId: factoryId,
            name: '王五',
            phone: '13800138003',
            company: '桂林建设集团',
            address: '桂林市象山区中山路789号',
            status: ClientStatus.active
          }
        })
      ])

      console.log('✅ 测试客户创建完成:', clients.length, '个')

      // 创建测试订单数据
      const timestamp = Date.now()
      const orders = await Promise.all([
        prisma.order.create({
          data: {
            orderNumber: `ORD-${timestamp}-001`,
            factoryId: factoryId,
            clientId: clients[0].id,
            clientName: clients[0].name,
            clientPhone: clients[0].phone,
            projectAddress: '南宁市青秀区办公楼项目',
            items: [
              {
                type: 'regular',
                length: 600,
                width: 400,
                quantity: 20,
                unitPrice: 45.0,
                totalPrice: 18000.0,
                floor: '1楼大厅'
              },
              {
                type: 'premium',
                length: 800,
                width: 0,
                quantity: 15,
                unitPrice: 120.0,
                totalPrice: 14400.0,
                floor: '2楼会议室'
              }
            ],
            totalAmount: 32400.0,
            status: 'pending',
            createdBy: userId,
            notes: '紧急订单，需要加急处理'
          }
        }),
        prisma.order.create({
          data: {
            orderNumber: `ORD-${timestamp}-002`,
            factoryId: factoryId,
            clientId: clients[1].id,
            clientName: clients[1].name,
            clientPhone: clients[1].phone,
            projectAddress: '江南区商业中心装修项目',
            items: [
              {
                type: 'regular',
                length: 500,
                width: 300,
                quantity: 30,
                unitPrice: 42.0,
                totalPrice: 25200.0,
                floor: '1-3楼商铺'
              }
            ],
            totalAmount: 25200.0,
            status: 'completed',
            createdBy: userId,
            notes: '标准订单'
          }
        }),
        prisma.order.create({
          data: {
            orderNumber: `ORD-${timestamp}-003`,
            factoryId: factoryId,
            clientId: clients[2].id,
            clientName: clients[2].name,
            clientPhone: clients[2].phone,
            projectAddress: '桂林市酒店改造项目',
            items: [
              {
                type: 'premium',
                length: 1000,
                width: 0,
                quantity: 25,
                unitPrice: 150.0,
                totalPrice: 37500.0,
                floor: '酒店大堂'
              },
              {
                type: 'regular',
                length: 400,
                width: 250,
                quantity: 40,
                unitPrice: 38.0,
                totalPrice: 15200.0,
                floor: '客房区域'
              }
            ],
            totalAmount: 52700.0,
            status: 'production',
            createdBy: userId,
            notes: '高端项目，质量要求严格'
          }
        })
      ])

      console.log('✅ 测试订单创建完成:', orders.length, '个')

      // 创建付款记录
      await Promise.all([
        prisma.payment.create({
          data: {
            orderId: orders[1].id, // 已完成订单的付款
            amount: 25200.0,
            method: 'BANK_TRANSFER',
            reference: 'TXN20240115001',
            notes: '银行转账全款',
            recordedBy: userId
          }
        }),
        prisma.payment.create({
          data: {
            orderId: orders[2].id, // 进行中订单的部分付款
            amount: 26350.0, // 50%预付款
            method: 'CASH',
            reference: 'CASH20240120001',
            notes: '现金预付款50%',
            recordedBy: userId
          }
        })
      ])

      console.log('✅ 测试付款记录创建完成')

      // 更新客户统计信息
      for (const client of clients) {
        await this.updateClientStatistics(client.id)
      }

      console.log('✅ 测试数据添加完成')
      console.log(`   - 客户: ${clients.length} 个`)
      console.log(`   - 订单: ${orders.length} 个`)
      console.log(`   - 总金额: ¥${orders.reduce((sum, order) => sum + toNumber(order.totalAmount), 0).toLocaleString()}`)
    } catch (error) {
      console.error('❌ 添加测试数据失败:', error)
    }
  }

  /**
   * 创建订单（支持订单号冲突重试）
   */
  async createOrder(orderData: Omit<Order, 'id'>): Promise<Order | null> {
    const maxRetries = 3
    let attempt = 0

    while (attempt < maxRetries) {
      try {
        console.log(`📦 开始创建订单 (尝试 ${attempt + 1}/${maxRetries}):`, {
          factoryId: orderData.factoryId,
          clientId: orderData.clientId,
          orderNumber: orderData.orderNumber,
          itemsCount: orderData.items?.length,
          totalAmount: orderData.totalAmount
        })

        // 验证必要字段
        if (!orderData.factoryId) {
          throw new Error('缺少工厂ID')
        }
        if (!orderData.clientId) {
          throw new Error('缺少客户ID')
        }
        if (!orderData.orderNumber) {
          throw new Error('缺少订单号')
        }
        if (!orderData.items || orderData.items.length === 0) {
          throw new Error('订单必须包含至少一个项目')
        }
        if (!orderData.createdBy) {
          throw new Error('缺少创建者信息')
        }

        const order = await prisma.order.create({
          data: {
            orderNumber: orderData.orderNumber,
            clientName: orderData.clientName,
            clientPhone: orderData.clientPhone,
            projectAddress: orderData.projectAddress,
            items: JSON.stringify(orderData.items), // SQLite中存储为JSON字符串
            totalAmount: orderData.totalAmount,
            status: orderData.status || 'pending',
            createdByName: orderData.createdByName, // 保存用户姓名
            notes: orderData.notes,
            // 使用关系连接而不是直接设置ID
            factory: {
              connect: { id: orderData.factoryId }
            },
            client: {
              connect: { id: orderData.clientId }
            },
            creator: {
              connect: { id: orderData.createdBy }
            }
          }
        })

        console.log('✅ 订单创建成功:', order.id)

        // 自动更新客户统计信息
        if (orderData.clientId && orderData.clientId !== 'new') {
          console.log('🔄 更新客户统计信息:', orderData.clientId)
          await this.updateClientStatistics(orderData.clientId)
        }

        // 解析items字段返回给客户端
        let parsedItems = []
        try {
          if (typeof order.items === 'string') {
            parsedItems = JSON.parse(order.items)
          } else if (Array.isArray(order.items)) {
            parsedItems = order.items
          }
        } catch (error) {
          console.warn('⚠️ 解析新创建订单items失败:', error)
          parsedItems = []
        }

        return {
          ...order,
          items: parsedItems
        } as Order
      } catch (error) {
        console.error(`❌ 创建订单失败 (尝试 ${attempt + 1}/${maxRetries}):`, error)

        // 检查是否是订单号重复错误
        if ((error as unknown).code === 'P2002' && (error as unknown).meta?.target?.includes('order_number')) {
          console.warn(`⚠️ 订单号重复冲突: ${orderData.orderNumber}`)

          if (attempt < maxRetries - 1) {
            // 重新生成订单号（服务端版本）
            try {
              const newOrderNumber = await this.generateOrderNumberServer(orderData.factoryId, attempt)
              console.log(`🔄 重新生成订单号: ${orderData.orderNumber} -> ${newOrderNumber}`)
              orderData.orderNumber = newOrderNumber
              attempt++
              continue
            } catch (genError) {
              console.error('❌ 重新生成订单号失败:', genError)
            }
          }
        }

        // 如果不是订单号冲突或已达到最大重试次数，直接抛出错误
        console.error('错误详情:', {
          message: (error as unknown).message,
          code: (error as unknown).code,
          meta: (error as unknown).meta
        })
        throw error
      }
    }

    throw new Error('创建订单失败：达到最大重试次数')
  }

  /**
   * 服务端版本的订单号生成函数（支持重试）
   */
  private async generateOrderNumberServer(factoryId: string, attempt: number = 0): Promise<string> {
    try {
      // 获取工厂设置中的订单号前缀（从数据库读取）
      const { getFactorySettingsFromDB, getDefaultSettingsForFactory } = await import('@/lib/utils/factory-settings')
      const dbSettings = await getFactorySettingsFromDB(factoryId)
      const settings = dbSettings || getDefaultSettingsForFactory(factoryId)
      const { getDefaultOrderPrefix } = await import('@/lib/utils/factory')
      const prefix = settings.orderNumberPrefix || getDefaultOrderPrefix(factoryId)

      console.log(`🏭 工厂 ${factoryId} 使用订单号前缀: ${prefix}`)

      // 生成日期字符串 (YYYYMMDD)
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const dateStr = `${year}${month}${day}`

      // 获取当日订单序号（实时查询，确保最新数据）
      const orders = await this.getOrdersByFactoryId(factoryId)
      const todayOrderNumbers = orders
        .filter(order => {
          const orderDate = new Date(order.createdAt)
          const orderDateStr = `${orderDate.getFullYear()}${String(orderDate.getMonth() + 1).padStart(2, '0')}${String(orderDate.getDate()).padStart(2, '0')}`
          return orderDateStr === dateStr
        })
        .map(order => {
          // 从订单号中提取序号部分，例如 "JGC-20250615-03" -> 3
          const match = order.orderNumber?.match(/-(\d+)$/)
          return match ? parseInt(match[1], 10) : 0
        })
        .filter(num => !isNaN(num))

      // 找到最大序号，然后 +1，并加上重试次数以避免冲突
      const maxSequence = todayOrderNumbers.length > 0 ? Math.max(...todayOrderNumbers) : 0
      const sequence = maxSequence + 1 + attempt
      const sequenceStr = String(sequence).padStart(2, '0')

      const orderNumber = `${prefix}-${dateStr}-${sequenceStr}`
      console.log(`✅ 服务端订单号生成成功 (尝试 ${attempt + 1}): ${orderNumber}`)

      // 验证订单号是否已存在
      const existingOrder = orders.find(order => order.orderNumber === orderNumber)
      if (existingOrder) {
        console.warn(`⚠️ 服务端生成的订单号 ${orderNumber} 已存在，使用时间戳方案`)
        throw new Error(`订单号 ${orderNumber} 已存在`)
      }

      return orderNumber
    } catch (error) {
      console.error(`❌ 服务端订单号生成失败 (尝试 ${attempt + 1}):`, error)
      // 回退到时间戳 + 随机数方式，确保唯一性
      const timestamp = Date.now()
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      const fallbackOrderNumber = `ORD-${timestamp}-${random}`
      console.log(`🔄 使用回退订单号: ${fallbackOrderNumber}`)
      return fallbackOrderNumber
    }
  }

  /**
   * 获取所有订单（用于数据修复）
   */
  async getAllOrders(): Promise<Order[]> {
    try {
      console.log('📋 获取所有订单')

      const orders = await prisma.order.findMany({
        include: {
          client: {
            select: {
              name: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      console.log('✅ 所有订单获取成功:', orders.length, '条')

      // 将关联的客户名称合并到订单数据中
      const ordersWithClientNames = orders.map(order => ({
        ...order,
        items: order.items as unknown,
        clientName: order.clientName || order.client?.name || '未知客户'
      })) as Order[]

      return ordersWithClientNames
    } catch (error) {
      console.error('❌ 获取所有订单失败:', error)
      return []
    }
  }

  /**
   * 获取所有工厂用户（用于数据修复）
   */
  async getAllFactoryUsers(): Promise<FactoryUser[]> {
    try {
      console.log('👥 获取所有工厂用户')

      const users = await prisma.factoryUser.findMany({
        include: {
          factory: {
            select: {
              name: true,
              code: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      console.log('✅ 所有工厂用户获取成功:', users.length, '个')
      return users.map(user => ({
        ...user,
        userType: 'factory_user' as const
      })) as FactoryUser[]
    } catch (error) {
      console.error('❌ 获取所有工厂用户失败:', error)
      return []
    }
  }

  /**
   * 更新订单录单员信息
   */
  async updateOrderRecorderInfo(orderId: string, recorderInfo: { createdBy: string }): Promise<boolean> {
    try {
      console.log('📝 更新订单录单员信息:', orderId, recorderInfo)

      await prisma.order.update({
        where: { id: orderId },
        data: {
          createdBy: recorderInfo.createdBy
        }
      })

      console.log('✅ 订单录单员信息更新成功')
      return true
    } catch (error) {
      console.error('❌ 更新订单录单员信息失败:', error)
      return false
    }
  }

  /**
   * 数据迁移：为现有订单填充 createdByName 字段
   */
  async migrateOrderCreatedByNames(): Promise<boolean> {
    try {
      console.log('🔄 开始迁移订单录单员姓名数据...')

      // 获取所有没有 createdByName 的订单
      const ordersToUpdate = await prisma.order.findMany({
        where: {
          OR: [
            { createdByName: null },
            { createdByName: '' }
          ]
        },
        include: {
          creator: {
            select: {
              name: true,
              username: true
            }
          }
        }
      })

      console.log(`📋 找到 ${ordersToUpdate.length} 个需要更新的订单`)

      let updatedCount = 0
      for (const order of ordersToUpdate) {
        try {
          const createdByName = order.creator?.name || order.creator?.username || '未知用户'

          await prisma.order.update({
            where: { id: order.id },
            data: { createdByName }
          })

          updatedCount++
          console.log(`✅ 订单 ${order.orderNumber || order.id} 录单员姓名已更新为: ${createdByName}`)
        } catch (error) {
          console.error(`❌ 更新订单 ${order.id} 失败:`, error)
        }
      }

      console.log(`✅ 数据迁移完成，共更新 ${updatedCount} 个订单`)
      return true
    } catch (error) {
      console.error('❌ 数据迁移失败:', error)
      return false
    }
  }

  /**
   * 获取工厂订单列表（包含客户名称信息）
   */
  async getOrdersByFactoryId(factoryId: string): Promise<Order[]> {
    try {
      console.log('📋 获取工厂订单:', factoryId)

      const orders = await prisma.order.findMany({
        where: { factoryId },
        include: {
          client: {
            select: {
              name: true
            }
          },
          creator: {
            select: {
              name: true,
              username: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      console.log('✅ 订单获取成功:', orders.length, '条')

      // 将关联的客户名称合并到订单数据中，并解析JSON字段
      const ordersWithClientNames = orders.map(order => {
        // 解析items字段（SQLite中存储为字符串）
        let parsedItems = []
        try {
          if (typeof order.items === 'string') {
            parsedItems = JSON.parse(order.items)
          } else if (Array.isArray(order.items)) {
            parsedItems = order.items
          }
        } catch (error) {
          console.warn('⚠️ 解析订单items失败:', error)
          parsedItems = []
        }

        return {
          ...order,
          items: parsedItems,
          // 如果订单中没有clientName，则使用关联客户的名称
          clientName: order.clientName || order.client?.name || '未知客户'
        }
      }) as Order[]

      return ordersWithClientNames
    } catch (error) {
      console.error('❌ 获取订单失败:', error)
      return []
    }
  }

  /**
   * 获取所有订单列表（用于管理员工具）
   */
  async getAllOrders(): Promise<Order[]> {
    try {
      console.log('📋 获取所有订单（管理员工具）')

      const orders = await prisma.order.findMany({
        include: {
          client: {
            select: {
              name: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      console.log('✅ 所有订单获取成功:', orders.length, '条')

      // 将关联的客户名称合并到订单数据中
      const ordersWithClientNames = orders.map(order => ({
        ...order,
        items: order.items as unknown,
        // 如果订单中没有clientName，则使用关联客户的名称
        clientName: order.clientName || order.client?.name || '未知客户'
      })) as Order[]

      return ordersWithClientNames
    } catch (error) {
      console.error('❌ 获取所有订单失败:', error)
      return []
    }
  }

  /**
   * 根据客户ID获取订单列表
   */
  async getOrdersByClientId(clientId: string): Promise<Order[]> {
    try {
      console.log('📋 获取客户订单:', clientId)

      const orders = await prisma.order.findMany({
        where: { clientId },
        orderBy: { createdAt: 'desc' }
      })

      console.log('✅ 客户订单获取成功:', orders.length, '条')

      // 解析items字段（SQLite中存储为字符串）并添加计算字段
      return orders.map(order => {
        let parsedItems = []
        try {
          if (typeof order.items === 'string') {
            parsedItems = JSON.parse(order.items)
          } else if (Array.isArray(order.items)) {
            parsedItems = order.items
          }
        } catch (error) {
          console.warn('⚠️ 解析订单items失败:', error)
          parsedItems = []
        }

        return {
          ...order,
          items: parsedItems,
          // 添加计算的录单员姓名
          createdByName: order.creator?.name || order.creator?.username || '未知用户'
        }
      }) as Order[]
    } catch (error) {
      console.error('❌ 获取客户订单失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取单个订单
   */
  async getOrderById(orderId: string): Promise<Order | null> {
    try {
      console.log('📦 获取订单:', orderId)

      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          client: {
            select: {
              name: true
            }
          },
          creator: {
            select: {
              name: true,
              username: true
            }
          }
        }
      })

      if (order) {
        console.log('✅ 订单获取成功:', order.orderNumber || order.id)

        // 解析items字段（SQLite中存储为字符串）
        let parsedItems = []
        try {
          if (typeof order.items === 'string') {
            parsedItems = JSON.parse(order.items)
          } else if (Array.isArray(order.items)) {
            parsedItems = order.items
          }
        } catch (error) {
          console.warn('⚠️ 解析订单items失败:', error)
          parsedItems = []
        }

        // 合并客户名称和录单员信息
        const orderWithClientName = {
          ...order,
          items: parsedItems,
          clientName: order.clientName || order.client?.name || '未知客户',
          createdByName: order.createdByName || order.creator?.name || order.creator?.username || '未知用户'
        } as Order
        return orderWithClientName
      } else {
        console.log('❌ 订单不存在:', orderId)
        return null
      }
    } catch (error) {
      console.error('❌ 获取订单失败:', error)
      return null
    }
  }

  /**
   * 工厂用户认证
   */
  async authenticateFactoryUser(
    username: string,
    password: string,
    clientInfo?: {
      ipAddress?: string
      userAgent?: string
      deviceInfo?: string
    }
  ): Promise<FactoryUser | null> {
    try {
      console.log('🔐 用户认证:', username)

      const user = await prisma.factoryUser.findFirst({
        where: {
          username,
          isActive: true
        },
        include: {
          factory: true
        }
      })

      if (!user) {
        console.log('❌ 用户不存在或已禁用')
        return null
      }

      const isValidPassword = await bcrypt.compare(password, user.passwordHash)
      if (!isValidPassword) {
        console.log('❌ 密码错误')
        return null
      }

      console.log('✅ 认证成功:', user.name)

      // 🔧 新增：检查工厂状态和使用期限
      const factoryStatusCheck = await this.checkFactoryStatus(user.factory)
      if (!factoryStatusCheck.canLogin) {
        console.log('❌ 工厂状态检查失败:', factoryStatusCheck.reason)

        // 记录登录失败
        await this.recordLogin({
          userId: user.id,
          userType: 'factory_user',
          username: user.username,
          userName: user.name,
          factoryId: user.factoryId,
          factoryName: user.factory?.name,
          loginStatus: 'blocked',
          failReason: factoryStatusCheck.reason,
          ipAddress: clientInfo?.ipAddress,
          userAgent: clientInfo?.userAgent,
          deviceInfo: clientInfo?.deviceInfo
        })

        // 返回特殊错误对象，包含详细信息和错误类型
        const error = new Error(factoryStatusCheck.reason) as any
        error.code = factoryStatusCheck.status === 'suspended' ? 'FACTORY_SUSPENDED' :
                    factoryStatusCheck.status === 'expired' ? 'FACTORY_EXPIRED' :
                    factoryStatusCheck.status === 'inactive' ? 'FACTORY_INACTIVE' : 'FACTORY_ERROR'
        error.statusCode = factoryStatusCheck.status === 'suspended' ? 403 :
                          factoryStatusCheck.status === 'expired' ? 403 :
                          factoryStatusCheck.status === 'inactive' ? 403 : 500
        throw error
      }

      // 🔧 新增：检查是否有其他活跃会话
      const activeSession = await this.checkActiveSession(user.id, 'factory_user')

      if (activeSession.hasActiveSession) {
        console.log('⚠️ 检测到其他地方登录，将踢出旧会话')
        console.log('📍 旧会话信息:', {
          sessionId: activeSession.activeSessionId,
          lastLoginTime: activeSession.lastLoginTime,
          ipAddress: activeSession.ipAddress
        })
      }

      // 记录登录成功
      const loginRecord = await this.recordLogin({
        userId: user.id,
        userType: 'factory_user',
        username: user.username,
        userName: user.name,
        factoryId: user.factoryId,
        factoryName: user.factory?.name,
        loginStatus: 'success',
        // 🔧 修复：传递客户端信息
        ipAddress: clientInfo?.ipAddress,
        userAgent: clientInfo?.userAgent,
        deviceInfo: clientInfo?.deviceInfo
      })

      // 🔧 新增：使旧会话失效
      if (activeSession.hasActiveSession && loginRecord?.sessionId) {
        await this.invalidateOldSessions(user.id, 'factory_user', loginRecord.sessionId)
      }

      // 检查是否为首次登录
      const isFirstLogin = !user.firstLoginAt
      const now = new Date()

      // 更新用户登录时间
      const updateUserData: any = { lastLoginAt: now }
      if (isFirstLogin) {
        updateUserData.firstLoginAt = now
      }

      await prisma.factoryUser.update({
        where: { id: user.id },
        data: updateUserData
      })

      // 如果是首次登录，同时更新工厂的首次登录时间并计算使用期限
      if (isFirstLogin) {
        const subscriptionData = this.calculateSubscriptionPeriod(user.factory, now)
        await prisma.factory.update({
          where: { id: user.factoryId },
          data: {
            firstLoginAt: now,
            ...subscriptionData
          }
        })
        console.log('✅ 记录工厂首次登录时间:', user.factory?.name)
        console.log('📅 设置使用期限:', subscriptionData)
      }

      // 🔧 新增：在返回的用户对象中包含会话ID，并解析permissions
      const userWithSession = {
        ...user,
        permissions: JSON.parse(user.permissions || '[]'),
        sessionId: loginRecord?.sessionId
      }

      return {
        ...userWithSession,
        userType: 'factory_user' as const
      } as FactoryUser
    } catch (error) {
      console.error('❌ 认证过程失败:', error)

      // 检查是否是工厂状态相关的异常，如果是则重新抛出
      if (error && typeof error === 'object' && 'code' in error) {
        const errorCode = (error as any).code
        if (errorCode === 'FACTORY_SUSPENDED' ||
            errorCode === 'FACTORY_EXPIRED' ||
            errorCode === 'FACTORY_INACTIVE' ||
            errorCode === 'FACTORY_ERROR') {
          console.log('🔥 重新抛出工厂状态异常:', errorCode)
          throw error
        }
      }

      // 记录登录失败
      if (username) {
        await this.recordLogin({
          userId: '',
          userType: 'factory_user',
          username,
          userName: '',
          loginStatus: 'failed',
          failReason: error instanceof Error ? error.message : '认证失败'
        })
      }

      return null
    }
  }

  /**
   * 总部管理员认证
   */
  async authenticateAdmin(
    username: string,
    password: string,
    clientInfo?: {
      ipAddress?: string
      userAgent?: string
      deviceInfo?: string
    }
  ): Promise<any | null> {
    try {
      console.log('🔐 管理员认证:', username)

      const admin = await prisma.admin.findFirst({
        where: { 
          username,
          isActive: true
        }
      })

      if (!admin) {
        console.log('❌ 管理员不存在或已禁用')
        return null
      }

      const isValidPassword = await bcrypt.compare(password, admin.passwordHash)
      if (!isValidPassword) {
        console.log('❌ 密码错误')

        // 记录登录失败
        await this.recordLogin({
          userId: admin.id,
          userType: 'admin',
          username: admin.username,
          userName: admin.name,
          loginStatus: 'failed',
          failReason: '密码错误',
          // 🔧 修复：传递客户端信息
          ipAddress: clientInfo?.ipAddress,
          userAgent: clientInfo?.userAgent,
          deviceInfo: clientInfo?.deviceInfo
        })

        return null
      }

      console.log('✅ 管理员认证成功:', admin.name)

      // 🔧 新增：检查是否有其他活跃会话
      const activeSession = await this.checkActiveSession(admin.id, 'admin')

      if (activeSession.hasActiveSession) {
        console.log('⚠️ 检测到其他地方登录，将踢出旧会话')
        console.log('📍 旧会话信息:', {
          sessionId: activeSession.activeSessionId,
          lastLoginTime: activeSession.lastLoginTime,
          ipAddress: activeSession.ipAddress
        })
      }

      // 记录登录成功
      const loginRecord = await this.recordLogin({
        userId: admin.id,
        userType: 'admin',
        username: admin.username,
        userName: admin.name,
        loginStatus: 'success',
        // 🔧 修复：传递客户端信息
        ipAddress: clientInfo?.ipAddress,
        userAgent: clientInfo?.userAgent,
        deviceInfo: clientInfo?.deviceInfo
      })

      // 🔧 新增：使旧会话失效
      if (activeSession.hasActiveSession && loginRecord?.sessionId) {
        await this.invalidateOldSessions(admin.id, 'admin', loginRecord.sessionId)
      }

      // 更新最后登录时间
      await prisma.admin.update({
        where: { id: admin.id },
        data: { lastLoginAt: new Date() }
      })

      // 🔧 新增：在返回的管理员对象中包含会话ID
      const adminWithSession = {
        ...admin,
        sessionId: loginRecord?.sessionId
      }

      return adminWithSession
    } catch (error) {
      console.error('❌ 管理员认证过程失败:', error)

      // 记录登录失败
      if (username) {
        await this.recordLogin({
          userId: '',
          userType: 'admin',
          username,
          userName: '',
          loginStatus: 'failed',
          failReason: error instanceof Error ? error.message : '认证失败'
        })
      }

      return null
    }
  }

  /**
   * 获取所有工厂
   */
  async getFactories(options?: {
    status?: string
    limit?: number
    offset?: number
  }): Promise<Factory[]> {
    try {
      console.log('🏭 获取工厂列表', options)

      // 构建查询条件
      const where: any = {}
      if (options?.status) {
        where.status = options.status
      }

      const factories = await prisma.factory.findMany({
        where,
        include: {
          users: {
            where: { role: 'owner' },
            select: {
              name: true,
              username: true,
              createdAt: true
            },
            orderBy: { createdAt: 'asc' } // 按创建时间排序，最早的在前
          }
        },
        orderBy: { createdAt: 'desc' },
        take: options?.limit,
        skip: options?.offset
      })

      // 添加管理员姓名到工厂数据
      const factoriesWithOwner = factories.map(factory => {
        let ownerName = null

        if (factory.users.length > 0) {
          // 优先选择用户名与工厂编码匹配的管理员（忽略大小写）
          const matchingOwner = factory.users.find(user =>
            user.username.toLowerCase() === factory.code.toLowerCase()
          )
          if (matchingOwner) {
            ownerName = matchingOwner.name
          } else {
            // 如果没有匹配的，选择最早创建的管理员
            ownerName = factory.users[0]?.name
          }
        }

        console.log(`🏭 工厂 ${factory.name} 的管理员:`, ownerName)
        console.log(`🔍 工厂 ${factory.name} 的用户数据:`, factory.users.map(u => `${u.name}(${u.username})`))
        return {
          ...factory,
          ownerName
        }
      })

      console.log('✅ 工厂获取成功:', factoriesWithOwner.length, '个')
      return factoriesWithOwner as Factory[]
    } catch (error) {
      console.error('❌ 获取工厂失败:', error)
      return []
    }
  }

  /**
   * 更新工厂信息
   */
  async updateFactory(factoryId: string, factoryData: Partial<Factory>): Promise<Factory> {
    try {
      console.log('🔧 更新工厂信息:', factoryId, factoryData)

      // 构建更新数据对象，只包含提供的字段
      const updateData: any = {
        updatedAt: new Date()
      }

      // 基本信息字段
      if (factoryData.name !== undefined) updateData.name = factoryData.name
      if (factoryData.code !== undefined) updateData.code = factoryData.code
      if (factoryData.address !== undefined) updateData.address = factoryData.address
      if (factoryData.phone !== undefined) updateData.phone = factoryData.phone
      if (factoryData.email !== undefined) updateData.email = factoryData.email
      if (factoryData.contactPhone !== undefined) updateData.contactPhone = factoryData.contactPhone

      // 状态相关字段
      if (factoryData.status !== undefined) updateData.status = factoryData.status

      // 订阅相关字段
      if (factoryData.subscriptionType !== undefined) updateData.subscriptionType = factoryData.subscriptionType
      if (factoryData.subscriptionStart !== undefined) updateData.subscriptionStart = factoryData.subscriptionStart
      if (factoryData.subscriptionEnd !== undefined) updateData.subscriptionEnd = factoryData.subscriptionEnd
      if (factoryData.isPermanent !== undefined) updateData.isPermanent = factoryData.isPermanent
      if (factoryData.firstLoginAt !== undefined) updateData.firstLoginAt = factoryData.firstLoginAt

      // 暂停相关字段
      if (factoryData.suspendedAt !== undefined) updateData.suspendedAt = factoryData.suspendedAt
      if (factoryData.suspendedReason !== undefined) updateData.suspendedReason = factoryData.suspendedReason
      if (factoryData.suspendedBy !== undefined) updateData.suspendedBy = factoryData.suspendedBy

      // 重新激活相关字段
      if (factoryData.reactivatedAt !== undefined) updateData.reactivatedAt = factoryData.reactivatedAt
      if (factoryData.reactivatedBy !== undefined) updateData.reactivatedBy = factoryData.reactivatedBy

      // 其他可能的字段
      if (factoryData.lastStatusCheck !== undefined) updateData.lastStatusCheck = factoryData.lastStatusCheck
      if (factoryData.autoSuspendEnabled !== undefined) updateData.autoSuspendEnabled = factoryData.autoSuspendEnabled

      // BigInt字段处理
      if (factoryData.totalSuspendedMs !== undefined) {
        updateData.totalSuspendedMs = typeof factoryData.totalSuspendedMs === 'bigint'
          ? factoryData.totalSuspendedMs
          : BigInt(factoryData.totalSuspendedMs || 0)
      }

      const updatedFactory = await prisma.factory.update({
        where: { id: factoryId },
        data: updateData
      })

      console.log('✅ 工厂信息更新成功:', updatedFactory.name)

      // 处理BigInt序列化
      const result = {
        ...updatedFactory,
        totalSuspendedMs: updatedFactory.totalSuspendedMs ? Number(updatedFactory.totalSuspendedMs) : 0
      } as Factory

      return result

    } catch (error) {
      console.error('❌ 更新工厂信息失败:', error)
      throw error
    }
  }

  /**
   * 更新工厂管理员信息
   */
  async updateFactoryAdmin(factoryId: string, adminData: { name?: string, phone?: string, email?: string }): Promise<void> {
    try {
      console.log('🔧 更新工厂管理员信息:', factoryId, adminData)

      // 查找工厂的管理员（owner角色）
      const factory = await prisma.factory.findUnique({
        where: { id: factoryId },
        include: {
          users: {
            where: { role: 'owner' },
            orderBy: { createdAt: 'asc' }
          }
        }
      })

      if (!factory) {
        throw new Error('工厂不存在')
      }

      if (factory.users.length === 0) {
        throw new Error('工厂没有管理员')
      }

      // 优先更新用户名与工厂编码匹配的管理员（忽略大小写）
      let targetAdmin = factory.users.find(user =>
        user.username.toLowerCase() === factory.code.toLowerCase()
      )
      if (!targetAdmin) {
        // 如果没有匹配的，更新第一个管理员
        targetAdmin = factory.users[0]
      }

      // 更新管理员姓名（在FactoryUser表中）
      if (adminData.name) {
        await prisma.factoryUser.update({
          where: { id: targetAdmin.id },
          data: {
            name: adminData.name,
            updatedAt: new Date()
          }
        })
      }

      // 更新管理员联系信息（在Factory表中）
      if (adminData.phone || adminData.email) {
        await prisma.factory.update({
          where: { id: factoryId },
          data: {
            phone: adminData.phone || factory.phone,
            email: adminData.email || factory.email,
            updatedAt: new Date()
          }
        })
      }

      console.log('✅ 工厂管理员信息更新成功:', targetAdmin.username)

    } catch (error) {
      console.error('❌ 更新工厂管理员信息失败:', error)
      throw error
    }
  }

  /**
   * 获取所有工厂（管理员端使用）
   */
  async getAllFactories(): Promise<Factory[]> {
    return this.getFactories()
  }



  /**
   * 创建客户
   */
  async createClient(clientData: unknown): Promise<Client | null> {
    try {
      console.log('👤 创建客户:', {
        name: clientData.name,
        phone: clientData.phone,
        factoryId: clientData.factoryId
      })

      // 过滤掉不存在的字段，只保留数据库schema中存在的字段
      const filteredData = {
        factoryId: clientData.factoryId,
        name: clientData.name,
        phone: clientData.phone,
        email: clientData.email || null,
        company: clientData.company || null,
        address: clientData.address || null,
        referrerId: clientData.referrerId || null,
        referrerName: clientData.referrerName || null,
        status: ClientStatus.active
        // 不传递任何统计字段，让数据库使用默认值
      }

      const client = await prisma.client.create({
        data: filteredData
      })

      console.log('✅ 客户创建成功:', {
        id: client.id,
        name: client.name,
        phone: client.phone
      })
      return client as Client
    } catch (error) {
      console.error('❌ 创建客户失败:', error)

      // 检查是否是唯一性约束违反错误
      if (error.code === 'P2002' && error.meta?.target?.includes('phone')) {
        console.log('📞 电话号码重复错误:', {
          phone: clientData.phone,
          factoryId: clientData.factoryId,
          constraint: error.meta?.target
        })
        // 重新抛出错误，让上层处理
        throw error
      }

      // 其他错误返回null
      return null
    }
  }

  /**
   * 删除客户
   */
  async deleteClient(clientId: string): Promise<boolean> {
    try {
      console.log('👤 删除客户:', clientId)

      // 检查客户是否存在
      const client = await prisma.client.findUnique({
        where: { id: clientId }
      })

      if (!client) {
        console.log('❌ 客户不存在')
        return false
      }

      // 检查客户是否有关联的订单
      const orders = await prisma.order.findMany({
        where: { clientId }
      })

      if (orders.length > 0) {
        console.log('❌ 客户有关联订单，无法删除')
        throw new Error('该客户有关联订单，无法删除。请先删除相关订单。')
      }

      // 🔄 自动处理推荐关系
      const referredClients = await prisma.client.findMany({
        where: { referrerId: clientId }
      })

      if (referredClients.length > 0) {
        console.log(`🔗 发现 ${referredClients.length} 个被推荐客户，自动解除推荐关系`)

        // 解除推荐关系：将被推荐客户的推荐人信息清空
        await prisma.client.updateMany({
          where: { referrerId: clientId },
          data: {
            referrerId: null,
            referrerName: null
          }
        })

        console.log(`✅ 已自动解除 ${referredClients.length} 个客户的推荐关系`)

        // 记录解除的推荐关系（用于日志）
        for (const referredClient of referredClients) {
          console.log(`🔗 解除推荐关系: ${client.name} -> ${referredClient.name}`)
        }
      }

      // 🔄 如果该客户也是被推荐的，需要更新推荐人的统计
      if (client.referrerId) {
        console.log(`🔄 更新推荐人 ${client.referrerName} 的统计信息`)
        await this.updateClientStatistics(client.referrerId)
      }

      // 删除客户
      await prisma.client.delete({
        where: { id: clientId }
      })

      console.log('✅ 客户删除成功')
      return true
    } catch (error) {
      console.error('❌ 删除客户失败:', error)
      if ((error as Error).message && (error as Error).message.includes('关联订单')) {
        throw error
      }
      return false
    }
  }

  /**
   * 删除订单
   */
  async deleteOrder(orderId: string): Promise<boolean> {
    try {
      console.log('📦 删除订单:', orderId)

      // 检查订单是否存在
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          client: true
        }
      })

      if (!order) {
        console.log('❌ 订单不存在')
        return false
      }

      // 检查订单状态，已完成的订单可能需要特殊处理
      if (order.status === 'completed') {
        console.log('⚠️ 删除已完成的订单')
        // 这里可以添加额外的业务逻辑，比如记录删除日志等
      }

      // 删除订单
      await prisma.order.delete({
        where: { id: orderId }
      })

      console.log('✅ 订单删除成功')

      // 如果订单有关联客户，更新客户统计信息
      if (order.clientId && order.clientId !== 'new') {
        console.log('🔄 更新客户统计信息:', order.clientId)
        await this.updateClientStatistics(order.clientId)
      }

      return true
    } catch (error) {
      console.error('❌ 删除订单失败:', error)
      return false
    }
  }

  /**
   * 获取工厂的客户列表
   */
  async getClientsByFactoryId(factoryId: string): Promise<Client[]> {
    try {
      console.log('👥 获取工厂客户:', factoryId)

      const clients = await prisma.client.findMany({
        where: { factoryId },
        orderBy: { createdAt: 'desc' }
      })

      console.log('✅ 客户获取成功:', clients.length, '个')

      // 转换Decimal字段为数字
      const convertedClients = clients.map(client => ({
        ...client,
        totalAmount: client.totalAmount ? Number(client.totalAmount) : 0,
        paidAmount: client.paidAmount ? Number(client.paidAmount) : 0,
        unpaidAmount: client.unpaidAmount ? Number(client.unpaidAmount) : 0,
        overdueAmount: client.overdueAmount ? Number(client.overdueAmount) : 0,
        referralReward: client.referralReward ? Number(client.referralReward) : 0,
        availableReward: client.availableReward ? Number(client.availableReward) : 0,
        usedReward: client.usedReward ? Number(client.usedReward) : 0,
        pendingReward: client.pendingReward ? Number(client.pendingReward) : 0
      }))

      return convertedClients as Client[]
    } catch (error) {
      console.error('❌ 获取客户失败:', error)
      return []
    }
  }

  /**
   * 获取所有管理员
   */
  async getAllAdmins(): Promise<unknown[]> {
    try {
      console.log('👑 获取所有管理员')

      const admins = await prisma.admin.findMany({
        orderBy: { createdAt: 'desc' }
      })

      console.log('✅ 管理员获取成功:', admins.length, '个')
      return admins
    } catch (error) {
      console.error('❌ 获取管理员失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取管理员
   */
  async getAdminById(adminId: string): Promise<any | null> {
    try {
      console.log('👑 获取管理员:', adminId)

      const admin = await prisma.admin.findUnique({
        where: { id: adminId }
      })

      if (admin) {
        console.log('✅ 管理员获取成功:', admin.name)
      } else {
        console.log('❌ 管理员不存在:', adminId)
      }

      return admin
    } catch (error) {
      console.error('❌ 获取管理员失败:', error)
      return null
    }
  }

  /**
   * 根据ID获取工厂
   */
  async getFactoryById(factoryId: string): Promise<Factory | null> {
    try {
      console.log('🏭 获取工厂:', factoryId)

      const factory = await prisma.factory.findUnique({
        where: { id: factoryId }
      })

      if (factory) {
        console.log('✅ 工厂获取成功:', factory.name)
        // 处理BigInt序列化
        const result = {
          ...factory,
          totalSuspendedMs: factory.totalSuspendedMs ? Number(factory.totalSuspendedMs) : 0
        } as Factory
        return result
      } else {
        console.log('❌ 工厂不存在:', factoryId)
        return null
      }
    } catch (error) {
      console.error('❌ 获取工厂失败:', error)
      return null
    }
  }

  /**
   * 更新客户信息
   */
  async updateClient(clientId: string, updateData: unknown): Promise<Client | null> {
    try {
      console.log('👤 更新客户信息:', clientId, updateData)

      const client = await prisma.client.update({
        where: { id: clientId },
        data: updateData
      })

      console.log('✅ 客户信息更新成功:', client.name)
      return client as Client
    } catch (error) {
      console.error('❌ 更新客户信息失败:', error)
      return null
    }
  }

  /**
   * 获取所有客户
   */
  async getAllClients(): Promise<Client[]> {
    try {
      console.log('👥 获取所有客户')

      const clients = await prisma.client.findMany({
        orderBy: { createdAt: 'desc' }
      })

      console.log('✅ 所有客户获取成功:', clients.length, '个')

      // 转换Decimal字段为数字
      const convertedClients = clients.map(client => ({
        ...client,
        totalAmount: client.totalAmount ? Number(client.totalAmount) : 0,
        paidAmount: client.paidAmount ? Number(client.paidAmount) : 0,
        unpaidAmount: client.unpaidAmount ? Number(client.unpaidAmount) : 0,
        overdueAmount: client.overdueAmount ? Number(client.overdueAmount) : 0,
        referralReward: client.referralReward ? Number(client.referralReward) : 0,
        availableReward: client.availableReward ? Number(client.availableReward) : 0,
        usedReward: client.usedReward ? Number(client.usedReward) : 0,
        pendingReward: client.pendingReward ? Number(client.pendingReward) : 0
      }))

      return convertedClients as Client[]
    } catch (error) {
      console.error('❌ 获取所有客户失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取客户
   */
  async getClientById(clientId: string): Promise<Client | null> {
    try {
      console.log('👤 获取客户:', clientId)

      const client = await prisma.client.findUnique({
        where: { id: clientId }
      })

      if (client) {
        console.log('✅ 客户获取成功:', client.name)

        // 转换Decimal字段为数字
        const convertedClient = {
          ...client,
          totalAmount: client.totalAmount ? Number(client.totalAmount) : 0,
          paidAmount: client.paidAmount ? Number(client.paidAmount) : 0,
          unpaidAmount: client.unpaidAmount ? Number(client.unpaidAmount) : 0,
          overdueAmount: client.overdueAmount ? Number(client.overdueAmount) : 0,
          referralReward: client.referralReward ? Number(client.referralReward) : 0,
          availableReward: client.availableReward ? Number(client.availableReward) : 0,
          usedReward: client.usedReward ? Number(client.usedReward) : 0,
          pendingReward: client.pendingReward ? Number(client.pendingReward) : 0
        }

        return convertedClient as Client
      } else {
        console.log('❌ 客户不存在:', clientId)
        return null
      }
    } catch (error) {
      console.error('❌ 获取客户失败:', error)
      return null
    }
  }

  /**
   * 更新客户统计信息
   */
  async updateClientStatistics(clientId: string, skipReferralRewards: boolean = false): Promise<void> {
    try {
      console.log('📊 更新客户统计:', clientId)

      // 获取客户信息
      const client = await prisma.client.findUnique({
        where: { id: clientId }
      })

      if (!client) {
        console.error('❌ 客户不存在:', clientId)
        return
      }

      // 获取客户的所有订单
      const orders = await prisma.order.findMany({
        where: { clientId }
      })

      // 计算统计数据
      const totalOrders = orders.length
      const totalAmount = orders.reduce((sum, order) => sum + Number(order.totalAmount), 0)

      // 从订单表直接计算已付款金额（修复数据不一致问题）
      const paidAmount = orders.reduce((sum, order) => sum + Number(order.paidAmount || 0), 0)
      const unpaidAmount = totalAmount - paidAmount

      console.log(`📊 客户统计计算结果:`, {
        clientId,
        totalOrders,
        totalAmount,
        paidAmount,
        unpaidAmount
      })

      // 注意：客户统计信息是计算字段，不存储在数据库中
      // 只更新客户的最后更新时间
      await prisma.client.update({
        where: { id: clientId },
        data: {
          updatedAt: new Date()
        }
      })

      // 🎁 处理推荐奖励更新（可选跳过，避免重复计算）
      if (!skipReferralRewards) {
        await this.updateReferralRewards(clientId, client.factoryId)
      }

      console.log('✅ 客户统计更新完成')
    } catch (error) {
      console.error('❌ 更新客户统计失败:', error)
    }
  }

  /**
   * 更新推荐奖励
   */
  async updateReferralRewards(clientId: string, factoryId: string): Promise<void> {
    try {
      console.log('🎁 开始更新推荐奖励:', clientId)

      // 获取客户信息
      const client = await prisma.client.findUnique({
        where: { id: clientId }
      })

      if (!client || !client.referrerId) {
        console.log('ℹ️ 客户无推荐人，跳过奖励计算')
        return
      }

      console.log(`🔗 客户 ${client.name} 的推荐人: ${client.referrerName} (${client.referrerId})`)

      // 获取被推荐客户的所有订单
      const referredOrders = await prisma.order.findMany({
        where: { clientId: clientId }
      })

      if (referredOrders.length === 0) {
        console.log('ℹ️ 被推荐客户无订单，跳过奖励计算')
        return
      }

      // ✅ 修复：重新计算推荐人的总奖励，而不是累加单个客户的奖励
      const totalReward = await this.calculateReferrerTotalReward(client.referrerId, factoryId)

      console.log('💰 推荐奖励计算结果:', {
        推荐人: client.referrerName,
        被推荐客户: client.name,
        订单数量: referredOrders.length,
        推荐人总奖励: totalReward
      })

      // 🆕 使用按比例奖励计算
      const { calculateProportionalReward } = await import('@/lib/utils/reward-calculator')
      const rewardStatus = await calculateProportionalReward(client.referrerId, factoryId)

      // 注意：奖励状态是计算字段，不存储在数据库中
      // 只更新推荐人的最后更新时间
      await prisma.client.update({
        where: { id: client.referrerId },
        data: {
          updatedAt: new Date()
        }
      })

      console.log('✅ 推荐人奖励更新完成（按比例模式）:', {
        推荐人: client.referrerName,
        总奖励: rewardStatus.totalReward.toFixed(2),
        可用奖励: rewardStatus.availableReward.toFixed(2),
        待结算奖励: rewardStatus.pendingReward.toFixed(2),
        结清客户: `${rewardStatus.settledClients}/${rewardStatus.totalClients}`
      })

      // 旧的日志已被上面的按比例模式日志替代，删除重复日志

      console.log('✅ 推荐奖励更新完成')
    } catch (error) {
      console.error('❌ 更新推荐奖励失败:', error)
    }
  }

  /**
   * 更新推荐人的奖励余额（累加模式）
   */
  async updateReferrerRewardBalance(referrerId: string, additionalReward: number, factoryId: string): Promise<void> {
    try {
      console.log('💰 更新推荐人奖励余额:', { referrerId, additionalReward })

      // 获取推荐人当前信息
      const referrer = await prisma.client.findUnique({
        where: { id: referrerId }
      })

      if (!referrer) {
        console.error('❌ 推荐人不存在:', referrerId)
        return
      }

      // 计算新的总奖励（累加）
      const currentReward = Number(referrer.referralReward || 0)
      const newTotalReward = currentReward + additionalReward

      // 获取推荐人的所有订单，用于判断奖励是否可用
      const referrerOrders = await prisma.order.findMany({
        where: { clientId: referrerId }
      })

      // 检查奖励是否可以使用（根据工厂设置）
      const canUse = canUseReward(referrerId, factoryId, referrerOrders.map(order => ({
        ...order,
        items: order.items as unknown
      })) as Order[])

      // 更新推荐人的奖励信息
      await prisma.client.update({
        where: { id: referrerId },
        data: {
          referralReward: newTotalReward,
          // 如果可以使用奖励，则设置为可用余额
          availableReward: canUse ? newTotalReward : 0,
          // 如果不能使用，则设置为待结算余额
          pendingReward: canUse ? 0 : newTotalReward,
          updatedAt: new Date()
        }
      })

      console.log('✅ 推荐人奖励余额更新完成:', {
        推荐人: referrer.name,
        原奖励: currentReward,
        新增奖励: additionalReward,
        总奖励: newTotalReward,
        可用奖励: canUse ? newTotalReward : 0,
        待结算奖励: canUse ? 0 : newTotalReward,
        可以使用: canUse
      })
    } catch (error) {
      console.error('❌ 更新推荐人奖励余额失败:', error)
    }
  }

  /**
   * 批量更新工厂所有客户的推荐奖励
   */
  async updateAllReferralRewards(factoryId: string): Promise<void> {
    try {
      console.log('🔄 开始批量更新推荐奖励:', factoryId)

      // 获取工厂所有客户
      const clients = await prisma.client.findMany({
        where: { factoryId }
      })

      console.log(`📋 找到 ${clients.length} 个客户`)

      // 第一步：重置所有客户的推荐奖励字段
      console.log('🧹 第一步：重置所有客户的推荐奖励字段...')
      await prisma.client.updateMany({
        where: { factoryId },
        data: {
          referralReward: 0,
          availableReward: 0,
          pendingReward: 0
        }
      })

      // 第二步：找出所有有推荐人的客户
      const referredClients = clients.filter(client => client.referrerId)
      console.log(`🔗 其中 ${referredClients.length} 个客户有推荐人`)

      // 第三步：为每个推荐人重新计算总奖励
      const referrers = [...new Set(referredClients.map(c => c.referrerId).filter(Boolean))]
      console.log(`👥 找到 ${referrers.length} 个推荐人`)

      for (const referrerId of referrers) {
        const referrer = clients.find(c => c.id === referrerId)
        if (referrer) {
          console.log(`🎁 处理推荐人: ${referrer.name}`)

          // 计算该推荐人的总奖励
          const totalReward = await this.calculateReferrerTotalReward(referrerId!, factoryId)

          if (totalReward > 0) {
            // 获取推荐人的订单，判断奖励是否可用
            const referrerOrders = await prisma.order.findMany({
              where: { clientId: referrerId! }
            })

            const canUse = canUseReward(referrerId!, factoryId, referrerOrders.map(order => ({
              ...order,
              items: order.items as unknown
            })) as Order[])

            // 直接设置总奖励（不是累加）
            await prisma.client.update({
              where: { id: referrerId! },
              data: {
                referralReward: totalReward,
                availableReward: canUse ? totalReward : 0,
                pendingReward: canUse ? 0 : totalReward,
                updatedAt: new Date()
              }
            })

            console.log(`✅ 推荐人 ${referrer.name} 总奖励: ${totalReward}元 (${canUse ? '可用' : '待结算'})`)
          }
        }
      }

      // 第四步：更新推荐数量统计
      console.log('📊 第四步：更新推荐数量统计...')
      for (const client of clients) {
        const referralCount = clients.filter(c => c.referrerId === client.id).length
        if (referralCount > 0) {
          await prisma.client.update({
            where: { id: client.id },
            data: { referralCount }
          })
          console.log(`📈 客户 ${client.name} 推荐了 ${referralCount} 个客户`)
        }
      }

      console.log('✅ 批量推荐奖励更新完成')
    } catch (error) {
      console.error('❌ 批量更新推荐奖励失败:', error)
    }
  }

  /**
   * 计算推荐人的总奖励（所有被推荐客户的奖励总和）
   */
  async calculateReferrerTotalReward(referrerId: string, factoryId: string): Promise<number> {
    try {
      console.log('🧮 计算推荐人总奖励:', referrerId)

      // 获取所有被该推荐人推荐的客户
      const referredClients = await prisma.client.findMany({
        where: {
          referrerId: referrerId,
          factoryId: factoryId
        }
      })

      console.log(`📋 推荐人推荐了 ${referredClients.length} 个客户`)

      let totalReward = 0

      // 为每个被推荐的客户计算奖励
      for (const referredClient of referredClients) {
        // 获取被推荐客户的所有订单
        const clientOrders = await prisma.order.findMany({
          where: { clientId: referredClient.id }
        })

        if (clientOrders.length > 0) {
          // 计算该客户产生的推荐奖励
          const rewardResult = calculateTotalReferralReward(clientOrders.map(order => ({
            ...order,
            items: order.items as unknown
          })) as Order[], factoryId)
          totalReward += rewardResult.totalReward

          console.log(`💰 客户 ${referredClient.name} 产生奖励: ${rewardResult.totalReward}元`)
        }
      }

      console.log(`✅ 推荐人总奖励计算完成: ${totalReward}元`)
      return totalReward
    } catch (error) {
      console.error('❌ 计算推荐人总奖励失败:', error)
      return 0
    }
  }

  /**
   * 创建工厂和管理员
   */
  async createFactoryWithAdmin(
    factoryData: Omit<Factory, 'id' | 'createdAt' | 'updatedAt'>,
    adminData: Omit<FactoryUser, 'id' | 'factoryId' | 'createdAt' | 'updatedAt'>,
    plainPassword: string
  ) {
    try {
      console.log('🏭 创建工厂和管理员:', factoryData.name)

      // 创建工厂 - 只使用数据库 schema 中存在的字段
      const factory = await prisma.factory.create({
        data: {
          name: factoryData.name,
          code: factoryData.code,
          address: factoryData.address,
          phone: factoryData.phone, // 使用正确的字段名
          email: factoryData.email,
          status: factoryData.status === 'active' ? FactoryStatus.active :
                  factoryData.status === 'inactive' ? FactoryStatus.inactive :
                  factoryData.status === 'suspended' ? FactoryStatus.suspended : FactoryStatus.active
        }
      })

      // 创建管理员用户
      const hashedPassword = await bcrypt.hash(plainPassword, 12)

      // 处理permissions字段 - 确保它是字符串格式
      let permissionsStr: string
      if (Array.isArray(adminData.permissions)) {
        permissionsStr = JSON.stringify(adminData.permissions)
      } else if (typeof adminData.permissions === 'string') {
        permissionsStr = adminData.permissions
      } else {
        permissionsStr = JSON.stringify(['all'])
      }

      const admin = await prisma.factoryUser.create({
        data: {
          factoryId: factory.id,
          username: adminData.username,
          passwordHash: hashedPassword,
          name: adminData.name,
          role: adminData.role || 'owner',
          permissions: permissionsStr,
          isActive: true
        }
      })

      console.log('✅ 工厂和管理员创建成功')

      // 处理BigInt序列化
      const factoryResult = {
        ...factory,
        totalSuspendedMs: factory.totalSuspendedMs ? Number(factory.totalSuspendedMs) : 0
      }

      return {
        factory: factoryResult,
        admin: {
          ...admin,
          permissions: JSON.parse(admin.permissions || '[]')
        }
      }
    } catch (error) {
      console.error('❌ 创建工厂和管理员失败:', error)
      throw error
    }
  }

  /**
   * 检查用户名是否存在（检查所有用户类型）
   */
  async checkUsernameExists(username: string): Promise<boolean> {
    try {
      // 检查工厂用户表
      const factoryUser = await prisma.factoryUser.findFirst({
        where: { username }
      })

      // 检查管理员表
      const admin = await prisma.admin.findFirst({
        where: { username }
      })

      const exists = !!(factoryUser || admin)
      console.log('🔍 用户名检查:', username, exists ? '已存在' : '可用',
        factoryUser ? '(工厂用户)' : admin ? '(管理员)' : '')
      return exists
    } catch (error) {
      console.error('❌ 检查用户名失败:', error)
      return false
    }
  }

  /**
   * 检查工厂编码是否存在
   */
  async checkFactoryCodeExists(code: string): Promise<boolean> {
    try {
      const factory = await prisma.factory.findFirst({
        where: { code }
      })
      const exists = !!factory
      console.log('🔍 工厂编码检查:', code, exists ? '已存在' : '可用')
      return exists
    } catch (error) {
      console.error('❌ 检查工厂编码失败:', error)
      return false
    }
  }

  /**
   * 获取工厂用户数量
   */
  async getFactoryUserCount(factoryId: string): Promise<number> {
    try {
      const count = await prisma.factoryUser.count({
        where: {
          factoryId,
          isActive: true
        }
      })
      return count
    } catch (error) {
      console.error('❌ 获取工厂用户数量失败:', error)
      return 0
    }
  }

  /**
   * 获取工厂最近登录记录
   */
  async getRecentLoginsByFactory(factoryId: string, limit: number = 10): Promise<any[]> {
    try {
      const logins = await prisma.loginRecord.findMany({
        where: {
          factoryId,
          userType: 'factory_user'
        },
        orderBy: {
          loginTime: 'desc'
        },
        take: limit
      })
      return logins
    } catch (error) {
      console.error('❌ 获取工厂登录记录失败:', error)
      return []
    }
  }

  /**
   * 记录工厂操作日志
   */
  async recordFactoryOperation(operationData: {
    factoryId: string
    operatorId: string
    operatorName: string
    action: string
    reason?: string
    beforeStatus: string
    afterStatus: string
    operatedAt: Date
  }): Promise<void> {
    try {
      // 这里可以创建一个专门的操作日志表，暂时使用console记录
      console.log('📝 工厂操作记录:', {
        factoryId: operationData.factoryId,
        operator: `${operationData.operatorName}(${operationData.operatorId})`,
        action: operationData.action,
        reason: operationData.reason,
        statusChange: `${operationData.beforeStatus} -> ${operationData.afterStatus}`,
        time: operationData.operatedAt.toLocaleString('zh-CN')
      })

      // 可以在这里添加数据库记录逻辑
      // await prisma.factoryOperationLog.create({ data: operationData })

    } catch (error) {
      console.error('❌ 记录工厂操作失败:', error)
    }
  }

  /**
   * 删除工厂及其所有相关数据
   */
  async deleteFactory(factoryId: string): Promise<boolean> {
    try {
      console.log('🗑️ 开始删除工厂:', factoryId)

      // 使用事务确保数据一致性
      await prisma.$transaction(async (tx) => {
        // 1. 删除工厂的所有订单
        await tx.order.deleteMany({
          where: { factoryId }
        })

        // 2. 删除工厂的所有客户
        await tx.client.deleteMany({
          where: { factoryId }
        })

        // 3. 删除工厂的所有用户
        await tx.factoryUser.deleteMany({
          where: { factoryId }
        })

        // 4. 删除工厂本身
        await tx.factory.delete({
          where: { id: factoryId }
        })
      })

      console.log('✅ 工厂删除成功')
      return true
    } catch (error) {
      console.error('❌ 删除工厂失败:', error)
      return false
    }
  }

  /**
   * 获取公告列表
   */
  async getAnnouncements(): Promise<Announcement[]> {
    try {
      console.log('📢 获取所有公告')

      const announcements = await prisma.announcement.findMany({
        include: {
          creator: {
            select: {
              name: true,
              username: true
            }
          },
          targets: {
            include: {
              factory: {
                select: {
                  name: true,
                  code: true
                }
              }
            }
          }
        },
        orderBy: { publishedAt: 'desc' }
      })

      // 映射数据库枚举到前端类型
      const frontendTypeMapping = {
        'system': 'info',
        'maintenance': 'warning',
        'feature': 'urgent',
        'promotion': 'info'
      }

      // 转换数据格式，将 targets 转换为 targetFactories
      const formattedAnnouncements = announcements.map(announcement => ({
        id: announcement.id,
        title: announcement.title,
        content: announcement.content,
        type: frontendTypeMapping[announcement.type] || 'info' as 'info' | 'warning' | 'urgent',
        targetFactories: announcement.targets.map(target => target.factoryId),
        publishedAt: announcement.publishedAt,
        expiresAt: announcement.expiresAt,
        createdBy: announcement.createdBy
      }))

      console.log('✅ 公告获取成功:', formattedAnnouncements.length, '条')
      return formattedAnnouncements
    } catch (error) {
      console.error('❌ 获取公告失败:', error)
      return []
    }
  }

  /**
   * 获取工厂相关公告
   */
  async getAnnouncementsForFactory(factoryId: string): Promise<Announcement[]> {
    try {
      console.log('📢 获取工厂公告:', factoryId)

      const announcements = await prisma.announcement.findMany({
        where: {
          AND: [
            // 公告目标条件
            {
              OR: [
                // 全局公告（没有指定目标工厂）
                {
                  targets: {
                    none: {}
                  }
                },
                // 指定给该工厂的公告
                {
                  targets: {
                    some: {
                      factoryId: factoryId
                    }
                  }
                }
              ]
            },
            // 有效期条件
            {
              OR: [
                { expiresAt: null },
                { expiresAt: { gte: new Date() } }
              ]
            }
          ]
        },
        include: {
          creator: {
            select: {
              name: true,
              username: true
            }
          },
          targets: {
            include: {
              factory: {
                select: {
                  name: true,
                  code: true
                }
              }
            }
          }
        },
        orderBy: { publishedAt: 'desc' }
      })

      // 映射数据库枚举到前端类型
      const frontendTypeMapping = {
        'system': 'info',
        'maintenance': 'warning',
        'feature': 'urgent',
        'promotion': 'info'
      }

      // 转换数据格式，将 targets 转换为 targetFactories
      const formattedAnnouncements = announcements.map(announcement => ({
        id: announcement.id,
        title: announcement.title,
        content: announcement.content,
        type: frontendTypeMapping[announcement.type] || 'info' as 'info' | 'warning' | 'urgent',
        targetFactories: announcement.targets.map(target => target.factoryId),
        publishedAt: announcement.publishedAt,
        expiresAt: announcement.expiresAt,
        createdBy: announcement.createdBy
      }))

      console.log('✅ 工厂公告获取成功:', formattedAnnouncements.length, '条')
      return formattedAnnouncements
    } catch (error) {
      console.error('❌ 获取工厂公告失败:', error)
      return []
    }
  }

  /**
   * 获取工厂未读公告（用于登录弹窗）
   */
  async getUnreadAnnouncementsForFactory(factoryId: string): Promise<Announcement[]> {
    try {
      console.log('🔔 获取工厂未读公告:', factoryId)

      // 获取所有工厂相关公告
      const allAnnouncements = await this.getAnnouncementsForFactory(factoryId)

      // 获取该工厂的已读状态记录
      const readStatusRecords = await prisma.announcementReadStatus.findMany({
        where: {
          factoryId: factoryId
        },
        select: {
          announcementId: true,
          isRead: true,
          isDismissed: true
        }
      })

      // 创建已读状态映射
      const readStatusMap = new Map<string, { isRead: boolean; isDismissed: boolean }>()
      readStatusRecords.forEach(record => {
        readStatusMap.set(record.announcementId, {
          isRead: record.isRead,
          isDismissed: record.isDismissed
        })
      })

      // 过滤出未读且未关闭的公告
      const unreadAnnouncements = allAnnouncements.filter(announcement => {
        const status = readStatusMap.get(announcement.id)

        if (!status) {
          // 没有记录表示未读且未关闭
          console.log(`📢 新公告 ${announcement.id} (${announcement.title}) - 无状态记录，标记为未读`)
          return true
        }

        // 如果已关闭，则不显示在弹窗中
        if (status.isDismissed) {
          console.log(`❌ 公告 ${announcement.id} (${announcement.title}) - 已关闭，不显示`)
          return false
        }

        // 如果已读，则不显示在弹窗中
        if (status.isRead) {
          console.log(`✅ 公告 ${announcement.id} (${announcement.title}) - 已读，不显示`)
          return false
        }

        // 未读且未关闭的公告
        console.log(`📖 公告 ${announcement.id} (${announcement.title}) - 未读，显示`)
        return true
      })

      console.log(`📊 未读公告统计:`, {
        factoryId,
        totalAnnouncements: allAnnouncements.length,
        readStatusRecords: readStatusRecords.length,
        unreadCount: unreadAnnouncements.length,
        unreadIds: unreadAnnouncements.map(a => a.id),
        unreadTitles: unreadAnnouncements.map(a => a.title)
      })

      return unreadAnnouncements
    } catch (error) {
      console.error('❌ 获取工厂未读公告失败:', error)
      return []
    }
  }

  /**
   * 获取工厂的公告已读状态
   */
  async getAnnouncementReadStatus(factoryId: string): Promise<Record<string, boolean>> {
    try {
      console.log('📋 获取工厂公告已读状态:', factoryId)

      const readStatusRecords = await prisma.announcementReadStatus.findMany({
        where: {
          factoryId: factoryId
        },
        select: {
          announcementId: true,
          isRead: true,
          isDismissed: true
        }
      })

      // 转换为前端需要的格式
      const readStatus: Record<string, boolean> = {}
      readStatusRecords.forEach(record => {
        readStatus[record.announcementId] = record.isRead
      })

      console.log('✅ 公告已读状态获取成功:', Object.keys(readStatus).length, '条记录')
      return readStatus
    } catch (error) {
      console.error('❌ 获取公告已读状态失败:', error)
      return {}
    }
  }

  /**
   * 标记公告为已读
   */
  async markAnnouncementAsRead(factoryId: string, announcementId: string): Promise<void> {
    try {
      console.log('✅ 标记公告为已读:', factoryId, announcementId)

      // 获取工厂的管理员用户ID
      const factory = await prisma.factory.findUnique({
        where: { id: factoryId },
        include: {
          users: {
            where: { role: 'owner' },
            take: 1
          }
        }
      })

      if (!factory || factory.users.length === 0) {
        console.error('❌ 找不到工厂或工厂管理员')
        throw new Error('找不到工厂或工厂管理员')
      }

      const userId = factory.users[0].id

      // 使用upsert确保记录存在并更新状态
      await prisma.announcementReadStatus.upsert({
        where: {
          announcementId_userId: {
            announcementId: announcementId,
            userId: userId
          }
        },
        update: {
          isRead: true,
          readAt: new Date()
        },
        create: {
          announcementId: announcementId,
          factoryId: factoryId,
          userId: userId,
          isRead: true,
          isDismissed: false,
          readAt: new Date()
        }
      })

      console.log('✅ 公告已标记为已读')
    } catch (error) {
      console.error('❌ 标记公告为已读失败:', error)
      throw error
    }
  }

  /**
   * 标记公告为未读
   */
  async markAnnouncementAsUnread(factoryId: string, announcementId: string): Promise<void> {
    try {
      console.log('📖 标记公告为未读:', factoryId, announcementId)

      // 获取工厂的管理员用户ID
      const factory = await prisma.factory.findUnique({
        where: { id: factoryId },
        include: {
          users: {
            where: { role: 'owner' },
            take: 1
          }
        }
      })

      if (!factory || factory.users.length === 0) {
        console.error('❌ 找不到工厂或工厂管理员')
        throw new Error('找不到工厂或工厂管理员')
      }

      const userId = factory.users[0].id

      // 使用upsert确保记录存在并更新状态
      await prisma.announcementReadStatus.upsert({
        where: {
          announcementId_userId: {
            announcementId: announcementId,
            userId: userId
          }
        },
        update: {
          isRead: false,
          isDismissed: false // 标记为未读时也取消关闭状态
        },
        create: {
          announcementId: announcementId,
          factoryId: factoryId,
          userId: userId,
          isRead: false,
          isDismissed: false,
          readAt: new Date()
        }
      })

      console.log('✅ 公告已标记为未读')
    } catch (error) {
      console.error('❌ 标记公告为未读失败:', error)
      throw error
    }
  }

  /**
   * 关闭公告（不再显示在弹窗中）
   */
  async dismissAnnouncement(factoryId: string, announcementId: string): Promise<void> {
    try {
      console.log('❌ 关闭公告:', factoryId, announcementId)

      // 获取工厂的管理员用户ID
      const factory = await prisma.factory.findUnique({
        where: { id: factoryId },
        include: {
          users: {
            where: { role: 'owner' },
            take: 1
          }
        }
      })

      if (!factory || factory.users.length === 0) {
        console.error('❌ 找不到工厂或工厂管理员')
        throw new Error('找不到工厂或工厂管理员')
      }

      const userId = factory.users[0].id

      // 使用upsert确保记录存在并更新状态
      await prisma.announcementReadStatus.upsert({
        where: {
          announcementId_userId: {
            announcementId: announcementId,
            userId: userId
          }
        },
        update: {
          isDismissed: true,
          isRead: true, // 关闭时也标记为已读
          readAt: new Date()
        },
        create: {
          announcementId: announcementId,
          factoryId: factoryId,
          userId: userId,
          isRead: true,
          isDismissed: true,
          readAt: new Date()
        }
      })

      console.log('✅ 公告已关闭')
    } catch (error) {
      console.error('❌ 关闭公告失败:', error)
      throw error
    }
  }

  /**
   * 获取工厂活跃公告（用于页面显示，排除已读和已关闭的公告）
   */
  async getActiveAnnouncementsForFactory(factoryId: string): Promise<Announcement[]> {
    try {
      console.log('📋 获取工厂活跃公告:', factoryId)

      // 获取所有工厂相关公告
      const allAnnouncements = await this.getAnnouncementsForFactory(factoryId)

      // 获取该工厂的已读状态记录
      const readStatusRecords = await prisma.announcementReadStatus.findMany({
        where: {
          factoryId: factoryId
        },
        select: {
          announcementId: true,
          isRead: true,
          isDismissed: true
        }
      })

      // 创建已读状态映射
      const readStatusMap = new Map<string, { isRead: boolean; isDismissed: boolean }>()
      readStatusRecords.forEach(record => {
        readStatusMap.set(record.announcementId, {
          isRead: record.isRead,
          isDismissed: record.isDismissed
        })
      })

      // 过滤出未读且未关闭的公告（与弹窗逻辑一致）
      const activeAnnouncements = allAnnouncements.filter(announcement => {
        const status = readStatusMap.get(announcement.id)

        if (!status) {
          // 没有记录表示未读且未关闭
          console.log(`📢 活跃公告 ${announcement.id} (${announcement.title}) - 无状态记录，显示`)
          return true
        }

        // 如果已关闭，则不显示
        if (status.isDismissed) {
          console.log(`❌ 公告 ${announcement.id} (${announcement.title}) - 已关闭，不显示`)
          return false
        }

        // 如果已读，则不显示
        if (status.isRead) {
          console.log(`✅ 公告 ${announcement.id} (${announcement.title}) - 已读，不显示`)
          return false
        }

        // 未读且未关闭的公告
        console.log(`📖 活跃公告 ${announcement.id} (${announcement.title}) - 未读，显示`)
        return true
      })

      console.log(`📊 工厂 ${factoryId} 活跃公告统计: 总计 ${allAnnouncements.length} 条，活跃 ${activeAnnouncements.length} 条`)

      return activeAnnouncements.sort((a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
      )
    } catch (error) {
      console.error('❌ 获取工厂活跃公告失败:', error)
      return []
    }
  }

  /**
   * 获取工厂的所有员工（包含统计数据）
   */
  async getEmployeesByFactoryId(factoryId: string): Promise<FactoryUser[]> {
    try {
      console.log('👥 获取工厂员工:', factoryId)

      const users = await prisma.factoryUser.findMany({
        where: { factoryId },
        include: {
          factory: {
            select: {
              name: true,
              code: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      console.log('📊 计算员工统计数据...')

      // 计算本月开始时间
      const thisMonthStart = new Date()
      thisMonthStart.setDate(1)
      thisMonthStart.setHours(0, 0, 0, 0)

      // 为每个员工计算统计数据
      const usersWithStats = await Promise.all(
        users.map(async (user) => {
          try {
            // 计算总订单数
            const totalOrders = await prisma.order.count({
              where: { createdBy: user.id }
            })

            // 计算本月订单数
            const thisMonthOrders = await prisma.order.count({
              where: {
                createdBy: user.id,
                createdAt: {
                  gte: thisMonthStart
                }
              }
            })

            console.log(`📊 员工 ${user.name}: 总订单 ${totalOrders}, 本月订单 ${thisMonthOrders}`)

            return {
              ...user,
              permissions: JSON.parse(user.permissions || '[]'),
              userType: 'factory_user' as const,
              totalOrders,
              thisMonthOrders
            }
          } catch (error) {
            console.error(`❌ 计算员工 ${user.name} 统计数据失败:`, error)
            return {
              ...user,
              permissions: JSON.parse(user.permissions || '[]'),
              userType: 'factory_user' as const,
              totalOrders: 0,
              thisMonthOrders: 0
            }
          }
        })
      )

      console.log('✅ 工厂员工获取成功:', usersWithStats.length, '个（含统计数据）')
      return usersWithStats as FactoryUser[]
    } catch (error) {
      console.error('❌ 获取工厂员工失败:', error)
      return []
    }
  }

  /**
   * 创建员工
   */
  async createEmployee(employeeData: unknown): Promise<FactoryUser | null> {
    try {
      console.log('👤 创建员工:', employeeData.name)

      // 检查用户名是否已存在
      const existingUser = await prisma.factoryUser.findFirst({
        where: { username: employeeData.username }
      })

      if (existingUser) {
        console.log('❌ 用户名已存在:', employeeData.username)
        return null
      }

      // 加密密码
      const passwordHash = await bcrypt.hash(employeeData.password || '123456', 12)

      // 处理permissions字段 - 确保它是字符串格式
      let permissionsStr: string
      if (Array.isArray(employeeData.permissions)) {
        permissionsStr = JSON.stringify(employeeData.permissions)
      } else if (typeof employeeData.permissions === 'string') {
        permissionsStr = employeeData.permissions
      } else {
        permissionsStr = JSON.stringify(['order_entry'])
      }

      const user = await prisma.factoryUser.create({
        data: {
          factoryId: employeeData.factoryId,
          username: employeeData.username,
          passwordHash,
          name: employeeData.name,
          role: employeeData.role || 'employee',
          permissions: permissionsStr,
          isActive: employeeData.isActive !== false
        },
        include: {
          factory: {
            select: {
              name: true,
              code: true
            }
          }
        }
      })

      console.log('✅ 员工创建成功:', user.name)
      return {
        ...user,
        permissions: JSON.parse(user.permissions || '[]'),
        userType: 'factory_user' as const
      } as FactoryUser
    } catch (error) {
      console.error('❌ 创建员工失败:', error)
      return null
    }
  }

  /**
   * 更新员工信息
   */
  async updateEmployee(employeeId: string, updates: unknown): Promise<FactoryUser | null> {
    try {
      console.log('👤 更新员工信息:', employeeId)
      console.log('📝 更新数据:', JSON.stringify(updates, null, 2))

      // 如果更新密码，需要加密
      const updateData: any = { ...updates }
      if (updateData.password) {
        console.log('🔐 检测到密码更新，正在加密...')
        updateData.passwordHash = await bcrypt.hash(updateData.password, 12)
        delete updateData.password // 删除明文密码
      }

      // 如果更新权限，需要序列化为JSON字符串
      if (updateData.permissions && Array.isArray(updateData.permissions)) {
        console.log('🔐 检测到权限更新，正在序列化...')
        updateData.permissions = JSON.stringify(updateData.permissions)
      }

      console.log('🔄 准备更新数据库...')
      const user = await prisma.factoryUser.update({
        where: { id: employeeId },
        data: updateData,
        include: {
          factory: {
            select: {
              name: true,
              code: true
            }
          }
        }
      })

      console.log('✅ 员工信息更新成功:', user.name)
      return {
        ...user,
        permissions: JSON.parse(user.permissions || '[]'),
        userType: 'factory_user' as const
      } as FactoryUser
    } catch (error) {
      console.error('❌ 更新员工信息失败:', error)
      console.error('❌ 错误详情:', error.message)
      console.error('❌ 错误堆栈:', error.stack)
      throw error // 重新抛出错误以便API路由能捕获
    }
  }

  /**
   * 删除员工
   */
  async deleteEmployee(employeeId: string): Promise<boolean> {
    try {
      console.log('👤 删除员工:', employeeId)

      await prisma.factoryUser.delete({
        where: { id: employeeId }
      })

      console.log('✅ 员工删除成功')
      return true
    } catch (error) {
      console.error('❌ 删除员工失败:', error)
      return false
    }
  }

  /**
   * 切换员工状态
   */
  async toggleEmployeeStatus(employeeId: string): Promise<FactoryUser | null> {
    try {
      console.log('👤 切换员工状态:', employeeId)

      // 先获取当前员工信息
      const currentEmployee = await prisma.factoryUser.findUnique({
        where: { id: employeeId }
      })

      if (!currentEmployee) {
        console.log('❌ 员工不存在')
        return null
      }

      // 切换状态
      const updatedEmployee = await prisma.factoryUser.update({
        where: { id: employeeId },
        data: {
          isActive: !currentEmployee.isActive,
          updatedAt: new Date()
        },
        include: {
          factory: {
            select: {
              name: true,
              code: true
            }
          }
        }
      })

      console.log('✅ 员工状态切换成功:', updatedEmployee.isActive ? '激活' : '停用')
      return {
        ...updatedEmployee,
        userType: 'factory_user' as const
      } as FactoryUser
    } catch (error) {
      console.error('❌ 切换员工状态失败:', error)
      return null
    }
  }

  /**
   * 更新所有员工统计信息（已弃用，改为在获取员工列表时动态计算）
   */
  async updateAllEmployeeStatistics(factoryId: string): Promise<void> {
    try {
      console.log('📊 更新员工统计信息（已弃用）:', factoryId)
      // 此方法已弃用，统计信息现在在 getEmployeesByFactoryId 中动态计算
      console.log('✅ 员工统计信息更新完成（跳过）')
    } catch (error) {
      console.error('❌ 更新员工统计信息失败:', error)
    }
  }

  /**
   * 验证管理员密码
   */
  async verifyAdminPassword(adminId: string, password: string): Promise<boolean> {
    try {
      console.log('🔐 验证管理员密码:', adminId)

      const admin = await prisma.admin.findFirst({
        where: {
          username: 'admin', // 使用固定的管理员用户名
          isActive: true
        }
      })

      if (!admin) {
        console.log('❌ 管理员不存在')
        return false
      }

      const isValidPassword = await bcrypt.compare(password, admin.passwordHash)
      console.log('✅ 管理员密码验证:', isValidPassword ? '成功' : '失败')
      return isValidPassword
    } catch (error) {
      console.error('❌ 验证管理员密码失败:', error)
      return false
    }
  }

  /**
   * 更新管理员密码
   */
  async updateAdminPassword(adminId: string, currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      console.log('🔐 更新管理员密码:', adminId)

      // 首先验证当前密码
      const isCurrentPasswordValid = await this.verifyAdminPassword(adminId, currentPassword)
      if (!isCurrentPasswordValid) {
        console.log('❌ 当前密码验证失败')
        return false
      }

      // 获取管理员
      const admin = await prisma.admin.findFirst({
        where: {
          username: 'admin', // 使用固定的管理员用户名
          isActive: true
        }
      })

      if (!admin) {
        console.log('❌ 管理员不存在')
        return false
      }

      // 生成新密码的哈希
      const newPasswordHash = await bcrypt.hash(newPassword, 12)

      // 更新密码
      await prisma.admin.update({
        where: { id: admin.id },
        data: {
          passwordHash: newPasswordHash,
          updatedAt: new Date()
        }
      })

      console.log('✅ 管理员密码更新成功')
      return true
    } catch (error) {
      console.error('❌ 更新管理员密码失败:', error)
      return false
    }
  }

  /**
   * 验证工厂用户密码
   */
  async verifyFactoryUserPassword(username: string, password: string): Promise<boolean> {
    try {
      console.log('🔐 验证工厂用户密码:', username)

      const user = await prisma.factoryUser.findFirst({
        where: {
          username,
          isActive: true
        }
      })

      if (!user) {
        console.log('❌ 工厂用户不存在或已禁用')
        return false
      }

      const isValidPassword = await bcrypt.compare(password, user.passwordHash)
      console.log('✅ 工厂用户密码验证:', isValidPassword ? '成功' : '失败')
      return isValidPassword
    } catch (error) {
      console.error('❌ 验证工厂用户密码失败:', error)
      return false
    }
  }

  /**
   * 创建公告
   */
  async createAnnouncement(announcementData: unknown): Promise<Announcement | null> {
    try {
      console.log('📢 创建公告:', announcementData.title)

      // 确保基础数据已初始化
      await this.initializeBaseData()

      // 获取管理员ID
      let admin = await prisma.admin.findFirst({
        where: {
          username: 'admin',
          isActive: true
        }
      })

      if (!admin) {
        console.log('❌ 找不到管理员账户，创建默认管理员...')
        // 如果找不到管理员，创建一个默认管理员
        admin = await prisma.admin.create({
          data: {
            username: 'admin',
            passwordHash: '$2b$10$rQZ9QmSTWzrV7LtHNdLo5.Oe8kVQ8B5FqA5Zx1YjKqY5Zx1YjKqY5Z', // 默认密码hash
            name: '系统管理员',
            email: '<EMAIL>',
            isActive: true
          }
        })
        console.log('✅ 默认管理员创建成功:', admin.id)
      }

      // 映射前端类型到数据库枚举
      const typeMapping = {
        'info': 'system',
        'warning': 'maintenance',
        'urgent': 'feature',
        'system': 'system',
        'maintenance': 'maintenance',
        'feature': 'feature',
        'promotion': 'promotion'
      }

      const dbType = typeMapping[announcementData.type] || 'system'

      const announcement = await prisma.announcement.create({
        data: {
          title: announcementData.title,
          content: announcementData.content,
          type: dbType, // 直接使用小写的枚举值，不要转换为大写
          publishedAt: announcementData.publishedAt || new Date(),
          expiresAt: announcementData.expiresAt,
          createdBy: admin.id, // 使用实际的管理员ID
          targets: announcementData.targetFactories && announcementData.targetFactories.length > 0 ? {
            create: announcementData.targetFactories.map((factoryId: string) => ({
              factoryId
            }))
          } : undefined
        },
        include: {
          creator: {
            select: {
              name: true,
              username: true
            }
          },
          targets: {
            include: {
              factory: {
                select: {
                  name: true,
                  code: true
                }
              }
            }
          }
        }
      })

      // 映射数据库枚举到前端类型
      const frontendTypeMapping = {
        'system': 'info',
        'maintenance': 'warning',
        'feature': 'urgent',
        'promotion': 'info'
      }

      const frontendType = frontendTypeMapping[announcement.type] || 'info'

      // 转换数据格式，将 targets 转换为 targetFactories
      const formattedAnnouncement = {
        id: announcement.id,
        title: announcement.title,
        content: announcement.content,
        type: frontendType as 'info' | 'warning' | 'urgent',
        targetFactories: announcement.targets.map(target => target.factoryId),
        publishedAt: announcement.publishedAt,
        expiresAt: announcement.expiresAt,
        createdBy: announcement.createdBy
      }

      console.log('✅ 公告创建成功:', announcement.id)
      return formattedAnnouncement
    } catch (error) {
      console.error('❌ 创建公告失败:', error)
      return null
    }
  }

  /**
   * 更新公告
   */
  async updateAnnouncement(id: string, updates: unknown): Promise<Announcement | null> {
    try {
      console.log('📢 更新公告:', id)

      // 映射前端类型到数据库枚举
      const typeMapping = {
        'info': 'system',
        'warning': 'maintenance',
        'urgent': 'feature',
        'system': 'system',
        'maintenance': 'maintenance',
        'feature': 'feature',
        'promotion': 'promotion'
      }

      const dbType = updates.type ? typeMapping[updates.type] || 'system' : undefined

      // 如果有目标工厂更新，需要先删除旧的关联
      if (updates.targetFactories !== undefined) {
        await prisma.announcementTarget.deleteMany({
          where: { announcementId: id }
        })
      }

      const announcement = await prisma.announcement.update({
        where: { id },
        data: {
          title: updates.title,
          content: updates.content,
          type: dbType, // 直接使用小写的枚举值
          expiresAt: updates.expiresAt,
          targets: updates.targetFactories && updates.targetFactories.length > 0 ? {
            create: updates.targetFactories.map((factoryId: string) => ({
              factoryId
            }))
          } : undefined
        },
        include: {
          creator: {
            select: {
              name: true,
              username: true
            }
          },
          targets: {
            include: {
              factory: {
                select: {
                  name: true,
                  code: true
                }
              }
            }
          }
        }
      })

      // 映射数据库枚举到前端类型
      const frontendTypeMapping = {
        'system': 'info',
        'maintenance': 'warning',
        'feature': 'urgent',
        'promotion': 'info'
      }

      const frontendType = frontendTypeMapping[announcement.type] || 'info'

      // 转换数据格式，将 targets 转换为 targetFactories
      const formattedAnnouncement = {
        id: announcement.id,
        title: announcement.title,
        content: announcement.content,
        type: frontendType as 'info' | 'warning' | 'urgent',
        targetFactories: announcement.targets.map(target => target.factoryId),
        publishedAt: announcement.publishedAt,
        expiresAt: announcement.expiresAt,
        createdBy: announcement.createdBy
      }

      console.log('✅ 公告更新成功')
      return formattedAnnouncement
    } catch (error) {
      console.error('❌ 更新公告失败:', error)
      return null
    }
  }

  /**
   * 删除公告
   */
  async deleteAnnouncement(id: string): Promise<boolean> {
    try {
      console.log('📢 删除公告:', id)

      await prisma.announcement.delete({
        where: { id }
      })

      console.log('✅ 公告删除成功')
      return true
    } catch (error) {
      console.error('❌ 删除公告失败:', error)
      return false
    }
  }

  /**
   * 更新订单付款信息
   */
  async updateOrderPayment(orderId: string, paymentData: {
    paymentStatus?: string
    paidAmount?: number
    dueDate?: Date
    paymentNotes?: string
  }): Promise<boolean> {
    try {
      console.log('💰 更新订单付款信息:', orderId, paymentData)

      // 首先检查订单是否存在
      const existingOrder = await prisma.order.findUnique({
        where: { id: orderId }
      })

      if (!existingOrder) {
        console.error('❌ 订单不存在:', orderId)
        return false
      }

      console.log('📋 找到订单:', {
        id: existingOrder.id,
        orderNumber: existingOrder.orderNumber,
        currentPaidAmount: existingOrder.paidAmount,
        currentPaymentStatus: existingOrder.paymentStatus
      })

      // 构建更新数据
      const updateData: any = {
        updatedAt: new Date()
      }

      let finalPaidAmount = existingOrder.paidAmount ? Number(existingOrder.paidAmount) : 0

      if (paymentData.paidAmount !== undefined) {
        // 确保 paidAmount 是有效的数字，避免浮点数精度问题
        const paidAmount = typeof paymentData.paidAmount === 'string'
          ? parseFloat(paymentData.paidAmount)
          : paymentData.paidAmount

        if (isNaN(paidAmount)) {
          console.error('❌ 无效的付款金额:', paymentData.paidAmount)
          return false
        }

        finalPaidAmount = Math.round(paidAmount * 100) / 100
        updateData.paidAmount = finalPaidAmount
      }

      // 🔧 修复：自动计算付款状态，确保数据库状态正确
      const totalAmount = Number(existingOrder.totalAmount)
      let autoCalculatedStatus = 'unpaid'

      if (finalPaidAmount <= 0) {
        autoCalculatedStatus = 'unpaid'
      } else if (Math.abs(finalPaidAmount - totalAmount) < 0.01 || finalPaidAmount >= totalAmount) {
        autoCalculatedStatus = 'paid'
      } else {
        autoCalculatedStatus = 'partial'
      }

      // 如果明确传入了付款状态，使用传入的状态，否则使用自动计算的状态
      if (paymentData.paymentStatus !== undefined) {
        updateData.paymentStatus = paymentData.paymentStatus
      } else {
        updateData.paymentStatus = autoCalculatedStatus
      }

      console.log('🔧 付款状态计算:', {
        订单总额: totalAmount,
        已付金额: finalPaidAmount,
        传入状态: paymentData.paymentStatus,
        自动计算状态: autoCalculatedStatus,
        最终状态: updateData.paymentStatus
      })
      if (paymentData.dueDate !== undefined) {
        updateData.dueDate = paymentData.dueDate
      }
      if (paymentData.paymentNotes !== undefined) {
        updateData.paymentNotes = paymentData.paymentNotes
      }

      console.log('🔄 准备更新数据:', updateData)

      // 更新订单
      const updatedOrder = await prisma.order.update({
        where: { id: orderId },
        data: updateData
      })

      console.log('✅ 订单付款信息更新成功:', {
        id: updatedOrder.id,
        newPaidAmount: updatedOrder.paidAmount,
        newPaymentStatus: updatedOrder.paymentStatus
      })
      return true
    } catch (error) {
      console.error('❌ 更新订单付款信息失败:', error)
      console.error('❌ 错误详情:', {
        name: (error as unknown).name,
        message: (error as unknown).message,
        code: (error as unknown).code,
        stack: (error as unknown).stack
      })
      return false
    }
  }

  /**
   * 更新订单状态信息
   */
  async updateOrderStatus(orderId: string, statusData: {
    status?: string
    notes?: string
  }): Promise<boolean> {
    try {
      console.log('📝 更新订单状态信息:', orderId, statusData)

      // 首先检查订单是否存在
      const existingOrder = await prisma.order.findUnique({
        where: { id: orderId }
      })

      if (!existingOrder) {
        console.error('❌ 订单不存在:', orderId)
        return false
      }

      console.log('📋 找到订单:', {
        id: existingOrder.id,
        orderNumber: existingOrder.orderNumber,
        currentStatus: existingOrder.status,
        currentNotes: existingOrder.notes
      })

      // 构建更新数据
      const updateData: any = {
        updatedAt: new Date()
      }

      if (statusData.status !== undefined) {
        updateData.status = statusData.status
      }
      if (statusData.notes !== undefined) {
        updateData.notes = statusData.notes
      }

      console.log('🔄 准备更新数据:', updateData)

      // 更新订单
      const updatedOrder = await prisma.order.update({
        where: { id: orderId },
        data: updateData
      })

      console.log('✅ 订单状态信息更新成功:', {
        id: updatedOrder.id,
        newStatus: updatedOrder.status,
        newNotes: updatedOrder.notes
      })
      return true
    } catch (error) {
      console.error('❌ 更新订单状态信息失败:', error)
      console.error('❌ 错误详情:', {
        name: (error as unknown).name,
        message: (error as unknown).message,
        code: (error as unknown).code,
        stack: (error as unknown).stack
      })
      return false
    }
  }

  /**
   * 获取系统设置
   */
  async getSystemSettings(): Promise<unknown> {
    try {
      // 返回完整的默认设置
      return {
        platform: {
          name: '风口云平台',
          version: '1.0.0',
          description: '中央空调风口加工厂SaaS管理系统',
          contactEmail: '<EMAIL>',
          contactPhone: '************',
          website: 'https://fengkoucloud.com'
        },
        security: {
          sessionTimeout: 24,
          passwordMinLength: 6,
          requirePasswordChange: false,
          allowMultipleLogin: true,
          enableTwoFactor: false,
          adminPassword: 'admin@yhg2025'
        },
        backup: {
          autoBackup: true,
          backupFrequency: 'daily',
          retentionDays: 30,
          backupLocation: 'local',
          lastBackupTime: null
        },
        notifications: {
          emailEnabled: false,
          smsEnabled: false,
          pushEnabled: true,
          orderNotifications: true,
          systemNotifications: true,
          announcementNotifications: true
        },
        business: {
          defaultCurrency: 'CNY',
          taxRate: 0.13,
          maxFactories: 100,
          orderNumberPrefix: 'FK',
          invoiceNumberPrefix: 'INV',
          enableRewards: false,
          enableDataSync: true
        },
        appearance: {
          theme: 'light',
          primaryColor: '#dc2626',
          logoUrl: '',
          faviconUrl: ''
        }
      }
    } catch (error) {
      console.error('❌ 获取系统设置失败:', error)
      return {}
    }
  }

  /**
   * 更新系统设置
   */
  async updateSystemSettings(settings: unknown): Promise<void> {
    try {
      console.log('💾 更新系统设置...')

      // 这里可以实现真正的数据库保存逻辑
      // 目前只是模拟保存成功
      console.log('✅ 系统设置更新成功')
    } catch (error) {
      console.error('❌ 更新系统设置失败:', error)
      throw error
    }
  }

  // ==================== 股东管理相关方法 ====================

  /**
   * 获取工厂股东列表
   */
  async getShareholdersByFactoryId(factoryId: string): Promise<Shareholder[]> {
    try {
      console.log('👥 获取工厂股东列表:', factoryId)

      const shareholders = await prisma.shareholder.findMany({
        where: { factoryId },
        orderBy: [
          { sharePercentage: 'desc' },
          { createdAt: 'desc' }
        ]
      })

      console.log(`✅ 获取到 ${shareholders.length} 个股东`)

      console.log(`✅ 获取到 ${shareholders.length} 个股东`)
      return shareholders as Shareholder[]
    } catch (error) {
      console.error('❌ 获取股东列表失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取股东信息
   */
  async getShareholderById(shareholderId: string): Promise<Shareholder | null> {
    try {
      console.log('👤 获取股东信息:', shareholderId)

      const shareholder = await prisma.shareholder.findUnique({
        where: { id: shareholderId }
      })

      if (shareholder) {
        console.log('✅ 股东信息获取成功:', shareholder.name)
      } else {
        console.log('❌ 股东不存在')
      }

      return shareholder as Shareholder | null
    } catch (error) {
      console.error('❌ 获取股东信息失败:', error)
      return null
    }
  }

  /**
   * 创建股东
   */
  async createShareholder(shareholderData: unknown): Promise<Shareholder | null> {
    try {
      console.log('👥 创建股东:', shareholderData.name)

      // 简化版本：暂时跳过股权比例验证，专注于基本功能
      console.log('📝 创建股东数据:', {
        factoryId: shareholderData.factoryId,
        name: shareholderData.name,
        sharePercentage: shareholderData.sharePercentage || 0,
        investmentAmount: shareholderData.investmentAmount || 0
      })

      // 🔧 修改：如果没有提供股份数量，则基于投资金额计算（假设每股1元）
      const shareCount = shareholderData.shareCount > 0 ?
        shareholderData.shareCount :
        Math.round(shareholderData.investmentAmount)

      console.log('📊 股东股份计算:', {
        提供的股份数: shareholderData.shareCount,
        投资金额: shareholderData.investmentAmount,
        计算的股份数: shareCount,
        股权比例: shareholderData.sharePercentage
      })

      const shareholder = await prisma.shareholder.create({
        data: {
          factoryId: shareholderData.factoryId,
          name: shareholderData.name,
          shareholderType: shareholderData.shareholderType || 'INVESTOR',
          shareCount: shareCount,
          sharePercentage: shareholderData.sharePercentage || 0,
          investmentAmount: shareholderData.investmentAmount || 0,
          joinDate: shareholderData.joinDate ? new Date(shareholderData.joinDate) : new Date(),
          status: shareholderData.status || 'active',
          createdBy: shareholderData.createdBy || 'system'
        }
      })

      console.log('✅ 股东创建成功:', shareholder.id)
      return shareholder as Shareholder
    } catch (error) {
      console.error('❌ 创建股东失败:', error)
      return null
    }
  }

  /**
   * 更新股东信息
   */
  async updateShareholder(shareholderId: string, updates: unknown): Promise<Shareholder | null> {
    try {
      console.log('👥 更新股东信息:', shareholderId)

      // 如果更新股权比例，需要验证总和不超过100%
      if (updates.sharePercentage !== undefined) {
        const currentShareholder = await prisma.shareholder.findUnique({
          where: { id: shareholderId }
        })

        if (!currentShareholder) {
          console.log('❌ 股东不存在')
          return null
        }

        const otherShareholders = await prisma.shareholder.findMany({
          where: {
            factoryId: currentShareholder.factoryId,
            id: { not: shareholderId },
            status: { not: 'exited' }  // 只考虑活跃的股东
          }
        })

        const otherTotalPercentage = otherShareholders.reduce((sum, sh) =>
          sum + parseFloat(sh.sharePercentage.toString()), 0
        )

        if (otherTotalPercentage + parseFloat(updates.sharePercentage) > 100) {
          console.log('❌ 股权比例超过100%')
          throw new Error('股权比例总和不能超过100%')
        }
      }

      // 现在字段名已经匹配，直接使用updates
      const updateData: unknown = { ...updates }
      if (updates.joinDate) {
        updateData.joinDate = new Date(updates.joinDate)
      }
      if (updates.exitDate) {
        updateData.exitDate = new Date(updates.exitDate)
      }

      const shareholder = await prisma.shareholder.update({
        where: { id: shareholderId },
        data: updateData
      })

      console.log('✅ 股东信息更新成功:', shareholder.name)
      return shareholder as Shareholder
    } catch (error) {
      console.error('❌ 更新股东信息失败:', error)
      return null
    }
  }

  /**
   * 删除股东
   */
  async deleteShareholder(shareholderId: string): Promise<boolean> {
    try {
      console.log('👥 删除股东:', shareholderId)

      // 获取股东信息
      const shareholder = await prisma.shareholder.findUnique({
        where: { id: shareholderId }
      })

      if (!shareholder) {
        console.log('❌ 股东不存在')
        return false
      }

      // 删除股东
      await prisma.shareholder.delete({
        where: { id: shareholderId }
      })

      // 重新计算剩余股东的股权比例
      await this.recalculateSharePercentages(shareholder.factoryId)

      console.log('✅ 股东删除成功')
      return true
    } catch (error) {
      console.error('❌ 删除股东失败:', error)
      return false
    }
  }

  /**
   * 🆕 追加投资
   */
  async addShareholderInvestment(shareholderId: string, investmentData: {
    amount: number
    notes?: string
    adjustmentType?: 'auto' | 'manual'  // 新增：调整类型
    newSharePercentage?: number         // 新增：手动设置的新股权比例
  }): Promise<{ success: boolean; error?: string; shareholder?: Shareholder }> {
    try {
      console.log('💰 处理追加投资:', {
        shareholderId,
        amount: investmentData.amount,
        adjustmentType: investmentData.adjustmentType || 'auto',
        newSharePercentage: investmentData.newSharePercentage
      })

      console.log('🔍 调试信息 - 接收到的参数:', JSON.stringify(investmentData, null, 2))

      // 获取股东信息
      const shareholder = await prisma.shareholder.findUnique({
        where: { id: shareholderId }
      })

      if (!shareholder) {
        return { success: false, error: '股东不存在' }
      }

      // 获取所有其他活跃股东
      const otherShareholders = await prisma.shareholder.findMany({
        where: {
          factoryId: shareholder.factoryId,
          id: { not: shareholderId },
          status: { not: 'exited' }  // 只考虑活跃的股东
        }
      })

      // 更新股东投资金额
      const originalInvestment = parseFloat(shareholder.investmentAmount.toString())
      const newInvestmentAmount = originalInvestment + investmentData.amount

      // 更新股东基本信息
      await prisma.shareholder.update({
        where: { id: shareholderId },
        data: {
          investmentAmount: newInvestmentAmount,
          notes: investmentData.notes ?
            `${shareholder.notes || ''}\n[${new Date().toLocaleDateString('zh-CN')}] 追加投资 ¥${investmentData.amount.toLocaleString()}: ${investmentData.notes}`.trim() :
            shareholder.notes,
          updatedAt: new Date()
        }
      })

      // 根据调整类型处理股权比例
      console.log('🔍 调试信息 - 股权调整模式判断:', {
        adjustmentType: investmentData.adjustmentType,
        newSharePercentage: investmentData.newSharePercentage,
        isManual: investmentData.adjustmentType === 'manual',
        hasNewPercentage: investmentData.newSharePercentage !== undefined
      })

      if (investmentData.adjustmentType === 'manual' && investmentData.newSharePercentage !== undefined) {
        // 🔧 手动调整模式：用户指定新的股权比例
        console.log('📝 使用手动调整模式，目标股权比例:', investmentData.newSharePercentage + '%')
        await this.manualAdjustSharePercentage(shareholderId, investmentData.newSharePercentage, shareholder.factoryId)
      } else {
        // 🔧 自动调整模式：基于投资金额科学稀释股权
        console.log('🤖 使用自动调整模式，基于投资金额重新计算股权比例')
        await this.autoAdjustSharePercentages(shareholder.factoryId)
      }

      // 获取更新后的股东信息
      const finalShareholder = await prisma.shareholder.findUnique({
        where: { id: shareholderId }
      })

      console.log('✅ 追加投资成功:', {
        原投资额: originalInvestment,
        追加金额: investmentData.amount,
        新投资额: newInvestmentAmount,
        调整方式: investmentData.adjustmentType || 'auto',
        新股权比例: finalShareholder?.sharePercentage
      })

      return {
        success: true,
        shareholder: finalShareholder as Shareholder
      }
    } catch (error) {
      console.error('❌ 追加投资失败:', error)
      return {
        success: false,
        error: (error as Error).message || '追加投资失败'
      }
    }
  }

  /**
   * 🆕 手动调整股权比例
   */
  private async manualAdjustSharePercentage(shareholderId: string, newPercentage: number, factoryId: string): Promise<void> {
    try {
      console.log('🔧 手动调整股权比例:', { shareholderId, newPercentage })

      // 验证新股权比例是否合理
      if (newPercentage < 0 || newPercentage > 100) {
        throw new Error('股权比例必须在0-100之间')
      }

      // 获取其他股东的股权比例总和
      const otherShareholders = await prisma.shareholder.findMany({
        where: {
          factoryId,
          id: { not: shareholderId },
          status: { not: 'exited' }  // 只考虑活跃的股东
        }
      })

      const otherTotalPercentage = otherShareholders.reduce((sum, sh) =>
        sum + parseFloat(sh.sharePercentage.toString()), 0
      )

      if (otherTotalPercentage + newPercentage > 100) {
        throw new Error(`新股权比例${newPercentage}%与其他股东股权比例${otherTotalPercentage}%总和超过100%`)
      }

      // 更新目标股东的股权比例
      const shareholder = await prisma.shareholder.findUnique({
        where: { id: shareholderId }
      })

      if (!shareholder) {
        throw new Error('股东不存在')
      }

      // 基于投资金额计算股份数（假设每股1元）
      const investmentAmount = parseFloat(shareholder.investmentAmount.toString())
      const shareCount = Math.round(investmentAmount) // 股份数 = 投资金额（假设每股1元）

      await prisma.shareholder.update({
        where: { id: shareholderId },
        data: {
          sharePercentage: newPercentage,
          shareCount,
          updatedAt: new Date()
        }
      })

      console.log('✅ 手动股权调整完成:', {
        股东: shareholder.name,
        新股权比例: `${newPercentage}%`,
        新股份数: shareCount
      })
    } catch (error) {
      console.error('❌ 手动调整股权比例失败:', error)
      throw error
    }
  }

  /**
   * 🆕 自动调整股权比例（科学稀释）
   */
  private async autoAdjustSharePercentages(factoryId: string): Promise<void> {
    try {
      console.log('🔄 自动调整股权比例（科学稀释）:', factoryId)

      // 获取所有活跃股东
      const shareholders = await prisma.shareholder.findMany({
        where: {
          factoryId,
          status: { not: 'exited' }  // 只考虑活跃的股东
        }
      })

      if (shareholders.length === 0) {
        console.log('✅ 没有活跃股东，无需计算')
        return
      }

      // 计算总投资额
      const totalInvestment = shareholders.reduce((sum, sh) =>
        sum + parseFloat(sh.investmentAmount.toString()), 0
      )

      console.log('📊 股权计算基础数据:', {
        股东数量: shareholders.length,
        总投资额: totalInvestment
      })

      // 更新每个股东的股权比例和股份数
      for (const shareholder of shareholders) {
        const investmentAmount = parseFloat(shareholder.investmentAmount.toString())
        const sharePercentage = totalInvestment > 0 ?
          Math.round((investmentAmount / totalInvestment) * 10000) / 100 : // 保留2位小数
          0

        // 基于投资金额计算股份数
        const shareCount = Math.round(investmentAmount)

        await prisma.shareholder.update({
          where: { id: shareholder.id },
          data: {
            sharePercentage,
            shareCount,
            updatedAt: new Date()
          }
        })

        console.log(`📈 股东 ${shareholder.name} 股权更新:`, {
          投资额: investmentAmount,
          股权比例: `${sharePercentage}%`,
          股份数: shareCount
        })
      }

      console.log('✅ 自动股权调整完成')
    } catch (error) {
      console.error('❌ 自动调整股权比例失败:', error)
      throw error
    }
  }

  /**
   * 🆕 重新计算股权比例（保留原方法兼容性）
   */
  private async recalculateSharePercentages(factoryId: string): Promise<void> {
    try {
      console.log('🔄 重新计算股权比例:', factoryId)

      // 获取所有活跃股东
      const shareholders = await prisma.shareholder.findMany({
        where: {
          factoryId,
          status: { not: 'exited' }  // 只考虑活跃的股东
        }
      })

      if (shareholders.length === 0) {
        console.log('✅ 没有活跃股东，无需计算')
        return
      }

      // 计算总投资额
      const totalInvestment = shareholders.reduce((sum, sh) =>
        sum + parseFloat(sh.investmentAmount.toString()), 0
      )

      console.log('📊 股权计算基础数据:', {
        股东数量: shareholders.length,
        总投资额: totalInvestment
      })

      // 更新每个股东的股权比例和股份数
      for (const shareholder of shareholders) {
        const investmentAmount = parseFloat(shareholder.investmentAmount.toString())
        const sharePercentage = totalInvestment > 0 ?
          Math.round((investmentAmount / totalInvestment) * 10000) / 100 : // 保留2位小数
          0

        // 假设每股1元，股份数等于投资金额
        const shareCount = Math.round(investmentAmount)

        await prisma.shareholder.update({
          where: { id: shareholder.id },
          data: {
            sharePercentage,
            shareCount,
            updatedAt: new Date()
          }
        })

        console.log(`📈 股东 ${shareholder.name} 股权更新:`, {
          投资额: investmentAmount,
          股权比例: `${sharePercentage}%`,
          股份数: shareCount
        })
      }

      console.log('✅ 股权比例重新计算完成')
    } catch (error) {
      console.error('❌ 重新计算股权比例失败:', error)
      throw error
    }
  }

  // ==================== 分红管理相关方法 ====================

  /**
   * 获取工厂分红列表
   */
  async getDividendsByFactoryId(factoryId: string): Promise<Dividend[]> {
    try {
      console.log('💰 获取工厂分红列表:', factoryId)

      const dividends = await prisma.dividend.findMany({
        where: { factoryId },
        include: {
          records: {
            include: {
              shareholder: {
                select: {
                  name: true,
                  shareholderType: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      console.log(`✅ 获取到 ${dividends.length} 个分红记录`)
      return dividends as Dividend[]
    } catch (error) {
      console.error('❌ 获取分红列表失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取分红信息
   */
  async getDividendById(dividendId: string): Promise<Dividend | null> {
    try {
      console.log('💰 获取分红信息:', dividendId)

      const dividend = await prisma.dividend.findUnique({
        where: { id: dividendId },
        include: {
          records: {
            include: {
              shareholder: {
                select: {
                  name: true,
                  shareholderType: true,
                  sharePercentage: true
                }
              }
            }
          }
        }
      })

      if (dividend) {
        console.log('✅ 分红信息获取成功:', dividend.title)
      } else {
        console.log('❌ 分红不存在')
      }

      return dividend as Dividend | null
    } catch (error) {
      console.error('❌ 获取分红信息失败:', error)
      return null
    }
  }

  /**
   * 创建分红
   */
  async createDividend(dividendData: unknown): Promise<Dividend | null> {
    try {
      console.log('💰 创建分红:', dividendData.title)

      // 获取工厂的活跃股东
      const shareholders = await prisma.shareholder.findMany({
        where: {
          factoryId: dividendData.factoryId,
          status: ShareholderStatus.active
        }
      })

      if (shareholders.length === 0) {
        console.log('❌ 没有活跃股东')
        throw new Error('没有活跃股东可以分红')
      }

      // 创建分红记录
      const dividend = await prisma.dividend.create({
        data: {
          factoryId: dividendData.factoryId,
          title: dividendData.title,
          description: dividendData.description || null,
          totalAmount: dividendData.totalAmount,
          baseAmount: dividendData.baseAmount,
          dividendRate: dividendData.dividendRate,
          periodStart: new Date(dividendData.periodStart),
          periodEnd: new Date(dividendData.periodEnd),
          status: dividendData.status || DividendStatus.draft,
          createdBy: dividendData.createdBy
        }
      })

      // 为每个股东创建分红明细
      const dividendRecords = shareholders.map(shareholder => {
        const dividendAmount = (parseFloat(dividend.totalAmount.toString()) *
          parseFloat(shareholder.sharePercentage.toString())) / 100

        return {
          dividendId: dividend.id,
          shareholderId: shareholder.id,
          shareCount: shareholder.shareCount,
          sharePercentage: shareholder.sharePercentage,
          dividendAmount: dividendAmount,
          actualAmount: dividendAmount, // 默认无税费
          status: 'pending' as const
        }
      })

      await prisma.dividendRecord.createMany({
        data: dividendRecords
      })

      console.log('✅ 分红创建成功:', dividend.id)
      return dividend as Dividend
    } catch (error) {
      console.error('❌ 创建分红失败:', error)
      return null
    }
  }

  /**
   * 更新分红信息
   */
  async updateDividend(dividendId: string, updates: unknown): Promise<Dividend | null> {
    try {
      console.log('💰 更新分红信息:', dividendId)

      const updateData: unknown = { ...updates }
      if (updates.periodStart) {
        updateData.periodStart = new Date(updates.periodStart)
      }
      if (updates.periodEnd) {
        updateData.periodEnd = new Date(updates.periodEnd)
      }
      if (updates.approvedAt) {
        updateData.approvedAt = new Date(updates.approvedAt)
      }
      if (updates.distributedAt) {
        updateData.distributedAt = new Date(updates.distributedAt)
      }

      const dividend = await prisma.dividend.update({
        where: { id: dividendId },
        data: updateData,
        include: {
          records: {
            include: {
              shareholder: {
                select: {
                  name: true,
                  shareholderType: true
                }
              }
            }
          }
        }
      })

      console.log('✅ 分红信息更新成功:', dividend.title)
      return dividend as Dividend
    } catch (error) {
      console.error('❌ 更新分红信息失败:', error)
      return null
    }
  }

  /**
   * 删除分红
   */
  async deleteDividend(dividendId: string): Promise<boolean> {
    try {
      console.log('💰 删除分红:', dividendId)

      await prisma.dividend.delete({
        where: { id: dividendId }
      })

      console.log('✅ 分红删除成功')
      return true
    } catch (error) {
      console.error('❌ 删除分红失败:', error)
      return false
    }
  }

  // ==================== 股东利润分析相关方法 ====================

  /**
   * 获取利润分析数据
   */
  async getProfitAnalysis(options: {
    factoryId: string
    viewType: 'monthly' | 'quarterly' | 'yearly'
    year: number
    month?: number
    quarter?: number
  }): Promise<{
    data: Array<{
      period: string
      revenue: number
      costs: number
      expenses: number
      profit: number
      profitMargin: number
    }>
    summary: {
      totalRevenue: number
      totalCosts: number
      totalProfit: number
      averageProfitMargin: number
      bestMonth: string
      worstMonth: string
    }
  }> {
    try {
      console.log('📊 获取利润分析数据:', options)

      const { factoryId, viewType, year, month, quarter } = options

      // 构建日期范围
      let startDate: Date
      let endDate: Date

      if (viewType === 'monthly' && month) {
        // 月度查看：指定月份
        startDate = new Date(year, month - 1, 1)
        endDate = new Date(year, month, 0, 23, 59, 59)
      } else if (viewType === 'quarterly' && quarter) {
        // 季度查看：指定季度
        const startMonth = (quarter - 1) * 3
        const endMonth = startMonth + 2
        startDate = new Date(year, startMonth, 1)
        endDate = new Date(year, endMonth + 1, 0, 23, 59, 59)
      } else {
        // 年度查看：整年
        startDate = new Date(year, 0, 1)
        endDate = new Date(year, 11, 31, 23, 59, 59)
      }

      // 获取订单数据（收入）
      const orders = await prisma.order.findMany({
        where: {
          factoryId,
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          paymentStatus: { in: ['paid', 'partial'] }
        },
        select: {
          totalAmount: true,
          paidAmount: true,
          createdAt: true
        }
      })

      // 获取成本数据（这里需要根据实际业务逻辑调整）
      // 暂时使用订单金额的60%作为成本估算
      const costRatio = 0.6

      // 按期间分组计算
      const periodData = new Map<string, {
        revenue: number
        costs: number
        expenses: number
      }>()

      orders.forEach(order => {
        const orderDate = new Date(order.createdAt)
        let periodKey: string

        if (viewType === 'monthly') {
          // 按日分组
          periodKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}-${String(orderDate.getDate()).padStart(2, '0')}`
        } else if (viewType === 'quarterly') {
          // 按月分组（季度内按月显示）
          periodKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`
        } else {
          // 按月分组（年度按月显示）
          periodKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`
        }

        if (!periodData.has(periodKey)) {
          periodData.set(periodKey, {
            revenue: 0,
            costs: 0,
            expenses: 0
          })
        }

        const data = periodData.get(periodKey)!
        const revenue = parseFloat(order.paidAmount?.toString() || '0')
        data.revenue += revenue
        data.costs += revenue * costRatio
        data.expenses += revenue * 0.1 // 假设费用为收入的10%
      })

      // 转换为数组并计算利润
      const data = Array.from(periodData.entries()).map(([period, values]) => {
        const profit = values.revenue - values.costs - values.expenses
        const profitMargin = values.revenue > 0 ? (profit / values.revenue) * 100 : 0

        return {
          period: this.formatPeriodDisplay(period, viewType),
          revenue: values.revenue,
          costs: values.costs,
          expenses: values.expenses,
          profit,
          profitMargin
        }
      }).sort((a, b) => a.period.localeCompare(b.period))

      // 计算汇总数据
      const summary = {
        totalRevenue: data.reduce((sum, item) => sum + item.revenue, 0),
        totalCosts: data.reduce((sum, item) => sum + item.costs, 0),
        totalProfit: data.reduce((sum, item) => sum + item.profit, 0),
        averageProfitMargin: data.length > 0 ?
          data.reduce((sum, item) => sum + item.profitMargin, 0) / data.length : 0,
        bestMonth: data.length > 0 ?
          data.reduce((best, current) => current.profit > best.profit ? current : best).period : '',
        worstMonth: data.length > 0 ?
          data.reduce((worst, current) => current.profit < worst.profit ? current : worst).period : ''
      }

      console.log('✅ 利润分析数据获取成功:', {
        数据条数: data.length,
        总收入: summary.totalRevenue,
        总利润: summary.totalProfit
      })

      return { data, summary }
    } catch (error) {
      console.error('❌ 获取利润分析数据失败:', error)
      return {
        data: [],
        summary: {
          totalRevenue: 0,
          totalCosts: 0,
          totalProfit: 0,
          averageProfitMargin: 0,
          bestMonth: '',
          worstMonth: ''
        }
      }
    }
  }

  /**
   * 格式化期间显示
   */
  private formatPeriodDisplay(period: string, viewType: 'monthly' | 'quarterly' | 'yearly'): string {
    if (viewType === 'monthly') {
      // 2024-01-15 -> 1月15日
      const [year, month, day] = period.split('-')
      return `${parseInt(month)}月${parseInt(day)}日`
    } else if (viewType === 'quarterly') {
      // 2024-01 -> 1月
      const [year, month] = period.split('-')
      return `${parseInt(month)}月`
    } else {
      // 2024-01 -> 1月
      const [year, month] = period.split('-')
      return `${parseInt(month)}月`
    }
  }

  // ==================== 登录记录管理相关方法 ====================

  /**
   * 记录登录信息
   */
  async recordLogin(loginData: {
    userId: string
    userType: 'admin' | 'factory_user'
    username: string
    userName: string
    factoryId?: string
    factoryName?: string
    loginStatus: 'success' | 'failed' | 'blocked'
    failReason?: string
    ipAddress?: string
    userAgent?: string
    deviceInfo?: string
    location?: string
    sessionId?: string  // 🔧 新增：支持传入sessionId
  }): Promise<any | null> {
    try {
      console.log('📝 记录登录信息:', loginData.username, loginData.loginStatus)

      // 获取请求信息（如果在服务器环境中）
      const requestInfo = this.getRequestInfo()

      const loginRecord = await prisma.loginRecord.create({
        data: {
          userId: loginData.userId || 'unknown',
          userType: loginData.userType,
          username: loginData.username,
          userName: loginData.userName,
          factoryId: loginData.factoryId || null,
          factoryName: loginData.factoryName || null,
          loginTime: new Date(),
          ipAddress: loginData.ipAddress || requestInfo.ipAddress,
          userAgent: loginData.userAgent || requestInfo.userAgent,
          deviceInfo: loginData.deviceInfo || requestInfo.deviceInfo,
          location: loginData.location || null,
          loginStatus: loginData.loginStatus,
          failReason: loginData.failReason || null,
          sessionId: loginData.sessionId || this.generateSessionId()  // 🔧 修复：优先使用传入的sessionId
        }
      })

      console.log('✅ 登录记录创建成功:', loginRecord.id)
      return loginRecord
    } catch (error) {
      console.error('❌ 记录登录信息失败:', error)
      return null
    }
  }

  /**
   * 🔧 新增：检查用户是否已在其他地方登录
   */
  async checkActiveSession(userId: string, userType: 'admin' | 'factory_user'): Promise<{
    hasActiveSession: boolean
    activeSessionId?: string
    lastLoginTime?: Date
    ipAddress?: string
  }> {
    try {
      console.log('🔍 检查活跃会话:', userId, userType)

      // 查找最近的成功登录记录（未登出的会话）
      const latestLogin = await prisma.loginRecord.findFirst({
        where: {
          userId,
          userType,
          loginStatus: 'success',
          logoutTime: null // 未登出的会话
        },
        orderBy: {
          loginTime: 'desc'
        }
      })

      if (!latestLogin || !latestLogin.sessionId) {
        console.log('✅ 无活跃会话')
        return { hasActiveSession: false }
      }

      console.log('⚠️ 发现活跃会话:', latestLogin.sessionId)
      return {
        hasActiveSession: true,
        activeSessionId: latestLogin.sessionId,
        lastLoginTime: latestLogin.loginTime,
        ipAddress: latestLogin.ipAddress || undefined
      }
    } catch (error) {
      console.error('❌ 检查活跃会话失败:', error)
      return { hasActiveSession: false }
    }
  }

  /**
   * 🔧 新增：使旧会话失效
   */
  async invalidateOldSessions(userId: string, userType: 'admin' | 'factory_user', currentSessionId: string): Promise<void> {
    try {
      console.log('🔄 使旧会话失效:', userId, currentSessionId)

      // 将所有旧的会话标记为已登出
      const result = await prisma.loginRecord.updateMany({
        where: {
          userId,
          userType,
          loginStatus: 'success',
          logoutTime: null,
          sessionId: {
            not: currentSessionId // 不包括当前会话
          }
        },
        data: {
          logoutTime: new Date(),
          sessionDuration: 0 // 强制登出，会话时长为0
        }
      })

      console.log('✅ 旧会话已失效，影响记录数:', result.count)
    } catch (error) {
      console.error('❌ 使旧会话失效失败:', error)
    }
  }

  /**
   * 🔧 新增：验证会话是否有效
   */
  async validateSession(userId: string, userType: 'admin' | 'factory_user', sessionId: string): Promise<boolean> {
    try {
      const session = await prisma.loginRecord.findFirst({
        where: {
          userId,
          userType,
          sessionId,
          loginStatus: 'success',
          logoutTime: null // 未登出
        }
      })

      const isValid = !!session
      console.log('🔍 会话验证结果:', sessionId, isValid ? '有效' : '无效')
      return isValid
    } catch (error) {
      console.error('❌ 验证会话失败:', error)
      return false
    }
  }

  /**
   * 🔧 新增：获取用户的所有活跃会话
   */
  async getActiveUserSessions(userId: string, userType: 'admin' | 'factory_user'): Promise<any[]> {
    try {
      console.log('🔍 获取用户活跃会话:', { userId, userType })

      const sessions = await prisma.loginRecord.findMany({
        where: {
          userId,
          userType,
          loginStatus: 'success',
          isActive: true,
          sessionStatus: 'ACTIVE'
        },
        orderBy: {
          loginTime: 'desc'
        },
        select: {
          id: true,
          sessionId: true,
          loginTime: true,
          ipAddress: true,
          userAgent: true,
          deviceInfo: true,
          sessionStatus: true,
          isActive: true
        }
      })

      console.log(`📊 找到 ${sessions.length} 个活跃会话`)
      return sessions
    } catch (error) {
      console.error('❌ 获取活跃会话失败:', error)
      return []
    }
  }

  /**
   * 🆕 获取奖励使用记录
   */
  async getRewardUsageRecords(clientId: string, factoryId: string): Promise<any[]> {
    try {
      console.log('🎁 获取奖励使用记录:', { clientId, factoryId })

      const records = await prisma.rewardUsage.findMany({
        where: {
          clientId,
          factoryId
        },
        select: {
          id: true,
          amount: true,
          status: true,
          usageType: true,
          description: true,
          createdAt: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      console.log(`💰 找到 ${records.length} 条奖励使用记录`)
      return records
    } catch (error) {
      console.error('❌ 获取奖励使用记录失败:', error)
      return []
    }
  }

  /**
   * 获取登录记录列表
   */
  async getLoginRecords(options: {
    factoryId?: string
    userType?: 'admin' | 'factory_user'
    loginStatus?: 'success' | 'failed' | 'blocked'
    startDate?: Date
    endDate?: Date
    limit?: number
    offset?: number
  } = {}): Promise<unknown[]> {
    try {
      console.log('📋 获取登录记录:', options)

      const where: unknown = {}

      if (options.factoryId) {
        where.factoryId = options.factoryId
      }
      if (options.userType) {
        where.userType = options.userType
      }
      if (options.loginStatus) {
        where.loginStatus = options.loginStatus
      }
      if (options.startDate || options.endDate) {
        where.loginTime = {}
        if (options.startDate) {
          where.loginTime.gte = options.startDate
        }
        if (options.endDate) {
          where.loginTime.lte = options.endDate
        }
      }

      const records = await prisma.loginRecord.findMany({
        where,
        orderBy: { loginTime: 'desc' },
        take: options.limit || 100,
        skip: options.offset || 0
      })

      console.log('✅ 登录记录获取成功:', records.length, '条')
      return records
    } catch (error) {
      console.error('❌ 获取登录记录失败:', error)
      return []
    }
  }

  /**
   * 获取登录统计信息
   */
  async getLoginStats(options: {
    factoryId?: string
    userType?: 'admin' | 'factory_user'
    startDate?: Date
    endDate?: Date
  } = {}): Promise<unknown> {
    try {
      console.log('📊 获取登录统计:', options)

      const where: unknown = {}
      if (options.factoryId) {
        where.factoryId = options.factoryId
      }
      if (options.userType) {
        where.userType = options.userType
      }
      if (options.startDate || options.endDate) {
        where.loginTime = {}
        if (options.startDate) {
          where.loginTime.gte = options.startDate
        }
        if (options.endDate) {
          where.loginTime.lte = options.endDate
        }
      }

      // 总登录次数
      const totalLogins = await prisma.loginRecord.count({ where })

      // 成功登录次数
      const successfulLogins = await prisma.loginRecord.count({
        where: { ...where, loginStatus: 'success' }
      })

      // 失败登录次数
      const failedLogins = await prisma.loginRecord.count({
        where: { ...where, loginStatus: 'failed' }
      })

      // 唯一用户数
      const uniqueUsers = await prisma.loginRecord.groupBy({
        by: ['userId'],
        where,
        _count: { userId: true }
      })

      // 按小时统计登录次数
      const loginsByHour = await this.getLoginsByHour(where)

      // 按天统计登录次数
      const loginsByDay = await this.getLoginsByDay(where)

      // 最活跃用户
      const topUsers = await this.getTopLoginUsers(where)

      const stats = {
        totalLogins,
        successfulLogins,
        failedLogins,
        uniqueUsers: uniqueUsers.length,
        averageSessionDuration: 0, // TODO: 计算平均会话时长
        peakLoginHour: this.findPeakHour(loginsByHour),
        loginsByHour,
        loginsByDay,
        topUsers
      }

      console.log('✅ 登录统计获取成功')
      return stats
    } catch (error) {
      console.error('❌ 获取登录统计失败:', error)
      return {
        totalLogins: 0,
        successfulLogins: 0,
        failedLogins: 0,
        uniqueUsers: 0,
        averageSessionDuration: 0,
        peakLoginHour: 0,
        loginsByHour: [],
        loginsByDay: [],
        topUsers: []
      }
    }
  }

  /**
   * 获取当前在线用户
   */
  async getOnlineUsers(factoryId?: string): Promise<unknown[]> {
    try {
      console.log('👥 获取在线用户:', factoryId)

      // 定义在线时间阈值（30分钟）
      const onlineThreshold = new Date(Date.now() - 30 * 60 * 1000)

      const where: unknown = {
        loginStatus: 'success',
        loginTime: { gte: onlineThreshold },
        logoutTime: null
      }

      if (factoryId) {
        where.factoryId = factoryId
      }

      const onlineRecords = await prisma.loginRecord.findMany({
        where,
        orderBy: { loginTime: 'desc' },
        distinct: ['userId'] // 每个用户只显示最新的登录记录
      })

      const onlineUsers = onlineRecords.map(record => ({
        userId: record.userId,
        userName: record.userName,
        userType: record.userType,
        factoryId: record.factoryId,
        factoryName: record.factoryName,
        loginTime: record.loginTime,
        lastActivity: record.loginTime, // TODO: 实现真实的最后活动时间
        ipAddress: record.ipAddress,
        deviceInfo: record.deviceInfo
      }))

      console.log('✅ 在线用户获取成功:', onlineUsers.length, '个')
      return onlineUsers
    } catch (error) {
      console.error('❌ 获取在线用户失败:', error)
      return []
    }
  }

  /**
   * 记录登出信息
   */
  async recordLogout(sessionId: string): Promise<boolean> {
    try {
      console.log('📝 记录登出信息:', sessionId)

      const logoutTime = new Date()

      const loginRecord = await prisma.loginRecord.findFirst({
        where: { sessionId }
      })

      if (loginRecord) {
        const sessionDuration = Math.floor((logoutTime.getTime() - loginRecord.loginTime.getTime()) / 1000)

        await prisma.loginRecord.update({
          where: { id: loginRecord.id },
          data: {
            logoutTime,
            sessionDuration
          }
        })

        console.log('✅ 登出记录更新成功')
        return true
      } else {
        console.log('❌ 找不到对应的登录记录')
        return false
      }
    } catch (error) {
      console.error('❌ 记录登出信息失败:', error)
      return false
    }
  }

  // ==================== 辅助方法 ====================

  /**
   * 获取请求信息
   */
  private getRequestInfo(): { ipAddress?: string; userAgent?: string; deviceInfo?: string } {
    // 在服务器环境中，这些信息需要从请求头中获取
    // 这里提供默认实现，实际使用时需要在API路由中传入
    return {
      ipAddress: undefined,
      userAgent: undefined,
      deviceInfo: undefined
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 检查工厂状态和使用期限（增强版）
   */
  async checkFactoryStatus(factory: any): Promise<{
    canLogin: boolean
    reason?: string
    status: string
    isExpired: boolean
    remainingDays?: number
    remainingHours?: number
    warningLevel?: 'none' | 'warning' | 'critical' | 'expired'
    autoSuspended?: boolean
  }> {
    try {
      if (!factory) {
        return {
          canLogin: false,
          reason: '工厂信息不存在，请联系管理员',
          status: 'not_found',
          isExpired: false,
          warningLevel: 'expired'
        }
      }

      const now = new Date()

      // 更新最后状态检查时间
      await prisma.factory.update({
        where: { id: factory.id },
        data: { lastStatusCheck: now }
      })

      // 优先检查手动暂停状态（暂停优先于到期检查）
      if (factory.status === 'suspended') {
        const suspendedBy = factory.suspendedBy ? `（操作者：${factory.suspendedBy}）` : ''
        const suspendedTime = factory.suspendedAt ?
          `暂停时间：${new Date(factory.suspendedAt).toLocaleString('zh-CN')}` : ''
        const reason = factory.suspendedReason || '服务已暂停'

        return {
          canLogin: false,
          reason: `工厂服务已暂停：${reason}。${suspendedTime}${suspendedBy}请联系管理员`,
          status: 'suspended',
          isExpired: false,
          warningLevel: 'expired'
        }
      }

      // 检查工厂是否未激活
      if (factory.status === 'inactive') {
        return {
          canLogin: false,
          reason: '工厂账号未激活，请联系管理员开通服务',
          status: 'inactive',
          isExpired: false,
          warningLevel: 'expired'
        }
      }

      // 如果是永久订阅，直接允许登录
      if (factory.isPermanent || factory.subscriptionType === 'permanent') {
        return {
          canLogin: true,
          status: 'active',
          isExpired: false,
          warningLevel: 'none'
        }
      }

      // 检查使用期限（精确到小时，考虑暂停时间）
      if (factory.subscriptionEnd) {
        const endDate = new Date(factory.subscriptionEnd)

        // 🔧 计算暂停时间累计，调整实际结束时间
        let totalSuspendedMs = Number(factory.totalSuspendedMs || 0)

        // 如果当前正在暂停中，加上当前暂停的时间
        if (factory.status === 'suspended' && factory.suspendedAt) {
          const suspendedAt = new Date(factory.suspendedAt)
          const currentSuspendedMs = now.getTime() - suspendedAt.getTime()
          totalSuspendedMs += currentSuspendedMs
        }

        // 实际结束时间 = 原结束时间 + 暂停时间累计
        const adjustedEndDate = new Date(endDate.getTime() + totalSuspendedMs)
        const remainingMs = adjustedEndDate.getTime() - now.getTime()
        const remainingDays = Math.ceil(remainingMs / (1000 * 60 * 60 * 24))
        const remainingHours = Math.ceil(remainingMs / (1000 * 60 * 60))

        // 已过期处理
        if (remainingMs <= 0) {
          let autoSuspended = false

          // 如果启用自动暂停且当前状态不是已过期，则自动暂停
          if (factory.autoSuspendEnabled !== false && factory.status !== 'expired') {
            await prisma.factory.update({
              where: { id: factory.id },
              data: {
                status: 'expired',
                suspendedAt: now,
                suspendedReason: '订阅期限已到期，系统自动暂停',
                suspendedBy: 'system'
              }
            })
            autoSuspended = true
          }

          const expiredDays = Math.abs(remainingDays)
          return {
            canLogin: false,
            reason: `使用期限已到期${expiredDays}天，请联系管理员续费`,
            status: 'expired',
            isExpired: true,
            remainingDays: 0,
            remainingHours: 0,
            warningLevel: 'expired',
            autoSuspended
          }
        }

        // 确定警告级别
        let warningLevel: 'none' | 'warning' | 'critical' | 'expired' = 'none'
        if (remainingDays <= 1) {
          warningLevel = 'critical'  // 1天内
        } else if (remainingDays <= 7) {
          warningLevel = 'warning'   // 7天内
        }

        return {
          canLogin: true,
          status: 'active',
          isExpired: false,
          remainingDays: remainingDays,
          remainingHours: remainingHours,
          warningLevel
        }
      }

      // 如果没有设置结束时间，检查是否是试用期
      if (factory.subscriptionType === 'trial') {
        // 试用期默认30天，从首次登录开始计算
        if (factory.firstLoginAt) {
          const trialStart = new Date(factory.firstLoginAt)
          const trialEnd = new Date(trialStart.getTime() + (30 * 24 * 60 * 60 * 1000)) // 30天
          const remainingMs = trialEnd.getTime() - now.getTime()
          const remainingDays = Math.ceil(remainingMs / (1000 * 60 * 60 * 24))

          if (remainingMs <= 0) {
            // 试用期过期
            await prisma.factory.update({
              where: { id: factory.id },
              data: {
                status: 'expired',
                subscriptionEnd: trialEnd
              }
            })

            return {
              canLogin: false,
              reason: '试用期已结束，请联系管理员购买正式订阅',
              status: 'expired',
              isExpired: true,
              remainingDays: 0,
              warningLevel: 'expired'
            }
          }

          let warningLevel: 'none' | 'warning' | 'critical' | 'expired' = 'none'
          if (remainingDays <= 1) {
            warningLevel = 'critical'
          } else if (remainingDays <= 3) {
            warningLevel = 'warning'
          }

          return {
            canLogin: true,
            status: 'active',
            isExpired: false,
            remainingDays: remainingDays,
            warningLevel
          }
        }
      }

      // 默认允许登录（新创建的工厂等）
      return {
        canLogin: true,
        status: 'active',
        isExpired: false,
        warningLevel: 'none'
      }

    } catch (error) {
      console.error('❌ 检查工厂状态失败:', error)
      return {
        canLogin: false,
        reason: '系统维护中，请稍后再试',
        status: 'error',
        isExpired: false,
        warningLevel: 'expired'
      }
    }
  }

  /**
   * 根据订阅类型计算使用期限
   */
  calculateSubscriptionPeriod(factory: any, startDate: Date): {
    subscriptionStart?: Date
    subscriptionEnd?: Date
  } {
    if (!factory || factory.isPermanent || factory.subscriptionType === 'permanent') {
      return {} // 永久订阅不需要设置期限
    }

    const start = startDate
    let end: Date

    switch (factory.subscriptionType) {
      case 'trial':
        // 试用期30天
        end = new Date(start.getTime() + 30 * 24 * 60 * 60 * 1000)
        break
      case 'monthly':
        // 按月：30天
        end = new Date(start.getTime() + 30 * 24 * 60 * 60 * 1000)
        break
      case 'quarterly':
        // 按季度：90天
        end = new Date(start.getTime() + 90 * 24 * 60 * 60 * 1000)
        break
      case 'yearly':
        // 按年：365天
        end = new Date(start.getTime() + 365 * 24 * 60 * 60 * 1000)
        break
      default:
        // 默认试用期30天
        end = new Date(start.getTime() + 30 * 24 * 60 * 60 * 1000)
        break
    }

    return {
      subscriptionStart: start,
      subscriptionEnd: end
    }
  }

  /**
   * 按小时统计登录次数
   */
  private async getLoginsByHour(where: unknown): Promise<{ hour: number; count: number }[]> {
    try {
      // 获取最近24小时的登录记录
      const records = await prisma.loginRecord.findMany({
        where: {
          ...where,
          loginTime: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
          }
        },
        select: {
          loginTime: true
        }
      })

      // 按小时分组统计
      const hourCounts: { [key: number]: number } = {}

      // 初始化24小时
      for (let i = 0; i < 24; i++) {
        hourCounts[i] = 0
      }

      // 统计每小时的登录次数
      records.forEach(record => {
        const hour = new Date(record.loginTime).getHours()
        hourCounts[hour]++
      })

      // 转换为数组格式
      return Object.entries(hourCounts).map(([hour, count]) => ({
        hour: parseInt(hour),
        count
      }))
    } catch (error) {
      console.error('❌ 按小时统计登录失败:', error)
      return []
    }
  }

  /**
   * 按天统计登录次数
   */
  private async getLoginsByDay(where: unknown): Promise<{ date: string; count: number }[]> {
    try {
      // 获取最近30天的登录记录
      const records = await prisma.loginRecord.findMany({
        where: {
          ...where,
          loginTime: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 最近30天
          }
        },
        select: {
          loginTime: true
        }
      })

      // 按天分组统计
      const dayCounts: { [key: string]: number } = {}

      // 初始化最近30天
      for (let i = 29; i >= 0; i--) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
        const dateStr = date.toISOString().split('T')[0] // YYYY-MM-DD格式
        dayCounts[dateStr] = 0
      }

      // 统计每天的登录次数
      records.forEach(record => {
        const dateStr = new Date(record.loginTime).toISOString().split('T')[0]
        if (dayCounts.hasOwnProperty(dateStr)) {
          dayCounts[dateStr]++
        }
      })

      // 转换为数组格式
      return Object.entries(dayCounts).map(([date, count]) => ({
        date,
        count
      })).sort((a, b) => a.date.localeCompare(b.date))
    } catch (error) {
      console.error('❌ 按天统计登录失败:', error)
      return []
    }
  }

  /**
   * 获取最活跃用户
   */
  private async getTopLoginUsers(where: unknown): Promise<{ userId: string; userName: string; loginCount: number }[]> {
    try {
      const topUsers = await prisma.loginRecord.groupBy({
        by: ['userId', 'userName'],
        where,
        _count: { userId: true },
        orderBy: { _count: { userId: 'desc' } },
        take: 10
      })

      return topUsers.map(user => ({
        userId: user.userId,
        userName: user.userName,
        loginCount: user._count.userId
      }))
    } catch (error) {
      console.error('❌ 获取最活跃用户失败:', error)
      return []
    }
  }

  /**
   * 找出登录高峰时段
   */
  private findPeakHour(loginsByHour: { hour: number; count: number }[]): number {
    if (loginsByHour.length === 0) return 0

    return loginsByHour.reduce((peak, current) =>
      current.count > peak.count ? current : peak
    ).hour
  }
}

// 统一数据库服务
let dbService: DatabaseService | any

if (typeof window === 'undefined') {
  // 服务端：使用PostgreSQL数据库服务
  dbService = new DatabaseService()
  console.log('✅ PostgreSQL数据库服务初始化完成（服务端）')
} else {
  // 客户端：使用API客户端服务，确保数据一致性
  console.log('🔄 初始化客户端API服务...')

  try {
    // 使用专门的客户端入口文件
    const clientEntryModule = require("./client-entry")
    dbService = clientEntryModule.db
    console.log('✅ 客户端API服务初始化完成')
  } catch (error) {
    console.error('❌ 客户端API服务初始化失败:', error)

    // 创建一个功能完整的回退服务对象
    dbService = {
      verifyAdminPassword: async () => {
        console.warn('⚠️ 使用回退数据库服务 - verifyAdminPassword')
        return false
      },
      updateAdminPassword: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateAdminPassword')
        return false
      },
      verifyFactoryUserPassword: async () => {
        console.warn('⚠️ 使用回退数据库服务 - verifyFactoryUserPassword')
        return false
      },
      getEmployeesByFactoryId: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getEmployeesByFactoryId')
        return []
      },
      updateEmployee: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateEmployee')
        return null
      },
      deleteFactory: async () => {
        console.warn('⚠️ 使用回退数据库服务 - deleteFactory')
        return false
      },
      createEmployee: async () => {
        console.warn('⚠️ 使用回退数据库服务 - createEmployee')
        return null
      },
      authenticateFactoryUser: async () => {
        console.warn('⚠️ 使用回退数据库服务 - authenticateFactoryUser')
        return null
      },
      authenticateAdmin: async () => {
        console.warn('⚠️ 使用回退数据库服务 - authenticateAdmin')
        return null
      },
      getFactories: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getFactories')
        return []
      },
      createFactoryWithAdmin: async () => {
        console.warn('⚠️ 使用回退数据库服务 - createFactoryWithAdmin')
        return null
      },
      getClientsByFactoryId: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getClientsByFactoryId')
        return []
      },
      getOrdersByFactoryId: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getOrdersByFactoryId')
        return []
      },
      createClient: async () => {
        console.warn('⚠️ 使用回退数据库服务 - createClient')
        return null
      },
      deleteClient: async () => {
        console.warn('⚠️ 使用回退数据库服务 - deleteClient')
        return false
      },
      createOrder: async () => {
        console.warn('⚠️ 使用回退数据库服务 - createOrder')
        return null
      },
      getAnnouncements: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getAnnouncements')
        return []
      },
      getAnnouncementsForFactory: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getAnnouncementsForFactory')
        return []
      },
      getUnreadAnnouncementsForFactory: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getUnreadAnnouncementsForFactory')
        return []
      },
      getActiveAnnouncementsForFactory: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getActiveAnnouncementsForFactory')
        return []
      },
      getAnnouncementReadStatus: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getAnnouncementReadStatus')
        return {}
      },
      markAnnouncementAsRead: async () => {
        console.warn('⚠️ 使用回退数据库服务 - markAnnouncementAsRead')
      },
      markAnnouncementAsUnread: async () => {
        console.warn('⚠️ 使用回退数据库服务 - markAnnouncementAsUnread')
      },
      dismissAnnouncement: async () => {
        console.warn('⚠️ 使用回退数据库服务 - dismissAnnouncement')
      },
      createAnnouncement: async () => {
        console.warn('⚠️ 使用回退数据库服务 - createAnnouncement')
        return null
      },
      updateAnnouncement: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateAnnouncement')
        return null
      },
      deleteAnnouncement: async () => {
        console.warn('⚠️ 使用回退数据库服务 - deleteAnnouncement')
        return false
      },
      getSystemSettings: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getSystemSettings')
        return {}
      },
      checkUsernameExists: async () => {
        console.warn('⚠️ 使用回退数据库服务 - checkUsernameExists')
        return false
      },
      checkFactoryCodeExists: async () => {
        console.warn('⚠️ 使用回退数据库服务 - checkFactoryCodeExists')
        return false
      },
      updateClientStatistics: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateClientStatistics')
      },
      updateReferralRewards: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateReferralRewards')
      },
      updateReferrerRewardBalance: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateReferrerRewardBalance')
      },
      updateAllReferralRewards: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateAllReferralRewards')
      },
      calculateReferrerTotalReward: async () => {
        console.warn('⚠️ 使用回退数据库服务 - calculateReferrerTotalReward')
        return 0
      },
      deleteEmployee: async () => {
        console.warn('⚠️ 使用回退数据库服务 - deleteEmployee')
        return false
      },
      toggleEmployeeStatus: async () => {
        console.warn('⚠️ 使用回退数据库服务 - toggleEmployeeStatus')
        return null
      },
      updateAllEmployeeStatistics: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateAllEmployeeStatistics')
      },
      updateOrderPayment: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateOrderPayment')
        return false
      },
      // 股东管理相关方法
      getShareholdersByFactoryId: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getShareholdersByFactoryId')
        return []
      },
      getShareholderById: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getShareholderById')
        return null
      },
      createShareholder: async () => {
        console.warn('⚠️ 使用回退数据库服务 - createShareholder')
        return null
      },
      updateShareholder: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateShareholder')
        return null
      },
      deleteShareholder: async () => {
        console.warn('⚠️ 使用回退数据库服务 - deleteShareholder')
        return false
      },
      addShareholderInvestment: async () => {
        console.warn('⚠️ 使用回退数据库服务 - addShareholderInvestment')
        return { success: false, error: '数据库服务不可用' }
      },
      // 分红管理相关方法
      getDividendsByFactoryId: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getDividendsByFactoryId')
        return []
      },
      getDividendById: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getDividendById')
        return null
      },
      createDividend: async () => {
        console.warn('⚠️ 使用回退数据库服务 - createDividend')
        return null
      },
      updateDividend: async () => {
        console.warn('⚠️ 使用回退数据库服务 - updateDividend')
        return null
      },
      deleteDividend: async () => {
        console.warn('⚠️ 使用回退数据库服务 - deleteDividend')
        return false
      },
      // 登录记录相关方法
      recordLogin: async () => {
        console.warn('⚠️ 使用回退数据库服务 - recordLogin')
        return null
      },
      getLoginRecords: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getLoginRecords')
        return []
      },
      getLoginStats: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getLoginStats')
        return {
          totalLogins: 0,
          successfulLogins: 0,
          failedLogins: 0,
          uniqueUsers: 0,
          averageSessionDuration: 0,
          peakLoginHour: 0,
          loginsByHour: [],
          loginsByDay: [],
          topUsers: []
        }
      },
      getOnlineUsers: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getOnlineUsers')
        return []
      },
      recordLogout: async () => {
        console.warn('⚠️ 使用回退数据库服务 - recordLogout')
        return false
      },
      getRewardUsageRecords: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getRewardUsageRecords')
        return []
      },
      // 股东利润分析相关方法
      getProfitAnalysis: async () => {
        console.warn('⚠️ 使用回退数据库服务 - getProfitAnalysis')
        return {
          data: [],
          summary: {
            totalRevenue: 0,
            totalCosts: 0,
            totalProfit: 0,
            averageProfitMargin: 0,
            bestMonth: '',
            worstMonth: ''
          }
        }
      }
    }
    console.log('⚠️ 使用回退数据库服务')
  }
}

// 导出数据库服务
export const db = dbService

// 导出类型
export type { Order, Factory, FactoryUser, Client }

// 导出Prisma客户端（仅服务端）
export { prisma }
