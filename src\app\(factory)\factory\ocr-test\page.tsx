"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { EnhancedOCRParser } from "@/lib/enhanced-ocr-parser"
import { FactoryRouteGuard } from "@/components/auth/factory-route-guard"
import { DashboardLayout } from "@/components/layout/dashboard-layout"

export default function OCRTestPage() {
  const [inputText, setInputText] = useState("")
  const [results, setResults] = useState<any[]>([])

  const testCases = [
    "26米x135=大厅",
    "26000*135",
    "1920x140mm 办公室 静音处理",
    "19.2x14cm 客厅",
    "1.92x0.14m 会议室",
    "300x1600 客厅 ￥130 2个",
    "01. 1920x140mm 办公室静音",
    "出风口 1920*140 办公室 静音处理 mm",
    "1920乘140毫米 会议室",
    "1920x140 19楼办公室 静音"
  ]

  const handleTest = () => {
    if (!inputText.trim()) return

    const lines = inputText.split('\n').filter(line => line.trim())
    const testResults = lines.map(line => {
      const result = EnhancedOCRParser.parseLineEnhanced(line.trim())
      return {
        input: line.trim(),
        ...result
      }
    })

    setResults(testResults)
  }

  const loadTestCase = (testCase: string) => {
    setInputText(testCase)
  }

  const loadAllTestCases = () => {
    setInputText(testCases.join('\n'))
  }

  return (
    <FactoryRouteGuard>
      <DashboardLayout>
        <div className="container mx-auto p-6 space-y-6">
          <div>
            <h1 className="text-3xl font-bold">OCR增强解析器测试</h1>
            <p className="text-gray-600 mt-2">
              测试基于用户反馈优化的OCR解析算法
            </p>
          </div>

          {/* 输入区域 */}
          <Card>
            <CardHeader>
              <CardTitle>输入测试文本</CardTitle>
              <CardDescription>
                每行一个测试用例，支持各种尺寸格式和单位
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="input">测试文本</Label>
                <Textarea
                  id="input"
                  placeholder="输入要测试的文本，每行一个..."
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  rows={8}
                />
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Button onClick={handleTest} disabled={!inputText.trim()}>
                  开始测试
                </Button>
                <Button variant="outline" onClick={loadAllTestCases}>
                  加载所有测试用例
                </Button>
                <Button variant="outline" onClick={() => setResults([])}>
                  清空结果
                </Button>
              </div>

              {/* 快速测试用例 */}
              <div>
                <Label>快速测试用例</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {testCases.map((testCase, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => loadTestCase(testCase)}
                    >
                      {testCase}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 结果显示 */}
          {results.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>解析结果</CardTitle>
                <CardDescription>
                  显示增强解析器的识别效果
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {results.map((result, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-3">
                      <div>
                        <Label className="text-sm font-medium">输入文本</Label>
                        <p className="text-sm bg-gray-50 p-2 rounded">{result.input}</p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* 尺寸识别结果 */}
                        <div>
                          <Label className="text-sm font-medium">尺寸识别</Label>
                          {result.dimensions ? (
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <Badge variant="default">
                                  {result.dimensions.length}×{result.dimensions.width}mm
                                </Badge>
                                <Badge variant="outline">
                                  {result.dimensions.detectedUnit}
                                </Badge>
                                <Badge variant="secondary">
                                  {(result.dimensions.confidence * 100).toFixed(1)}%
                                </Badge>
                              </div>
                              <p className="text-xs text-gray-600">
                                原始: {result.dimensions.originalText}
                              </p>
                            </div>
                          ) : (
                            <Badge variant="destructive">未识别</Badge>
                          )}
                        </div>

                        {/* 备注清理结果 */}
                        <div>
                          <Label className="text-sm font-medium">备注清理</Label>
                          <div className="space-y-2">
                            <div>
                              <p className="text-sm">
                                <span className="font-medium">清理后:</span> 
                                <span className="ml-2 bg-green-50 px-2 py-1 rounded">
                                  {result.notes.cleanedNotes || '(空)'}
                                </span>
                              </p>
                            </div>
                            {result.notes.removedItems.length > 0 && (
                              <div>
                                <p className="text-xs text-gray-600">移除内容:</p>
                                <div className="flex flex-wrap gap-1">
                                  {result.notes.removedItems.map((item, i) => (
                                    <Badge key={i} variant="outline" className="text-xs">
                                      {item}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                            <Badge variant="secondary" className="text-xs">
                              质量: {(result.notes.confidence * 100).toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 功能说明 */}
          <Card>
            <CardHeader>
              <CardTitle>增强功能说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">🎯 尺寸识别增强</h4>
                  <ul className="text-sm space-y-1 text-gray-600">
                    <li>• 支持等号格式: 26米x135=大厅</li>
                    <li>• 智能单位推断: 无小数=mm, 1位=cm, 2位=m</li>
                    <li>• 中文单位支持: 毫米、厘米、米</li>
                    <li>• 自动单位转换: 统一转为mm</li>
                    <li>• 多种乘号支持: x, ×, *, 乘</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">🧹 备注清理增强</h4>
                  <ul className="text-sm space-y-1 text-gray-600">
                    <li>• 移除尺寸信息: 1920x140mm</li>
                    <li>• 移除等号内容: =大厅</li>
                    <li>• 移除单位信息: mm, cm, m</li>
                    <li>• 移除价格信息: ￥130</li>
                    <li>• 保留有意义内容: 房间、要求等</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    </FactoryRouteGuard>
  )
}
