{"name": "ductcloud-platform", "version": "0.1.0", "private": true, "description": "中央空调风口加工厂SaaS平台 - 多工厂管理与数据运营平台 | ⚠️重要：本项目要求所有交流必须使用中文！", "scripts": {"dev": "next dev -p 3000", "dev:turbo": "next dev --turbopack -p 3000", "dev:80": "next dev -p 80", "dev:clean": "npm run kill-port && npm run dev", "kill-port": "powershell -Command \"$port = Get-NetTCPConnection -LocalPort 80 -ErrorAction SilentlyContinue; if ($port) { Stop-Process -Id $port.OwningProcess -Force; Write-Host '端口80已释放' } else { Write-Host '端口80未被占用' }\"", "build": "next build", "start": "next start", "lint": "next lint", "build:local": "cp .env.local.production .env.production && npm run build", "start:local": "cp .env.local.production .env.production && npm start", "setup:dev": "node scripts/setup-local-dev.js", "setup:prod": "node scripts/setup-production.js", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset", "db:push": "npx prisma db push", "db:generate": "npx prisma generate", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.9.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.468.0", "next": "15.3.3", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.0", "recharts": "^2.13.3", "sharp": "^0.34.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv-cli": "^8.0.0", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}