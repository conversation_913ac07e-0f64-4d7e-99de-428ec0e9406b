'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, Calculator, Users, DollarSign, AlertCircle } from 'lucide-react'
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { safeNumber, safeAmountFormat } from '@/lib/utils/number-utils'
import { calculateTotalReferralReward } from '@/lib/utils/reward-calculator'

interface ClientRewardDetail {
  clientId: string
  clientName: string
  clientPhone: string
  referrerId?: string
  referrerName?: string
  
  // 订单信息
  orders: Array<{
    id: string
    orderNumber?: string
    totalAmount: number
    items: Array<{
      productType: string
      productName: string
      quantity: number
      unitPrice: number
      totalPrice: number
    }>
    createdAt: Date
  }>
  
  // 奖励计算
  calculatedReward: number
  currentReward: number
  rewardBreakdown: Array<{
    orderNumber: string
    orderAmount: number
    rewardType: string
    rewardAmount: number
    calculation: string
  }>
  
  // 状态
  hasDiscrepancy: boolean
  discrepancyAmount: number
}

export default function RewardDetailsPage() {
  const [loading, setLoading] = useState(false)
  const [clientDetails, setClientDetails] = useState<ClientRewardDetail[]>([])
  const [summary, setSummary] = useState({
    totalClients: 0,
    clientsWithRewards: 0,
    totalCalculatedRewards: 0,
    totalCurrentRewards: 0,
    totalDiscrepancy: 0
  })

  // 加载客户奖励详情
  const loadRewardDetails = async () => {
    try {
      setLoading(true)
      setClientDetails([])
      
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        alert('无法获取工厂ID')
        return
      }

      console.log('📊 开始加载客户奖励详情...')

      // 获取所有客户
      const clients = await db.getClientsByFactoryId(factoryId)
      console.log(`👥 找到 ${clients.length} 个客户`)

      // 获取所有订单
      const allOrders = await db.getOrdersByFactoryId(factoryId)
      console.log(`📦 找到 ${allOrders.length} 个订单`)

      const details: ClientRewardDetail[] = []
      let totalCalculatedRewards = 0
      let totalCurrentRewards = 0
      let clientsWithRewards = 0

      // 只处理有推荐人的客户
      const clientsWithReferrers = clients.filter(client => client.referrerId)
      console.log(`🔗 其中 ${clientsWithReferrers.length} 个客户有推荐人`)

      for (const client of clientsWithReferrers) {
        // 获取客户的订单
        const clientOrders = allOrders.filter(order => order.clientId === client.id)
        
        if (clientOrders.length === 0) {
          console.log(`ℹ️ 客户 ${client.name} 无订单，跳过`)
          continue
        }

        // 计算应得奖励
        const rewardResult = calculateTotalReferralReward(clientOrders, factoryId)
        const calculatedReward = rewardResult.totalReward

        // 获取当前数据库中的奖励
        const referrer = clients.find(c => c.id === client.referrerId)
        const currentReward = safeNumber(referrer?.referralReward || 0)

        // 生成奖励明细
        const rewardBreakdown = clientOrders.map(order => {
          const orderReward = calculateTotalReferralReward([order], factoryId)
          
          // 分析订单类型 - 使用统一的高端风口判断逻辑
          const hasHighEndProducts = order.items.some(item => {
            // 使用统一的高端风口识别函数
            return isPremiumVent(item.productType, item.productName)
          })
          
          const rewardType = hasHighEndProducts ? '高端风口奖励' : '普通风口奖励'

          // 生成详细的计算说明
          let calculation = ''
          if (hasHighEndProducts) {
            if (orderReward.breakdown.premiumVentDetails.length > 0) {
              const details = orderReward.breakdown.premiumVentDetails.map(detail =>
                `${detail.tier}: ¥${safeAmountFormat(detail.rewardAmount)}`
              ).join(', ')
              calculation = `高端风口固定奖励: ${details}`
            } else {
              calculation = '高端风口固定奖励计算'
            }
          } else {
            const rate = (orderReward.breakdown.regularVentRate * 100).toFixed(1)
            calculation = `${safeAmountFormat(orderReward.breakdown.regularVentAmount)} × ${rate}% = ${safeAmountFormat(orderReward.totalReward)}`
          }

          return {
            orderNumber: order.orderNumber || order.id,
            orderAmount: order.totalAmount,
            rewardType,
            rewardAmount: orderReward.totalReward,
            calculation
          }
        })

        const hasDiscrepancy = Math.abs(calculatedReward - currentReward) > 0.01
        const discrepancyAmount = calculatedReward - currentReward

        details.push({
          clientId: client.id,
          clientName: client.name,
          clientPhone: client.phone,
          referrerId: client.referrerId,
          referrerName: client.referrerName || referrer?.name || '未知',
          orders: clientOrders.map(order => ({
            id: order.id,
            orderNumber: order.orderNumber,
            totalAmount: order.totalAmount,
            items: order.items,
            createdAt: order.createdAt
          })),
          calculatedReward,
          currentReward,
          rewardBreakdown,
          hasDiscrepancy,
          discrepancyAmount
        })

        totalCalculatedRewards += calculatedReward
        totalCurrentRewards += currentReward
        if (calculatedReward > 0) clientsWithRewards++
      }

      // 按推荐人分组显示
      const groupedByReferrer = details.reduce((groups, detail) => {
        const referrerId = detail.referrerId!
        if (!groups[referrerId]) {
          groups[referrerId] = {
            referrerName: detail.referrerName,
            clients: [],
            totalCalculated: 0,
            totalCurrent: 0
          }
        }
        groups[referrerId].clients.push(detail)
        groups[referrerId].totalCalculated += detail.calculatedReward
        groups[referrerId].totalCurrent += detail.currentReward
        return groups
      }, {} as Record<string, any>)

      console.log('📊 奖励详情分组:', groupedByReferrer)

      setClientDetails(details)
      setSummary({
        totalClients: clients.length,
        clientsWithRewards,
        totalCalculatedRewards,
        totalCurrentRewards,
        totalDiscrepancy: totalCalculatedRewards - totalCurrentRewards
      })

      console.log('✅ 奖励详情加载完成')

    } catch (error) {
      console.error('❌ 加载奖励详情失败:', error)
      alert('加载失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRewardDetails()
  }, [])

  // 按推荐人分组数据
  const groupedData = clientDetails.reduce((groups, detail) => {
    const referrerId = detail.referrerId!
    if (!groups[referrerId]) {
      groups[referrerId] = {
        referrerName: detail.referrerName,
        referrerId,
        clients: [],
        totalCalculated: 0,
        totalCurrent: 0,
        totalDiscrepancy: 0
      }
    }
    groups[referrerId].clients.push(detail)
    groups[referrerId].totalCalculated += detail.calculatedReward
    groups[referrerId].totalCurrent += detail.currentReward
    groups[referrerId].totalDiscrepancy += detail.discrepancyAmount
    return groups
  }, {} as Record<string, any>)

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">客户推荐奖励计算明细</h1>
            <p className="text-gray-600">详细查看每个客户的推荐奖励计算过程</p>
          </div>
          <Button onClick={loadRewardDetails} disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                加载中...
              </>
            ) : (
              <>
                <Calculator className="h-4 w-4 mr-2" />
                刷新数据
              </>
            )}
          </Button>
        </div>

        {/* 汇总信息 */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总客户数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalClients}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">有奖励客户</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.clientsWithRewards}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">应得奖励</CardTitle>
              <DollarSign className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                ¥{safeAmountFormat(summary.totalCalculatedRewards)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">当前奖励</CardTitle>
              <DollarSign className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                ¥{safeAmountFormat(summary.totalCurrentRewards)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">差额</CardTitle>
              <AlertCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${summary.totalDiscrepancy > 0 ? 'text-red-600' : 'text-green-600'}`}>
                ¥{safeAmountFormat(Math.abs(summary.totalDiscrepancy))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 详细列表 */}
        <div className="space-y-6">
          {Object.values(groupedData).map((group: unknown) => (
            <Card key={group.referrerId}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>推荐人: {group.referrerName}</span>
                  <div className="flex space-x-4 text-sm">
                    <span className="text-green-600">应得: ¥{safeAmountFormat(group.totalCalculated)}</span>
                    <span className="text-blue-600">当前: ¥{safeAmountFormat(group.totalCurrent)}</span>
                    {group.totalDiscrepancy !== 0 && (
                      <span className="text-red-600">差额: ¥{safeAmountFormat(Math.abs(group.totalDiscrepancy))}</span>
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {group.clients.map((detail: ClientRewardDetail) => (
                    <div key={detail.clientId} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-semibold">{detail.clientName}</h4>
                          <p className="text-sm text-gray-600">{detail.clientPhone}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm">
                            <span className="text-green-600">应得: ¥{safeAmountFormat(detail.calculatedReward)}</span>
                          </div>
                          {detail.hasDiscrepancy && (
                            <Badge variant="destructive" className="mt-1">
                              差额: ¥{safeAmountFormat(Math.abs(detail.discrepancyAmount))}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* 订单明细 */}
                      <div className="space-y-2">
                        <h5 className="font-medium text-sm">订单明细:</h5>
                        {detail.rewardBreakdown.map((breakdown, index) => (
                          <div key={index} className="bg-gray-50 p-3 rounded text-sm">
                            <div className="flex justify-between items-center">
                              <span className="font-medium">{breakdown.orderNumber}</span>
                              <span className="text-green-600">¥{safeAmountFormat(breakdown.rewardAmount)}</span>
                            </div>
                            <div className="text-gray-600 mt-1">
                              <div>订单金额: ¥{safeAmountFormat(breakdown.orderAmount)}</div>
                              <div>奖励类型: {breakdown.rewardType}</div>
                              <div>计算方式: {breakdown.calculation}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {clientDetails.length === 0 && !loading && (
          <Card>
            <CardContent className="p-8 text-center">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
              <p className="text-gray-600">
                没有找到有推荐奖励的客户
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
