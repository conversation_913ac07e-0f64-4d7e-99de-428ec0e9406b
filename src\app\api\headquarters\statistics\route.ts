/**
 * 🇨🇳 风口云平台 - 总部统计数据API
 */

import { NextRequest, NextResponse } from 'next/server'
import { dataSyncService } from '@/lib/services/data-sync'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    console.log('📊 获取总部统计数据...')

    // 获取总部统计数据
    const headquartersStats = await dataSyncService.getHeadquartersStatistics()
    
    // 获取所有工厂的详细统计
    const allFactoriesStats = await dataSyncService.getAllFactoriesStatistics()
    
    // 获取工厂列表
    const factories = await db.getFactories()
    
    // 构建详细的工厂统计信息
    const factoryDetails: unknown[] = []
    for (const factory of factories) {
      try {
        const factoryStats = await dataSyncService.getFactoryStatistics(factory.id)
        factoryDetails.push({
          id: factory.id,
          name: factory.name,
          address: factory.address,
          status: factory.status,
          phone: factory.phone,
          createdAt: factory.createdAt,
          statistics: factoryStats
        })
      } catch (error) {
        console.warn(`获取工厂 ${factory.name} 统计失败:`, error)
        factoryDetails.push({
          id: factory.id,
          name: factory.name,
          address: factory.address,
          status: factory.status,
          phone: factory.phone,
          createdAt: factory.createdAt,
          statistics: {
            totalClients: 0,
            totalOrders: 0,
            monthlyRevenue: 0,
            activeClients: 0,
            pendingOrders: 0
          },
          error: (error as Error).message
        })
      }
    }

    // 计算额外的统计信息
    const currentMonth = new Date().getMonth()
    const currentYear = new Date().getFullYear()
    
    const additionalStats = {
      averageClientsPerFactory: factories.length > 0 ? 
        Math.round(headquartersStats.totalClients / factories.length) : 0,
      averageOrdersPerFactory: factories.length > 0 ? 
        Math.round(headquartersStats.totalOrders / factories.length) : 0,
      averageRevenuePerFactory: factories.length > 0 ? 
        Math.round(headquartersStats.totalRevenue / factories.length) : 0,
      factoryUtilizationRate: factories.length > 0 ? 
        Math.round((headquartersStats.activeFactories / factories.length) * 100) : 0
    }

    const response = {
      success: true,
      data: {
        summary: headquartersStats,
        additional: additionalStats,
        factories: factoryDetails,
        lastUpdated: new Date().toISOString()
      },
      message: '总部统计数据获取成功'
    }

    console.log('✅ 总部统计数据获取成功')
    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ 获取总部统计数据失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '获取总部统计数据失败',
      message: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 刷新总部统计数据...')

    const body = await request.json()
    const { forceRefresh = false } = body

    if (forceRefresh) {
      // 强制刷新所有工厂的统计数据
      const factories = await db.getFactories()
      
      for (const factory of factories) {
        try {
          // 这里可以添加强制刷新逻辑
          console.log(`🔄 刷新工厂 ${factory.name} 的统计数据...`)
        } catch (error) {
          console.warn(`刷新工厂 ${factory.name} 统计失败:`, error)
        }
      }
    }

    // 重新获取统计数据
    const headquartersStats = await dataSyncService.getHeadquartersStatistics()

    const response = {
      success: true,
      data: headquartersStats,
      message: '总部统计数据刷新成功'
    }

    console.log('✅ 总部统计数据刷新成功')
    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ 刷新总部统计数据失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '刷新总部统计数据失败',
      message: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
