"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { ThemeSelector } from "@/components/theme/theme-selector"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { useAuthStore } from "@/lib/store/auth"
import { getCurrentUser, getCurrentFactoryId } from "@/lib/utils/factory"
import { extractPinyinInitials, checkPrefixConflict } from "@/lib/utils/factory-settings"
import {
  Settings,
  Factory,
  User,
  Shield,
  Database,
  Palette,
  Bell,
  Save,
  RefreshCw,
  Download,
  Upload,
  AlertTriangle,
  Calendar,
  CheckCircle,
  Crown,
  Pause,
  Info,
  Clock,
  XCircle
} from "lucide-react"
import PrefixSelector from "@/components/PrefixSelector"
import { api } from "@/lib/api"

interface SubscriptionData {
  factory: {
    id: string
    name: string
    code: string
    status: 'active' | 'inactive' | 'suspended' | 'expired'
    subscriptionType: 'trial' | 'monthly' | 'quarterly' | 'yearly' | 'permanent'
    subscriptionStart?: string | null
    subscriptionEnd?: string | null
    firstLoginAt?: string | null
    isPermanent: boolean
    suspendedAt?: string | null
    suspendedReason?: string | null
  }
  statusCheck: {
    canLogin: boolean
    reason?: string
    status: string
    isExpired: boolean
    remainingDays?: number
    remainingHours?: number
    warningLevel: 'none' | 'warning' | 'critical' | 'expired'
  }
  remainingDays?: number | null
}

interface FactorySettings {
  // 基本信息
  factoryName: string
  factoryCode: string
  address: string
  contactPerson: string
  contactPhone: string
  email: string

  // 业务设置
  defaultUnitPrices: {
    // 常规风口 (元/㎡)
    double_white_outlet: number
    double_black_outlet: number
    white_black_bottom_outlet: number
    white_black_bottom_return: number
    white_return: number
    black_return: number
    white_linear: number
    black_linear: number
    white_linear_return: number
    black_linear_return: number
    maintenance: number
    // 高端风口 (元/m) - 统一命名
    high_end_white_outlet: number
    high_end_black_outlet: number
    high_end_white_return: number
    high_end_black_return: number
    arrow_outlet: number
    arrow_return: number
    claw_outlet: number
    claw_return: number
    black_white_dual_outlet: number
    black_white_dual_return: number
    wood_grain_outlet: number
    wood_grain_return: number
    white_putty_outlet: number
    white_putty_return: number
    black_putty_outlet: number
    black_putty_return: number
    white_gypsum_outlet: number
    white_gypsum_return: number
    black_gypsum_outlet: number
    black_gypsum_return: number
  }

  // 客户奖励设置
  rewardSettings: {
    // 普通风口奖励（按比例）
    regularVentRewards: {
      baseRate: number        // 基础奖励比例 (默认2%)
      tier1Amount: number     // 第一档金额门槛 (默认10万)
      tier1Rate: number       // 第一档奖励比例 (默认3%)
      tier2Amount: number     // 第二档金额门槛 (默认20万)
      tier2Rate: number       // 第二档奖励比例 (默认4%)
      maxRate: number         // 最高奖励比例 (默认5%)
    }

    // 高端风口奖励（固定金额）
    premiumVentRewards: {
      baseAmount: number      // 基础奖励金额 (默认30元)
      tier1Amount: number     // 第一档订单门槛 (默认1万)
      tier1Reward: number     // 第一档奖励金额 (默认50元)
      tier2Amount: number     // 第二档订单门槛 (默认2万)
      tier2Reward: number     // 第二档奖励金额 (默认100元)
      tier3Amount: number     // 第三档订单门槛 (默认5万)
      tier3Reward: number     // 第三档奖励金额 (默认500元)
    }

    // 奖励使用规则
    rewardRules: {
      requireFullPayment: boolean  // 是否要求结清货款才能使用奖励
      allowCashOut: boolean        // 是否允许兑现提取
      allowOrderDeduction: boolean // 是否允许抵扣订单
    }
  }

  // 系统设置
  orderNumberPrefix: string
  autoBackup: boolean
  backupFrequency: string

  // 前缀生成配置
  prefixMinLength?: number
  prefixMaxLength?: number
  prefixIncludeNumbers?: boolean
  prefixPreferLonger?: boolean

  // 通知设置
  emailNotifications: boolean
  smsNotifications: boolean
  orderStatusNotifications: boolean

  // 界面设置
  theme: string
  language: string
  dateFormat: string
  currencyFormat: string
}

// 订阅信息组件
function SubscriptionInfoCard() {
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchSubscriptionInfo = async () => {
    try {
      setLoading(true)
      setError(null)

      const factoryId = getCurrentFactoryId()
      console.log('🏭 获取到的工厂ID:', factoryId)

      if (!factoryId) {
        throw new Error('无法获取工厂ID')
      }

      console.log('📡 开始调用订阅信息API...')
      const response = await api.get(`/api/factory/subscription?factoryId=${factoryId}`)

      console.log('📊 订阅信息API响应:', response)
      console.log('📊 响应数据类型:', typeof response)
      console.log('📊 响应数据结构:', JSON.stringify(response, null, 2))

      if (response && response.success && response.data) {
        // 处理双重嵌套的数据结构
        const actualData = response.data.data || response.data
        console.log('✅ 设置订阅数据:', actualData)
        setSubscriptionData(actualData)
      } else {
        console.error('❌ API响应错误:', response)
        throw new Error('获取订阅信息失败')
      }
    } catch (err) {
      console.error('❌ 获取订阅信息失败:', err)
      setError(err instanceof Error ? err.message : '获取订阅信息失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    console.log('🔄 SubscriptionInfoCard useEffect 执行')
    fetchSubscriptionInfo()
  }, [])

  const getSubscriptionTypeText = (type: string) => {
    const typeMap = {
      trial: '试用期',
      monthly: '月度订阅',
      quarterly: '季度订阅',
      yearly: '年度订阅',
      permanent: '永久订阅'
    }
    return typeMap[type as keyof typeof typeMap] || type
  }

  const getStatusBadge = (data: SubscriptionData | null) => {
    if (!data || !data.factory) {
      return <Badge className="bg-gray-100 text-gray-800 border-gray-200"><Clock className="h-3 w-3 mr-1" />加载中...</Badge>
    }

    if (data.factory.isPermanent || data.factory.subscriptionType === 'permanent') {
      return <Badge className="bg-purple-100 text-purple-800 border-purple-200"><Crown className="h-3 w-3 mr-1" />永久订阅</Badge>
    }

    if (data.factory.status === 'suspended') {
      return <Badge variant="secondary"><Pause className="h-3 w-3 mr-1" />已暂停</Badge>
    }

    if (data.statusCheck?.isExpired) {
      return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />已过期</Badge>
    }

    if (data.statusCheck?.warningLevel === 'warning' || data.statusCheck?.warningLevel === 'critical') {
      return <Badge className="bg-orange-100 text-orange-800 border-orange-200"><AlertTriangle className="h-3 w-3 mr-1" />即将过期</Badge>
    }

    return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="h-3 w-3 mr-1" />正常</Badge>
  }

  const formatDate = (dateStr: string | null | undefined) => {
    if (!dateStr) return '未设置'
    return new Date(dateStr).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getRemainingTimeText = (data: SubscriptionData | null) => {
    if (!data || !data.factory) {
      return '加载中...'
    }

    if (data.factory.isPermanent || data.factory.subscriptionType === 'permanent') {
      return '永久有效'
    }

    if (data.statusCheck?.isExpired) {
      return '已过期'
    }

    const remainingDays = data.remainingDays ?? data.statusCheck?.remainingDays
    if (remainingDays !== null && remainingDays !== undefined) {
      if (remainingDays <= 0) {
        return '已过期'
      } else if (remainingDays === 1) {
        return '剩余1天'
      } else {
        return `剩余${remainingDays}天`
      }
    }

    if (data.factory.subscriptionType === 'trial' && !data.factory.firstLoginAt) {
      return '试用期未开始'
    }

    return '计算中...'
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            订阅信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">加载中...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            订阅信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchSubscriptionInfo} variant="outline">
              重试
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  console.log('🎯 组件渲染状态:', { loading, error, subscriptionData })
  console.log('📋 subscriptionData 详细信息:', subscriptionData)
  console.log('🏭 subscriptionData.factory:', subscriptionData?.factory)

  // 显示加载状态
  if (loading) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            订阅信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">加载中...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 显示错误状态
  if (error) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            订阅信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchSubscriptionInfo} variant="outline">
              重试
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 如果没有订阅数据，显示暂无信息
  if (!subscriptionData || !subscriptionData.factory) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            订阅信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-gray-500">暂无订阅信息</p>
            <Button
              onClick={fetchSubscriptionInfo}
              variant="outline"
              size="sm"
            >
              刷新
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  console.log('🎨 正在渲染订阅信息卡片')
  console.log('📊 订阅类型:', subscriptionData.factory?.subscriptionType)
  console.log('⏰ 剩余天数:', subscriptionData.remainingDays)

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calendar className="h-5 w-5 mr-2" />
          订阅信息
        </CardTitle>
        <CardDescription>查看当前工厂的订阅状态和使用期限</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 调试信息 */}
        <div className="bg-gray-50 p-3 rounded text-xs">
          <div>工厂名称: {subscriptionData.factory?.name}</div>
          <div>订阅类型: {subscriptionData.factory?.subscriptionType}</div>
          <div>剩余天数: {subscriptionData.remainingDays}</div>
          <div>状态: {subscriptionData.factory?.status}</div>
        </div>

        {/* 订阅状态 */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">订阅状态</span>
          {getStatusBadge(subscriptionData)}
        </div>

        {/* 订阅类型 */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">订阅类型</span>
          <span className="text-sm text-gray-900 font-medium">
            {getSubscriptionTypeText(subscriptionData.factory?.subscriptionType || '')}
          </span>
        </div>

        {/* 剩余时间 */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">剩余时间</span>
          <span className={`text-sm font-medium ${
            subscriptionData.statusCheck?.isExpired ? 'text-red-600' :
            subscriptionData.statusCheck?.warningLevel === 'warning' || subscriptionData.statusCheck?.warningLevel === 'critical' ? 'text-orange-600' :
            'text-green-600'
          }`}>
            {getRemainingTimeText(subscriptionData)}
          </span>
        </div>

        {/* 刷新按钮 */}
        <div className="pt-4 border-t">
          <Button
            onClick={fetchSubscriptionInfo}
            variant="outline"
            size="sm"
            className="w-full"
          >
            <Clock className="h-4 w-4 mr-2" />
            刷新订阅信息
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default function FactorySettingsPage() {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")
  const { user } = useAuthStore()
  
  const [settings, setSettings] = useState<FactorySettings>({
    factoryName: "",
    factoryCode: "",
    address: "",
    contactPerson: "",
    contactPhone: "",
    email: "",
    defaultUnitPrices: {
      // 常规风口 (元/㎡)
      double_white_outlet: 150,
      double_black_outlet: 150,
      white_black_bottom_outlet: 150,
      white_black_bottom_return: 150,
      white_return: 150,
      black_return: 150,
      white_linear: 150,
      black_linear: 150,
      white_linear_return: 150,
      black_linear_return: 150,
      maintenance: 150,
      // 高端风口 (元/m) - 统一命名
      high_end_white_outlet: 80,
      high_end_black_outlet: 80,
      high_end_white_return: 80,
      high_end_black_return: 80,
      arrow_outlet: 90,
      arrow_return: 90,
      claw_outlet: 100,
      claw_return: 100,
      black_white_dual_outlet: 85,
      black_white_dual_return: 85,
      wood_grain_outlet: 120,
      wood_grain_return: 120,
      white_putty_outlet: 110,
      white_putty_return: 110,
      black_putty_outlet: 110,
      black_putty_return: 110,
      white_gypsum_outlet: 130,
      white_gypsum_return: 130,
      black_gypsum_outlet: 130,
      black_gypsum_return: 130
    },
    rewardSettings: {
      regularVentRewards: {
        baseRate: 0.02,        // 2%
        tier1Amount: 100000,   // 10万
        tier1Rate: 0.03,       // 3%
        tier2Amount: 200000,   // 20万
        tier2Rate: 0.04,       // 4%
        maxRate: 0.05          // 5%
      },
      premiumVentRewards: {
        baseAmount: 30,        // 30元
        tier1Amount: 10000,    // 1万
        tier1Reward: 50,       // 50元
        tier2Amount: 20000,    // 2万
        tier2Reward: 100,      // 100元
        tier3Amount: 50000,    // 5万
        tier3Reward: 500       // 500元
      },
      rewardRules: {
        requireFullPayment: true,     // 要求结清货款
        allowCashOut: true,           // 允许兑现
        allowOrderDeduction: true     // 允许抵扣
      }},
    orderNumberPrefix: "ORD",
    autoBackup: true,
    backupFrequency: "daily",
    emailNotifications: true,
    smsNotifications: false,
    orderStatusNotifications: true,
    theme: "light",
    language: "zh-CN",
    dateFormat: "YYYY-MM-DD",
    currencyFormat: "CNY"
  })

  useEffect(() => {
    loadFactorySettings()
  }, [])

  // 获取风口类型显示名称
  const getVentTypeDisplayName = (type: string): string => {
    const displayNames: Record<string, string> = {
      // 常规风口
      double_white_outlet: '双层白色出风口',
      double_black_outlet: '双层黑色出风口',
      white_black_bottom_outlet: '白色黑底出风口',
      white_black_bottom_return: '白色黑底回风口',
      white_return: '白色回风口',
      black_return: '黑色回风口',
      white_linear: '白色线型风口',
      black_linear: '黑色线型风口',
      white_linear_return: '白色线型回风口',
      black_linear_return: '黑色线型回风口',
      maintenance: '检修口',
      // 高端风口 - 统一命名
      high_end_white_outlet: '高端白色出风口',
      high_end_black_outlet: '高端黑色出风口',
      high_end_white_return: '高端白色回风口',
      high_end_black_return: '高端黑色回风口',
      arrow_outlet: '箭型出风口',
      arrow_return: '箭型回风口',
      claw_outlet: '爪型出风口',
      claw_return: '爪型回风口',
      black_white_dual_outlet: '黑白双色出风口',
      black_white_dual_return: '黑白双色回风口',
      wood_grain_outlet: '木纹出风口',
      wood_grain_return: '木纹回风口',
      white_putty_outlet: '白色腻子粉出风口',
      white_putty_return: '白色腻子粉回风口',
      black_putty_outlet: '黑色腻子粉出风口',
      black_putty_return: '黑色腻子粉回风口',
      white_gypsum_outlet: '白色石膏板出风口',
      white_gypsum_return: '白色石膏板回风口',
      black_gypsum_outlet: '黑色石膏板出风口',
      black_gypsum_return: '黑色石膏板回风口'
    }
    return displayNames[type] || type
  }

  // 深度合并设置对象，确保所有必需字段都存在
  const mergeSettings = (savedSettings: unknown, defaultSettings: FactorySettings): FactorySettings => {
    const merged = { ...defaultSettings }

    // 基本信息字段 - 优先使用默认设置中的中文名称，除非用户明确修改过
    // 工厂名称：如果保存的设置中有合理的中文名称，则使用；否则使用默认的中文名称
    if (savedSettings.factoryName &&
        savedSettings.factoryName !== "演示工厂" &&
        /[\u4e00-\u9fa5]/.test(savedSettings.factoryName)) {
      merged.factoryName = savedSettings.factoryName
    } // 否则保持默认的中文名称

    if (savedSettings.factoryCode) merged.factoryCode = savedSettings.factoryCode
    if (savedSettings.address) merged.address = savedSettings.address
    if (savedSettings.contactPerson) merged.contactPerson = savedSettings.contactPerson
    if (savedSettings.contactPhone) merged.contactPhone = savedSettings.contactPhone
    if (savedSettings.email) merged.email = savedSettings.email

    // 默认单价设置
    if (savedSettings.defaultUnitPrices) {
      merged.defaultUnitPrices = {
        ...defaultSettings.defaultUnitPrices,
        ...savedSettings.defaultUnitPrices
      }
    }

    // 奖励设置 - 确保完整性
    if (savedSettings.rewardSettings) {
      merged.rewardSettings = {
        regularVentRewards: {
          ...defaultSettings.rewardSettings.regularVentRewards,
          ...(savedSettings.rewardSettings.regularVentRewards || {})
        },
        premiumVentRewards: {
          ...defaultSettings.rewardSettings.premiumVentRewards,
          ...(savedSettings.rewardSettings.premiumVentRewards || {})
        },
        rewardRules: {
          ...defaultSettings.rewardSettings.rewardRules,
          ...(savedSettings.rewardSettings.rewardRules || {})
        }
      }
    }

    // 其他设置
    if (savedSettings.orderNumberPrefix) merged.orderNumberPrefix = savedSettings.orderNumberPrefix
    if (typeof savedSettings.autoBackup === 'boolean') merged.autoBackup = savedSettings.autoBackup
    if (savedSettings.backupFrequency) merged.backupFrequency = savedSettings.backupFrequency
    if (typeof savedSettings.emailNotifications === 'boolean') merged.emailNotifications = savedSettings.emailNotifications
    if (typeof savedSettings.smsNotifications === 'boolean') merged.smsNotifications = savedSettings.smsNotifications
    if (typeof savedSettings.orderStatusNotifications === 'boolean') merged.orderStatusNotifications = savedSettings.orderStatusNotifications
    if (savedSettings.theme) merged.theme = savedSettings.theme
    if (savedSettings.language) merged.language = savedSettings.language
    if (savedSettings.dateFormat) merged.dateFormat = savedSettings.dateFormat
    if (savedSettings.currencyFormat) merged.currencyFormat = savedSettings.currencyFormat

    return merged
  }

  const loadFactorySettings = async () => {
    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()

      if (factoryId) {
        // 尝试从API获取设置
        try {
          const response = await fetch(`/api/factory/${factoryId}/settings`)
          if (response.ok) {
            const data = await response.json()
            console.log('✅ 从API加载工厂设置成功:', data.settings)
            console.log('✅ 工厂基本信息:', data.factory)

            // 直接使用API返回的设置，这些设置已经包含了真实的工厂信息
            setSettings(data.settings)

            // 如果有工厂基本信息，可以在这里进行额外处理
            if (data.factory) {
              console.log('🏭 工厂信息:', {
                name: data.factory.name,
                code: data.factory.code,
                owner: data.factory.ownerName
              })
            }
            return
          }
        } catch (apiError) {
          console.warn('⚠️ API加载失败，使用默认设置:', apiError)
        }

        // 备用方案：使用默认设置
        const defaultSettings = getDefaultSettings(factoryId)
        setSettings(defaultSettings)
        console.log('✅ 使用默认工厂设置:', defaultSettings)
      }
    } catch (error) {
      console.error('❌ 加载工厂设置失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getDefaultSettings = (factoryId: string): FactorySettings => {
    // 智能生成订单号前缀
    const generateSmartPrefix = (id: string, name: string): string => {
      // 已知工厂的特殊配置
      if (id === 'lin001') return "NNJGC"
      if (id === 'lzfxc' || id === 'LZFXC') return "LZFXC"

      // 尝试从工厂名称提取拼音首字母
      try {
        const pinyinPrefix = extractPinyinInitials(name)
        if (pinyinPrefix.length >= 2) {
          return pinyinPrefix
        }
      } catch (error) {
        console.warn('⚠️ 拼音提取失败，使用备用方案:', error)
      }

      // 备用方案：基于工厂ID生成
      const cleanId = id.toUpperCase().replace(/[^A-Z0-9]/g, '')
      if (cleanId.length >= 3) {
        return cleanId.substring(0, 6)
      }

      return "JGC" // 最终备用
    }

    // 根据工厂ID设置默认值
    const factoryName = factoryId === 'lin001' ? "南宁加工厂" :
                       factoryId === 'lzfxc' || factoryId === 'LZFXC' ? "柳州风向厂" :
                       "演示工厂"

    const smartPrefix = generateSmartPrefix(factoryId, factoryName)

    return {
      factoryName,
      factoryCode: factoryId === 'lin001' ? "LIN001" :
                   factoryId === 'lzfxc' || factoryId === 'LZFXC' ? "LZFXC" :
                   "DEMO001",
      address: factoryId === 'lin001' ? "广西南宁市" :
               factoryId === 'lzfxc' || factoryId === 'LZFXC' ? "广西柳州市" :
               "演示地址",
      contactPerson: factoryId === 'lin001' ? "林经理" :
                     factoryId === 'lzfxc' || factoryId === 'LZFXC' ? "厂长" :
                     "演示联系人",
      contactPhone: "13800138000",
      email: factoryId === 'lin001' ? "<EMAIL>" :
             factoryId === 'lzfxc' || factoryId === 'LZFXC' ? "<EMAIL>" :
             "<EMAIL>",
      defaultUnitPrices: {
        // 常规风口 (元/㎡)
        double_white_outlet: 150,
        double_black_outlet: 150,
        white_black_bottom_outlet: 150,
        white_black_bottom_return: 150,
        white_return: 150,
        black_return: 150,
        white_linear: 150,
        black_linear: 150,
        white_linear_return: 150,
        black_linear_return: 150,
        maintenance: 150,
        // 高端风口 (元/m) - 统一命名
        high_end_white_outlet: 80,
        high_end_black_outlet: 80,
        high_end_white_return: 80,
        high_end_black_return: 80,
        arrow_outlet: 90,
        arrow_return: 90,
        claw_outlet: 100,
        claw_return: 100,
        black_white_dual_outlet: 85,
        black_white_dual_return: 85,
        wood_grain_outlet: 120,
        wood_grain_return: 120,
        white_putty_outlet: 110,
        white_putty_return: 110,
        black_putty_outlet: 110,
        black_putty_return: 110,
        white_gypsum_outlet: 130,
        white_gypsum_return: 130,
        black_gypsum_outlet: 130,
        black_gypsum_return: 130
      },
      rewardSettings: {
        regularVentRewards: {
          baseRate: 0.02,        // 2%
          tier1Amount: 100000,   // 10万
          tier1Rate: 0.03,       // 3%
          tier2Amount: 200000,   // 20万
          tier2Rate: 0.04,       // 4%
          maxRate: 0.05          // 5%
        },
        premiumVentRewards: {
          baseAmount: 30,        // 30元
          tier1Amount: 10000,    // 1万
          tier1Reward: 50,       // 50元
          tier2Amount: 20000,    // 2万
          tier2Reward: 100,      // 100元
          tier3Amount: 50000,    // 5万
          tier3Reward: 500       // 500元
        },
        rewardRules: {
          requireFullPayment: true,     // 要求结清货款
          allowCashOut: true,           // 允许兑现
          allowOrderDeduction: true     // 允许抵扣
        }},
      orderNumberPrefix: smartPrefix,
      autoBackup: true,
      backupFrequency: "daily",
      emailNotifications: true,
      smsNotifications: false,
      orderStatusNotifications: true,
      theme: "light",
      language: "zh-CN",
      dateFormat: "YYYY-MM-DD",
      currencyFormat: "CNY"
    }
  }

  const validateSettings = (settings: FactorySettings): string[] => {
    const errors: string[] = []

    if (!settings.factoryName.trim()) {
      errors.push('工厂名称不能为空')
    }

    if (!settings.factoryCode.trim()) {
      errors.push('工厂编码不能为空')
    }

    if (!settings.address.trim()) {
      errors.push('工厂地址不能为空')
    }

    if (!settings.contactPerson.trim()) {
      errors.push('联系人不能为空')
    }

    if (!settings.contactPhone.trim()) {
      errors.push('联系电话不能为空')
    } else if (!/^1[3-9]\d{9}$/.test(settings.contactPhone.replace(/\D/g, ''))) {
      errors.push('联系电话格式不正确')
    }

    if (settings.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(settings.email)) {
      errors.push('邮箱格式不正确')
    }

    // 验证常规风口价格
    const regularVentTypes = [
      'double_white_outlet', 'double_black_outlet', 'white_black_bottom_outlet', 'white_black_bottom_return',
      'white_return', 'black_return', 'white_linear', 'black_linear',
      'white_linear_return', 'black_linear_return', 'maintenance'
    ]

    for (const type of regularVentTypes) {
      if (settings.defaultUnitPrices[type] <= 0) {
        errors.push(`${getVentTypeDisplayName(type)}单价必须大于0`)
      }
    }

    // 验证高端风口价格
    const premiumVentTypes = [
      'high_end_white_outlet', 'high_end_black_outlet', 'high_end_white_return', 'high_end_black_return',
      'arrow_outlet', 'arrow_return', 'claw_outlet', 'claw_return',
      'black_white_dual_outlet', 'black_white_dual_return', 'wood_grain_outlet', 'wood_grain_return',
      'white_putty_outlet', 'white_putty_return', 'black_putty_outlet', 'black_putty_return',
      'white_gypsum_outlet', 'white_gypsum_return', 'black_gypsum_outlet', 'black_gypsum_return'
    ]

    for (const type of premiumVentTypes) {
      if (settings.defaultUnitPrices[type] <= 0) {
        errors.push(`${getVentTypeDisplayName(type)}单价必须大于0`)
      }
    }

    if (!settings.orderNumberPrefix.trim()) {
      errors.push('订单号前缀不能为空')
    } else if (!/^[A-Z]{2,10}$/.test(settings.orderNumberPrefix)) {
      errors.push('订单号前缀必须是2-10位大写字母')
    } else {
      // 检查前缀冲突
      const factoryId = user?.factoryId || ''
      if (checkPrefixConflict(settings.orderNumberPrefix, factoryId)) {
        errors.push('此订单号前缀已被其他工厂使用，请选择其他前缀')
      }
    }

    return errors
  }

  const handleSaveSettings = async () => {
    try {
      setSaving(true)
      const factoryId = getCurrentFactoryId()

      if (!factoryId) {
        throw new Error('无法获取工厂ID')
      }

      // 验证设置数据
      const validationErrors = validateSettings(settings)
      if (validationErrors.length > 0) {
        alert(`请修正以下错误：\n\n${validationErrors.join('\n')}`)
        return
      }

      // 使用工厂设置工具函数保存设置到数据库
      const { saveFactorySettings } = await import('@/lib/utils/factory-settings')
      await saveFactorySettings(factoryId, settings, user?.username)

      // 显示成功消息
      const successMessage = `✅ 设置保存成功！\n\n📅 保存时间: ${new Date().toLocaleString('zh-CN')}\n🏭 工厂: ${settings.factoryName}\n📝 订单号前缀: ${settings.orderNumberPrefix}\n👤 操作人: ${user?.username || '未知'}`
      alert(successMessage)

    } catch (error) {
      console.error('❌ 保存设置失败:', error)
      alert(`❌ 保存失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setSaving(false)
    }
  }

  const handleExportSettings = () => {
    try {
      const dataStr = JSON.stringify(settings, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `工厂设置_${new Date().toISOString().split('T')[0]}.json`
      link.click()
      URL.revokeObjectURL(url)
      
      console.log('✅ 设置导出成功')
    } catch (error) {
      console.error('❌ 导出设置失败:', error)
      alert('导出失败，请重试')
    }
  }

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target?.result as string)
        setSettings(importedSettings)
        alert('设置导入成功！')
      } catch (error) {
        console.error('❌ 导入设置失败:', error)
        alert('导入失败，文件格式错误')
      }
    }
    reader.readAsText(file)
  }

  if (loading) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">正在加载设置...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Settings className="h-6 w-6 mr-2 text-blue-600" />
              工厂设置
            </h1>
            <p className="text-gray-600">配置工厂基本信息和系统参数</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={handleExportSettings}>
              <Download className="h-4 w-4 mr-2" />
              导出设置
            </Button>
            <label className="cursor-pointer">
              <Button variant="outline" asChild>
                <span>
                  <Upload className="h-4 w-4 mr-2" />
                  导入设置
                </span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={handleImportSettings}
                className="hidden"
              />
            </label>
            <Button onClick={handleSaveSettings} disabled={saving} className="bg-blue-600 hover:bg-blue-700">
              {saving ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存设置
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 设置标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="business">业务设置</TabsTrigger>
            <TabsTrigger value="rewards">奖励系统</TabsTrigger>
            <TabsTrigger value="system">系统设置</TabsTrigger>
            <TabsTrigger value="notifications">通知设置</TabsTrigger>
            <TabsTrigger value="appearance">界面设置</TabsTrigger>
            <TabsTrigger value="security">安全设置</TabsTrigger>
          </TabsList>

          {/* 基本信息设置 */}
          <TabsContent value="basic" className="space-y-6">
            {/* 订阅信息卡片 */}
            <SubscriptionInfoCard />

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Factory className="h-5 w-5 mr-2" />
                  工厂基本信息
                </CardTitle>
                <CardDescription>配置工厂的基本信息和联系方式</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="factoryName">工厂名称 *</Label>
                    <Input
                      id="factoryName"
                      value={settings.factoryName}
                      onChange={(e) => setSettings(prev => ({ ...prev, factoryName: e.target.value }))}
                      placeholder="请输入工厂名称"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="factoryCode">工厂编码 *</Label>
                    <Input
                      id="factoryCode"
                      value={settings.factoryCode}
                      onChange={(e) => setSettings(prev => ({ ...prev, factoryCode: e.target.value }))}
                      placeholder="请输入工厂编码"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">工厂地址 *</Label>
                  <Textarea
                    id="address"
                    value={settings.address}
                    onChange={(e) => setSettings(prev => ({ ...prev, address: e.target.value }))}
                    placeholder="请输入详细地址"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="contactPerson">联系人 *</Label>
                    <Input
                      id="contactPerson"
                      value={settings.contactPerson}
                      onChange={(e) => setSettings(prev => ({ ...prev, contactPerson: e.target.value }))}
                      placeholder="请输入联系人姓名"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">联系电话 *</Label>
                    <Input
                      id="contactPhone"
                      type="tel"
                      value={settings.contactPhone}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\D/g, '') // 只允许数字
                        setSettings(prev => ({ ...prev, contactPhone: value }))
                      }}
                      placeholder="请输入11位手机号码"
                      maxLength={11}
                      pattern="[0-9]*"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">邮箱地址</Label>
                    <Input
                      id="email"
                      type="email"
                      value={settings.email}
                      onChange={(e) => setSettings(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="请输入邮箱地址"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 业务设置 */}
          <TabsContent value="business" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>默认单价设置</CardTitle>
                <CardDescription>配置各类产品的默认单价，可在录单时修改</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 常规风口价格设置 */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900">常规风口 (元/㎡)</h4>
                  <p className="text-sm text-gray-500">计算公式: (长+60mm) × (宽+60mm) ÷ 1,000,000 × 单价(元/㎡) × 数量</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[
                      'double_white_outlet', 'double_black_outlet', 'white_black_bottom_outlet', 'white_black_bottom_return',
                      'white_return', 'black_return', 'white_linear', 'black_linear',
                      'white_linear_return', 'black_linear_return', 'maintenance'
                    ].map((type) => (
                      <div key={type} className="space-y-2">
                        <Label htmlFor={`${type}Price`}>{getVentTypeDisplayName(type)}</Label>
                        <Input
                          id={`${type}Price`}
                          type="number"
                          step="0.01"
                          value={settings.defaultUnitPrices[type]}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            defaultUnitPrices: { ...prev.defaultUnitPrices, [type]: parseFloat(e.target.value) || 0 }
                          }))}
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* 高端风口价格设置 */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900">高端风口 (元/m)</h4>
                  <p className="text-sm text-gray-500">计算公式: (长+120mm) ÷ 1,000 × 单价(元/m) × 数量</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[
                      'high_end_white_outlet', 'high_end_black_outlet', 'high_end_white_return', 'high_end_black_return',
                      'arrow_outlet', 'arrow_return', 'claw_outlet', 'claw_return',
                      'black_white_dual_outlet', 'black_white_dual_return', 'wood_grain_outlet', 'wood_grain_return',
                      'white_putty_outlet', 'white_putty_return', 'black_putty_outlet', 'black_putty_return',
                      'white_gypsum_outlet', 'white_gypsum_return', 'black_gypsum_outlet', 'black_gypsum_return'
                    ].map((type) => (
                      <div key={type} className="space-y-2">
                        <Label htmlFor={`${type}Price`}>{getVentTypeDisplayName(type)}</Label>
                        <Input
                          id={`${type}Price`}
                          type="number"
                          step="0.01"
                          value={settings.defaultUnitPrices[type]}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            defaultUnitPrices: { ...prev.defaultUnitPrices, [type]: parseFloat(e.target.value) || 0 }
                          }))}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>订单设置</CardTitle>
                <CardDescription>配置订单相关的业务规则和前缀生成</CardDescription>
              </CardHeader>
              <CardContent>
                <PrefixSelector
                  factoryId={user?.factoryId || ''}
                  factoryName={settings.factoryName}
                  currentPrefix={settings.orderNumberPrefix}
                  onPrefixChange={(prefix) => setSettings(prev => ({
                    ...prev,
                    orderNumberPrefix: prefix
                  }))}
                  minLength={settings.prefixMinLength || 3}
                  maxLength={settings.prefixMaxLength || 8}
                  includeNumbers={settings.prefixIncludeNumbers || false}
                  preferLonger={settings.prefixPreferLonger !== false}
                />

                {/* 前缀配置选项 */}
                <div className="mt-6 bg-gray-50 p-4 rounded-lg space-y-3">
                  <h4 className="font-medium text-sm text-gray-700">🎯 前缀生成配置</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-xs">最小长度</Label>
                      <select
                        className="w-full mt-1 px-2 py-1 border rounded text-sm"
                        value={settings.prefixMinLength || 3}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          prefixMinLength: parseInt(e.target.value)
                        }))}
                      >
                        <option value={2}>2位</option>
                        <option value={3}>3位</option>
                        <option value={4}>4位</option>
                        <option value={5}>5位</option>
                      </select>
                    </div>
                    <div>
                      <Label className="text-xs">最大长度</Label>
                      <select
                        className="w-full mt-1 px-2 py-1 border rounded text-sm"
                        value={settings.prefixMaxLength || 8}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          prefixMaxLength: parseInt(e.target.value)
                        }))}
                      >
                        <option value={6}>6位</option>
                        <option value={7}>7位</option>
                        <option value={8}>8位</option>
                        <option value={9}>9位</option>
                        <option value={10}>10位</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center text-xs">
                      <input
                        type="checkbox"
                        checked={settings.prefixIncludeNumbers || false}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          prefixIncludeNumbers: e.target.checked
                        }))}
                        className="mr-1"
                      />
                      允许数字后缀
                    </label>
                    <label className="flex items-center text-xs">
                      <input
                        type="checkbox"
                        checked={settings.prefixPreferLonger !== false}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          prefixPreferLonger: e.target.checked
                        }))}
                        className="mr-1"
                      />
                      偏好较长前缀
                    </label>
                  </div>
                  <p className="text-xs text-blue-600">
                    💡 提示: 修改配置后，智能建议会自动更新
                  </p>
                </div>
              </CardContent>
            </Card>

          </TabsContent>

          {/* 奖励系统设置 */}
          <TabsContent value="rewards" className="space-y-6">
            {/* 安全检查：确保奖励设置存在 */}
            {settings.rewardSettings && settings.rewardSettings.regularVentRewards && settings.rewardSettings.premiumVentRewards && settings.rewardSettings.rewardRules ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <span className="text-yellow-600 mr-2">🎁</span>
                  客户奖励系统
                </CardTitle>
                <CardDescription>配置客户推荐奖励的计算规则和使用条件</CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                {/* 普通风口奖励设置 */}
                <div className="space-y-4">
                  <h4 className="font-medium text-lg flex items-center">
                    <span className="text-blue-600 mr-2">📊</span>
                    普通风口奖励（按比例）
                  </h4>
                  <p className="text-sm text-gray-600">客户介绍新客户下单普通风口时，按订单金额比例给予奖励</p>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-blue-50 rounded-lg">
                    <div className="space-y-2">
                      <Label htmlFor="baseRate">基础奖励比例 (%)</Label>
                      <Input
                        id="baseRate"
                        type="number"
                        step="0.1"
                        min="0"
                        max="10"
                        value={(settings.rewardSettings.regularVentRewards.baseRate * 100).toFixed(1)}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            regularVentRewards: {
                              ...prev.rewardSettings.regularVentRewards,
                              baseRate: parseFloat(e.target.value) / 100 || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">默认奖励比例</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tier1Amount">第一档门槛 (元)</Label>
                      <Input
                        id="tier1Amount"
                        type="number"
                        step="1000"
                        min="0"
                        value={settings.rewardSettings.regularVentRewards.tier1Amount}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            regularVentRewards: {
                              ...prev.rewardSettings.regularVentRewards,
                              tier1Amount: parseFloat(e.target.value) || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">达到此金额提升奖励</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tier1Rate">第一档奖励 (%)</Label>
                      <Input
                        id="tier1Rate"
                        type="number"
                        step="0.1"
                        min="0"
                        max="10"
                        value={(settings.rewardSettings.regularVentRewards.tier1Rate * 100).toFixed(1)}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            regularVentRewards: {
                              ...prev.rewardSettings.regularVentRewards,
                              tier1Rate: parseFloat(e.target.value) / 100 || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">≥{(settings.rewardSettings.regularVentRewards.tier1Amount/10000).toFixed(0)}万时的奖励</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tier2Amount">第二档门槛 (元)</Label>
                      <Input
                        id="tier2Amount"
                        type="number"
                        step="1000"
                        min="0"
                        value={settings.rewardSettings.regularVentRewards.tier2Amount}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            regularVentRewards: {
                              ...prev.rewardSettings.regularVentRewards,
                              tier2Amount: parseFloat(e.target.value) || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">更高金额的奖励门槛</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tier2Rate">第二档奖励 (%)</Label>
                      <Input
                        id="tier2Rate"
                        type="number"
                        step="0.1"
                        min="0"
                        max="10"
                        value={(settings.rewardSettings.regularVentRewards.tier2Rate * 100).toFixed(1)}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            regularVentRewards: {
                              ...prev.rewardSettings.regularVentRewards,
                              tier2Rate: parseFloat(e.target.value) / 100 || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">≥{(settings.rewardSettings.regularVentRewards.tier2Amount/10000).toFixed(0)}万时的奖励</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maxRate">最高奖励比例 (%)</Label>
                      <Input
                        id="maxRate"
                        type="number"
                        step="0.1"
                        min="0"
                        max="10"
                        value={(settings.rewardSettings.regularVentRewards.maxRate * 100).toFixed(1)}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            regularVentRewards: {
                              ...prev.rewardSettings.regularVentRewards,
                              maxRate: parseFloat(e.target.value) / 100 || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">奖励比例封顶</p>
                    </div>
                  </div>

                  <div className="p-3 bg-blue-100 rounded-lg text-sm text-blue-800">
                    <p><strong>奖励规则示例：</strong></p>
                    <p>• 订单金额 &lt; {(settings.rewardSettings.regularVentRewards.tier1Amount/10000).toFixed(0)}万：{(settings.rewardSettings.regularVentRewards.baseRate * 100).toFixed(1)}% 奖励</p>
                    <p>• 订单金额 ≥ {(settings.rewardSettings.regularVentRewards.tier1Amount/10000).toFixed(0)}万：{(settings.rewardSettings.regularVentRewards.tier1Rate * 100).toFixed(1)}% 奖励</p>
                    <p>• 订单金额 ≥ {(settings.rewardSettings.regularVentRewards.tier2Amount/10000).toFixed(0)}万：{(settings.rewardSettings.regularVentRewards.tier2Rate * 100).toFixed(1)}% 奖励</p>
                    <p>• 最高奖励比例：{(settings.rewardSettings.regularVentRewards.maxRate * 100).toFixed(1)}%</p>
                  </div>
                </div>

                {/* 高端风口奖励设置 */}
                <div className="space-y-4">
                  <h4 className="font-medium text-lg flex items-center">
                    <span className="text-purple-600 mr-2">💎</span>
                    高端风口奖励（固定金额）
                  </h4>
                  <p className="text-sm text-gray-600">客户介绍新客户下单高端风口（黑白双色、木纹、嵌入式等）时，按单笔订单给予固定奖励</p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-purple-50 rounded-lg">
                    <div className="space-y-2">
                      <Label htmlFor="premiumBaseAmount">基础奖励金额 (元)</Label>
                      <Input
                        id="premiumBaseAmount"
                        type="number"
                        step="1"
                        min="0"
                        value={settings.rewardSettings.premiumVentRewards.baseAmount}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            premiumVentRewards: {
                              ...prev.rewardSettings.premiumVentRewards,
                              baseAmount: parseFloat(e.target.value) || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">单笔订单基础奖励</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="premiumTier1Amount">第一档门槛 (元)</Label>
                      <Input
                        id="premiumTier1Amount"
                        type="number"
                        step="1000"
                        min="0"
                        value={settings.rewardSettings.premiumVentRewards.tier1Amount}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            premiumVentRewards: {
                              ...prev.rewardSettings.premiumVentRewards,
                              tier1Amount: parseFloat(e.target.value) || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">达到此金额提升奖励</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="premiumTier1Reward">第一档奖励 (元)</Label>
                      <Input
                        id="premiumTier1Reward"
                        type="number"
                        step="1"
                        min="0"
                        value={settings.rewardSettings.premiumVentRewards.tier1Reward}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            premiumVentRewards: {
                              ...prev.rewardSettings.premiumVentRewards,
                              tier1Reward: parseFloat(e.target.value) || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">≥{(settings.rewardSettings.premiumVentRewards.tier1Amount/10000).toFixed(1)}万时的奖励</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="premiumTier2Amount">第二档门槛 (元)</Label>
                      <Input
                        id="premiumTier2Amount"
                        type="number"
                        step="1000"
                        min="0"
                        value={settings.rewardSettings.premiumVentRewards.tier2Amount}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            premiumVentRewards: {
                              ...prev.rewardSettings.premiumVentRewards,
                              tier2Amount: parseFloat(e.target.value) || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">第二档奖励门槛</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="premiumTier2Reward">第二档奖励 (元)</Label>
                      <Input
                        id="premiumTier2Reward"
                        type="number"
                        step="1"
                        min="0"
                        value={settings.rewardSettings.premiumVentRewards.tier2Reward}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            premiumVentRewards: {
                              ...prev.rewardSettings.premiumVentRewards,
                              tier2Reward: parseFloat(e.target.value) || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">≥{(settings.rewardSettings.premiumVentRewards.tier2Amount/10000).toFixed(1)}万时的奖励</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="premiumTier3Amount">第三档门槛 (元)</Label>
                      <Input
                        id="premiumTier3Amount"
                        type="number"
                        step="1000"
                        min="0"
                        value={settings.rewardSettings.premiumVentRewards.tier3Amount}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            premiumVentRewards: {
                              ...prev.rewardSettings.premiumVentRewards,
                              tier3Amount: parseFloat(e.target.value) || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">最高档奖励门槛</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="premiumTier3Reward">第三档奖励 (元)</Label>
                      <Input
                        id="premiumTier3Reward"
                        type="number"
                        step="1"
                        min="0"
                        value={settings.rewardSettings.premiumVentRewards.tier3Reward}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            premiumVentRewards: {
                              ...prev.rewardSettings.premiumVentRewards,
                              tier3Reward: parseFloat(e.target.value) || 0
                            }
                          }
                        }))}
                      />
                      <p className="text-xs text-gray-500">≥{(settings.rewardSettings.premiumVentRewards.tier3Amount/10000).toFixed(1)}万时的奖励</p>
                    </div>
                  </div>

                  <div className="p-3 bg-purple-100 rounded-lg text-sm text-purple-800">
                    <p><strong>奖励规则示例：</strong></p>
                    <p>• 单笔订单 &lt; {(settings.rewardSettings.premiumVentRewards.tier1Amount/10000).toFixed(1)}万：¥{settings.rewardSettings.premiumVentRewards.baseAmount} 奖励</p>
                    <p>• 单笔订单 ≥ {(settings.rewardSettings.premiumVentRewards.tier1Amount/10000).toFixed(1)}万：¥{settings.rewardSettings.premiumVentRewards.tier1Reward} 奖励</p>
                    <p>• 单笔订单 ≥ {(settings.rewardSettings.premiumVentRewards.tier2Amount/10000).toFixed(1)}万：¥{settings.rewardSettings.premiumVentRewards.tier2Reward} 奖励</p>
                    <p>• 单笔订单 ≥ {(settings.rewardSettings.premiumVentRewards.tier3Amount/10000).toFixed(1)}万：¥{settings.rewardSettings.premiumVentRewards.tier3Reward} 奖励</p>
                  </div>
                </div>

                {/* 奖励使用规则 */}
                <div className="space-y-4">
                  <h4 className="font-medium text-lg flex items-center">
                    <span className="text-green-600 mr-2">⚙️</span>
                    奖励使用规则
                  </h4>
                  <p className="text-sm text-gray-600">配置客户如何使用获得的推荐奖励</p>

                  <div className="space-y-4 p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>要求结清货款</Label>
                        <p className="text-sm text-gray-500">客户必须结清所有订单货款后才能使用奖励</p>
                      </div>
                      <Switch
                        checked={settings.rewardSettings.rewardRules.requireFullPayment}
                        onCheckedChange={(checked) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            rewardRules: {
                              ...prev.rewardSettings.rewardRules,
                              requireFullPayment: checked
                            }
                          }
                        }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>允许兑现提取</Label>
                        <p className="text-sm text-gray-500">客户可以申请将奖励兑现为现金</p>
                      </div>
                      <Switch
                        checked={settings.rewardSettings.rewardRules.allowCashOut}
                        onCheckedChange={(checked) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            rewardRules: {
                              ...prev.rewardSettings.rewardRules,
                              allowCashOut: checked
                            }
                          }
                        }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>允许抵扣订单</Label>
                        <p className="text-sm text-gray-500">客户可以使用奖励抵扣新订单的货款</p>
                      </div>
                      <Switch
                        checked={settings.rewardSettings.rewardRules.allowOrderDeduction}
                        onCheckedChange={(checked) => setSettings(prev => ({
                          ...prev,
                          rewardSettings: {
                            ...prev.rewardSettings,
                            rewardRules: {
                              ...prev.rewardSettings.rewardRules,
                              allowOrderDeduction: checked
                            }
                          }
                        }))}
                      />
                    </div>
                  </div>

                  <div className="p-3 bg-yellow-100 rounded-lg text-sm text-yellow-800">
                    <p><strong>💡 温馨提示：</strong></p>
                    <p>• 奖励系统可以有效激励客户推荐新客户，提升业务增长</p>
                    <p>• 建议根据实际业务情况调整奖励比例和金额</p>
                    <p>• 要求结清货款可以降低坏账风险，但可能影响客户积极性</p>
                    <p>• 定期检查和调整奖励设置，确保系统的可持续性</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <span className="text-red-600 mr-2">⚠️</span>
                    奖励系统配置错误
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600">奖励系统设置数据不完整，正在重新加载...</p>
                    <Button
                      onClick={() => window.location.reload()}
                      className="mt-4"
                      variant="outline"
                    >
                      刷新页面
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* 系统设置 */}
          <TabsContent value="system" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="h-5 w-5 mr-2" />
                  数据备份设置
                </CardTitle>
                <CardDescription>配置数据备份和恢复选项</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>自动备份</Label>
                    <p className="text-sm text-gray-500">定期自动备份工厂数据</p>
                  </div>
                  <Switch
                    checked={settings.autoBackup}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoBackup: checked }))}
                  />
                </div>

                {settings.autoBackup && (
                  <div className="space-y-2">
                    <Label htmlFor="backupFrequency">备份频率</Label>
                    <Select
                      value={settings.backupFrequency}
                      onValueChange={(value) => setSettings(prev => ({ ...prev, backupFrequency: value }))}
                    >
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">每日备份</SelectItem>
                        <SelectItem value="weekly">每周备份</SelectItem>
                        <SelectItem value="monthly">每月备份</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 通知设置 */}
          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="h-5 w-5 mr-2" />
                  通知偏好设置
                </CardTitle>
                <CardDescription>配置各种通知的接收方式</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>邮件通知</Label>
                      <p className="text-sm text-gray-500">接收重要事件的邮件通知</p>
                    </div>
                    <Switch
                      checked={settings.emailNotifications}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, emailNotifications: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>短信通知</Label>
                      <p className="text-sm text-gray-500">接收紧急事件的短信通知</p>
                    </div>
                    <Switch
                      checked={settings.smsNotifications}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, smsNotifications: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>订单状态通知</Label>
                      <p className="text-sm text-gray-500">订单状态变更时发送通知</p>
                    </div>
                    <Switch
                      checked={settings.orderStatusNotifications}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, orderStatusNotifications: checked }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 界面设置 */}
          <TabsContent value="appearance" className="space-y-6">
            {/* 主题选择器 */}
            <ThemeSelector />

            {/* 其他界面设置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  其他界面设置
                </CardTitle>
                <CardDescription>配置语言、日期和货币格式</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="language">系统语言</Label>
                    <Select
                      value={settings.language}
                      onValueChange={(value) => setSettings(prev => ({ ...prev, language: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="zh-CN">简体中文</SelectItem>
                        <SelectItem value="zh-TW">繁体中文</SelectItem>
                        <SelectItem value="en-US">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dateFormat">日期格式</Label>
                    <Select
                      value={settings.dateFormat}
                      onValueChange={(value) => setSettings(prev => ({ ...prev, dateFormat: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="YYYY-MM-DD">2024-12-01</SelectItem>
                        <SelectItem value="DD/MM/YYYY">01/12/2024</SelectItem>
                        <SelectItem value="MM/DD/YYYY">12/01/2024</SelectItem>
                        <SelectItem value="YYYY年MM月DD日">2024年12月01日</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="currencyFormat">货币格式</Label>
                    <Select
                      value={settings.currencyFormat}
                      onValueChange={(value) => setSettings(prev => ({ ...prev, currencyFormat: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CNY">人民币 (¥)</SelectItem>
                        <SelectItem value="USD">美元 ($)</SelectItem>
                        <SelectItem value="EUR">欧元 (€)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* 主题设置说明 */}
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-800 mb-2">💡 主题设置说明</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• <strong>浅色主题</strong>：适合白天使用，界面明亮清晰</li>
                    <li>• <strong>深色主题</strong>：适合夜间使用，减少眼部疲劳</li>
                    <li>• <strong>跟随系统</strong>：根据操作系统设置自动切换</li>
                    <li>• <strong>主题颜色</strong>：可选择不同的主色调来个性化界面</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 安全设置 */}
          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  安全与权限设置
                </CardTitle>
                <CardDescription>管理系统安全和用户权限</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center space-x-2 text-yellow-600">
                    <AlertTriangle className="h-5 w-5" />
                    <span className="font-medium">安全提醒</span>
                  </div>
                  <div className="mt-2 text-sm text-yellow-600">
                    <p>• 定期更改密码，使用强密码</p>
                    <p>• 不要与他人共享账号信息</p>
                    <p>• 及时备份重要数据</p>
                    <p>• 发现异常登录及时联系管理员</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">当前用户信息</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">用户名:</span>
                        <span className="ml-2 font-medium">{user?.username || '未知'}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">角色:</span>
                        <span className="ml-2 font-medium">{user?.role === 'owner' ? '工厂老板' : '员工'}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">姓名:</span>
                        <span className="ml-2 font-medium">{user?.name || '未知'}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">邮箱:</span>
                        <span className="ml-2 font-medium">{user?.email || '未设置'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <Button variant="outline" className="mr-4">
                      <User className="h-4 w-4 mr-2" />
                      修改密码
                    </Button>
                    <Button variant="outline">
                      <Shield className="h-4 w-4 mr-2" />
                      查看权限
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
