/**
 * 🇨🇳 风口云平台 - 获取推荐客户API
 * 
 * 获取指定客户推荐的其他客户列表
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const clientId = searchParams.get('clientId')
    const isPublic = searchParams.get('public') === 'true'

    console.log('👥 获取推荐客户API请求:', { clientId, isPublic })

    if (!clientId) {
      return NextResponse.json({
        success: false,
        error: '客户ID不能为空'
      }, { status: 400 })
    }

    // 检查客户是否存在
    const client = await db.getClientById(clientId)
    if (!client) {
      return NextResponse.json({
        success: false,
        error: '客户不存在'
      }, { status: 404 })
    }

    // 获取同工厂的所有客户（确保数据一致性）
    const allClients = await db.getClientsByFactoryId(client.factoryId)

    // 查找由该客户推荐的其他客户
    const referredClients = allClients.filter(c =>
      c.referrerId === clientId && c.id !== clientId
    )

    console.log('✅ 推荐客户查询完成，找到', referredClients.length, '个推荐客户')

    // 为每个推荐客户计算统计信息
    const enrichedReferredClients = await Promise.all(referredClients.map(async (referredClient) => {
      // 获取该客户的订单
      const clientOrders = await db.getOrdersByClientId(referredClient.id)

      // 计算总金额和订单数量
      const totalAmount = clientOrders.reduce((sum, order) => {
        const amount = typeof order.totalAmount === 'number' ? order.totalAmount : 0
        return sum + amount
      }, 0)

      // 计算推荐奖励（这里可以根据业务规则计算）
      const referralReward = totalAmount * 0.02 // 假设2%的推荐奖励

      return {
        ...referredClient,
        totalOrders: clientOrders.length,
        totalAmount,
        referralReward
      }
    }))

    // 按加入时间倒序排列
    enrichedReferredClients.sort((a, b) => {
      const aTime = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt)
      const bTime = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt)
      return bTime.getTime() - aTime.getTime()
    })

    return NextResponse.json({
      success: true,
      clients: enrichedReferredClients,
      total: enrichedReferredClients.length,
      clientId,
      isPublic
    })

  } catch (error) {
    console.error('❌ 获取推荐客户API错误:', error)
    return NextResponse.json({
      success: false,
      error: '获取推荐客户失败，请重试'
    }, { status: 500 })
  }
}
