/**
 * 🇨🇳 风口云平台 - 图片数据库存储服务
 * 
 * 功能说明：
 * - 将图片存储到数据库中，实现真正的持久化
 * - 支持图片的增删改查操作
 * - 自动处理图片压缩和格式转换
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export interface CarouselImage {
  id: string
  name: string
  category: 'product' | 'process' | 'other'
  imageData: string  // Base64编码的图片数据
  mimeType: string   // 图片MIME类型
  size: number       // 文件大小
  width?: number     // 图片宽度
  height?: number    // 图片高度
  uploadDate: Date
  isActive: boolean
}

export class ImageDatabaseService {
  private static instance: ImageDatabaseService
  
  static getInstance(): ImageDatabaseService {
    if (!ImageDatabaseService.instance) {
      ImageDatabaseService.instance = new ImageDatabaseService()
    }
    return ImageDatabaseService.instance
  }

  /**
   * 上传图片到数据库
   */
  async uploadImage(file: File, category: 'product' | 'process' | 'other'): Promise<CarouselImage> {
    try {
      // 直接转换为Base64（跳过压缩，因为服务端无法使用Canvas）
      const base64Data = await this.fileToBase64(file)

      // 保存到数据库
      const savedImage = await prisma.carouselImage.create({
        data: {
          name: file.name,
          category,
          imageData: base64Data,
          mimeType: file.type,
          size: file.size,
          isActive: true
        }
      })

      return {
        id: savedImage.id,
        name: savedImage.name,
        category: savedImage.category as 'product' | 'process' | 'other',
        imageData: savedImage.imageData,
        mimeType: savedImage.mimeType,
        size: savedImage.size,
        uploadDate: savedImage.createdAt,
        isActive: savedImage.isActive
      }
    } catch (error) {
      console.error('上传图片到数据库失败:', error)
      throw new Error('图片上传失败')
    }
  }

  /**
   * 获取指定分类的图片列表
   */
  async getImages(category: 'product' | 'process' | 'other'): Promise<CarouselImage[]> {
    try {
      const images = await prisma.carouselImage.findMany({
        where: {
          category,
          isActive: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return images.map(img => ({
        id: img.id,
        name: img.name,
        category: img.category as 'product' | 'process' | 'other',
        imageData: img.imageData,
        mimeType: img.mimeType,
        size: img.size,
        width: img.width || undefined,
        height: img.height || undefined,
        uploadDate: img.createdAt,
        isActive: img.isActive
      }))
    } catch (error) {
      console.error('获取图片列表失败:', error)
      return []
    }
  }

  /**
   * 获取图片URL列表（用于轮播组件）
   */
  async getImageUrls(category: 'product' | 'process' | 'other'): Promise<string[]> {
    const images = await this.getImages(category)
    return images.map(img => img.imageData)
  }

  /**
   * 删除图片
   */
  async deleteImage(imageId: string): Promise<boolean> {
    try {
      await prisma.carouselImage.update({
        where: { id: imageId },
        data: { isActive: false }
      })
      return true
    } catch (error) {
      console.error('删除图片失败:', error)
      return false
    }
  }

  /**
   * 压缩图片（服务端暂不支持，直接返回原文件）
   * 注意：在服务端环境中无法使用Canvas和Image对象
   */
  private async compressImage(file: File): Promise<File> {
    // 服务端无法使用Canvas进行图片压缩
    // 如果需要压缩，建议在客户端完成后再上传
    return file
  }

  /**
   * 获取图片尺寸（服务端暂不支持）
   * 注意：在服务端环境中无法使用Image对象
   */
  private async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    // 服务端无法获取图片尺寸
    // 返回默认值，或者在客户端获取后传递给服务端
    return { width: 0, height: 0 }
  }

  /**
   * 将文件转换为Base64（服务端版本）
   */
  private async fileToBase64(file: File): Promise<string> {
    try {
      // 在服务端环境中，使用 arrayBuffer 和 Buffer
      const arrayBuffer = await file.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      const base64 = buffer.toString('base64')

      // 构造完整的 data URL
      return `data:${file.type};base64,${base64}`
    } catch (error) {
      console.error('文件转换Base64失败:', error)
      throw new Error('文件转换失败')
    }
  }

  /**
   * 清理无效的图片数据
   */
  async cleanupInvalidImages(): Promise<number> {
    try {
      const result = await prisma.carouselImage.deleteMany({
        where: {
          OR: [
            { imageData: { equals: null } },
            { imageData: '' },
            { isActive: false }
          ]
        }
      })
      return result.count
    } catch (error) {
      console.error('清理无效图片失败:', error)
      return 0
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    totalImages: number
    totalSize: number
    byCategory: Record<string, { count: number; size: number }>
  }> {
    try {
      const images = await prisma.carouselImage.findMany({
        where: { isActive: true }
      })

      const stats = {
        totalImages: images.length,
        totalSize: images.reduce((sum, img) => sum + img.size, 0),
        byCategory: {} as Record<string, { count: number; size: number }>
      }

      // 按分类统计
      images.forEach(img => {
        if (!stats.byCategory[img.category]) {
          stats.byCategory[img.category] = { count: 0, size: 0 }
        }
        stats.byCategory[img.category].count++
        stats.byCategory[img.category].size += img.size
      })

      return stats
    } catch (error) {
      console.error('获取存储统计失败:', error)
      return {
        totalImages: 0,
        totalSize: 0,
        byCategory: {}
      }
    }
  }
}

// 导出单例实例
export const imageDatabase = ImageDatabaseService.getInstance()
