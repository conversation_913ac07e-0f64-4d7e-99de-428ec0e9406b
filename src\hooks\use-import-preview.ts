"use client"

import { useState, useCallback, useMemo } from 'react'
import { smartDimensionRecognition } from "@/lib/utils/dimension-utils"
import type {
  EditableProjectData,
  EditableFloorData,
  EditableVentItem,
  ImportPreviewState,
  ImportType,
  ValidationError
} from "@/types/import-preview"

// 生成唯一ID
const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

// 计算风口价格
const calculateVentPrice = (vent: EditableVentItem): number => {
  const { type, length, width, quantity, unitPrice } = vent

  console.log(`🔧 [价格计算] 输入参数:`, {
    type, length, width, quantity, unitPrice
  })

  if (unitPrice <= 0) {
    console.log(`❌ [价格计算] 单价无效: ${unitPrice}`)
    return 0
  }

  let calculatedPrice = 0

  // 常规风口 (元/㎡)：(长+60) × (宽+60) ÷ 1,000,000 × 单价 × 数量
  const regularVentTypes = [
    'double_white_outlet', 'double_black_outlet', 'white_return', 'black_return',
    'white_linear', 'black_linear', 'white_linear_return', 'black_linear_return', 'maintenance'
  ]

  if (regularVentTypes.includes(type)) {
    if (length > 0 && width > 0) {
      const area = (length + 60) * (width + 60) / 1000000
      calculatedPrice = area * unitPrice * quantity
      console.log(`✅ [价格计算] 常规风口: 面积=${area.toFixed(6)}㎡, 计算=${area} × ${unitPrice} × ${quantity} = ${calculatedPrice}`)
    } else {
      console.log(`❌ [价格计算] 常规风口尺寸无效: ${length}×${width}`)
    }
  } else {
    // 高端风口 (元/m)：(长+120) ÷ 1,000 × 单价 × 数量
    if (length > 0) {
      const lengthInM = (length + 120) / 1000
      calculatedPrice = lengthInM * unitPrice * quantity
      console.log(`✅ [价格计算] 高端风口: 长度=${lengthInM.toFixed(3)}m, 计算=${lengthInM} × ${unitPrice} × ${quantity} = ${calculatedPrice}`)
    } else {
      console.log(`❌ [价格计算] 高端风口长度无效: ${length}`)
    }
  }

  // 🔧 精确到一位小数，四舍五入
  const finalPrice = Math.round(calculatedPrice * 10) / 10
  console.log(`🔧 [价格计算] 最终价格: ${calculatedPrice} → ${finalPrice}`)
  return finalPrice
}

// 验证风口数据
const validateVent = (vent: EditableVentItem): ValidationError[] => {
  const errors: ValidationError[] = []
  
  if (!vent.length || vent.length < 10) {
    errors.push({ field: 'length', message: '长度不能小于10mm', severity: 'error' })
  }
  if (!vent.width || vent.width < 10) {
    errors.push({ field: 'width', message: '宽度不能小于10mm', severity: 'error' })
  }
  if (!vent.quantity || vent.quantity < 1) {
    errors.push({ field: 'quantity', message: '数量不能小于1', severity: 'error' })
  }
  if (vent.unitPrice < 0) {
    errors.push({ field: 'unitPrice', message: '单价不能为负数', severity: 'error' })
  }
  
  return errors
}

// 将原始项目数据转换为可编辑格式
const convertToEditableProject = (project: any): EditableProjectData => {
  return {
    id: project.id || generateId(),
    orderDate: project.orderDate || '',
    projectAddress: project.projectAddress || '',
    floors: project.floors.map((floor: any) => convertToEditableFloor(floor)),
    totalAmount: project.totalAmount || 0,
    isSelected: true,
    hasChanges: false,
    validationErrors: [],
    originalData: { ...project }
  }
}

// 将原始楼层数据转换为可编辑格式
const convertToEditableFloor = (floor: any): EditableFloorData => {
  // 🔧 处理两种数据结构：
  // 1. 直接包含vents的结构：floor.vents
  // 2. 包含rooms的结构：floor.rooms[].vents
  let allVents: any[] = []

  if (floor.vents && Array.isArray(floor.vents)) {
    // 直接包含vents的结构
    allVents = floor.vents
  } else if (floor.rooms && Array.isArray(floor.rooms)) {
    // 包含rooms的结构，需要提取所有房间的vents
    allVents = floor.rooms.reduce((acc: any[], room: any) => {
      if (room.vents && Array.isArray(room.vents)) {
        return acc.concat(room.vents)
      }
      return acc
    }, [])
  }

  // 转换风口数据
  const editableVents = allVents.map((vent: any) => convertToEditableVent(vent))

  // 🔧 重新计算楼层总价
  const calculatedTotalPrice = editableVents.reduce((sum, vent) => sum + vent.totalPrice, 0)
  console.log(`🔧 [楼层总价] 楼层"${floor.floorName || floor.name || '未知'}"总价计算: ${editableVents.length}个风口, 总价=${calculatedTotalPrice}`)

  return {
    id: floor.id || generateId(),
    floorName: floor.floorName || floor.name || '',
    vents: editableVents,
    totalPrice: calculatedTotalPrice, // 🔧 使用重新计算的总价
    hasChanges: false,
    validationErrors: [],
    originalData: { ...floor }
  }
}

// 根据产品类型获取默认单价
const getDefaultPriceByType = (type: string): number => {
  // 🔧 尝试从全局工厂设置获取价格
  if (typeof window !== 'undefined') {
    try {
      // 从localStorage获取工厂设置
      const factoryId = localStorage.getItem('currentFactoryId')
      if (factoryId) {
        const settingsKey = `factory_settings_${factoryId}`
        const settingsStr = localStorage.getItem(settingsKey)
        if (settingsStr) {
          const settings = JSON.parse(settingsStr)
          const price = settings.defaultUnitPrices?.[type]
          if (price && price > 0) {
            console.log(`🔍 [预览编辑器] 从工厂设置获取单价: ${type} -> ${price}`)
            return price
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ [预览编辑器] 获取工厂设置失败，使用默认价格:', error)
    }
  }

  // 🔧 备用默认价格配置
  const defaultUnitPrices = {
    // 常规风口 (元/㎡)
    double_white_outlet: 130,  // 🔧 更新为与工厂设置一致的默认值
    double_black_outlet: 130,
    white_black_bottom_outlet: 130,
    white_black_bottom_return: 130,
    white_return: 130,
    black_return: 130,
    white_linear: 130,
    black_linear: 130,
    white_linear_return: 130,
    black_linear_return: 130,
    maintenance: 130,
    // 高端风口 (元/m)
    high_end_white_outlet: 80,
    high_end_black_outlet: 80,
    high_end_white_return: 80,
    high_end_black_return: 80,
    arrow_outlet: 90,
    arrow_return: 90,
    claw_outlet: 100,
    claw_return: 100,
    black_white_dual_outlet: 85,
    black_white_dual_return: 85,
    wood_grain_outlet: 120,
    wood_grain_return: 120,
    white_putty_outlet: 110,
    white_putty_return: 110,
    black_putty_outlet: 110,
    black_putty_return: 110,
    white_gypsum_outlet: 130,
    white_gypsum_return: 130,
    black_gypsum_outlet: 130,
    black_gypsum_return: 130
  }

  const price = defaultUnitPrices[type as keyof typeof defaultUnitPrices] || defaultUnitPrices.double_white_outlet
  console.log(`🔍 [预览编辑器] 使用默认单价: ${type} -> ${price}`)
  return price
}

// 将原始风口数据转换为可编辑格式
const convertToEditableVent = (vent: any): EditableVentItem => {
  console.log(`🔧 [预览编辑器] 转换风口数据:`, vent)

  // 应用智能尺寸识别
  const smartDimensions = smartDimensionRecognition(vent.length || 0, vent.width || 0)

  const finalUnitPrice = vent.unitPrice || getDefaultPriceByType(vent.type || 'double_white_outlet')
  console.log(`🔧 [预览编辑器] 单价处理: 原始=${vent.unitPrice}, 最终=${finalUnitPrice}`)

  const editableVent: EditableVentItem = {
    id: vent.id || generateId(),
    type: vent.type || 'double_white_outlet',
    length: smartDimensions.length,
    width: smartDimensions.width,
    quantity: vent.quantity || 1,
    unitPrice: finalUnitPrice,
    totalPrice: vent.totalPrice || 0,
    notes: vent.notes || '',
    hasChanges: false,
    validationErrors: [],
    originalData: { ...vent }
  }

  // 重新计算价格和验证
  editableVent.totalPrice = calculateVentPrice(editableVent)
  editableVent.validationErrors = validateVent(editableVent)

  return editableVent
}

export function useImportPreview() {
  const [state, setState] = useState<ImportPreviewState>({
    importType: 'single_project',
    editStatus: 'preview',
    isLoading: false,
    projects: [],
    selectedProjectIds: [],
    floors: [],
    singleProjectUpdates: undefined,
    editingItemId: undefined,
    hasUnsavedChanges: false,
    globalValidationErrors: [],
    isValid: true,
    showPreview: false,
    showValidationSummary: false
  })

  // 初始化预览数据
  const initializePreview = useCallback((
    importType: ImportType,
    data: { projects?: any[], floors?: any[] }
  ) => {
    if (importType === 'multi_project' && data.projects) {
      const editableProjects = data.projects.map(convertToEditableProject)
      setState(prev => ({
        ...prev,
        importType,
        projects: editableProjects,
        selectedProjectIds: editableProjects.map(p => p.id),
        showPreview: true,
        editStatus: 'preview'
      }))
    } else if (importType === 'single_project' && data.floors) {
      const editableFloors = data.floors.map(convertToEditableFloor)
      setState(prev => ({
        ...prev,
        importType,
        floors: editableFloors,
        showPreview: true,
        editStatus: 'preview'
      }))
    }
  }, [])

  // 更新项目
  const updateProject = useCallback((projectId: string, updates: Partial<EditableProjectData>) => {
    setState(prev => {
      // 单项目模式：存储项目更新信息
      if (prev.importType === 'single_project') {
        return {
          ...prev,
          singleProjectUpdates: {
            ...prev.singleProjectUpdates,
            ...updates
          },
          hasUnsavedChanges: true
        }
      }

      // 多项目模式：更新 projects 数组
      return {
        ...prev,
        projects: prev.projects.map(project =>
          project.id === projectId
            ? { ...project, ...updates, hasChanges: true }
            : project
        ),
        hasUnsavedChanges: true
      }
    })
  }, [])

  // 更新楼层
  const updateFloor = useCallback((projectId: string, floorId: string, updates: Partial<EditableFloorData>) => {
    setState(prev => ({
      ...prev,
      projects: prev.projects.map(project => 
        project.id === projectId 
          ? {
              ...project,
              floors: project.floors.map(floor =>
                floor.id === floorId
                  ? { ...floor, ...updates, hasChanges: true }
                  : floor
              ),
              hasChanges: true
            }
          : project
      ),
      hasUnsavedChanges: true
    }))
  }, [])

  // 更新风口
  const updateVent = useCallback((projectId: string, floorId: string, ventId: string, updates: Partial<EditableVentItem>) => {
    setState(prev => {
      // 单项目模式：更新 floors 数组
      if (prev.importType === 'single_project') {
        return {
          ...prev,
          floors: prev.floors.map(floor => {
            if (floor.id === floorId) {
              const updatedVents = floor.vents.map(vent => {
                if (vent.id === ventId) {
                  const updatedVent = { ...vent, ...updates, hasChanges: true }
                  // 如果更新了产品类型，自动更新单价
                  if (updates.type && updates.type !== vent.type) {
                    updatedVent.unitPrice = getDefaultPriceByType(updates.type)
                  }
                  // 重新计算价格和验证
                  updatedVent.totalPrice = calculateVentPrice(updatedVent)
                  updatedVent.validationErrors = validateVent(updatedVent)
                  return updatedVent
                }
                return vent
              })

              // 🔧 重新计算楼层总价
              const floorTotalPrice = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)
              console.log(`🔧 [楼层总价更新] 楼层"${floor.floorName}"总价: ${floorTotalPrice}`)

              return {
                ...floor,
                vents: updatedVents,
                totalPrice: floorTotalPrice, // 🔧 更新楼层总价
                hasChanges: true
              }
            }
            return floor
          }),
          hasUnsavedChanges: true
        }
      }

      // 多项目模式：更新 projects 数组
      return {
        ...prev,
        projects: prev.projects.map(project =>
          project.id === projectId
            ? {
                ...project,
                floors: project.floors.map(floor => {
                  if (floor.id === floorId) {
                    const updatedVents = floor.vents.map(vent => {
                      if (vent.id === ventId) {
                        const updatedVent = { ...vent, ...updates, hasChanges: true }
                        // 如果更新了产品类型，自动更新单价
                        if (updates.type && updates.type !== vent.type) {
                          updatedVent.unitPrice = getDefaultPriceByType(updates.type)
                        }
                        // 重新计算价格和验证
                        updatedVent.totalPrice = calculateVentPrice(updatedVent)
                        updatedVent.validationErrors = validateVent(updatedVent)
                        return updatedVent
                      }
                      return vent
                    })

                    // 🔧 重新计算楼层总价
                    const floorTotalPrice = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)

                    return {
                      ...floor,
                      vents: updatedVents,
                      totalPrice: floorTotalPrice, // 🔧 更新楼层总价
                      hasChanges: true
                    }
                  }
                  return floor
                }),
                hasChanges: true
              }
            : project
        ),
        hasUnsavedChanges: true
      }
    })
  }, [])

  // 添加风口
  const addVent = useCallback((projectId: string, floorId: string) => {
    // 使用智能尺寸识别设置默认尺寸
    const defaultDimensions = smartDimensionRecognition(600, 150)

    const newVent: EditableVentItem = {
      id: generateId(),
      type: 'double_white_outlet',
      length: defaultDimensions.length,
      width: defaultDimensions.width,
      quantity: 1,
      unitPrice: getDefaultPriceByType('double_white_outlet'),
      totalPrice: 0,
      notes: '',
      hasChanges: true,
      validationErrors: [],
      originalData: undefined
    }

    newVent.totalPrice = calculateVentPrice(newVent)
    newVent.validationErrors = validateVent(newVent)

    setState(prev => {
      // 单项目模式：更新 floors 数组
      if (prev.importType === 'single_project') {
        return {
          ...prev,
          floors: prev.floors.map(floor =>
            floor.id === floorId
              ? {
                  ...floor,
                  vents: [...floor.vents, newVent],
                  hasChanges: true
                }
              : floor
          ),
          hasUnsavedChanges: true
        }
      }

      // 多项目模式：更新 projects 数组
      return {
        ...prev,
        projects: prev.projects.map(project =>
          project.id === projectId
            ? {
                ...project,
                floors: project.floors.map(floor =>
                  floor.id === floorId
                    ? {
                        ...floor,
                        vents: [...floor.vents, newVent],
                        hasChanges: true
                      }
                    : floor
                ),
                hasChanges: true
              }
            : project
        ),
        hasUnsavedChanges: true
      }
    })
  }, [])

  // 删除风口
  const removeVent = useCallback((projectId: string, floorId: string, ventId: string) => {
    setState(prev => {
      // 单项目模式：更新 floors 数组
      if (prev.importType === 'single_project') {
        return {
          ...prev,
          floors: prev.floors.map(floor =>
            floor.id === floorId
              ? {
                  ...floor,
                  vents: floor.vents.filter(vent => vent.id !== ventId),
                  hasChanges: true
                }
              : floor
          ),
          hasUnsavedChanges: true
        }
      }

      // 多项目模式：更新 projects 数组
      return {
        ...prev,
        projects: prev.projects.map(project =>
          project.id === projectId
            ? {
                ...project,
                floors: project.floors.map(floor =>
                  floor.id === floorId
                    ? {
                        ...floor,
                        vents: floor.vents.filter(vent => vent.id !== ventId),
                        hasChanges: true
                      }
                    : floor
                ),
                hasChanges: true
              }
            : project
        ),
        hasUnsavedChanges: true
      }
    })
  }, [])

  // 复制风口
  const duplicateVent = useCallback((projectId: string, floorId: string, ventId: string) => {
    setState(prev => {
      let vent: EditableVentItem | undefined

      // 根据模式查找风口数据
      if (prev.importType === 'single_project') {
        const floor = prev.floors.find(f => f.id === floorId)
        vent = floor?.vents.find(v => v.id === ventId)
      } else {
        const project = prev.projects.find(p => p.id === projectId)
        const floor = project?.floors.find(f => f.id === floorId)
        vent = floor?.vents.find(v => v.id === ventId)
      }

      if (!vent) return prev

      const duplicatedVent: EditableVentItem = {
        ...vent,
        id: generateId(),
        hasChanges: true,
        originalData: undefined
      }

      // 单项目模式：更新 floors 数组
      if (prev.importType === 'single_project') {
        return {
          ...prev,
          floors: prev.floors.map(f =>
            f.id === floorId
              ? {
                  ...f,
                  vents: [...f.vents, duplicatedVent],
                  hasChanges: true
                }
              : f
          ),
          hasUnsavedChanges: true
        }
      }

      // 多项目模式：更新 projects 数组
      return {
        ...prev,
        projects: prev.projects.map(p =>
          p.id === projectId
            ? {
                ...p,
                floors: p.floors.map(f =>
                  f.id === floorId
                    ? {
                        ...f,
                        vents: [...f.vents, duplicatedVent],
                        hasChanges: true
                      }
                    : f
                ),
                hasChanges: true
              }
            : p
        ),
        hasUnsavedChanges: true
      }
    })
  }, [])

  // 重置项目/楼层/风口数据
  const resetItem = useCallback((projectId: string, floorId?: string, ventId?: string) => {
    setState(prev => {
      // 单项目模式：重置 floors 数组
      if (prev.importType === 'single_project') {
        if (!floorId) {
          // 重置所有楼层（单项目模式下 projectId 被忽略）
          return {
            ...prev,
            floors: prev.floors.map(floor =>
              floor.originalData ? { ...floor.originalData } as EditableFloorData : floor
            ),
            hasUnsavedChanges: true
          }
        }

        return {
          ...prev,
          floors: prev.floors.map(floor => {
            if (floor.id !== floorId) return floor

            if (!ventId) {
              // 重置整个楼层
              return floor.originalData ? { ...floor.originalData } as EditableFloorData : floor
            }

            return {
              ...floor,
              vents: floor.vents.map(vent =>
                vent.id === ventId && vent.originalData
                  ? { ...vent.originalData } as EditableVentItem
                  : vent
              )
            }
          }),
          hasUnsavedChanges: true
        }
      }

      // 多项目模式：重置 projects 数组
      return {
        ...prev,
        projects: prev.projects.map(project => {
          if (project.id !== projectId) return project

          if (!floorId) {
            // 重置整个项目
            return project.originalData ? { ...project.originalData } as EditableProjectData : project
          }

          return {
            ...project,
            floors: project.floors.map(floor => {
              if (floor.id !== floorId) return floor

              if (!ventId) {
                // 重置整个楼层
                return floor.originalData ? { ...floor.originalData } as EditableFloorData : floor
              }

              return {
                ...floor,
                vents: floor.vents.map(vent =>
                  vent.id === ventId && vent.originalData
                    ? { ...vent.originalData } as EditableVentItem
                    : vent
                )
              }
            })
          }
        }),
        hasUnsavedChanges: true
      }
    })
  }, [])

  // 切换项目选择状态
  const toggleProjectSelection = useCallback((projectId: string) => {
    setState(prev => ({
      ...prev,
      selectedProjectIds: prev.selectedProjectIds.includes(projectId)
        ? prev.selectedProjectIds.filter(id => id !== projectId)
        : [...prev.selectedProjectIds, projectId]
    }))
  }, [])

  // 全选项目
  const selectAllProjects = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedProjectIds: prev.projects.map(p => p.id)
    }))
  }, [])

  // 取消全选
  const deselectAllProjects = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedProjectIds: []
    }))
  }, [])

  // 验证所有数据
  const validateAll = useCallback((): boolean => {
    let isValid = true
    const globalErrors: ValidationError[] = []

    state.projects.forEach(project => {
      project.floors.forEach(floor => {
        floor.vents.forEach(vent => {
          const errors = validateVent(vent)
          if (errors.length > 0) {
            isValid = false
            globalErrors.push(...errors)
          }
        })
      })
    })

    setState(prev => ({
      ...prev,
      isValid,
      globalValidationErrors: globalErrors
    }))

    return isValid
  }, [state.projects])

  // 关闭预览
  const closePreview = useCallback(() => {
    setState(prev => ({
      ...prev,
      showPreview: false,
      projects: [],
      floors: [],
      selectedProjectIds: [],
      hasUnsavedChanges: false,
      editingItemId: undefined
    }))
  }, [])

  // 计算统计信息
  const statistics = useMemo(() => {
    const selectedProjects = state.projects.filter(p => state.selectedProjectIds.includes(p.id))
    
    return {
      totalProjects: selectedProjects.length,
      totalVents: selectedProjects.reduce((sum, project) => 
        sum + project.floors.reduce((floorSum, floor) => 
          floorSum + floor.vents.reduce((ventSum, vent) => ventSum + vent.quantity, 0), 0
        ), 0
      ),
      totalAmount: selectedProjects.reduce((sum, project) => 
        sum + project.floors.reduce((floorSum, floor) => 
          floorSum + floor.vents.reduce((ventSum, vent) => ventSum + vent.totalPrice, 0), 0
        ), 0
      ),
      hasErrors: !state.isValid || state.globalValidationErrors.length > 0
    }
  }, [state.projects, state.selectedProjectIds, state.isValid, state.globalValidationErrors])

  return {
    state,
    actions: {
      initializePreview,
      updateProject,
      updateFloor,
      updateVent,
      addVent,
      removeVent,
      duplicateVent,
      resetItem,
      toggleProjectSelection,
      selectAllProjects,
      deselectAllProjects,
      validateAll,
      closePreview
    },
    statistics
  }
}
