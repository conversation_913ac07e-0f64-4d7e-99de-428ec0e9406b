/**
 * 🔢 数字处理工具函数
 * 解决项目中NaN错误和数据类型转换问题
 */

import type { Decimal } from '@prisma/client/runtime/library'

/**
 * 安全的数字转换函数
 * 处理各种数据类型，确保返回有效数字
 * 🔧 修复：自动处理浮点数精度问题，统一精确到1位小数
 * @param value 任意类型的值
 * @returns 安全的数字，无效值返回0，自动处理精度问题
 */
export const safeNumber = (value: unknown): number => {
  // 处理null和undefined
  if (value === null || value === undefined) return 0

  let result = 0

  // 处理已经是数字的情况
  if (typeof value === 'number') {
    result = isNaN(value) ? 0 : value
  }
  // 处理字符串
  else if (typeof value === 'string') {
    // 移除空格和货币符号
    const cleaned = value.replace(/[¥$,\s]/g, '')
    if (cleaned === '') return 0

    const parsed = parseFloat(cleaned)
    result = isNaN(parsed) ? 0 : parsed
  }
  // 处理Decimal类型（Prisma返回的数据）
  else if (value && typeof value === 'object') {
    // Prisma Decimal类型
    if ('toNumber' in value && typeof value.toNumber === 'function') {
      try {
        result = value.toNumber()
      } catch {
        return 0
      }
    }
    // 其他对象类型，尝试转换为字符串再解析
    else if ('toString' in value) {
      try {
        const str = value.toString()
        const parsed = parseFloat(str)
        result = isNaN(parsed) ? 0 : parsed
      } catch {
        return 0
      }
    }
  }

  // 🔧 关键修复：处理浮点数精度问题，统一精确到1位小数
  // 这样可以避免 4947.780000000001 这样的精度问题
  return Math.round(result * 10) / 10
}

/**
 * 安全的百分比计算
 * @param numerator 分子
 * @param denominator 分母
 * @param decimals 小数位数，默认1位
 * @returns 百分比字符串
 */
export const safePercentage = (numerator: unknown, denominator: unknown, decimals: number = 1): string => {
  const num = safeNumber(numerator)
  const den = safeNumber(denominator)
  
  if (den === 0) return '0.0'
  
  const percentage = (num / den) * 100
  return percentage.toFixed(decimals)
}

/**
 * 安全的金额格式化（不带货币符号）
 * @param amount 金额
 * @param decimals 小数位数，默认1位
 * @returns 格式化的金额字符串（不含前导零）
 */
export const safeAmountFormat = (amount: unknown, decimals: number = 1): string => {
  const num = safeNumber(amount)
  if (num === 0) return decimals === 1 ? '0.0' : '0.' + '0'.repeat(decimals)

  // 🔧 先四舍五入到指定精度，再格式化
  const rounded = Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
  const formatted = rounded.toFixed(decimals)
  // 添加千分位分隔符
  return formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 安全的金额格式化（带货币符号）
 * @param amount 金额
 * @param currency 货币符号，默认¥
 * @param decimals 小数位数，默认1位
 * @returns 格式化的金额字符串
 */
export const safeAmount = (amount: unknown, currency: string = '¥', decimals: number = 1): string => {
  const formatted = safeAmountFormat(amount, decimals)
  return `${currency}${formatted}`
}

/**
 * 安全的千分位格式化（改进版，避免前导零）
 * @param value 数值
 * @param decimals 小数位数，默认0位
 * @returns 带千分位的数字字符串
 */
export const safeLocaleString = (value: unknown, decimals: number = 0): string => {
  const num = safeNumber(value)
  if (num === 0) return decimals > 0 ? '0.' + '0'.repeat(decimals) : '0'

  // 使用自定义格式化避免toLocaleString的前导零问题
  const formatted = num.toFixed(decimals)
  return formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 安全的万元格式化
 * @param amount 金额
 * @param decimals 小数位数，默认1位
 * @returns 万元格式的字符串
 */
export const safeWanYuan = (amount: unknown, decimals: number = 1): string => {
  const num = safeNumber(amount)
  if (num === 0) return '¥0.0万'

  const wanAmount = num / 10000
  const formatted = wanAmount.toFixed(decimals)
  return `¥${formatted}万`
}

/**
 * 智能金额格式化（自动选择最佳显示方式）
 * @param amount 金额
 * @param currency 货币符号，默认¥
 * @returns 格式化的金额字符串
 */
export const smartAmountFormat = (amount: unknown, currency: string = '¥'): string => {
  const num = safeNumber(amount)

  if (num === 0) return `${currency}0.0`

  // 大于等于1万的金额显示为万元
  if (num >= 10000) {
    const wanAmount = num / 10000
    return `${currency}${wanAmount.toFixed(1)}万`
  }

  // 小于1万的金额显示为普通格式 - 使用1位小数
  return safeAmount(num, currency, 1)
}

/**
 * 安全的平均值计算
 * @param values 数值数组
 * @returns 平均值
 */
export const safeAverage = (values: unknown[]): number => {
  if (!Array.isArray(values) || values.length === 0) return 0
  
  const validNumbers = values.map(safeNumber).filter(num => !isNaN(num))
  if (validNumbers.length === 0) return 0
  
  const sum = validNumbers.reduce((acc, num) => acc + num, 0)
  return sum / validNumbers.length
}

/**
 * 安全的求和计算 - 支持精度控制
 * @param values 数值数组
 * @param precision 精度位数，默认1位小数
 * @returns 总和，自动处理精度
 */
export const safeSum = (values: unknown[], precision: number = 1): number => {
  if (!Array.isArray(values)) return 0

  const sum = values.reduce((total, value) => total + safeNumber(value), 0)
  const factor = Math.pow(10, precision)
  return Math.round(sum * factor) / factor
}

/**
 * 检查值是否为有效数字
 * @param value 待检查的值
 * @returns 是否为有效数字
 */
export const isValidNumber = (value: unknown): boolean => {
  const num = safeNumber(value)
  return !isNaN(num) && isFinite(num)
}

/**
 * 数字范围限制
 * @param value 数值
 * @param min 最小值
 * @param max 最大值
 * @returns 限制在范围内的数值
 */
export const clampNumber = (value: unknown, min: number = 0, max: number = Infinity): number => {
  const num = safeNumber(value)
  return Math.max(min, Math.min(max, num))
}

/**
 * 安全的除法运算
 * @param dividend 被除数
 * @param divisor 除数
 * @param defaultValue 除数为0时的默认值
 * @returns 除法结果
 */
export const safeDivide = (dividend: unknown, divisor: unknown, defaultValue: number = 0): number => {
  const num1 = safeNumber(dividend)
  const num2 = safeNumber(divisor)
  
  if (num2 === 0) return defaultValue
  return num1 / num2
}

/**
 * 数字精度处理 - 默认精确到一位小数
 * @param value 数值
 * @param precision 精度位数，默认1位小数
 * @returns 处理精度后的数值
 */
export const safePrecision = (value: unknown, precision: number = 1): number => {
  const num = safeNumber(value)
  const factor = Math.pow(10, precision)
  return Math.round(num * factor) / factor
}

/**
 * 安全的Decimal转换函数
 * 专门处理Prisma Decimal类型
 * @param value Decimal或其他类型的值
 * @returns 安全的数字
 */
export const safeDecimal = (value: number | Decimal | null | undefined): number => {
  if (value === null || value === undefined) return 0

  if (typeof value === 'number') {
    return isNaN(value) ? 0 : value
  }

  // 处理Prisma Decimal类型
  if (value && typeof value === 'object' && 'toNumber' in value) {
    try {
      return (value as Decimal).toNumber()
    } catch {
      return 0
    }
  }

  return safeNumber(value)
}

/**
 * 安全的算术运算 - 加法
 * 处理Decimal类型的加法运算
 */
export const safeAdd = (a: number | Decimal | null | undefined, b: number | Decimal | null | undefined): number => {
  return safeDecimal(a) + safeDecimal(b)
}

/**
 * 价格精度处理 - 专门用于价格计算
 * 统一将所有价格精确到一位小数，四舍五入
 * @param price 价格数值
 * @returns 精确到一位小数的价格
 */
export const safePriceRound = (price: unknown): number => {
  const num = safeNumber(price)
  return Math.round(num * 10) / 10
}

/**
 * 价格格式化显示 - 显示一位小数
 * @param price 价格数值
 * @returns 格式化的价格字符串
 */
export const formatPrice = (price: unknown): string => {
  const num = safePriceRound(price)
  return num.toFixed(1)
}

/**
 * 安全的算术运算 - 减法
 * 处理Decimal类型的减法运算
 */
export const safeSubtract = (a: number | Decimal | null | undefined, b: number | Decimal | null | undefined): number => {
  return safeDecimal(a) - safeDecimal(b)
}



/**
 * 安全的金额累加 - 专门用于金额计算
 * @param amounts 金额数组
 * @returns 累加结果，精确到1位小数
 */
export const safeAmountSum = (amounts: unknown[]): number => {
  return safeSum(amounts, 1)
}

/**
 * 安全的比较运算
 * 处理Decimal类型的比较
 */
export const safeGreaterThan = (a: number | Decimal | null | undefined, b: number | Decimal | null | undefined): boolean => {
  return safeDecimal(a) > safeDecimal(b)
}
