/**
 * 🇨🇳 风口云平台 - 数据同步状态API
 */

import { NextRequest, NextResponse } from 'next/server'
import { dataSyncService } from '@/lib/services/data-sync'

export async function GET(request: NextRequest) {
  try {
    console.log('📊 获取数据同步状态...')

    // 获取同步状态
    const syncStatus = dataSyncService.getSyncStatus()
    
    // 获取同步事件统计
    const syncEvents = dataSyncService.getSyncEvents()
    const recentEvents = syncEvents.slice(0, 10) // 最近10个事件
    
    // 计算统计信息
    const stats = {
      totalEvents: syncEvents.length,
      successfulEvents: syncEvents.filter(e => e.synced).length,
      failedEvents: syncEvents.filter(e => !e.synced).length,
      recentEvents: recentEvents.map(event => ({
        id: event.id,
        type: event.type,
        factoryId: event.factoryId,
        timestamp: event.timestamp,
        synced: event.synced
      }))
    }

    const response = {
      success: true,
      data: {
        syncStatus,
        stats
      },
      message: '数据同步状态获取成功'
    }

    console.log('✅ 数据同步状态获取成功')
    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ 获取数据同步状态失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '获取数据同步状态失败',
      message: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
