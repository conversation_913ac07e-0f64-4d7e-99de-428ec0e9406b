/**
 * 🇨🇳 风口云平台 - 获取公告已读状态API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 获取工厂的公告已读状态
export async function GET(request: NextRequest) {
  try {
    console.log('📢 接收到获取公告已读状态请求')
    
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')

    // 验证必需参数
    if (!factoryId) {
      console.log('❌ 缺少工厂ID参数')
      return NextResponse.json(
        { error: '缺少工厂ID参数' },
        { status: 400 }
      )
    }

    console.log('📝 获取工厂已读状态:', { factoryId })

    // 使用数据库服务获取已读状态
    const readStatus = await db.getAnnouncementReadStatus(factoryId)

    console.log('📊 已读状态详情:', {
      factoryId,
      readStatusKeys: Object.keys(readStatus),
      readStatusValues: readStatus
    })

    return NextResponse.json({
      success: true,
      readStatus: readStatus,
      message: '获取已读状态成功'
    })

  } catch (error) {
    console.error('❌ 获取公告已读状态失败:', error)
    return NextResponse.json(
      { error: `获取公告已读状态失败: ${error.message || '未知错误'}` },
      { status: 500 }
    )
  }
}
