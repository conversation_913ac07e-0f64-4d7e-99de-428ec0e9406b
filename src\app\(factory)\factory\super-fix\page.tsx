'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Zap, CheckCircle, AlertCircle, RefreshCw, Eye } from 'lucide-react'
import { safeAmountFormat } from '@/lib/utils/number-utils'

interface AnalysisResult {
  totalClients: number
  clientsWithRewards: number
  totalCalculatedRewards: number
  totalCurrentRewards: number
  clientsNeedingUpdate: number
  details: Array<{
    clientId: string
    clientName: string
    calculatedReward: number
    currentReward: number
    needsUpdate: boolean
    orders: Array<{
      id: string
      orderNumber?: string
      totalAmount: number
      clientName?: string
      reward: number
    }>
  }>
}

interface FixResult {
  success: boolean
  message?: string
  error?: string
  details?: {
    updatedClients: number
    totalRewardsFixed: number
    isCompletelyFixed: boolean
    remainingIssues: number
  }
}

export default function SuperFixPage() {
  const [loading, setLoading] = useState(false)
  const [analyzing, setAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null)
  const [fixResult, setFixResult] = useState<FixResult | null>(null)

  const analyzeData = async () => {
    try {
      setAnalyzing(true)
      setAnalysis(null)
      
      console.log('🔍 开始分析数据...')

      const response = await fetch('/api/fix-rewards-completely', {
        method: 'GET'
      })

      const data = await response.json()

      if (data.success) {
        setAnalysis(data.data)
        console.log('✅ 分析完成:', data.data)
      } else {
        alert(`分析失败: ${data.error}`)
      }

    } catch (error) {
      console.error('❌ 分析请求失败:', error)
      alert('分析请求失败')
    } finally {
      setAnalyzing(false)
    }
  }

  const fixRewards = async () => {
    try {
      setLoading(true)
      setFixResult(null)
      
      console.log('🔧 开始彻底修复...')

      const response = await fetch('/api/fix-rewards-completely', {
        method: 'POST'
      })

      const data = await response.json()
      setFixResult(data)

      if (data.success) {
        console.log('✅ 修复成功:', data)
        // 修复后重新分析
        setTimeout(() => {
          analyzeData()
        }, 1000)
      } else {
        console.error('❌ 修复失败:', data.error)
      }

    } catch (error) {
      console.error('❌ 修复请求失败:', error)
      setFixResult({
        success: false,
        error: error instanceof Error ? error.message : '修复请求失败'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">🎁 推荐奖励超级修复工具</h1>
            <p className="text-gray-600 text-lg">
              专为小白用户设计的一键修复工具，彻底解决推荐奖励问题
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-center space-x-4 mb-8">
            <Button 
              onClick={analyzeData} 
              disabled={analyzing || loading}
              variant="outline"
              size="lg"
            >
              {analyzing ? (
                <>
                  <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                  分析中...
                </>
              ) : (
                <>
                  <Eye className="h-5 w-5 mr-2" />
                  1. 先分析数据
                </>
              )}
            </Button>

            <Button 
              onClick={fixRewards} 
              disabled={loading || analyzing}
              size="lg"
              className="bg-red-600 hover:bg-red-700"
            >
              {loading ? (
                <>
                  <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                  修复中...
                </>
              ) : (
                <>
                  <Zap className="h-5 w-5 mr-2" />
                  2. 彻底修复
                </>
              )}
            </Button>
          </div>

          {/* 分析结果 */}
          {analysis && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>📊 数据分析结果</span>
                  {analysis.clientsNeedingUpdate > 0 ? (
                    <Badge variant="destructive">发现 {analysis.clientsNeedingUpdate} 个问题</Badge>
                  ) : (
                    <Badge variant="default" className="bg-green-600">数据正常</Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{analysis.totalClients}</div>
                    <div className="text-sm text-gray-600">总客户数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{analysis.clientsWithRewards}</div>
                    <div className="text-sm text-gray-600">推荐人数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">¥{safeAmountFormat(analysis.totalCalculatedRewards)}</div>
                    <div className="text-sm text-gray-600">应得奖励</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">¥{safeAmountFormat(analysis.totalCurrentRewards)}</div>
                    <div className="text-sm text-gray-600">当前奖励</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${analysis.clientsNeedingUpdate > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {analysis.clientsNeedingUpdate}
                    </div>
                    <div className="text-sm text-gray-600">需要修复</div>
                  </div>
                </div>

                {/* 详细列表 */}
                <div className="space-y-4">
                  {analysis.details.map((client) => (
                    <div key={client.clientId} className={`p-4 rounded-lg border ${
                      client.needsUpdate ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
                    }`}>
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-semibold text-lg">{client.clientName}</h3>
                        {client.needsUpdate ? (
                          <Badge variant="destructive">❌ 需要修复</Badge>
                        ) : (
                          <Badge variant="default" className="bg-green-600">✅ 正常</Badge>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 mb-3">
                        <div>
                          <span className="text-sm text-gray-600">应得奖励: </span>
                          <span className="font-mono font-bold text-green-600">¥{safeAmountFormat(client.calculatedReward)}</span>
                        </div>
                        <div>
                          <span className="text-sm text-gray-600">当前奖励: </span>
                          <span className="font-mono font-bold text-blue-600">¥{safeAmountFormat(client.currentReward)}</span>
                        </div>
                      </div>

                      {client.orders.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">奖励来源订单:</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {client.orders.map((order) => (
                              <div key={order.id} className="text-xs bg-white p-2 rounded border">
                                <div className="font-medium">{order.orderNumber || order.id}</div>
                                <div className="text-gray-600">
                                  {order.clientName} - ¥{safeAmountFormat(order.totalAmount)} → ¥{safeAmountFormat(order.reward)}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 修复结果 */}
          {fixResult && (
            <Card className={`border-2 ${fixResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
              <CardHeader>
                <CardTitle className={`flex items-center space-x-2 ${fixResult.success ? 'text-green-800' : 'text-red-800'}`}>
                  {fixResult.success ? (
                    <>
                      <CheckCircle className="h-5 w-5" />
                      <span>🎉 修复成功！</span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-5 w-5" />
                      <span>修复失败</span>
                    </>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className={fixResult.success ? 'text-green-700' : 'text-red-700'}>
                <p className="text-lg font-medium mb-4">
                  {fixResult.success ? fixResult.message : fixResult.error}
                </p>
                
                {fixResult.success && fixResult.details && (
                  <div className="bg-white p-4 rounded-lg border">
                    <h4 className="font-medium text-gray-900 mb-3">修复详情:</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">更新客户: </span>
                        <span className="font-bold">{fixResult.details.updatedClients} 个</span>
                      </div>
                      <div>
                        <span className="text-gray-600">修复奖励: </span>
                        <span className="font-bold">¥{safeAmountFormat(fixResult.details.totalRewardsFixed)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">完全修复: </span>
                        <span className={`font-bold ${fixResult.details.isCompletelyFixed ? 'text-green-600' : 'text-orange-600'}`}>
                          {fixResult.details.isCompletelyFixed ? '是' : '否'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">剩余问题: </span>
                        <span className="font-bold">{fixResult.details.remainingIssues} 个</span>
                      </div>
                    </div>
                    
                    <div className="mt-4 p-3 bg-green-100 rounded text-green-800">
                      <p className="font-medium">✅ 现在您可以:</p>
                      <p>1. 刷新客户管理页面</p>
                      <p>2. 查看王先生等客户的推荐奖励</p>
                      <p>3. 确认奖励金额是否正确</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 使用说明 */}
          <Card className="mt-8 border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-800">📖 使用说明</CardTitle>
            </CardHeader>
            <CardContent className="text-blue-700">
              <div className="space-y-2">
                <p><strong>第1步</strong>: 点击"先分析数据"查看当前奖励状态</p>
                <p><strong>第2步</strong>: 如果发现问题，点击"彻底修复"</p>
                <p><strong>第3步</strong>: 修复完成后，去客户管理页面验证结果</p>
                <p className="text-sm mt-4 p-3 bg-blue-100 rounded">
                  💡 <strong>小白提示</strong>: 这个工具会自动重置所有错误的奖励数据，然后重新计算正确的奖励。
                  王先生推荐客户订单¥2,344.68，应该获得奖励¥46.89 (2,344.68 × 2%)
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
