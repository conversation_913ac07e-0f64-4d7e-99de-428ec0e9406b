# 豆包AI集成文档

## 概述

本项目已成功集成豆包AI（火山引擎），为"AI智能识别"功能增加了新的AI服务提供商选项。豆包AI是字节跳动开发的大语言模型，具有优秀的中文理解能力和性价比。

## 功能特性

### 🚀 核心功能
- **风口订单智能识别**：基于豆包AI的文本理解能力，准确识别风口类型、尺寸、数量等信息
- **兼容OpenAI API格式**：使用标准的OpenAI API接口格式，便于集成和维护
- **智能错误处理**：包含重试机制、自动回退和备用解析功能
- **多模型支持**：支持多种豆包AI模型规格

### 🎯 识别规则
豆包AI使用与通义千问相同的识别规则：
- **风口类型判断**：根据关键词和尺寸特征智能判断出风口/回风口
- **尺寸智能解析**：支持多种分隔符，自动单位转换
- **楼层房间识别**：智能提取项目信息、楼层和房间结构
- **备注信息提取**：识别特殊规格和工艺要求

## API配置

### 1. API密钥配置
在 `src/lib/config/api-keys.ts` 中已添加豆包AI配置：

```typescript
export const API_KEYS = {
  // 豆包AI（火山引擎）API密钥
  DOUBAO: process.env.NEXT_PUBLIC_DOUBAO_API_KEY || '20dc1a0c-8219-4280-9441-901efe0d7637'
}
```

### 2. API提供商配置
```typescript
doubao: {
  name: '豆包AI',
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  models: ['doubao-lite-4k', 'doubao-lite-32k', 'doubao-pro-4k', 'doubao-pro-32k', 'doubao-pro-128k'],
  description: '字节跳动豆包大模型，兼容OpenAI API格式',
  pricing: {
    input: '¥1.00/M tokens',
    output: '¥2.00/M tokens'
  },
  advantages: [
    '字节跳动技术',
    '兼容OpenAI API格式',
    '多种模型规格',
    '性价比高'
  ]
}
```

## 技术实现

### 1. 核心服务类
`src/lib/services/doubao-api.ts` - 豆包AI API服务实现

主要方法：
- `callAPI()` - 调用豆包AI API（带重试机制）
- `testConnection()` - 测试API连接
- `analyzeVentOrder()` - 分析风口订单
- `parseResponse()` - 解析API响应
- `fallbackParse()` - 备用解析方法

### 2. UI组件集成
已在以下组件中添加豆包AI支持：
- `src/components/ai/ai-paste-recognition.tsx` - AI粘贴识别组件
- `src/components/ocr/intelligent-ocr-upload.tsx` - 智能OCR上传组件

### 3. API路由
`src/app/api/ai/analyze/route.ts` - 添加豆包AI处理逻辑

## 使用方法

### 1. 在AI识别界面
1. 打开AI智能识别页面
2. 在"API提供商选择"中选择"豆包AI"
3. 输入风口文本内容
4. 点击"🚀 开始AI智能识别"

### 2. 在OCR识别中
豆包AI已设置为OCR智能识别的默认AI服务提供商，会自动使用豆包AI进行文本分析。

### 3. 测试页面
访问 `/test-doubao` 页面可以直接测试豆包AI的功能。

## 错误处理

### 1. 自动回退机制
当豆包AI服务不可用时，系统会自动回退到DeepSeek官方API：
```typescript
} catch (doubaoError) {
  console.warn('🔄 豆包AI失败，自动切换到DeepSeek官方API:', doubaoError)
  // 自动回退逻辑
}
```

### 2. 连接测试
提供连接测试功能，可以验证API密钥和网络连接状态。

### 3. 备用解析
当JSON解析失败时，使用正则表达式进行备用解析，确保系统稳定性。

## 性能优化

### 1. 智能JSON修复
- 自动修复截断的JSON响应
- 处理常见的JSON格式问题
- 多层级解析策略

### 2. 缓存和重试
- 指数退避重试机制
- 请求超时控制（60秒）
- 性能监控和日志记录

## 配置说明

### 环境变量
```bash
# 豆包AI API密钥
NEXT_PUBLIC_DOUBAO_API_KEY=your_doubao_api_key_here
```

### 默认配置
- **默认模型**：`doubao-lite-4k`
- **API端点**：`https://ark.cn-beijing.volces.com/api/v3`
- **超时时间**：60秒
- **重试次数**：1次
- **温度参数**：0.2
- **最大令牌数**：2000

## 监控和日志

系统提供详细的日志记录：
- API调用耗时监控
- 响应解析状态
- 错误详情和回退原因
- 性能指标统计

## 注意事项

1. **API密钥安全**：请妥善保管API密钥，不要在代码中硬编码
2. **费用控制**：豆包AI按使用量计费，请注意控制使用成本
3. **网络要求**：需要稳定的网络连接访问火山引擎API
4. **模型选择**：根据实际需求选择合适的模型规格

## 故障排除

### 常见问题
1. **连接超时**：检查网络连接和API密钥
2. **解析失败**：查看日志中的详细错误信息
3. **识别不准确**：可以尝试调整prompt或使用其他AI服务

### 调试方法
1. 使用测试页面验证功能
2. 查看浏览器控制台日志
3. 检查API密钥配置
4. 测试网络连接

## 更新日志

### v1.0.0 (2025-01-28)
- ✅ 完成豆包AI基础集成
- ✅ 实现风口订单识别功能
- ✅ 添加错误处理和自动回退
- ✅ 集成到现有UI组件
- ✅ 创建测试页面和文档
