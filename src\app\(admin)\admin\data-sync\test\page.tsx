'use client'

/**
 * 🇨🇳 风口云平台 - 数据同步测试页面
 * 
 * 功能说明：
 * - 测试数据同步功能
 * - 模拟数据变更和同步
 * - 验证数据一致性
 */

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import {
  Play,
  CheckCircle,
  AlertCircle,
  Clock,
  TestTube,
  RefreshCw
} from "lucide-react"
import { db } from '@/lib/database'

interface TestResult {
  id: string
  name: string
  status: 'pending' | 'running' | 'success' | 'error'
  message: string
  duration?: number
  details?: unknown
}

export default function DataSyncTest() {
  const [tests, setTests] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  // 测试用例定义
  const testCases = [
    {
      id: 'create-client',
      name: '创建客户测试',
      description: '测试创建客户时的数据同步',
      action: async () => {
        const clientData = {
          factoryId: 'test-factory',
          name: `测试客户_${Date.now()}`,
          phone: '13800138000',
          address: '测试地址',
          status: 'active' as const
        }
        
        const result = await db.createClient(clientData)
        return { success: !!result, data: result }
      }
    },
    {
      id: 'create-order',
      name: '创建订单测试',
      description: '测试创建订单时的数据同步',
      action: async () => {
        const orderData = {
          factoryId: 'test-factory',
          clientId: 'test-client',
          orderNumber: `TEST-${Date.now()}`,
          items: [
            {
              id: '1',
              productName: '测试风口',
              productType: 'square',
              specifications: '600×400mm',
              dimensions: { length: 600, width: 400 },
              floor: '1F',
              quantity: 10,
              unitPrice: 150,
              totalPrice: 1500,
              notes: ''
            }
          ],
          totalAmount: 1500,
          status: 'pending' as const,
          projectAddress: '测试项目地址',
          notes: '测试订单',
          createdBy: 'test-user',
          createdByName: '测试用户',
          createdAt: new Date(),
          updatedAt: new Date()
        }
        
        const result = await db.createOrder(orderData)
        return { success: !!result, data: result }
      }
    },
    {
      id: 'check-consistency',
      name: '数据一致性检查',
      description: '检查工厂数据的一致性',
      action: async () => {
        const result = await db.checkDataConsistency('test-factory')
        return { success: result.success, data: result }
      }
    },
    {
      id: 'fix-inconsistency',
      name: '数据一致性修复',
      description: '修复发现的数据不一致问题',
      action: async () => {
        const result = await db.fixDataInconsistency('test-factory')
        return { success: result.success, data: result }
      }
    },
    {
      id: 'full-sync',
      name: '全量数据同步',
      description: '执行全量数据同步',
      action: async () => {
        const result = await db.performFullDataSync()
        return { success: result.success, data: result }
      }
    }
  ]

  // 运行单个测试
  const runSingleTest = async (testCase: any) => {
    const testId = testCase.id
    
    // 更新测试状态为运行中
    setTests(prev => prev.map(test => 
      test.id === testId 
        ? { ...test, status: 'running', message: '测试运行中...' }
        : test
    ))

    const startTime = Date.now()
    
    try {
      const result = await testCase.action()
      const duration = Date.now() - startTime
      
      setTests(prev => prev.map(test => 
        test.id === testId 
          ? { 
              ...test, 
              status: result.success ? 'success' : 'error',
              message: result.success ? '测试通过' : '测试失败',
              duration,
              details: result.data
            }
          : test
      ))
    } catch (error) {
      const duration = Date.now() - startTime
      
      setTests(prev => prev.map(test => 
        test.id === testId 
          ? { 
              ...test, 
              status: 'error',
              message: `测试失败: ${error instanceof Error ? error.message : String(error)}`,
              duration,
              details: error
            }
          : test
      ))
    }
  }

  // 运行所有测试
  const runAllTests = async () => {
    setIsRunning(true)
    
    // 初始化测试状态
    const initialTests = testCases.map(testCase => ({
      id: testCase.id,
      name: testCase.name,
      status: 'pending' as const,
      message: '等待运行...'
    }))
    setTests(initialTests)

    // 依次运行所有测试
    for (const testCase of testCases) {
      await runSingleTest(testCase)
      // 在测试之间添加短暂延迟
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    setIsRunning(false)
  }

  // 清除测试结果
  const clearResults = () => {
    setTests([])
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-500" />
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-800'
      case 'running': return 'bg-blue-100 text-blue-800'
      case 'success': return 'bg-green-100 text-green-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const successCount = tests.filter(t => t.status === 'success').length
  const errorCount = tests.filter(t => t.status === 'error').length
  const runningCount = tests.filter(t => t.status === 'running').length

  return (
    <DashboardLayout role="admin">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">数据同步测试</h1>
            <p className="text-gray-600">测试和验证数据同步功能的正确性</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={clearResults}
              disabled={isRunning}
            >
              清除结果
            </Button>
            <Button
              onClick={runAllTests}
              disabled={isRunning}
            >
              <TestTube className={`h-4 w-4 mr-2 ${isRunning ? 'animate-pulse' : ''}`} />
              运行所有测试
            </Button>
          </div>
        </div>

        {/* 测试统计 */}
        {tests.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">总测试数</p>
                    <p className="text-2xl font-bold">{tests.length}</p>
                  </div>
                  <TestTube className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">通过测试</p>
                    <p className="text-2xl font-bold text-green-600">{successCount}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">失败测试</p>
                    <p className="text-2xl font-bold text-red-600">{errorCount}</p>
                  </div>
                  <AlertCircle className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">运行中</p>
                    <p className="text-2xl font-bold text-blue-600">{runningCount}</p>
                  </div>
                  <RefreshCw className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 测试用例列表 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 可用测试 */}
          <Card>
            <CardHeader>
              <CardTitle>可用测试</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testCases.map((testCase) => (
                  <div key={testCase.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{testCase.name}</h4>
                      <p className="text-sm text-gray-600">{testCase.description}</p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => runSingleTest(testCase)}
                      disabled={isRunning}
                    >
                      <Play className="h-4 w-4 mr-1" />
                      运行
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 测试结果 */}
          <Card>
            <CardHeader>
              <CardTitle>测试结果</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {tests.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">暂无测试结果</p>
                ) : (
                  tests.map((test) => (
                    <div key={test.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(test.status)}
                          <h4 className="font-medium">{test.name}</h4>
                        </div>
                        <Badge className={getStatusColor(test.status)}>
                          {test.status === 'pending' && '等待中'}
                          {test.status === 'running' && '运行中'}
                          {test.status === 'success' && '成功'}
                          {test.status === 'error' && '失败'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{test.message}</p>
                      {test.duration && (
                        <p className="text-xs text-gray-500">
                          执行时间: {test.duration}ms
                        </p>
                      )}
                      {test.details && test.status === 'error' && (
                        <div className="mt-2 p-2 bg-red-50 rounded text-xs">
                          <pre className="text-red-700 whitespace-pre-wrap">
                            {JSON.stringify(test.details, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
