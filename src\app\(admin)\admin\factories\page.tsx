"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { AddFactoryDialog } from "@/components/admin/add-factory-dialog"
import { db } from "@/lib/database/client"
import { dataSyncService } from "@/lib/services/data-sync"
import { copyToClipboard } from "@/utils/clipboard"
import { safeNumber, safeWanYuan, safeLocaleString } from "@/lib/utils/number-utils"
import { useAuthStore } from "@/lib/store/auth"
import { AdminRouteGuard } from "@/components/auth/route-guard"
import { AuthService } from "@/lib/services/auth.service"
import { isJWTExpired, isJWTExpiringSoon } from "@/lib/auth/jwt"
import {
  getSubscriptionStatusText,
  formatSubscriptionType,
  canFactoryOperate,
  SUBSCRIPTION_CONFIGS,
  createSubscriptionConfig,
  type SubscriptionType
} from "@/lib/utils/factory-subscription"
import {
  Factory,
  Users,
  ShoppingCart,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Building2,
  Phone,
  Mail,
  MapPin,
  Key,
  EyeOff,
  Save,
  Database,
  Clock,
  Calendar,
  Settings,
  Play,
  Pause,
  Crown
} from "lucide-react"

// 模拟工厂数据
const mockFactories = [
  {
    id: "factory-1",
    name: "华东风口制造厂",
    code: "HD001",
    location: "上海市浦东新区",
    contactPerson: "张经理",
    contactPhone: "13800138001",
    email: "<EMAIL>",
    status: "active" as const,
    totalClients: 156,
    totalOrders: 234,
    monthlyRevenue: 285000,
    createdAt: "2024-01-01"
  },
  {
    id: "factory-2",
    name: "广州精工风口厂",
    code: "GZ002",
    location: "广州市天河区",
    contactPerson: "李经理",
    contactPhone: "13800138002",
    email: "<EMAIL>",
    status: "active" as const,
    totalClients: 89,
    totalOrders: 167,
    monthlyRevenue: 198000,
    createdAt: "2024-01-05"
  },
  {
    id: "factory-3",
    name: "北京天成风口厂",
    code: "BJ003",
    location: "北京市朝阳区",
    contactPerson: "王经理",
    contactPhone: "13800138003",
    email: "<EMAIL>",
    status: "active" as const,
    totalClients: 203,
    totalOrders: 298,
    monthlyRevenue: 356000,
    createdAt: "2024-01-10"
  },
  {
    id: "factory-4",
    name: "深圳创新风口厂",
    code: "SZ004",
    location: "深圳市南山区",
    contactPerson: "陈经理",
    contactPhone: "13800138004",
    email: "<EMAIL>",
    status: "inactive" as const,
    totalClients: 45,
    totalOrders: 78,
    monthlyRevenue: 89000,
    createdAt: "2024-01-15"
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'text-green-600 bg-green-100'
    case 'inactive': return 'text-gray-600 bg-gray-100'
    case 'suspended': return 'text-red-600 bg-red-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '正常运营'
    case 'inactive': return '暂停营业'
    case 'suspended': return '已停用'
    default: return '未知'
  }
}

export default function FactoriesPage() {
  // 使用新的认证系统
  const { accessToken, tokenType, isAuthenticated, user } = useAuthStore()

  // Token 状态检查
  const [tokenStatus, setTokenStatus] = useState<{
    isExpired: boolean
    isExpiringSoon: boolean
    expirationTime?: string
  }>({ isExpired: false, isExpiringSoon: false })

  const [searchTerm, setSearchTerm] = useState("")
  const [factories, setFactories] = useState<typeof mockFactories>([])
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [loading, setLoading] = useState(false)
  const [selectedFactory, setSelectedFactory] = useState<any>(null)
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletePassword, setDeletePassword] = useState("")
  const [deleteLoading, setDeleteLoading] = useState(false)

  // 密码管理相关状态
  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [factoryUsers, setFactoryUsers] = useState<unknown[]>([])
  const [selectedUser, setSelectedUser] = useState<unknown>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [adminPassword, setAdminPassword] = useState("")
  const [passwordLoading, setPasswordLoading] = useState(false)

  // 订阅管理相关状态
  const [showSubscriptionDialog, setShowSubscriptionDialog] = useState(false)
  const [subscriptionType, setSubscriptionType] = useState<SubscriptionType>('monthly')
  const [subscriptionLoading, setSubscriptionLoading] = useState(false)

  // 编辑工厂相关状态
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingFactory, setEditingFactory] = useState<any>(null)
  const [editLoading, setEditLoading] = useState(false)
  const [editFormData, setEditFormData] = useState({
    name: '',
    code: '',
    address: '',
    phone: '',
    email: '',
    adminName: '',
    adminPhone: '',
    adminEmail: ''
  })

  // 从数据库加载工厂数据并计算实时统计
  useEffect(() => {
    console.log('🔄 组件挂载，开始加载工厂数据...')
    loadFactoriesWithStats()
  }, [])

  // Token 状态监控
  useEffect(() => {
    const checkTokenStatus = () => {
      if (!accessToken) {
        setTokenStatus({ isExpired: true, isExpiringSoon: false })
        return
      }

      try {
        const expired = isJWTExpired(accessToken)
        const expiringSoon = isJWTExpiringSoon(accessToken, 30)

        // 获取过期时间用于显示
        let expirationTimeString = ''
        try {
          const parts = accessToken.split('.')
          if (parts.length === 3) {
            // 使用我们的安全解码函数
            const payload = parts[1]
              .replace(/-/g, '+')
              .replace(/_/g, '/')
            let fixedPayload = payload
            while (fixedPayload.length % 4 !== 0) {
              fixedPayload += '='
            }
            const decodedPayload = atob(fixedPayload)
            const parsedPayload = JSON.parse(decodedPayload)
            if (parsedPayload.exp) {
              expirationTimeString = new Date(parsedPayload.exp * 1000).toLocaleString()
            }
          }
        } catch (e) {
          // 忽略解析错误，只是为了显示
        }

        setTokenStatus({
          isExpired: expired,
          isExpiringSoon: expiringSoon,
          expirationTime: expirationTimeString
        })

        if (expired) {
          console.log('⚠️ Token 已过期，尝试刷新...')
          AuthService.checkAndRefreshToken()
        } else if (expiringSoon) {
          console.log('⚠️ Token 即将过期，主动刷新...')
          AuthService.checkAndRefreshToken()
        }
      } catch (error) {
        console.error('❌ Token 状态检查失败:', error)
        setTokenStatus({ isExpired: true, isExpiringSoon: false })
      }
    }

    // 立即检查一次
    checkTokenStatus()

    // 每分钟检查一次
    const interval = setInterval(checkTokenStatus, 60000)

    return () => clearInterval(interval)
  }, [accessToken])

  const loadFactoriesWithStats = async () => {
    try {
      setLoading(true)
      console.log('🔄 开始加载工厂数据和统计信息...')

      // 检查认证状态
      console.log('🔍 认证状态检查:', {
        isAuthenticated,
        hasAccessToken: !!accessToken,
        tokenType,
        userType: user?.role || 'unknown',
        userName: user?.name || 'unknown'
      })

      if (!accessToken) {
        console.error('❌ 未找到访问令牌，无法获取管理员数据')
        return
      }

      // 获取工厂基本信息（管理员API，包含完整订阅信息）
      console.log('🔄 开始获取工厂数据...', new Date().toISOString())
      console.log('🔑 使用访问令牌:', accessToken ? `已设置 (${accessToken.substring(0, 20)}...)` : '未设置')
      const storedFactories = await db.getAllFactories()
      console.log('📋 获取到工厂数据:', storedFactories.length, '个工厂')
      console.log('📋 工厂详细信息:', storedFactories)

      // 详细调试每个工厂的 ownerName 字段
      storedFactories.forEach((factory, index) => {
        console.log(`🔍 工厂 ${index + 1} 详细信息:`)
        console.log(`  - 工厂名称: ${factory.name}`)
        console.log(`  - 工厂编码: ${factory.code}`)
        console.log(`  - ownerName 字段:`, factory.ownerName)
        console.log(`  - users 字段:`, factory.users)
        console.log(`  - 完整对象:`, JSON.stringify(factory, null, 2))
      })

      // 为每个工厂计算实时统计数据
      const factoriesWithStats = await Promise.all(
        storedFactories.map(async (factory) => {
          try {
            console.log(`🔍 开始获取工厂 ${factory.name} (${factory.id}) 的统计数据...`)

            // 直接获取客户和订单数据进行调试
            const clients = await db.getClientsByFactoryId(factory.id)
            const orders = await db.getOrdersByFactoryId(factory.id)

            console.log(`📊 工厂 ${factory.name} 原始数据:`)
            console.log(`   - 客户数量: ${clients.length}`)
            console.log(`   - 订单数量: ${orders.length}`)
            console.log(`   - 客户详情:`, clients)
            console.log(`   - 订单详情:`, orders)

            // 获取工厂的统计数据
            const stats = await dataSyncService.getFactoryStatistics(factory.id)
            console.log(`📊 工厂 ${factory.name} 统计结果:`, stats)
            console.log(`👤 工厂 ${factory.name} 管理员姓名:`, factory.ownerName)

            const contactPerson = factory.ownerName || '未设置管理员'
            console.log(`👤 工厂 ${factory.name} 最终联系人显示:`, contactPerson)

            return {
              ...factory,
              // 🔧 修复：只有当字段真正为 null/undefined 时才设置默认值
              subscriptionType: factory.subscriptionType ?? 'trial',
              isPermanent: factory.isPermanent ?? false,
              status: factory.status ?? 'active',

              location: factory.address || '未设置', // 适配显示字段，确保不为null
              contactPerson: contactPerson, // 使用真实管理员姓名
              contactPhone: factory.phone || '未设置', // 映射 phone 到 contactPhone，确保不为null
              email: factory.email || '未设置', // 确保 email 不为 null
              createdAt: (() => {
                try {
                  const date = new Date(factory.createdAt)
                  return isNaN(date.getTime()) ? '未知' : date.toISOString().split('T')[0]
                } catch (error) {
                  return '未知'
                }
              })(),
              totalClients: safeNumber(stats.totalClients),
              totalOrders: safeNumber(stats.totalOrders),
              monthlyRevenue: safeNumber(stats.monthlyRevenue)
            }
          } catch (error) {
            console.warn(`获取工厂 ${factory.name} 统计数据失败:`, error)

            const contactPerson = factory.ownerName || '未设置管理员'
            console.log(`👤 工厂 ${factory.name} 错误情况下的最终联系人显示:`, contactPerson)

            return {
              ...factory,
              // 🔧 修复：只有当字段真正为 null/undefined 时才设置默认值
              subscriptionType: factory.subscriptionType ?? 'trial',
              isPermanent: factory.isPermanent ?? false,
              status: factory.status ?? 'active',

              location: factory.address || '未设置',
              contactPerson: contactPerson, // 使用真实管理员姓名
              contactPhone: factory.phone || '未设置', // 映射 phone 到 contactPhone，确保不为null
              email: factory.email || '未设置', // 确保 email 不为 null
              createdAt: (() => {
                try {
                  const date = new Date(factory.createdAt)
                  return isNaN(date.getTime()) ? '未知' : date.toISOString().split('T')[0]
                } catch (error) {
                  return '未知'
                }
              })(),
              totalClients: 0,
              totalOrders: 0,
              monthlyRevenue: 0
            }
          }
        })
      )

      setFactories(factoriesWithStats as any)
      console.log('✅ 工厂数据和统计信息加载完成:', factoriesWithStats)
    } catch (error) {
      console.error('❌ 加载工厂数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 初始化测试数据
  const initTestData = async () => {
    try {
      setLoading(true)
      console.log('🔄 开始初始化测试数据...')

      if (!accessToken) {
        console.error('❌ 没有访问令牌')
        return
      }

      const response = await fetch('/api/admin/init-test-data', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      })

      const result = await response.json()

      if (result.success) {
        alert('测试数据初始化成功！现在可以看到工厂的客户和订单数据了。')
        // 重新加载工厂数据
        await loadFactoriesWithStats()
      } else {
        alert(`初始化失败：${result.error}`)
      }
    } catch (error) {
      console.error('初始化测试数据失败:', error)
      alert('初始化失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const filteredFactories = factories.filter(factory =>
    factory.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    factory.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    factory.location.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 处理创建工厂
  const handleCreateFactory = async (data: { factory: any; admin: any }) => {
    try {
      console.log('创建工厂:', data.factory)
      console.log('创建管理员:', data.admin)

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 检查工厂编码是否已存在
      const codeExists = await db.checkFactoryCodeExists(data.factory.code)
      if (codeExists) {
        alert('工厂编码已存在，请使用其他编码')
        return
      }

      // 检查管理员用户名是否已存在
      const usernameExists = await db.checkUsernameExists(data.admin.username)
      if (usernameExists) {
        alert('管理员用户名已存在，请使用其他用户名')
        return
      }

      // 使用数据库服务创建工厂和管理员 - 只传递数据库中存在的字段
      const result = await db.createFactoryWithAdmin(
        {
          name: data.factory.name,
          code: data.factory.code,
          address: data.factory.address,
          phone: data.factory.contactPhone, // 映射 contactPhone 到 phone
          email: data.factory.email,
          status: 'active'
          // 移除数据库中不存在的字段：contactPerson, totalClients, totalOrders, monthlyRevenue
        },
        {
          username: data.admin.username,
          name: data.admin.name,
          email: data.admin.email,
          phone: data.admin.phone,
          role: 'owner',
          permissions: ['all'],
          isActive: true,
          createdBy: 'admin',
          passwordHash: '', // 这个字段会在数据库服务中被正确设置
          userType: 'factory_user' as const
        },
        data.admin.password // 明文密码，将在数据库服务中加密
      )

      // 显示成功消息
      alert(`工厂 "${result.factory.name}" 创建成功！\n\n管理员登录信息：\n用户名：${result.admin.username}\n密码：${data.admin.password}\n\n请将登录信息发送给工厂管理员。\n\n工厂管理员可以在工厂登录页面使用此账号登录。`)

      // 重新加载工厂数据以确保数据同步
      console.log('🔄 重新加载工厂数据以确保同步...')
      await loadFactoriesWithStats()
    } catch (error) {
      console.error('创建工厂失败:', error)
      alert('创建失败，请重试')
    }
  }

  // 查看工厂详情
  const handleViewFactory = (factory: unknown) => {
    setSelectedFactory(factory)
    setShowViewDialog(true)
  }

  // 删除工厂
  const handleDeleteFactory = (factory: unknown) => {
    setSelectedFactory(factory)
    setShowDeleteDialog(true)
    setDeletePassword("")
  }

  // 确认删除工厂
  const confirmDeleteFactory = async () => {
    if (!selectedFactory) return

    // 动态验证管理员密码
    try {
      const response = await fetch('/api/auth/admin/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ adminId: 'admin-1', password: deletePassword })
      })

      const data = await response.json()

      if (!data.success || !data.isValid) {
        alert("管理员密码错误！")
        return
      }
    } catch (error) {
      console.error('验证管理员密码失败:', error)
      alert('验证管理员密码失败，请重试')
      return
    }

    try {
      setDeleteLoading(true)

      // 删除工厂相关数据
      await db.deleteFactory((selectedFactory as any).id)

      // 更新本地状态
      setFactories(prev => prev.filter(f => f.id !== (selectedFactory as any).id))

      // 关闭对话框
      setShowDeleteDialog(false)
      setSelectedFactory(null)
      setDeletePassword("")

      alert(`工厂 "${(selectedFactory as any).name}" 已成功删除！`)
    } catch (error) {
      console.error('删除工厂失败:', error)
      alert('删除失败，请重试')
    } finally {
      setDeleteLoading(false)
    }
  }

  // 编辑工厂相关函数
  const handleEditFactory = (factory: any) => {
    console.log('🔧 开始编辑工厂:', factory.name)
    setEditingFactory(factory)
    setEditFormData({
      name: factory.name || '',
      code: factory.code || '',
      address: factory.address || '',
      phone: factory.phone || factory.contactPhone || '',
      email: factory.email || '',
      adminName: factory.ownerName || '',
      adminPhone: factory.phone || factory.contactPhone || '',
      adminEmail: factory.email || ''
    })
    setShowEditDialog(true)
  }

  const handleSaveEdit = async () => {
    if (!editingFactory) return

    try {
      setEditLoading(true)
      console.log('💾 保存工厂编辑:', editFormData)

      // 准备工厂数据
      const factoryData = {
        name: editFormData.name,
        code: editFormData.code,
        address: editFormData.address,
        phone: editFormData.phone,
        email: editFormData.email
      }

      // 准备管理员数据
      const adminData = {
        name: editFormData.adminName,
        phone: editFormData.adminPhone,
        email: editFormData.adminEmail
      }

      // 调用API更新工厂信息
      const response = await fetch('/api/factories', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factoryId: editingFactory.id,
          factoryData,
          adminData
        })
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '更新失败')
      }

      console.log('✅ 工厂信息更新成功')

      // 重新加载工厂数据
      await loadFactoriesWithStats()

      // 关闭对话框
      setShowEditDialog(false)
      setEditingFactory(null)

      alert('工厂信息更新成功！')

    } catch (error) {
      console.error('❌ 更新工厂信息失败:', error)
      alert('更新失败，请重试')
    } finally {
      setEditLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setShowEditDialog(false)
    setEditingFactory(null)
    setEditFormData({
      name: '',
      code: '',
      address: '',
      phone: '',
      email: '',
      adminName: '',
      adminPhone: '',
      adminEmail: ''
    })
  }

  // 密码管理相关函数
  const handlePasswordManagement = async (factory: any) => {
    try {
      setSelectedFactory(factory)
      setPasswordLoading(true)

      console.log('🔄 开始获取工厂用户:', factory.id)

      // 直接使用 API 调用获取工厂的所有用户
      const response = await fetch(`/api/employees?factoryId=${factory.id}`)
      const data = await response.json()

      if (data.success) {
        console.log('✅ 获取工厂用户成功:', data.employees.length, '个用户')
        setFactoryUsers(data.employees)
        setShowPasswordDialog(true)
      } else {
        console.error('❌ 获取工厂用户失败:', data.error)
        alert(`获取用户信息失败：${data.error}`)
      }
    } catch (error) {
      console.error('❌ 获取工厂用户请求失败:', error)
      alert('获取用户信息失败，请重试')
    } finally {
      setPasswordLoading(false)
    }
  }

  // 查看用户密码
  const handleViewPassword = (user: unknown) => {
    setSelectedUser(user)
    setShowPassword(true)
    setNewPassword("")
    setConfirmPassword("")
    setAdminPassword("")
  }

  // 修改用户密码
  const handleChangePassword = async () => {
    if (!selectedUser) return

    // 验证新密码
    if (!newPassword || newPassword.length < 6) {
      alert('新密码长度至少6位')
      return
    }

    if (newPassword !== confirmPassword) {
      alert('两次输入的密码不一致')
      return
    }

    // 动态验证管理员密码
    try {
      const response = await fetch('/api/auth/admin/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ adminId: 'admin-1', password: adminPassword })
      })

      const data = await response.json()

      if (!data.success || !data.isValid) {
        alert('管理员密码错误')
        return
      }
    } catch (error) {
      console.error('验证管理员密码失败:', error)
      alert('验证管理员密码失败，请重试')
      return
    }

    try {
      setPasswordLoading(true)

      // 更新用户密码
      const response = await fetch('/api/employees', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedUser.id,
          password: newPassword,
          updatedAt: new Date()
        })
      })

      const data = await response.json()

      if (data.success) {
        // 更新本地用户列表
        setFactoryUsers(prev =>
          prev.map(user =>
            user.id === selectedUser.id
              ? { ...user, password: newPassword }: user
          )
        )

        alert(`用户 "${selectedUser.name}" 的密码已成功修改！\n\n新密码：${newPassword}\n\n请将新密码告知该用户。`)

        // 重置表单
        setSelectedUser(null)
        setShowPassword(false)
        setNewPassword("")
        setConfirmPassword("")
        setAdminPassword("")
      } else {
        alert(`密码修改失败：${data.error}`)
      }
    } catch (error) {
      console.error('修改密码失败:', error)
      alert('修改密码失败，请重试')
    } finally {
      setPasswordLoading(false)
    }
  }

  // 订阅管理
  const handleSubscriptionManagement = (factory: any) => {
    setSelectedFactory(factory)
    setSubscriptionType(factory.subscriptionType || 'monthly')
    setShowSubscriptionDialog(true)
  }

  // 更新订阅配置
  const handleUpdateSubscription = async () => {
    if (!selectedFactory) return

    try {
      setSubscriptionLoading(true)

      const subscriptionConfig = createSubscriptionConfig(subscriptionType)

      if (!accessToken) {
        console.error('❌ 没有访问令牌')
        return
      }

      const response = await fetch('/api/admin/factories/subscription', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factoryId: selectedFactory.id,
          ...subscriptionConfig
        })
      })

      const data = await response.json()

      if (data.success) {
        // 更新本地工厂列表
        setFactories(prev =>
          prev.map(factory =>
            factory.id === selectedFactory.id
              ? { ...factory, ...subscriptionConfig }
              : factory
          )
        )

        alert(`工厂 "${selectedFactory.name}" 的订阅配置已更新！`)
        setShowSubscriptionDialog(false)
        setSelectedFactory(null)
      } else {
        alert(`订阅配置更新失败：${data.error}`)
      }
    } catch (error) {
      console.error('更新订阅配置失败:', error)
      alert('更新订阅配置失败，请重试')
    } finally {
      setSubscriptionLoading(false)
    }
  }

  // 切换工厂状态（暂停/启动）
  const handleToggleFactoryStatus = async (factory: any) => {
    const newStatus = factory.status === 'suspended' ? 'active' : 'suspended'
    const action = newStatus === 'suspended' ? '暂停' : '启动'

    if (!confirm(`确定要${action}工厂 "${factory.name}" 吗？`)) {
      return
    }

    try {
      if (!accessToken) {
        console.error('❌ 没有访问令牌')
        return
      }

      const response = await fetch('/api/admin/factories/status', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factoryId: factory.id,
          status: newStatus,
          suspendedAt: newStatus === 'suspended' ? new Date() : null,
          suspendedReason: newStatus === 'suspended' ? '管理员手动暂停' : null
        })
      })

      const data = await response.json()

      if (data.success) {
        // 更新本地工厂列表
        setFactories(prev =>
          prev.map(f =>
            f.id === factory.id
              ? {
                  ...f,
                  status: newStatus,
                  suspendedAt: newStatus === 'suspended' ? new Date() : null,
                  suspendedReason: newStatus === 'suspended' ? '管理员手动暂停' : null
                }
              : f
          )
        )

        alert(`工厂 "${factory.name}" 已${action}！`)
      } else {
        alert(`${action}工厂失败：${data.error}`)
      }
    } catch (error) {
      console.error(`${action}工厂失败:`, error)
      alert(`${action}工厂失败，请重试`)
    }
  }

  return (
    <AdminRouteGuard>
      <DashboardLayout role="admin">
      <div className="p-8 bg-background min-h-full">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-foreground">工厂管理</h1>
            <p className="text-muted-foreground">管理平台上的所有加工厂</p>
            {/* Token 状态显示 */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-2 text-xs">
                <span className={`inline-block px-2 py-1 rounded text-white ${
                  tokenStatus.isExpired ? 'bg-red-500' :
                  tokenStatus.isExpiringSoon ? 'bg-yellow-500' : 'bg-green-500'
                }`}>
                  Token: {tokenStatus.isExpired ? '已过期' :
                          tokenStatus.isExpiringSoon ? '即将过期' : '正常'}
                  {tokenStatus.expirationTime && ` (到期: ${tokenStatus.expirationTime})`}
                </span>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <Button
              onClick={loadFactoriesWithStats}
              disabled={loading}
              variant="outline"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新数据
            </Button>
            <Button
              onClick={initTestData}
              disabled={loading}
              variant="outline"
              className="text-blue-600 hover:text-blue-700"
            >
              <Database className="h-4 w-4 mr-2" />
              初始化测试数据
            </Button>
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              添加工厂
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索工厂名称、编码或地址..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline">筛选</Button>
            </div>
          </CardContent>
        </Card>

        {/* Factories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredFactories.map((factory) => (
            <Card key={factory.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      <Factory className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{factory.name}</CardTitle>
                      <CardDescription>{factory.code}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(factory.status)}`}>
                      {getStatusText(factory.status)}
                    </span>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">地址</p>
                    <p className="text-sm font-medium">{factory.address || factory.location}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">管理员</p>
                    <p className="text-sm font-medium">
                      {factory.ownerName || '未设置'} - {factory.phone || factory.contactPhone}
                    </p>
                  </div>

                  {/* 订阅状态 */}
                  <div className="bg-gray-50 p-2 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600">订阅状态</span>
                      </div>
                      {factory.isPermanent ? (
                        <div className="flex items-center space-x-1">
                          <Crown className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm font-medium text-yellow-600">永久授权</span>
                        </div>
                      ) : (
                        <span className={`text-sm font-medium ${getSubscriptionStatusText(factory).color}`}>
                          {getSubscriptionStatusText(factory).status}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {getSubscriptionStatusText(factory).description}
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 pt-3 border-t">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Users className="h-4 w-4 text-blue-600" />
                      </div>
                      <p className="text-lg font-bold text-blue-600">{factory.totalClients}</p>
                      <p className="text-xs text-gray-600">客户</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <ShoppingCart className="h-4 w-4 text-green-600" />
                      </div>
                      <p className="text-lg font-bold text-green-600">{factory.totalOrders}</p>
                      <p className="text-xs text-gray-600">订单</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <span className="text-purple-600">¥</span>
                      </div>
                      <p className="text-lg font-bold text-purple-600">
                        {safeWanYuan(factory.monthlyRevenue, 1)}
                      </p>
                      <p className="text-xs text-gray-600">月收入</p>
                    </div>
                  </div>

                  <div className="space-y-2 pt-3">
                    {/* 第一行按钮 */}
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => handleViewFactory(factory)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        查看
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1 text-green-600 hover:text-green-700"
                        onClick={() => handleSubscriptionManagement(factory)}
                        title="订阅管理"
                      >
                        <Calendar className="h-4 w-4 mr-1" />
                        订阅
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-blue-600 hover:text-blue-700"
                        onClick={() => handlePasswordManagement(factory)}
                        title="密码管理"
                      >
                        <Key className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* 第二行按钮 */}
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className={`flex-1 ${
                          factory.status === 'suspended'
                            ? 'text-green-600 hover:text-green-700'
                            : 'text-orange-600 hover:text-orange-700'
                        }`}
                        onClick={() => handleToggleFactoryStatus(factory)}
                      >
                        {factory.status === 'suspended' ? (
                          <>
                            <Play className="h-4 w-4 mr-1" />
                            启动
                          </>
                        ) : (
                          <>
                            <Pause className="h-4 w-4 mr-1" />
                            暂停
                          </>
                        )}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => handleEditFactory(factory)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteFactory(factory)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredFactories.length === 0 && (
          <div className="text-center py-12">
            <Factory className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到工厂</h3>
            <p className="text-gray-600">尝试调整搜索条件或添加新的工厂</p>
          </div>
        )}

        {/* 添加工厂对话框 */}
        <AddFactoryDialog
          isOpen={showAddDialog}
          onClose={() => setShowAddDialog(false)}
          onSave={handleCreateFactory}
        />

        {/* 查看工厂详情对话框 */}
        <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Building2 className="h-5 w-5" />
                <span>工厂详情</span>
              </DialogTitle>
              <DialogDescription>
                查看工厂的详细信息和统计数据
              </DialogDescription>
            </DialogHeader>

            {selectedFactory && (
              <div className="space-y-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">工厂名称</Label>
                      <p className="text-lg font-semibold">{(selectedFactory as any)?.name}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700">工厂编码</Label>
                      <p className="text-sm text-gray-600">{(selectedFactory as any)?.code}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700 flex items-center space-x-1">
                        <MapPin className="h-4 w-4" />
                        <span>地址</span>
                      </Label>
                      <p className="text-sm text-gray-600">{(selectedFactory as any)?.location}</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">联系人</Label>
                      <p className="text-sm text-gray-600">{(selectedFactory as any)?.contactPerson}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700 flex items-center space-x-1">
                        <Phone className="h-4 w-4" />
                        <span>联系电话</span>
                      </Label>
                      <p className="text-sm text-gray-600">{(selectedFactory as any)?.contactPhone}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700 flex items-center space-x-1">
                        <Mail className="h-4 w-4" />
                        <span>邮箱</span>
                      </Label>
                      <p className="text-sm text-gray-600">{(selectedFactory as any)?.email}</p>
                    </div>
                  </div>
                </div>

                {/* 统计数据 */}
                <div className="border-t pt-4">
                  <Label className="text-sm font-medium text-gray-700 mb-3 block">业务统计</Label>
                  <div className="grid grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="flex items-center justify-center mb-2">
                          <Users className="h-5 w-5 text-blue-600" />
                        </div>
                        <p className="text-2xl font-bold text-blue-600">{(selectedFactory as any)?.totalClients || 0}</p>
                        <p className="text-sm text-gray-600">客户总数</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="flex items-center justify-center mb-2">
                          <ShoppingCart className="h-5 w-5 text-green-600" />
                        </div>
                        <p className="text-2xl font-bold text-green-600">{(selectedFactory as any)?.totalOrders || 0}</p>
                        <p className="text-sm text-gray-600">订单总数</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="flex items-center justify-center mb-2">
                          <span className="text-purple-600 text-lg">¥</span>
                        </div>
                        <p className="text-2xl font-bold text-purple-600">
                          {safeWanYuan((selectedFactory as any)?.monthlyRevenue || 0, 1)}
                        </p>
                        <p className="text-sm text-gray-600">月收入</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                {/* 状态信息 */}
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">工厂状态</Label>
                      <p className={`text-sm font-medium ${
                        (selectedFactory as any)?.status === 'active' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {(selectedFactory as any)?.status === 'active' ? '正常运营' : '暂停营业'}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700">创建时间</Label>
                      <p className="text-sm text-gray-600">{(selectedFactory as any)?.createdAt}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowViewDialog(false)}>
                关闭
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 删除工厂确认对话框 */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2 text-red-600">
                <AlertCircle className="h-5 w-5" />
                <span>删除工厂确认</span>
              </DialogTitle>
              <DialogDescription>
                此操作将永久删除工厂及其所有相关数据，包括客户、订单等信息。请谨慎操作！
              </DialogDescription>
            </DialogHeader>

            {selectedFactory && (
              <div className="space-y-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-sm text-red-800">
                    <strong>即将删除的工厂：</strong>{(selectedFactory as any)?.name} ({(selectedFactory as any)?.code})
                  </p>
                  <p className="text-sm text-red-600 mt-1">
                    包含 {(selectedFactory as any)?.totalClients || 0} 个客户和 {(selectedFactory as any)?.totalOrders || 0} 个订单
                  </p>
                </div>

                <div>
                  <Label htmlFor="delete-password" className="text-sm font-medium">
                    请输入管理员密码确认删除：
                  </Label>
                  <Input
                    id="delete-password"
                    type="password"
                    value={deletePassword}
                    onChange={(e) => setDeletePassword(e.target.value)}
                    placeholder="输入密码..."
                    className="mt-1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    请输入当前的管理员密码进行确认
                  </p>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteDialog(false)
                  setDeletePassword("")
                }}
                disabled={deleteLoading}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDeleteFactory}
                disabled={deleteLoading || !deletePassword}
              >
                {deleteLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    删除中...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    确认删除
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 密码管理对话框 */}
        <Dialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5 text-blue-600" />
                <span>密码管理</span>
              </DialogTitle>
              <DialogDescription>
                {selectedFactory && `管理 "${(selectedFactory as any)?.name}" 的用户密码`}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {passwordLoading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                  <span>加载用户信息中...</span>
                </div>
              ) : (
                <>
                  {factoryUsers.length === 0 ? (
                    <div className="text-center py-8">
                      <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">暂无用户</h3>
                      <p className="text-gray-600">该工厂还没有创建用户账号</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2 text-yellow-600">
                          <AlertCircle className="h-5 w-5" />
                          <span className="font-medium">安全提醒</span>
                        </div>
                        <div className="mt-2 text-sm text-yellow-600">
                          <p>• 查看和修改密码需要管理员权限验证</p>
                          <p>• 修改密码后请及时通知相关用户</p>
                          <p>• 建议定期更换密码以确保账号安全</p>
                        </div>
                      </div>

                      <div className="grid gap-4">
                        {factoryUsers.map((user) => (
                          <Card key={user.id} className="hover:shadow-md transition-shadow">
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                  <div className="bg-blue-100 p-2 rounded-lg">
                                    <Users className="h-5 w-5 text-blue-600" />
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-gray-900">{user.name}</h4>
                                    <p className="text-sm text-gray-600">用户名：{user.username}</p>
                                    <p className="text-sm text-gray-600">
                                      角色：{user.role === 'owner' ? '工厂老板' : '录单员'}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      状态：
                                      <span className={user.isActive ? 'text-green-600' : 'text-red-600'}>
                                        {user.isActive ? '已激活' : '已停用'}
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div className="flex space-x-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleViewPassword(user)}
                                  >
                                    <Eye className="h-4 w-4 mr-1" />
                                    查看密码
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowPasswordDialog(false)
                  setSelectedFactory(null)
                  setFactoryUsers([])
                  setSelectedUser(null)
                  setShowPassword(false)
                }}
              >
                关闭
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 查看/修改密码对话框 */}
        <Dialog open={showPassword} onOpenChange={setShowPassword}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5 text-blue-600" />
                <span>密码管理</span>
              </DialogTitle>
              <DialogDescription>
                {selectedUser && `管理用户 "${selectedUser.name}" 的密码`}
              </DialogDescription>
            </DialogHeader>

            {selectedUser && (
              <div className="space-y-4">
                {/* 当前密码显示 */}
                <div className="bg-gray-50 border rounded-lg p-4">
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">当前密码</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      type="text"
                      value={selectedUser.password}
                      readOnly
                      className="bg-white"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={async () => {
                        try {
                          await copyToClipboard(selectedUser.password, '密码已复制到剪贴板')
                        } catch (error) {
                          console.error('复制失败:', error)
                          alert(`复制失败，请手动复制密码：${selectedUser.password}`)
                        }
                      }}
                    >
                      复制
                    </Button>
                  </div>
                </div>

                {/* 修改密码表单 */}
                <div className="space-y-4 border-t pt-4">
                  <h4 className="font-medium text-gray-900">修改密码</h4>

                  <div>
                    <Label htmlFor="new-password" className="text-sm font-medium">
                      新密码
                    </Label>
                    <Input
                      id="new-password"
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      placeholder="请输入新密码（至少6位）"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="confirm-password" className="text-sm font-medium">
                      确认新密码
                    </Label>
                    <Input
                      id="confirm-password"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      placeholder="请再次输入新密码"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="admin-password" className="text-sm font-medium">
                      管理员密码确认
                    </Label>
                    <Input
                      id="admin-password"
                      type="password"
                      value={adminPassword}
                      onChange={(e) => setAdminPassword(e.target.value)}
                      placeholder="请输入管理员密码"
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      请输入当前的管理员密码进行确认
                    </p>
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowPassword(false)
                  setSelectedUser(null)
                  setNewPassword("")
                  setConfirmPassword("")
                  setAdminPassword("")
                }}
                disabled={passwordLoading}
              >
                取消
              </Button>
              <Button
                onClick={handleChangePassword}
                disabled={passwordLoading || !newPassword || !confirmPassword || !adminPassword}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {passwordLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    修改中...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    确认修改
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 订阅管理对话框 */}
        <Dialog open={showSubscriptionDialog} onOpenChange={setShowSubscriptionDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5 text-green-600" />
                <span>订阅管理</span>
              </DialogTitle>
              <DialogDescription>
                {selectedFactory && `管理工厂 "${selectedFactory.name}" 的订阅配置`}
              </DialogDescription>
            </DialogHeader>

            {selectedFactory && (
              <div className="space-y-4">
                {/* 当前订阅状态 */}
                <div className="bg-gray-50 border rounded-lg p-4">
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">当前状态</Label>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">订阅类型</span>
                      <span className="text-sm font-medium">
                        {formatSubscriptionType(selectedFactory.subscriptionType || 'trial')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">状态</span>
                      <span className={`text-sm font-medium ${getSubscriptionStatusText(selectedFactory).color}`}>
                        {getSubscriptionStatusText(selectedFactory).status}
                      </span>
                    </div>
                    {selectedFactory.subscriptionEnd && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">到期时间</span>
                        <span className="text-sm font-medium">
                          {new Date(selectedFactory.subscriptionEnd).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* 订阅类型选择 */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700">选择新的订阅类型</Label>
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(SUBSCRIPTION_CONFIGS).map(([type, config]) => (
                      <label
                        key={type}
                        className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                          subscriptionType === type
                            ? 'border-green-500 bg-green-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <input
                          type="radio"
                          name="subscriptionType"
                          value={type}
                          checked={subscriptionType === type}
                          onChange={(e) => setSubscriptionType(e.target.value as SubscriptionType)}
                          className="text-green-600"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-sm">{config.name}</div>
                          <div className="text-xs text-gray-500">
                            {type === 'permanent'
                              ? '永久使用权限'
                              : `${config.duration} ${config.unit === 'days' ? '天' : '个月'}`
                            }
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowSubscriptionDialog(false)}>
                取消
              </Button>
              <Button
                onClick={handleUpdateSubscription}
                disabled={subscriptionLoading}
                className="bg-green-600 hover:bg-green-700"
              >
                {subscriptionLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    更新中...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    确认更新
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 编辑工厂对话框 */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Edit className="h-5 w-5 text-blue-600" />
                <span>编辑工厂信息</span>
              </DialogTitle>
              <DialogDescription>
                {editingFactory && `编辑工厂 "${editingFactory.name}" 的基本信息和管理员信息`}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* 工厂基本信息 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">工厂基本信息</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-name">工厂名称</Label>
                    <Input
                      id="edit-name"
                      value={editFormData.name}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="请输入工厂名称"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-code">工厂编码</Label>
                    <Input
                      id="edit-code"
                      value={editFormData.code}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, code: e.target.value }))}
                      placeholder="请输入工厂编码"
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor="edit-address">工厂地址</Label>
                    <Input
                      id="edit-address"
                      value={editFormData.address}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, address: e.target.value }))}
                      placeholder="请输入工厂地址"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-phone">联系电话</Label>
                    <Input
                      id="edit-phone"
                      value={editFormData.phone}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="请输入联系电话"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-email">邮箱地址</Label>
                    <Input
                      id="edit-email"
                      type="email"
                      value={editFormData.email}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="请输入邮箱地址"
                    />
                  </div>
                </div>
              </div>

              {/* 管理员信息 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">管理员信息</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-admin-name">管理员姓名</Label>
                    <Input
                      id="edit-admin-name"
                      value={editFormData.adminName}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, adminName: e.target.value }))}
                      placeholder="请输入管理员姓名"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-admin-phone">管理员电话</Label>
                    <Input
                      id="edit-admin-phone"
                      value={editFormData.adminPhone}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, adminPhone: e.target.value }))}
                      placeholder="请输入管理员电话"
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor="edit-admin-email">管理员邮箱</Label>
                    <Input
                      id="edit-admin-email"
                      type="email"
                      value={editFormData.adminEmail}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, adminEmail: e.target.value }))}
                      placeholder="请输入管理员邮箱"
                    />
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={handleCancelEdit}
                disabled={editLoading}
              >
                取消
              </Button>
              <Button
                onClick={handleSaveEdit}
                disabled={editLoading || !editFormData.name || !editFormData.code}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {editLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    保存修改
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
    </AdminRouteGuard>
  )
}
