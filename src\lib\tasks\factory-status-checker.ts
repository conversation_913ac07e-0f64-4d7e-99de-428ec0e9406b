/**
 * 🇨🇳 风口云平台 - 工厂状态自动检查任务
 * 
 * 定期检查所有工厂的订阅状态，自动处理过期工厂
 */

import { db, prisma } from '@/lib/database'

interface FactoryStatusReport {
  totalFactories: number
  activeFactories: number
  expiredFactories: number
  suspendedFactories: number
  warningFactories: number
  processedFactories: number
  errors: string[]
}

/**
 * 工厂状态检查器类
 */
export class FactoryStatusChecker {
  private isRunning = false
  private lastRunTime: Date | null = null

  /**
   * 执行工厂状态检查任务
   */
  async runStatusCheck(): Promise<FactoryStatusReport> {
    if (this.isRunning) {
      throw new Error('状态检查任务正在运行中')
    }

    this.isRunning = true
    this.lastRunTime = new Date()

    console.log('🔍 开始工厂状态检查任务...')

    const report: FactoryStatusReport = {
      totalFactories: 0,
      activeFactories: 0,
      expiredFactories: 0,
      suspendedFactories: 0,
      warningFactories: 0,
      processedFactories: 0,
      errors: []
    }

    try {
      // 获取所有工厂
      const factories = await prisma.factory.findMany({
        include: {
          users: {
            where: { isActive: true },
            select: { id: true, username: true, name: true }
          }
        }
      })

      report.totalFactories = factories.length
      console.log(`📊 找到 ${factories.length} 个工厂需要检查`)

      for (const factory of factories) {
        try {
          await this.checkSingleFactory(factory, report)
          report.processedFactories++
        } catch (error) {
          const errorMsg = `工厂 ${factory.name}(${factory.id}) 检查失败: ${error instanceof Error ? error.message : '未知错误'}`
          console.error('❌', errorMsg)
          report.errors.push(errorMsg)
        }
      }

      console.log('✅ 工厂状态检查任务完成')
      console.log('📊 检查报告:', {
        总工厂数: report.totalFactories,
        正常运行: report.activeFactories,
        已过期: report.expiredFactories,
        已暂停: report.suspendedFactories,
        即将过期: report.warningFactories,
        处理成功: report.processedFactories,
        错误数量: report.errors.length
      })

      return report

    } catch (error) {
      console.error('❌ 工厂状态检查任务失败:', error)
      report.errors.push(`任务执行失败: ${error instanceof Error ? error.message : '未知错误'}`)
      return report
    } finally {
      this.isRunning = false
    }
  }

  /**
   * 检查单个工厂状态
   */
  private async checkSingleFactory(factory: any, report: FactoryStatusReport): Promise<void> {
    console.log(`🔍 检查工厂: ${factory.name}(${factory.code})`)

    // 使用数据库服务的状态检查方法
    const statusCheck = await db.checkFactoryStatus(factory)

    // 统计不同状态的工厂
    switch (statusCheck.status) {
      case 'active':
        if (statusCheck.warningLevel === 'warning' || statusCheck.warningLevel === 'critical') {
          report.warningFactories++
          console.log(`⚠️ 工厂 ${factory.name} 即将过期: ${statusCheck.remainingDays}天`)
        } else {
          report.activeFactories++
        }
        break
      case 'expired':
        report.expiredFactories++
        console.log(`❌ 工厂 ${factory.name} 已过期`)
        break
      case 'suspended':
        report.suspendedFactories++
        console.log(`⏸️ 工厂 ${factory.name} 已暂停`)
        break
      default:
        console.log(`ℹ️ 工厂 ${factory.name} 状态: ${statusCheck.status}`)
    }

    // 如果工厂自动暂停，发送通知（这里可以集成邮件或短信通知）
    if (statusCheck.autoSuspended) {
      await this.notifyFactoryAutoSuspended(factory, statusCheck)
    }

    // 如果工厂即将过期，发送警告通知
    if (statusCheck.warningLevel === 'critical' || statusCheck.warningLevel === 'warning') {
      await this.notifyFactoryExpiring(factory, statusCheck)
    }
  }

  /**
   * 通知工厂自动暂停
   */
  private async notifyFactoryAutoSuspended(factory: any, statusCheck: any): Promise<void> {
    console.log(`📧 发送工厂自动暂停通知: ${factory.name}`)
    
    // 这里可以实现邮件、短信或系统通知
    // 暂时只记录日志
    console.log(`🔔 工厂 ${factory.name} 因订阅到期已自动暂停，请及时续费`)
  }

  /**
   * 通知工厂即将过期
   */
  private async notifyFactoryExpiring(factory: any, statusCheck: any): Promise<void> {
    const daysLeft = statusCheck.remainingDays || 0
    console.log(`📧 发送工厂到期警告: ${factory.name} (剩余${daysLeft}天)`)
    
    // 这里可以实现邮件、短信或系统通知
    // 暂时只记录日志
    console.log(`🔔 工厂 ${factory.name} 将在 ${daysLeft} 天后到期，请及时续费`)
  }

  /**
   * 获取任务状态
   */
  getTaskStatus() {
    return {
      isRunning: this.isRunning,
      lastRunTime: this.lastRunTime
    }
  }

  /**
   * 检查所有即将过期的工厂（7天内）
   */
  async getExpiringFactories(days: number = 7): Promise<any[]> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() + days)

    const factories = await prisma.factory.findMany({
      where: {
        AND: [
          { status: 'active' },
          { isPermanent: false },
          { subscriptionEnd: { not: null } },
          { subscriptionEnd: { lte: cutoffDate } }
        ]
      },
      include: {
        users: {
          where: { role: 'admin' },
          select: { name: true, username: true }
        }
      }
    })

    return factories
  }

  /**
   * 获取所有过期但未暂停的工厂
   */
  async getExpiredActiveFactories(): Promise<any[]> {
    const now = new Date()

    const factories = await prisma.factory.findMany({
      where: {
        AND: [
          { status: 'active' },
          { isPermanent: false },
          { subscriptionEnd: { not: null } },
          { subscriptionEnd: { lt: now } }
        ]
      }
    })

    return factories
  }
}

// 创建全局实例
export const factoryStatusChecker = new FactoryStatusChecker()

/**
 * 启动定时任务（每小时检查一次）
 */
export function startFactoryStatusScheduler() {
  console.log('🚀 启动工厂状态定时检查器...')
  
  // 立即执行一次
  factoryStatusChecker.runStatusCheck().catch(error => {
    console.error('❌ 初始工厂状态检查失败:', error)
  })

  // 每小时执行一次
  setInterval(async () => {
    try {
      await factoryStatusChecker.runStatusCheck()
    } catch (error) {
      console.error('❌ 定时工厂状态检查失败:', error)
    }
  }, 60 * 60 * 1000) // 1小时

  console.log('✅ 工厂状态定时检查器已启动')
}

/**
 * 手动触发状态检查的API辅助函数
 */
export async function triggerManualStatusCheck(): Promise<FactoryStatusReport> {
  console.log('🔧 手动触发工厂状态检查...')
  return await factoryStatusChecker.runStatusCheck()
}
