/**
 * 表格数量验证工具
 * 专门用于确保表格识别中数量字段的准确性
 */

export interface TableQuantityValidationResult {
  isValid: boolean
  extractedQuantity: number
  originalValue: string
  warnings: string[]
  columnIndex: number
}

/**
 * 验证并提取表格中的数量信息
 * @param tableData 表格数据
 * @param rowIndex 行索引
 * @returns 验证结果
 */
export function validateTableQuantity(
  tableData: { headers: string[], rows: string[][] },
  rowIndex: number
): TableQuantityValidationResult {
  const result: TableQuantityValidationResult = {
    isValid: false,
    extractedQuantity: 1,
    originalValue: '',
    warnings: [],
    columnIndex: -1
  }

  // 1. 找到数量列的位置
  const quantityColumnIndex = findQuantityColumn(tableData.headers)
  
  if (quantityColumnIndex === -1) {
    result.warnings.push('未找到数量列，使用默认值1')
    return result
  }

  result.columnIndex = quantityColumnIndex

  // 2. 获取指定行的数量值
  const row = tableData.rows[rowIndex]
  if (!row || quantityColumnIndex >= row.length) {
    result.warnings.push(`行${rowIndex + 1}数据不完整，使用默认值1`)
    return result
  }

  const quantityCell = row[quantityColumnIndex] || ''
  result.originalValue = quantityCell

  // 3. 解析数量值
  const parsedQuantity = parseQuantityFromCell(quantityCell)
  
  // 4. 验证数量合理性
  const validation = validateQuantityValue(parsedQuantity, quantityCell, row)
  
  result.extractedQuantity = validation.finalQuantity
  result.isValid = validation.isValid
  result.warnings.push(...validation.warnings)

  return result
}

/**
 * 找到数量列的位置
 */
function findQuantityColumn(headers: string[]): number {
  const quantityKeywords = ['数量', 'quantity', 'qty', '个数', '件数']
  
  for (let i = 0; i < headers.length; i++) {
    const header = (headers[i] || '').toLowerCase().trim()
    
    for (const keyword of quantityKeywords) {
      if (header.includes(keyword)) {
        console.log(`🔢 找到数量列: 第${i + 1}列 "${headers[i]}"`)
        return i
      }
    }
  }
  
  console.log('⚠️ 未找到明确的数量列')
  return -1
}

/**
 * 从单元格中解析数量
 */
function parseQuantityFromCell(cellValue: string): number {
  if (!cellValue) return 1
  
  const cleanValue = cellValue.trim()
  if (!cleanValue) return 1
  
  console.log(`    🔍 解析数量单元格: "${cleanValue}"`)
  
  // 1. 尝试直接解析为整数
  const directNumber = parseInt(cleanValue)
  if (!isNaN(directNumber) && directNumber > 0) {
    console.log(`    ✅ 直接数字解析: ${directNumber}`)
    return directNumber
  }
  
  // 2. 提取第一个数字
  const numberMatch = cleanValue.match(/\d+/)
  if (numberMatch) {
    const extractedNumber = parseInt(numberMatch[0])
    console.log(`    ✅ 提取数字: ${extractedNumber}`)
    return extractedNumber
  }
  
  // 3. 中文数字转换
  const chineseNumber = convertChineseNumber(cleanValue)
  if (chineseNumber > 0) {
    console.log(`    ✅ 中文数字转换: ${chineseNumber}`)
    return chineseNumber
  }
  
  console.log(`    ❌ 无法解析数量，使用默认值1`)
  return 1
}

/**
 * 验证数量值的合理性
 */
function validateQuantityValue(
  quantity: number, 
  originalValue: string, 
  rowData: string[]
): { isValid: boolean, finalQuantity: number, warnings: string[] } {
  const warnings: string[] = []
  let finalQuantity = quantity
  let isValid = true
  
  // 1. 基本范围检查
  if (quantity <= 0) {
    warnings.push(`数量${quantity}无效，使用默认值1`)
    finalQuantity = 1
    isValid = false
  }
  
  // 2. 异常大数量检查
  if (quantity > 100) {
    warnings.push(`数量${quantity}异常大，可能是OCR错误`)
    
    // 检查是否可能是尺寸数据
    const isDimensionLike = checkIfDimensionLike(quantity, rowData)
    if (isDimensionLike) {
      warnings.push(`数量${quantity}疑似尺寸数据，重置为1`)
      finalQuantity = 1
      isValid = false
    }
  }
  
  // 3. 常见OCR错误模式检查
  const ocrErrorCheck = checkOCRErrorPatterns(quantity, originalValue)
  if (ocrErrorCheck.isError) {
    warnings.push(ocrErrorCheck.message)
    finalQuantity = ocrErrorCheck.correctedValue
    isValid = false
  }
  
  return { isValid, finalQuantity, warnings }
}

/**
 * 检查数量是否可能是尺寸数据
 */
function checkIfDimensionLike(quantity: number, rowData: string[]): boolean {
  // 常见的风口尺寸数字
  const commonDimensions = [100, 125, 140, 150, 160, 200, 250, 295, 300, 400, 500, 600, 800, 1000, 1100, 1200, 1500, 2000, 2500, 3000, 3500, 4000]
  
  if (commonDimensions.includes(quantity)) {
    console.log(`    ⚠️ 数量${quantity}是常见风口尺寸，可能是误识别`)
    return true
  }
  
  // 检查行数据中是否包含相同的数字（可能是尺寸）
  for (const cell of rowData) {
    if (cell && cell.includes(quantity.toString())) {
      console.log(`    ⚠️ 数量${quantity}在行数据中重复出现，可能是误识别`)
      return true
    }
  }
  
  return false
}

/**
 * 检查常见的OCR错误模式
 */
function checkOCRErrorPatterns(
  quantity: number, 
  originalValue: string
): { isError: boolean, message: string, correctedValue: number } {
  // 常见的OCR错误模式
  const errorPatterns = [
    { pattern: /^295$/, corrected: 1, reason: '295可能是宽度尺寸' },
    { pattern: /^125$/, corrected: 1, reason: '125可能是宽度尺寸' },
    { pattern: /^150$/, corrected: 1, reason: '150可能是价格或尺寸' },
    { pattern: /^1100$/, corrected: 1, reason: '1100可能是长度尺寸' },
    { pattern: /^1085$/, corrected: 1, reason: '1085可能是长度尺寸' },
    { pattern: /^3360$/, corrected: 1, reason: '3360可能是长度尺寸' },
    { pattern: /^2235$/, corrected: 1, reason: '2235可能是长度尺寸' }
  ]
  
  for (const error of errorPatterns) {
    if (error.pattern.test(originalValue)) {
      return {
        isError: true,
        message: `${error.reason}，修正为${error.corrected}`,
        correctedValue: error.corrected
      }
    }
  }
  
  return { isError: false, message: '', correctedValue: quantity }
}

/**
 * 中文数字转换
 */
function convertChineseNumber(text: string): number {
  const chineseNumbers: { [key: string]: number } = {
    '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
    '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
    '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5,
    '陆': 6, '柒': 7, '捌': 8, '玖': 9, '拾': 10,
    '两': 2, '俩': 2
  }
  
  for (const [chinese, number] of Object.entries(chineseNumbers)) {
    if (text.includes(chinese)) {
      return number
    }
  }
  
  return 0
}

/**
 * 批量验证表格中所有行的数量
 */
export function validateAllTableQuantities(
  tableData: { headers: string[], rows: string[][] }
): TableQuantityValidationResult[] {
  console.log('🔢 开始批量验证表格数量...')
  
  const results: TableQuantityValidationResult[] = []
  
  for (let i = 0; i < tableData.rows.length; i++) {
    console.log(`\n验证第${i + 1}行数量:`)
    const result = validateTableQuantity(tableData, i)
    results.push(result)
    
    console.log(`  原始值: "${result.originalValue}"`)
    console.log(`  提取数量: ${result.extractedQuantity}`)
    console.log(`  是否有效: ${result.isValid ? '✅' : '❌'}`)
    
    if (result.warnings.length > 0) {
      console.log(`  警告: ${result.warnings.join(', ')}`)
    }
  }
  
  const validCount = results.filter(r => r.isValid).length
  const totalCount = results.length
  
  console.log(`\n📊 数量验证总结: ${validCount}/${totalCount} 有效`)
  
  return results
}
