 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风1140✖️230普通下出"
 🔄 [通义千问] 识别风口: "出风1140✖️230普通下出" (楼层: 1, 房间: 茶室)
 🔄 [通义千问] 解析风口行: "出风1140✖️230普通下出"
 🔄 [通义千问] 标准化后: "出风1140×230普通下出"
 🔄 [通义千问] 原始尺寸: 1140×230
 🔄 [通义千问] 转换后尺寸: 1140×230
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=230, 类型=double_white_outlet
 🔄 [通义千问] 备注: "普通下出"
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "棋牌室"
 🔄 [通义千问] 识别房间: "棋牌室"
 🔄 [通义千问] 处理行: "回风1280✖️300"
 🔄 [通义千问] 识别风口: "回风1280✖️300" (楼层: 1, 房间: 棋牌室)
 🔄 [通义千问] 解析风口行: "回风1280✖️300"
 🔄 [通义千问] 标准化后: "回风1280×300"
 🔄 [通义千问] 原始尺寸: 1280×300
 🔄 [通义千问] 转换后尺寸: 1280×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风3000✖️140"
 🔄 [通义千问] 识别风口: "出风3000✖️140" (楼层: 1, 房间: 棋牌室)
 🔄 [通义千问] 解析风口行: "出风3000✖️140"
 🔄 [通义千问] 标准化后: "出风3000×140"
 🔄 [通义千问] 原始尺寸: 3000×140
 🔄 [通义千问] 转换后尺寸: 3000×140
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=140, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "餐厅"
 🔄 [通义千问] 识别房间: "餐厅"
 🔄 [通义千问] 处理行: "回风1700✖️290"
 🔄 [通义千问] 识别风口: "回风1700✖️290" (楼层: 1, 房间: 餐厅)
 🔄 [通义千问] 解析风口行: "回风1700✖️290"
 🔄 [通义千问] 标准化后: "回风1700×290"
 🔄 [通义千问] 原始尺寸: 1700×290
 🔄 [通义千问] 转换后尺寸: 1700×290
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=290, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风4630✖️150"
 🔄 [通义千问] 识别风口: "出风4630✖️150" (楼层: 1, 房间: 餐厅)
 🔄 [通义千问] 解析风口行: "出风4630✖️150"
 🔄 [通义千问] 标准化后: "出风4630×150"
 🔄 [通义千问] 原始尺寸: 4630×150
 🔄 [通义千问] 转换后尺寸: 4630×150
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=150, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "回风1700✖️300"
 🔄 [通义千问] 识别风口: "回风1700✖️300" (楼层: 1, 房间: 餐厅)
 🔄 [通义千问] 解析风口行: "回风1700✖️300"
 🔄 [通义千问] 标准化后: "回风1700×300"
 🔄 [通义千问] 原始尺寸: 1700×300
 🔄 [通义千问] 转换后尺寸: 1700×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "厨房"
 🔄 [通义千问] 识别房间: "厨房"
 🔄 [通义千问] 处理行: "回风1700✖️290"
 🔄 [通义千问] 识别风口: "回风1700✖️290" (楼层: 1, 房间: 厨房)
 🔄 [通义千问] 解析风口行: "回风1700✖️290"
 🔄 [通义千问] 标准化后: "回风1700×290"
 🔄 [通义千问] 原始尺寸: 1700×290
 🔄 [通义千问] 转换后尺寸: 1700×290
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=290, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风400✖️400  3个，留圆接200软管"
 🔄 [通义千问] 识别风口: "出风400✖️400  3个，留圆接200软管" (楼层: 1, 房间: 厨房)
 🔄 [通义千问] 解析风口行: "出风400✖️400  3个，留圆接200软管"
 🔄 [通义千问] 标准化后: "出风400×400  3个，留圆接200软管"
 🔄 [通义千问] 原始尺寸: 400×400
 🔄 [通义千问] 转换后尺寸: 400×400
 🔄 [通义千问] 数量: 3
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=400, 类型=white_return
 🔄 [通义千问] 备注: "留圆接，软管"
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "KTV"
 🔄 [通义千问] 识别房间: "KTV"
 🔄 [通义千问] 处理行: "回风1470✖️300"
 🔄 [通义千问] 识别风口: "回风1470✖️300" (楼层: 1, 房间: KTV)
 🔄 [通义千问] 解析风口行: "回风1470✖️300"
 🔄 [通义千问] 标准化后: "回风1470×300"
 🔄 [通义千问] 原始尺寸: 1470×300
 🔄 [通义千问] 转换后尺寸: 1470×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "回风1570✖️300"
 🔄 [通义千问] 识别风口: "回风1570✖️300" (楼层: 1, 房间: KTV)
 🔄 [通义千问] 解析风口行: "回风1570✖️300"
 🔄 [通义千问] 标准化后: "回风1570×300"
 🔄 [通义千问] 原始尺寸: 1570×300
 🔄 [通义千问] 转换后尺寸: 1570×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风3890✖️190"
 🔄 [通义千问] 识别风口: "出风3890✖️190" (楼层: 1, 房间: KTV)
 🔄 [通义千问] 解析风口行: "出风3890✖️190"
 🔄 [通义千问] 标准化后: "出风3890×190"
 🔄 [通义千问] 原始尺寸: 3890×190
 🔄 [通义千问] 转换后尺寸: 3890×190
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=190, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风3870✖️190"
 🔄 [通义千问] 识别风口: "出风3870✖️190" (楼层: 1, 房间: KTV)
 🔄 [通义千问] 解析风口行: "出风3870✖️190"
 🔄 [通义千问] 标准化后: "出风3870×190"
 🔄 [通义千问] 原始尺寸: 3870×190
 🔄 [通义千问] 转换后尺寸: 3870×190
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=190, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "二楼"
 🔄 [通义千问] 识别楼层: "二楼" → "2"
 🔄 [通义千问] 处理行: "A201"
 🔄 [通义千问] 识别房间: "A201"
 🔄 [通义千问] 处理行: "回风3040✖️300"
 🔄 [通义千问] 识别风口: "回风3040✖️300" (楼层: 2, 房间: A201)
 🔄 [通义千问] 解析风口行: "回风3040✖️300"
 🔄 [通义千问] 标准化后: "回风3040×300"
 🔄 [通义千问] 原始尺寸: 3040×300
 🔄 [通义千问] 转换后尺寸: 3040×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风400✖️400，4个普通下出"
 🔄 [通义千问] 识别风口: "出风400✖️400，4个普通下出" (楼层: 2, 房间: A201)
 🔄 [通义千问] 解析风口行: "出风400✖️400，4个普通下出"
 🔄 [通义千问] 标准化后: "出风400×400，4个普通下出"
 🔄 [通义千问] 原始尺寸: 400×400
 🔄 [通义千问] 转换后尺寸: 400×400
 🔄 [通义千问] 数量: 4
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=400, 类型=white_return
 🔄 [通义千问] 备注: "普通下出"
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "A202"
 🔄 [通义千问] 识别房间: "A202"
 🔄 [通义千问] 处理行: "回风1380✖️300"
 🔄 [通义千问] 识别风口: "回风1380✖️300" (楼层: 2, 房间: A202)
 🔄 [通义千问] 解析风口行: "回风1380✖️300"
 🔄 [通义千问] 标准化后: "回风1380×300"
 🔄 [通义千问] 原始尺寸: 1380×300
 🔄 [通义千问] 转换后尺寸: 1380×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风3720✖️260"
 🔄 [通义千问] 识别风口: "出风3720✖️260" (楼层: 2, 房间: A202)
 🔄 [通义千问] 解析风口行: "出风3720✖️260"
 🔄 [通义千问] 标准化后: "出风3720×260"
 🔄 [通义千问] 原始尺寸: 3720×260
 🔄 [通义千问] 转换后尺寸: 3720×260
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=260, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "A203"
 🔄 [通义千问] 识别房间: "A203"
 🔄 [通义千问] 处理行: "回风1260✖️300"
 🔄 [通义千问] 识别风口: "回风1260✖️300" (楼层: 2, 房间: A203)
 🔄 [通义千问] 解析风口行: "回风1260✖️300"
 🔄 [通义千问] 标准化后: "回风1260×300"
 🔄 [通义千问] 原始尺寸: 1260×300
 🔄 [通义千问] 转换后尺寸: 1260×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风3730✖️230"
 🔄 [通义千问] 识别风口: "出风3730✖️230" (楼层: 2, 房间: A203)
 🔄 [通义千问] 解析风口行: "出风3730✖️230"
 🔄 [通义千问] 标准化后: "出风3730×230"
 🔄 [通义千问] 原始尺寸: 3730×230
 🔄 [通义千问] 转换后尺寸: 3730×230
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=230, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "B201"
 🔄 [通义千问] 识别房间: "B201"
 🔄 [通义千问] 处理行: "回风3010✖️300"
 🔄 [通义千问] 识别风口: "回风3010✖️300" (楼层: 2, 房间: B201)
 🔄 [通义千问] 解析风口行: "回风3010✖️300"
 🔄 [通义千问] 标准化后: "回风3010×300"
 🔄 [通义千问] 原始尺寸: 3010×300
 🔄 [通义千问] 转换后尺寸: 3010×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风400✖️400，4个普通下出"
 🔄 [通义千问] 识别风口: "出风400✖️400，4个普通下出" (楼层: 2, 房间: B201)
 🔄 [通义千问] 解析风口行: "出风400✖️400，4个普通下出"
 🔄 [通义千问] 标准化后: "出风400×400，4个普通下出"
 🔄 [通义千问] 原始尺寸: 400×400
 🔄 [通义千问] 转换后尺寸: 400×400
 🔄 [通义千问] 数量: 4
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=400, 类型=white_return
 🔄 [通义千问] 备注: "普通下出"
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "B202"
 🔄 [通义千问] 识别房间: "B202"
 🔄 [通义千问] 处理行: "回风1410✖️300"
 🔄 [通义千问] 识别风口: "回风1410✖️300" (楼层: 2, 房间: B202)
 🔄 [通义千问] 解析风口行: "回风1410✖️300"
 🔄 [通义千问] 标准化后: "回风1410×300"
 🔄 [通义千问] 原始尺寸: 1410×300
 🔄 [通义千问] 转换后尺寸: 1410×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风3720✖️280"
 🔄 [通义千问] 识别风口: "出风3720✖️280" (楼层: 2, 房间: B202)
 🔄 [通义千问] 解析风口行: "出风3720✖️280"
 🔄 [通义千问] 标准化后: "出风3720×280"
 🔄 [通义千问] 原始尺寸: 3720×280
 🔄 [通义千问] 转换后尺寸: 3720×280
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=280, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "B203"
 🔄 [通义千问] 识别房间: "B203"
 🔄 [通义千问] 处理行: "回风1350✖️300"
 🔄 [通义千问] 识别风口: "回风1350✖️300" (楼层: 2, 房间: B203)
 🔄 [通义千问] 解析风口行: "回风1350✖️300"
 🔄 [通义千问] 标准化后: "回风1350×300"
 🔄 [通义千问] 原始尺寸: 1350×300
 🔄 [通义千问] 转换后尺寸: 1350×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风饿3730✖️200"
 🔄 [通义千问] 识别风口: "出风饿3730✖️200" (楼层: 2, 房间: B203)
 🔄 [通义千问] 解析风口行: "出风饿3730✖️200"
 🔄 [通义千问] 标准化后: "出风饿3730×200"
 🔄 [通义千问] 原始尺寸: 3730×200
 🔄 [通义千问] 转换后尺寸: 3730×200
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=200, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "3楼"
 🔄 [通义千问] 识别楼层: "3楼" → "3"
 🔄 [通义千问] 处理行: "A301"
 🔄 [通义千问] 识别房间: "A301"
 🔄 [通义千问] 处理行: "回风3020✖️300"
 🔄 [通义千问] 识别风口: "回风3020✖️300" (楼层: 3, 房间: A301)
 🔄 [通义千问] 解析风口行: "回风3020✖️300"
 🔄 [通义千问] 标准化后: "回风3020×300"
 🔄 [通义千问] 原始尺寸: 3020×300
 🔄 [通义千问] 转换后尺寸: 3020×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风5950✖️200"
 🔄 [通义千问] 识别风口: "出风5950✖️200" (楼层: 3, 房间: A301)
 🔄 [通义千问] 解析风口行: "出风5950✖️200"
 🔄 [通义千问] 标准化后: "出风5950×200"
 🔄 [通义千问] 原始尺寸: 5950×200
 🔄 [通义千问] 转换后尺寸: 5950×200
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=200, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "A302"
 🔄 [通义千问] 识别房间: "A302"
 🔄 [通义千问] 处理行: "回风1520✖️300"
 🔄 [通义千问] 识别风口: "回风1520✖️300" (楼层: 3, 房间: A302)
 🔄 [通义千问] 解析风口行: "回风1520✖️300"
 🔄 [通义千问] 标准化后: "回风1520×300"
 🔄 [通义千问] 原始尺寸: 1520×300
 🔄 [通义千问] 转换后尺寸: 1520×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风3680✖️210"
 🔄 [通义千问] 识别风口: "出风3680✖️210" (楼层: 3, 房间: A302)
 🔄 [通义千问] 解析风口行: "出风3680✖️210"
 🔄 [通义千问] 标准化后: "出风3680×210"
 🔄 [通义千问] 原始尺寸: 3680×210
 🔄 [通义千问] 转换后尺寸: 3680×210
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=210, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "B301"
 🔄 [通义千问] 识别房间: "B301"
 🔄 [通义千问] 处理行: "回风2960✖️300"
 🔄 [通义千问] 识别风口: "回风2960✖️300" (楼层: 3, 房间: B301)
 🔄 [通义千问] 解析风口行: "回风2960✖️300"
 🔄 [通义千问] 标准化后: "回风2960×300"
 🔄 [通义千问] 原始尺寸: 2960×300
 🔄 [通义千问] 转换后尺寸: 2960×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风5930✖️200"
 🔄 [通义千问] 识别风口: "出风5930✖️200" (楼层: 3, 房间: B301)
 🔄 [通义千问] 解析风口行: "出风5930✖️200"
 🔄 [通义千问] 标准化后: "出风5930×200"
 🔄 [通义千问] 原始尺寸: 5930×200
 🔄 [通义千问] 转换后尺寸: 5930×200
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=200, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "B302"
 🔄 [通义千问] 识别房间: "B302"
 🔄 [通义千问] 处理行: "回风1450✖️300"
 🔄 [通义千问] 识别风口: "回风1450✖️300" (楼层: 3, 房间: B302)
 🔄 [通义千问] 解析风口行: "回风1450✖️300"
 🔄 [通义千问] 标准化后: "回风1450×300"
 🔄 [通义千问] 原始尺寸: 1450×300
 🔄 [通义千问] 转换后尺寸: 1450×300
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=true, 宽度=300, 类型=white_return
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 处理行: "出风3660✖️220"
 🔄 [通义千问] 识别风口: "出风3660✖️220" (楼层: 3, 房间: B302)
 🔄 [通义千问] 解析风口行: "出风3660✖️220"
 🔄 [通义千问] 标准化后: "出风3660×220"
 🔄 [通义千问] 原始尺寸: 3660×220
 🔄 [通义千问] 转换后尺寸: 3660×220
 🔄 [通义千问] 数量: 1
 🔄 [通义千问] 类型判断: 关键词=false, 宽度=220, 类型=double_white_outlet
 🔄 [通义千问] 备注: ""
 🔄 [通义千问] 风口解析结果: Object
 🔄 [通义千问] 备用解析结果: {
  "projects": [
    {
      "projectName": "金桂苑",
      "clientInfo": "金桂苑",
      "floors": [
        {
          "floorName": "1",
          "rooms": [
            {
              "roomName": "活动室",
              "vents": [
                {
                  "systemType": "white_return",
                  "originalType": "回风3390✖️300",
                  "dimensions": {
                    "length": 3390,
                    "width": 300,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                },
                {
                  "systemType": "double_white_outlet",
                  "originalType": "出风4640✖️160",
                  "dimensions": {
                    "length": 4640,
                    "width": 160,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                }
              ]
            },
            {
              "roomName": "客厅",
              "vents": [
                {
                  "systemType": "white_return",
                  "originalType": "回风1570✖️290",
                  "dimensions": {
                    "length": 1570,
                    "width": 290,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                },
                {
                  "systemType": "white_return",
                  "originalType": "回风1700✖️300",
                  "dimensions": {
                    "length": 1700,
                    "width": 300,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                },
                {
                  "systemType": "white_return",
                  "originalType": "检修口400✖️400",
                  "dimensions": {
                    "length": 400,
                    "width": 400,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": "检修"
                },
                {
                  "systemType": "double_white_outlet",
                  "originalType": "出风4430✖️130",
                  "dimensions": {
                    "length": 4430,
                    "width": 130,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                },
                {
                  "systemType": "double_white_outlet",
                  "originalType": "出风4450✖️135",
                  "dimensions": {
                    "length": 4450,
                    "width": 135,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                }
              ]
            },
            {
              "roomName": "茶室",
              "vents": [
                {
                  "systemType": "white_return",
                  "originalType": "回风1390✖️300",
                  "dimensions": {
                    "length": 1390,
                    "width": 300,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                },
                {
                  "systemType": "double_white_outlet",
                  "originalType": "出风1140✖️230普通下出",
                  "dimensions": {
                    "length": 1140,
                    "width": 230,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": "普通下出"
                }
              ]
            },
            {
              "roomName": "棋牌室",
              "vents": [
                {
                  "systemType": "white_return",
                  "originalType": "回风1280✖️300",
                  "dimensions": {
                    "length": 1280,
                    "width": 300,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                },
                {
                  "systemType": "double_white_outlet",
                  "originalType": "出风3000✖️140",
                  "dimensions": {
                    "length": 3000,
                    "width": 140,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                }
              ]
            },
            {
              "roomName": "餐厅",
              "vents": [
                {
                  "systemType": "white_return",
                  "originalType": "回风1700✖️290",
                  "dimensions": {
                    "length": 1700,
                    "width": 290,
                    "unit": "mm"
                  },
                  "quantity": 1,
                  "notes": ""
                },
                {
                  "systemType": "double_white
 🔄 [通义千问] 解析到的楼层数: 3
 🔄 [通义千问] 楼层1: 1, 房间数: 7
 🔄 [通义千问] 房间1: 活动室, 风口数: 2
 🔄 [通义千问] 房间2: 客厅, 风口数: 5
 🔄 [通义千问] 房间3: 茶室, 风口数: 2
 🔄 [通义千问] 房间4: 棋牌室, 风口数: 2
 🔄 [通义千问] 房间5: 餐厅, 风口数: 3
 🔄 [通义千问] 房间6: 厨房, 风口数: 2
 🔄 [通义千问] 房间7: KTV, 风口数: 4
 🔄 [通义千问] 楼层2: 2, 房间数: 6
 🔄 [通义千问] 房间1: A201, 风口数: 2
 🔄 [通义千问] 房间2: A202, 风口数: 2
 🔄 [通义千问] 房间3: A203, 风口数: 2
 🔄 [通义千问] 房间4: B201, 风口数: 2
 🔄 [通义千问] 房间5: B202, 风口数: 2
 🔄 [通义千问] 房间6: B203, 风口数: 2
 🔄 [通义千问] 楼层3: 3, 房间数: 4
 🔄 [通义千问] 房间1: A301, 风口数: 2
 🔄 [通义千问] 房间2: A302, 风口数: 2
 🔄 [通义千问] 房间3: B301, 风口数: 2
 🔄 [通义千问] 房间4: B302, 风口数: 2
 ✅ [通义千问] 备用解析成功，跳过JSON错误显示
 🔧 [通义千问] JSON解析耗时: 8.30ms
 🎉 [通义千问] 风口分析总耗时: 29791.20ms (29.79秒)
 ✅ AI识别完成: Object
 🤖 AI识别结果: Object
 🏢 智能楼层提取: "金桂苑 金桂苑"
 ⚠️ 无法提取楼层，使用默认值: "1楼"
 🔍 风口类型识别: 原始="回风3390✖️300", 系统="white_return", 尺寸=3390×300
 🎯 关键词识别为回风口: "回风3390✖️300"
 🏠 添加有意义的房间名到备注: "活动室"
 📋 最终备注: "活动室"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风4640✖️160", 系统="double_white_outlet", 尺寸=4640×160
 🎯 关键词识别为出风口: "出风4640✖️160"
 🏠 添加有意义的房间名到备注: "活动室"
 📋 最终备注: "活动室"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1570✖️290", 系统="white_return", 尺寸=1570×290
 🎯 关键词识别为回风口: "回风1570✖️290"
 🏠 添加有意义的房间名到备注: "客厅"
 📋 最终备注: "客厅"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1700✖️300", 系统="white_return", 尺寸=1700×300
 🎯 关键词识别为回风口: "回风1700✖️300"
 🏠 添加有意义的房间名到备注: "客厅"
 📋 最终备注: "客厅"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="检修口400✖️400", 系统="white_return", 尺寸=400×400
 🎯 尺寸识别为回风口: 宽度400mm≥255mm
 🏠 添加有意义的房间名到备注: "客厅"
 📝 添加原始备注: "检修"
 📋 最终备注: "客厅，检修"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风4430✖️130", 系统="double_white_outlet", 尺寸=4430×130
 🎯 关键词识别为出风口: "出风4430✖️130"
 🏠 添加有意义的房间名到备注: "客厅"
 📋 最终备注: "客厅"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风4450✖️135", 系统="double_white_outlet", 尺寸=4450×135
 🎯 关键词识别为出风口: "出风4450✖️135"
 🏠 添加有意义的房间名到备注: "客厅"
 📋 最终备注: "客厅"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1390✖️300", 系统="white_return", 尺寸=1390×300
 🎯 关键词识别为回风口: "回风1390✖️300"
 🏠 添加有意义的房间名到备注: "茶室"
 📋 最终备注: "茶室"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风1140✖️230普通下出", 系统="double_white_outlet", 尺寸=1140×230
 🎯 关键词识别为出风口: "出风1140✖️230普通下出"
 🏠 添加有意义的房间名到备注: "茶室"
 📝 添加原始备注: "普通下出"
 📋 最终备注: "茶室，普通下出"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1280✖️300", 系统="white_return", 尺寸=1280×300
 🎯 关键词识别为回风口: "回风1280✖️300"
 🏠 添加有意义的房间名到备注: "棋牌室"
 📋 最终备注: "棋牌室"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风3000✖️140", 系统="double_white_outlet", 尺寸=3000×140
 🎯 关键词识别为出风口: "出风3000✖️140"
 🏠 添加有意义的房间名到备注: "棋牌室"
 📋 最终备注: "棋牌室"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1700✖️290", 系统="white_return", 尺寸=1700×290
 🎯 关键词识别为回风口: "回风1700✖️290"
 🏠 添加有意义的房间名到备注: "餐厅"
 📋 最终备注: "餐厅"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风4630✖️150", 系统="double_white_outlet", 尺寸=4630×150
 🎯 关键词识别为出风口: "出风4630✖️150"
 🏠 添加有意义的房间名到备注: "餐厅"
 📋 最终备注: "餐厅"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1700✖️300", 系统="white_return", 尺寸=1700×300
 🎯 关键词识别为回风口: "回风1700✖️300"
 🏠 添加有意义的房间名到备注: "餐厅"
 📋 最终备注: "餐厅"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1700✖️290", 系统="white_return", 尺寸=1700×290
 🎯 关键词识别为回风口: "回风1700✖️290"
 🏠 添加有意义的房间名到备注: "厨房"
 📋 最终备注: "厨房"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风400✖️400  3个，留圆接200软管", 系统="white_return", 尺寸=400×400
 🎯 关键词识别为出风口: "出风400✖️400  3个，留圆接200软管"
 🏠 添加有意义的房间名到备注: "厨房"
 📝 添加原始备注: "留圆接，软管"
 📋 最终备注: "厨房，留圆接，软管"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1470✖️300", 系统="white_return", 尺寸=1470×300
 🎯 关键词识别为回风口: "回风1470✖️300"
 🏠 添加有意义的房间名到备注: "KTV"
 📋 最终备注: "KTV"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1570✖️300", 系统="white_return", 尺寸=1570×300
 🎯 关键词识别为回风口: "回风1570✖️300"
 🏠 添加有意义的房间名到备注: "KTV"
 📋 最终备注: "KTV"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风3890✖️190", 系统="double_white_outlet", 尺寸=3890×190
 🎯 关键词识别为出风口: "出风3890✖️190"
 🏠 添加有意义的房间名到备注: "KTV"
 📋 最终备注: "KTV"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风3870✖️190", 系统="double_white_outlet", 尺寸=3870×190
 🎯 关键词识别为出风口: "出风3870✖️190"
 🏠 添加有意义的房间名到备注: "KTV"
 📋 最终备注: "KTV"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风3040✖️300", 系统="white_return", 尺寸=3040×300
 🎯 关键词识别为回风口: "回风3040✖️300"
 🏠 添加有意义的房间名到备注: "A201"
 📋 最终备注: "A201"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风400✖️400，4个普通下出", 系统="white_return", 尺寸=400×400
 🎯 关键词识别为出风口: "出风400✖️400，4个普通下出"
 🏠 添加有意义的房间名到备注: "A201"
 📝 添加原始备注: "普通下出"
 📋 最终备注: "A201，普通下出"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1380✖️300", 系统="white_return", 尺寸=1380×300
 🎯 关键词识别为回风口: "回风1380✖️300"
 🏠 添加有意义的房间名到备注: "A202"
 📋 最终备注: "A202"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风3720✖️260", 系统="white_return", 尺寸=3720×260
 🎯 关键词识别为出风口: "出风3720✖️260"
 🏠 添加有意义的房间名到备注: "A202"
 📋 最终备注: "A202"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1260✖️300", 系统="white_return", 尺寸=1260×300
 🎯 关键词识别为回风口: "回风1260✖️300"
 🏠 添加有意义的房间名到备注: "A203"
 📋 最终备注: "A203"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风3730✖️230", 系统="double_white_outlet", 尺寸=3730×230
 🎯 关键词识别为出风口: "出风3730✖️230"
 🏠 添加有意义的房间名到备注: "A203"
 📋 最终备注: "A203"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风3010✖️300", 系统="white_return", 尺寸=3010×300
 🎯 关键词识别为回风口: "回风3010✖️300"
 🏠 添加有意义的房间名到备注: "B201"
 📋 最终备注: "B201"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风400✖️400，4个普通下出", 系统="white_return", 尺寸=400×400
 🎯 关键词识别为出风口: "出风400✖️400，4个普通下出"
 🏠 添加有意义的房间名到备注: "B201"
 📝 添加原始备注: "普通下出"
 📋 最终备注: "B201，普通下出"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1410✖️300", 系统="white_return", 尺寸=1410×300
 🎯 关键词识别为回风口: "回风1410✖️300"
 🏠 添加有意义的房间名到备注: "B202"
 📋 最终备注: "B202"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风3720✖️280", 系统="white_return", 尺寸=3720×280
 🎯 关键词识别为出风口: "出风3720✖️280"
 🏠 添加有意义的房间名到备注: "B202"
 📋 最终备注: "B202"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1350✖️300", 系统="white_return", 尺寸=1350×300
 🎯 关键词识别为回风口: "回风1350✖️300"
 🏠 添加有意义的房间名到备注: "B203"
 📋 最终备注: "B203"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风饿3730✖️200", 系统="double_white_outlet", 尺寸=3730×200
 🎯 关键词识别为出风口: "出风饿3730✖️200"
 🏠 添加有意义的房间名到备注: "B203"
 📋 最终备注: "B203"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风3020✖️300", 系统="white_return", 尺寸=3020×300
 🎯 关键词识别为回风口: "回风3020✖️300"
 🏠 添加有意义的房间名到备注: "A301"
 📋 最终备注: "A301"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风5950✖️200", 系统="double_white_outlet", 尺寸=5950×200
 🎯 关键词识别为出风口: "出风5950✖️200"
 🏠 添加有意义的房间名到备注: "A301"
 📋 最终备注: "A301"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1520✖️300", 系统="white_return", 尺寸=1520×300
 🎯 关键词识别为回风口: "回风1520✖️300"
 🏠 添加有意义的房间名到备注: "A302"
 📋 最终备注: "A302"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风3680✖️210", 系统="double_white_outlet", 尺寸=3680×210
 🎯 关键词识别为出风口: "出风3680✖️210"
 🏠 添加有意义的房间名到备注: "A302"
 📋 最终备注: "A302"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风2960✖️300", 系统="white_return", 尺寸=2960×300
 🎯 关键词识别为回风口: "回风2960✖️300"
 🏠 添加有意义的房间名到备注: "B301"
 📋 最终备注: "B301"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风5930✖️200", 系统="double_white_outlet", 尺寸=5930×200
 🎯 关键词识别为出风口: "出风5930✖️200"
 🏠 添加有意义的房间名到备注: "B301"
 📋 最终备注: "B301"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="回风1450✖️300", 系统="white_return", 尺寸=1450×300
 🎯 关键词识别为回风口: "回风1450✖️300"
 🏠 添加有意义的房间名到备注: "B302"
 📋 最终备注: "B302"
 🔍 获取默认单价: white_return -> 130 Object
 🔍 获取默认单价: white_return -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔍 风口类型识别: 原始="出风3660✖️220", 系统="double_white_outlet", 尺寸=3660×220
 🎯 关键词识别为出风口: "出风3660✖️220"
 🏠 添加有意义的房间名到备注: "B302"
 📋 最终备注: "B302"
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔍 获取默认单价: double_white_outlet -> 130 Object
 🔧 [AI识别] 风口数据构建完成: Object
 🔄 转换后的项目数据: Array(1)
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=3390×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值3390作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=1.242000㎡, 计算=1.242 × 130 × 1 = 161.46
 🔧 [价格计算] 最终价格: 161.46 → 161.5
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=4640×160
 📏 检测到特殊规格风口: 宽度160mm (小于标准250mm)
 🔄 智能识别结果: 大值4640作为长度，小值160作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=1.034000㎡, 计算=1.034 × 130 × 1 = 134.42000000000002
 🔧 [价格计算] 最终价格: 134.42000000000002 → 134.4
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1570×290
 ✅ 长方形风口宽度正常: 290mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1570作为长度，小值290作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.570500㎡, 计算=0.5705 × 130 × 1 = 74.165
 🔧 [价格计算] 最终价格: 74.165 → 74.2
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1700×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1700作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.633600㎡, 计算=0.6336 × 130 × 1 = 82.36800000000001
 🔧 [价格计算] 最终价格: 82.36800000000001 → 82.4
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=400×400
 ✅ 长宽相等: 400×400，无需调整
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.211600㎡, 计算=0.2116 × 130 × 1 = 27.508000000000003
 🔧 [价格计算] 最终价格: 27.508000000000003 → 27.5
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=4430×130
 📏 检测到特殊规格风口: 宽度130mm (小于标准250mm)
 🔄 智能识别结果: 大值4430作为长度，小值130作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.853100㎡, 计算=0.8531 × 130 × 1 = 110.90299999999999
 🔧 [价格计算] 最终价格: 110.90299999999999 → 110.9
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=4450×135
 📏 检测到特殊规格风口: 宽度135mm (小于标准250mm)
 🔄 智能识别结果: 大值4450作为长度，小值135作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.879450㎡, 计算=0.87945 × 130 × 1 = 114.32849999999999
 🔧 [价格计算] 最终价格: 114.32849999999999 → 114.3
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1390×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1390作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.522000㎡, 计算=0.522 × 130 × 1 = 67.86
 🔧 [价格计算] 最终价格: 67.86 → 67.9
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1140×230
 📏 检测到特殊规格风口: 宽度230mm (小于标准250mm)
 🔄 智能识别结果: 大值1140作为长度，小值230作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.348000㎡, 计算=0.348 × 130 × 1 = 45.239999999999995
 🔧 [价格计算] 最终价格: 45.239999999999995 → 45.2
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1280×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1280作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.482400㎡, 计算=0.4824 × 130 × 1 = 62.711999999999996
 🔧 [价格计算] 最终价格: 62.711999999999996 → 62.7
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=3000×140
 📏 检测到特殊规格风口: 宽度140mm (小于标准250mm)
 🔄 智能识别结果: 大值3000作为长度，小值140作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.612000㎡, 计算=0.612 × 130 × 1 = 79.56
 🔧 [价格计算] 最终价格: 79.56 → 79.6
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1700×290
 ✅ 长方形风口宽度正常: 290mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1700作为长度，小值290作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.616000㎡, 计算=0.616 × 130 × 1 = 80.08
 🔧 [价格计算] 最终价格: 80.08 → 80.1
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=4630×150
 📏 检测到特殊规格风口: 宽度150mm (小于标准250mm)
 🔄 智能识别结果: 大值4630作为长度，小值150作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.984900㎡, 计算=0.9849 × 130 × 1 = 128.037
 🔧 [价格计算] 最终价格: 128.037 → 128
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1700×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1700作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.633600㎡, 计算=0.6336 × 130 × 1 = 82.36800000000001
 🔧 [价格计算] 最终价格: 82.36800000000001 → 82.4
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1700×290
 ✅ 长方形风口宽度正常: 290mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1700作为长度，小值290作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.616000㎡, 计算=0.616 × 130 × 1 = 80.08
 🔧 [价格计算] 最终价格: 80.08 → 80.1
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=400×400
 ✅ 长宽相等: 400×400，无需调整
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.211600㎡, 计算=0.2116 × 130 × 3 = 82.524
 🔧 [价格计算] 最终价格: 82.524 → 82.5
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1470×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1470作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.550800㎡, 计算=0.5508 × 130 × 1 = 71.604
 🔧 [价格计算] 最终价格: 71.604 → 71.6
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1570×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1570作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.586800㎡, 计算=0.5868 × 130 × 1 = 76.28399999999999
 🔧 [价格计算] 最终价格: 76.28399999999999 → 76.3
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=3890×190
 📏 检测到特殊规格风口: 宽度190mm (小于标准250mm)
 🔄 智能识别结果: 大值3890作为长度，小值190作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.987500㎡, 计算=0.9875 × 130 × 1 = 128.375
 🔧 [价格计算] 最终价格: 128.375 → 128.4
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=3870×190
 📏 检测到特殊规格风口: 宽度190mm (小于标准250mm)
 🔄 智能识别结果: 大值3870作为长度，小值190作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.982500㎡, 计算=0.9825 × 130 × 1 = 127.72500000000001
 🔧 [价格计算] 最终价格: 127.72500000000001 → 127.7
 🔧 [楼层总价] 楼层"1楼"总价计算: 20个风口, 总价=1817.7
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=3040×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值3040作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=1.116000㎡, 计算=1.116 × 130 × 1 = 145.08
 🔧 [价格计算] 最终价格: 145.08 → 145.1
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=400×400
 ✅ 长宽相等: 400×400，无需调整
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.211600㎡, 计算=0.2116 × 130 × 4 = 110.03200000000001
 🔧 [价格计算] 最终价格: 110.03200000000001 → 110
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1380×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1380作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.518400㎡, 计算=0.5184 × 130 × 1 = 67.392
 🔧 [价格计算] 最终价格: 67.392 → 67.4
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=3720×260
 ✅ 长方形风口宽度正常: 260mm (标准范围250-350mm)
 🔄 智能识别结果: 大值3720作为长度，小值260作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=1.209600㎡, 计算=1.2096 × 130 × 1 = 157.248
 🔧 [价格计算] 最终价格: 157.248 → 157.2
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1260×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1260作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.475200㎡, 计算=0.4752 × 130 × 1 = 61.776
 🔧 [价格计算] 最终价格: 61.776 → 61.8
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=3730×230
 📏 检测到特殊规格风口: 宽度230mm (小于标准250mm)
 🔄 智能识别结果: 大值3730作为长度，小值230作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=1.099100㎡, 计算=1.0991 × 130 × 1 = 142.88299999999998
 🔧 [价格计算] 最终价格: 142.88299999999998 → 142.9
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=3010×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值3010作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=1.105200㎡, 计算=1.1052 × 130 × 1 = 143.676
 🔧 [价格计算] 最终价格: 143.676 → 143.7
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=400×400
 ✅ 长宽相等: 400×400，无需调整
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.211600㎡, 计算=0.2116 × 130 × 4 = 110.03200000000001
 🔧 [价格计算] 最终价格: 110.03200000000001 → 110
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1410×300
 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1410作为长度，小值300作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.529200㎡, 计算=0.5292 × 130 × 1 = 68.796
 🔧 [价格计算] 最终价格: 68.796 → 68.8
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=3720×280
 ✅ 长方形风口宽度正常: 280mm (标准范围250-350mm)
 🔄 智能识别结果: 大值3720作为长度，小值280作为宽度
 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=1.285200㎡, 计算=1.2852 × 130 × 1 = 167.076
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 167.076 → 167.1
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=1350×300
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:95 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值1350作为长度，小值300作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.507600㎡, 计算=0.5076 × 130 × 1 = 65.988
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 65.988 → 66
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=3730×200
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:97 📏 检测到特殊规格风口: 宽度200mm (小于标准250mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值3730作为长度，小值200作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.985400㎡, 计算=0.9854 × 130 × 1 = 128.102
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 128.102 → 128.1
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:123 🔧 [楼层总价] 楼层"1楼"总价计算: 12个风口, 总价=1368.0999999999997
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=3020×300
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:95 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值3020作为长度，小值300作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=1.108800㎡, 计算=1.1088 × 130 × 1 = 144.144
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 144.144 → 144.1
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=5950×200
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:97 📏 检测到特殊规格风口: 宽度200mm (小于标准250mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值5950作为长度，小值200作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=1.562600㎡, 计算=1.5626 × 130 × 1 = 203.138
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 203.138 → 203.1
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=1520×300
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:95 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值1520作为长度，小值300作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.568800㎡, 计算=0.5688 × 130 × 1 = 73.944
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 73.944 → 73.9
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=3680×210
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:97 📏 检测到特殊规格风口: 宽度210mm (小于标准250mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值3680作为长度，小值210作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=1.009800㎡, 计算=1.0098 × 130 × 1 = 131.274
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 131.274 → 131.3
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=2960×300
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:95 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值2960作为长度，小值300作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=1.087200㎡, 计算=1.0872 × 130 × 1 = 141.33599999999998
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 141.33599999999998 → 141.3
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=5930×200
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:97 📏 检测到特殊规格风口: 宽度200mm (小于标准250mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值5930作为长度，小值200作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=1.557400㎡, 计算=1.5574 × 130 × 1 = 202.462
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 202.462 → 202.5
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=1450×300
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:95 ✅ 长方形风口宽度正常: 300mm (标准范围250-350mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值1450作为长度，小值300作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.543600㎡, 计算=0.5436 × 130 × 1 = 70.66799999999999
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 70.66799999999999 → 70.7
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=3660×220
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:97 📏 检测到特殊规格风口: 宽度220mm (小于标准250mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值3660作为长度，小值220作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=130, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=1.041600㎡, 计算=1.0416 × 130 × 1 = 135.40800000000002
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 135.40800000000002 → 135.4
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:123 🔧 [楼层总价] 楼层"1楼"总价计算: 8个风口, 总价=1102.3000000000002
D:\personal\2025Projects\factorysystem\src\app\(factory)\factory\orders\create-table\page.tsx:7785 🎉 AI识别成功！识别到 1 个项目
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:163 🎯 页面重新获得焦点，检查 Token 状态
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:69 🔍 检查 Token 状态...
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:78 ✅ Token 状态正常
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:163 🎯 页面重新获得焦点，检查 Token 状态
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:69 🔍 检查 Token 状态...
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:78 ✅ Token 状态正常
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:154 👁️ 页面重新可见，检查 Token 状态
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:69 🔍 检查 Token 状态...
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:78 ✅ Token 状态正常
