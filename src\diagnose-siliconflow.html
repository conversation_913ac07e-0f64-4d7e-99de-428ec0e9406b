<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硅基流动 API 诊断工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover { background: #1976D2; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #4CAF50; background: #e8f5e8; }
        .error { border-color: #f44336; background: #ffe8e8; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        .step { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 硅基流动 API 诊断工具</h1>
        <p>逐步诊断硅基流动API连接问题，找出超时的根本原因。</p>
        
        <div class="step">
            <strong>🎯 诊断步骤：</strong><br>
            1. 测试基础网络连接<br>
            2. 验证API Key有效性<br>
            3. 测试简单API调用<br>
            4. 测试完整风口识别<br>
            5. 提供解决方案
        </div>
        
        <button class="button" onclick="runFullDiagnosis()">🚀 开始完整诊断</button>
        <button class="button" onclick="testNetworkOnly()">🌐 仅测试网络</button>
        <button class="button" onclick="testSimpleAPI()">⚡ 测试简单API</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_KEY = 'sk-szczomdkrprhzlzuzwlblenwfvvuuuyxbxnjgmrcetorftth';
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}] ${title}</strong>\n${content}`;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function testNetworkConnection() {
            addResult('🌐 步骤1: 测试网络连接', '检查到硅基流动服务器的基础连接...', 'info');
            
            const startTime = performance.now();
            
            try {
                // 测试基础连接（不需要API Key）
                const response = await fetch('https://api.siliconflow.cn/v1/models', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer invalid-key-for-connection-test'
                    }
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '✅ 网络连接测试',
                    `连接时间: ${duration.toFixed(2)}ms
状态码: ${response.status}
${duration < 5000 ? '🟢 网络连接正常' : '🟡 网络连接较慢'}`,
                    duration < 5000 ? 'success' : 'warning'
                );
                
                return { success: true, duration };
                
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '❌ 网络连接失败',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}
可能原因: 网络问题、防火墙阻止、DNS解析失败`,
                    'error'
                );
                
                return { success: false, error: error.message };
            }
        }
        
        async function testAPIKeyValidity() {
            addResult('🔑 步骤2: 验证API Key', '检查API Key是否有效...', 'info');
            
            const startTime = performance.now();
            
            try {
                const response = await fetch('https://api.siliconflow.cn/v1/models', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`
                    }
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(
                        '✅ API Key验证成功',
                        `响应时间: ${duration.toFixed(2)}ms
可用模型数量: ${data.data?.length || 'N/A'}
API Key状态: 有效`,
                        'success'
                    );
                    return { success: true, duration };
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ API Key验证失败',
                        `状态码: ${response.status}
响应时间: ${duration.toFixed(2)}ms
错误信息: ${errorText}
可能原因: API Key无效、权限不足、账户余额不足`,
                        'error'
                    );
                    return { success: false, error: `API Key验证失败: ${response.status}` };
                }
                
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '❌ API Key验证异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}`,
                    'error'
                );
                
                return { success: false, error: error.message };
            }
        }
        
        async function testSimpleAPICall() {
            addResult('⚡ 步骤3: 测试简单API调用', '发送最简单的聊天请求...', 'info');
            
            const startTime = performance.now();
            
            const requestBody = {
                model: 'deepseek-ai/DeepSeek-V3',
                messages: [
                    {
                        role: 'user',
                        content: '你好，请回复"测试成功"'
                    }
                ],
                max_tokens: 10,
                temperature: 0.1,
                stream: false
            };
            
            try {
                const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0]?.message?.content;
                    
                    addResult(
                        '✅ 简单API调用成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
使用模型: ${data.model}
AI回复: ${content}
Token使用: ${data.usage?.total_tokens || 'N/A'}
${duration < 10000 ? '🟢 响应速度正常' : '🟡 响应速度较慢'}`,
                        duration < 10000 ? 'success' : 'warning'
                    );
                    
                    return { success: true, duration, content };
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ 简单API调用失败',
                        `状态码: ${response.status}
响应时间: ${duration.toFixed(2)}ms
错误信息: ${errorText}`,
                        'error'
                    );
                    return { success: false, error: `API调用失败: ${response.status}` };
                }
                
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '❌ 简单API调用异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}
${error.name === 'AbortError' ? '⚠️ 请求超时' : ''}`,
                    'error'
                );
                
                return { success: false, error: error.message };
            }
        }
        
        async function testComplexAPICall() {
            addResult('🔧 步骤4: 测试复杂API调用', '测试完整的风口识别请求...', 'info');
            
            const startTime = performance.now();
            
            const prompt = `识别风口订单，返回JSON。

文本：项目：测试项目
一楼：
出风口 2665×155 白色 1个

规则：
1. 风口类型：宽度≤254mm为出风口，宽度≥255mm为回风口
2. 必须返回完整JSON格式

返回格式：
{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet","dimensions":{"length":2665,"width":155}}]}]}]}]}`;

            const requestBody = {
                model: 'deepseek-ai/DeepSeek-V3',
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 1500,
                temperature: 0.1,
                stream: false
            };
            
            try {
                const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0]?.message?.content;
                    
                    addResult(
                        '✅ 复杂API调用成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
使用模型: ${data.model}
Token使用: ${data.usage?.total_tokens || 'N/A'}
响应长度: ${content?.length || 0} 字符
${duration < 15000 ? '🟢 性能优秀' : duration < 30000 ? '🟡 性能一般' : '🔴 性能较差'}

AI响应内容:
${content}`,
                        duration < 15000 ? 'success' : 'warning'
                    );
                    
                    return { success: true, duration, content };
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ 复杂API调用失败',
                        `状态码: ${response.status}
响应时间: ${duration.toFixed(2)}ms
错误信息: ${errorText}`,
                        'error'
                    );
                    return { success: false, error: `API调用失败: ${response.status}` };
                }
                
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '❌ 复杂API调用异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}
${error.name === 'AbortError' ? '⚠️ 请求超时' : ''}`,
                    'error'
                );
                
                return { success: false, error: error.message };
            }
        }
        
        async function runFullDiagnosis() {
            addResult('🚀 开始完整诊断', '正在逐步检查硅基流动API连接...', 'info');
            
            // 步骤1: 网络连接
            const networkResult = await testNetworkConnection();
            if (!networkResult.success) {
                addResult('🛑 诊断终止', '网络连接失败，无法继续后续测试', 'error');
                return;
            }
            
            // 步骤2: API Key验证
            const keyResult = await testAPIKeyValidity();
            if (!keyResult.success) {
                addResult('🛑 诊断终止', 'API Key验证失败，请检查密钥是否正确', 'error');
                return;
            }
            
            // 步骤3: 简单API调用
            const simpleResult = await testSimpleAPICall();
            if (!simpleResult.success) {
                addResult('🛑 诊断终止', '简单API调用失败，服务可能不可用', 'error');
                return;
            }
            
            // 步骤4: 复杂API调用
            const complexResult = await testComplexAPICall();
            
            // 生成诊断报告
            generateDiagnosisReport({
                network: networkResult,
                apiKey: keyResult,
                simple: simpleResult,
                complex: complexResult
            });
        }
        
        function generateDiagnosisReport(results) {
            const allSuccess = results.network.success && results.apiKey.success && 
                             results.simple.success && results.complex.success;
            
            if (allSuccess) {
                addResult(
                    '🎉 诊断完成 - 一切正常',
                    `硅基流动API工作正常！
网络连接: ✅ ${results.network.duration.toFixed(2)}ms
API Key: ✅ 有效
简单调用: ✅ ${results.simple.duration.toFixed(2)}ms
复杂调用: ✅ ${results.complex.duration.toFixed(2)}ms

建议: 如果在应用中仍然超时，可能是应用内部的网络配置问题。`,
                    'success'
                );
            } else {
                let issues = [];
                if (!results.network.success) issues.push('网络连接问题');
                if (!results.apiKey.success) issues.push('API Key无效');
                if (!results.simple.success) issues.push('API服务异常');
                if (!results.complex.success) issues.push('复杂请求失败');
                
                addResult(
                    '⚠️ 诊断完成 - 发现问题',
                    `发现以下问题:
${issues.map(issue => `❌ ${issue}`).join('\n')}

解决建议:
1. 检查网络连接和防火墙设置
2. 验证API Key是否正确和有效
3. 检查账户余额是否充足
4. 尝试更换网络环境
5. 联系硅基流动技术支持`,
                    'warning'
                );
            }
        }
        
        async function testNetworkOnly() {
            await testNetworkConnection();
        }
        
        async function testSimpleAPI() {
            await testSimpleAPICall();
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult(
                '📋 诊断工具说明',
                `API Key: ${API_KEY.substring(0, 20)}...
目标服务: https://api.siliconflow.cn
测试模型: deepseek-ai/DeepSeek-V3

点击"开始完整诊断"进行全面检查！`,
                'info'
            );
        };
    </script>
</body>
</html>
