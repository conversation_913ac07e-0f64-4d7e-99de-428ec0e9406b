'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Edit3, RotateCcw, Brain, FileText, Loader2 } from 'lucide-react'

interface OCRTextEditorProps {
  isOpen: boolean
  onClose: () => void
  originalText: string
  fileName: string
  onConfirm: (editedText: string) => void
  onCancel: () => void
  onAIAnalyze?: (text: string) => Promise<any> // 新增AI分析回调
}

export default function OCRTextEditor({
  isOpen,
  onClose,
  originalText,
  fileName,
  onConfirm,
  onCancel,
  onAIAnalyze
}: OCRTextEditorProps) {
  const [editedText, setEditedText] = useState(originalText)
  const [hasChanges, setHasChanges] = useState(false)
  const [isAIAnalyzing, setIsAIAnalyzing] = useState(false)
  const [aiResult, setAiResult] = useState<any>(null)

  useEffect(() => {
    setEditedText(originalText)
    setHasChanges(false)
  }, [originalText])

  useEffect(() => {
    setHasChanges(editedText !== originalText)
  }, [editedText, originalText])

  const handleReset = () => {
    setEditedText(originalText)
    setHasChanges(false)
    setAiResult(null)
  }

  const handleConfirm = () => {
    onConfirm(editedText)
    onClose()
  }

  const handleCancel = () => {
    onCancel()
    onClose()
  }

  // 处理AI识别并创建订单
  const handleAIAnalyze = async () => {
    if (!onAIAnalyze) return

    setIsAIAnalyzing(true)
    try {
      console.log('🤖 开始AI智能识别OCR文本并创建订单...')
      const result = await onAIAnalyze(editedText)
      setAiResult(result)
      console.log('✅ AI识别完成，准备创建订单:', result)

      // AI识别成功后，直接使用AI结果创建订单
      if (result) {
        console.log('🚀 使用AI识别结果创建订单...')
        // 调用原始的文字识别回调，传递AI识别的结果
        onConfirm(editedText) // 传递原始文本，让系统使用AI结果
        onClose()
      }
    } catch (error) {
      console.error('❌ AI识别失败:', error)
      alert('AI识别失败，请稍后重试')
    } finally {
      setIsAIAnalyzing(false)
    }
  }

  // 使用AI分析结果（预览功能）
  const handleUseAIResult = () => {
    if (aiResult) {
      // 将AI分析的结构化数据转换为文本格式
      const aiText = formatAIResultToText(aiResult)
      setEditedText(aiText)
      setHasChanges(true)
    }
  }

  // 将AI结果格式化为文本（适配通义千问格式）
  const formatAIResultToText = (result: any): string => {
    let formattedText = ''

    // 处理通义千问的projects结构
    if (result.projects && result.projects.length > 0) {
      result.projects.forEach((project: any, projectIndex: number) => {
        // 添加项目信息
        if (project.projectName) {
          formattedText += `项目：${project.projectName}\n`
        }
        if (project.clientInfo) {
          formattedText += `客户：${project.clientInfo}\n`
        }

        formattedText += '\n'

        // 处理楼层信息
        if (project.floors && project.floors.length > 0) {
          project.floors.forEach((floor: any) => {
            if (floor.floorName) {
              formattedText += `${floor.floorName}:\n`
            }

            // 处理房间信息
            if (floor.rooms && floor.rooms.length > 0) {
              floor.rooms.forEach((room: any) => {
                if (room.roomName && room.roomName !== '默认房间') {
                  formattedText += `  ${room.roomName}:\n`
                }

                // 处理风口信息
                if (room.vents && room.vents.length > 0) {
                  room.vents.forEach((vent: any, ventIndex: number) => {
                    const prefix = room.roomName && room.roomName !== '默认房间' ? '    ' : '  '
                    formattedText += `${prefix}${ventIndex + 1}. `

                    // 尺寸信息
                    if (vent.length && vent.width) {
                      formattedText += `${vent.length}×${vent.width}`
                    }

                    // 数量信息
                    if (vent.quantity && vent.quantity > 1) {
                      formattedText += ` ${vent.quantity}个`
                    }

                    // 备注信息
                    if (vent.notes) {
                      formattedText += ` (${vent.notes})`
                    }

                    formattedText += '\n'
                  })
                }
              })
            }
            formattedText += '\n'
          })
        }
      })
    } else if (result.entities) {
      // 兼容其他格式（如DeepSeek格式）
      const { entities } = result

      // 添加项目信息
      if (entities.projectName) {
        formattedText += `项目：${entities.projectName}\n`
      }
      if (entities.floorInfo) {
        formattedText += `楼层：${entities.floorInfo}\n`
      }

      formattedText += '\n'

      // 添加风口信息
      if (entities.ventItems && entities.ventItems.length > 0) {
        entities.ventItems.forEach((item: any, index: number) => {
          formattedText += `${index + 1}. ${item.type || '风口'}`
          if (item.dimensions) {
            formattedText += ` ${item.dimensions}`
          }
          if (item.quantity && item.quantity > 1) {
            formattedText += ` ${item.quantity}个`
          }
          if (item.notes) {
            formattedText += ` (${item.notes})`
          }
          formattedText += '\n'
        })
      }
    }

    return formattedText.trim()
  }

  if (!isOpen) return null

  // 分析文本内容，提供编辑提示
  const analyzeText = (text: string) => {
    const lines = text.split('\n').filter(line => line.trim())
    const ventLines = lines.filter(line => 
      /(出风|回风|进气|检修|维修|长.*宽|宽.*长|\d+.*[×xX].*\d+)/.test(line)
    )
    const projectLines = lines.filter(line => 
      !/(出风|回风|进气|检修|维修|长.*宽|宽.*长|\d+.*[×xX].*\d+)/.test(line) && line.trim()
    )
    
    return { ventLines, projectLines, totalLines: lines.length }
  }

  const analysis = analyzeText(editedText)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-2">
      <Card className="w-full max-w-7xl h-[95vh] overflow-hidden bg-white shadow-2xl flex flex-col">
        <CardHeader className="pb-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-semibold flex items-center gap-2 text-gray-800">
                <Edit3 className="w-6 h-6 text-blue-600" />
                OCR识别结果编辑
              </CardTitle>
              <p className="text-sm text-gray-700 mt-1">
                文件：{fileName}
              </p>
            </div>
            <div className="flex gap-2">
              <Badge variant={hasChanges ? "destructive" : "secondary"} className="bg-white">
                {hasChanges ? "已修改" : "未修改"}
              </Badge>
              <Badge variant="outline" className="bg-white border-gray-300">
                {analysis.totalLines} 行
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4 bg-white flex-1 overflow-hidden flex flex-col">
          {/* 分析结果 */}
          <div className="grid grid-cols-2 gap-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-gray-200 flex-shrink-0">
            <div>
              <div className="text-base font-medium text-green-800 mb-3">
                🏢 项目信息 ({analysis.projectLines.length}行)
              </div>
              <div className="text-sm text-gray-700 max-h-24 overflow-y-auto space-y-1">
                {analysis.projectLines.slice(0, 5).map((line, i) => (
                  <div key={i} className="bg-white px-3 py-2 rounded shadow-sm">• {line}</div>
                ))}
                {analysis.projectLines.length > 5 && (
                  <div className="text-gray-500 italic text-center py-1">...还有{analysis.projectLines.length - 5}行</div>
                )}
              </div>
            </div>
            <div>
              <div className="text-base font-medium text-blue-800 mb-3">
                🌪️ 风口信息 ({analysis.ventLines.length}行)
              </div>
              <div className="text-sm text-gray-700 max-h-24 overflow-y-auto space-y-1">
                {analysis.ventLines.slice(0, 5).map((line, i) => (
                  <div key={i} className="bg-white px-3 py-2 rounded shadow-sm">• {line}</div>
                ))}
                {analysis.ventLines.length > 5 && (
                  <div className="text-gray-500 italic text-center py-1">...还有{analysis.ventLines.length - 5}行</div>
                )}
              </div>
            </div>
          </div>

          {/* 编辑提示 */}
          <div className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200 flex-shrink-0">
            <div className="text-base font-medium text-amber-800 mb-3 flex items-center gap-2">
              💡 编辑提示
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm text-amber-800">
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <span className="text-amber-600 font-bold">•</span>
                  <span>检查OCR是否漏掉数字：如"长.34"应该是"长2.34"</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-amber-600 font-bold">•</span>
                  <span>确认尺寸格式：如"长1.2×宽0.15"表示1.2米×0.15米</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <span className="text-amber-600 font-bold">•</span>
                  <span>删除无关内容：如页码、标题等非风口信息</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-amber-600 font-bold">•</span>
                  <span>每行一个风口或项目信息</span>
                </div>
              </div>
            </div>
          </div>

          {/* 文本编辑区 */}
          <div className="space-y-3 flex-1 flex flex-col">
            <div className="flex items-center justify-between">
              <label className="text-base font-medium text-gray-800">识别内容编辑</label>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                disabled={!hasChanges}
                className="text-sm border-gray-300 hover:bg-gray-50"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                重置
              </Button>
            </div>
            <Textarea
              value={editedText}
              onChange={(e) => setEditedText(e.target.value)}
              className="flex-1 min-h-0 font-mono text-base bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-gray-900 placeholder-gray-500 resize-none"
              placeholder="请编辑OCR识别的文本内容..."
            />
          </div>



          {/* 操作按钮 */}
          <div className="flex justify-between gap-4 pt-4 border-t border-gray-200 bg-gray-50 -mx-6 -mb-6 px-6 py-5 flex-shrink-0">
            {/* 左侧：取消按钮 */}
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex items-center gap-2 border-gray-300 hover:bg-gray-50 text-gray-700 px-6 py-2 text-base"
            >
              <XCircle className="w-5 h-5" />
              取消
            </Button>

            {/* 右侧：识别选择按钮 */}
            <div className="flex gap-3">
              {/* AI智能识别按钮 */}
              {onAIAnalyze && (
                <Button
                  variant="outline"
                  onClick={handleAIAnalyze}
                  disabled={isAIAnalyzing || !editedText.trim()}
                  className="flex items-center gap-2 border-purple-300 text-purple-700 hover:bg-purple-50 px-6 py-2 text-base"
                >
                  {isAIAnalyzing ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      AI识别中...
                    </>
                  ) : (
                    <>
                      <Brain className="w-5 h-5" />
                      AI识别并创建订单
                    </>
                  )}
                </Button>
              )}

              {/* 默认识别按钮 */}
              <Button
                onClick={handleConfirm}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white shadow-md px-6 py-2 text-base"
              >
                <FileText className="w-5 h-5" />
                默认识别并创建订单
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
