"use client"

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { updateDimensionsWithSmartRecognition, smartDimensionRecognition } from "@/lib/utils/dimension-utils"
import {
  Edit3,
  Save,
  X,
  Plus,
  Trash2,
  Copy,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  FileText
} from "lucide-react"
import type { 
  EditableProjectData, 
  EditableFloorData, 
  EditableVentItem, 
  ValidationError 
} from "@/types/import-preview"

interface ImportPreviewEditorProps {
  projects: EditableProjectData[]
  onProjectUpdate: (projectId: string, updates: Partial<EditableProjectData>) => void
  onFloorUpdate: (projectId: string, floorId: string, updates: Partial<EditableFloorData>) => void
  onVentUpdate: (projectId: string, floorId: string, ventId: string, updates: Partial<EditableVentItem>) => void
  onAddVent: (projectId: string, floorId: string) => void
  onRemoveVent: (projectId: string, floorId: string, ventId: string) => void
  onDuplicateVent: (projectId: string, floorId: string, ventId: string) => void
  onResetItem: (projectId: string, floorId?: string, ventId?: string) => void
  onConfirm: () => void
  onCancel: () => void
  isLoading?: boolean
  selectedProjectIds?: string[]
  onToggleProjectSelection?: (projectId: string) => void
  onSelectAllProjects?: () => void
  onDeselectAllProjects?: () => void
  // 客户选择相关
  showClientSelection?: boolean
  selectedClient?: any
  onClientSelect?: (client: any) => void
  existingClients?: any[]
  newClientData?: any
  onNewClientDataChange?: (data: any) => void
}

// 风口类型选项（使用原有系统的类型命名）
const VENT_TYPE_OPTIONS = [
  // 常规风口类型 (元/㎡)
  { value: 'double_white_outlet', label: '双层白色出风口', unit: '元/㎡' },
  { value: 'double_black_outlet', label: '双层黑色出风口', unit: '元/㎡' },
  { value: 'white_black_bottom_outlet', label: '白色黑底出风口', unit: '元/㎡' },
  { value: 'white_black_bottom_return', label: '白色黑底回风口', unit: '元/㎡' },
  { value: 'white_return', label: '白色回风口', unit: '元/㎡' },
  { value: 'black_return', label: '黑色回风口', unit: '元/㎡' },
  { value: 'white_linear', label: '白色线型风口', unit: '元/㎡' },
  { value: 'black_linear', label: '黑色线型风口', unit: '元/㎡' },
  { value: 'white_linear_return', label: '白色线型回风口', unit: '元/㎡' },
  { value: 'black_linear_return', label: '黑色线型回风口', unit: '元/㎡' },
  { value: 'maintenance', label: '检修口', unit: '元/㎡' },

  // 高端风口系列 (元/m)
  { value: 'arrow_outlet', label: '箭型出风口', unit: '元/m' },
  { value: 'arrow_return', label: '箭型回风口', unit: '元/m' },
  { value: 'claw_outlet', label: '爪型出风口', unit: '元/m' },
  { value: 'claw_return', label: '爪型回风口', unit: '元/m' },
  { value: 'black_white_outlet', label: '黑白双色出风口', unit: '元/m' },
  { value: 'black_white_return', label: '黑白双色回风口', unit: '元/m' },
  { value: 'wood_outlet', label: '木纹出风口', unit: '元/m' },
  { value: 'wood_return', label: '木纹回风口', unit: '元/m' },
  { value: 'white_putty_outlet', label: '白色腻子粉出风口', unit: '元/m' },
  { value: 'white_putty_return', label: '白色腻子粉回风口', unit: '元/m' },
  { value: 'black_putty_outlet', label: '黑色腻子粉出风口', unit: '元/m' },
  { value: 'black_putty_return', label: '黑色腻子粉回风口', unit: '元/m' },

  // 石膏板风口 (元/m)
  { value: 'white_gypsum_outlet', label: '白色石膏板出风口', unit: '元/m' },
  { value: 'white_gypsum_return', label: '白色石膏板回风口', unit: '元/m' },
  { value: 'black_gypsum_outlet', label: '黑色石膏板出风口', unit: '元/m' },
  { value: 'black_gypsum_return', label: '黑色石膏板回风口', unit: '元/m' },
]

// 获取风口类型显示名称
const getVentTypeName = (type: string) => {
  const option = VENT_TYPE_OPTIONS.find(opt => opt.value === type)
  return option?.label || type
}

// 获取风口类型单位
const getVentTypeUnit = (type: string) => {
  const option = VENT_TYPE_OPTIONS.find(opt => opt.value === type)
  return option?.unit || '元/个'
}

// 计算风口价格
const calculateVentPrice = (vent: EditableVentItem): number => {
  const { type, length, width, quantity, unitPrice } = vent

  if (unitPrice <= 0) return 0

  let calculatedPrice = 0

  // 常规风口 (元/㎡)：(长+60) × (宽+60) ÷ 1,000,000 × 单价 × 数量
  const regularVentTypes = [
    'double_white_outlet', 'double_black_outlet', 'white_return', 'black_return',
    'white_linear', 'black_linear', 'white_linear_return', 'black_linear_return', 'maintenance'
  ]

  if (regularVentTypes.includes(type)) {
    if (length > 0 && width > 0) {
      const area = (length + 60) * (width + 60) / 1000000
      calculatedPrice = area * unitPrice * quantity
    }
  } else {
    // 高端风口 (元/m)：(长+120) ÷ 1,000 × 单价 × 数量
    if (length > 0) {
      calculatedPrice = ((length + 120) / 1000) * unitPrice * quantity
    }
  }

  return Math.round(calculatedPrice * 100) / 100
}

// 验证风口数据
const validateVent = (vent: EditableVentItem): ValidationError[] => {
  const errors: ValidationError[] = []
  
  if (!vent.length || vent.length < 10) {
    errors.push({ field: 'length', message: '长度不能小于10mm', severity: 'error' })
  }
  if (!vent.width || vent.width < 10) {
    errors.push({ field: 'width', message: '宽度不能小于10mm', severity: 'error' })
  }
  if (!vent.quantity || vent.quantity < 1) {
    errors.push({ field: 'quantity', message: '数量不能小于1', severity: 'error' })
  }
  if (vent.unitPrice < 0) {
    errors.push({ field: 'unitPrice', message: '单价不能为负数', severity: 'error' })
  }
  
  return errors
}

export default function ImportPreviewEditor({
  projects,
  onProjectUpdate,
  onFloorUpdate,
  onVentUpdate,
  onAddVent,
  onRemoveVent,
  onDuplicateVent,
  onResetItem,
  onConfirm,
  onCancel,
  isLoading = false,
  selectedProjectIds = [],
  onToggleProjectSelection,
  onSelectAllProjects,
  onDeselectAllProjects,
  // 客户选择相关
  showClientSelection = false,
  selectedClient,
  onClientSelect,
  existingClients = [],
  newClientData,
  onNewClientDataChange
}: ImportPreviewEditorProps) {
  const [editingItem, setEditingItem] = useState<string | null>(null)
  const [batchPriceModal, setBatchPriceModal] = useState<{ projectId: string, floorId: string } | null>(null)
  const [batchPrice, setBatchPrice] = useState<number>(0)
  const [globalBatchPriceModal, setGlobalBatchPriceModal] = useState<boolean>(false)
  const [globalBatchPrice, setGlobalBatchPrice] = useState<number>(0)

  // 批量修改风口类型相关状态
  const [globalBatchVentTypeModal, setGlobalBatchVentTypeModal] = useState<boolean>(false)
  const [globalBatchOutletType, setGlobalBatchOutletType] = useState<string>('')
  const [globalBatchReturnType, setGlobalBatchReturnType] = useState<string>('')

  // 批量备注管理状态
  const [selectedVentIds, setSelectedVentIds] = useState<Set<string>>(new Set())
  const [showBatchNotesModal, setShowBatchNotesModal] = useState(false)
  const [batchNotesText, setBatchNotesText] = useState('')
  const [batchNotesMode, setBatchNotesMode] = useState<'overwrite' | 'append'>('overwrite')

  // 开始编辑
  const startEditing = useCallback((itemId: string) => {
    setEditingItem(itemId)
  }, [])

  // 停止编辑
  const stopEditing = useCallback(() => {
    setEditingItem(null)
  }, [])

  // 批量备注管理函数
  // 生成风口的唯一ID（项目ID + 楼层ID + 风口ID）
  const getVentUniqueId = (projectId: string, floorId: string, ventId: string) =>
    `${projectId}-${floorId}-${ventId}`

  // 处理单个风口选择
  const handleVentSelection = useCallback((projectId: string, floorId: string, ventId: string) => {
    const uniqueId = getVentUniqueId(projectId, floorId, ventId)
    setSelectedVentIds(prev => {
      const newSet = new Set(prev)
      if (newSet.has(uniqueId)) {
        newSet.delete(uniqueId)
      } else {
        newSet.add(uniqueId)
      }
      return newSet
    })
  }, [])

  // 全选所有风口
  const handleSelectAllVents = useCallback(() => {
    const allVentIds = projects.flatMap(project =>
      project.floors.flatMap(floor =>
        floor.vents.map(vent => getVentUniqueId(project.id, floor.id, vent.id))
      )
    )
    setSelectedVentIds(new Set(allVentIds))
  }, [projects])

  // 取消全选
  const handleDeselectAllVents = useCallback(() => {
    setSelectedVentIds(new Set())
  }, [])

  // 批量添加备注
  const handleBatchAddNotes = useCallback(() => {
    if (selectedVentIds.size === 0) {
      alert('请先选择要添加备注的风口')
      return
    }
    setShowBatchNotesModal(true)
  }, [selectedVentIds.size])

  // 解析风口的唯一ID（支持复杂ID格式）
  const parseVentUniqueId = (uniqueId: string) => {
    console.log(`🔍 导入预览解析ID: "${uniqueId}"`)

    // 复杂格式：project-1753186706038-18-floor-1753186706038-19-vent-1753186706038-20
    // 需要找到 '-floor-' 和 '-vent-' 分隔符来正确分割
    const floorIndex = uniqueId.indexOf('-floor-')
    const ventIndex = uniqueId.indexOf('-vent-')

    if (floorIndex !== -1 && ventIndex !== -1 && ventIndex > floorIndex) {
      // 找到了 '-floor-' 和 '-vent-' 分隔符
      const projectId = uniqueId.substring(0, floorIndex)
      const floorId = uniqueId.substring(floorIndex + 1, ventIndex) // +1 是为了去掉开头的 '-'
      const ventId = uniqueId.substring(ventIndex + 1) // +1 是为了去掉开头的 '-'

      console.log(`📝 复杂格式解析: projectId="${projectId}", floorId="${floorId}", ventId="${ventId}"`)
      return { projectId, floorId, ventId }
    }

    // 简单格式：projectId-floorId-ventId
    const parts = uniqueId.split('-')
    if (parts.length === 3) {
      const [projectId, floorId, ventId] = parts
      console.log(`📝 简单格式解析: projectId="${projectId}", floorId="${floorId}", ventId="${ventId}"`)
      return { projectId, floorId, ventId }
    }

    // 回退到简单分割（取前三部分）
    const [projectId, floorId, ventId] = parts
    console.log(`⚠️ 回退解析: projectId="${projectId}", floorId="${floorId}", ventId="${ventId}"`)
    return { projectId, floorId, ventId }
  }

  // 确认批量添加备注
  const confirmBatchAddNotes = useCallback(() => {
    if (!batchNotesText.trim()) {
      alert('请输入备注内容')
      return
    }

    console.log('🔍 导入预览批量添加备注调试信息:')
    console.log('选中的风口IDs:', Array.from(selectedVentIds))
    console.log('当前项目数据:', projects.map(p => ({
      id: p.id,
      address: p.projectAddress,
      floors: p.floors.map(f => ({ id: f.id, name: f.floorName, ventCount: f.vents.length }))
    })))

    let processedCount = 0

    selectedVentIds.forEach(uniqueId => {
      console.log(`🔍 处理风口ID: ${uniqueId}`)

      const { projectId, floorId, ventId } = parseVentUniqueId(uniqueId)
      console.log(`🔍 解析得到 projectId: ${projectId}, floorId: ${floorId}, ventId: ${ventId}`)

      const project = projects.find(p => p.id === projectId)
      console.log(`🔍 找到项目: ${project ? `${project.projectAddress} (${project.floors.length}个楼层)` : '未找到'}`)

      const floor = project?.floors.find(f => f.id === floorId)
      console.log(`🔍 找到楼层: ${floor ? `${floor.floorName} (${floor.vents.length}个风口)` : '未找到'}`)

      const vent = floor?.vents.find(v => v.id === ventId)
      console.log(`🔍 找到风口: ${vent ? `${vent.type} (当前备注: "${vent.notes}")` : '未找到'}`)

      if (vent) {
        const newNotes = batchNotesMode === 'overwrite'
          ? batchNotesText.trim()
          : vent.notes
            ? `${vent.notes} ${batchNotesText.trim()}`
            : batchNotesText.trim()

        console.log(`🔍 新备注内容: "${newNotes}"`)
        onVentUpdate(projectId, floorId, ventId, { notes: newNotes })
        processedCount++
      } else {
        console.log(`❌ 未找到风口: projectId=${projectId}, floorId=${floorId}, ventId=${ventId}`)
      }
    })

    console.log(`🔍 处理完成，成功处理 ${processedCount} 个风口`)

    const modeText = batchNotesMode === 'overwrite' ? '设置' : '追加'
    const notesContent = batchNotesText.trim() // 保存备注内容

    setShowBatchNotesModal(false)
    setBatchNotesText('')
    setSelectedVentIds(new Set())

    // 显示详细的操作结果
    const successMessage = `✅ 批量备注操作完成！\n\n` +
      `📊 操作统计:\n` +
      `• 处理风口数量: ${processedCount} 个\n` +
      `• 操作模式: ${modeText}备注\n` +
      `• 备注内容: "${notesContent}"`

    alert(successMessage)
  }, [batchNotesText, batchNotesMode, selectedVentIds, projects, onVentUpdate])

  // 批量清除备注
  const handleBatchClearNotes = useCallback(() => {
    if (selectedVentIds.size === 0) {
      alert('请先选择要清除备注的风口')
      return
    }

    const confirmed = window.confirm(`确定要清除 ${selectedVentIds.size} 个风口的备注吗？此操作不可撤销。`)
    if (!confirmed) return

    console.log('🔍 导入预览批量清除备注调试信息:')
    console.log('选中的风口IDs:', Array.from(selectedVentIds))
    console.log('当前项目数据:', projects.map(p => ({
      id: p.id,
      address: p.projectAddress,
      floors: p.floors.map(f => ({ id: f.id, name: f.floorName, ventCount: f.vents.length }))
    })))

    let processedCount = 0

    selectedVentIds.forEach(uniqueId => {
      console.log(`🔍 处理风口ID: ${uniqueId}`)

      const { projectId, floorId, ventId } = parseVentUniqueId(uniqueId)
      console.log(`🔍 解析得到 projectId: ${projectId}, floorId: ${floorId}, ventId: ${ventId}`)

      const project = projects.find(p => p.id === projectId)
      console.log(`🔍 找到项目: ${project ? `${project.projectAddress} (${project.floors.length}个楼层)` : '未找到'}`)

      const floor = project?.floors.find(f => f.id === floorId)
      console.log(`🔍 找到楼层: ${floor ? `${floor.floorName} (${floor.vents.length}个风口)` : '未找到'}`)

      const vent = floor?.vents.find(v => v.id === ventId)
      console.log(`🔍 找到风口: ${vent ? `${vent.type} (当前备注: "${vent.notes}")` : '未找到'}`)

      if (vent && vent.notes && vent.notes.trim() !== '') {
        console.log(`🔍 清除备注: "${vent.notes}" → ""`)
        onVentUpdate(projectId, floorId, ventId, { notes: '' })
        processedCount++
      } else if (vent) {
        console.log(`ℹ️ 风口备注已为空，跳过`)
      } else {
        console.log(`❌ 未找到风口: projectId=${projectId}, floorId=${floorId}, ventId=${ventId}`)
      }
    })

    console.log(`🔍 处理完成，成功处理 ${processedCount} 个风口`)

    const totalSelected = selectedVentIds.size // 保存选中数量
    setSelectedVentIds(new Set())

    if (processedCount > 0) {
      const successMessage = `✅ 批量清除备注完成！\n\n` +
        `📊 操作统计:\n` +
        `• 清除备注数量: ${processedCount} 个\n` +
        `• 选中风口总数: ${totalSelected} 个\n` +
        `• 跳过空备注: ${totalSelected - processedCount} 个`

      alert(successMessage)
    } else {
      alert('ℹ️ 没有找到需要清除的备注\n\n所选风口的备注都是空的')
    }
  }, [selectedVentIds, projects, onVentUpdate])

  // 更新风口数据并重新计算价格（不进行智能尺寸识别）
  const updateVent = useCallback((projectId: string, floorId: string, ventId: string, updates: Partial<EditableVentItem>) => {
    // 获取当前风口数据
    const project = projects.find(p => p.id === projectId)
    const floor = project?.floors.find(f => f.id === floorId)
    const vent = floor?.vents.find(v => v.id === ventId)

    if (!vent) return

    // 直接使用更新数据，不进行智能尺寸识别
    const processedUpdates = { ...updates }

    // 如果更新了影响价格的字段，重新计算总价
    if ('length' in processedUpdates || 'width' in processedUpdates || 'quantity' in processedUpdates || 'unitPrice' in processedUpdates || 'type' in processedUpdates) {
      const newVent = { ...vent, ...processedUpdates }
      processedUpdates.totalPrice = calculateVentPrice(newVent)
      processedUpdates.hasChanges = true
      processedUpdates.validationErrors = validateVent(newVent)
    }

    onVentUpdate(projectId, floorId, ventId, processedUpdates)
  }, [projects, onVentUpdate])

  // 更新风口尺寸并进行智能识别
  const updateVentWithSmartDimensions = useCallback((projectId: string, floorId: string, ventId: string, length: number, width: number) => {
    console.log(`🔍 智能尺寸更新: 输入=${length}×${width}`)

    // 直接使用智能尺寸识别函数
    const smartResult = smartDimensionRecognition(length, width)

    console.log(`📏 智能识别结果: 处理后=${smartResult.length}×${smartResult.width}`)

    // 检查是否需要调整
    if (smartResult.length !== length || smartResult.width !== width) {
      console.log(`🔄 需要调整尺寸: ${length}×${width} → ${smartResult.length}×${smartResult.width}`)

      // 获取当前风口数据
      const project = projects.find(p => p.id === projectId)
      const floor = project?.floors.find(f => f.id === floorId)
      const vent = floor?.vents.find(v => v.id === ventId)

      if (vent) {
        const processedUpdates = {
          length: smartResult.length,
          width: smartResult.width,
          hasChanges: true
        }

        // 重新计算价格
        const newVent = { ...vent, ...processedUpdates }
        processedUpdates.totalPrice = calculateVentPrice(newVent)
        processedUpdates.validationErrors = validateVent(newVent)

        onVentUpdate(projectId, floorId, ventId, processedUpdates)
      }
    } else {
      console.log(`✅ 尺寸已经正确，无需调整`)
    }
  }, [projects, onVentUpdate])

  // 处理尺寸输入完成（失去焦点时进行智能转换）
  const handleDimensionInputComplete = useCallback((projectId: string, floorId: string, ventId: string) => {
    // 使用 setTimeout 确保在下一个事件循环中执行，此时数据已经更新
    setTimeout(() => {
      const project = projects.find(p => p.id === projectId)
      const floor = project?.floors.find(f => f.id === floorId)
      const vent = floor?.vents.find(v => v.id === ventId)

      console.log(`🔍 尺寸输入完成检查: 风口ID=${ventId}, 当前尺寸=${vent?.length}×${vent?.width}`)

      if (vent && vent.length > 0 && vent.width > 0) {
        console.log(`📏 开始智能尺寸识别: 原始=${vent.length}×${vent.width}`)

        // 应用智能尺寸识别
        const processedUpdates = updateDimensionsWithSmartRecognition(
          {},
          vent.length,
          vent.width
        )

        console.log(`📏 智能识别结果: 处理后=${processedUpdates.length}×${processedUpdates.width}`)

        // 如果发生了调整，更新数据
        if (processedUpdates.length !== vent.length || processedUpdates.width !== vent.width) {
          console.log(`🔄 需要调整尺寸: ${vent.length}×${vent.width} → ${processedUpdates.length}×${processedUpdates.width}`)
          console.log(`✅ 确保大值(${Math.max(vent.length, vent.width)})作为长度，小值(${Math.min(vent.length, vent.width)})作为宽度`)

          // 重新计算价格
          const newVent = { ...vent, ...processedUpdates }
          processedUpdates.totalPrice = calculateVentPrice(newVent)
          processedUpdates.hasChanges = true
          processedUpdates.validationErrors = validateVent(newVent)

          onVentUpdate(projectId, floorId, ventId, processedUpdates)
        } else {
          console.log(`✅ 尺寸已经正确，无需调整`)
        }
      } else {
        console.log(`⚠️ 跳过智能识别: 长度=${vent?.length}, 宽度=${vent?.width}`)
      }
    }, 0)
  }, [projects, onVentUpdate])

  // 批量设置单价（单个楼层）
  const handleBatchSetPrice = useCallback(() => {
    if (!batchPriceModal) return

    const { projectId, floorId } = batchPriceModal
    const project = projects.find(p => p.id === projectId)
    const floor = project?.floors.find(f => f.id === floorId)

    if (floor) {
      floor.vents.forEach(vent => {
        updateVent(projectId, floorId, vent.id, { unitPrice: batchPrice })
      })
    }

    setBatchPriceModal(null)
    setBatchPrice(0)
  }, [batchPriceModal, batchPrice, projects, updateVent])

  // 全局批量设置单价（所有项目所有楼层）
  const handleGlobalBatchSetPrice = useCallback(() => {
    if (globalBatchPrice <= 0) {
      alert('请输入有效的单价')
      return
    }

    let totalVentsUpdated = 0

    projects.forEach(project => {
      project.floors.forEach(floor => {
        floor.vents.forEach(vent => {
          updateVent(project.id, floor.id, vent.id, { unitPrice: globalBatchPrice })
          totalVentsUpdated++
        })
      })
    })

    setGlobalBatchPriceModal(false)
    setGlobalBatchPrice(0)
    alert(`已将所有项目共 ${totalVentsUpdated} 个风口的单价设置为 ${globalBatchPrice} 元`)
  }, [globalBatchPrice, projects, updateVent])

  // 全局批量修改风口类型
  const handleGlobalBatchSetVentType = useCallback(() => {
    if (!globalBatchOutletType && !globalBatchReturnType) {
      alert('请至少选择一种风口类型')
      return
    }

    let totalOutletsUpdated = 0
    let totalReturnsUpdated = 0

    projects.forEach(project => {
      project.floors.forEach(floor => {
        floor.vents.forEach(vent => {
          // 判断是出风口还是回风口
          const isOutlet = vent.type.includes('outlet') || vent.type.includes('linear') && !vent.type.includes('return')
          const isReturn = vent.type.includes('return')

          if (isOutlet && globalBatchOutletType) {
            updateVent(project.id, floor.id, vent.id, { type: globalBatchOutletType })
            totalOutletsUpdated++
          } else if (isReturn && globalBatchReturnType) {
            updateVent(project.id, floor.id, vent.id, { type: globalBatchReturnType })
            totalReturnsUpdated++
          }
        })
      })
    })

    setGlobalBatchVentTypeModal(false)
    setGlobalBatchOutletType('')
    setGlobalBatchReturnType('')

    const messages = []
    if (totalOutletsUpdated > 0) {
      messages.push(`${totalOutletsUpdated} 个出风口`)
    }
    if (totalReturnsUpdated > 0) {
      messages.push(`${totalReturnsUpdated} 个回风口`)
    }

    if (messages.length > 0) {
      alert(`已批量修改 ${messages.join('、')} 的类型`)
    }
  }, [globalBatchOutletType, globalBatchReturnType, projects, updateVent])

  // 计算项目总金额
  const calculateProjectTotal = useCallback((project: EditableProjectData): number => {
    return project.floors.reduce((sum, floor) => 
      sum + floor.vents.reduce((ventSum, vent) => ventSum + vent.totalPrice, 0), 0
    )
  }, [])

  // 检查是否有验证错误
  const hasValidationErrors = useCallback((project: EditableProjectData): boolean => {
    return project.floors.some(floor =>
      floor.vents.some(vent => vent.validationErrors && vent.validationErrors.length > 0)
    )
  }, [])

  // 🔧 清空所有备注
  const handleClearAllNotes = useCallback(() => {
    const confirmed = window.confirm('确定要清空所有风口的备注信息吗？此操作不可撤销。')
    if (!confirmed) return

    let totalCleared = 0

    projects.forEach(project => {
      project.floors.forEach(floor => {
        floor.vents.forEach(vent => {
          if (vent.notes && vent.notes.trim() !== '') {
            onVentUpdate(project.id, floor.id, vent.id, {
              notes: '',
              hasChanges: true
            })
            totalCleared++
          }
        })
      })
    })

    if (totalCleared > 0) {
      alert(`已清空 ${totalCleared} 个风口的备注信息`)
    } else {
      alert('没有找到需要清空的备注信息')
    }
  }, [projects, onVentUpdate])

  return (
    <div className="flex flex-col h-full">
      {/* 头部操作区 - 固定在顶部 */}
      <div className="flex-shrink-0 p-6 border-b bg-white">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">导入数据预览编辑</h3>
            <p className="text-gray-600">请检查并修改导入的数据，确认无误后点击确认导入</p>
          </div>
          <div className="flex space-x-3">
            {/* 批量备注管理按钮 */}
            <div className="flex items-center space-x-2 border-l border-gray-300 pl-3">
              <span className="text-sm text-gray-600 font-medium">批量备注:</span>
              <Button
                onClick={selectedVentIds.size === projects.reduce((total, project) => total + project.floors.reduce((floorTotal, floor) => floorTotal + floor.vents.length, 0), 0) ? handleDeselectAllVents : handleSelectAllVents}
                variant="outline"
                size="sm"
                className="hover:bg-purple-50 hover:border-purple-300"
              >
                <input
                  type="checkbox"
                  checked={selectedVentIds.size > 0 && selectedVentIds.size === projects.reduce((total, project) => total + project.floors.reduce((floorTotal, floor) => floorTotal + floor.vents.length, 0), 0)}
                  onChange={() => {}}
                  className="h-3 w-3 mr-1"
                  readOnly
                />
                {selectedVentIds.size === projects.reduce((total, project) => total + project.floors.reduce((floorTotal, floor) => floorTotal + floor.vents.length, 0), 0) ? '取消全选' : '全选'}
                {selectedVentIds.size > 0 && ` (${selectedVentIds.size})`}
              </Button>
              <Button
                onClick={handleBatchAddNotes}
                variant="outline"
                size="sm"
                disabled={selectedVentIds.size === 0}
                className="hover:bg-green-50 hover:border-green-300 disabled:opacity-50"
              >
                <FileText className="h-3 w-3 mr-1" />
                添加备注
              </Button>
              <Button
                onClick={handleBatchClearNotes}
                variant="outline"
                size="sm"
                disabled={selectedVentIds.size === 0}
                className="hover:bg-red-50 hover:border-red-300 disabled:opacity-50"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                清除备注
              </Button>
            </div>
            <Button
              variant="outline"
              onClick={handleClearAllNotes}
              disabled={isLoading}
              className="text-orange-600 border-orange-300 hover:bg-orange-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              清空所有备注
            </Button>
            <Button variant="outline" onClick={onCancel} disabled={isLoading}>
              <X className="h-4 w-4 mr-2" />
              取消
            </Button>
            <Button onClick={onConfirm} disabled={isLoading} className="bg-blue-600 hover:bg-blue-700">
              <CheckCircle className="h-4 w-4 mr-2" />
              确认导入
            </Button>
          </div>
        </div>
      </div>

      {/* 内容区域 - 可滚动 */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6">

      {/* 客户选择区域 */}
      {showClientSelection && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-3">选择客户</h4>
          <p className="text-xs text-blue-700 mb-3">💡 在此选择的客户信息将自动同步到新建订单页面</p>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                选择客户 <span className="text-red-500">*</span>
              </label>
              <select
                value={selectedClient || ''}
                onChange={(e) => {
                  const clientId = e.target.value
                  onClientSelect?.(clientId)
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">请选择客户</option>
                {existingClients.map((client: any) => (
                  <option key={client.id} value={client.id}>
                    {client.name} - {client.phone}
                  </option>
                ))}
                <option value="new">+ 新增客户</option>
              </select>
            </div>

            {selectedClient === 'new' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    客户姓名 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={newClientData?.name || ''}
                    onChange={(e) => onNewClientDataChange?.({...newClientData, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入客户姓名"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    联系电话 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="tel"
                    value={newClientData?.phone || ''}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '') // 只允许数字
                      onNewClientDataChange?.({...newClientData, phone: value})
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入11位手机号码"
                    maxLength={11}
                    pattern="[0-9]*"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    客户地址
                  </label>
                  <input
                    type="text"
                    value={newClientData?.address || ''}
                    onChange={(e) => onNewClientDataChange?.({...newClientData, address: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入客户地址（可选）"
                  />
                </div>

                {/* 推荐人信息 */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                  <h5 className="font-medium text-yellow-800 mb-2">推荐人信息</h5>
                  <p className="text-sm text-yellow-700 mb-3">
                    ⚠️ 如果此客户是由其他客户推荐的，请务必填写推荐人信息，关系到推荐者的费用！
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">推荐人</label>
                      <select
                        value={newClientData?.referrerId || ''}
                        onChange={(e) => {
                          const selectedReferrer = (existingClients || []).find(c => c.id === e.target.value)
                          onNewClientDataChange?.({
                            ...newClientData,
                            referrerId: e.target.value,
                            referrerName: selectedReferrer?.name || ""
                          })
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">无推荐人</option>
                        {(existingClients || []).map((client: any) => (
                          <option key={client.id} value={client.id}>
                            {client.name} - {client.phone}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">推荐人姓名</label>
                      <input
                        type="text"
                        value={newClientData?.referrerName || ''}
                        onChange={(e) => onNewClientDataChange?.({...newClientData, referrerName: e.target.value})}
                        placeholder="推荐人姓名"
                        disabled={!!newClientData?.referrerId}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                      />
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* 多项目选择控制 */}
      {projects.length > 1 && onToggleProjectSelection && (
        <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium">项目选择:</span>
            <span className="text-sm text-gray-600">
              已选择 {selectedProjectIds.length} / {projects.length} 个项目
            </span>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={onSelectAllProjects}>
              全选
            </Button>
            <Button variant="outline" size="sm" onClick={onDeselectAllProjects}>
              取消全选
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setGlobalBatchPriceModal(true)}
              className="bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
            >
              全局批量设价
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setGlobalBatchVentTypeModal(true)}
              className="bg-green-50 hover:bg-green-100 border-green-300 text-green-700"
            >
              批量修改类型
            </Button>
          </div>
        </div>
      )}

      {/* 单项目时也显示全局批量操作 */}
      {projects.length === 1 && (
        <div className="flex justify-end bg-gray-50 p-4 rounded-lg space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setGlobalBatchPriceModal(true)}
            className="bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
          >
            全局批量设价
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setGlobalBatchVentTypeModal(true)}
            className="bg-green-50 hover:bg-green-100 border-green-300 text-green-700"
          >
            批量修改类型
          </Button>
        </div>
      )}

      {/* 项目列表 */}
      <div className="space-y-4">
        {projects.map((project, projectIndex) => (
          <Card key={project.id} className={`border-2 ${
            projects.length > 1 && selectedProjectIds.includes(project.id)
              ? 'border-blue-300 bg-blue-50'
              : 'border-gray-200'
          }`}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* 多项目模式下显示选择框 */}
                  {projects.length > 1 && onToggleProjectSelection && (
                    <input
                      type="checkbox"
                      checked={selectedProjectIds.includes(project.id)}
                      onChange={() => onToggleProjectSelection(project.id)}
                      className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                    />
                  )}
                  <CardTitle className="text-lg">
                    项目 {projectIndex + 1}
                    {hasValidationErrors(project) && (
                      <AlertTriangle className="h-5 w-5 text-red-500 ml-2 inline" />
                    )}
                  </CardTitle>
                  {project.hasChanges && (
                    <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded">
                      已修改
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onResetItem(project.id)}
                    title="重置项目数据"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {/* 项目基本信息编辑 */}
              <div className="grid grid-cols-2 gap-4 mt-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">项目地址</label>
                  <Input
                    value={project.projectAddress}
                    onChange={(e) => onProjectUpdate(project.id, { 
                      projectAddress: e.target.value,
                      hasChanges: true 
                    })}
                    placeholder="请输入项目地址"
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">订货日期</label>
                  <Input
                    value={project.orderDate}
                    onChange={(e) => onProjectUpdate(project.id, { 
                      orderDate: e.target.value,
                      hasChanges: true 
                    })}
                    placeholder="请输入订货日期"
                    className="mt-1"
                  />
                </div>
              </div>
            </CardHeader>

            <CardContent>
              {/* 楼层列表 */}
              <div className="space-y-4">
                {project.floors.map((floor, floorIndex) => (
                  <div key={floor.id} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <h4 className="font-medium">{floor.floorName}</h4>
                        <span className="text-sm text-gray-600">
                          {floor.vents.length} 种风口，
                          {floor.vents.reduce((sum, vent) => sum + vent.quantity, 0)} 个
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setBatchPriceModal({ projectId: project.id, floorId: floor.id })}
                          title="批量设置单价"
                        >
                          批量设价
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onAddVent(project.id, floor.id)}
                          title="添加风口"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* 风口表格 */}
                    <div className="bg-white rounded border">
                      {/* 表头 */}
                      <div className="grid grid-cols-10 gap-2 p-3 bg-gray-100 text-sm font-medium border-b">
                        <div>选择</div>
                        <div>类型</div>
                        <div>长度(mm)</div>
                        <div>宽度(mm)</div>
                        <div>数量</div>
                        <div>单价</div>
                        <div>小计</div>
                        <div>备注</div>
                        <div>状态</div>
                        <div>操作</div>
                      </div>

                      {/* 风口行 */}
                      {floor.vents.map((vent) => {
                        const uniqueId = getVentUniqueId(project.id, floor.id, vent.id)
                        const isSelected = selectedVentIds.has(uniqueId)
                        return (
                          <div key={vent.id} className={`grid grid-cols-10 gap-2 p-3 border-b transition-all duration-200 ${
                            isSelected
                              ? 'bg-blue-50 border-blue-300 shadow-sm'
                              : 'hover:bg-gray-50 hover:border-gray-300'
                          }`}>
                            {/* 选择复选框 */}
                            <div className="flex items-center justify-center">
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={() => handleVentSelection(project.id, floor.id, vent.id)}
                                className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                              />
                            </div>

                            {/* 类型 */}
                            <div>
                            <select
                              value={vent.type}
                              onChange={(e) => updateVent(project.id, floor.id, vent.id, { type: e.target.value as any })}
                              className="w-full px-2 py-1 text-xs border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                              {VENT_TYPE_OPTIONS.map(option => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          </div>

                          {/* 长度 */}
                          <div>
                            <Input
                              type="number"
                              value={vent.length}
                              onChange={(e) => updateVent(project.id, floor.id, vent.id, { length: Number(e.target.value) })}
                              onBlur={(e) => {
                                const newLength = Number(e.target.value)
                                if (newLength > 0 && vent.width > 0) {
                                  console.log(`🔍 长度输入完成: ${newLength}×${vent.width}`)
                                  updateVentWithSmartDimensions(project.id, floor.id, vent.id, newLength, vent.width)
                                }
                              }}
                              className="text-xs"
                              min="10"
                            />
                          </div>

                          {/* 宽度 */}
                          <div>
                            <Input
                              type="number"
                              value={vent.width}
                              onChange={(e) => updateVent(project.id, floor.id, vent.id, { width: Number(e.target.value) })}
                              onBlur={(e) => {
                                const newWidth = Number(e.target.value)
                                if (vent.length > 0 && newWidth > 0) {
                                  console.log(`🔍 宽度输入完成: ${vent.length}×${newWidth}`)
                                  updateVentWithSmartDimensions(project.id, floor.id, vent.id, vent.length, newWidth)
                                }
                              }}
                              className="text-xs"
                              min="10"
                            />
                          </div>

                          {/* 数量 */}
                          <div>
                            <Input
                              type="number"
                              value={vent.quantity}
                              onChange={(e) => updateVent(project.id, floor.id, vent.id, { quantity: Number(e.target.value) })}
                              className="text-xs"
                              min="1"
                            />
                          </div>

                          {/* 单价 */}
                          <div>
                            <Input
                              type="number"
                              value={vent.unitPrice}
                              onChange={(e) => updateVent(project.id, floor.id, vent.id, { unitPrice: Number(e.target.value) })}
                              className="text-xs"
                              min="0"
                              step="0.01"
                            />
                            <div className="text-xs text-gray-500 mt-1">
                              {getVentTypeUnit(vent.type)}
                            </div>
                          </div>

                          {/* 小计 */}
                          <div className="flex items-center">
                            <span className="text-sm font-mono font-bold text-blue-600">
                              ¥{vent.totalPrice.toFixed(1)}
                            </span>
                          </div>

                          {/* 备注 */}
                          <div>
                            <Input
                              value={vent.notes || ''}
                              onChange={(e) => updateVent(project.id, floor.id, vent.id, { notes: e.target.value })}
                              placeholder="备注"
                              className="text-xs"
                            />
                          </div>

                          {/* 状态 */}
                          <div className="flex items-center">
                            {vent.validationErrors && vent.validationErrors.length > 0 ? (
                              <AlertTriangle className="h-4 w-4 text-red-500" title="有验证错误" />
                            ) : vent.hasChanges ? (
                              <Edit3 className="h-4 w-4 text-orange-500" title="已修改" />
                            ) : (
                              <CheckCircle className="h-4 w-4 text-green-500" title="正常" />
                            )}
                          </div>

                          {/* 操作 */}
                          <div className="flex space-x-1">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onDuplicateVent(project.id, floor.id, vent.id)}
                              title="复制"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onRemoveVent(project.id, floor.id, vent.id)}
                              title="删除"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        )
                      })}
                    </div>

                    {/* 楼层小计 */}
                    <div className="flex justify-end mt-3">
                      <div className="text-sm">
                        楼层小计: <span className="font-bold text-blue-600">
                          ¥{floor.vents.reduce((sum, vent) => sum + vent.totalPrice, 0).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 项目总计 */}
              <div className="flex justify-end mt-4 pt-4 border-t">
                <div className="text-lg">
                  项目总计: <span className="font-bold text-green-600">
                    ¥{calculateProjectTotal(project).toFixed(2)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 批量设价模态框 */}
      {batchPriceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">批量设置单价</h3>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">单价 (元)</label>
              <Input
                type="number"
                value={batchPrice}
                onChange={(e) => setBatchPrice(Number(e.target.value))}
                min="0"
                step="0.01"
                placeholder="请输入单价"
              />
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={() => setBatchPriceModal(null)}>
                取消
              </Button>
              <Button onClick={handleBatchSetPrice}>
                确认设置
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 全局批量设价模态框 */}
      {globalBatchPriceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">全局批量设置单价</h3>
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-3">
                将为所有项目的所有楼层的所有风口设置统一单价
              </p>
              <label className="block text-sm font-medium mb-2">单价 (元)</label>
              <Input
                type="number"
                value={globalBatchPrice}
                onChange={(e) => setGlobalBatchPrice(Number(e.target.value))}
                min="0"
                step="0.01"
                placeholder="请输入单价"
              />
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={() => setGlobalBatchPriceModal(false)}>
                取消
              </Button>
              <Button onClick={handleGlobalBatchSetPrice} className="bg-blue-600 hover:bg-blue-700">
                确认设置
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 全局批量修改风口类型模态框 */}
      {globalBatchVentTypeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">批量修改风口类型</h3>
            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-4">
                将为所有项目的所有楼层批量修改风口类型。系统会自动识别出风口和回风口。
              </p>

              {/* 出风口类型选择 */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">出风口类型</label>
                <select
                  value={globalBatchOutletType}
                  onChange={(e) => setGlobalBatchOutletType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择出风口类型（可选）</option>
                  <optgroup label="常规出风口 (元/㎡)">
                    <option value="double_white_outlet">双层白色出风口</option>
                    <option value="double_black_outlet">双层黑色出风口</option>
                    <option value="white_black_bottom_outlet">白色黑底出风口</option>
                    <option value="white_linear">白色线型风口</option>
                    <option value="black_linear">黑色线型风口</option>
                    <option value="maintenance">检修口</option>
                  </optgroup>
                  <optgroup label="高端出风口 (元/m)">
                    <option value="arrow_outlet">箭型出风口</option>
                    <option value="claw_outlet">爪型出风口</option>
                    <option value="black_white_outlet">黑白双色出风口</option>
                    <option value="wood_outlet">木纹出风口</option>
                    <option value="white_putty_outlet">白色腻子粉出风口</option>
                    <option value="black_putty_outlet">黑色腻子粉出风口</option>
                    <option value="white_gypsum_outlet">白色石膏板出风口</option>
                    <option value="black_gypsum_outlet">黑色石膏板出风口</option>
                  </optgroup>
                </select>
              </div>

              {/* 回风口类型选择 */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">回风口类型</label>
                <select
                  value={globalBatchReturnType}
                  onChange={(e) => setGlobalBatchReturnType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择回风口类型（可选）</option>
                  <optgroup label="常规回风口 (元/㎡)">
                    <option value="white_black_bottom_return">白色黑底回风口</option>
                    <option value="white_return">白色回风口</option>
                    <option value="black_return">黑色回风口</option>
                    <option value="white_linear_return">白色线型回风口</option>
                    <option value="black_linear_return">黑色线型回风口</option>
                  </optgroup>
                  <optgroup label="高端回风口 (元/m)">
                    <option value="arrow_return">箭型回风口</option>
                    <option value="claw_return">爪型回风口</option>
                    <option value="black_white_return">黑白双色回风口</option>
                    <option value="wood_return">木纹回风口</option>
                    <option value="white_putty_return">白色腻子粉回风口</option>
                    <option value="black_putty_return">黑色腻子粉回风口</option>
                    <option value="white_gypsum_return">白色石膏板回风口</option>
                    <option value="black_gypsum_return">黑色石膏板回风口</option>
                  </optgroup>
                </select>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button variant="outline" onClick={() => setGlobalBatchVentTypeModal(false)}>
                取消
              </Button>
              <Button onClick={handleGlobalBatchSetVentType} className="bg-green-600 hover:bg-green-700">
                确认修改
              </Button>
            </div>
          </div>
        </div>
      )}
      </div>

      {/* 批量备注弹窗 */}
      {showBatchNotesModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">批量添加备注</h3>
            <p className="text-gray-600 mb-4">
              将为 <span className="font-medium text-blue-600">{selectedVentIds.size}</span> 个风口添加备注
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
              <p className="text-sm text-blue-700">
                💡 <strong>提示:</strong> 支持覆盖或追加模式，可使用常用备注快捷按钮快速输入
              </p>
            </div>

            {/* 备注模式选择 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">添加模式</label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="overwrite"
                    checked={batchNotesMode === 'overwrite'}
                    onChange={(e) => setBatchNotesMode(e.target.value as 'overwrite' | 'append')}
                    className="mr-2"
                  />
                  覆盖现有备注
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="append"
                    checked={batchNotesMode === 'append'}
                    onChange={(e) => setBatchNotesMode(e.target.value as 'overwrite' | 'append')}
                    className="mr-2"
                  />
                  追加到现有备注
                </label>
              </div>
            </div>

            {/* 备注内容输入 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">备注内容</label>
              <textarea
                value={batchNotesText}
                onChange={(e) => setBatchNotesText(e.target.value)}
                placeholder="请输入备注内容..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                autoFocus
              />
            </div>

            {/* 常用备注快捷按钮 */}
            <div className="mb-4">
              <p className="text-sm font-medium mb-2">常用备注:</p>
              <div className="flex flex-wrap gap-2">
                {['主卧', '次卧', '客厅', '厨房', '卫生间', '走廊', '阳台', '书房'].map(note => (
                  <button
                    key={note}
                    onClick={() => setBatchNotesText(note)}
                    className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded border"
                  >
                    {note}
                  </button>
                ))}
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowBatchNotesModal(false)
                  setBatchNotesText('')
                }}
              >
                取消
              </Button>
              <Button onClick={confirmBatchAddNotes}>
                确认添加
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
