# 🤖 智能图片识别系统使用指南

## 概述

智能图片识别系统是一个统一的图像识别解决方案，融合了文字识别和表格识别功能，能够智能判断图片内容类型并选择最适合的识别方式。

## 主要特性

### 🎯 智能图片识别模式
- **自动模式**：系统自动检测图片内容，智能选择文字识别或表格识别
- **文字识别**：专门识别文档、截图、手写内容等文字信息
- **表格识别**：专门识别表格结构，保持行列关系

### 🔧 高级设置
- **识别质量**：
  - 高精度模式（推荐）：使用百度OCR高精度API，识别准确率更高
  - 标准模式：使用标准API，速度更快
- **批量处理**：支持同时上传多张图片进行批量识别
- **预览功能**：可选择是否显示图片预览

### 📊 支持的文件格式
- JPG/JPEG
- PNG
- BMP
- GIF
- 单个文件大小限制：4MB
- 最多同时处理：5张图片

## 使用方法

### 1. 打开智能图片识别
在订单创建页面点击 **🤖 智能图片识别** 按钮

### 2. 配置识别设置
- **识别模式**：选择自动、文字或表格识别
- **识别质量**：选择高精度或标准模式
- **显示预览**：开启/关闭图片预览功能

### 3. 上传图片
- **拖拽上传**：直接将图片拖拽到上传区域
- **点击上传**：点击"选择图片"按钮选择文件
- **批量上传**：可同时选择多张图片

### 4. 开始识别
点击 **🤖 开始智能图片识别** 按钮，系统将：
1. 自动检测每张图片的内容类型
2. 选择最适合的识别API
3. 批量处理所有图片
4. 合并识别结果

### 5. 查看结果
- 文字识别结果将自动填入文本框
- 表格识别结果将转换为订单数据
- 系统会显示识别统计信息

## 最佳实践

### 📸 图片质量要求
1. **清晰度**：确保图片清晰，文字可读
2. **光线**：避免过暗或过亮的图片
3. **角度**：尽量保持图片水平，避免倾斜
4. **完整性**：确保文字或表格内容完整显示

### 📝 文字识别优化
1. **格式规范**：
   - 项目名称 + 风口规格
   - 例：`粤桂苑A 回风1300×300 出风1300×160`
2. **分行清晰**：每个项目占一行
3. **信息完整**：包含项目地址、风口类型、尺寸信息

### 📊 表格识别优化
1. **表格结构**：
   - 清晰的表格线条
   - 规整的行列结构
   - 表头信息完整
2. **内容格式**：
   - 项目名称列
   - 风口类型列
   - 尺寸规格列
   - 数量信息列

## 技术实现

### API架构
```
智能OCR系统
├── 统一入口：/api/ocr/intelligent
├── 内容检测：自动判断图片类型
├── 文字识别：百度OCR通用文字识别API
├── 表格识别：百度OCR表格识别API
└── 结果合并：统一返回格式
```

### 识别流程
1. **预处理**：文件验证、格式转换
2. **智能检测**：分析图片内容特征
3. **API调用**：选择合适的百度OCR接口
4. **结果处理**：格式化识别结果
5. **数据合并**：整合多张图片的识别结果

### 错误处理
- API调用失败自动重试
- 详细的错误信息提示
- 部分失败时显示成功统计
- 支持单独重新识别失败的图片

## 常见问题

### Q: 为什么识别结果不准确？
A: 请检查：
- 图片清晰度是否足够
- 文字是否完整显示
- 是否选择了正确的识别模式
- 建议使用高精度模式

### Q: 表格识别失败怎么办？
A: 请确保：
- 表格有清晰的边框线
- 表格结构规整
- 内容不要过于复杂
- 可以尝试手动选择表格识别模式

### Q: 支持手写文字吗？
A: 支持，但建议：
- 字迹清晰工整
- 避免连笔字
- 使用高精度识别模式
- 必要时可以分多张图片上传

### Q: 如何提高识别速度？
A: 建议：
- 使用标准识别模式
- 减少单次上传的图片数量
- 确保网络连接稳定
- 避免上传过大的图片文件

## 更新日志

### v1.0.0 (2025-01-12)
- ✨ 首次发布智能OCR识别系统
- 🤖 支持自动内容类型检测
- 📊 融合文字识别和表格识别
- 🔧 提供高级配置选项
- 📱 优化用户界面体验
- 🚀 支持批量图片处理

---

## 技术支持

如有问题或建议，请联系开发团队。

**让甲方满意，提高订单创建效率！** 🎉
