'use client'

import React, { useState } from 'react'
import { getDeepSeekAPI } from '@/lib/services/deepseek-api'
import { getSiliconFlowAPI } from '@/lib/services/siliconflow-api'
import { API_KEYS } from '@/lib/config/api-keys'

interface PerformanceTest {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  status: 'pending' | 'success' | 'error'
  details?: string
}

export default function PerformanceMonitor() {
  const [tests, setTests] = useState<PerformanceTest[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addTest = (name: string) => {
    const test: PerformanceTest = {
      name,
      startTime: performance.now(),
      status: 'pending'
    }
    setTests(prev => [...prev, test])
    return test
  }

  const completeTest = (test: PerformanceTest, status: 'success' | 'error', details?: string) => {
    test.endTime = performance.now()
    test.duration = test.endTime - test.startTime
    test.status = status
    test.details = details
    setTests(prev => [...prev])
  }

  const runPerformanceTest = async () => {
    setIsRunning(true)
    setTests([])

    try {
      // 测试1: API初始化
      const initTest = addTest('API初始化')
      const apiKey = 'sk-c9047196d51d4f6c99dc94f9fbef602c'
      const deepSeek = getDeepSeekAPI(apiKey)
      completeTest(initTest, 'success', 'API实例创建完成')

      // 测试2: 简单文本处理
      const textTest = addTest('文本预处理')
      const testText = `项目：测试项目
一楼：
出风口 2665×155 白色 1个
回风口 1200×300 白色 1个`
      completeTest(textTest, 'success', `文本长度: ${testText.length} 字符`)

      // 测试3: DeepSeek API调用
      const deepSeekTest = addTest('DeepSeek API调用')
      try {
        const result = await deepSeek.analyzeVentOrder(testText, 'deepseek-chat', true)
        completeTest(deepSeekTest, 'success', `识别到 ${result.projects?.[0]?.floors?.[0]?.rooms?.[0]?.vents?.length || 0} 个风口`)
      } catch (error) {
        completeTest(deepSeekTest, 'error', error instanceof Error ? error.message : '未知错误')
      }

      // 测试4: 硅基流动API调用
      const siliconFlowTest = addTest('硅基流动 API调用')
      try {
        const siliconFlow = getSiliconFlowAPI(API_KEYS.SILICONFLOW)
        const result = await siliconFlow.analyzeVentOrder(testText)
        completeTest(siliconFlowTest, 'success', `识别到 ${result.projects?.[0]?.floors?.[0]?.rooms?.[0]?.vents?.length || 0} 个风口`)
      } catch (error) {
        completeTest(siliconFlowTest, 'error', error instanceof Error ? error.message : '未知错误')
      }

    } catch (error) {
      console.error('性能测试失败:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const clearTests = () => {
    setTests([])
  }

  const formatDuration = (duration?: number) => {
    if (!duration) return '-'
    if (duration < 1000) return `${duration.toFixed(2)}ms`
    return `${(duration / 1000).toFixed(2)}s`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600'
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return '⏳'
      case 'success': return '✅'
      case 'error': return '❌'
      default: return '❓'
    }
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-4">🔍 AI 性能监控器</h2>
      
      <div className="mb-4">
        <button
          onClick={runPerformanceTest}
          disabled={isRunning}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:bg-gray-400 mr-2"
        >
          {isRunning ? '🔄 测试中...' : '🚀 开始性能测试'}
        </button>
        
        <button
          onClick={clearTests}
          disabled={isRunning}
          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 disabled:bg-gray-400"
        >
          🧹 清空结果
        </button>
      </div>

      {tests.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-lg font-semibold mb-2">测试结果：</h3>
          
          {tests.map((test, index) => (
            <div key={index} className="border rounded p-3 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{getStatusIcon(test.status)}</span>
                  <span className="font-medium">{test.name}</span>
                  <span className={`text-sm ${getStatusColor(test.status)}`}>
                    {test.status === 'pending' ? '进行中...' : test.status}
                  </span>
                </div>
                <div className="text-right">
                  <div className="font-mono text-sm">
                    {formatDuration(test.duration)}
                  </div>
                  {test.duration && test.duration > 10000 && (
                    <div className="text-red-500 text-xs">⚠️ 耗时过长</div>
                  )}
                </div>
              </div>
              
              {test.details && (
                <div className="mt-2 text-sm text-gray-600 bg-white p-2 rounded border">
                  {test.details}
                </div>
              )}
            </div>
          ))}
          
          {tests.length > 0 && tests.every(t => t.status !== 'pending') && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
              <h4 className="font-semibold text-blue-800">📊 性能分析：</h4>
              <div className="text-sm text-blue-700 mt-1">
                总耗时: {formatDuration(tests.reduce((sum, test) => sum + (test.duration || 0), 0))}
                <br />
                最慢环节: {tests.reduce((slowest, test) => 
                  (test.duration || 0) > (slowest.duration || 0) ? test : slowest
                ).name} ({formatDuration(tests.reduce((slowest, test) => 
                  (test.duration || 0) > (slowest.duration || 0) ? test : slowest
                ).duration)})
              </div>
            </div>
          )}
        </div>
      )}

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h4 className="font-semibold text-yellow-800">💡 性能基准：</h4>
        <ul className="text-sm text-yellow-700 mt-2 space-y-1">
          <li>• API初始化: &lt;10ms (正常)</li>
          <li>• 文本预处理: &lt;50ms (正常)</li>
          <li>• DeepSeek API调用: &lt;15秒 (正常), &gt;30秒 (异常)</li>
          <li>• 硅基流动 API调用: &lt;10秒 (优秀), &lt;15秒 (正常), &gt;30秒 (异常)</li>
          <li>• 总耗时: &lt;20秒 (正常), &gt;60秒 (需要优化)</li>
        </ul>
      </div>
    </div>
  )
}
