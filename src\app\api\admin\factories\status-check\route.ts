/**
 * 🇨🇳 风口云平台 - 工厂状态检查API
 * 
 * 提供手动触发工厂状态检查的接口
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth } from '@/lib/middleware/auth'
import { factoryStatusChecker, triggerManualStatusCheck } from '@/lib/tasks/factory-status-checker'

/**
 * 获取工厂状态检查器状态
 */
export const GET = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const taskStatus = factoryStatusChecker.getTaskStatus()
    
    // 获取即将过期的工厂
    const expiringFactories = await factoryStatusChecker.getExpiringFactories(7)
    
    // 获取过期但未暂停的工厂
    const expiredActiveFactories = await factoryStatusChecker.getExpiredActiveFactories()

    return NextResponse.json({
      success: true,
      data: {
        taskStatus,
        expiringFactories: expiringFactories.map(f => ({
          id: f.id,
          name: f.name,
          code: f.code,
          subscriptionEnd: f.subscriptionEnd,
          adminUsers: f.users
        })),
        expiredActiveFactories: expiredActiveFactories.map(f => ({
          id: f.id,
          name: f.name,
          code: f.code,
          subscriptionEnd: f.subscriptionEnd
        }))
      }
    })

  } catch (error) {
    console.error('❌ 获取工厂状态检查器状态失败:', error)
    return NextResponse.json({
      success: false,
      error: '获取状态失败'
    }, { status: 500 })
  }
})

/**
 * 手动触发工厂状态检查
 */
export const POST = withAdminAuth(async (request: NextRequest, user) => {
  try {
    console.log(`🔧 管理员 ${user.username} 手动触发工厂状态检查`)

    const report = await triggerManualStatusCheck()

    return NextResponse.json({
      success: true,
      message: '工厂状态检查完成',
      data: {
        report,
        triggeredBy: user.username,
        triggeredAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('❌ 手动工厂状态检查失败:', error)
    
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    
    return NextResponse.json({
      success: false,
      error: `状态检查失败: ${errorMessage}`
    }, { status: 500 })
  }
})
