import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { safeNumber } from '@/lib/utils/number-utils'

/**
 * 推荐奖励数据同步API
 * 用于修复推荐系统页面和客户查询页面数据不一致的问题
 */

interface SyncResult {
  clientId: string
  clientName: string
  before: {
    referralReward: number
    referralCount: number
  }
  after: {
    referralReward: number
    referralCount: number
  }
  difference: number
  status: 'updated' | 'no_change' | 'error'
  message: string
}

interface SyncSummary {
  totalClients: number
  clientsWithReferrals: number
  clientsUpdated: number
  totalRewardsBefore: number
  totalRewardsAfter: number
  results: SyncResult[]
}

// 简单的奖励计算规则（与系统其他地方保持一致）
const calculateSimpleReward = (orderAmount: number): number => {
  return orderAmount * 0.02 // 2%
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 开始同步推荐奖励数据...')

    // 从请求体中获取工厂ID
    const body = await request.json()
    const factoryId = body.factoryId || getCurrentFactoryId()

    if (!factoryId) {
      return NextResponse.json(
        { error: '无法获取工厂ID，请在请求中提供factoryId' },
        { status: 400 }
      )
    }

    // 获取所有客户和订单
    const clients = await db.getClientsByFactoryId(factoryId)
    const orders = await db.getOrdersByFactoryId(factoryId)

    console.log(`📋 找到 ${clients.length} 个客户，${orders.length} 个订单`)

    const results: SyncResult[] = []
    let totalRewardsBefore = 0
    let totalRewardsAfter = 0
    let clientsUpdated = 0

    // 处理每个有推荐的客户
    for (const client of clients) {
      // 获取该客户推荐的其他客户
      const referredClients = clients.filter((c: unknown) => c.referrerId === client.id)
      
      if (referredClients.length > 0) {
        // 记录修复前的状态
        const beforeReward = safeNumber(client.referralReward || 0)
        const beforeCount = safeNumber(client.referralCount || 0)
        
        totalRewardsBefore += beforeReward

        // 计算实际应得的奖励
        let calculatedReward = 0

        for (const referredClient of referredClients) {
          const clientOrders = orders.filter((order: unknown) => order.clientId === referredClient.id)
          const totalAmount = clientOrders.reduce((sum: unknown, order: unknown) => sum + order.totalAmount, 0)
          const rewardGenerated = calculateSimpleReward(totalAmount)
          calculatedReward += rewardGenerated
        }

        // 四舍五入到分
        calculatedReward = Math.round(calculatedReward * 100) / 100
        totalRewardsAfter += calculatedReward

        const actualReferralCount = referredClients.length
        const difference = Math.abs(calculatedReward - beforeReward)

        try {
          // 只有当数据有差异时才更新
          if (difference > 0.01 || actualReferralCount !== beforeCount) {
            await db.updateClient(client.id, {
              referralReward: calculatedReward,
              availableReward: calculatedReward, // 暂时设为可用，后续可以根据业务规则调整
              pendingReward: 0,
              referralCount: actualReferralCount
            })

            clientsUpdated++

            results.push({
              clientId: client.id,
              clientName: client.name,
              before: {
                referralReward: beforeReward,
                referralCount: beforeCount
              },
              after: {
                referralReward: calculatedReward,
                referralCount: actualReferralCount
              },
              difference,
              status: 'updated',
              message: `奖励已更新: ¥${beforeReward.toFixed(2)} → ¥${calculatedReward.toFixed(2)}, 推荐数: ${beforeCount} → ${actualReferralCount}`
            })

            console.log(`✅ 更新 ${client.name}: 奖励 ¥${beforeReward} → ¥${calculatedReward}, 推荐数 ${beforeCount} → ${actualReferralCount}`)
          } else {
            results.push({
              clientId: client.id,
              clientName: client.name,
              before: {
                referralReward: beforeReward,
                referralCount: beforeCount
              },
              after: {
                referralReward: calculatedReward,
                referralCount: actualReferralCount
              },
              difference: 0,
              status: 'no_change',
              message: '数据已是最新，无需更新'
            })
          }
        } catch (error) {
          console.error(`❌ 更新客户 ${client.name} 失败:`, error)
          results.push({
            clientId: client.id,
            clientName: client.name,
            before: {
              referralReward: beforeReward,
              referralCount: beforeCount
            },
            after: {
              referralReward: beforeReward,
              referralCount: beforeCount
            },
            difference: 0,
            status: 'error',
            message: `更新失败: ${error instanceof Error ? error.message : '未知错误'}`
          })
        }
      }
    }

    const summary: SyncSummary = {
      totalClients: clients.length,
      clientsWithReferrals: results.length,
      clientsUpdated,
      totalRewardsBefore: Math.round(totalRewardsBefore * 100) / 100,
      totalRewardsAfter: Math.round(totalRewardsAfter * 100) / 100,
      results
    }

    console.log('✅ 推荐奖励数据同步完成:', {
      总客户数: summary.totalClients,
      有推荐的客户: summary.clientsWithReferrals,
      已更新客户: summary.clientsUpdated,
      修复前总奖励: summary.totalRewardsBefore,
      修复后总奖励: summary.totalRewardsAfter
    })

    return NextResponse.json({
      success: true,
      message: `成功同步推荐奖励数据，更新了 ${clientsUpdated} 个客户`,
      summary
    })

  } catch (error) {
    console.error('❌ 同步推荐奖励数据失败:', error)
    return NextResponse.json(
      { 
        error: '同步推荐奖励数据失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 检查推荐奖励数据状态...')

    // 从URL参数中获取工厂ID
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId') || getCurrentFactoryId()

    if (!factoryId) {
      return NextResponse.json(
        { error: '无法获取工厂ID，请在URL参数中提供factoryId' },
        { status: 400 }
      )
    }

    // 获取所有客户和订单
    const clients = await db.getClientsByFactoryId(factoryId)
    const orders = await db.getOrdersByFactoryId(factoryId)

    let clientsWithDiscrepancy = 0
    let totalStoredRewards = 0
    let totalCalculatedRewards = 0

    // 检查每个有推荐的客户
    for (const client of clients) {
      const referredClients = clients.filter(c => c.referrerId === client.id)
      
      if (referredClients.length > 0) {
        const storedReward = safeNumber(client.referralReward || 0)
        totalStoredRewards += storedReward

        // 计算实际应得的奖励
        let calculatedReward = 0
        for (const referredClient of referredClients) {
          const clientOrders = orders.filter(order => order.clientId === referredClient.id)
          const totalAmount = clientOrders.reduce((sum, order) => sum + order.totalAmount, 0)
          calculatedReward += calculateSimpleReward(totalAmount)
        }

        calculatedReward = Math.round(calculatedReward * 100) / 100
        totalCalculatedRewards += calculatedReward

        // 检查是否有差异
        if (Math.abs(storedReward - calculatedReward) > 0.01) {
          clientsWithDiscrepancy++
        }
      }
    }

    return NextResponse.json({
      success: true,
      status: {
        totalClients: clients.length,
        clientsWithReferrals: clients.filter(c => clients.some(ref => ref.referrerId === c.id)).length,
        clientsWithDiscrepancy,
        totalStoredRewards: Math.round(totalStoredRewards * 100) / 100,
        totalCalculatedRewards: Math.round(totalCalculatedRewards * 100) / 100,
        needsSync: clientsWithDiscrepancy > 0
      }
    })

  } catch (error) {
    console.error('❌ 检查推荐奖励数据状态失败:', error)
    return NextResponse.json(
      { 
        error: '检查推荐奖励数据状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
