'use client'

import React, { useState, useRef, use<PERSON>allback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Upload, X, FileImage, Grid3X3, Eye, EyeOff, AlertCircle, CheckCircle, Loader2, Settings } from 'lucide-react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import OCRTextEditor from './ocr-text-editor'

// 智能OCR上传组件的类型定义
interface IntelligentOCRUploadProps {
  onTextRecognized?: (text: string, ocrResult?: any) => void
  onClose: () => void
  maxFiles?: number
  acceptedTypes?: string[]
}

// 上传文件的状态类型
interface UploadedFile {
  id: string
  file: File
  preview: string
  status: 'pending' | 'processing' | 'success' | 'error'
  result?: any
  error?: string
  recognitionType?: 'text'
}

// OCR识别模式
type OCRMode = 'auto' | 'text' | 'handwriting'

// OCR识别质量
type OCRQuality = 'standard' | 'accurate'

export function IntelligentOCRUpload({
  onTextRecognized,
  onClose,
  maxFiles = 5,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/bmp', 'image/gif']
}: IntelligentOCRUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [showPreview, setShowPreview] = useState(true)
  const [ocrMode, setOCRMode] = useState<OCRMode>('auto')
  const [ocrQuality, setOCRQuality] = useState<OCRQuality>('accurate')
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // OCR文本编辑器状态
  const [showTextEditor, setShowTextEditor] = useState(false)
  const [currentEditingFile, setCurrentEditingFile] = useState<{
    fileName: string
    originalText: string
    ocrResult: any
  } | null>(null)

  // 生成唯一ID
  const generateId = () => Math.random().toString(36).substr(2, 9)

  // 创建文件预览URL
  const createPreview = (file: File): string => {
    return URL.createObjectURL(file)
  }

  // 清理预览URL
  const cleanupPreview = (preview: string) => {
    URL.revokeObjectURL(preview)
  }

  // 压缩图片
  const compressImage = (file: File, maxWidth: number = 1920, maxHeight: number = 1080, quality: number = 0.8): Promise<File> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // 计算压缩后的尺寸
        let { width, height } = img

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width *= ratio
          height *= ratio
        }

        canvas.width = width
        canvas.height = height

        // 绘制压缩后的图片
        ctx?.drawImage(img, 0, 0, width, height)

        // 转换为Blob
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now()
              })
              console.log(`🗜️ 图片压缩完成: ${file.name}`)
              console.log(`📊 压缩前: ${(file.size / 1024 / 1024).toFixed(2)}MB`)
              console.log(`📊 压缩后: ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`)
              resolve(compressedFile)
            } else {
              reject(new Error('图片压缩失败'))
            }
          },
          file.type,
          quality
        )
      }

      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = URL.createObjectURL(file)
    })
  }

  // 验证文件
  const validateFile = (file: File): string | null => {
    // 检查文件类型
    if (!acceptedTypes.includes(file.type)) {
      return `不支持的文件类型: ${file.type}`
    }

    // 检查文件大小 (10MB限制，压缩后会更小)
    if (file.size > 10 * 1024 * 1024) {
      return '文件大小不能超过10MB'
    }

    return null
  }

  // 处理文件选择
  const handleFiles = useCallback(async (files: FileList | File[]) => {
    const fileArray = Array.from(files)
    const newFiles: UploadedFile[] = []

    for (const file of fileArray) {
      // 检查文件数量限制
      if (uploadedFiles.length + newFiles.length >= maxFiles) {
        alert(`最多只能上传${maxFiles}张图片`)
        break
      }

      // 验证文件
      const error = validateFile(file)
      if (error) {
        alert(`文件 ${file.name}: ${error}`)
        continue
      }

      // 检查是否已存在相同文件
      const isDuplicate = uploadedFiles.some(uf =>
        uf.file.name === file.name && uf.file.size === file.size
      )
      if (isDuplicate) {
        alert(`文件 ${file.name} 已存在`)
        continue
      }

      try {
        // 自动压缩图片以减少文件大小
        let processedFile = file
        if (file.size > 1 * 1024 * 1024) { // 大于1MB的图片进行压缩
          console.log(`🗜️ 开始压缩图片: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`)
          // 根据文件大小选择不同的压缩参数
          if (file.size > 5 * 1024 * 1024) {
            // 大文件使用更激进的压缩
            processedFile = await compressImage(file, 1280, 720, 0.6)
          } else if (file.size > 3 * 1024 * 1024) {
            processedFile = await compressImage(file, 1600, 900, 0.7)
          } else {
            processedFile = await compressImage(file, 1920, 1080, 0.8)
          }
        }

        newFiles.push({
          file: processedFile,
          preview: createPreview(processedFile),
          status: 'pending',
          id: generateId()
        })
      } catch (error) {
        console.error(`❌ 图片处理失败: ${file.name}`, error)
        alert(`图片处理失败: ${file.name}`)
      }
    }

    if (newFiles.length > 0) {
      setUploadedFiles(prev => [...prev, ...newFiles])
    }
  }, [uploadedFiles, maxFiles, acceptedTypes])

  // 拖拽处理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files)
    }
  }, [handleFiles])

  // 文件输入处理
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files)
    }
  }

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  // 移除文件
  const removeFile = (id: string) => {
    setUploadedFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id)
      if (fileToRemove) {
        cleanupPreview(fileToRemove.preview)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  // 智能检测图片内容类型（简化为只支持文字识别）
  const detectContentType = async (file: File): Promise<'text'> => {
    console.log('🔍 检测为文字类型')
    return 'text'
  }

  // 智能OCR识别（带重试机制）
  const performIntelligentOCR = async (file: File, retryCount: number = 0): Promise<any> => {
    console.log(`🤖 开始智能OCR识别: ${file.name} (尝试 ${retryCount + 1}/3)`)

    let recognitionType: 'text' = 'text'

    if (ocrMode === 'auto') {
      // 自动检测内容类型
      recognitionType = await detectContentType(file)
      console.log(`🔍 自动检测结果: ${recognitionType}`)
    } else if (ocrMode === 'handwriting') {
      recognitionType = 'text' // 手写识别归类为文字识别
    } else {
      recognitionType = 'text' // 统一为文字识别
    }

    const formData = new FormData()
    formData.append('files', file)
    formData.append('useAccurate', (ocrQuality === 'accurate').toString())

    try {
      let apiUrl: string
      let result: any

      // 统一使用文字识别API
      apiUrl = '/api/ocr'

      console.log('📝 调用文字识别API...')
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        if (response.status === 413) {
          // 如果是413错误且还有重试机会，尝试更高压缩率
          if (retryCount < 2) {
            console.log(`🗜️ 文件过大，尝试更高压缩率重试...`)
            const compressedFile = await compressImage(file, 1024, 768, 0.5 - retryCount * 0.1)
            return await performIntelligentOCR(compressedFile, retryCount + 1)
          }
          throw new Error(`图片文件过大，即使压缩后仍无法处理，请使用更小的图片`)
        }
        throw new Error(`文字识别API错误: ${response.status}`)
      }

      result = await response.json()
      console.log('🔍 文字API返回结果:', result)

      if (!result.success) {
        throw new Error(result.error || '文字识别失败')
      }

      return {
        success: true,
        type: 'text',
        data: result.data
      }
    } catch (error) {
      // 如果不是413错误或已经重试过，直接抛出错误
      if (error instanceof Error && !error.message.includes('图片文件过大')) {
        console.error('❌ OCR识别失败:', error)
      }
      throw error
    }
  }

  // 处理编辑器确认
  const handleTextEditorConfirm = (editedText: string) => {
    if (currentEditingFile && onTextRecognized) {
      console.log('✅ 用户确认编辑后的文本:', editedText)
      // 调用原始的文字识别回调，传递编辑后的文本
      onTextRecognized(editedText, currentEditingFile.ocrResult)
    }
    setCurrentEditingFile(null)
    setShowTextEditor(false)
  }

  // 处理编辑器取消
  const handleTextEditorCancel = () => {
    console.log('❌ 用户取消了文本编辑')
    // 将当前文件状态重置为pending，允许重新识别
    if (currentEditingFile) {
      setUploadedFiles(prev => prev.map(f =>
        f.file.name === currentEditingFile.fileName ? { ...f, status: 'pending' } : f
      ))
    }
    setCurrentEditingFile(null)
    setShowTextEditor(false)
  }

  // 处理AI识别并创建订单
  const handleAIAnalyze = async (text: string): Promise<any> => {
    try {
      console.log('🤖 开始AI智能识别OCR文本...')

      // 调用AI识别API，使用系统默认的通义千问
      const response = await fetch('/api/ai/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: text,
          provider: 'qwen', // 使用通义千问作为默认
          model: 'qwen-turbo' // 使用通义千问的快速模型
        })
      })

      if (!response.ok) {
        throw new Error(`AI识别请求失败: ${response.status}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'AI识别失败')
      }

      console.log('✅ AI识别完成:', result.data)
      console.log('🔧 使用的AI服务:', result.metadata?.provider)

      // 将AI识别结果转换为订单格式并直接创建订单
      await createOrderFromAIResult(result.data, text)

      return result.data

    } catch (error) {
      console.error('❌ AI识别失败:', error)
      throw error
    }
  }

  // 从AI识别结果创建订单
  const createOrderFromAIResult = async (aiResult: any, originalText: string) => {
    try {
      console.log('🚀 开始从AI识别结果创建订单...')

      // 🔧 修复：直接使用AI结构化数据，不要转换回文本再解析
      if (onTextRecognized) {
        console.log('📝 调用文字识别回调，直接传递AI结构化数据...')

        // 直接传递AI结构化数据，避免格式化后再解析的问题
        onTextRecognized(originalText, aiResult)
      }

    } catch (error) {
      console.error('❌ 从AI结果创建订单失败:', error)
      throw error
    }
  }

  // 将AI结果格式化为适合订单创建的文本格式
  const formatAIResultForOrder = (aiResult: any): string => {
    let formattedText = ''

    // 处理通义千问的projects结构
    if (aiResult.projects && aiResult.projects.length > 0) {
      aiResult.projects.forEach((project: any) => {
        // 添加项目信息
        if (project.projectName) {
          formattedText += `${project.projectName}\n`
        }

        // 处理楼层和风口信息
        if (project.floors && project.floors.length > 0) {
          project.floors.forEach((floor: any) => {
            if (floor.floorName) {
              formattedText += `${floor.floorName}\n`
            }

            // 处理房间和风口
            if (floor.rooms && floor.rooms.length > 0) {
              floor.rooms.forEach((room: any) => {
                if (room.roomName && room.roomName !== '默认房间') {
                  formattedText += `${room.roomName}\n`
                }

                // 处理风口信息
                if (room.vents && room.vents.length > 0) {
                  room.vents.forEach((vent: any) => {
                    if (vent.length && vent.width) {
                      formattedText += `${vent.length}×${vent.width}`
                      if (vent.quantity && vent.quantity > 1) {
                        formattedText += `×${vent.quantity}个`
                      }
                      if (vent.notes) {
                        formattedText += ` ${vent.notes}`
                      }
                      formattedText += '\n'
                    }
                  })
                }
              })
            }
          })
        }
      })
    }

    return formattedText.trim()
  }

  // 开始智能识别
  const startIntelligentRecognition = async () => {
    const pendingFiles = uploadedFiles.filter(f => f.status === 'pending')
    if (pendingFiles.length === 0) {
      alert('没有待识别的文件')
      return
    }

    setIsProcessing(true)

    try {
      console.log(`🚀 开始批量智能图片识别 (${pendingFiles.length}个文件)...`)
      
      for (const fileItem of pendingFiles) {
        try {
          // 更新状态为处理中
          setUploadedFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { ...f, status: 'processing' } : f
          ))

          console.log(`🔍 识别文件: ${fileItem.file.name}`)
          
          const result = await performIntelligentOCR(fileItem.file)
          
          // 更新状态为成功
          setUploadedFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { 
              ...f, 
              status: 'success', 
              result,
              recognitionType: result.type
            } : f
          ))

          console.log(`✅ 文件识别成功: ${fileItem.file.name} (类型: ${result.type})`)

          // 🔧 新流程：显示编辑界面而不是立即处理
          if (result.type === 'text') {
            const resultData = result.data
            console.log('🔍 处理文字识别数据:', resultData)

            // 尝试多种方式获取识别文本
            let recognizedText = ''
            if (resultData?.mergedText) {
              recognizedText = resultData.mergedText
            } else if (resultData?.text) {
              recognizedText = resultData.text
            } else if (resultData?.words_result) {
              recognizedText = resultData.words_result.map((item: any) => item.words).join('\n')
            } else if (resultData?.words) {
              recognizedText = resultData.words.map((item: any) => item.words).join('\n')
            }

            console.log('📝 提取的文字内容:', recognizedText)

            if (recognizedText.trim()) {
              // 🔧 显示编辑界面，让用户确认和修正OCR结果
              setCurrentEditingFile({
                fileName: fileItem.file.name,
                originalText: recognizedText,
                ocrResult: resultData
              })
              setShowTextEditor(true)
              setIsProcessing(false) // 暂停处理，等待用户编辑
              return // 退出循环，等待用户确认
            } else {
              console.warn('⚠️ 识别结果为空')
            }
          }

          // 添加延迟避免API调用过于频繁
          if (pendingFiles.indexOf(fileItem) < pendingFiles.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500))
          }
          
        } catch (error) {
          console.error(`❌ 文件识别失败: ${fileItem.file.name}`, error)
          
          // 更新状态为失败
          setUploadedFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { 
              ...f, 
              status: 'error', 
              error: error instanceof Error ? error.message : '识别失败'
            } : f
          ))
        }
      }

      // 等待状态更新完成后统计结果
      setTimeout(() => {
        setUploadedFiles(currentFiles => {
          const successFiles = currentFiles.filter(f => f.status === 'success' && f.result)
          const failedFiles = currentFiles.filter(f => f.status === 'error')

          console.log(`🎉 批量图片识别完成! 成功: ${successFiles.length}个文件, 失败: ${failedFiles.length}个文件`)
          console.log('📋 成功文件详情:', successFiles)

          // 显示完成提示
          if (successFiles.length > 0) {
            const message = `🎉 智能图片识别完成！\n` +
              `📸 处理图片: ${pendingFiles.length}张\n` +
              `✅ 成功: ${successFiles.length}张\n` +
              `📝 文字识别: ${successFiles.length}张`

            alert(message)
          } else {
            alert('未能识别到有效内容，请检查图片质量或重新上传')
          }

          return currentFiles
        })
      }, 200)

    } catch (error) {
      console.error('❌ 批量识别失败:', error)
      alert(`批量识别失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsProcessing(false)
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <FileImage className="h-4 w-4 text-gray-400" />
    }
  }

  // 获取识别类型图标
  const getRecognitionTypeIcon = (type?: string) => {
    if (type === 'text') {
      return <FileImage className="h-3 w-3 text-green-500" />
    }
    return null
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-bold text-gray-900">
            🤖 智能OCR识别
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-4 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* 智能设置区域 */}
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-700">智能图片识别设置</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                className="h-6 text-xs"
              >
                <Settings className="h-3 w-3 mr-1" />
                {showAdvancedSettings ? '收起' : '展开'}
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ocr-mode" className="text-sm">识别模式</Label>
                <Select value={ocrMode} onValueChange={(value: OCRMode) => setOCRMode(value)}>
                  <SelectTrigger id="ocr-mode">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">🤖 智能自动 (推荐)</SelectItem>
                    <SelectItem value="text">📝 文字识别</SelectItem>
                    <SelectItem value="handwriting">✍️ 手写识别</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="ocr-quality" className="text-sm">识别质量</Label>
                <Select value={ocrQuality} onValueChange={(value: OCRQuality) => setOCRQuality(value)}>
                  <SelectTrigger id="ocr-quality">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="accurate">🎯 高精度 (推荐)</SelectItem>
                    <SelectItem value="standard">⚡ 标准版</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {showAdvancedSettings && (
              <div className="pt-2 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-preview"
                    checked={showPreview}
                    onCheckedChange={setShowPreview}
                  />
                  <Label htmlFor="show-preview" className="text-sm">显示图片预览</Label>
                </div>
              </div>
            )}
          </div>

          {/* 上传区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
              dragActive
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={handleFileSelect}
          >
            <Upload className="h-16 w-16 mx-auto text-blue-500 mb-6" />
            <p className="text-xl font-bold text-gray-800 mb-3">
              点击此处上传图片
            </p>
            <p className="text-lg text-gray-600 mb-4">
              或拖拽图片到此区域
            </p>
            <p className="text-sm text-gray-500 mb-6">
              支持 JPG、PNG、BMP、GIF 格式，单个文件不超过4MB<br/>
              智能图片识别手写图片、截图文件等各种类型
            </p>

            {/* 大号上传按钮 */}
            <Button
              onClick={(e) => {
                e.stopPropagation()
                handleFileSelect()
              }}
              disabled={isProcessing}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-medium"
            >
              <Upload className="h-5 w-5 mr-3" />
              选择图片文件
            </Button>

            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept={acceptedTypes.join(',')}
              onChange={handleFileInputChange}
              className="hidden"
            />
          </div>

          {/* 文件列表 */}
          {uploadedFiles.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-700">
                  已上传文件 ({uploadedFiles.length}/{maxFiles})
                </h3>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPreview(!showPreview)}
                    className="h-6 text-xs"
                  >
                    {showPreview ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 gap-3 max-h-60 overflow-y-auto">
                {uploadedFiles.map((fileItem) => (
                  <div
                    key={fileItem.id}
                    className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
                  >
                    {showPreview && (
                      <img
                        src={fileItem.preview}
                        alt={fileItem.file.name}
                        className="w-12 h-12 object-cover rounded"
                      />
                    )}
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(fileItem.status)}
                        {getRecognitionTypeIcon(fileItem.recognitionType)}
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {fileItem.file.name}
                        </p>
                      </div>
                      <p className="text-xs text-gray-500">
                        {(fileItem.file.size / 1024).toFixed(1)}KB
                        {fileItem.recognitionType && (
                          <span className="ml-2 px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded text-xs">
                            文字
                          </span>
                        )}
                      </p>
                      {fileItem.error && (
                        <p className="text-xs text-red-500 mt-1">{fileItem.error}</p>
                      )}
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(fileItem.id)}
                      className="h-6 w-6 p-0 text-gray-400 hover:text-red-500"
                      disabled={isProcessing}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isProcessing}
            >
              取消
            </Button>
            <Button
              onClick={startIntelligentRecognition}
              disabled={uploadedFiles.length === 0 || isProcessing}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  智能图片识别中...
                </>
              ) : (
                <>
                  🤖 开始智能图片识别
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* OCR文本编辑器 */}
      <OCRTextEditor
        isOpen={showTextEditor}
        onClose={() => setShowTextEditor(false)}
        originalText={currentEditingFile?.originalText || ''}
        fileName={currentEditingFile?.fileName || ''}
        onConfirm={handleTextEditorConfirm}
        onCancel={handleTextEditorCancel}
        onAIAnalyze={handleAIAnalyze}
      />
    </div>
  )
}
