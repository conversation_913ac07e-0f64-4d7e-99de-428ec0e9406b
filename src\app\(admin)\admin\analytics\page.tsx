"use client"

import { useState, use<PERSON>ffect, use<PERSON><PERSON>back } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from "@/lib/database"
import { safeNumber, safeWanYuan, safeLocaleString, safeDivide } from "@/lib/utils/number-utils"
import { getProductTypeName } from "@/lib/pricing"
import * as XLSX from 'xlsx'
import {
  TrendingUp,
  Users,
  ShoppingCart,
  DollarSign,
  Factory,
  Download,
  Target,
  Award,
  AlertCircle,
  Globe
} from "lucide-react"
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts'

// 颜色配置
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

interface PlatformAnalyticsData {
  // 平台总体统计
  totalFactories: number
  activeFactories: number
  totalClients: number
  totalOrders: number
  totalRevenue: number
  
  // 工厂排行
  factoryRankings: Array<{
    name: string
    orders: number
    revenue: number
    clients: number
    growth: number
  }>
  
  // 月度趋势
  monthlyTrends: Array<{
    month: string
    orders: number
    revenue: number
    factories: number
  }>
  
  // 地区分布
  regionStats: Array<{
    region: string
    factories: number
    revenue: number
  }>
  
  // 产品分析（全平台）
  platformProductStats: Array<{
    name: string
    quantity: number
    revenue: number
    factories: number
  }>
}

export default function AdminAnalyticsPage() {
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<PlatformAnalyticsData | null>(null)
  const [timeRange, setTimeRange] = useState("6months")
  const [activeTab, setActiveTab] = useState("overview")

  const loadPlatformAnalytics = useCallback(async () => {
    try {
      setLoading(true)
      console.log('🔄 开始加载平台数据分析...')

      // 获取所有工厂数据
      const factories = await db.getAllFactories()
      const allOrders: unknown[] = []
      const allClients: unknown[] = []

      // 汇总所有工厂的数据
      for (const factory of factories) {
        try {
          const factoryOrders = await db.getOrdersByFactoryId(factory.id)
          const factoryClients = await db.getClientsByFactoryId(factory.id)
          
          allOrders.push(...factoryOrders.map((order: any) => ({ ...order, factoryName: factory.name })))
          allClients.push(...factoryClients.map((client: any) => ({ ...client, factoryName: factory.name })))
        } catch (error) {
          console.warn(`获取工厂 ${factory.name} 数据失败:`, error)
        }
      }

      console.log('📊 平台数据加载完成:', { 
        factories: factories.length, 
        orders: allOrders.length, 
        clients: allClients.length 
      })

      // 处理平台数据
      const analyticsData = processPlatformData(factories, allOrders, allClients)
      setData(analyticsData)

    } catch (error) {
      console.error('❌ 加载平台数据分析失败:', error)
      // 使用模拟数据
      setData(generatePlatformMockData())
    } finally {
      setLoading(false)
    }
  }, [timeRange])

  useEffect(() => {
    loadPlatformAnalytics()
  }, [loadPlatformAnalytics])

  const processPlatformData = (factories: unknown[], orders: unknown[], clients: unknown[]): PlatformAnalyticsData => {
    // 平台总体统计
    const totalFactories = factories.length
    const activeFactories = factories.filter((f: any) => f.isActive !== false).length
    const totalClients = clients.length
    const totalOrders = orders.length
    const totalRevenue: number = (orders as any[]).reduce((sum: number, order: any) => sum + safeNumber(order.totalAmount), 0)

    // 工厂排行
    const factoryRankings = generateFactoryRankings(factories, orders, clients)

    // 月度趋势
    const monthlyTrends = generatePlatformMonthlyTrends(orders, factories)

    // 地区分布
    const regionStats = generateRegionStats(factories, orders)

    // 产品分析
    const platformProductStats = generatePlatformProductStats(orders)

    return {
      totalFactories,
      activeFactories,
      totalClients,
      totalOrders,
      totalRevenue,
      factoryRankings,
      monthlyTrends,
      regionStats,
      platformProductStats
    }
  }

  const generateFactoryRankings = (factories: any[], orders: any[], clients: any[]) => {
    return factories.map((factory: any) => {
      const factoryOrders = orders.filter((order: any) => order.factoryName === factory.name)
      const factoryClients = clients.filter((client: any) => client.factoryName === factory.name)
      const revenue = factoryOrders.reduce((sum: number, order: any) => sum + safeNumber(order.totalAmount), 0)

      return {
        name: factory.name,
        orders: factoryOrders.length,
        revenue: safeNumber(revenue),
        clients: factoryClients.length,
        growth: Math.random() * 30 - 10 // 模拟增长率
      }
    }).sort((a, b) => safeNumber(b.revenue) - safeNumber(a.revenue))
  }

  const generatePlatformMonthlyTrends = (orders: any[], factories: any[]) => {
    const months: { month: string; orders: number; revenue: number; factories: number; }[] = []
    const now = new Date()
    
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
      
      const monthOrders = orders.filter((order: any) => {
        const orderDate = new Date(order.createdAt)
        return orderDate.getFullYear() === date.getFullYear() &&
               orderDate.getMonth() === date.getMonth()
      })
      
      months.push({
        month: `${date.getMonth() + 1}月`,
        orders: monthOrders.length,
        revenue: monthOrders.reduce((sum: number, order: any) => sum + safeNumber(order.totalAmount), 0),
        factories: factories.length
      })
    }
    
    return months
  }

  const generateRegionStats = (factories: any[], orders: any[]) => {
    const regionMap = new Map()

    factories.forEach((factory: any) => {
      const region = factory.address?.split(' ')[0] || '未知地区'
      const factoryOrders = orders.filter((order: any) => order.factoryName === factory.name)
      const revenue = factoryOrders.reduce((sum: number, order: any) => sum + safeNumber(order.totalAmount), 0)

      if (!regionMap.has(region)) {
        regionMap.set(region, { region, factories: 0, revenue: 0 })
      }

      const regionData = regionMap.get(region)
      regionData.factories += 1
      regionData.revenue += safeNumber(revenue)
    })
    
    return Array.from(regionMap.values()).sort((a, b) => b.revenue - a.revenue)
  }

  const generatePlatformProductStats = (orders: any[]) => {
    const productMap = new Map()

    orders.forEach((order: any) => {
      order.items?.forEach((item: any) => {
        // 优先使用统一的产品类型名称转换函数，确保显示中文名称
        let productName = '未知产品'

        if (item.productType) {
          // 使用统一的产品类型名称转换函数
          productName = getProductTypeName(item.productType)
        } else if (item.productName) {
          // 如果没有productType，检查productName是否为英文代码
          const chineseName = getProductTypeName(item.productName)
          productName = chineseName !== item.productName ? chineseName : item.productName
        }

        if (!productMap.has(productName)) {
          productMap.set(productName, { name: productName, quantity: 0, revenue: 0, factories: new Set() })
        }
        const product = productMap.get(productName)
        product.quantity += safeNumber(item.quantity)
        product.revenue += safeNumber(item.totalPrice)
        product.factories.add(order.factoryName)
      })
    })

    return Array.from(productMap.values())
      .map(product => ({
        name: product.name,
        quantity: product.quantity,
        revenue: product.revenue,
        factories: product.factories.size
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10)
  }

  const generatePlatformMockData = (): PlatformAnalyticsData => {
    return {
      totalFactories: 25,
      activeFactories: 23,
      totalClients: 1248,
      totalOrders: 3567,
      totalRevenue: 15680000,
      factoryRankings: [
        { name: '南宁加工厂', orders: 156, revenue: 2850000, clients: 89, growth: 15.2 },
        { name: '广州风口厂', orders: 134, revenue: 2456000, clients: 76, growth: 12.8 },
        { name: '深圳制造厂', orders: 128, revenue: 2234000, clients: 68, growth: 8.5 },
        { name: '上海精工厂', orders: 98, revenue: 1890000, clients: 54, growth: -2.1 },
        { name: '北京暖通厂', orders: 87, revenue: 1567000, clients: 45, growth: 6.3 }
      ],
      monthlyTrends: [
        { month: '8月', orders: 285, revenue: 5680000, factories: 23 },
        { month: '9月', orders: 312, revenue: 6234000, factories: 24 },
        { month: '10月', orders: 356, revenue: 7123000, factories: 24 },
        { month: '11月', orders: 398, revenue: 7890000, factories: 25 },
        { month: '12月', orders: 367, revenue: 7345000, factories: 25 },
        { month: '1月', orders: 289, revenue: 5890000, factories: 25 }
      ],
      regionStats: [
        { region: '广东省', factories: 8, revenue: 6780000 },
        { region: '江苏省', factories: 5, revenue: 4560000 },
        { region: '浙江省', factories: 4, revenue: 3890000 },
        { region: '山东省', factories: 3, revenue: 2340000 },
        { region: '河南省', factories: 2, revenue: 1890000 },
        { region: '其他', factories: 3, revenue: 1220000 }
      ],
      platformProductStats: [
        { name: '方形风口', quantity: 2456, revenue: 8560000, factories: 18 },
        { name: '圆形风口', quantity: 1890, revenue: 6780000, factories: 15 },
        { name: '矩形风口', quantity: 1567, revenue: 5450000, factories: 12 },
        { name: '定制风口', quantity: 890, revenue: 4450000, factories: 8 },
        { name: '百叶风口', quantity: 678, revenue: 2340000, factories: 10 },
        { name: '散流器', quantity: 456, revenue: 1560000, factories: 6 }
      ]
    }
  }

  // 导出平台数据分析报告
  const handleExportPlatformReport = () => {
    if (!data) return

    try {
      const wb = XLSX.utils.book_new()

      // 平台概览工作表
      const overviewData = [
        ['平台数据分析报告', ''],
        ['生成时间', new Date().toLocaleString('zh-CN')],
        ['统计范围', '全平台数据'],
        ['', ''],
        ['平台概况', '数值'],
        ['工厂总数', data.totalFactories],
        ['活跃工厂', data.activeFactories],
        ['客户总数', data.totalClients],
        ['订单总数', data.totalOrders],
        ['平台总营收(元)', data.totalRevenue],
        ['', ''],
        ['工厂排行榜', ''],
        ['工厂名称', '订单数', '营收(元)', '客户数', '增长率(%)'],
        ...data.factoryRankings.map(factory => [
          factory.name,
          factory.orders,
          factory.revenue,
          factory.clients,
          factory.growth.toFixed(1)
        ])
      ]

      const overviewWs = XLSX.utils.aoa_to_sheet(overviewData)
      overviewWs['!cols'] = [{ wch: 20 }, { wch: 15 }, { wch: 20 }, { wch: 15 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(wb, overviewWs, '平台概览')

      // 地区分布工作表
      const regionData = [
        ['地区分布分析', ''],
        ['', ''],
        ['地区', '工厂数量', '营收(元)'],
        ...data.regionStats.map(region => [
          region.region,
          region.factories,
          region.revenue
        ])
      ]

      const regionWs = XLSX.utils.aoa_to_sheet(regionData)
      regionWs['!cols'] = [{ wch: 20 }, { wch: 15 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(wb, regionWs, '地区分布')

      // 产品分析工作表
      const productData = [
        ['平台产品分析', ''],
        ['', ''],
        ['产品名称', '总销量', '总营收(元)', '涉及工厂数'],
        ...data.platformProductStats.map(product => [
          product.name,
          product.quantity,
          product.revenue,
          product.factories
        ])
      ]

      const productWs = XLSX.utils.aoa_to_sheet(productData)
      productWs['!cols'] = [{ wch: 20 }, { wch: 15 }, { wch: 20 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(wb, productWs, '产品分析')

      // 导出Excel文件
      const fileName = `平台数据分析报告_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(wb, fileName)

      console.log('✅ 平台数据分析报告导出成功')
    } catch (error) {
      console.error('❌ 导出平台数据分析报告失败:', error)
      alert('导出失败，请重试')
    }
  }

  if (loading) {
    return (
      <DashboardLayout role="admin">
        <div className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">正在加载平台数据分析...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!data) {
    return (
      <DashboardLayout role="admin">
        <div className="p-8">
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">数据加载失败</h3>
            <p className="text-gray-600">无法加载平台数据分析，请稍后重试</p>
            <Button onClick={loadPlatformAnalytics} className="mt-4">
              重新加载
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout role="admin">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Globe className="h-6 w-6 mr-2 text-red-600" />
              平台数据分析
            </h1>
            <p className="text-gray-600">全平台业务数据洞察与工厂运营分析</p>
          </div>
          <div className="flex items-center space-x-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3months">最近3个月</SelectItem>
                <SelectItem value="6months">最近6个月</SelectItem>
                <SelectItem value="1year">最近1年</SelectItem>
                <SelectItem value="all">全部时间</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleExportPlatformReport} className="border border-gray-300">
              <Download className="h-4 w-4 mr-2" />
              导出报告
            </Button>
          </div>
        </div>

        {/* 平台关键指标 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">工厂总数</CardTitle>
              <Factory className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.totalFactories}</div>
              <p className="text-xs text-muted-foreground">
                活跃: {data.activeFactories} 个
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平台总营收</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{safeWanYuan(data.totalRevenue, 0)}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                较上期增长 18.5%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">订单总数</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.totalOrders}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                较上期增长 12.3%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">客户总数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.totalClients}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                较上期增长 25.8%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均订单价值</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ¥{safeLocaleString(safeDivide(data.totalRevenue, data.totalOrders))}
              </div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                较上期增长 5.2%
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 分析标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">平台概览</TabsTrigger>
            <TabsTrigger value="factories">工厂分析</TabsTrigger>
            <TabsTrigger value="regions">地区分析</TabsTrigger>
            <TabsTrigger value="products">产品分析</TabsTrigger>
          </TabsList>

          {/* 平台概览 */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 平台月度趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle>平台月度趋势</CardTitle>
                  <CardDescription>订单数量和营收变化趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={data.monthlyTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip
                        formatter={(value, name) => [
                          name === 'orders' ? `${value} 个` : `¥${Number(value).toLocaleString()}`,
                          name === 'orders' ? '订单数' : '营收'
                        ]}
                      />
                      <Legend />
                      <Bar yAxisId="left" dataKey="orders" fill="#8884d8" name="订单数" />
                      <Line yAxisId="right" type="monotone" dataKey="revenue" stroke="#82ca9d" name="营收" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 工厂活跃度 */}
              <Card>
                <CardHeader>
                  <CardTitle>工厂活跃度</CardTitle>
                  <CardDescription>活跃工厂数量变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={data.monthlyTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value} 个`, '工厂数']} />
                      <Area type="monotone" dataKey="factories" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 平台运营提醒 */}
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-6">
                <div className="flex items-center space-x-2 text-blue-600">
                  <AlertCircle className="h-5 w-5" />
                  <span className="font-medium">平台运营提醒</span>
                </div>
                <div className="mt-2 text-sm text-blue-600">
                  <p>• 平台整体增长态势良好，订单量和营收均呈上升趋势</p>
                  <p>• 建议关注非活跃工厂，提供运营支持和培训</p>
                  <p>• 可考虑在高增长地区扩展更多工厂</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 工厂分析 */}
          <TabsContent value="factories" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 工厂营收排行 */}
              <Card>
                <CardHeader>
                  <CardTitle>工厂营收排行</CardTitle>
                  <CardDescription>按营收金额排序</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.factoryRankings.slice(0, 8)} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={100} />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']} />
                      <Bar dataKey="revenue" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 工厂增长率分析 */}
              <Card>
                <CardHeader>
                  <CardTitle>工厂增长率分析</CardTitle>
                  <CardDescription>各工厂增长率对比</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.factoryRankings.slice(0, 8)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value}%`, '增长率']} />
                      <Bar dataKey="growth" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 工厂详细排行榜 */}
            <Card>
              <CardHeader>
                <CardTitle>工厂详细排行榜</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">排名</th>
                        <th className="text-left p-2">工厂名称</th>
                        <th className="text-right p-2">订单数量</th>
                        <th className="text-right p-2">营收金额</th>
                        <th className="text-right p-2">客户数量</th>
                        <th className="text-right p-2">增长率</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.factoryRankings.map((factory, index) => (
                        <tr key={factory.name} className="border-b hover:bg-gray-50">
                          <td className="p-2 font-medium flex items-center">
                            {index < 3 && (
                              <Award className={`h-4 w-4 mr-2 ${
                                index === 0 ? 'text-yellow-500' :
                                index === 1 ? 'text-gray-400' : 'text-orange-500'
                              }`} />
                            )}
                            {index + 1}
                          </td>
                          <td className="p-2 font-medium">{factory.name}</td>
                          <td className="p-2 text-right">{factory.orders}</td>
                          <td className="p-2 text-right">¥{factory.revenue.toLocaleString()}</td>
                          <td className="p-2 text-right">{factory.clients}</td>
                          <td className={`p-2 text-right ${factory.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {factory.growth >= 0 ? '+' : ''}{factory.growth.toFixed(1)}%
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 地区分析 */}
          <TabsContent value="regions" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 地区工厂分布 */}
              <Card>
                <CardHeader>
                  <CardTitle>地区工厂分布</CardTitle>
                  <CardDescription>各地区工厂数量</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={data.regionStats}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ region, factories }) => `${region}: ${factories}`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="factories"
                      >
                        {data.regionStats.map((entry, index) => (
                          <Cell key={`region-stats-cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value} 个`, '工厂数']} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 地区营收分布 */}
              <Card>
                <CardHeader>
                  <CardTitle>地区营收分布</CardTitle>
                  <CardDescription>各地区营收金额</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.regionStats}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="region" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']} />
                      <Bar dataKey="revenue" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 地区详细统计 */}
            <Card>
              <CardHeader>
                <CardTitle>地区详细统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">地区</th>
                        <th className="text-right p-2">工厂数量</th>
                        <th className="text-right p-2">营收金额</th>
                        <th className="text-right p-2">平均工厂营收</th>
                        <th className="text-right p-2">营收占比</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.regionStats.map((region, index) => {
                        const avgRevenue = region.revenue / region.factories
                        const percentage = ((region.revenue / data.totalRevenue) * 100).toFixed(1)
                        return (
                          <tr key={region.region} className="border-b hover:bg-gray-50">
                            <td className="p-2 font-medium">{region.region}</td>
                            <td className="p-2 text-right">{region.factories}</td>
                            <td className="p-2 text-right">¥{region.revenue.toLocaleString()}</td>
                            <td className="p-2 text-right">¥{Math.round(avgRevenue).toLocaleString()}</td>
                            <td className="p-2 text-right">{percentage}%</td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 产品分析 */}
          <TabsContent value="products" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 产品销量排行 */}
              <Card>
                <CardHeader>
                  <CardTitle>产品销量排行</CardTitle>
                  <CardDescription>全平台产品销量统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.platformProductStats.slice(0, 6)} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={80} />
                      <Tooltip formatter={(value) => [`${value} 个`, '销量']} />
                      <Bar dataKey="quantity" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 产品营收排行 */}
              <Card>
                <CardHeader>
                  <CardTitle>产品营收排行</CardTitle>
                  <CardDescription>全平台产品营收统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.platformProductStats.slice(0, 6)} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={80} />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']} />
                      <Bar dataKey="revenue" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 产品详细统计表 */}
            <Card>
              <CardHeader>
                <CardTitle>产品详细统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">产品名称</th>
                        <th className="text-right p-2">总销量</th>
                        <th className="text-right p-2">总营收</th>
                        <th className="text-right p-2">涉及工厂</th>
                        <th className="text-right p-2">平均单价</th>
                        <th className="text-right p-2">营收占比</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.platformProductStats.map((product, index) => {
                        const avgPrice = product.revenue / product.quantity
                        const percentage = ((product.revenue / data.totalRevenue) * 100).toFixed(1)
                        return (
                          <tr key={product.name} className="border-b hover:bg-gray-50">
                            <td className="p-2 font-medium">{product.name}</td>
                            <td className="p-2 text-right">{product.quantity}</td>
                            <td className="p-2 text-right">¥{product.revenue.toLocaleString()}</td>
                            <td className="p-2 text-right">{product.factories}</td>
                            <td className="p-2 text-right">¥{Math.round(avgPrice).toLocaleString()}</td>
                            <td className="p-2 text-right">{percentage}%</td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
