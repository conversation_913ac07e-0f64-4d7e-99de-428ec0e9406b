# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

/src/generated/prisma

# Docker
docker-compose.override.yml

# Backup files
*.backup
*.bak
backups/

# Upload files (in production, these should be in external storage)
uploads/
public/uploads/

# SSL certificates
ssl/
*.key
*.crt

# Logs
logs/
*.log

# Database files
*.db
*.sqlite
*.sqlite3

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
Thumbs.db
ehthumbs.db

# Temporary folders
tmp/
temp/

# Production environment file (contains sensitive data)
.env.production
