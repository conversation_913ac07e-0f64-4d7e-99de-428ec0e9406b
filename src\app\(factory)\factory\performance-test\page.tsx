"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { quickPerformanceTest, PerformanceTestResult } from "@/lib/utils/performance-test"
import { deepSeekAPI } from "@/lib/services/deepseek-api"
import { Clock, Zap, Target, CheckCircle, XCircle } from "lucide-react"

export default function PerformanceTestPage() {
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<PerformanceTestResult[]>([])
  const [report, setReport] = useState<string>('')
  const [quickTestResult, setQuickTestResult] = useState<any>(null)

  const handleFullTest = async () => {
    try {
      setTesting(true)
      setResults([])
      setReport('')
      
      console.log('🚀 开始完整性能测试...')
      const reportText = await quickPerformanceTest()
      setReport(reportText)
      
    } catch (error) {
      console.error('❌ 性能测试失败:', error)
      setReport(`测试失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setTesting(false)
    }
  }

  const handleQuickTest = async () => {
    try {
      setTesting(true)
      setQuickTestResult(null)
      
      const testText = "2665×155  1525×255  3805×155"
      
      // 测试快速模式
      const startTime = Date.now()
      const result = await deepSeekAPI.analyzeVentOrder(testText, 'deepseek-chat', true)
      const responseTime = Date.now() - startTime
      
      // 计算风口数量
      let ventCount = 0
      result.projects.forEach(project => {
        project.floors.forEach(floor => {
          floor.rooms.forEach(room => {
            ventCount += room.vents.length
          })
        })
      })
      
      setQuickTestResult({
        responseTime,
        ventCount,
        confidence: result.confidence,
        success: true
      })
      
    } catch (error) {
      console.error('❌ 快速测试失败:', error)
      setQuickTestResult({
        responseTime: 0,
        ventCount: 0,
        confidence: 0,
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      })
    } finally {
      setTesting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">性能测试</h1>
          <p className="text-muted-foreground">
            测试DeepSeek API优化后的性能表现
          </p>
        </div>

        {/* 快速测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              快速测试
            </CardTitle>
            <CardDescription>
              测试简单风口识别的响应速度
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleQuickTest} 
              disabled={testing}
              className="w-full"
            >
              {testing ? '测试中...' : '开始快速测试'}
            </Button>
            
            {quickTestResult && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm font-medium">响应时间</span>
                  </div>
                  <Badge variant={quickTestResult.responseTime < 5000 ? "default" : "destructive"}>
                    {quickTestResult.responseTime}ms
                  </Badge>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Target className="h-4 w-4" />
                    <span className="text-sm font-medium">识别数量</span>
                  </div>
                  <Badge variant="outline">
                    {quickTestResult.ventCount}个风口
                  </Badge>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm font-medium">置信度</span>
                  </div>
                  <Badge variant="secondary">
                    {(quickTestResult.confidence * 100).toFixed(1)}%
                  </Badge>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    {quickTestResult.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-sm font-medium">状态</span>
                  </div>
                  <Badge variant={quickTestResult.success ? "default" : "destructive"}>
                    {quickTestResult.success ? '成功' : '失败'}
                  </Badge>
                </div>
              </div>
            )}
            
            {quickTestResult?.error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{quickTestResult.error}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 完整测试 */}
        <Card>
          <CardHeader>
            <CardTitle>完整性能测试</CardTitle>
            <CardDescription>
              对比快速模式和详细模式的性能差异
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleFullTest} 
              disabled={testing}
              variant="outline"
              className="w-full"
            >
              {testing ? '测试中...' : '开始完整测试'}
            </Button>
            
            {report && (
              <div className="bg-gray-50 p-4 rounded-md">
                <pre className="text-sm whitespace-pre-wrap font-mono">
                  {report}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 优化说明 */}
        <Card>
          <CardHeader>
            <CardTitle>性能优化说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <strong>🚀 快速模式优化：</strong>
                <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                  <li>精简prompt，减少不必要的说明文字</li>
                  <li>降低max_tokens从2000到800</li>
                  <li>降低temperature从0.1到0.01</li>
                  <li>跳过知识库查询和智能prompt生成</li>
                </ul>
              </div>
              
              <div>
                <strong>⚡ 预期性能提升：</strong>
                <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                  <li>响应时间减少50-70%</li>
                  <li>保持识别准确性</li>
                  <li>更适合实时交互场景</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
