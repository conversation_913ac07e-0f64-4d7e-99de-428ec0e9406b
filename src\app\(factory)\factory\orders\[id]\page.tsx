"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button  } from "@/components/ui/button"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from "@/lib/database"
import { getCurrentFactoryId, getCurrentUser } from "@/lib/utils/factory"
import { extractDimensionsFromItem } from "@/lib/utils/dimension-utils"
import { getProductTypeName } from "@/lib/pricing"
import type { Order } from "@/types"
import * as XLSX from 'xlsx'
import { exportPurchaseDetailExcel } from '@/lib/utils/purchase-detail-export'
import {
  ArrowLeft,
  User,
  Phone,
  MapPin,
  Calendar,
  Package,
  Edit,
  Download,
  Printer,
  ShoppingCart
} from "lucide-react"

// 模拟客户数据
const mockClients = [
  {
    id: "client-1",
    name: "上海建筑公司",
    phone: "13800138001",
    email: "<EMAIL>",
    company: "上海建筑有限公司",
    address: "上海市浦东新区张江高科技园区"
  },
  {
    id: "client-2",
    name: "北京装饰工程",
    phone: "13800138002",
    email: "<EMAIL>",
    company: "北京装饰工程有限公司",
    address: "北京市朝阳区CBD商务区"
  },
  {
    id: "client-3",
    name: "广州空调安装",
    phone: "13800138003",
    email: "<EMAIL>",
    company: "广州空调安装服务公司",
    address: "广州市天河区珠江新城"
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'text-yellow-600 bg-yellow-100'
    case 'production': return 'text-blue-600 bg-blue-100'
    case 'completed': return 'text-green-600 bg-green-100'
    case 'cancelled': return 'text-red-600 bg-red-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待确认'
    case 'production': return '生产中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

// 格式化录单员显示
const formatCreatedBy = (order: unknown): string => {
  const name = order.createdByName
  const account = order.createdBy

  // 清理账号ID，提取简短的用户名
  const cleanAccount = (rawAccount: string): string => {
    if (!rawAccount) return ''

    // 处理括号格式：lz001(cmbw1e306001sunaccm8wln16) -> lz001
    if (rawAccount.includes('(')) {
      return rawAccount.split('(')[0]
    }

    // 处理长ID，只保留前面的简短部分
    if (rawAccount.length > 10) {
      // 尝试按分隔符分割
      const parts = rawAccount.split(/[_\-\.]/);
      if (parts.length > 1 && parts[0].length <= 8) {
        return parts[0]
      }
      // 如果没有分隔符，截取前8个字符
      return rawAccount.substring(0, 8)
    }

    return rawAccount
  }

  // 如果有姓名，优先显示姓名
  if (name && name.trim()) {
    // 如果有账号且与姓名不同，显示格式：姓名(账号)
    if (account && account.trim() && account !== name) {
      const shortAccount = cleanAccount(account)
      return shortAccount ? `${name}(${shortAccount})` : name
    }
    return name
  }

  // 如果没有姓名，只显示清理后的账号
  if (account && account.trim()) {
    return cleanAccount(account) || '未知录单员'
  }

  return '未知录单员'
}

export default function OrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [order, setOrder] = useState<Order | null>(null)
  const [client, setClient] = useState<unknown>(null)
  const [loading, setLoading] = useState(true)
  const [isExporting, setIsExporting] = useState(false)
  const [isPrinting, setIsPrinting] = useState(false)
  const [isExportingPurchase, setIsExportingPurchase] = useState(false)
  const [isExportingFactory, setIsExportingFactory] = useState(false)
  const [showExportOptions, setShowExportOptions] = useState(false)
  const [exportOptions, setExportOptions] = useState({
    includeNotes: true,
    includeBasicInfo: true
  })

  useEffect(() => {
    loadOrderDetail()
  }, [params.id])

  const loadOrderDetail = async () => {
    try {
      setLoading(true)
      console.log('🔄 开始加载订单详情...', params.id)

      const factoryId = getCurrentFactoryId()

      if (factoryId && db && typeof db.getOrdersByFactoryId === 'function') {
        // 从数据库获取订单
        const orders = await db.getOrdersByFactoryId(factoryId)
        const foundOrder = orders.find(o => o.id === params.id)

        if (foundOrder) {
          setOrder(foundOrder)

          // 如果需要更详细的客户信息（如邮箱、地址等），才查找客户信息
          if (foundOrder.clientId && foundOrder.clientId !== 'new') {
            try {
              console.log('🔄 获取客户详细信息:', foundOrder.clientId)
              const allClients = await db.getClientsByFactoryId(factoryId)
              const foundClient = allClients.find(c => c.id === foundOrder.clientId)
              setClient(foundClient)
              console.log('✅ 客户详细信息获取成功:', foundClient?.name || '未找到')
            } catch (error) {
              console.error('❌ 获取客户详细信息失败:', error)
            }
          }
        }
      } else {
        // 使用模拟数据
        console.warn('⚠️ 数据库服务不可用，使用模拟数据')
        // 这里可以添加模拟订单数据的查找逻辑
      }
    } catch (error) {
      console.error('❌ 加载订单详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 编辑订单功能
  const handleEditOrder = () => {
    router.push(`/factory/orders/edit/${params.id}`)
  }

  // 导出订单功能为Excel（美化版本）
  const handleExportOrder = async (options = exportOptions) => {
    if (!order) return

    setIsExporting(true)
    try {
      // 动态导入ExcelJS
      const ExcelJS = await import('exceljs')

      // 创建工作簿
      const workbook = new ExcelJS.Workbook()
      workbook.creator = '风口云平台'
      workbook.created = new Date()

      // 订单详情工作表
      const worksheet = workbook.addWorksheet('订单详情', {
        pageSetup: { paperSize: 9, orientation: 'portrait' }
      })

      // 设置主标题
      worksheet.mergeCells('A1:H1')
      const titleCell = worksheet.getCell('A1')
      titleCell.value = '订单详情报表'
      titleCell.font = { name: '微软雅黑', size: 20, bold: true, color: { argb: 'FF1F4E79' } }
      titleCell.alignment = { horizontal: 'center', vertical: 'middle' }
      titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF2F2F2' } }
      worksheet.getRow(1).height = 35

      // 客户信息区域（紧凑布局，统一边框）
      worksheet.mergeCells('A3:H3')
      const clientTitleCell = worksheet.getCell('A3')
      clientTitleCell.value = '客户信息'
      clientTitleCell.font = { name: '微软雅黑', size: 14, bold: true, color: { argb: 'FF1F4E79' } }
      clientTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } }
      clientTitleCell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
      worksheet.getRow(3).height = 25

      // 客户信息数据行（紧凑排列）
      const clientRow = worksheet.getRow(4)
      const clientData = [
        '客户名称',
        order.clientName || (client ? client.name : '未提供'),
        '联系电话',
        order.clientPhone || (client ? client.phone : '未提供'),
        '项目地址',
        order.projectAddress || '未填写',
        '公司名称',
        (client ? client.company : '') || '未提供'
      ]

      // 设置客户信息行的所有单元格
      clientData.forEach((value, index) => {
        const cell = clientRow.getCell(index + 1)
        cell.value = value

        // 统一字体和边框
        if (index % 2 === 0) {
          // 标签列：粗体
          cell.font = { bold: true, color: { argb: 'FF1F4E79' } }
        } else {
          // 内容列：普通字体
          cell.font = { color: { argb: 'FF333333' } }
        }

        // 统一边框样式
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }

        // 统一背景色
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } }
      })

      clientRow.height = 25

      // 订单明细
      const detailStartRow = 6
      worksheet.mergeCells(`A${detailStartRow}:H${detailStartRow}`)
      const detailTitleCell = worksheet.getCell(`A${detailStartRow}`)
      detailTitleCell.value = '订单明细'
      detailTitleCell.font = { name: '微软雅黑', size: 14, bold: true, color: { argb: 'FF1F4E79' } }
      detailTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } }
      worksheet.getRow(detailStartRow).height = 25

      // 明细表头
      const headerRow = worksheet.getRow(detailStartRow + 1)
      const headers = ['序号', '产品名称', '尺寸(mm)', '楼层', '数量', '单价(元)', '小计(元)', '备注']
      headers.forEach((header, index) => {
        const cell = headerRow.getCell(index + 1)
        cell.value = header
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } }
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
      headerRow.height = 25

      // 明细数据
      order.items.forEach((item, index) => {
        const row = worksheet.getRow(detailStartRow + 2 + index)
        const rowData = [
          index + 1,
          getProductTypeName(item.productType) || item.productName,
          // 去掉尺寸中的"mm"单位
          item.specifications ? item.specifications.replace(/mm/g, '') : '',
          item.floor || '未指定',
          item.quantity,
          item.unitPrice,
          Number(item.totalPrice || 0),
          // 根据选项决定备注内容：显示备注内容或留空
          options.includeNotes ? (item.notes || '') : ''
        ]

        rowData.forEach((data, cellIndex) => {
          const cell = row.getCell(cellIndex + 1)
          cell.value = data
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }

          // 金额列格式化
          if (cellIndex === 5 || cellIndex === 6) {
            cell.numFmt = '#,##0.00'
          }

          // 居中对齐
          if (cellIndex === 0 || cellIndex === 4) {
            cell.alignment = { horizontal: 'center' }
          }
        })

        // 交替行颜色
        if (index % 2 === 1) {
          row.eachCell((cell) => {
            if (!cell.fill || !cell.fill.fgColor) {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
            }
          })
        }
      })

      // 总计行
      const totalRow = worksheet.getRow(detailStartRow + 2 + order.items.length)
      totalRow.getCell(6).value = '总计:'
      totalRow.getCell(6).font = { bold: true, color: { argb: 'FF1F4E79' } }
      totalRow.getCell(6).alignment = { horizontal: 'right' }
      totalRow.getCell(7).value = Number(order.totalAmount || 0)
      totalRow.getCell(7).font = { bold: true, color: { argb: 'FF1F4E79' } }
      totalRow.getCell(7).numFmt = '#,##0.00'
      totalRow.getCell(7).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFEAA7' } }

      // 设置列宽
      worksheet.columns = [
        { width: 8 },  // 序号
        { width: 20 }, // 产品名称
        { width: 25 }, // 规格
        { width: 12 }, // 楼层
        { width: 10 }, // 数量
        { width: 12 }, // 单价
        { width: 12 }, // 小计
        { width: 20 }  // 备注（始终保留列）
      ]

      // 导出Excel文件
      const fileName = `订单详情_${order.orderNumber || order.id}_${new Date().toISOString().split('T')[0]}.xlsx`
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.click()
      window.URL.revokeObjectURL(url)

      console.log('✅ 订单导出成功 (美化Excel格式)')
    } catch (error) {
      console.error('❌ 导出订单失败:', error)
      alert('导出失败，请重试')
    } finally {
      setIsExporting(false)
    }
  }

  // 打印订单功能
  const handlePrintOrder = () => {
    if (!order) return

    setIsPrinting(true)
    try {
      // 创建打印内容
      const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title> </title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 15px;
              line-height: 1.3;
              color: #333;
              /* 确保内容从页面顶部开始 */
              padding-top: 0;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #333;
              padding-bottom: 10px;
              margin-bottom: 15px;
            }
            .header h1 {
              margin: 0;
              font-size: 20px;
              font-weight: bold;
              color: #333;
            }
            .section {
              margin-bottom: 12px;
            }
            .section-title {
              font-size: 13px;
              font-weight: bold;
              color: #333;
              border-bottom: 1px solid #ddd;
              padding-bottom: 3px;
              margin-bottom: 6px;
            }
            .compact-info {
              display: flex;
              gap: 15px;
              flex-wrap: wrap;
              padding: 6px;
              background: #f9f9f9;
              border-radius: 3px;
              font-size: 12px;
            }
            .compact-info-item {
              white-space: nowrap;
            }
            .compact-info-label {
              font-weight: bold;
              color: #666;
              margin-right: 4px;
            }
            .items-table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 8px;
              font-size: 12px;
            }
            .items-table th, .items-table td {
              border: 1px solid #ddd;
              padding: 8px 6px;
              text-align: center;
            }
            .items-table th {
              background-color: #f5f5f5;
              font-weight: bold;
              font-size: 13px;
            }
            .items-table td {
              font-size: 12px;
            }
            /* 调整列宽 */
            .items-table th:nth-child(1), .items-table td:nth-child(1) { width: 8%; }  /* 序号 */
            .items-table th:nth-child(2), .items-table td:nth-child(2) { width: 25%; } /* 产品名称 */
            .items-table th:nth-child(3), .items-table td:nth-child(3) { width: 30%; } /* 规格 */
            .items-table th:nth-child(4), .items-table td:nth-child(4) { width: 12%; } /* 楼层 */
            .items-table th:nth-child(5), .items-table td:nth-child(5) { width: 10%; } /* 数量 */
            .items-table th:nth-child(6), .items-table td:nth-child(6) { width: 15%; } /* 备注 */
            @media print {
              body {
                margin: 10px;
                font-size: 11px;
              }
              .section {
                margin-bottom: 15px;
              }
              .items-table {
                /* 允许表格在页面间分页，避免空白页 */
                page-break-inside: auto;
              }
              .items-table thead {
                /* 确保表头在每页都显示 */
                display: table-header-group;
              }
              .items-table tbody tr {
                /* 避免单行被分页 */
                page-break-inside: avoid;
              }
              .header {
                margin-bottom: 10px;
                /* 确保标题不会单独占一页 */
                page-break-after: avoid;
              }
              /* 优化页面设置，减少边距避免空白页 */
              @page {
                margin: 0.3in 0.5in;
                size: A4;
              }
              /* 隐藏页面URL等信息 */
              body::before,
              body::after {
                display: none !important;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>风口送货单</h1>
          </div>

          <div class="section">
            <div class="section-title">项目信息</div>
            <div class="compact-info">
              <div class="compact-info-item">
                <span class="compact-info-label">项目地址:</span>
                <span>${order.projectAddress || '未填写'}</span>
              </div>
              <div class="compact-info-item">
                <span class="compact-info-label">客户名称:</span>
                <span>${order.clientName || (client ? client.name : '未提供')}</span>
              </div>
              <div class="compact-info-item">
                <span class="compact-info-label">联系电话:</span>
                <span>${order.clientPhone || (client ? client.phone : '未提供')}</span>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">送货清单</div>
            <table class="items-table">
              <thead>
                <tr>
                  <th>序号</th>
                  <th>产品名称</th>
                  <th>规格</th>
                  <th>楼层</th>
                  <th>数量</th>
                  <th>备注</th>
                </tr>
              </thead>
              <tbody>
                ${order.items.map((item, index) => `
                  <tr>
                    <td>${index + 1}</td>
                    <td>${getProductTypeName(item.productType) || item.productName}</td>
                    <td>${item.specifications}</td>
                    <td>${item.floor || '未指定'}</td>
                    <td>${item.quantity}</td>
                    <td>${item.notes || '无'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </body>
        </html>
      `

      // 创建新窗口并打印
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(printContent)
        printWindow.document.close()
        printWindow.focus()

        // 等待内容加载完成后打印
        setTimeout(() => {
          printWindow.print()
          printWindow.close()
        }, 500)
      } else {
        alert('无法打开打印窗口，请检查浏览器设置')
      }

      console.log('✅ 订单打印功能已启动')
    } catch (error) {
      console.error('❌ 打印订单失败:', error)
      alert('打印失败，请重试')
    } finally {
      setIsPrinting(false)
    }
  }

  // 导出采购明细功能
  const handleExportPurchaseDetail = async () => {
    if (!order) return

    setIsExportingPurchase(true)
    try {
      console.log('🔄 开始导出订单采购明细...', order.orderNumber)

      // 生成标题
      const orderDate = new Date(order.createdAt)
      const title = `${orderDate.getFullYear()}年${orderDate.getMonth() + 1}月份风口采购明细`

      // 调用导出函数
      await exportPurchaseDetailExcel({
        title,
        orders: [order],
        clientName: order.clientName || '未知客户'
      })

      console.log('✅ 订单采购明细导出成功')
    } catch (error) {
      console.error('❌ 导出订单采购明细失败:', error)
      alert('导出失败，请重试')
    } finally {
      setIsExportingPurchase(false)
    }
  }

  // 导出加工厂专用格式
  const handleExportFactoryFormat = async () => {
    if (!order) return

    setIsExportingFactory(true)
    try {
      console.log('🔄 开始导出加工厂专用格式...', order.orderNumber)

      // 动态导入ExcelJS
      const ExcelJS = await import('exceljs')

      // 创建工作簿
      const workbook = new ExcelJS.Workbook()
      workbook.creator = '风口云平台'
      workbook.created = new Date()

      // 创建工作表
      const worksheet = workbook.addWorksheet('加工单', {
        pageSetup: {
          paperSize: 9,
          orientation: 'portrait',
          margins: {
            left: 0.5, right: 0.5, top: 0.5, bottom: 0.5,
            header: 0.3, footer: 0.3
          }
        }
      })

      // 分类风口数据
      const outletVents = [] // 出风口
      const returnVents = [] // 回风口

      order.items.forEach((item, index) => {
        // 使用智能尺寸识别获取正确的长度和宽度
        const { length, width } = extractDimensionsFromItem(item)

        // 判断是否为出风口或回风口
        const isReturnVent = item.productType?.includes('return') ||
                            item.productName?.includes('回风') ||
                            (width > 280)

        // 判断是否为线型风口
        const isLinearVent = item.productType?.includes('linear') ||
                            item.productName?.includes('线型') ||
                            item.productName?.includes('线形')

        const ventData = {
          长度: length,
          宽度: width,
          颜色: getVentColor(item.productType || item.productName || ''),
          数量: item.quantity,
          卡座: calculateKazuo(width, isReturnVent, isLinearVent),
          备注: item.notes || ''
        }

        if (isReturnVent) {
          ventData['斜叶片'] = calculateXieYePian(length, isReturnVent, isLinearVent) // 回风口使用斜叶片
          ventData['支撑条'] = '' // 回风口特有
          returnVents.push(ventData)
        } else {
          ventData['线型叶片'] = calculateXieYePian(length, isReturnVent, isLinearVent) // 出风口使用线型叶片
          ventData['弯叶片'] = calculateWanYePian(length, isLinearVent)
          ventData['菱形叶片'] = '' // 出风口特有
          outletVents.push(ventData)
        }
      })

      let currentRow = 1

      // 1. 客户名称
      worksheet.mergeCells(`A${currentRow}:J${currentRow}`)
      const nameCell = worksheet.getCell(`A${currentRow}`)
      nameCell.value = order.clientName || '客户姓名'
      nameCell.font = { size: 18, bold: true }
      nameCell.alignment = { horizontal: 'center', vertical: 'middle' }
      nameCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      }
      worksheet.getRow(currentRow).height = 30
      currentRow++

      // 2. 客户地址
      worksheet.mergeCells(`A${currentRow}:J${currentRow}`)
      const addressCell = worksheet.getCell(`A${currentRow}`)
      addressCell.value = `客户地址：${order.projectAddress || ''}`
      addressCell.font = { size: 14 }
      addressCell.alignment = { horizontal: 'left', vertical: 'middle' }
      addressCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      }
      worksheet.getRow(currentRow).height = 25
      currentRow++

      // 3. 打包数量和日期
      const totalQuantity = order.items.reduce((sum, item) => sum + item.quantity, 0)
      const orderDate = new Date(order.createdAt).toLocaleDateString('zh-CN')

      worksheet.mergeCells(`A${currentRow}:F${currentRow}`)
      const quantityCell = worksheet.getCell(`A${currentRow}`)
      quantityCell.value = `打包数量：${totalQuantity}`
      quantityCell.font = { size: 14 }
      quantityCell.alignment = { horizontal: 'left', vertical: 'middle' }
      quantityCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      }

      worksheet.mergeCells(`G${currentRow}:J${currentRow}`)
      const dateCell = worksheet.getCell(`G${currentRow}`)
      dateCell.value = `日期：${orderDate}`
      dateCell.font = { size: 14 }
      dateCell.alignment = { horizontal: 'right', vertical: 'middle' }
      dateCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      }
      worksheet.getRow(currentRow).height = 25
      currentRow++

      // 4. 回风口标题
      if (returnVents.length > 0) {
        worksheet.mergeCells(`A${currentRow}:J${currentRow}`)
        const returnTitleCell = worksheet.getCell(`A${currentRow}`)
        returnTitleCell.value = '回风口'
        returnTitleCell.font = { size: 16, bold: true }
        returnTitleCell.alignment = { horizontal: 'center', vertical: 'middle' }
        returnTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6E6' } }
        returnTitleCell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        }
        worksheet.getRow(currentRow).height = 28
        currentRow++

        // 回风口表头
        const returnHeaders = ['序号', '长度', '宽度', '颜色', '数量', '卡座', '斜叶片', '支撑条', '', '备注']
        returnHeaders.forEach((header, index) => {
          const cell = worksheet.getCell(currentRow, index + 1)
          cell.value = header
          cell.font = { size: 13, bold: true }
          cell.alignment = { horizontal: 'center', vertical: 'middle' }
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0F0F0' } }
          cell.border = {
            top: { style: 'thin' }, bottom: { style: 'thin' },
            left: { style: 'thin' }, right: { style: 'thin' }
          }
        })
        worksheet.getRow(currentRow).height = 25
        currentRow++

        // 回风口数据
        returnVents.forEach((vent, index) => {
          const values = [index + 1, vent.长度, vent.宽度, vent.颜色, vent.数量, vent.卡座, vent.斜叶片, vent.支撑条, '', vent.备注]
          values.forEach((value, colIndex) => {
            const cell = worksheet.getCell(currentRow, colIndex + 1)
            cell.value = value
            cell.font = { size: 12 }
            cell.alignment = { horizontal: 'center', vertical: 'middle' }
            cell.border = {
              top: { style: 'thin' }, bottom: { style: 'thin' },
              left: { style: 'thin' }, right: { style: 'thin' }
            }
          })
          worksheet.getRow(currentRow).height = 22
          currentRow++
        })

        // 回风口注意事项
        worksheet.mergeCells(`A${currentRow}:J${currentRow}`)
        const returnNotesCell = worksheet.getCell(`A${currentRow}`)
        returnNotesCell.value = '下单注意事项：2.0边框，回风单层，下料扣减：普通风口长宽各扣减10mm，卡座用宽度扣减48mm，斜叶片用长度扣减49mm'
        returnNotesCell.font = { size: 11 }
        returnNotesCell.alignment = { horizontal: 'left', vertical: 'top', wrapText: true }
        returnNotesCell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        }
        worksheet.getRow(currentRow).height = 40 // 增加行高以容纳多行文本
        currentRow++
      }

      // 5. 出风口标题
      if (outletVents.length > 0) {
        worksheet.mergeCells(`A${currentRow}:J${currentRow}`)
        const outletTitleCell = worksheet.getCell(`A${currentRow}`)
        outletTitleCell.value = '出风口'
        outletTitleCell.font = { size: 16, bold: true }
        outletTitleCell.alignment = { horizontal: 'center', vertical: 'middle' }
        outletTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6E6' } }
        outletTitleCell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        }
        worksheet.getRow(currentRow).height = 28
        currentRow++

        // 出风口表头
        const outletHeaders = ['序号', '长度', '宽度', '颜色', '数量', '卡座', '线型叶片', '弯叶片', '菱形叶片', '备注']
        outletHeaders.forEach((header, index) => {
          const cell = worksheet.getCell(currentRow, index + 1)
          cell.value = header
          cell.font = { size: 13, bold: true }
          cell.alignment = { horizontal: 'center', vertical: 'middle' }
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0F0F0' } }
          cell.border = {
            top: { style: 'thin' }, bottom: { style: 'thin' },
            left: { style: 'thin' }, right: { style: 'thin' }
          }
        })
        worksheet.getRow(currentRow).height = 25
        currentRow++

        // 出风口数据
        outletVents.forEach((vent, index) => {
          const values = [index + 1, vent.长度, vent.宽度, vent.颜色, vent.数量, vent.卡座, vent.线型叶片, vent.弯叶片, vent.菱形叶片, vent.备注]
          values.forEach((value, colIndex) => {
            const cell = worksheet.getCell(currentRow, colIndex + 1)
            cell.value = value
            cell.font = { size: 12 }
            cell.alignment = { horizontal: 'center', vertical: 'middle' }
            cell.border = {
              top: { style: 'thin' }, bottom: { style: 'thin' },
              left: { style: 'thin' }, right: { style: 'thin' }
            }
          })
          worksheet.getRow(currentRow).height = 22
          currentRow++
        })

        // 出风口注意事项
        worksheet.mergeCells(`A${currentRow}:J${currentRow}`)
        const outletNotesCell = worksheet.getCell(`A${currentRow}`)
        outletNotesCell.value = '下单注意事项：2.0边框，出风双层，下料扣减：普通风口长宽各扣减10mm，卡座用宽度扣减15mm，叶片用长度扣减17mm'
        outletNotesCell.font = { size: 11 }
        outletNotesCell.alignment = { horizontal: 'left', vertical: 'top', wrapText: true }
        outletNotesCell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        }
        worksheet.getRow(currentRow).height = 40 // 增加行高以容纳多行文本
        currentRow++
      }

      // 设置列宽
      worksheet.getColumn(1).width = 8   // 序号
      worksheet.getColumn(2).width = 10  // 长度
      worksheet.getColumn(3).width = 10  // 宽度
      worksheet.getColumn(4).width = 8   // 颜色
      worksheet.getColumn(5).width = 8   // 数量
      worksheet.getColumn(6).width = 10  // 卡座
      worksheet.getColumn(7).width = 10  // 斜叶片
      worksheet.getColumn(8).width = 10  // 弯叶片/支撑条
      worksheet.getColumn(9).width = 10  // 菱形叶片/空列
      worksheet.getColumn(10).width = 20 // 备注（增加宽度以自适应内容）

      // 导出Excel文件
      const fileName = `加工单_${order.clientName || '客户'}_${order.orderNumber || order.id}_${new Date().toISOString().split('T')[0]}.xlsx`
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.click()
      window.URL.revokeObjectURL(url)

      console.log('✅ 加工厂专用格式导出成功')
    } catch (error) {
      console.error('❌ 导出加工厂专用格式失败:', error)
      alert('导出失败，请重试')
    } finally {
      setIsExportingFactory(false)
    }
  }

  // 获取风口颜色
  const getVentColor = (productType: string): string => {
    const hasBlack = productType.includes('black') || productType.includes('黑')
    const hasWhite = productType.includes('white') || productType.includes('白')

    if (hasBlack && hasWhite) {
      return '黑白'
    } else if (hasBlack) {
      return '黑色'
    } else if (hasWhite) {
      return '白色'
    }
    return '白色' // 默认白色
  }

  // 计算卡座尺寸
  const calculateKazuo = (width: number, isReturn: boolean, isLinear: boolean = false): string => {
    if (width <= 0) return ''

    if (isLinear) {
      // 线型风口
      if (isReturn) {
        // 线型回风卡座：宽度扣减12mm
        return `${width - 12}`
      } else {
        // 线型出风卡座：宽度扣减12mm
        return `${width - 12}`
      }
    } else {
      // 普通风口
      if (isReturn) {
        // 普通回风卡座：宽度扣减48mm
        return `${width - 48}`
      } else {
        // 普通出风卡座：宽度扣减15mm
        return `${width - 15}`
      }
    }
  }

  // 计算斜叶片尺寸
  const calculateXieYePian = (length: number, isReturn: boolean, isLinear: boolean = false): string => {
    if (length <= 0) return ''

    if (isLinear) {
      // 线型风口
      if (isReturn) {
        // 线型回风斜叶片：长度扣减14mm
        return `${length - 14}`
      } else {
        // 线型出风斜叶片：长度扣减15mm
        return `${length - 15}`
      }
    } else {
      // 普通风口
      if (isReturn) {
        // 普通回风斜叶片：长度扣减49mm
        return `${length - 49}`
      } else {
        // 普通出风斜叶片：长度扣减17mm
        return `${length - 17}`
      }
    }
  }

  // 计算弯叶片尺寸（仅出风口）
  const calculateWanYePian = (length: number, isLinear: boolean = false): string => {
    if (length <= 0) return ''

    if (isLinear) {
      // 线型出风弯叶片：长度扣减15mm
      return `${length - 15}`
    } else {
      // 普通出风弯叶片：长度扣减17mm
      return `${length - 17}`
    }
  }

  if (loading) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载订单详情中...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!order) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="text-center py-12">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">订单不存在</h3>
            <p className="text-gray-600 mb-4">未找到指定的订单信息</p>
            <Link href="/factory/orders">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回订单列表
              </Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/factory/orders">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">订单详情</h1>
              <p className="text-gray-600">查看完整的订单和客户信息</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleEditOrder}>
              <Edit className="h-4 w-4 mr-2" />
              编辑订单
            </Button>
            <div className="relative">
              <Button
                variant="outline"
                onClick={() => setShowExportOptions(true)}
                disabled={isExporting}
              >
                <Download className="h-4 w-4 mr-2" />
                {isExporting ? '导出中...' : '导出Excel'}
              </Button>
            </div>
            <Button
              variant="outline"
              onClick={handlePrintOrder}
              disabled={isPrinting}
            >
              <Printer className="h-4 w-4 mr-2" />
              {isPrinting ? '准备打印...' : '打印'}
            </Button>
            <Button
              variant="outline"
              onClick={handleExportPurchaseDetail}
              disabled={isExportingPurchase}
              className="text-orange-600 border-orange-300 hover:bg-orange-50"
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {isExportingPurchase ? '导出中...' : '采购明细'}
            </Button>
            <Button
              variant="outline"
              onClick={handleExportFactoryFormat}
              disabled={isExportingFactory}
              className="text-purple-600 border-purple-300 hover:bg-purple-50"
            >
              <Download className="h-4 w-4 mr-2" />
              {isExportingFactory ? '导出中...' : '加工单'}
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                const user = getCurrentUser()
                const factoryId = getCurrentFactoryId()
                console.log('🔍 当前用户信息:', user)
                console.log('🏭 当前工厂ID:', factoryId)
                console.log('📦 订单录单员信息:', {
                  createdBy: order?.createdBy,
                  createdByName: order?.createdByName,
                  formatted: formatCreatedBy(order)
                })
                alert(`调试信息已输出到控制台\n\n用户姓名: ${user?.name || '未设置'}\n用户名: ${user?.username || '未知'}\n录单员显示: ${formatCreatedBy(order)}`)
              }}
            >
              🐛 调试信息
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 订单基本信息 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>订单信息</CardTitle>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(order.status)}`}>
                    {getStatusText(order.status)}
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">订单号</p>
                    <p className="text-lg font-mono">{order.orderNumber || order.id}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">订单金额</p>
                    <p className="text-lg font-bold text-green-600">¥{order.totalAmount.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">创建时间</p>
                    <p className="text-sm">
                      {(() => {
                        try {
                          const date = order.createdAt instanceof Date ? order.createdAt : new Date(order.createdAt)
                          return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`
                        } catch (error) {
                          return '日期格式错误'
                        }
                      })()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">录单员</p>
                    <p className="text-sm font-medium text-blue-600">{formatCreatedBy(order)}</p>
                  </div>
                  {order.notes && (
                    <div className="md:col-span-2">
                      <p className="text-sm font-medium text-gray-600 mb-1">订单备注</p>
                      <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded">{order.notes}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 订单项目详情 */}
            <Card>
              <CardHeader>
                <CardTitle>订单项目</CardTitle>
                <CardDescription>共 {order.items.length} 种产品，{order.items.reduce((sum, item) => sum + item.quantity, 0)} 件</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{getProductTypeName(item.productType) || item.productName}</h4>
                        <p className="text-sm text-gray-600">规格: {item.specifications}</p>
                        {item.floor && (
                          <p className="text-sm text-gray-600">楼层: {item.floor}</p>
                        )}
                        {item.notes && (
                          <p className="text-sm text-gray-500">备注: {item.notes}</p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="font-medium">数量: {item.quantity}</p>
                        <p className="text-sm text-gray-600">单价: ¥{item.unitPrice}</p>
                        <p className="font-bold text-green-600">小计: ¥{Number(item.totalPrice || 0).toFixed(1)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 客户信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  客户信息
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.clientName ? (
                    // 显示订单中的客户信息
                    <>
                      <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">客户名称</p>
                        <p className="font-medium">{order.clientName}</p>
                      </div>
                      {order.clientPhone && (
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-sm">{order.clientPhone}</span>
                        </div>
                      )}
                    </>
                  ) : client ? (
                    // 显示关联的客户详细信息
                    <>
                      <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">客户名称</p>
                        <p className="font-medium">{client.name}</p>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm">{client.phone}</span>
                      </div>
                      {client.email && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 mb-1">邮箱</p>
                          <p className="text-sm">{client.email}</p>
                        </div>
                      )}
                      {client.company && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 mb-1">公司名称</p>
                          <p className="text-sm">{client.company}</p>
                        </div>
                      )}
                      {client.address && (
                        <div className="flex items-start">
                          <MapPin className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                          <span className="text-sm">{client.address}</span>
                        </div>
                      )}
                    </>
                  ) : (
                    <div>
                      <p className="text-sm text-gray-500">客户ID: {order.clientId}</p>
                      <p className="text-xs text-gray-400">客户详细信息不可用</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 订单统计 */}
            <Card>
              <CardHeader>
                <CardTitle>订单统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">产品种类:</span>
                    <span className="font-medium">{order.items.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">总数量:</span>
                    <span className="font-medium">{order.items.reduce((sum, item) => sum + item.quantity, 0)}</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between">
                      <span className="font-medium">订单总额:</span>
                      <span className="font-bold text-lg text-green-600">¥{order.totalAmount.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 导出选项模态框 */}
      {showExportOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">导出选项</h3>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="includeNotes"
                  checked={exportOptions.includeNotes}
                  onChange={(e) => setExportOptions(prev => ({
                    ...prev,
                    includeNotes: e.target.checked
                  }))}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="includeNotes" className="text-sm font-medium text-gray-700">
                  显示备注内容
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="includeBasicInfo"
                  checked={exportOptions.includeBasicInfo}
                  onChange={(e) => setExportOptions(prev => ({
                    ...prev,
                    includeBasicInfo: e.target.checked
                  }))}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="includeBasicInfo" className="text-sm font-medium text-gray-700">
                  包含基本订单信息
                </label>
              </div>

              <div className="text-xs text-gray-500 mt-2">
                <p>• 显示备注内容：在备注列中显示具体内容，取消则备注列留空</p>
                <p>• 基本订单信息：包含订单号、客户信息等</p>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowExportOptions(false)
                  handleExportOrder(exportOptions)
                }}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                确认导出
              </button>
              <button
                onClick={() => setShowExportOptions(false)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  )
}
