# 🇨🇳 风口云平台 - 本地开发环境指南

## 🎯 问题解决方案

您遇到的问题是**本地开发环境与生产环境数据库配置混乱**导致的。我们已经为您创建了一个完全独立的本地开发环境，使用SQLite数据库，无需安装任何额外的数据库软件。

## ✅ 已完成的配置

### 1. 环境分离
- **生产环境**: 使用PostgreSQL数据库，连接到阿里云服务器
- **本地开发环境**: 使用SQLite数据库，完全独立运行

### 2. 配置文件
- `.env` - 生产环境配置（连接阿里云PostgreSQL）
- `.env.local` - 本地开发环境配置（使用SQLite）
- `prisma/schema.prisma` - 当前使用的schema（已切换为SQLite版本）
- `prisma/schema.production.prisma` - 生产环境schema备份
- `prisma/schema.dev.prisma` - 开发环境schema模板

### 3. 测试数据
已创建完整的测试数据，包括：
- 管理员账户: `admin` / `admin123`
- 工厂用户账户: `lin001` / `lin001`
- 测试工厂: 南宁加工厂
- 测试客户: 张三、李四
- 测试订单和付款记录
- 系统公告
- 轮播图片数据（产品展示、生产流程等）

## 🚀 使用方法

### 启动本地开发环境
```bash
# 1. 启动开发服务器
npm run dev

# 2. 访问应用
# 浏览器打开: http://localhost:3000
```

### 登录测试
- **管理员登录**: 用户名 `admin`，密码 `admin123`
- **工厂用户登录**: 用户名 `lin001`，密码 `lin001`

### 数据库管理
```bash
# 查看数据库（图形界面）
npm run db:studio

# 重置数据库
npm run db:reset

# 推送schema变更
npm run db:push

# 重新生成Prisma客户端
npm run db:generate
```

## 🔄 环境切换

### 切换到生产环境
```bash
# 使用脚本切换
npm run setup:prod

# 或手动切换
node scripts/setup-production.js
```

### 切换回开发环境
```bash
# 使用脚本切换
npm run setup:dev

# 或手动切换
node scripts/setup-local-dev.js
```

## 📁 文件结构

```
factorysystem/
├── .env                          # 生产环境配置
├── .env.local                    # 本地开发环境配置
├── dev.db                        # SQLite数据库文件
├── prisma/
│   ├── schema.prisma            # 当前使用的schema
│   ├── schema.dev.prisma        # 开发环境schema模板
│   └── schema.production.prisma # 生产环境schema备份
└── scripts/
    ├── setup-local-dev.js       # 本地开发环境设置脚本
    ├── setup-production.js      # 生产环境设置脚本
    └── init-test-data.js         # 测试数据初始化脚本
```

## 🛠️ 开发工作流

### 1. 日常开发
```bash
# 启动开发服务器
npm run dev

# 在另一个终端查看数据库
npm run db:studio
```

### 2. 数据库变更
```bash
# 修改 prisma/schema.prisma 后
npm run db:push
npm run db:generate
```

### 3. 重置开发环境
```bash
# 完全重置（清除所有数据）
npm run db:reset

# 重新初始化测试数据
npx dotenv -e .env.local -- node scripts/init-test-data.js

# 重新初始化轮播图片
npx dotenv -e .env.local -- node scripts/init-carousel-images.js
```

### 4. 部署到生产环境
```bash
# 1. 切换到生产环境配置
npm run setup:prod

# 2. 提交代码到Gitee
git add .
git commit -m "更新功能"
git push origin main

# 3. 在服务器上拉取更新
# ssh到服务器后执行:
# cd /opt/factorysystem
# git pull origin main
# npm run build
# pm2 restart factorysystem
```

## 🔍 故障排除

### 问题1: 数据库连接错误
```bash
# 确保使用正确的环境配置
npm run setup:dev

# 重新生成Prisma客户端
npm run db:generate
```

### 问题2: 端口被占用
```bash
# 杀死占用端口的进程
npm run kill-port

# 或使用不同端口
npm run dev:3000
```

### 问题3: 数据混乱
```bash
# 重置开发数据库
npm run db:reset

# 重新初始化测试数据
npx dotenv -e .env.local -- node scripts/init-test-data.js

# 重新初始化轮播图片
npx dotenv -e .env.local -- node scripts/init-carousel-images.js
```

## 💡 重要提示

1. **数据安全**: 本地开发环境的数据完全独立，不会影响生产环境
2. **环境隔离**: 开发和生产环境使用不同的数据库类型和配置
3. **自动备份**: 生产环境schema已自动备份为 `schema.production.prisma`
4. **便捷切换**: 使用npm脚本可以快速在开发和生产环境间切换

## 📞 技术支持

如果遇到问题，请检查：
1. 环境变量配置是否正确
2. 是否在正确的环境模式下运行
3. 数据库文件是否存在且可访问
4. Node.js和npm版本是否兼容

---

**🎉 现在您可以安全地在本地环境进行开发测试，不用担心影响生产环境的数据！**
