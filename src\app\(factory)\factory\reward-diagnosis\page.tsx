'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, Calculator, Users, DollarSign, AlertCircle, CheckCircle, XCircle } from 'lucide-react'
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { safeNumber, safeAmountFormat } from '@/lib/utils/number-utils'
import { calculateTotalReferralReward } from '@/lib/utils/reward-calculator'

interface ClientDiagnosis {
  clientId: string
  clientName: string
  clientPhone: string
  
  // 数据库中的奖励信息
  dbReferralReward: number
  dbAvailableReward: number
  dbPendingReward: number
  dbReferralCount: number
  
  // 实际计算的奖励信息
  calculatedReward: number
  actualReferralCount: number
  
  // 推荐的客户详情
  referredClients: Array<{
    id: string
    name: string
    phone: string
    totalAmount: number
    orderCount: number
    rewardGenerated: number
  }>
  
  // 诊断结果
  hasRewardDiscrepancy: boolean
  hasCountDiscrepancy: boolean
  rewardDifference: number
  countDifference: number
  
  // 状态
  status: 'correct' | 'warning' | 'error'
}

export default function RewardDiagnosisPage() {
  const [loading, setLoading] = useState(false)
  const [diagnoses, setDiagnoses] = useState<ClientDiagnosis[]>([])
  const [summary, setSummary] = useState({
    totalClients: 0,
    clientsWithRewards: 0,
    correctClients: 0,
    warningClients: 0,
    errorClients: 0,
    totalRewardDiscrepancy: 0
  })

  const runDiagnosis = async () => {
    try {
      setLoading(true)
      console.log('🔍 开始奖励系统诊断...')

      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        console.error('❌ 无法获取工厂ID')
        return
      }

      // 获取所有客户和订单
      const clients = await db.getClientsByFactoryId(factoryId)
      const orders = await db.getOrdersByFactoryId(factoryId)

      console.log(`📊 数据统计: ${clients.length} 个客户, ${orders.length} 个订单`)

      const diagnoses: ClientDiagnosis[] = []
      let totalRewardDiscrepancy = 0
      let correctClients = 0
      let warningClients = 0
      let errorClients = 0

      // 找出所有推荐人
      const referrers = clients.filter(client => 
        clients.some(c => c.referrerId === client.id)
      )

      console.log(`👥 找到 ${referrers.length} 个推荐人`)

      for (const referrer of referrers) {
        console.log(`\n🔍 诊断推荐人: ${referrer.name}`)

        // 获取被推荐的客户
        const referredClients = clients.filter(c => c.referrerId === referrer.id)
        console.log(`  推荐了 ${referredClients.length} 个客户`)

        // 计算每个被推荐客户的奖励
        const referredDetails = []
        let totalCalculatedReward = 0

        for (const referred of referredClients) {
          const clientOrders = orders.filter(order => order.clientId === referred.id)
          const totalAmount = clientOrders.reduce((sum, order) => sum + safeNumber(order.totalAmount), 0)
          
          // 使用奖励计算器计算奖励
          const rewardResult = calculateTotalReferralReward(clientOrders, factoryId)
          const rewardGenerated = rewardResult.totalReward

          referredDetails.push({
            id: referred.id,
            name: referred.name,
            phone: referred.phone,
            totalAmount,
            orderCount: clientOrders.length,
            rewardGenerated
          })

          totalCalculatedReward += rewardGenerated
          console.log(`    ${referred.name}: ${clientOrders.length}个订单, ¥${totalAmount.toFixed(2)} → ¥${rewardGenerated.toFixed(2)}`)
        }

        // 从数据库读取的奖励信息
        const dbReferralReward = safeNumber(referrer.referralReward || 0)
        const dbAvailableReward = safeNumber(referrer.availableReward || 0)
        const dbPendingReward = safeNumber(referrer.pendingReward || 0)
        const dbReferralCount = safeNumber(referrer.referralCount || 0)

        // 计算差异
        const rewardDifference = Math.abs(totalCalculatedReward - dbReferralReward)
        const countDifference = Math.abs(referredClients.length - dbReferralCount)

        const hasRewardDiscrepancy = rewardDifference > 0.01 // 允许1分钱的误差
        const hasCountDiscrepancy = countDifference > 0

        // 确定状态
        let status: 'correct' | 'warning' | 'error' = 'correct'
        if (hasRewardDiscrepancy || hasCountDiscrepancy) {
          status = rewardDifference > 10 ? 'error' : 'warning' // 超过10元算严重错误
        }

        if (status === 'correct') correctClients++
        else if (status === 'warning') warningClients++
        else errorClients++

        totalRewardDiscrepancy += rewardDifference

        const diagnosis: ClientDiagnosis = {
          clientId: referrer.id,
          clientName: referrer.name,
          clientPhone: referrer.phone,
          dbReferralReward,
          dbAvailableReward,
          dbPendingReward,
          dbReferralCount,
          calculatedReward: totalCalculatedReward,
          actualReferralCount: referredClients.length,
          referredClients: referredDetails,
          hasRewardDiscrepancy,
          hasCountDiscrepancy,
          rewardDifference,
          countDifference,
          status
        }

        diagnoses.push(diagnosis)

        console.log(`  诊断结果: ${status} (奖励差异: ¥${rewardDifference.toFixed(2)}, 数量差异: ${countDifference})`)
      }

      // 按状态排序，错误的排在前面
      diagnoses.sort((a, b) => {
        const statusOrder = { error: 0, warning: 1, correct: 2 }
        return statusOrder[a.status] - statusOrder[b.status]
      })

      setDiagnoses(diagnoses)
      setSummary({
        totalClients: clients.length,
        clientsWithRewards: referrers.length,
        correctClients,
        warningClients,
        errorClients,
        totalRewardDiscrepancy
      })

      console.log('✅ 奖励系统诊断完成')

    } catch (error) {
      console.error('❌ 奖励系统诊断失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    runDiagnosis()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'correct': return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-600" />
      case 'error': return <XCircle className="h-5 w-5 text-red-600" />
      default: return <AlertCircle className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'correct': return 'bg-green-100 text-green-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">奖励系统诊断</h1>
            <p className="text-gray-600">检查客户奖励计算和显示的准确性</p>
          </div>
          <Button onClick={runDiagnosis} disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                诊断中...
              </>
            ) : (
              <>
                <Calculator className="h-4 w-4 mr-2" />
                重新诊断
              </>
            )}
          </Button>
        </div>

        {/* 诊断摘要 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总客户数</CardTitle>
              <Users className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{summary.totalClients}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">有奖励客户</CardTitle>
              <DollarSign className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{summary.clientsWithRewards}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">正确客户</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{summary.correctClients}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">问题客户</CardTitle>
              <AlertCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{summary.warningClients + summary.errorClients}</div>
            </CardContent>
          </Card>
        </div>

        {/* 详细诊断结果 */}
        <div className="space-y-6">
          {diagnoses.map((diagnosis) => (
            <Card key={diagnosis.clientId} className={`border-l-4 ${
              diagnosis.status === 'error' ? 'border-l-red-500' :
              diagnosis.status === 'warning' ? 'border-l-yellow-500' :
              'border-l-green-500'
            }`}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(diagnosis.status)}
                    <span>{diagnosis.clientName}</span>
                    <Badge className={getStatusColor(diagnosis.status)}>
                      {diagnosis.status === 'correct' ? '正确' :
                       diagnosis.status === 'warning' ? '警告' : '错误'}
                    </Badge>
                  </div>
                  <div className="text-sm text-gray-600">{diagnosis.clientPhone}</div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                  {/* 数据库中的奖励 */}
                  <div>
                    <h4 className="font-semibold mb-2 text-blue-600">数据库奖励</h4>
                    <div className="space-y-1 text-sm">
                      <div>总奖励: ¥{safeAmountFormat(diagnosis.dbReferralReward)}</div>
                      <div>可用奖励: ¥{safeAmountFormat(diagnosis.dbAvailableReward)}</div>
                      <div>待结算: ¥{safeAmountFormat(diagnosis.dbPendingReward)}</div>
                      <div>推荐数量: {diagnosis.dbReferralCount}</div>
                    </div>
                  </div>

                  {/* 计算的奖励 */}
                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">计算奖励</h4>
                    <div className="space-y-1 text-sm">
                      <div>应得奖励: ¥{safeAmountFormat(diagnosis.calculatedReward)}</div>
                      <div>实际推荐: {diagnosis.actualReferralCount}</div>
                    </div>
                  </div>

                  {/* 差异分析 */}
                  <div>
                    <h4 className="font-semibold mb-2 text-orange-600">差异分析</h4>
                    <div className="space-y-1 text-sm">
                      <div className={diagnosis.hasRewardDiscrepancy ? 'text-red-600' : 'text-green-600'}>
                        奖励差异: ¥{safeAmountFormat(diagnosis.rewardDifference)}
                      </div>
                      <div className={diagnosis.hasCountDiscrepancy ? 'text-red-600' : 'text-green-600'}>
                        数量差异: {diagnosis.countDifference}
                      </div>
                    </div>
                  </div>

                  {/* 状态说明 */}
                  <div>
                    <h4 className="font-semibold mb-2 text-purple-600">状态说明</h4>
                    <div className="text-sm">
                      {diagnosis.status === 'correct' && '✅ 奖励计算正确'}
                      {diagnosis.status === 'warning' && '⚠️ 存在轻微差异'}
                      {diagnosis.status === 'error' && '❌ 存在严重错误'}
                    </div>
                  </div>
                </div>

                {/* 推荐客户详情 */}
                {diagnosis.referredClients.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3">推荐客户详情</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {diagnosis.referredClients.map((referred) => (
                        <div key={referred.id} className="bg-gray-50 p-3 rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <div>
                              <div className="font-medium">{referred.name}</div>
                              <div className="text-sm text-gray-600">{referred.phone}</div>
                            </div>
                            <div className="text-right text-sm">
                              <div>{referred.orderCount}个订单</div>
                              <div>¥{safeAmountFormat(referred.totalAmount)}</div>
                            </div>
                          </div>
                          <div className="text-sm font-medium text-green-600">
                            产生奖励: ¥{safeAmountFormat(referred.rewardGenerated)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {diagnoses.length === 0 && !loading && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无奖励数据</h3>
            <p className="text-gray-600">系统中还没有客户推荐奖励数据</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
