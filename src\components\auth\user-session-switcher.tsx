'use client'

/**
 * 🇨🇳 风口云平台 - 用户会话切换器
 * 
 * 功能说明：
 * - 显示当前设备上的所有用户会话
 * - 允许用户快速切换到不同的账号
 * - 显示会话状态和最后活跃时间
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { userSessionManager } from '@/lib/utils/user-session-manager'
import { useAuthStore } from '@/lib/store/auth'

interface UserSessionData {
  userId: string
  username: string
  role: string
  factoryId?: string
  loginTime: number
  lastActiveTime: number
  deviceFingerprint: string
}

export function UserSessionSwitcher() {
  const [sessions, setSessions] = useState<UserSessionData[]>([])
  const [currentSession, setCurrentSession] = useState<UserSessionData | null>(null)
  const [isOpen, setIsOpen] = useState(false)
  const { user, role, logout } = useAuthStore()

  // 刷新会话列表
  const refreshSessions = () => {
    try {
      const allSessions = userSessionManager.getAllUserSessions()
      const current = userSessionManager.getCurrentSession()

      setSessions(allSessions)
      setCurrentSession(current)

      console.log('🔄 会话列表已刷新:', {
        total: allSessions.length,
        current: current?.username
      })
    } catch (error) {
      console.error('❌ 刷新会话列表失败:', error)
    }
  }

  // 切换到指定会话
  const switchToSession = (sessionData: UserSessionData) => {
    try {
      const success = userSessionManager.switchToUserSession(
        sessionData.userId,
        sessionData.role as any
      )

      if (success) {
        // 刷新页面以应用新的会话状态
        window.location.reload()
      } else {
        console.error('❌ 切换会话失败')
      }
    } catch (error) {
      console.error('❌ 切换会话异常:', error)
    }
  }

  // 删除指定会话
  const removeSession = (sessionData: UserSessionData) => {
    try {
      // 如果是当前会话，执行登出
      if (currentSession && sessionData.userId === currentSession.userId) {
        logout()
        return
      }

      // 删除其他会话
      userSessionManager.removeUserSession()
      refreshSessions()

      console.log('✅ 会话已删除:', sessionData.username)
    } catch (error) {
      console.error('❌ 删除会话失败:', error)
    }
  }

  // 格式化时间
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - timestamp

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  }

  // 获取角色显示名称
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'admin': return '管理员'
      case 'factory': return '工厂用户'
      case 'client': return '客户'
      default: return role
    }
  }

  useEffect(() => {
    refreshSessions()

    // 定期刷新会话列表
    const interval = setInterval(refreshSessions, 30000) // 30秒刷新一次

    return () => clearInterval(interval)
  }, [])

  // 如果只有一个会话或没有会话，不显示切换器
  if (sessions.length <= 1) {
    return null
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        className="gap-2"
        onClick={() => setIsOpen(!isOpen)}
      >
        👥 {sessions.length} 个会话
      </Button>

      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <span className="font-medium">用户会话</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={refreshSessions}
                className="h-6 w-6 p-0"
              >
                🔄
              </Button>
            </div>
          </div>

          <div className="max-h-64 overflow-y-auto">
            {sessions.map((session) => {
              const isCurrent = currentSession && session.userId === currentSession.userId

              return (
                <div
                  key={`${session.userId}_${session.role}`}
                  className="flex items-center gap-3 p-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                >
                  <div className="h-8 w-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-medium">
                    {session.username.slice(0, 2).toUpperCase()}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm truncate">
                        {session.username}
                      </span>
                      {isCurrent && (
                        <Badge variant="default" className="text-xs">
                          当前
                        </Badge>
                      )}
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {getRoleDisplayName(session.role)}
                      {session.factoryId && ' • 工厂用户'}
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      活跃: {formatTime(session.lastActiveTime)}
                    </div>
                  </div>

                  <div className="flex flex-col gap-1">
                    {!isCurrent && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => switchToSession(session)}
                        className="h-6 px-2 text-xs"
                      >
                        切换
                      </Button>
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSession(session)}
                      className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
                    >
                      🚪
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>

          <div className="p-3 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
            👥 共 {sessions.length} 个活跃会话
          </div>
        </div>
      )}

      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

// 简化版本 - 仅显示会话数量
export function UserSessionIndicator() {
  const [sessionCount, setSessionCount] = useState(0)

  useEffect(() => {
    const updateCount = () => {
      try {
        const sessions = userSessionManager.getAllUserSessions()
        setSessionCount(sessions.length)
      } catch (error) {
        console.error('❌ 获取会话数量失败:', error)
      }
    }

    updateCount()
    const interval = setInterval(updateCount, 30000)
    
    return () => clearInterval(interval)
  }, [])

  if (sessionCount <= 1) {
    return null
  }

  return (
    <Badge variant="secondary" className="text-xs">
      {sessionCount} 个会话
    </Badge>
  )
}
