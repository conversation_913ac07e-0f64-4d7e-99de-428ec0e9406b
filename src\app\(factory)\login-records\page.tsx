'use client'

/**
 * 🇨🇳 风口云平台 - 工厂登录记录管理页面
 * 工厂管理员和老板可以查看本工厂员工的登录记录
 * 只显示当前工厂的员工登录记录，不显示总部管理员的记录
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button  } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Clock, Users, Shield, Download, Search, Filter, Eye } from "lucide-react"
import { LoginRecord, LoginStats, OnlineUser } from "@/types"
import { getCurrentFactoryId } from "@/lib/utils/factory"

export default function LoginRecordsPage() {
  const [loginRecords, setLoginRecords] = useState<LoginRecord[]>([])
  const [loginStats, setLoginStats] = useState<LoginStats | null>(null)
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  // 筛选条件
  const [filters, setFilters] = useState({
    userType: 'factory_user', // 默认只显示工厂用户
    loginStatus: 'all',
    startDate: '',
    endDate: '',
    searchTerm: ''
  })

  // 分页
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(50)

  useEffect(() => {
    loadData()
  }, [filters, currentPage])

  const loadData = async () => {
    setIsLoading(true)
    setError("")

    try {
      // 获取当前工厂ID
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        setError('无法获取工厂信息，请重新登录')
        return
      }

      // 构建查询参数
      const params = new URLSearchParams({
        limit: pageSize.toString(),
        offset: ((currentPage - 1) * pageSize).toString(),
        factoryId: factoryId // 添加工厂ID参数，确保只显示当前工厂的记录
      })

      if (filters.userType && filters.userType !== 'all') params.append('userType', filters.userType)
      if (filters.loginStatus && filters.loginStatus !== 'all') params.append('loginStatus', filters.loginStatus)
      if (filters.startDate) params.append('startDate', filters.startDate)
      if (filters.endDate) params.append('endDate', filters.endDate)

      // 获取登录记录
      const recordsResponse = await fetch(`/api/login-records?${params}`)
      const recordsData = await recordsResponse.json()

      if (recordsData.success) {
        let records = recordsData.data

        // 客户端搜索过滤
        if (filters.searchTerm) {
          const searchLower = filters.searchTerm.toLowerCase()
          records = records.filter((record: LoginRecord) =>
            record.username.toLowerCase().includes(searchLower) ||
            record.userName.toLowerCase().includes(searchLower) ||
            (record.factoryName && record.factoryName.toLowerCase().includes(searchLower))
          )
        }

        setLoginRecords(records)
      } else {
        setError(recordsData.error || '获取登录记录失败')
      }

      // 获取统计信息
      const statsParams = new URLSearchParams()
      statsParams.append('factoryId', factoryId) // 添加工厂ID参数
      if (filters.userType && filters.userType !== 'all') statsParams.append('userType', filters.userType)
      if (filters.startDate) statsParams.append('startDate', filters.startDate)
      if (filters.endDate) statsParams.append('endDate', filters.endDate)

      const statsResponse = await fetch(`/api/login-stats?${statsParams}`)
      const statsData = await statsResponse.json()

      if (statsData.success) {
        setLoginStats(statsData.data)
      }

      // 获取在线用户
      const onlineParams = new URLSearchParams()
      onlineParams.append('factoryId', factoryId) // 添加工厂ID参数
      const onlineResponse = await fetch(`/api/online-users?${onlineParams}`)
      const onlineData = await onlineResponse.json()

      if (onlineData.success) {
        setOnlineUsers(onlineData.data)
      }

    } catch (err) {
      console.error('❌ 加载数据失败:', err)
      setError('加载数据失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1) // 重置到第一页
  }

  const clearFilters = () => {
    setFilters({
      userType: 'factory_user', // 保持默认只显示工厂用户
      loginStatus: 'all',
      startDate: '',
      endDate: '',
      searchTerm: ''
    })
    setCurrentPage(1)
  }

  const exportRecords = () => {
    // TODO: 实现导出功能
    console.log('导出登录记录')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'blocked': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success': return '成功'
      case 'failed': return '失败'
      case 'blocked': return '锁定'
      default: return '未知'
    }
  }

  const getUserTypeText = (userType: string) => {
    switch (userType) {
      case 'admin': return '总部管理员'
      case 'factory_user': return '工厂用户'
      default: return '未知'
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">工厂登录记录</h1>
          <p className="text-gray-600 mt-2">查看本工厂员工的登录记录和在线状态</p>
        </div>
        <Button onClick={exportRecords} variant="outline">
          <Download className="h-4 w-4 mr-2" />
          导出记录
        </Button>
      </div>

      {/* 统计卡片 */}
      {loginStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-blue-600">{loginStats.totalLogins}</div>
              <p className="text-sm text-gray-600">总登录次数</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-green-600">{loginStats.successfulLogins}</div>
              <p className="text-sm text-gray-600">成功登录</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-red-600">{loginStats.failedLogins}</div>
              <p className="text-sm text-gray-600">失败登录</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-purple-600">{onlineUsers.length}</div>
              <p className="text-sm text-gray-600">当前在线</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 在线用户 */}
      {onlineUsers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>当前在线用户</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {onlineUsers.map((user, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="font-medium">{user.userName}</p>
                    <p className="text-sm text-gray-600">{user.factoryName || '总部'}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(user.loginTime).toLocaleString('zh-CN')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 筛选条件 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>筛选条件</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div>
              <label className="text-sm font-medium">搜索</label>
              <Input
                placeholder="用户名/姓名/工厂"
                value={filters.searchTerm}
                onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium">用户类型</label>
              <Select value={filters.userType} onValueChange={(value) => handleFilterChange('userType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="工厂用户" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="factory_user">工厂用户</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">登录状态</label>
              <Select value={filters.loginStatus} onValueChange={(value) => handleFilterChange('loginStatus', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="全部" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="success">成功</SelectItem>
                  <SelectItem value="failed">失败</SelectItem>
                  <SelectItem value="blocked">锁定</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">开始日期</label>
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium">结束日期</label>
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
              />
            </div>
            <div className="flex items-end">
              <Button onClick={clearFilters} variant="outline" className="w-full">
                清除筛选
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 登录记录列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>登录记录</span>
          </CardTitle>
          <CardDescription>
            共 {loginRecords.length} 条记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="text-red-600 mb-4 p-3 bg-red-50 rounded">
              {error}
            </div>
          )}

          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3">用户信息</th>
                    <th className="text-left p-3">登录时间</th>
                    <th className="text-left p-3">状态</th>
                    <th className="text-left p-3">IP地址</th>
                    <th className="text-left p-3">设备信息</th>
                    <th className="text-left p-3">会话时长</th>
                  </tr>
                </thead>
                <tbody>
                  {loginRecords.map((record) => (
                    <tr key={record.id} className="border-b hover:bg-gray-50">
                      <td className="p-3">
                        <div>
                          <p className="font-medium">{record.userName || record.username}</p>
                          <p className="text-sm text-gray-600">{getUserTypeText(record.userType)}</p>
                          {record.factoryName && (
                            <p className="text-xs text-gray-500">{record.factoryName}</p>
                          )}
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {new Date(record.loginTime).toLocaleString('zh-CN')}
                          </span>
                        </div>
                      </td>
                      <td className="p-3">
                        <Badge className={getStatusColor(record.loginStatus)}>
                          {getStatusText(record.loginStatus)}
                        </Badge>
                        {record.failReason && (
                          <p className="text-xs text-red-600 mt-1">{record.failReason}</p>
                        )}
                      </td>
                      <td className="p-3">
                        <span className="text-sm font-mono">{record.ipAddress || '-'}</span>
                      </td>
                      <td className="p-3">
                        <span className="text-sm">{record.deviceInfo || '-'}</span>
                      </td>
                      <td className="p-3">
                        {record.sessionDuration ? (
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">
                              {Math.floor(record.sessionDuration / 60)}分{record.sessionDuration % 60}秒
                            </span>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">-</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {loginRecords.length === 0 && !isLoading && (
                <div className="text-center py-8 text-gray-500">
                  暂无登录记录
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 分页 */}
      {loginRecords.length >= pageSize && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            上一页
          </Button>
          <span className="flex items-center px-4">
            第 {currentPage} 页
          </span>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => prev + 1)}
            disabled={loginRecords.length < pageSize}
          >
            下一页
          </Button>
        </div>
      )}
    </div>
  )
}
