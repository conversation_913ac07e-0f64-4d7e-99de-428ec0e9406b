/**
 * 🇨🇳 风口云平台 - 剪贴板工具函数
 * 提供兼容性更好的复制功能
 */

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param successMessage 成功提示信息
 * @param errorMessage 失败提示信息
 * @returns Promise<boolean> 是否复制成功
 */
export const copyToClipboard = async (
  text: string,
  successMessage: string = '已复制到剪贴板',
  errorMessage: string = '复制失败，请手动复制'
): Promise<boolean> => {
  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      if (successMessage) {
        console.log('✅ 复制成功:', text)
        alert(successMessage)
      }
      return true
    } else {
      // 备用方法：使用 execCommand
      console.log('⚠️ 使用备用复制方法')
      return fallbackCopyTextToClipboard(text, successMessage, errorMessage)
    }
  } catch (error) {
    console.error('❌ 现代剪贴板API失败:', error)
    // 尝试备用方法
    return fallbackCopyTextToClipboard(text, successMessage, errorMessage)
  }
}

/**
 * 备用复制方法（兼容旧浏览器）
 * @param text 要复制的文本
 * @param successMessage 成功提示信息
 * @param errorMessage 失败提示信息
 * @returns boolean 是否复制成功
 */
const fallbackCopyTextToClipboard = (
  text: string,
  successMessage: string,
  errorMessage: string
): boolean => {
  try {
    const textArea = document.createElement("textarea")
    textArea.value = text

    // 避免在页面上显示
    textArea.style.top = "0"
    textArea.style.left = "0"
    textArea.style.position = "fixed"
    textArea.style.opacity = "0"
    textArea.style.pointerEvents = "none"

    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    try {
      const successful = document.execCommand('copy')
      if (successful) {
        console.log('✅ 备用复制方法成功:', text)
        if (successMessage) {
          alert(successMessage)
        }
      } else {
        console.warn('⚠️ execCommand 复制失败')
        if (errorMessage) {
          alert(errorMessage)
        }
      }
      return successful
    } catch (err) {
      console.error('❌ execCommand 复制异常:', err)
      if (errorMessage) {
        alert(errorMessage)
      }
      return false
    } finally {
      document.body.removeChild(textArea)
    }
  } catch (error) {
    console.error('❌ 备用复制方法初始化失败:', error)
    if (errorMessage) {
      alert(errorMessage)
    }
    return false
  }
}

/**
 * 检查是否支持剪贴板功能
 * @returns boolean 是否支持
 */
export const isClipboardSupported = (): boolean => {
  return !!(navigator.clipboard && window.isSecureContext) || 
         document.queryCommandSupported?.('copy') === true
}

/**
 * 复制对象为JSON格式
 * @param obj 要复制的对象
 * @param successMessage 成功提示信息
 * @param errorMessage 失败提示信息
 * @returns Promise<boolean> 是否复制成功
 */
export const copyObjectAsJSON = async (
  obj: unknown,
  successMessage: string = 'JSON数据已复制到剪贴板',
  errorMessage: string = '复制失败，请手动复制'
): Promise<boolean> => {
  try {
    const jsonString = JSON.stringify(obj, null, 2)
    return await copyToClipboard(jsonString, successMessage, errorMessage)
  } catch (error) {
    console.error('JSON序列化失败:', error)
    if (errorMessage) {
      alert(errorMessage)
    }
    return false
  }
}

/**
 * 复制表格数据为CSV格式
 * @param data 表格数据数组
 * @param headers 表头数组
 * @param successMessage 成功提示信息
 * @param errorMessage 失败提示信息
 * @returns Promise<boolean> 是否复制成功
 */
export const copyTableAsCSV = async (
  data: unknown[],
  headers: string[],
  successMessage: string = 'CSV数据已复制到剪贴板',
  errorMessage: string = '复制失败，请手动复制'
): Promise<boolean> => {
  try {
    let csvContent = headers.join(',') + '\n'
    
    data.forEach(row => {
      const values = headers.map(header => {
        const value = row[header] || ''
        // 处理包含逗号或引号的值
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value
      })
      csvContent += values.join(',') + '\n'
    })
    
    return await copyToClipboard(csvContent, successMessage, errorMessage)
  } catch (error) {
    console.error('CSV生成失败:', error)
    if (errorMessage) {
      alert(errorMessage)
    }
    return false
  }
}
