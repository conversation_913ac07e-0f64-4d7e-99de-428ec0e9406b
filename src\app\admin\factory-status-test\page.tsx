'use client'

import { useState, useEffect } from 'react'
import FactorySubscriptionManager from '@/components/admin/FactorySubscriptionManager'
import { useAuthStore } from '@/lib/store/auth'

interface Factory {
  id: string
  name: string
  code: string
  status: string
  suspendedAt?: string
  suspendedReason?: string
  subscriptionEnd?: string
  isPermanent?: boolean
}

interface StatusCheckReport {
  totalFactories: number
  activeFactories: number
  expiredFactories: number
  suspendedFactories: number
  warningFactories: number
  processedFactories: number
  errors: string[]
}

export default function FactoryStatusTestPage() {
  const [factories, setFactories] = useState<Factory[]>([])
  const [loading, setLoading] = useState(true)
  const [statusCheckReport, setStatusCheckReport] = useState<StatusCheckReport | null>(null)
  const [isCheckingStatus, setIsCheckingStatus] = useState(false)
  const [selectedFactoryId, setSelectedFactoryId] = useState<string | null>(null)
  const [adminCredentials, setAdminCredentials] = useState({ username: 'admin', password: 'admin123' })
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const { user, login } = useAuthStore()

  useEffect(() => {
    fetchFactories()
    // 检查是否已经登录
    if (user && user.role === 'admin') {
      setIsLoggedIn(true)
    }
  }, [user])

  const fetchFactories = async () => {
    try {
      const response = await fetch('/api/factories?public=true')
      const data = await response.json()

      if (data.success) {
        setFactories(data.factories)
      }
    } catch (error) {
      console.error('获取工厂列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAdminLogin = async () => {
    try {
      const response = await fetch('/api/auth/admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(adminCredentials),
      })

      const data = await response.json()

      if (data.success) {
        // 正确调用 login 方法，传递用户信息、角色和令牌对象
        login(data.user, 'admin', {
          accessToken: data.token,
          refreshToken: data.token, // 管理员登录可能只有一个令牌
          tokenType: 'Bearer',
          expiresIn: 3600
        })
        setIsLoggedIn(true)
        alert('管理员登录成功！')
      } else {
        alert('登录失败：' + data.error)
      }
    } catch (error) {
      console.error('登录失败:', error)
      alert('登录失败，请检查网络连接')
    }
  }

  const testFactoryLogin = async (factoryCode: string) => {
    try {
      // 使用实际的用户名进行测试登录
      let username = factoryCode.toLowerCase() // 转换为小写
      let password = 'test123' // 默认测试密码

      // 根据工厂代码设置正确的用户名和密码
      if (factoryCode === 'GXSFKC') {
        username = 'gxsfkc'
        password = 'gxsfkc' // 尝试使用用户名作为密码
      } else if (factoryCode === 'LMJGC') {
        username = 'lmjgc'
        password = 'lmjgc'
      }

      const response = await fetch('/api/auth/factory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: username,
          password: password
        })
      })

      const result = await response.json()

      if (response.ok) {
        alert(`✅ 工厂 ${factoryCode} 登录成功`)
      } else {
        // 显示详细的错误信息
        let errorMsg = `❌ 工厂 ${factoryCode} 登录失败:\n${result.error}`
        if (result.title) {
          errorMsg += `\n\n${result.title}`
        }
        if (result.suggestion) {
          errorMsg += `\n💡 ${result.suggestion}`
        }
        alert(errorMsg)
      }
    } catch (error) {
      alert(`❌ 测试登录失败: ${error}`)
    }
  }

  const runStatusCheck = async () => {
    if (!isLoggedIn) {
      alert('请先登录管理员账户')
      return
    }

    setIsCheckingStatus(true)
    try {
      // 从 localStorage 或 useAuthStore 获取令牌
      const token = localStorage.getItem('accessToken') || user?.accessToken
      if (!token) {
        alert('未找到认证令牌，请重新登录')
        return
      }

      const response = await fetch('/api/admin/factories/status-check', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      })

      const result = await response.json()

      if (response.ok) {
        setStatusCheckReport(result.data.report)
        alert(`✅ 状态检查完成！处理了 ${result.data.report.processedFactories} 个工厂`)
        // 重新加载工厂列表
        fetchFactories()
      } else {
        alert(`❌ 状态检查失败: ${result.error}`)
      }
    } catch (error) {
      alert(`❌ 状态检查失败: ${error}`)
    } finally {
      setIsCheckingStatus(false)
    }
  }

  if (loading) {
    return <div className="p-8">加载中...</div>
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">工厂认证系统测试</h1>

      {/* 管理员登录面板 */}
      {!isLoggedIn && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4 text-yellow-800">管理员登录</h2>
          <div className="flex gap-4 items-end">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">用户名</label>
              <input
                type="text"
                value={adminCredentials.username}
                onChange={(e) => setAdminCredentials({...adminCredentials, username: e.target.value})}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">密码</label>
              <input
                type="password"
                value={adminCredentials.password}
                onChange={(e) => setAdminCredentials({...adminCredentials, password: e.target.value})}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button
              onClick={handleAdminLogin}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              🔐 登录
            </button>
          </div>
        </div>
      )}

      {/* 登录状态显示 */}
      {isLoggedIn && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <span className="text-green-800">✅ 已登录为管理员: {user?.name || '系统管理员'}</span>
            <button
              onClick={() => {
                setIsLoggedIn(false)
                localStorage.removeItem('token')
              }}
              className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
            >
              登出
            </button>
          </div>
        </div>
      )}

      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">系统控制</h2>
        <div className="flex gap-4">
          <button
            onClick={runStatusCheck}
            disabled={isCheckingStatus}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            {isCheckingStatus ? '检查中...' : '🔍 运行状态检查'}
          </button>
          <button
            onClick={fetchFactories}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            🔄 刷新工厂列表
          </button>
        </div>
      </div>

      {/* 状态检查报告 */}
      {statusCheckReport && (
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4">最新检查报告</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 text-center">
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-2xl font-bold">{statusCheckReport.totalFactories}</div>
              <div className="text-sm text-gray-600">总工厂数</div>
            </div>
            <div className="bg-green-50 p-3 rounded">
              <div className="text-2xl font-bold text-green-600">{statusCheckReport.activeFactories}</div>
              <div className="text-sm text-gray-600">正常运行</div>
            </div>
            <div className="bg-yellow-50 p-3 rounded">
              <div className="text-2xl font-bold text-yellow-600">{statusCheckReport.warningFactories}</div>
              <div className="text-sm text-gray-600">即将过期</div>
            </div>
            <div className="bg-red-50 p-3 rounded">
              <div className="text-2xl font-bold text-red-600">{statusCheckReport.expiredFactories}</div>
              <div className="text-sm text-gray-600">已过期</div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-2xl font-bold text-gray-600">{statusCheckReport.suspendedFactories}</div>
              <div className="text-sm text-gray-600">已暂停</div>
            </div>
            <div className="bg-blue-50 p-3 rounded">
              <div className="text-2xl font-bold text-blue-600">{statusCheckReport.processedFactories}</div>
              <div className="text-sm text-gray-600">处理成功</div>
            </div>
          </div>
          {statusCheckReport.errors.length > 0 && (
            <div className="mt-4 p-3 bg-red-50 rounded">
              <div className="font-medium text-red-800">检查错误 ({statusCheckReport.errors.length})</div>
              <div className="text-sm text-red-600 mt-1">
                {statusCheckReport.errors.slice(0, 3).map((error, index) => (
                  <div key={index}>• {error}</div>
                ))}
                {statusCheckReport.errors.length > 3 && (
                  <div>... 还有 {statusCheckReport.errors.length - 3} 个错误</div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 工厂列表 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">工厂列表</h2>
        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : (
          <div className="space-y-4">
            {factories.map((factory) => (
              <div key={factory.id} className="border p-4 rounded-lg">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold">{factory.name}</h3>
                    <div className="mt-2 space-y-1">
                      <p><strong>工厂代码:</strong> {factory.code}</p>
                      <p><strong>状态:</strong>
                        <span className={`ml-2 px-2 py-1 rounded text-sm ${
                          factory.status === 'active' ? 'bg-green-100 text-green-800' :
                          factory.status === 'suspended' ? 'bg-red-100 text-red-800' :
                          factory.status === 'inactive' ? 'bg-gray-100 text-gray-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {factory.status}
                        </span>
                      </p>
                      {factory.suspendedAt && (
                        <p><strong>暂停时间:</strong> {new Date(factory.suspendedAt).toLocaleString()}</p>
                      )}
                      {factory.suspendedReason && (
                        <p><strong>暂停原因:</strong> {factory.suspendedReason}</p>
                      )}
                      {factory.subscriptionEnd && (
                        <p><strong>订阅到期:</strong> {new Date(factory.subscriptionEnd).toLocaleString()}</p>
                      )}
                      <p><strong>永久订阅:</strong> {factory.isPermanent ? '是' : '否'}</p>
                    </div>
                  </div>

                  <div className="flex gap-2 ml-4">
                    <button
                      onClick={() => testFactoryLogin(factory.code)}
                      className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
                    >
                      🔐 测试登录
                    </button>
                    <button
                      onClick={() => setSelectedFactoryId(factory.id)}
                      className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
                    >
                      ⚙️ 管理
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 工厂管理弹窗 */}
      {selectedFactoryId && (
        <FactorySubscriptionManager
          factoryId={selectedFactoryId}
          onClose={() => setSelectedFactoryId(null)}
          onUpdate={() => {
            fetchFactories()
            setSelectedFactoryId(null)
          }}
        />
      )}
    </div>
  )
}
