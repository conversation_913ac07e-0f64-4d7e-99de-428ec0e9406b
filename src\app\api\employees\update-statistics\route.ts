/**
 * 🇨🇳 风口云平台 - 员工统计信息更新API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 更新员工统计信息
export async function POST(request: NextRequest) {
  try {
    const { factoryId } = await request.json()

    if (!factoryId) {
      return NextResponse.json(
        { error: '工厂ID不能为空' },
        { status: 400 }
      )
    }

    // 更新员工统计信息
    await db.updateAllEmployeeStatistics(factoryId)

    return NextResponse.json({
      success: true
    })

  } catch (error) {
    console.error('❌ 更新员工统计信息失败:', error)
    return NextResponse.json(
      { error: '更新员工统计信息失败' },
      { status: 500 }
    )
  }
}
