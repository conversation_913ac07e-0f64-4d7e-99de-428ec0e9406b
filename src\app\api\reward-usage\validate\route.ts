/**
 * 🔍 奖励使用条件验证API
 *
 * 验证客户是否可以使用推荐奖励
 */

import { NextRequest, NextResponse } from 'next/server'
import { validateRewardUsage, getRewardUsageLimits } from '@/lib/utils/reward-usage-validator'
import { getUserFromRequest } from '@/lib/auth/jwt'

// 获取认证用户信息 - 使用JWT认证，不依赖会话
async function getAuthenticatedUser(request: NextRequest) {
  try {
    // 直接使用JWT认证，不依赖会话验证
    const authResult = getUserFromRequest(request)
    if (authResult.success && authResult.user) {
      const user = authResult.user
      return {
        success: true,
        user: {
          id: user.userId,
          userType: user.userType,
          factoryId: user.factoryId,
          username: user.username,
          name: user.name
        }
      }
    }
  } catch (error) {
    console.log('JWT认证失败:', error)
  }

  return {
    success: false,
    error: '认证失败'
  }
}

export async function POST(request: NextRequest) {
  try {
    // 认证检查
    const authResult = await getAuthenticatedUser(request)
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: '认证失败，请重新登录'
      }, { status: 401 })
    }

    const user = authResult.user
    const body = await request.json()
    const { clientId, amount, factoryId: requestFactoryId } = body

    console.log('🔍 验证奖励使用条件请求:', { clientId, amount, requestFactoryId, userFactoryId: user?.factoryId })

    // 确定工厂ID
    const factoryId = requestFactoryId || user?.factoryId
    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '无法获取工厂信息'
      }, { status: 400 })
    }

    // 验证工厂访问权限
    if (user?.userType !== 'admin' && user?.factoryId !== factoryId) {
      return NextResponse.json({
        success: false,
        error: '无权访问该工厂数据'
      }, { status: 403 })
    }

    // 验证必填参数
    if (!clientId) {
      return NextResponse.json({
        success: false,
        error: '缺少客户ID'
      }, { status: 400 })
    }

    // 如果提供了金额，进行完整验证
    if (amount !== undefined) {
      const usageAmount = parseFloat(amount)
      if (isNaN(usageAmount) || usageAmount <= 0) {
        return NextResponse.json({
          success: false,
          error: '使用金额必须大于0'
        }, { status: 400 })
      }

      // 完整验证
      const validationResult = await validateRewardUsage(clientId, usageAmount)
      
      return NextResponse.json({
        success: true,
        data: {
          canUse: validationResult.canUse,
          reason: validationResult.reason,
          validationDetails: validationResult.details
        }
      })
    } else {
      // 只获取限制信息
      const limits = await getRewardUsageLimits(clientId)
      
      const canUse = limits.maxUsableAmount > 0 && limits.restrictions.length === 0

      console.log('🔍 奖励使用限制验证 (无金额):', {
        clientId,
        maxUsableAmount: limits.maxUsableAmount,
        restrictions: limits.restrictions,
        canUse,
        详细原因: limits.restrictions.length > 0 ? '有限制条件' : '无限制条件',
        可用金额检查: limits.maxUsableAmount > 0 ? '有可用金额' : '无可用金额'
      })

      return NextResponse.json({
        success: true,
        data: {
          canUse,
          maxUsableAmount: limits.maxUsableAmount,
          restrictions: limits.restrictions
        }
      })
    }

  } catch (error) {
    console.error('❌ 验证奖励使用条件失败:', error)
    return NextResponse.json({
      success: false,
      error: '验证奖励使用条件失败，请重试'
    }, { status: 500 })
  }
}
