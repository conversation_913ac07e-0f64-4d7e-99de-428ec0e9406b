"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
// import { Checkbox } from "@/components/ui/checkbox" // 暂时注释掉
import { Star, ThumbsUp, ThumbsDown, MessageSquare, X } from "lucide-react"
import { ocrDataCollector } from "@/lib/ocr-data-collector"

interface OCRFeedbackPanelProps {
  sessionId: string | null
  onClose: () => void
  onSubmit?: () => void
}

export function OCRFeedbackPanel({ sessionId, onClose, onSubmit }: OCRFeedbackPanelProps) {
  const [accuracy, setAccuracy] = useState(3) // 1-5分，默认3分
  const [issues, setIssues] = useState<string[]>([])
  const [suggestions, setSuggestions] = useState("")
  const [wouldRecommend, setWouldRecommend] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [neverShowAgain, setNeverShowAgain] = useState(false)

  // 常见问题选项
  const commonIssues = [
    { id: 'floor_recognition', label: '楼层识别错误', description: '无法正确识别楼层信息' },
    { id: 'vent_type_error', label: '风口类型错误', description: '出风口、回风口类型判断错误' },
    { id: 'dimension_parse_error', label: '尺寸解析错误', description: '长宽尺寸识别不准确' },
    { id: 'notes_messy', label: '备注信息混乱', description: '备注包含无用信息或缺失重要信息' },
    { id: 'quantity_error', label: '数量识别错误', description: '风口数量识别不正确' },
    { id: 'layout_detection_failed', label: '表格布局识别失败', description: '无法正确识别表格结构' },
    { id: 'text_quality_poor', label: '文字识别质量差', description: 'OCR文字识别错误较多' },
    { id: 'missing_data', label: '数据缺失', description: '部分重要信息未被识别' }
  ]

  const handleIssueChange = (issueId: string, checked: boolean) => {
    if (checked) {
      setIssues([...issues, issueId])
    } else {
      setIssues(issues.filter(id => id !== issueId))
    }
  }

  const handleSubmit = async () => {
    if (!sessionId) {
      alert('无效的会话ID，无法提交反馈')
      return
    }

    setIsSubmitting(true)

    try {
      // 收集用户反馈
      ocrDataCollector.collectUserFeedback(sessionId, {
        accuracy,
        issues,
        suggestions: suggestions.trim(),
        wouldRecommend
      })

      // 保存反馈设置到本地存储
      const feedbackSettings = {
        neverShow: neverShowAgain,
        lastFeedbackTime: Date.now()
      }
      localStorage.setItem('ocr_feedback_settings', JSON.stringify(feedbackSettings))

      console.log('✅ 用户反馈提交成功:', {
        sessionId,
        accuracy,
        issues,
        suggestions: suggestions.trim(),
        wouldRecommend,
        neverShowAgain
      })

      alert('感谢您的反馈！这将帮助我们改进OCR识别效果。')

      onSubmit?.()
      onClose()
    } catch (error) {
      console.error('❌ 提交反馈失败:', error)
      alert('提交反馈失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSkip = () => {
    // 即使跳过也要保存设置
    if (neverShowAgain) {
      const feedbackSettings = {
        neverShow: true,
        lastFeedbackTime: Date.now()
      }
      localStorage.setItem('ocr_feedback_settings', JSON.stringify(feedbackSettings))
    }
    onClose()
  }

  const renderStarRating = () => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => setAccuracy(star)}
            className={`p-1 rounded transition-colors ${
              star <= accuracy 
                ? 'text-yellow-500 hover:text-yellow-600' 
                : 'text-gray-300 hover:text-gray-400'
            }`}
          >
            <Star 
              className="w-6 h-6" 
              fill={star <= accuracy ? 'currentColor' : 'none'}
            />
          </button>
        ))}
        <span className="ml-2 text-sm text-gray-600">
          {accuracy === 1 && '很差'}
          {accuracy === 2 && '较差'}
          {accuracy === 3 && '一般'}
          {accuracy === 4 && '良好'}
          {accuracy === 5 && '优秀'}
        </span>
      </div>
    )
  }

  if (!sessionId) {
    return null
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <MessageSquare className="w-5 h-5" />
                <span>OCR识别效果反馈</span>
              </CardTitle>
              <CardDescription>
                您的反馈将帮助我们改进OCR识别准确性
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 准确率评分 */}
          <div className="space-y-2">
            <Label className="text-base font-medium">
              整体识别准确率评分
            </Label>
            <p className="text-sm text-gray-600">
              请为本次OCR识别的整体效果打分
            </p>
            {renderStarRating()}
          </div>

          {/* 遇到的问题 - 简化版本 */}
          <div className="space-y-3">
            <Label className="text-base font-medium">
              遇到的问题 (可多选)
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {commonIssues.map((issue) => (
                <div key={issue.id} className="flex items-start space-x-2">
                  <input
                    type="checkbox"
                    id={issue.id}
                    checked={issues.includes(issue.id)}
                    onChange={(e) => handleIssueChange(issue.id, e.target.checked)}
                    className="mt-1"
                  />
                  <div className="grid gap-1.5 leading-none">
                    <label
                      htmlFor={issue.id}
                      className="text-sm font-medium leading-none cursor-pointer"
                    >
                      {issue.label}
                    </label>
                    <p className="text-xs text-gray-500">
                      {issue.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 改进建议 */}
          <div className="space-y-2">
            <Label htmlFor="suggestions" className="text-base font-medium">
              改进建议 (可选)
            </Label>
            <Textarea
              id="suggestions"
              placeholder="请描述您遇到的具体问题或改进建议..."
              value={suggestions}
              onChange={(e) => setSuggestions(e.target.value)}
              rows={4}
            />
          </div>

          {/* 推荐度 */}
          <div className="space-y-3">
            <Label className="text-base font-medium">
              您是否会推荐这个OCR识别功能？
            </Label>
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => setWouldRecommend(true)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  wouldRecommend 
                    ? 'bg-green-50 border-green-200 text-green-700' 
                    : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                }`}
              >
                <ThumbsUp className="w-4 h-4" />
                <span>会推荐</span>
              </button>
              <button
                type="button"
                onClick={() => setWouldRecommend(false)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  !wouldRecommend 
                    ? 'bg-red-50 border-red-200 text-red-700' 
                    : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                }`}
              >
                <ThumbsDown className="w-4 h-4" />
                <span>不会推荐</span>
              </button>
            </div>
          </div>

          {/* 不再显示选项 */}
          <div className="flex items-center space-x-2 pt-4 border-t">
            <input
              type="checkbox"
              id="neverShowAgain"
              checked={neverShowAgain}
              onChange={(e) => setNeverShowAgain(e.target.checked)}
              className="rounded"
            />
            <Label htmlFor="neverShowAgain" className="text-sm text-gray-600">
              不再显示此反馈面板
            </Label>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-3 pt-3">
            <Button
              variant="outline"
              onClick={handleSkip}
              disabled={isSubmitting}
            >
              跳过反馈
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? '提交中...' : '提交反馈'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
