'use client'

import { useState } from 'react'

export default function TestOCRPerformancePage() {
  const [testResults, setTestResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const performanceTests = [
    {
      name: '手写识别测试',
      description: '测试手写文字识别准确性和速度',
      type: 'handwriting',
      expectedImprovement: '准确性提升10-15%'
    },
    {
      name: '表格识别测试', 
      description: '测试表格结构识别和数据提取',
      type: 'table',
      expectedImprovement: '数字识别错误减少'
    },
    {
      name: '智能识别测试',
      description: '测试自动内容类型检测',
      type: 'auto',
      expectedImprovement: '识别策略选择更准确'
    },
    {
      name: '批量处理测试',
      description: '测试多文件并发处理性能',
      type: 'batch',
      expectedImprovement: '处理速度提升30-50%'
    }
  ]

  const runPerformanceTest = async (testType: string) => {
    setIsLoading(true)
    const startTime = Date.now()
    
    try {
      // 这里可以添加实际的测试逻辑
      // 模拟测试过程
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      const result = {
        testType,
        duration,
        timestamp: new Date().toLocaleString(),
        status: 'success',
        improvements: getTestImprovements(testType)
      }
      
      setTestResults(prev => [...prev, result])
    } catch (error) {
      const result = {
        testType,
        duration: 0,
        timestamp: new Date().toLocaleString(),
        status: 'error',
        error: error instanceof Error ? error.message : '测试失败'
      }
      
      setTestResults(prev => [...prev, result])
    } finally {
      setIsLoading(false)
    }
  }

  const getTestImprovements = (testType: string) => {
    const improvements: { [key: string]: string[] } = {
      handwriting: [
        '✅ 图像预处理：灰度化、去噪、二值化',
        '✅ 后处理校正：O→0, I→1, l→1',
        '✅ 智能评分：基于置信度分布',
        '📈 预期准确性提升：10-15%'
      ],
      table: [
        '✅ 表格单元格后处理',
        '✅ 数字识别校正',
        '✅ 符号标准化：×, X → x',
        '📈 预期错误减少：显著'
      ],
      auto: [
        '✅ 智能内容类型检测',
        '✅ 多重识别策略',
        '✅ 最佳结果选择',
        '📈 预期准确性：更高'
      ],
      batch: [
        '✅ 并发处理：批量限制2个',
        '✅ Promise.allSettled处理',
        '✅ 批次间延迟控制',
        '📈 预期速度提升：30-50%'
      ]
    }
    
    return improvements[testType] || []
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            📊 OCR性能优化测试
          </h1>
          
          <div className="mb-8">
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <h2 className="text-xl font-semibold text-blue-800 mb-2">
                🎯 优化概览
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div className="bg-white rounded p-3">
                  <div className="font-semibold text-green-600">图像预处理</div>
                  <div className="text-gray-600">Sharp库优化</div>
                </div>
                <div className="bg-white rounded p-3">
                  <div className="font-semibold text-blue-600">代码重构</div>
                  <div className="text-gray-600">减少15-20%</div>
                </div>
                <div className="bg-white rounded p-3">
                  <div className="font-semibold text-purple-600">智能评分</div>
                  <div className="text-gray-600">置信度优化</div>
                </div>
                <div className="bg-white rounded p-3">
                  <div className="font-semibold text-orange-600">并发处理</div>
                  <div className="text-gray-600">速度提升30-50%</div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                🧪 性能测试项目
              </h2>
              <div className="space-y-3">
                {performanceTests.map((test, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-gray-800">{test.name}</h3>
                      <button
                        onClick={() => runPerformanceTest(test.type)}
                        disabled={isLoading}
                        className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 disabled:opacity-50"
                      >
                        {isLoading ? '测试中...' : '运行测试'}
                      </button>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{test.description}</p>
                    <div className="text-xs text-green-600 font-medium">
                      {test.expectedImprovement}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                📈 测试结果
              </h2>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {testResults.length === 0 ? (
                  <div className="text-gray-500 text-center py-8">
                    暂无测试结果，请运行测试
                  </div>
                ) : (
                  testResults.map((result, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold text-gray-800">
                          {performanceTests.find(t => t.type === result.testType)?.name}
                        </h3>
                        <span className={`px-2 py-1 rounded text-xs ${
                          result.status === 'success' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {result.status === 'success' ? '成功' : '失败'}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600 mb-2">
                        耗时: {result.duration}ms | 时间: {result.timestamp}
                      </div>
                      {result.improvements && (
                        <div className="text-xs space-y-1">
                          {result.improvements.map((improvement: string, i: number) => (
                            <div key={i} className="text-gray-700">{improvement}</div>
                          ))}
                        </div>
                      )}
                      {result.error && (
                        <div className="text-xs text-red-600">错误: {result.error}</div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              💡 优化说明
            </h3>
            <div className="text-sm text-yellow-700 space-y-1">
              <p>• 本页面展示OCR优化的各项改进和预期效果</p>
              <p>• 实际测试需要上传相应类型的图片文件</p>
              <p>• 建议使用包含手写文字、表格数据的真实图片进行测试</p>
              <p>• 性能提升数据基于理论分析和代码优化程度</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
