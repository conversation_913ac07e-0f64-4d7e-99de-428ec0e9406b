/**
 * 🇨🇳 风口云平台 - 客户端数据库服务
 *
 * 通过API调用与数据库交互，适用于浏览器环境
 */

import type { Order, Factory, FactoryUser, Client, Announcement, User } from "@/types"
import { isAdmin, isFactoryUser, isClient, toNumber  } from "@/types"
import { api } from "@/lib/api/client"
import { useAuthStore } from "@/lib/store/auth"

/**
 * 客户端数据库服务
 * 所有操作通过API调用完成，自动处理认证
 */
class ClientDatabaseService {

  /**
   * 工厂用户认证
   */
  async authenticateFactoryUser(username: string, password: string): Promise<FactoryUser | null> {
    try {
      const response = await api.post('/api/auth/factory', {
        username,
        password
      }, { requireAuth: false })

      if (response.success) {
        return response.data?.user || null
      } else {
        console.error('❌ 认证失败:', response.error)
        return null
      }
    } catch (error) {
      console.error('❌ 认证请求失败:', error)
      return null
    }
  }

  /**
   * 管理员认证
   */
  async authenticateAdmin(username: string, password: string): Promise<any | null> {
    try {
      const response = await api.post('/api/auth/admin', {
        username,
        password
      }, { requireAuth: false })

      if (response.success) {
        return response.data?.admin || null
      } else {
        console.error('❌ 管理员认证失败:', response.error)
        return null
      }
    } catch (error) {
      console.error('❌ 管理员认证请求失败:', error)
      return null
    }
  }

  /**
   * 创建订单（支持重试机制）
   */
  async createOrder(orderData: Omit<Order, 'id'>): Promise<Order | null> {
    const maxRetries = 3
    let attempt = 0

    while (attempt < maxRetries) {
      try {
        console.log(`📞 客户端发送创建订单请求 (尝试 ${attempt + 1}/${maxRetries}):`, {
          factoryId: orderData.factoryId,
          clientId: orderData.clientId,
          orderNumber: orderData.orderNumber,
          itemsCount: orderData.items?.length,
          totalAmount: orderData.totalAmount
        })

        // 获取当前用户信息用于调试
        const currentUser = useAuthStore.getState().user
        const currentFactoryId = useAuthStore.getState().factoryId
        const currentRole = useAuthStore.getState().role

        console.log('👤 当前用户信息:', {
          role: currentRole,
          factoryId: currentFactoryId,
          userType: (currentUser as User)?.userType || 'unknown'
        })

        // 在请求头中添加工厂ID，帮助后端验证权限
        const headers: Record<string, string> = {}
        if (currentFactoryId) {
          headers['x-factory-id'] = currentFactoryId
        }

        const response = await api.post('/api/orders', orderData, { headers })

        if (response.success) {
          console.log('✅ 订单创建成功:', response.data?.order?.id)
          return response.data?.order || null
        } else {
          console.error(`❌ 创建订单失败 (尝试 ${attempt + 1}/${maxRetries}):`, response.error)

          // 检查是否是订单号重复错误
          if (response.error?.includes('order_number') || response.error?.includes('订单号')) {
            console.warn('⚠️ 检测到订单号冲突，准备重试')

            if (attempt < maxRetries - 1) {
              // 重新生成订单号（传入重试次数）
              try {
                const { generateOrderNumber } = await import('@/lib/utils/factory')
                const newOrderNumber = await generateOrderNumber(attempt + 1) // 传入重试次数
                console.log(`🔄 重新生成订单号: ${orderData.orderNumber} -> ${newOrderNumber}`)
                orderData.orderNumber = newOrderNumber
                attempt++
                continue
              } catch (genError) {
                console.error('❌ 重新生成订单号失败:', genError)
              }
            }
          }

          throw new Error(response.error || '创建订单失败')
        }
      } catch (error) {
        console.error(`❌ 创建订单请求失败 (尝试 ${attempt + 1}/${maxRetries}):`, error)

        // 如果是网络错误或服务器错误，且不是最后一次尝试，则重试
        if (attempt < maxRetries - 1 && (
          (error as Error).message?.includes('fetch') ||
          (error as Error).message?.includes('network') ||
          (error as Error).message?.includes('500')
        )) {
          console.log('🔄 网络错误，等待后重试...')
          attempt++
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt)) // 递增延迟
          continue
        }

        console.error('错误详情:', {
          message: (error as Error).message,
          stack: (error as Error).stack,
          name: (error as Error).name
        })

        if (attempt >= maxRetries - 1) {
          throw error
        }

        attempt++
      }
    }

    throw new Error('创建订单失败：达到最大重试次数')
  }

  /**
   * 获取工厂订单列表
   */
  async getOrdersByFactoryId(factoryId: string): Promise<Order[]> {
    try {
      const response = await api.get(`/api/orders?factoryId=${factoryId}`)

      if (response.success) {
        return response.data?.orders || []
      } else {
        console.error('❌ 获取订单失败:', response.error)
        return []
      }
    } catch (error) {
      console.error('❌ 获取订单请求失败:', error)
      return []
    }
  }

  /**
   * 根据客户ID获取订单列表
   */
  async getOrdersByClientId(clientId: string): Promise<Order[]> {
    try {
      console.log('📋 获取客户订单:', clientId)

      // 使用公开API，不需要认证
      const response = await api.get(`/api/orders/by-client?clientId=${clientId}&public=true`, {
        requireAuth: false
      })

      if (response.success) {
        console.log('✅ 客户订单获取成功:', response.data?.orders?.length || 0, '条')
        return response.data?.orders || []
      } else {
        console.error('❌ 获取客户订单失败:', response.error)
        return []
      }
    } catch (error) {
      console.error('❌ 获取客户订单请求失败:', error)
      return []
    }
  }

  /**
   * 创建客户
   */
  async createClient(clientData: Omit<Client, 'id'>): Promise<Client | null> {
    try {
      console.log('📞 客户端发送创建客户请求:', {
        factoryId: clientData.factoryId,
        name: clientData.name,
        phone: clientData.phone
      })

      // 获取当前用户信息用于调试
      const currentUser = useAuthStore.getState().user
      const currentFactoryId = useAuthStore.getState().factoryId
      const currentRole = useAuthStore.getState().role

      console.log('👤 当前用户信息:', {
        role: currentRole,
        factoryId: currentFactoryId,
        userType: (currentUser as User)?.userType || 'unknown'
      })

      // 在请求头中添加工厂ID，帮助后端验证权限
      const headers: Record<string, string> = {}
      if (currentFactoryId) {
        headers['x-factory-id'] = currentFactoryId
      }

      const response = await api.post('/api/clients', clientData, { headers })

      if (response.success) {
        console.log('✅ 客户创建成功:', response.data?.client?.id)
        return response.data?.client || null
      } else {
        console.error('❌ 创建客户失败:', response.error)
        throw new Error(response.error || '创建客户失败')
      }
    } catch (error) {
      console.error('❌ 创建客户请求失败:', error)
      throw error
    }
  }

  /**
   * 获取工厂的客户列表
   */
  async getClientsByFactoryId(factoryId: string): Promise<Client[]> {
    try {
      const response = await api.get(`/api/clients?factoryId=${factoryId}`)

      if (response.success) {
        return response.data?.clients || []
      } else {
        console.error('❌ 获取客户失败:', response.error)
        return []
      }
    } catch (error) {
      console.error('❌ 获取客户请求失败:', error)
      return []
    }
  }

  /**
   * 搜索客户
   */
  async searchClients(searchTerm: string, searchType: string = 'all'): Promise<Client[]> {
    try {
      console.log('🔍 客户端发送公开搜索请求:', { searchTerm, searchType })

      // 使用公开搜索API，不需要认证
      const response = await api.get(`/api/clients/search?term=${encodeURIComponent(searchTerm)}&type=${searchType}&public=true`, {
        requireAuth: false
      })

      if (response.success) {
        console.log('✅ 客户搜索成功:', response.data?.clients?.length || 0, '个结果')
        return response.data?.clients || []
      } else {
        console.error('❌ 搜索客户失败:', response.error)
        return []
      }
    } catch (error) {
      console.error('❌ 搜索客户请求失败:', error)
      return []
    }
  }

  /**
   * 获取客户推荐的其他客户
   */
  async getReferredClients(clientId: string): Promise<Client[]> {
    try {
      console.log('👥 获取推荐客户:', clientId)

      // 使用公开API，不需要认证
      const response = await api.get(`/api/clients/referred?clientId=${clientId}&public=true`, {
        requireAuth: false
      })

      if (response.success) {
        console.log('✅ 推荐客户获取成功:', response.data?.clients?.length || 0, '个')
        return response.data?.clients || []
      } else {
        console.error('❌ 获取推荐客户失败:', response.error)
        return []
      }
    } catch (error) {
      console.error('❌ 获取推荐客户请求失败:', error)
      return []
    }
  }



  /**
   * 获取所有工厂信息
   */
  async getFactories(): Promise<Factory[]> {
    try {
      console.log('🏭 获取工厂列表')

      // 使用公开API，不需要认证
      const response = await api.get('/api/factories?public=true', {
        requireAuth: false
      })

      if (response.success) {
        console.log('✅ 工厂列表获取成功:', response.data?.factories?.length || 0, '个')
        return response.data?.factories || []
      } else {
        console.error('❌ 获取工厂列表失败:', response.error)
        return []
      }
    } catch (error) {
      console.error('❌ 获取工厂列表请求失败:', error)
      return []
    }
  }



  /**
   * 获取所有工厂（管理员端使用，包含完整订阅信息）
   */
  async getAllFactories(): Promise<Factory[]> {
    try {
      console.log('🏭 获取工厂列表（管理员）')

      // 使用管理员API，需要认证
      const response = await api.get('/api/admin/factories', {
        requireAuth: true
      })

      console.log('📋 管理员API响应:', response)

      if (response.success) {
        // API客户端会将响应包装在data字段中
        const responseData = (response as any).data || {}
        const factories = responseData.factories || []
        console.log('✅ 管理员工厂列表获取成功:', factories.length, '个')
        console.log('🔍 工厂数据示例:', factories[0] ? {
          id: factories[0].id,
          name: factories[0].name,
          subscriptionType: factories[0].subscriptionType,
          subscriptionEnd: factories[0].subscriptionEnd
        } : '无数据')
        return factories
      } else {
        console.error('❌ 获取管理员工厂列表失败:', response.error)
        throw new Error(response.error || '获取工厂列表失败')
      }
    } catch (error) {
      console.error('❌ 获取管理员工厂列表请求失败:', error)
      throw error
    }
  }

  /**
   * 创建工厂和管理员
   */
  async createFactoryWithAdmin(
    factoryData: Omit<Factory, 'id' | 'createdAt' | 'updatedAt'>,
    adminData: Omit<FactoryUser, 'id' | 'factoryId' | 'createdAt' | 'updatedAt'>,
    plainPassword: string
  ) {
    try {
      const response = await api.post('/api/factories', {
        factoryData,
        adminData,
        password: plainPassword
      })

      if (response.success) {
        return {
          factory: response.data?.data?.factory || response.data?.factory,
          admin: response.data?.data?.admin || response.data?.admin
        }
      } else {
        console.error('❌ 创建工厂失败:', response.error)
        throw new Error(response.error)
      }
    } catch (error) {
      console.error('❌ 创建工厂请求失败:', error)
      throw error
    }
  }

  /**
   * 获取公告列表
   */
  async getAnnouncements(): Promise<Announcement[]> {
    try {
      const response = await api.get('/api/announcements')

      if (response.success) {
        return response.data?.announcements || []
      }

      console.error('❌ 获取公告失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 获取公告失败:', error)
      return []
    }
  }

  /**
   * 获取工厂相关公告
   */
  async getAnnouncementsForFactory(factoryId: string): Promise<Announcement[]> {
    try {
      const response = await api.get(`/api/announcements?factoryId=${factoryId}`)

      if (response.success) {
        // 转换日期字符串为Date对象
        return (response.data?.announcements || []).map((announcement: unknown) => ({
          ...announcement,
          publishedAt: new Date(announcement.publishedAt),
          expiresAt: announcement.expiresAt ? new Date(announcement.expiresAt) : null
        }))
      }

      console.error('❌ 获取工厂公告失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 获取工厂公告失败:', error)
      return []
    }
  }

  /**
   * 获取工厂未读公告（用于登录弹窗）
   */
  async getUnreadAnnouncementsForFactory(factoryId: string): Promise<Announcement[]> {
    try {
      console.log('🔔 获取工厂未读公告:', factoryId)

      // 直接获取所有工厂相关公告，不使用本地存储
      const allAnnouncements = await this.getAnnouncementsForFactory(factoryId)

      // 返回所有公告，让服务端处理已读状态
      console.log('✅ 工厂公告获取成功:', allAnnouncements.length, '条')
      return allAnnouncements
    } catch (error) {
      console.error('❌ 获取工厂公告失败:', error)
      return []
    }
  }

  /**
   * 获取工厂活跃公告（用于页面显示，排除已读和已关闭的公告）
   */
  async getActiveAnnouncementsForFactory(factoryId: string): Promise<Announcement[]> {
    try {
      console.log('📋 获取工厂活跃公告:', factoryId)

      // 通过API获取活跃公告
      const response = await api.get(`/api/announcements/active?factoryId=${factoryId}`)

      if (response.success) {
        // 转换日期字符串为Date对象
        const activeAnnouncements = (response.data?.announcements || []).map((announcement: unknown) => ({
          ...announcement,
          publishedAt: new Date(announcement.publishedAt),
          expiresAt: announcement.expiresAt ? new Date(announcement.expiresAt) : null
        }))

        console.log('✅ 工厂活跃公告获取成功:', activeAnnouncements.length, '条')
        return activeAnnouncements
      }

      console.error('❌ 获取工厂活跃公告失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 获取工厂活跃公告失败:', error)
      return []
    }
  }

  /**
   * 标记公告为已读
   */
  async markAnnouncementAsRead(factoryId: string, announcementId: string): Promise<void> {
    try {
      console.log('✅ 标记公告为已读:', factoryId, announcementId)

      // 通过API标记公告为已读，不使用本地存储
      const response = await api.post('/api/announcements/mark-read', {
        factoryId,
        announcementId
      })

      if (response.success) {
        console.log('✅ 公告已标记为已读')
      } else {
        console.error('❌ 标记公告为已读失败:', response.error)
      }
    } catch (error) {
      console.error('❌ 标记公告为已读失败:', error)
    }
  }

  /**
   * 关闭公告（不再显示在弹窗中）
   */
  async dismissAnnouncement(factoryId: string, announcementId: string): Promise<void> {
    try {
      console.log('❌ 关闭公告:', factoryId, announcementId)

      // 通过API关闭公告，不使用本地存储
      const response = await api.post('/api/announcements/dismiss', {
        factoryId,
        announcementId
      })

      if (response.success) {
        console.log('✅ 公告已关闭')
      } else {
        console.error('❌ 关闭公告失败:', response.error)
      }
    } catch (error) {
      console.error('❌ 关闭公告失败:', error)
    }
  }

  /**
   * 创建公告
   */
  async createAnnouncement(announcementData: unknown): Promise<Announcement | null> {
    try {
      const response = await api.post('/api/announcements', announcementData)

      if (response.success) {
        return response.data?.announcement || null
      }

      console.error('❌ 创建公告失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 创建公告失败:', error)
      return null
    }
  }

  /**
   * 更新公告
   */
  async updateAnnouncement(id: string, updates: unknown): Promise<Announcement | null> {
    try {
      const response = await api.put('/api/announcements', { id, ...updates })

      if (response.success) {
        return response.data?.announcement || null
      }

      console.error('❌ 更新公告失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 更新公告失败:', error)
      return null
    }
  }

  /**
   * 删除公告
   */
  async deleteAnnouncement(id: string): Promise<boolean> {
    try {
      const response = await api.delete(`/api/announcements?id=${id}`)

      if (response.success) {
        return true
      }

      console.error('❌ 删除公告失败:', response.error)
      return false
    } catch (error) {
      console.error('❌ 删除公告失败:', error)
      return false
    }
  }

  /**
   * 获取系统设置
   */
  async getSystemSettings(): Promise<unknown> {
    try {
      // 返回完整的默认设置
      return {
        platform: {
          name: '风口云平台',
          version: '1.0.0',
          description: '中央空调风口加工厂SaaS管理系统',
          contactEmail: '<EMAIL>',
          contactPhone: '************',
          website: 'https://fengkoucloud.com'
        },
        security: {
          sessionTimeout: 24,
          passwordMinLength: 6,
          requirePasswordChange: false,
          allowMultipleLogin: true,
          enableTwoFactor: false,
          adminPassword: 'admin@yhg2025'
        },
        backup: {
          autoBackup: true,
          backupFrequency: 'daily',
          retentionDays: 30,
          backupLocation: 'local',
          lastBackupTime: null
        },
        notifications: {
          emailEnabled: false,
          smsEnabled: false,
          pushEnabled: true,
          orderNotifications: true,
          systemNotifications: true,
          announcementNotifications: true
        },
        business: {
          defaultCurrency: 'CNY',
          taxRate: 0.13,
          maxFactories: 100,
          orderNumberPrefix: 'FK',
          invoiceNumberPrefix: 'INV',
          enableRewards: false,
          enableDataSync: true
        },
        appearance: {
          theme: 'light',
          primaryColor: '#dc2626',
          logoUrl: '',
          faviconUrl: ''
        }
      }
    } catch (error) {
      console.error('❌ 获取系统设置失败:', error)
      return {}
    }
  }

  /**
   * 更新系统设置
   */
  async updateSystemSettings(settings: unknown): Promise<void> {
    try {
      console.log('💾 更新系统设置...')

      // 这里可以实现真正的API调用保存逻辑
      // 目前只是模拟保存成功
      console.log('✅ 系统设置更新成功')
    } catch (error) {
      console.error('❌ 更新系统设置失败:', error)
      throw error
    }
  }

  /**
   * 更新订单付款信息
   */
  async updateOrderPayment(orderId: string, paymentData: {
    paymentStatus?: string
    paidAmount?: number
    dueDate?: Date
    paymentNotes?: string
  }): Promise<boolean> {
    try {
      const response = await api.put('/api/orders/payment', {
        orderId,
        ...paymentData
      })

      if (response.success) {
        console.log('✅ 订单付款信息更新成功')
        return true
      } else {
        console.error('❌ 更新订单付款信息失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('❌ 更新订单付款信息请求失败:', error)
      return false
    }
  }

  /**
   * 更新订单状态信息
   */
  async updateOrderStatus(orderId: string, statusData: {
    status?: string
    notes?: string
  }): Promise<boolean> {
    try {
      const response = await api.put('/api/orders/status', {
        orderId,
        ...statusData
      })

      if (response.success) {
        console.log('✅ 订单状态信息更新成功')
        return true
      } else {
        console.error('❌ 更新订单状态信息失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('❌ 更新订单状态信息请求失败:', error)
      return false
    }
  }

  // 兼容性方法
  async updateClientStatistics(clientId: string): Promise<boolean> {
    try {
      console.log('🔄 开始更新客户统计信息:', clientId)

      const response = await api.post('/api/clients/update-statistics', { clientId })

      if (response.success) {
        console.log('✅ 客户统计信息更新成功:', clientId)
        return true
      } else {
        console.error('❌ 更新客户统计信息失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('❌ 更新客户统计信息请求失败:', error)
      return false
    }
  }

  /**
   * 修复数据同步问题 - 强制同步所有客户统计信息
   */
  async fixDataSyncIssues(factoryId: string): Promise<{success: boolean, message: string, details: unknown[]}> {
    try {
      console.log('🔧 开始修复数据同步问题...')

      // 获取工厂的所有客户
      const clients = await this.getClientsByFactoryId(factoryId)
      const orders = await this.getOrdersByFactoryId(factoryId)

      const results: unknown[] = []
      let successCount = 0
      let failCount = 0

      for (const client of clients) {
        try {
          // 计算客户的实际统计数据
          const clientOrders = orders.filter(order => order.clientId === client.id)
          const actualTotalAmount = clientOrders.reduce((sum, order) => sum + toNumber(order.totalAmount || 0), 0)
          const actualPaidAmount = clientOrders.reduce((sum, order) => sum + toNumber(order.paidAmount || 0), 0)
          const actualUnpaidAmount = actualTotalAmount - actualPaidAmount

          // 检查是否需要更新
          const needsUpdate =
            Math.abs(toNumber(client.totalAmount || 0) - actualTotalAmount) > 0.01 ||
            Math.abs(toNumber(client.paidAmount || 0) - actualPaidAmount) > 0.01 ||
            Math.abs(toNumber(client.unpaidAmount || 0) - actualUnpaidAmount) > 0.01

          if (needsUpdate) {
            console.log(`🔄 客户 ${client.name} 需要同步数据`)
            const updateSuccess = await this.updateClientStatistics(client.id)

            if (updateSuccess) {
              successCount++
              results.push({
                clientId: client.id,
                clientName: client.name,
                status: 'success',
                before: {
                  totalAmount: client.totalAmount || 0,
                  paidAmount: client.paidAmount || 0,
                  unpaidAmount: client.unpaidAmount || 0
                },
                after: {
                  totalAmount: actualTotalAmount,
                  paidAmount: actualPaidAmount,
                  unpaidAmount: actualUnpaidAmount
                }
              })
            } else {
              failCount++
              results.push({
                clientId: client.id,
                clientName: client.name,
                status: 'failed',
                error: '统计信息更新失败'
              })
            }
          } else {
            results.push({
              clientId: client.id,
              clientName: client.name,
              status: 'no_change',
              message: '数据已同步，无需更新'
            })
          }
        } catch (error) {
          failCount++
          results.push({
            clientId: client.id,
            clientName: client.name,
            status: 'error',
            error: (error as Error).message
          })
        }
      }

      const message = `数据同步修复完成：成功 ${successCount} 个，失败 ${failCount} 个，总计 ${clients.length} 个客户`
      console.log('✅', message)

      return {
        success: failCount === 0,
        message,
        details: results
      }
    } catch (error) {
      console.error('❌ 修复数据同步问题失败:', error)
      return {
        success: false,
        message: `修复失败: ${(error as Error).message}`,
        details: []
      }
    }
  }

  async checkUsernameExists(username: string): Promise<boolean> {
    // 暂时返回false，实际检查在服务端进行
    return false
  }

  async checkFactoryCodeExists(code: string): Promise<boolean> {
    // 暂时返回false，实际检查在服务端进行
    return false
  }

  /**
   * 验证管理员密码
   */
  async verifyAdminPassword(adminId: string, password: string): Promise<boolean> {
    try {
      const response = await api.post('/api/auth/admin/verify', { adminId, password })

      if (response.success) {
        return response.data?.isValid || false
      } else {
        console.error('❌ 验证管理员密码失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('❌ 验证管理员密码请求失败:', error)
      return false
    }
  }

  /**
   * 更新管理员密码
   */
  async updateAdminPassword(adminId: string, currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      const response = await api.post('/api/auth/admin/update-password', {
        adminId,
        currentPassword,
        newPassword
      })

      if (response.success) {
        return response.data?.success || false
      } else {
        console.error('❌ 更新管理员密码失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('❌ 更新管理员密码请求失败:', error)
      return false
    }
  }

  /**
   * 验证工厂用户密码
   */
  async verifyFactoryUserPassword(username: string, password: string): Promise<boolean> {
    try {
      console.log('🔐 前端发送密码验证请求:', { username, password: '***' })
      const response = await api.post('/api/auth/factory/verify', { username, password })

      console.log('🔐 密码验证API响应:', JSON.stringify(response, null, 2))
      console.log('🔐 response.success:', response.success)
      console.log('🔐 response.data:', response.data)
      console.log('🔐 response.data?.isValid:', response.data?.isValid)

      if (response.success) {
        // 修复：正确访问嵌套的数据结构
        const isValid = response.data?.data?.isValid || false
        console.log('🔐 密码验证结果:', isValid)
        return isValid
      } else {
        console.error('❌ 验证工厂用户密码失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('❌ 验证工厂用户密码请求失败:', error)
      return false
    }
  }

  /**
   * 获取工厂员工列表
   */
  async getEmployeesByFactoryId(factoryId: string): Promise<FactoryUser[]> {
    try {
      const response = await api.get(`/api/employees?factoryId=${factoryId}`)

      if (response.success) {
        return response.data?.employees || []
      } else {
        console.error('❌ 获取员工列表失败:', response.error)
        return []
      }
    } catch (error) {
      console.error('❌ 获取员工列表请求失败:', error)
      return []
    }
  }

  /**
   * 更新员工信息
   */
  async updateEmployee(employeeId: string, updates: unknown): Promise<FactoryUser | null> {
    try {
      const response = await api.put('/api/employees', { id: employeeId, ...updates })

      if (response.success) {
        // 修复：API返回的数据结构是 {success: true, employee}
        return response.data?.employee || response.data || null
      } else {
        console.error('❌ 更新员工信息失败:', response.error)
        return null
      }
    } catch (error) {
      console.error('❌ 更新员工信息请求失败:', error)
      return null
    }
  }

  /**
   * 创建员工
   */
  async createEmployee(employeeData: unknown): Promise<FactoryUser | null> {
    try {
      const response = await api.post('/api/employees', employeeData)

      if (response.success) {
        return response.data?.employee || null
      } else {
        console.error('❌ 创建员工失败:', response.error)
        return null
      }
    } catch (error) {
      console.error('❌ 创建员工请求失败:', error)
      return null
    }
  }

  async deleteFactory(factoryId: string): Promise<boolean> {
    try {
      const response = await api.delete(`/api/factories?id=${factoryId}`)

      if (response.success) {
        return true
      } else {
        console.error('❌ 删除工厂失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('❌ 删除工厂请求失败:', error)
      return false
    }
  }

  /**
   * 删除员工
   */
  async deleteEmployee(employeeId: string): Promise<boolean> {
    try {
      const response = await api.delete(`/api/employees?id=${employeeId}`)

      if (response.success) {
        return true
      } else {
        console.error('❌ 删除员工失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('❌ 删除员工请求失败:', error)
      return false
    }
  }

  /**
   * 删除客户
   */
  async deleteClient(clientId: string): Promise<boolean> {
    try {
      const response = await api.delete(`/api/clients/${clientId}`)

      if (response.success) {
        return true
      } else {
        console.error('❌ 删除客户失败:', response.error)
        // 抛出错误以便前端能够显示具体的错误信息
        throw new Error(response.error || '删除客户失败')
      }
    } catch (error) {
      console.error('❌ 删除客户请求失败:', error)
      // 重新抛出错误以便前端处理
      throw error
    }
  }

  /**
   * 删除订单
   */
  async deleteOrder(orderId: string): Promise<boolean> {
    try {
      const response = await api.delete(`/api/orders/${orderId}`)

      if (response.success) {
        return true
      } else {
        console.error('❌ 删除订单失败:', response.error)
        throw new Error(response.error || '删除订单失败')
      }
    } catch (error) {
      console.error('❌ 删除订单失败:', error)
      throw error
    }
  }

  /**
   * 切换员工状态
   */
  async toggleEmployeeStatus(employeeId: string): Promise<FactoryUser | null> {
    try {
      const response = await api.put('/api/employees/toggle-status', { id: employeeId })

      if (response.success) {
        return response.data?.employee || null
      } else {
        console.error('❌ 切换员工状态失败:', response.error)
        return null
      }
    } catch (error) {
      console.error('❌ 切换员工状态请求失败:', error)
      return null
    }
  }

  /**
   * 更新所有员工统计信息
   */
  async updateAllEmployeeStatistics(factoryId: string): Promise<void> {
    try {
      const response = await api.post('/api/employees/update-statistics', { factoryId })

      if (!response.success) {
        console.error('❌ 更新员工统计信息失败:', response.error)
      }
    } catch (error) {
      console.error('❌ 更新员工统计信息请求失败:', error)
    }
  }
}

// 创建单例实例
export const db = new ClientDatabaseService()

// 导出类型
export type { Order, Factory, FactoryUser, Client }

console.log('✅ 客户端数据库服务初始化完成')
