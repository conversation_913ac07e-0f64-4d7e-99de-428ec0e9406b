"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Building2, Factory, Eye, EyeOff } from "lucide-react"
import { AuthService } from "@/lib/services/auth.service"
import "@/lib/utils/clear-auth-state" // 导入清理工具（开发环境）

export default function FactoryLoginPage() {
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      console.log('🔐 尝试登录工厂用户:', username)

      // 使用新的认证服务
      const result = await AuthService.loginFactory({ username, password })

      if (result.success && result.user) {
        console.log('✅ 工厂用户登录成功:', result.user.name)
        console.log('🔄 正在跳转到仪表板...')

        // 添加延迟确保状态完全保存
        setTimeout(() => {
          console.log('🔄 执行跳转到仪表板...')
          router.replace("/factory/dashboard")
        }, 1000) // 增加到1秒，确保状态完全同步
      } else {
        console.log('❌ 工厂用户登录失败:', result.error)

        // 根据错误类型显示不同的提示信息
        let errorMessage = result.error || "用户名或密码错误，请检查账号信息或联系总部开通账号"

        if (result.code) {
          switch (result.code) {
            case 'FACTORY_SUSPENDED':
              errorMessage = `🚫 ${result.title || '服务已暂停'}\n\n${result.error}\n\n💡 ${result.suggestion || '请联系管理员了解详情'}`
              break
            case 'FACTORY_EXPIRED':
              errorMessage = `⏰ ${result.title || '服务已到期'}\n\n${result.error}\n\n💡 ${result.suggestion || '请联系管理员续费'}`
              break
            case 'FACTORY_INACTIVE':
              errorMessage = `❓ ${result.title || '账号未激活'}\n\n${result.error}\n\n💡 ${result.suggestion || '请联系管理员激活账号'}`
              break
            case 'FACTORY_NOT_FOUND':
              errorMessage = `⚠️ ${result.title || '工厂信息异常'}\n\n${result.error}\n\n💡 ${result.suggestion || '请联系管理员检查配置'}`
              break
            default:
              errorMessage = result.error
          }
        }

        setError(errorMessage)
      }
    } catch (err) {
      console.error('❌ 登录请求失败:', err)
      setError("登录失败，请重试")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-100 flex items-center justify-center p-4">
      <div className="w-full max-w-lg">
        {/* Logo and Title */}
        <div className="text-center mb-10">
          <div className="flex items-center justify-center space-x-4 mb-6">
            <div className="bg-blue-600 p-4 rounded-xl shadow-lg">
              <Factory className="h-10 w-10 text-white" />
            </div>
            <div className="text-left">
              <h1 className="text-3xl font-bold text-gray-900 leading-tight">风口云平台</h1>
              <p className="text-base text-gray-600 mt-1">工厂管理系统</p>
            </div>
          </div>
        </div>

        {/* Login Form */}
        <Card className="shadow-xl border-0">
          <CardHeader className="text-center pb-6 pt-8">
            <CardTitle className="text-2xl font-bold text-gray-900 mb-2">工厂登录</CardTitle>
            <CardDescription className="text-base text-gray-600">
              请使用工厂账号登录管理系统
            </CardDescription>
          </CardHeader>
          <CardContent className="px-8 pb-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-3">
                <label htmlFor="username" className="text-base font-semibold text-gray-700 block">
                  工厂账号
                </label>
                <Input
                  id="username"
                  type="text"
                  placeholder="请输入工厂账号"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  className="h-12 text-base px-4 border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg transition-colors"
                />
              </div>

              <div className="space-y-3">
                <label htmlFor="password" className="text-base font-semibold text-gray-700 block">
                  密码
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="请输入密码"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="h-12 text-base px-4 pr-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg transition-colors"
                  />
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>

              {error && (
                <div className="text-sm text-red-600 bg-red-50 p-4 rounded-lg border border-red-200">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                className="w-full h-12 text-base font-semibold bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
                disabled={isLoading}
              >
                {isLoading ? "登录中..." : "登录"}
              </Button>
            </form>

            <div className="mt-8 text-center space-y-3">
              <p className="text-base text-gray-600 font-medium">
                默认演示账号：lin001 / lin001
              </p>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">
                  如需开通工厂账号，请联系平台总部创建
                </p>
                <p className="text-sm text-gray-500">
                  新创建的工厂管理员可以使用总部分配的账号登录
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Back to Home */}
        <div className="text-center mt-8">
          <Link
            href="/"
            className="text-base text-gray-600 hover:text-gray-900 flex items-center justify-center space-x-2 transition-colors"
          >
            <Building2 className="h-5 w-5" />
            <span>返回首页</span>
          </Link>
        </div>
      </div>
    </div>
  )
}
