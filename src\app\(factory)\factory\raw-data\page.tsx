'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Database, RefreshCw } from 'lucide-react'

export default function RawDataPage() {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<unknown>(null)

  const loadRawData = async () => {
    try {
      setLoading(true)
      
      console.log('📊 开始读取原始数据...')

      const response = await fetch('/api/read-raw-data')
      const result = await response.json()

      if (result.success) {
        setData(result.data)
        console.log('✅ 原始数据读取成功:', result.data)
      } else {
        alert(`读取失败: ${result.error}`)
      }

    } catch (error) {
      console.error('❌ 读取失败:', error)
      alert('读取失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">📊 数据库原始数据</h1>
            <p className="text-gray-600">直接从数据库读取的真实数据</p>
          </div>
          <Button onClick={loadRawData} disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                读取中...
              </>
            ) : (
              <>
                <Database className="h-4 w-4 mr-2" />
                读取原始数据
              </>
            )}
          </Button>
        </div>

        {data && (
          <div className="space-y-6">
            {/* 数据概览 */}
            <Card>
              <CardHeader>
                <CardTitle>📈 数据概览</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{data.summary.totalClients}</div>
                    <div className="text-sm text-gray-600">总客户数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{data.summary.totalOrders}</div>
                    <div className="text-sm text-gray-600">总订单数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{data.summary.referrersCount}</div>
                    <div className="text-sm text-gray-600">推荐人数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{data.summary.referredClientsCount}</div>
                    <div className="text-sm text-gray-600">被推荐客户</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${data.summary.clientsWithDiscrepancy > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {data.summary.clientsWithDiscrepancy}
                    </div>
                    <div className="text-sm text-gray-600">数据异常</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 推荐人详情 */}
            <Card>
              <CardHeader>
                <CardTitle>👥 推荐人详情</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.referrers.map((referrer: unknown) => (
                    <div key={referrer.referrerId} className={`p-4 border rounded-lg ${
                      referrer.hasDiscrepancy ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
                    }`}>
                      <div className="flex justify-between items-center mb-3">
                        <div>
                          <h3 className="font-bold text-lg">{referrer.referrerName}</h3>
                          <p className="text-sm text-gray-600">{referrer.referrerPhone}</p>
                        </div>
                        <div className="text-right">
                          {referrer.hasDiscrepancy ? (
                            <Badge variant="destructive">❌ 数据异常</Badge>
                          ) : (
                            <Badge variant="default" className="bg-green-600">✅ 正常</Badge>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <span className="text-sm text-gray-600">应得奖励:</span>
                          <div className="font-bold text-green-600">¥{referrer.calculatedReward.toFixed(2)}</div>
                        </div>
                        <div>
                          <span className="text-sm text-gray-600">数据库奖励:</span>
                          <div className="font-bold text-blue-600">¥{referrer.databaseReward.toFixed(2)}</div>
                        </div>
                        <div>
                          <span className="text-sm text-gray-600">可用奖励:</span>
                          <div className="font-bold text-purple-600">¥{referrer.availableReward.toFixed(2)}</div>
                        </div>
                        <div>
                          <span className="text-sm text-gray-600">推荐人数:</span>
                          <div className="font-bold">{referrer.referredCount}</div>
                        </div>
                      </div>

                      {referrer.hasDiscrepancy && (
                        <div className="mb-4 p-3 bg-red-100 border border-red-200 rounded">
                          <span className="text-red-800 font-medium">
                            ⚠️ 差额: ¥{Math.abs(referrer.discrepancyAmount).toFixed(2)}
                          </span>
                        </div>
                      )}

                      <div>
                        <h4 className="font-medium mb-2">推荐的客户:</h4>
                        <div className="space-y-2">
                          {referrer.referredDetails.map((referred: unknown) => (
                            <div key={referred.clientId} className="bg-white p-3 border rounded">
                              <div className="flex justify-between items-center mb-2">
                                <div>
                                  <span className="font-medium">{referred.clientName}</span>
                                  <span className="text-sm text-gray-600 ml-2">({referred.clientPhone})</span>
                                </div>
                                <div className="text-right">
                                  <div className="text-sm">订单: {referred.orderCount}个</div>
                                  <div className="text-sm">金额: ¥{referred.totalAmount.toFixed(2)}</div>
                                  <div className="text-sm font-bold text-green-600">奖励: ¥{referred.rewardGenerated.toFixed(2)}</div>
                                </div>
                              </div>
                              
                              {referred.orders.length > 0 && (
                                <div className="mt-2">
                                  <div className="text-xs text-gray-600 mb-1">订单详情:</div>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                                    {referred.orders.map((order: unknown) => (
                                      <div key={order.id} className="text-xs bg-gray-50 p-2 rounded">
                                        <div className="font-medium">{order.orderNumber || order.id}</div>
                                        <div>¥{order.totalAmount.toFixed(2)}</div>
                                        <div className="text-gray-500">
                                          {new Date(order.createdAt).toLocaleDateString()}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 原始客户数据 */}
            <Card>
              <CardHeader>
                <CardTitle>📋 原始客户数据</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">姓名</th>
                        <th className="text-left p-2">电话</th>
                        <th className="text-left p-2">推荐人</th>
                        <th className="text-right p-2">推荐奖励</th>
                        <th className="text-right p-2">可用奖励</th>
                        <th className="text-right p-2">待结算</th>
                        <th className="text-right p-2">推荐数</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.rawClients.map((client: unknown) => (
                        <tr key={client.id} className="border-b hover:bg-gray-50">
                          <td className="p-2 font-medium">{client.name}</td>
                          <td className="p-2">{client.phone}</td>
                          <td className="p-2">{client.referrerName || '-'}</td>
                          <td className="p-2 text-right">¥{client.referralReward.toFixed(2)}</td>
                          <td className="p-2 text-right">¥{client.availableReward.toFixed(2)}</td>
                          <td className="p-2 text-right">¥{client.pendingReward.toFixed(2)}</td>
                          <td className="p-2 text-right">{client.referralCount}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {!data && !loading && (
          <Card>
            <CardContent className="p-8 text-center">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">点击读取数据</h3>
              <p className="text-gray-600">点击"读取原始数据"按钮查看数据库中的真实数据
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
