"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  <PERSON><PERSON>hart3, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Download, 
  RefreshCw, 
  Trash2,
  Eye,
  Users,
  Target,
  AlertCircle
} from "lucide-react"
import { ocrDataCollector } from "@/lib/ocr-data-collector"

export function OCRDataReport() {
  const [report, setReport] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [showRawData, setShowRawData] = useState(false)

  const loadReport = () => {
    setLoading(true)
    try {
      const reportData = ocrDataCollector.generateAnalysisReport()
      setReport(reportData)
    } catch (error) {
      console.error('生成报告失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadReport()
  }, [])

  const exportData = () => {
    try {
      const data = ocrDataCollector.exportData()
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `ocr-analysis-data-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('导出数据失败:', error)
      alert('导出数据失败')
    }
  }

  const clearData = () => {
    if (confirm('确定要清空所有收集的数据吗？此操作不可恢复。')) {
      ocrDataCollector.clearData()
      loadReport()
      alert('数据已清空')
    }
  }

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'bg-green-500'
      case 'good': return 'bg-blue-500'
      case 'fair': return 'bg-yellow-500'
      case 'poor': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getLayoutTypeLabel = (type: string) => {
    switch (type) {
      case 'standard_table': return '标准表格'
      case 'left_right_pairs': return '左右对应'
      case 'single_column': return '单列排列'
      case 'mixed_format': return '混合格式'
      default: return '未知格式'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin mr-2" />
        <span>加载数据中...</span>
      </div>
    )
  }

  if (!report || report.message) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5" />
            <span>OCR数据分析报告</span>
          </CardTitle>
          <CardDescription>
            暂无收集到的OCR使用数据
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">
              还没有OCR识别数据。请先使用OCR功能识别一些图片。
            </p>
            <Button onClick={loadReport} variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              刷新数据
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center space-x-2">
            <BarChart3 className="w-6 h-6" />
            <span>OCR数据分析报告</span>
          </h2>
          <p className="text-gray-600 mt-1">
            基于真实用户使用数据的OCR识别效果分析
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={loadReport} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          <Button onClick={exportData} variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            导出数据
          </Button>
          <Button onClick={clearData} variant="destructive" size="sm">
            <Trash2 className="w-4 h-4 mr-2" />
            清空数据
          </Button>
        </div>
      </div>

      {/* 总体统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">总识别次数</p>
                <p className="text-2xl font-bold">{report.totalSessions}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">整体成功率</p>
                <p className="text-2xl font-bold">{report.averageAccuracy.overallSuccess}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">楼层识别率</p>
                <p className="text-2xl font-bold">{report.averageAccuracy.floorRecognition}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <PieChart className="w-5 h-5 text-orange-500" />
              <div>
                <p className="text-sm text-gray-600">风口类型准确率</p>
                <p className="text-2xl font-bold">{report.averageAccuracy.ventTypeAccuracy}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 布局类型分布 */}
        <Card>
          <CardHeader>
            <CardTitle>布局类型分布</CardTitle>
            <CardDescription>
              用户上传图片的表格布局类型统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(report.layoutTypeDistribution).map(([type, count]) => {
                const percentage = ((count as number) / report.totalSessions * 100).toFixed(1)
                return (
                  <div key={type} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{getLayoutTypeLabel(type)}</Badge>
                      <span className="text-sm text-gray-600">{count}次</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Progress value={parseFloat(percentage)} className="w-20" />
                      <span className="text-sm font-medium">{percentage}%</span>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* 坐标质量分布 */}
        <Card>
          <CardHeader>
            <CardTitle>坐标质量分布</CardTitle>
            <CardDescription>
              百度OCR返回的坐标信息质量统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(report.coordinateQualityDistribution).map(([quality, count]) => {
                const percentage = ((count as number) / report.totalSessions * 100).toFixed(1)
                return (
                  <div key={quality} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${getQualityColor(quality)}`} />
                      <span className="capitalize">{quality}</span>
                      <span className="text-sm text-gray-600">{count}次</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Progress value={parseFloat(percentage)} className="w-20" />
                      <span className="text-sm font-medium">{percentage}%</span>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* 识别准确率详情 */}
        <Card>
          <CardHeader>
            <CardTitle>识别准确率详情</CardTitle>
            <CardDescription>
              各项识别功能的平均准确率
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">楼层识别</span>
                  <span className="text-sm font-medium">{report.averageAccuracy.floorRecognition}</span>
                </div>
                <Progress value={parseFloat(report.averageAccuracy.floorRecognition)} />
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">风口类型</span>
                  <span className="text-sm font-medium">{report.averageAccuracy.ventTypeAccuracy}</span>
                </div>
                <Progress value={parseFloat(report.averageAccuracy.ventTypeAccuracy)} />
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">尺寸解析</span>
                  <span className="text-sm font-medium">{report.averageAccuracy.dimensionParseRate}</span>
                </div>
                <Progress value={parseFloat(report.averageAccuracy.dimensionParseRate)} />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 用户反馈统计 */}
        <Card>
          <CardHeader>
            <CardTitle>用户反馈统计</CardTitle>
            <CardDescription>
              用户对OCR识别效果的评价
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>反馈数量</span>
                <Badge>{report.userFeedback.totalFeedbacks}条</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>平均评分</span>
                <div className="flex items-center space-x-1">
                  <span className="font-medium">{report.userFeedback.averageRating}</span>
                  <span className="text-sm text-gray-600">/ 5.0</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 原始数据查看 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>原始数据</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowRawData(!showRawData)}
            >
              <Eye className="w-4 h-4 mr-2" />
              {showRawData ? '隐藏' : '查看'}原始数据
            </Button>
          </CardTitle>
        </CardHeader>
        {showRawData && (
          <CardContent>
            <pre className="bg-gray-50 p-4 rounded-lg text-xs overflow-auto max-h-96">
              {JSON.stringify(ocrDataCollector.exportData(), null, 2)}
            </pre>
          </CardContent>
        )}
      </Card>
    </div>
  )
}
