import { NextRequest, NextResponse } from 'next/server'
import { recognizeIntent } from '@/lib/nlp/intent-recognition'
import { generateSmartOrder, validateSmartOrder } from '@/lib/nlp/smart-order-generator'

/**
 * 🇨🇳 智能OCR订单生成API
 * 
 * 接收OCR识别的文本，通过NLP意图识别自动生成风口订单
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🤖 开始智能OCR订单生成处理...')
    
    const body = await request.json()
    const { text, factoryId, createdBy } = body
    
    if (!text || typeof text !== 'string') {
      return NextResponse.json({
        success: false,
        error: '缺少文本内容'
      }, { status: 400 })
    }
    
    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '缺少工厂ID'
      }, { status: 400 })
    }
    
    if (!createdBy) {
      return NextResponse.json({
        success: false,
        error: '缺少创建者信息'
      }, { status: 400 })
    }
    
    console.log('📝 输入文本长度:', text.length)
    console.log('🏭 工厂ID:', factoryId)
    console.log('👤 创建者:', createdBy)
    
    // 步骤1: 意图识别
    console.log('🧠 步骤1: 执行意图识别...')
    const intentResult = recognizeIntent(text)
    
    if (intentResult.intent === 'unknown') {
      return NextResponse.json({
        success: false,
        error: '无法识别文本意图',
        data: {
          intentResult,
          suggestions: [
            '请确保文本包含风口尺寸信息（如：140x240）',
            '请包含风口类型信息（如：出风口、回风口）',
            '请检查文本格式是否清晰'
          ]
        }
      })
    }
    
    if (intentResult.intent === 'query_price') {
      return NextResponse.json({
        success: false,
        error: '识别到价格查询意图，暂不支持自动报价',
        data: {
          intentResult,
          suggestions: [
            '请联系客服获取最新报价',
            '或访问价格管理页面查看单价设置'
          ]
        }
      })
    }
    
    // 步骤2: 智能订单生成
    console.log('🤖 步骤2: 执行智能订单生成...')
    const orderResult = generateSmartOrder(intentResult, factoryId, createdBy)
    
    if (!orderResult.success) {
      return NextResponse.json({
        success: false,
        error: '订单生成失败',
        data: {
          intentResult,
          orderResult,
          suggestions: orderResult.warnings || []
        }
      })
    }
    
    // 步骤3: 验证订单数据
    console.log('✅ 步骤3: 验证订单数据...')
    const validation = validateSmartOrder(orderResult.orderData)
    
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: '订单数据验证失败',
        data: {
          intentResult,
          orderResult,
          validationErrors: validation.errors
        }
      })
    }
    
    // 成功返回
    console.log('🎉 智能OCR订单生成成功!')
    console.log(`📊 生成了 ${orderResult.orderData?.items.length} 个风口项目`)
    console.log(`💰 订单总金额: ¥${orderResult.orderData?.totalAmount}`)
    console.log(`🎯 识别置信度: ${(orderResult.confidence * 100).toFixed(1)}%`)
    
    return NextResponse.json({
      success: true,
      data: {
        intentResult,
        orderResult,
        orderData: orderResult.orderData,
        metadata: {
          processingTime: Date.now(),
          confidence: orderResult.confidence,
          itemCount: orderResult.orderData?.items.length || 0,
          totalAmount: orderResult.orderData?.totalAmount || 0
        }
      },
      message: '智能订单生成成功'
    })
    
  } catch (error) {
    console.error('❌ 智能OCR订单生成失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '智能订单生成失败',
      message: '智能OCR订单生成处理失败'
    }, { status: 500 })
  }
}

/**
 * 获取支持的意图类型
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    data: {
      supportedIntents: [
        {
          type: 'create_order',
          name: '创建订单',
          description: '识别风口尺寸和数量信息，自动生成订单',
          examples: [
            '140x240 出风口 5个',
            '回风口 300x160 数量3',
            '线型风口 1200x140 x2'
          ]
        },
        {
          type: 'query_price',
          name: '价格查询',
          description: '查询风口价格信息',
          examples: [
            '140x240出风口多少钱',
            '回风口价格',
            '线型风口报价'
          ]
        }
      ],
      supportedVentTypes: [
        '出风口', '回风口', '线型风口', '检修口'
      ],
      supportedFormats: [
        '尺寸格式: 长x宽 (如: 140x240)',
        '数量格式: 数字+单位 (如: 5个, 3只)',
        '类型格式: 中文描述 (如: 出风口, 回风口)'
      ],
      tips: [
        '📝 文本越清晰，识别准确率越高',
        '📊 支持批量识别多个风口项目',
        '🎯 建议包含项目名称和楼层信息',
        '⚡ 自动智能尺寸排序(长×宽)',
        '💡 支持中文数字和阿拉伯数字'
      ]
    },
    message: '智能图片识别订单生成API信息'
  })
}
