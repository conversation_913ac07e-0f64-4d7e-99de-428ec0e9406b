#!/usr/bin/env node

/**
 * 🇨🇳 风口云平台 - 生产环境设置脚本
 * 切换回生产环境配置，使用PostgreSQL数据库
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🏭 开始设置生产环境...\n');

// 1. 恢复生产环境schema文件
console.log('📋 恢复生产环境Prisma schema文件...');
const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
const schemaBackupPath = path.join(process.cwd(), 'prisma', 'schema.production.prisma');

if (fs.existsSync(schemaBackupPath)) {
  fs.copyFileSync(schemaBackupPath, schemaPath);
  console.log('✅ 已恢复生产环境schema (PostgreSQL)');
} else {
  console.error('❌ 生产环境schema备份文件不存在');
  console.log('💡 请手动检查 prisma/schema.prisma 文件配置');
}

// 2. 检查生产环境变量文件
console.log('⚙️ 检查生产环境变量配置...');
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env 文件不存在，请先创建生产环境配置');
  process.exit(1);
}

// 3. 清理开发环境数据库文件
console.log('🧹 清理开发环境数据库文件...');
const dbFiles = ['dev.db', 'dev.db-journal', 'dev.db-wal'];
dbFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`🗑️ 删除开发数据库文件: ${file}`);
  }
});

// 4. 清理Prisma生成的文件
console.log('🧹 清理Prisma缓存...');
try {
  const prismaPath = path.join(process.cwd(), 'node_modules', '.prisma');
  if (fs.existsSync(prismaPath)) {
    fs.rmSync(prismaPath, { recursive: true, force: true });
    console.log('✅ Prisma缓存已清理');
  }
} catch (error) {
  console.log('⚠️ 清理Prisma缓存时出现警告:', error.message);
}

// 5. 重新生成Prisma客户端
console.log('🔧 重新生成Prisma客户端...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma客户端生成成功');
} catch (error) {
  console.error('❌ Prisma客户端生成失败:', error.message);
  process.exit(1);
}

console.log('\n🎉 生产环境设置完成！');
console.log('\n📝 接下来的步骤：');
console.log('1. 确保生产环境数据库正在运行');
console.log('2. 运行数据库迁移: npx prisma db push');
console.log('3. 构建应用: npm run build');
console.log('4. 启动生产服务器: npm start');
console.log('\n💡 提示：');
console.log('- 生产环境使用PostgreSQL数据库');
console.log('- 确保 .env 文件中的 DATABASE_URL 配置正确');
console.log('- 要切换回开发环境，运行: node scripts/setup-local-dev.js');
