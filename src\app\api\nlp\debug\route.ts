import { NextRequest, NextResponse } from 'next/server'
import { recognizeIntent } from '@/lib/nlp/intent-recognition'
import { generateSmartOrder } from '@/lib/nlp/smart-order-generator'
import { recordLearning, getLearningHistory, getLearningStats, getImprovementSuggestions, simulateLearningProgress } from '@/lib/nlp/learning-system'

/**
 * 🇨🇳 NLP调试API - 提供详细的处理过程信息
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔍 开始NLP调试分析...')
    
    const body = await request.json()
    const { text, enableDebug = true } = body
    
    if (!text || typeof text !== 'string') {
      return NextResponse.json({
        success: false,
        error: '缺少文本内容'
      }, { status: 400 })
    }
    
    const startTime = Date.now()
    
    // 步骤1: 文本预处理调试
    console.log('🔧 步骤1: 文本预处理...')
    const originalLength = text.length
    const preprocessedText = preprocessTextDebug(text)
    const processedLength = preprocessedText.length
    
    // 步骤2: 意图识别调试
    console.log('🧠 步骤2: 意图识别...')
    const intentResult = recognizeIntent(text)
    
    // 步骤3: 智能订单生成调试
    console.log('📋 步骤3: 智能订单生成...')
    let orderResult = null
    if (intentResult.intent === 'create_order') {
      orderResult = generateSmartOrder(intentResult, 'debug-factory', 'debug-user')
    }
    
    const processingTime = Date.now() - startTime

    // 记录学习数据
    console.log('📚 记录学习数据...')
    const learningRecord = recordLearning(text, intentResult, processingTime)

    // 获取学习统计和建议
    const learningStats = getLearningStats()
    const improvementSuggestions = getImprovementSuggestions()
    const learningProgress = simulateLearningProgress()

    // 构建调试信息
    const debugInfo = {
      // 文本预处理信息
      preprocessing: {
        originalLength,
        processedLength,
        processedText: preprocessedText,
        transformations: getTextTransformations(text, preprocessedText)
      },
      
      // 意图识别详情
      intentAnalysis: {
        intent: intentResult.intent,
        confidence: intentResult.confidence,
        classificationScores: calculateClassificationScores(preprocessedText),
        keywordMatches: findKeywordMatches(preprocessedText)
      },
      
      // 实体提取详情
      entityExtraction: {
        projectName: intentResult.entities.projectName,
        floorInfo: intentResult.entities.floorInfo,
        ventItems: intentResult.entities.ventItems.map(item => ({
          ...item,
          extractionDetails: analyzeVentItemExtraction(item)
        }))
      },
      
      // 性能指标
      performance: {
        processingTime,
        itemsProcessed: intentResult.entities.ventItems.length,
        averageConfidence: intentResult.entities.ventItems.length > 0 
          ? intentResult.entities.ventItems.reduce((sum, item) => sum + item.confidence, 0) / intentResult.entities.ventItems.length
          : 0
      }
    }
    
    console.log('✅ NLP调试分析完成')
    console.log(`📊 处理时间: ${processingTime}ms`)
    console.log(`🎯 识别置信度: ${(intentResult.confidence * 100).toFixed(1)}%`)
    console.log(`📋 风口项目数: ${intentResult.entities.ventItems.length}`)
    
    return NextResponse.json({
      success: true,
      intentResult,
      orderResult,
      debug: debugInfo,
      learning: {
        currentRecord: learningRecord,
        stats: learningStats,
        suggestions: improvementSuggestions,
        progress: learningProgress,
        recentHistory: getLearningHistory(5)
      },
      metadata: {
        processingTime,
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      },
      message: 'NLP调试分析完成'
    })
    
  } catch (error) {
    console.error('❌ NLP调试分析失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'NLP调试分析失败',
      message: 'NLP调试处理失败'
    }, { status: 500 })
  }
}

/**
 * 文本预处理调试版本
 */
function preprocessTextDebug(text: string): string {
  return text
    // 统一换行符
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    // 统一乘号
    .replace(/[×X*]/g, 'x')
    // 去除多余空格
    .replace(/\s+/g, ' ')
    // 统一标点符号
    .replace(/[。，；：]/g, '.')
    .trim()
}

/**
 * 获取文本转换详情
 */
function getTextTransformations(original: string, processed: string): any[] {
  const transformations = []
  
  if (original.includes('×') || original.includes('X') || original.includes('*')) {
    transformations.push({
      type: '符号标准化',
      description: '将×、X、*统一为x',
      examples: ['140×240 → 140x240', '150*300 → 150x300']
    })
  }
  
  if (original.includes('\r\n') || original.includes('\r')) {
    transformations.push({
      type: '换行符统一',
      description: '统一换行符为\\n',
      count: (original.match(/\r\n|\r/g) || []).length
    })
  }
  
  if (/\s{2,}/.test(original)) {
    transformations.push({
      type: '空格规范化',
      description: '去除多余空格',
      count: (original.match(/\s{2,}/g) || []).length
    })
  }
  
  return transformations
}

/**
 * 计算分类得分
 */
function calculateClassificationScores(text: string): any {
  const orderKeywords = ['出风', '回风', '风口', 'x', '×', '数量', '个', '楼', '层']
  const priceKeywords = ['价格', '多少钱', '报价', '单价']
  
  const orderMatches = orderKeywords.filter(keyword => text.includes(keyword))
  const priceMatches = priceKeywords.filter(keyword => text.includes(keyword))
  
  return {
    orderScore: orderMatches.length,
    priceScore: priceMatches.length,
    orderKeywords: orderMatches,
    priceKeywords: priceMatches,
    classification: orderMatches.length >= 2 ? 'create_order' : 
                   priceMatches.length >= 1 ? 'query_price' : 'unknown'
  }
}

/**
 * 查找关键词匹配
 */
function findKeywordMatches(text: string): any {
  const ventTypes = {
    出风: ['出风', '出风口', '送风', '送风口'],
    回风: ['回风', '回风口'],
    线型: ['线型', '线形', '条形'],
    检修: ['检修', '检修口', '维修', '维修口']
  }
  
  const matches: any = {}
  
  for (const [category, keywords] of Object.entries(ventTypes)) {
    matches[category] = keywords.filter(keyword => text.includes(keyword))
  }
  
  // 尺寸匹配
  const dimensionMatches = text.match(/\d+\s*[xX×*]\s*\d+/g) || []
  matches.dimensions = dimensionMatches
  
  // 数量匹配
  const quantityMatches = text.match(/\d+\s*[个只套]|\d+\s*数量|数量\s*[:：]\s*\d+/g) || []
  matches.quantities = quantityMatches
  
  return matches
}

/**
 * 分析风口项目提取详情
 */
function analyzeVentItemExtraction(item: any): any {
  return {
    dimensionSource: `从"${item.originalText}"中提取`,
    typeInference: {
      method: item.width > 280 ? '基于宽度判断(>280mm)' : '基于关键词匹配',
      confidence: item.confidence,
      reasoning: item.width > 280 ? '宽度较大，判断为回风口' : '根据文本关键词判断'
    },
    quantityExtraction: {
      found: item.quantity > 1,
      method: item.quantity > 1 ? '从文本中提取' : '使用默认值1'
    },
    sizeOptimization: {
      original: `${Math.min(item.length, item.width)}x${Math.max(item.length, item.width)}`,
      optimized: `${item.length}x${item.width}`,
      swapped: item.length !== Math.max(item.length, item.width)
    }
  }
}

/**
 * 获取调试统计信息
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    data: {
      debugFeatures: [
        {
          name: '文本预处理跟踪',
          description: '显示文本清理和标准化的每个步骤'
        },
        {
          name: '意图分类详情',
          description: '展示关键词匹配和分类得分计算过程'
        },
        {
          name: '实体提取分析',
          description: '详细显示项目名称、楼层、风口信息的提取过程'
        },
        {
          name: '置信度计算',
          description: '展示每个识别结果的置信度计算依据'
        },
        {
          name: '性能监控',
          description: '提供处理时间和效率指标'
        }
      ],
      supportedAnalysis: [
        '文本转换跟踪',
        '关键词匹配分析',
        '实体提取详情',
        '类型推断逻辑',
        '置信度评估',
        '性能指标统计'
      ]
    },
    message: 'NLP调试API信息'
  })
}
