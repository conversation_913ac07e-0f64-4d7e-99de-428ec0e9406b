/**
 * 🇨🇳 风口云平台 - 工厂订阅管理API
 * 
 * 提供管理员用于管理工厂订阅状态的接口
 * 包括启动、暂停、续费、状态查询等功能
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth } from '@/lib/middleware/auth'
import { db } from '@/lib/database'
import { calculateSubscriptionEnd, SUBSCRIPTION_CONFIGS } from '@/lib/utils/factory-subscription'

/**
 * 从指定日期计算订阅结束时间（使用固定天数）
 */
function calculateSubscriptionEndFromDate(startDate: Date, subscriptionType: string): Date {
  const start = new Date(startDate)
  let daysToAdd: number

  switch (subscriptionType) {
    case 'trial':
      daysToAdd = 30
      break
    case 'monthly':
      daysToAdd = 30
      break
    case 'quarterly':
      daysToAdd = 90
      break
    case 'yearly':
      daysToAdd = 365
      break
    default:
      daysToAdd = 30
      break
  }

  return new Date(start.getTime() + daysToAdd * 24 * 60 * 60 * 1000)
}

/**
 * 获取工厂详细状态信息
 */
export const GET = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const url = new URL(request.url)
    const factoryId = url.searchParams.get('factoryId')

    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '工厂ID不能为空'
      }, { status: 400 })
    }

    // 获取工厂信息
    const factory = await db.getFactoryById(factoryId)
    if (!factory) {
      return NextResponse.json({
        success: false,
        error: '工厂不存在'
      }, { status: 404 })
    }

    // 检查工厂状态
    const statusCheck = await db.checkFactoryStatus(factory)

    // 获取工厂用户数量
    const userCount = await db.getFactoryUserCount(factoryId)

    // 获取最近登录记录
    const recentLogins = await db.getRecentLoginsByFactory(factoryId, 10)

    return NextResponse.json({
      success: true,
      data: {
        factory,
        statusCheck,
        userCount,
        recentLogins
      }
    })

  } catch (error) {
    console.error('❌ 获取工厂状态失败:', error)
    return NextResponse.json({
      success: false,
      error: '获取工厂状态失败'
    }, { status: 500 })
  }
})

/**
 * 管理工厂状态（启动、暂停、续费等）
 */
export const POST = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { 
      factoryId, 
      action, 
      reason,
      subscriptionType,
      subscriptionDuration,
      customEndDate
    } = body

    console.log('🔧 工厂管理操作:', {
      factoryId,
      action,
      reason,
      operatorId: user.id,
      operatorName: user.username
    })

    if (!factoryId || !action) {
      return NextResponse.json({
        success: false,
        error: '工厂ID和操作类型不能为空'
      }, { status: 400 })
    }

    // 获取工厂信息
    const factory = await db.getFactoryById(factoryId)
    if (!factory) {
      return NextResponse.json({
        success: false,
        error: '工厂不存在'
      }, { status: 404 })
    }

    const now = new Date()
    let updateData: any = {}
    let message = ''

    switch (action) {
      case 'suspend':
        // 暂停工厂
        updateData = {
          status: 'suspended',
          suspendedAt: now,
          suspendedReason: reason || '管理员手动暂停',
          suspendedBy: user.id
        }
        message = '工厂已暂停'
        break

      case 'activate':
        // 启动工厂 - 需要累计暂停时间
        let totalSuspendedMs = factory.totalSuspendedMs || 0n

        // 如果当前是暂停状态，计算这次暂停的时间
        if (factory.status === 'suspended' && factory.suspendedAt) {
          const suspendedAt = new Date(factory.suspendedAt)
          const suspendedDuration = now.getTime() - suspendedAt.getTime()
          totalSuspendedMs = BigInt(Number(totalSuspendedMs) + suspendedDuration)
        }

        updateData = {
          status: 'active',
          reactivatedAt: now,
          reactivatedBy: user.id,
          suspendedAt: null,
          suspendedReason: null,
          suspendedBy: null,
          totalSuspendedMs: totalSuspendedMs
        }
        message = '工厂已启动'
        break

      case 'renew':
        // 续费工厂
        if (!subscriptionType) {
          return NextResponse.json({
            success: false,
            error: '续费时必须指定订阅类型'
          }, { status: 400 })
        }

        let newEndDate: Date | null = null
        
        if (subscriptionType === 'permanent') {
          // 永久订阅
          updateData = {
            subscriptionType: 'permanent',
            isPermanent: true,
            subscriptionStart: factory.firstLoginAt || null, // 如果已登录过则保持原开始时间
            subscriptionEnd: null,
            status: 'active'
          }
        } else if (customEndDate) {
          // 自定义结束时间
          newEndDate = new Date(customEndDate)
          updateData = {
            subscriptionType,
            isPermanent: false,
            subscriptionStart: factory.firstLoginAt || null, // 等首次登录时才设置开始时间
            subscriptionEnd: newEndDate,
            status: 'active'
          }
        } else {
          // 🔧 修复续费逻辑：从当前时间或剩余时间基础上延长
          const now = new Date()
          let renewalStartTime: Date

          if (!factory.firstLoginAt) {
            // 如果还没登录，等首次登录时再设置具体时间
            renewalStartTime = now
            newEndDate = null // 等首次登录时计算
          } else {
            // 已登录的工厂，从当前时间或到期时间延长
            if (factory.subscriptionEnd) {
              const currentEndDate = new Date(factory.subscriptionEnd)
              // 如果还没过期，从到期时间延长；如果已过期，从当前时间延长
              renewalStartTime = currentEndDate > now ? currentEndDate : now
            } else {
              // 没有到期时间，从当前时间开始
              renewalStartTime = now
            }

            // 计算新的结束时间
            newEndDate = calculateSubscriptionEndFromDate(renewalStartTime, subscriptionType as any)
          }

          updateData = {
            subscriptionType,
            isPermanent: false,
            subscriptionStart: factory.firstLoginAt || null, // 保持原始开始时间
            subscriptionEnd: newEndDate,
            status: 'active'
          }
        }

        // 清除暂停状态
        updateData.suspendedAt = null
        updateData.suspendedReason = null
        updateData.suspendedBy = null
        updateData.reactivatedAt = now
        updateData.reactivatedBy = user.id

        message = `工厂订阅已续费（${subscriptionType}）`
        break

      case 'extend':
        // 延长订阅
        if (!subscriptionDuration || !factory.subscriptionEnd) {
          return NextResponse.json({
            success: false,
            error: '延长订阅需要指定天数且工厂必须有现有订阅'
          }, { status: 400 })
        }

        const currentEnd = new Date(factory.subscriptionEnd)
        const extendedEnd = new Date(currentEnd.getTime() + (subscriptionDuration * 24 * 60 * 60 * 1000))
        
        updateData = {
          subscriptionEnd: extendedEnd,
          status: 'active'
        }
        message = `工厂订阅已延长${subscriptionDuration}天`
        break

      default:
        return NextResponse.json({
          success: false,
          error: '不支持的操作类型'
        }, { status: 400 })
    }

    // 更新工厂信息
    const updatedFactory = await db.updateFactory(factoryId, updateData)

    // 记录操作日志
    await db.recordFactoryOperation({
      factoryId,
      operatorId: user.id,
      operatorName: user.username,
      action,
      reason,
      beforeStatus: factory.status,
      afterStatus: updateData.status || factory.status,
      operatedAt: now
    })

    console.log('✅ 工厂管理操作完成:', message)

    // 处理BigInt序列化问题
    const factoryForResponse = {
      ...updatedFactory,
      totalSuspendedMs: updatedFactory.totalSuspendedMs ? Number(updatedFactory.totalSuspendedMs) : 0
    }

    return NextResponse.json({
      success: true,
      message,
      factory: factoryForResponse
    })

  } catch (error) {
    console.error('❌ 工厂管理操作失败:', error)
    return NextResponse.json({
      success: false,
      error: '操作失败，请重试'
    }, { status: 500 })
  }
})

/**
 * 批量管理工厂状态
 */
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { factoryIds, action, reason } = body

    if (!factoryIds || !Array.isArray(factoryIds) || factoryIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: '工厂ID列表不能为空'
      }, { status: 400 })
    }

    if (!action) {
      return NextResponse.json({
        success: false,
        error: '操作类型不能为空'
      }, { status: 400 })
    }

    const results = []
    const now = new Date()

    for (const factoryId of factoryIds) {
      try {
        const factory = await db.getFactoryById(factoryId)
        if (!factory) {
          results.push({
            factoryId,
            success: false,
            error: '工厂不存在'
          })
          continue
        }

        let updateData: any = {}
        
        switch (action) {
          case 'suspend':
            updateData = {
              status: 'suspended',
              suspendedAt: now,
              suspendedReason: reason || '管理员批量暂停',
              suspendedBy: user.id
            }
            break
          case 'activate':
            updateData = {
              status: 'active',
              reactivatedAt: now,
              reactivatedBy: user.id,
              suspendedAt: null,
              suspendedReason: null,
              suspendedBy: null
            }
            break
          default:
            results.push({
              factoryId,
              success: false,
              error: '不支持的批量操作类型'
            })
            continue
        }

        await db.updateFactory(factoryId, updateData)
        
        // 记录操作日志
        await db.recordFactoryOperation({
          factoryId,
          operatorId: user.id,
          operatorName: user.username,
          action: `batch_${action}`,
          reason,
          beforeStatus: factory.status,
          afterStatus: updateData.status,
          operatedAt: now
        })

        results.push({
          factoryId,
          factoryName: factory.name,
          success: true,
          message: action === 'suspend' ? '已暂停' : '已启动'
        })

      } catch (error) {
        console.error(`❌ 批量操作工厂${factoryId}失败:`, error)
        results.push({
          factoryId,
          success: false,
          error: '操作失败'
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.length - successCount

    return NextResponse.json({
      success: failCount === 0,
      message: `批量操作完成：成功${successCount}个，失败${failCount}个`,
      results
    })

  } catch (error) {
    console.error('❌ 批量工厂管理操作失败:', error)
    return NextResponse.json({
      success: false,
      error: '批量操作失败'
    }, { status: 500 })
  }
})
