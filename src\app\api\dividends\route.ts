/**
 * 🇨🇳 风口云平台 - 分红管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 获取分红列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')

    if (!factoryId) {
      return NextResponse.json(
        { error: '工厂ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📞 获取分红列表请求:', factoryId)

    // 获取工厂分红列表
    const dividends = await db.getDividendsByFactoryId(factoryId)

    return NextResponse.json({
      success: true,
      dividends
    })

  } catch (error) {
    console.error('❌ 获取分红列表失败:', error)
    return NextResponse.json(
      { error: '获取分红列表失败' },
      { status: 500 }
    )
  }
}

// 创建分红
export async function POST(request: NextRequest) {
  try {
    const dividendData = await request.json()

    console.log('📞 创建分红请求:', dividendData.title)

    // 验证必需字段
    if (!dividendData.factoryId || !dividendData.title) {
      return NextResponse.json(
        { error: '工厂ID和分红标题不能为空' },
        { status: 400 }
      )
    }

    if (!dividendData.totalAmount || !dividendData.baseAmount || !dividendData.dividendRate) {
      return NextResponse.json(
        { error: '总分红金额、分红基数和分红比例不能为空' },
        { status: 400 }
      )
    }

    if (!dividendData.periodStart || !dividendData.periodEnd) {
      return NextResponse.json(
        { error: '分红期间开始和结束日期不能为空' },
        { status: 400 }
      )
    }

    // 创建分红
    const dividend = await db.createDividend(dividendData)

    if (!dividend) {
      return NextResponse.json(
        { error: '创建分红失败' },
        { status: 500 }
      )
    }

    console.log('✅ 分红创建成功:', dividend.id)

    return NextResponse.json({
      success: true,
      dividend
    })

  } catch (error) {
    console.error('❌ 创建分红失败:', error)
    
    // 处理特定错误
    if (error.message && error.message.includes('股东')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: '创建分红失败' },
      { status: 500 }
    )
  }
}

// 更新分红信息
export async function PUT(request: NextRequest) {
  try {
    const { id, ...updates } = await request.json()

    console.log('📞 更新分红信息请求:', id)

    if (!id) {
      return NextResponse.json(
        { error: '分红ID不能为空' },
        { status: 400 }
      )
    }

    // 更新分红信息
    const dividend = await db.updateDividend(id, updates)

    if (!dividend) {
      return NextResponse.json(
        { error: '更新分红信息失败' },
        { status: 500 }
      )
    }

    console.log('✅ 分红信息更新成功:', dividend.title)

    return NextResponse.json({
      success: true,
      dividend
    })

  } catch (error) {
    console.error('❌ 更新分红信息失败:', error)
    return NextResponse.json(
      { error: '更新分红信息失败' },
      { status: 500 }
    )
  }
}

// 删除分红
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const dividendId = searchParams.get('id')

    console.log('📞 删除分红请求:', dividendId)

    if (!dividendId) {
      return NextResponse.json(
        { error: '分红ID不能为空' },
        { status: 400 }
      )
    }

    // 删除分红
    const success = await db.deleteDividend(dividendId)

    if (!success) {
      return NextResponse.json(
        { error: '删除分红失败' },
        { status: 500 }
      )
    }

    console.log('✅ 分红删除成功')

    return NextResponse.json({
      success: true
    })

  } catch (error) {
    console.error('❌ 删除分红失败:', error)
    return NextResponse.json(
      { error: '删除分红失败' },
      { status: 500 }
    )
  }
}
