"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ProductCarousel } from "@/components/ui/carousel"
import { Building2, Factory, Users, BarChart3, Shield, Zap, ArrowRight, User2, Lock } from "lucide-react"
import { AuthService } from "@/lib/services/auth.service"

export default function Home() {
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      console.log('🔐 尝试登录工厂用户:', username)

      // 使用认证服务
      const result = await AuthService.loginFactory({ username, password })

      if (result.success && result.user) {
        console.log('✅ 工厂用户登录成功:', result.user.name)
        console.log('🔄 正在跳转到仪表板...')

        // 添加延迟确保状态完全保存
        setTimeout(() => {
          console.log('🔄 执行跳转到仪表板...')
          router.replace("/factory/dashboard")
        }, 500)
      } else {
        console.log('❌ 工厂用户登录失败:', result.error)

        // 根据错误类型显示不同的提示信息
        let errorMessage = result.error || "用户名或密码错误，请检查账号信息或联系总部开通账号"

        if (result.code) {
          switch (result.code) {
            case 'FACTORY_SUSPENDED':
              errorMessage = `🚫 ${result.title || '服务已暂停'}\n\n${result.error}\n\n💡 ${result.suggestion || '请联系管理员了解详情'}`
              break
            case 'FACTORY_EXPIRED':
              errorMessage = `⏰ ${result.title || '服务已到期'}\n\n${result.error}\n\n💡 ${result.suggestion || '请联系管理员续费'}`
              break
            case 'FACTORY_INACTIVE':
              errorMessage = `❓ ${result.title || '账号未激活'}\n\n${result.error}\n\n💡 ${result.suggestion || '请联系管理员激活账号'}`
              break
            case 'FACTORY_NOT_FOUND':
              errorMessage = `⚠️ ${result.title || '工厂信息异常'}\n\n${result.error}\n\n💡 ${result.suggestion || '请联系管理员检查配置'}`
              break
            default:
              errorMessage = result.error
          }
        }

        setError(errorMessage)
      }
    } catch (err) {
      console.error('❌ 登录请求失败:', err)
      setError("登录失败，请重试")
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50 to-indigo-50">
      {/* Header */}
      <header className="relative bg-white/98 backdrop-blur-sm shadow-lg border-b border-blue-100 overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/80 via-indigo-50/60 to-purple-50/80"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6 lg:py-8">
            {/* Logo区域 */}
            <div className="flex items-center space-x-5">
              <div className="relative group">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
                <div className="relative bg-gradient-to-br from-blue-600 to-indigo-700 p-4 rounded-xl shadow-lg transform group-hover:scale-105 transition duration-300">
                  <Building2 className="h-12 w-12 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-700 bg-clip-text text-transparent">
                  风口云平台
                </h1>
              </div>
            </div>


          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-indigo-400/10"></div>
        <div className="relative max-w-8xl mx-auto px-4 sm:px-6 lg:px-12 xl:px-16 text-center">
          <div className="mb-8 animate-fadeInUp">
            <h2 className="text-6xl md:text-7xl lg:text-8xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-8 leading-tight">
              <span className="text-5xl md:text-6xl lg:text-7xl animate-shimmer">风口加工厂智能管理系统</span>
            </h2>
            <p className="text-2xl md:text-3xl lg:text-4xl text-gray-700 mb-10 max-w-5xl mx-auto leading-relaxed font-medium">
              为全国中央空调风口加工厂提供一站式数字化解决方案
            </p>
            <div className="flex flex-wrap justify-center gap-6 md:gap-8 lg:gap-12 text-gray-500 animate-fadeInUp max-w-6xl mx-auto" style={{animationDelay: '0.3s'}}>
              <div className="flex items-center space-x-4 bg-white/60 backdrop-blur-sm px-8 py-4 rounded-full hover:bg-white/80 transition-all duration-300 hover:scale-105 text-lg md:text-xl font-medium">
                <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
                <span>智能录单</span>
              </div>
              <div className="flex items-center space-x-4 bg-white/60 backdrop-blur-sm px-8 py-4 rounded-full hover:bg-white/80 transition-all duration-300 hover:scale-105 text-lg md:text-xl font-medium">
                <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
                <span>快速导出</span>
              </div>
              <div className="flex items-center space-x-4 bg-white/60 backdrop-blur-sm px-8 py-4 rounded-full hover:bg-white/80 transition-all duration-300 hover:scale-105 text-lg md:text-xl font-medium">
                <div className="w-4 h-4 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
                <span>客户管理</span>
              </div>
              <div className="flex items-center space-x-4 bg-white/60 backdrop-blur-sm px-8 py-4 rounded-full hover:bg-white/80 transition-all duration-300 hover:scale-105 text-lg md:text-xl font-medium">
                <div className="w-4 h-4 bg-orange-500 rounded-full animate-pulse" style={{animationDelay: '1.5s'}}></div>
                <span>数据分析</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Entrances */}
      <section className="py-20 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">


          {/* 主要登录区域 - 明亮现代设计 */}
          <div className="max-w-4xl mx-auto mb-20">
            <Card className="group hover:shadow-2xl transition-all duration-500 border-0 bg-white shadow-lg overflow-hidden relative">
              {/* 装饰性背景 */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-white to-indigo-100"></div>
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400"></div>

              <div className="relative p-10 lg:p-14">
                {/* 登录表单 */}
                <div className="max-w-2xl mx-auto">
                  <form onSubmit={handleSubmit} className="space-y-8">
                    <div className="grid md:grid-cols-2 gap-8">
                      <div className="space-y-4">
                        <label htmlFor="username" className="text-xl font-semibold text-gray-700 block">
                          登录账号
                        </label>
                        <div className="relative">
                          <input
                            id="username"
                            type="text"
                            placeholder="请输入工厂账号"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            required
                            className="flex h-16 w-full rounded-2xl border-2 border-blue-200 bg-white px-6 py-4 text-lg ring-offset-background file:border-0 file:bg-transparent file:text-lg file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2 focus-visible:border-blue-400 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-blue-300 hover:bg-blue-50 shadow-sm hover:shadow-md"
                          />
                          <div className="absolute inset-y-0 right-4 flex items-center">
                            <User2 className="h-5 w-5 text-gray-400" />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <label htmlFor="password" className="text-xl font-semibold text-gray-700 block">
                          登录密码
                        </label>
                        <div className="relative">
                          <input
                            id="password"
                            type="password"
                            placeholder="请输入登录密码"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                            className="flex h-16 w-full rounded-2xl border-2 border-blue-200 bg-white px-6 py-4 text-lg ring-offset-background file:border-0 file:bg-transparent file:text-lg file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2 focus-visible:border-blue-400 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-blue-300 hover:bg-blue-50 shadow-sm hover:shadow-md"
                          />
                          <div className="absolute inset-y-0 right-4 flex items-center">
                            <Lock className="h-5 w-5 text-gray-400" />
                          </div>
                        </div>
                      </div>
                    </div>

                    {error && (
                      <div className="text-sm text-red-600 bg-red-50 p-4 rounded-lg border border-red-200 whitespace-pre-line">
                        {error}
                      </div>
                    )}

                    <div className="pt-6">
                      <Button
                        type="submit"
                        disabled={isLoading}
                        className="w-full h-18 text-xl font-bold bg-gradient-to-r from-blue-500 via-blue-400 to-indigo-500 hover:from-blue-600 hover:via-blue-500 hover:to-indigo-600 shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl transform hover:scale-[1.02] group disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                      >
                        <div className="flex items-center justify-center space-x-3">
                          <Factory className="h-6 w-6 group-hover:rotate-12 transition-transform duration-300" />
                          <span>{isLoading ? "登录中..." : "登录工厂系统"}</span>
                          <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                        </div>
                      </Button>
                    </div>
                  </form>



                  {/* 其他登录方式 */}
                  <div className="mt-12 pt-8 border-t border-blue-100">
                    <p className="text-sm text-gray-600 text-center mb-8 font-medium">其他登录方式</p>
                    <div className="flex justify-center space-x-6">
                      <Button
                        variant="outline"
                        size="lg"
                        className="px-8 py-4 text-base font-semibold hover:bg-red-50 hover:border-red-400 hover:text-red-600 transition-all duration-300 rounded-xl border-2 border-red-200 shadow-sm hover:shadow-md transform hover:scale-105"
                        asChild
                      >
                        <Link href="/admin/login">
                          <Shield className="h-5 w-5 mr-3" />
                          总部管理
                        </Link>
                      </Button>
                      <Button
                        variant="outline"
                        size="lg"
                        className="px-8 py-4 text-base font-semibold hover:bg-green-50 hover:border-green-400 hover:text-green-600 transition-all duration-300 rounded-xl border-2 border-green-200 shadow-sm hover:shadow-md transform hover:scale-105"
                        asChild
                      >
                        <Link href="/client">
                          <Users className="h-5 w-5 mr-3" />
                          客户查询
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* 产品展示区域 */}
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-2">产品工艺展示</h3>
              <p className="text-gray-600">了解我们的专业产品和精湛工艺</p>
            </div>
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 shadow-sm">
              <ProductCarousel
                title=""
                className="w-full max-w-2xl mx-auto"
              />
            </div>
          </div>

          {/* 移动端产品轮播 */}
          <div className="lg:hidden mt-12">
            <ProductCarousel title="产品与工艺展示" />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gradient-to-br from-blue-50 to-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/8 to-purple-400/8"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4">
              平台核心优势
            </h3>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              专为风口加工厂设计的智能录单系统，让订单管理更简单高效
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-10">
            <div className="group text-center hover:transform hover:scale-105 transition-all duration-500 animate-fadeInUp">
              <div className="mx-auto bg-gradient-to-br from-blue-500 to-blue-600 p-6 rounded-2xl w-20 h-20 flex items-center justify-center mb-6 shadow-lg group-hover:shadow-2xl group-hover:animate-float transition-all duration-300">
                <BarChart3 className="h-10 w-10 text-white group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h4 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">智能录单</h4>
              <p className="text-gray-600 text-lg leading-relaxed group-hover:text-gray-700 transition-colors">
                表格式批量录入，支持Excel导入，智能识别风口规格，大幅提升录单效率
              </p>
            </div>

            <div className="group text-center hover:transform hover:scale-105 transition-all duration-500 animate-fadeInUp" style={{animationDelay: '0.1s'}}>
              <div className="mx-auto bg-gradient-to-br from-green-500 to-green-600 p-6 rounded-2xl w-20 h-20 flex items-center justify-center mb-6 shadow-lg group-hover:shadow-2xl group-hover:animate-float transition-all duration-300">
                <Zap className="h-10 w-10 text-white group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h4 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors">数据导出</h4>
              <p className="text-gray-600 text-lg leading-relaxed group-hover:text-gray-700 transition-colors">
                支持Excel格式导出，美化表格样式，专业报价单生成，满足各种业务需求
              </p>
            </div>

            <div className="group text-center hover:transform hover:scale-105 transition-all duration-500 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
              <div className="mx-auto bg-gradient-to-br from-purple-500 to-purple-600 p-6 rounded-2xl w-20 h-20 flex items-center justify-center mb-6 shadow-lg group-hover:shadow-2xl group-hover:animate-float transition-all duration-300">
                <Shield className="h-10 w-10 text-white group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h4 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors">客户管理</h4>
              <p className="text-gray-600 text-lg leading-relaxed group-hover:text-gray-700 transition-colors">
                完整的客户档案管理，推荐奖励系统，订单历史追踪，提升客户满意度
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-4 mb-6">
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
                <Building2 className="h-8 w-8 text-white" />
              </div>
              <span className="text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                风口云平台
              </span>
            </div>
            <p className="text-gray-300 text-lg mb-8 max-w-2xl mx-auto leading-relaxed">
              专业的中央空调风口加工厂SaaS管理系统，为全国加工厂提供数字化转型解决方案
            </p>

            <div className="border-t border-gray-700 pt-6">
              <p className="text-sm text-gray-500">
                © 2025风口云平台. 保留所有权利. | 专业 · 安全 · 高效
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
