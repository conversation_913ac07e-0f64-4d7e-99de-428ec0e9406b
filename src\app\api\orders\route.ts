/**
 * 🇨🇳 风口云平台 - 订单管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminOrFactoryAuth, validateFactoryAccess } from '@/lib/middleware/auth'

// 获取订单列表
export const GET = withAdminOrFactoryAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')
    const clientId = searchParams.get('clientId')

    // 验证工厂访问权限
    if (factoryId) {
      const validatedFactoryId = validateFactoryAccess(request, user)
      if (!validatedFactoryId || validatedFactoryId !== factoryId) {
        console.log('❌ 订单工厂访问权限验证失败:', {
          userType: user.userType,
          userFactoryId: user.factoryId,
          requestFactoryId: factoryId,
          validatedFactoryId
        })
        return NextResponse.json(
          { error: '无权访问该工厂数据' },
          { status: 403 }
        )
      }

      console.log('✅ 订单工厂访问权限验证通过:', {
        userType: user.userType,
        factoryId: validatedFactoryId
      })

      const orders = await db.getOrdersByFactoryId(factoryId)
      return NextResponse.json({ success: true, orders })
    }

    if (clientId) {
      // 对于客户订单，需要确保客户属于用户的工厂
      const orders = await db.getOrdersByClientId(clientId)
      // 过滤只属于用户工厂的订单
      const filteredOrders = orders.filter((order: unknown) =>
        user.userType === 'admin' || order.factoryId === user.factoryId
      )
      return NextResponse.json({ success: true, orders: filteredOrders })
    }

    return NextResponse.json(
      { error: '缺少必要参数' },
      { status: 400 }
    )

  } catch (error) {
    console.error('❌ 获取订单失败:', error)
    return NextResponse.json(
      { error: '获取订单失败' },
      { status: 500 }
    )
  }
})

// 创建订单
export const POST = withAdminOrFactoryAuth(async (request: NextRequest, user) => {
  try {
    const orderData = await request.json()

    console.log('📦 接收到订单创建请求:')
    console.log('  - factoryId:', orderData.factoryId)
    console.log('  - clientId:', orderData.clientId)
    console.log('  - clientName:', orderData.clientName)
    console.log('  - items数量:', orderData.items?.length)
    console.log('  - 用户:', user.username)
    console.log('  - 完整数据:', JSON.stringify(orderData, null, 2))

    // 验证必要字段
    if (!orderData.factoryId) {
      console.error('❌ 缺少工厂ID')
      return NextResponse.json(
        { error: '缺少工厂ID' },
        { status: 400 }
      )
    }

    // 验证工厂访问权限（管理员可以在任何工厂创建订单）
    if (user.userType !== 'admin') {
      const validatedFactoryId = validateFactoryAccess(request, user)
      if (!validatedFactoryId || validatedFactoryId !== orderData.factoryId) {
        return NextResponse.json(
          { error: '无权在该工厂创建订单' },
          { status: 403 }
        )
      }
    }

    // 验证客户信息：要么有clientId（现有客户），要么有clientName（新客户）
    if (!orderData.clientId && !orderData.clientName) {
      console.error('❌ 缺少客户信息')
      console.error('  - clientId:', orderData.clientId)
      console.error('  - clientName:', orderData.clientName)
      return NextResponse.json(
        { error: '缺少客户信息，请选择现有客户或填写新客户信息' },
        { status: 400 }
      )
    }

    // 验证订单项目
    if (!orderData.items || !Array.isArray(orderData.items) || orderData.items.length === 0) {
      console.error('❌ 缺少订单项目')
      console.error('  - items:', orderData.items)
      console.error('  - 是否为数组:', Array.isArray(orderData.items))
      console.error('  - 数组长度:', orderData.items?.length)
      return NextResponse.json(
        { error: '订单必须包含至少一个产品项目' },
        { status: 400 }
      )
    }

    console.log('✅ 订单数据验证通过，开始创建订单...')

    // 调试：打印用户信息
    console.log('🔍 当前用户信息:', {
      userId: user.userId,
      username: user.username,
      name: user.name,
      userType: user.userType,
      factoryId: user.factoryId
    })

    // 添加创建者信息
    // ⚠️ 重要：createdBy 必须存储用户的ID（外键约束要求），不能是用户名
    orderData.createdBy = user.userId  // 存储用户ID（外键约束要求）
    orderData.createdByName = user.name  // 存储用户姓名（用于显示）

    console.log('📝 设置录单员信息 (修复后):', {
      createdBy: orderData.createdBy,
      createdByName: orderData.createdByName,
      userInfo: {
        userId: user.userId,
        username: user.username,
        name: user.name
      }
    })

    const order = await db.createOrder(orderData)

    if (!order) {
      console.error('❌ 数据库创建订单失败')
      return NextResponse.json(
        { error: '创建订单失败' },
        { status: 500 }
      )
    }

    console.log('✅ 订单创建成功:', order.id)

    return NextResponse.json({
      success: true,
      order
    })

  } catch (error) {
    console.error('❌ 创建订单失败:', error)
    console.error('错误详情:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : 'Unknown'
    })
    return NextResponse.json(
      { error: `创建订单失败: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    )
  }
})
