'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'

interface DiagnosisResult {
  clientInfo: unknown
  referredClients: unknown[]
  databaseReferredClients: unknown[]
  rewardInfo: {
    availableReward: number
    referralReward: number
    pendingReward: number
    referralCount: number
  }
  apiResponse: unknown
}

export default function DiagnoseReferralIssuePage() {
  const [clientId, setClientId] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<DiagnosisResult | null>(null)
  const [error, setError] = useState('')

  const diagnoseClient = async () => {
    if (!clientId.trim()) {
      setError('请输入客户ID')
      return
    }

    setLoading(true)
    setError('')
    setResult(null)

    try {
      console.log('🔍 开始诊断客户推荐问题:', clientId)

      // 1. 获取客户基本信息
      const clientInfo = await db.getClientById(clientId)
      if (!clientInfo) {
        throw new Error('客户不存在')
      }

      console.log('👤 客户信息:', clientInfo)

      // 2. 通过API获取推荐客户
      const referredClients = await db.getReferredClients(clientId)
      console.log('👥 API返回的推荐客户:', referredClients)

      // 3. 直接从数据库查询推荐关系
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        throw new Error('无法获取工厂ID')
      }

      const allClients = await db.getClientsByFactoryId(factoryId)
      const databaseReferredClients = allClients.filter(c => c.referrerId === clientId)
      console.log('🗄️ 数据库中的推荐客户:', databaseReferredClients)

      // 4. 直接调用API查看原始响应
      const apiResponse = await fetch(`/api/clients/referred?clientId=${clientId}&public=true`)
      const apiData = await apiResponse.json()
      console.log('🌐 API原始响应:', apiData)

      // 5. 整理奖励信息
      const rewardInfo = {
        availableReward: clientInfo.availableReward || 0,
        referralReward: clientInfo.referralReward || 0,
        pendingReward: clientInfo.pendingReward || 0,
        referralCount: clientInfo.referralCount || 0
      }

      setResult({
        clientInfo,
        referredClients,
        databaseReferredClients,
        rewardInfo,
        apiResponse: apiData
      })

    } catch (err) {
      console.error('❌ 诊断失败:', err)
      setError(err instanceof Error ? err.message : '诊断失败')
    } finally {
      setLoading(false)
    }
  }

  const formatAmount = (amount: number | undefined) => {
    return `¥${(amount || 0).toFixed(2)}`
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🔍 推荐客户问题诊断工具</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="clientId">客户ID</Label>
            <Input
              id="clientId"
              value={clientId}
              onChange={(e) => setClientId(e.target.value)}
              placeholder="输入要诊断的客户ID"
            />
          </div>
          <Button onClick={diagnoseClient} disabled={loading}>
            {loading ? '诊断中...' : '开始诊断'}
          </Button>
          {error && (
            <div className="text-red-600 bg-red-50 p-3 rounded">
              ❌ {error}
            </div>
          )}
        </CardContent>
      </Card>

      {result && (
        <div className="space-y-6">
          {/* 客户基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>👤 客户基本信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <strong>姓名:</strong> {result.clientInfo.name}
                </div>
                <div>
                  <strong>手机:</strong> {result.clientInfo.phone}
                </div>
                <div>
                  <strong>工厂ID:</strong> {result.clientInfo.factoryId}
                </div>
                <div>
                  <strong>推荐人ID:</strong> {result.clientInfo.referrerId || '无'}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 奖励信息 */}
          <Card>
            <CardHeader>
              <CardTitle>💰 奖励信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatAmount(result.rewardInfo.availableReward)}
                  </div>
                  <div className="text-sm text-gray-600">可用奖励</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded">
                  <div className="text-2xl font-bold text-green-600">
                    {formatAmount(result.rewardInfo.referralReward)}
                  </div>
                  <div className="text-sm text-gray-600">总推荐奖励</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded">
                  <div className="text-2xl font-bold text-orange-600">
                    {formatAmount(result.rewardInfo.pendingReward)}
                  </div>
                  <div className="text-sm text-gray-600">待结算奖励</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded">
                  <div className="text-2xl font-bold text-purple-600">
                    {result.rewardInfo.referralCount}
                  </div>
                  <div className="text-sm text-gray-600">推荐数量</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* API返回的推荐客户 */}
          <Card>
            <CardHeader>
              <CardTitle>🌐 API返回的推荐客户 ({result.referredClients.length}个)</CardTitle>
            </CardHeader>
            <CardContent>
              {result.referredClients.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  API未返回推荐客户
                </div>
              ) : (
                <div className="space-y-3">
                  {result.referredClients.map((client, index) => (
                    <div key={client.id || index} className="border p-3 rounded">
                      <div className="font-medium">{client.name}</div>
                      <div className="text-sm text-gray-600">
                        手机: {client.phone} | 订单: {client.totalOrders || 0}个 | 
                        金额: {formatAmount(client.totalAmount)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 数据库中的推荐客户 */}
          <Card>
            <CardHeader>
              <CardTitle>🗄️ 数据库中的推荐客户 ({result.databaseReferredClients.length}个)</CardTitle>
            </CardHeader>
            <CardContent>
              {result.databaseReferredClients.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  数据库中无推荐客户记录
                </div>
              ) : (
                <div className="space-y-3">
                  {result.databaseReferredClients.map((client, index) => (
                    <div key={client.id || index} className="border p-3 rounded">
                      <div className="font-medium">{client.name}</div>
                      <div className="text-sm text-gray-600">
                        手机: {client.phone} | 推荐人ID: {client.referrerId}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* API原始响应 */}
          <Card>
            <CardHeader>
              <CardTitle>📡 API原始响应</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(result.apiResponse, null, 2)}
              </pre>
            </CardContent>
          </Card>

          {/* 问题分析 */}
          <Card>
            <CardHeader>
              <CardTitle>🔍 问题分析</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className={`p-3 rounded ${
                  result.rewardInfo.availableReward > 0 ? 'bg-yellow-50 border border-yellow-200' : 'bg-green-50 border border-green-200'
                }`}>
                  <strong>奖励状态:</strong> {
                    result.rewardInfo.availableReward > 0 
                      ? `有可用奖励 ${formatAmount(result.rewardInfo.availableReward)}` 
                      : '无可用奖励'
                  }
                </div>
                
                <div className={`p-3 rounded ${
                  result.referredClients.length === 0 ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'
                }`}>
                  <strong>API推荐客户:</strong> {
                    result.referredClients.length === 0 
                      ? '未查询到推荐客户' 
                      : `查询到 ${result.referredClients.length} 个推荐客户`
                  }
                </div>
                
                <div className={`p-3 rounded ${
                  result.databaseReferredClients.length === 0 ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'
                }`}>
                  <strong>数据库推荐客户:</strong> {
                    result.databaseReferredClients.length === 0 
                      ? '数据库中无推荐关系记录' 
                      : `数据库中有 ${result.databaseReferredClients.length} 个推荐客户`
                  }
                </div>

                {result.rewardInfo.availableReward > 0 && result.referredClients.length === 0 && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded">
                    <strong>⚠️ 发现问题:</strong> 客户有可用奖励但查询不到推荐客户，可能的原因：
                    <ul className="mt-2 ml-4 list-disc">
                      <li>推荐关系数据不一致</li>
                      <li>API查询逻辑有问题</li>
                      <li>数据库中的推荐关系被删除但奖励未清零</li>
                    </ul>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
