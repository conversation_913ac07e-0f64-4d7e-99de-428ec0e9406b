<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硅基流动修复验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover { background: #1976D2; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #4CAF50; background: #e8f5e8; }
        .error { border-color: #f44336; background: #ffe8e8; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 硅基流动修复验证</h1>
        <p>验证 "response is not defined" 错误修复效果</p>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 4px; margin: 15px 0;">
            <strong>🎯 修复内容：</strong><br>
            • 修复了重试机制中的 response 变量未定义问题<br>
            • 增加了超时时间到60秒<br>
            • 添加了专门的连接测试方法<br>
            • 改进了错误处理逻辑
        </div>
        
        <button class="button" onclick="testBasicConnection()">🌐 基础连接测试</button>
        <button class="button" onclick="testWithRetry()">🔄 重试机制测试</button>
        <button class="button" onclick="testSimpleAPI()">⚡ 简单API测试</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_KEY = 'sk-szczomdkrprhzlzuzwlblenwfvvuuuyxbxnjgmrcetorftth';
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}] ${title}</strong>\n${content}`;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function testBasicConnection() {
            addResult('🌐 基础连接测试', '测试到硅基流动服务器的基本连接...', 'info');
            
            const startTime = performance.now();
            
            try {
                const response = await fetch('https://api.siliconflow.cn/v1/models', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    signal: AbortSignal.timeout(10000) // 10秒超时
                });
                
                const duration = performance.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(
                        '✅ 基础连接成功',
                        `响应时间: ${duration.toFixed(2)}ms
状态码: ${response.status}
可用模型: ${data.data?.length || 'N/A'} 个
连接状态: 正常`,
                        'success'
                    );
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ 基础连接失败',
                        `状态码: ${response.status}
响应时间: ${duration.toFixed(2)}ms
错误信息: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const duration = performance.now() - startTime;
                addResult(
                    '❌ 基础连接异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}
类型: ${error.name}`,
                    'error'
                );
            }
        }
        
        async function testWithRetry() {
            addResult('🔄 重试机制测试', '测试修复后的重试逻辑...', 'info');
            
            const startTime = performance.now();
            
            // 模拟重试逻辑
            let response;
            const maxRetries = 2;
            
            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    addResult('🔄 重试信息', `第 ${attempt + 1} 次尝试...`, 'info');
                    
                    response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${API_KEY}`
                        },
                        body: JSON.stringify({
                            model: 'deepseek-ai/DeepSeek-V3',
                            messages: [{ role: 'user', content: '测试重试机制' }],
                            max_tokens: 5,
                            temperature: 0
                        }),
                        signal: AbortSignal.timeout(15000) // 15秒超时
                    });
                    
                    // 成功获得响应，跳出循环
                    break;
                    
                } catch (error) {
                    if (attempt === maxRetries) {
                        // 最后一次尝试失败
                        throw error;
                    }
                    
                    // 等待后重试
                    const waitTime = Math.pow(2, attempt) * 1000;
                    addResult('⏳ 等待重试', `等待 ${waitTime}ms 后重试...`, 'warning');
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                }
            }
            
            const duration = performance.now() - startTime;
            
            // 检查 response 是否定义
            if (!response) {
                addResult(
                    '❌ 重试测试失败',
                    `所有重试都失败了
总耗时: ${duration.toFixed(2)}ms
错误: 未能获得有效响应`,
                    'error'
                );
                return;
            }
            
            if (response.ok) {
                const data = await response.json();
                addResult(
                    '✅ 重试机制正常',
                    `总耗时: ${duration.toFixed(2)}ms
响应状态: ${response.status}
Token使用: ${data.usage?.total_tokens || 'N/A'}
重试机制: 工作正常`,
                    'success'
                );
            } else {
                const errorText = await response.text();
                addResult(
                    '❌ 重试测试失败',
                    `状态码: ${response.status}
总耗时: ${duration.toFixed(2)}ms
错误信息: ${errorText}`,
                    'error'
                );
            }
        }
        
        async function testSimpleAPI() {
            addResult('⚡ 简单API测试', '测试最基本的API调用...', 'info');
            
            const startTime = performance.now();
            
            try {
                const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'deepseek-ai/DeepSeek-V3',
                        messages: [{ role: 'user', content: '你好' }],
                        max_tokens: 10,
                        temperature: 0
                    }),
                    signal: AbortSignal.timeout(30000) // 30秒超时
                });
                
                const duration = performance.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0]?.message?.content;
                    
                    addResult(
                        '✅ 简单API测试成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
使用模型: ${data.model}
AI回复: ${content}
Token使用: ${data.usage?.total_tokens || 'N/A'}
状态: API工作正常`,
                        'success'
                    );
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ 简单API测试失败',
                        `状态码: ${response.status}
响应时间: ${duration.toFixed(2)}ms
错误信息: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const duration = performance.now() - startTime;
                addResult(
                    '❌ 简单API测试异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}
类型: ${error.name}`,
                    'error'
                );
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult(
                '📋 修复验证说明',
                `API Key: ${API_KEY.substring(0, 20)}...
目标: 验证 "response is not defined" 错误已修复
测试内容: 基础连接、重试机制、简单API调用

点击上方按钮开始验证！`,
                'info'
            );
        };
    </script>
</body>
</html>
