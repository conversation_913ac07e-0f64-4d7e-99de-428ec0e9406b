/**
 * 🇨🇳 风口云平台 - 未授权访问页面
 */

'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Shield, ArrowLeft, Home } from 'lucide-react'
import { AuthService } from '@/lib/services/auth.service'

export default function UnauthorizedPage() {
  const router = useRouter()

  const handleGoBack = () => {
    router.back()
  }

  const handleGoHome = () => {
    const role = AuthService.getCurrentRole()
    
    if (role === 'admin') {
      router.push('/admin/dashboard')
    } else if (['owner', 'manager', 'operator'].includes(role || '')) {
      router.push('/factory/dashboard')
    } else {
      router.push('/')
    }
  }

  const handleLogout = async () => {
    await AuthService.logout()
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Icon and Title */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-red-600 p-4 rounded-full">
              <Shield className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">访问被拒绝</h1>
          <p className="text-gray-600">您没有权限访问此页面</p>
        </div>

        {/* Error Card */}
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-xl text-red-600">权限不足</CardTitle>
            <CardDescription>
              抱歉，您当前的账号权限不足以访问此页面。
              请联系管理员获取相应权限，或使用有权限的账号登录。
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Action Buttons */}
            <div className="space-y-3">
              <Button 
                onClick={handleGoBack} 
                variant="outline" 
                className="w-full"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回上一页
              </Button>
              
              <Button 
                onClick={handleGoHome} 
                className="w-full"
              >
                <Home className="h-4 w-4 mr-2" />
                返回首页
              </Button>
              
              <Button 
                onClick={handleLogout} 
                variant="destructive" 
                className="w-full"
              >
                退出登录
              </Button>
            </div>

            {/* Help Information */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">需要帮助？</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 确认您使用的是正确的账号</li>
                <li>• 检查您的账号是否有相应权限</li>
                <li>• 联系系统管理员申请权限</li>
                <li>• 如果是工厂用户，请联系工厂管理员</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Footer Links */}
        <div className="text-center mt-6 space-y-2">
          <Link 
            href="/" 
            className="text-sm text-gray-600 hover:text-gray-900"
          >
            返回平台首页
          </Link>
          <div className="text-xs text-gray-500">
            如有问题，请联系技术支持
          </div>
        </div>
      </div>
    </div>
  )
}
