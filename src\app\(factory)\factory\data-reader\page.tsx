'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Database, Eye } from 'lucide-react'
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { safeAmountFormat } from '@/lib/utils/number-utils'

interface ClientData {
  id: string
  name: string
  phone: string
  referrerId?: string
  referrerName?: string
  currentReward: number
  shouldBeReward: number
  orders: Array<{
    id: string
    orderNumber?: string
    totalAmount: number
    createdAt: Date
  }>
  referredClients: Array<{
    id: string
    name: string
    phone: string
    totalAmount: number
    orderCount: number
  }>
}

export default function DataReaderPage() {
  const [loading, setLoading] = useState(false)
  const [clientsData, setClientsData] = useState<ClientData[]>([])

  const loadData = async () => {
    try {
      setLoading(true)
      
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        alert('无法获取工厂ID')
        return
      }

      console.log('📊 开始读取客户数据...')

      // 获取所有客户和订单
      const clients = await db.getClientsByFactoryId(factoryId)
      const orders = await db.getOrdersByFactoryId(factoryId)

      console.log(`👥 客户数量: ${clients.length}`)
      console.log(`📦 订单数量: ${orders.length}`)

      const processedData: ClientData[] = []

      // 处理每个客户
      for (const client of clients) {
        // 获取客户的订单
        const clientOrders = orders.filter((order: unknown) => order.clientId === client.id)
        
        // 获取客户推荐的其他客户
        const referredClients = clients.filter((c: unknown) => c.referrerId === client.id)
        
        // 计算推荐奖励
        let shouldBeReward = 0
        const referredClientsData: unknown[] = []
        
        for (const referredClient of referredClients) {
          const referredOrders = orders.filter((order: unknown) => order.clientId === referredClient.id)
          const totalAmount = referredOrders.reduce((sum: number, order: unknown) => sum + order.totalAmount, 0)
          
          // 简单按2%计算（这里先用简单计算，后面会用正确的计算逻辑）
          shouldBeReward += totalAmount * 0.02
          
          referredClientsData.push({
            id: referredClient.id,
            name: referredClient.name,
            phone: referredClient.phone,
            totalAmount,
            orderCount: referredOrders.length
          })
        }

        // 获取当前数据库中的奖励
        const currentReward = typeof client.referralReward === 'number' ? client.referralReward : 
                             typeof client.referralReward === 'object' && client.referralReward ? 
                             parseFloat(client.referralReward.toString()) : 0

        // 只显示有推荐关系的客户
        if (referredClients.length > 0 || client.referrerId) {
          processedData.push({
            id: client.id,
            name: client.name,
            phone: client.phone,
            referrerId: client.referrerId,
            referrerName: client.referrerName,
            currentReward: Math.round(currentReward * 100) / 100,
            shouldBeReward: Math.round(shouldBeReward * 100) / 100,
            orders: clientOrders.map((order: unknown) => ({
              id: order.id,
              orderNumber: order.orderNumber,
              totalAmount: order.totalAmount,
              createdAt: order.createdAt
            })),
            referredClients: referredClientsData
          })
        }
      }

      setClientsData(processedData)
      console.log('✅ 数据读取完成')

    } catch (error) {
      console.error('❌ 读取数据失败:', error)
      alert('读取数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [])

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">客户数据读取</h1>
            <p className="text-gray-600">查看当前客户的推荐奖励数据</p>
          </div>
          <Button onClick={loadData} disabled={loading}>
            {loading ? (
              <>
                <Database className="h-4 w-4 mr-2 animate-spin" />
                读取中...
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                刷新数据
              </>
            )}
          </Button>
        </div>

        <div className="space-y-6">
          {clientsData.map((client) => (
            <Card key={client.id} className={`${
              Math.abs(client.currentReward - client.shouldBeReward) > 0.01 ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
            }`}>
              <CardHeader>
                <CardTitle className="flex justify-between items-center">
                  <span>{client.name} ({client.phone})</span>
                  <div className="text-right">
                    {Math.abs(client.currentReward - client.shouldBeReward) > 0.01 ? (
                      <span className="text-red-600 font-bold">❌ 数据错误</span>
                    ) : (
                      <span className="text-green-600 font-bold">✅ 数据正确</span>
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 奖励信息 */}
                  <div>
                    <h4 className="font-semibold mb-3">奖励信息</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>数据库中的奖励:</span>
                        <span className="font-mono">¥{safeAmountFormat(client.currentReward)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>应该的奖励:</span>
                        <span className="font-mono">¥{safeAmountFormat(client.shouldBeReward)}</span>
                      </div>
                      <div className="flex justify-between font-bold">
                        <span>差额:</span>
                        <span className={`font-mono ${
                          Math.abs(client.currentReward - client.shouldBeReward) > 0.01 ? 'text-red-600' : 'text-green-600'
                        }`}>
                          ¥{safeAmountFormat(Math.abs(client.currentReward - client.shouldBeReward))}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 推荐关系 */}
                  <div>
                    <h4 className="font-semibold mb-3">推荐关系</h4>
                    {client.referrerId && (
                      <div className="mb-2">
                        <span className="text-sm text-gray-600">推荐人: {client.referrerName || '未知'}</span>
                      </div>
                    )}
                    {client.referredClients.length > 0 && (
                      <div>
                        <span className="text-sm text-gray-600">推荐了 {client.referredClients.length} 个客户:</span>
                        <div className="mt-2 space-y-1">
                          {client.referredClients.map((referred) => (
                            <div key={referred.id} className="text-sm bg-white p-2 rounded border">
                              <div className="flex justify-between">
                                <span>{referred.name}</span>
                                <span>¥{safeAmountFormat(referred.totalAmount)}</span>
                              </div>
                              <div className="text-xs text-gray-500">
                                {referred.orderCount} 个订单 → 奖励: ¥{safeAmountFormat(referred.totalAmount * 0.02)}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 客户自己的订单 */}
                {client.orders.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">客户订单 ({client.orders.length} 个)</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {client.orders.map((order) => (
                        <div key={order.id} className="text-sm bg-white p-2 rounded border">
                          <div className="font-medium">{order.orderNumber || order.id}</div>
                          <div>¥{safeAmountFormat(order.totalAmount)}</div>
                          <div className="text-xs text-gray-500">
                            {new Date(order.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {clientsData.length === 0 && !loading && (
          <Card>
            <CardContent className="p-8 text-center">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
              <p className="text-gray-600">
                没有找到有推荐关系的客户
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
