/**
 * 🧹 认证状态清理工具
 * 
 * 用于彻底清理所有认证相关的状态和存储
 */

import { useAuthStore } from '@/lib/store/auth'

/**
 * 彻底清理所有认证状态
 */
export function clearAllAuthState(): void {
  console.log('🧹 开始彻底清理所有认证状态...')
  
  // 1. 重置 Zustand 状态
  const { logout } = useAuthStore.getState()
  logout()
  
  // 2. 清理所有可能的 localStorage 项
  if (typeof window !== 'undefined') {
    const keysToRemove = [
      'accessToken',
      'refreshToken', 
      'user',
      'role',
      'factoryId',
      'auth-storage',
      'token',
      'userInfo',
      'authState',
      'loginState'
    ]
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
    })
    
    // 3. 清理所有以 'auth' 开头的键
    const allKeys = Object.keys(localStorage)
    allKeys.forEach(key => {
      if (key.toLowerCase().includes('auth') || 
          key.toLowerCase().includes('token') ||
          key.toLowerCase().includes('user') ||
          key.toLowerCase().includes('session')) {
        localStorage.removeItem(key)
      }
    })
  }
  
  // 4. 清理 sessionStorage
  if (typeof window !== 'undefined') {
    const sessionKeysToRemove = [
      'accessToken',
      'refreshToken',
      'user',
      'role',
      'factoryId'
    ]
    
    sessionKeysToRemove.forEach(key => {
      sessionStorage.removeItem(key)
    })
  }
  
  console.log('✅ 所有认证状态已彻底清理')
}

/**
 * 检查当前认证状态
 */
export function checkCurrentAuthState(): void {
  const state = useAuthStore.getState()
  
  console.group('🔍 当前认证状态检查')
  console.log('Zustand状态:', {
    isAuthenticated: state.isAuthenticated,
    role: state.role,
    factoryId: state.factoryId,
    hasAccessToken: !!state.accessToken,
    userName: state.user?.name
  })
  
  if (typeof window !== 'undefined') {
    console.log('localStorage状态:', {
      accessToken: !!localStorage.getItem('accessToken'),
      role: localStorage.getItem('role'),
      factoryId: localStorage.getItem('factoryId'),
      authStorage: !!localStorage.getItem('auth-storage')
    })
  }
  
  console.groupEnd()
}

/**
 * 在控制台中暴露清理函数（仅开发环境）
 */
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // 使用 setTimeout 确保 window 对象完全初始化
  setTimeout(() => {
    try {
      (window as any).clearAuthState = clearAllAuthState
      (window as any).checkAuthState = checkCurrentAuthState

      console.log('🛠️ 开发工具已加载:')
      console.log('  - clearAuthState(): 清理所有认证状态')
      console.log('  - checkAuthState(): 检查当前认证状态')
    } catch (error) {
      console.warn('⚠️ 开发工具加载失败:', error)
    }
  }, 100)
}
