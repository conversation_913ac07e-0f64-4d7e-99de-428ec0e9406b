"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from "@/lib/database"
import { useAuthStore } from "@/lib/store/auth"
import {
  Settings,
  Save,
  Refresh<PERSON><PERSON>,
  Shield,
  Database,
  Bell,
  <PERSON><PERSON>,
  <PERSON>,
  Download,
  Upload,
  <PERSON>ert<PERSON><PERSON>cle,
  Key,
  Eye,
  EyeOff
} from "lucide-react"

interface SystemSettings {
  platform: {
    name: string
    version: string
    description: string
    contactEmail: string
    contactPhone: string
    website: string
  }
  security: {
    sessionTimeout: number
    passwordMinLength: number
    requirePasswordChange: boolean
    allowMultipleLogin: boolean
    enableTwoFactor: boolean
    adminPassword: string
  }
  backup: {
    autoBackup: boolean
    backupFrequency: string
    retentionDays: number
    backupLocation: string
    lastBackupTime: string | null
  }
  notifications: {
    emailEnabled: boolean
    smsEnabled: boolean
    pushEnabled: boolean
    orderNotifications: boolean
    systemNotifications: boolean
    announcementNotifications: boolean
  }
  business: {
    defaultCurrency: string
    taxRate: number
    orderNumberPrefix: string
    invoiceNumberPrefix: string
    enableRewards: boolean
    maxFactories: number
    enableDataSync: boolean
  }
  appearance: {
    theme: string
    primaryColor: string
    logoUrl: string
    faviconUrl: string
  }
}

export default function AdminSettingsPage() {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [settings, setSettings] = useState<SystemSettings | null>(null)
  const [activeTab, setActiveTab] = useState("platform")

  // 密码修改相关状态
  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [passwordLoading, setPasswordLoading] = useState(false)

  const { user } = useAuthStore()

  // 加载系统设置
  const loadSettings = async () => {
    try {
      setLoading(true)
      console.log('🔄 加载系统设置...')

      const settingsData = await db.getSystemSettings()
      setSettings(settingsData)
      
      console.log('✅ 系统设置加载完成')
    } catch (error) {
      console.error('❌ 加载系统设置失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadSettings()
  }, [])

  // 保存系统设置
  const handleSaveSettings = async () => {
    try {
      if (!settings) return

      setSaving(true)
      console.log('💾 保存系统设置...')

      await db.updateSystemSettings(settings)
      
      console.log('✅ 系统设置保存成功')
      alert('系统设置保存成功！')
    } catch (error) {
      console.error('❌ 保存系统设置失败:', error)
      alert('保存系统设置失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  // 重置为默认设置
  const handleResetSettings = async () => {
    try {
      if (!confirm('确定要重置为默认设置吗？这将清除所有自定义配置。')) {
        return
      }

      // 清除本地存储的设置
      localStorage.removeItem('systemSettings')
      
      // 重新加载默认设置
      await loadSettings()
      
      console.log('✅ 设置已重置为默认值')
      alert('设置已重置为默认值！')
    } catch (error) {
      console.error('❌ 重置设置失败:', error)
      alert('重置设置失败，请重试')
    }
  }

  // 导出设置
  const handleExportSettings = () => {
    try {
      if (!settings) return

      const dataStr = JSON.stringify(settings, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `系统设置_${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      URL.revokeObjectURL(url)
      console.log('✅ 设置导出成功')
    } catch (error) {
      console.error('❌ 导出设置失败:', error)
      alert('导出设置失败，请重试')
    }
  }

  // 导入设置
  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = event.target.files?.[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string)
          setSettings(importedSettings)
          console.log('✅ 设置导入成功')
          alert('设置导入成功！请点击保存按钮应用设置。')
        } catch (error) {
          console.error('❌ 解析导入文件失败:', error)
          alert('导入文件格式错误，请检查文件内容')
        }
      }
      reader.readAsText(file)
    } catch (error) {
      console.error('❌ 导入设置失败:', error)
      alert('导入设置失败，请重试')
    }
  }

  // 更新设置
  const updateSettings = (section: keyof SystemSettings, key: string, value: unknown) => {
    if (!settings) return

    setSettings(prev => ({
      ...prev!,
      [section]: {
        ...prev![section],
        [key]: value
      }
    }))
  }

  // 密码修改相关函数
  const handleChangePassword = async () => {
    if (!user) {
      alert('用户信息不存在')
      return
    }

    // 验证输入
    if (!currentPassword) {
      alert('请输入当前密码')
      return
    }

    if (!newPassword || newPassword.length < 6) {
      alert('新密码长度至少6位')
      return
    }

    if (newPassword !== confirmPassword) {
      alert('两次输入的新密码不一致')
      return
    }

    if (currentPassword === newPassword) {
      alert('新密码不能与当前密码相同')
      return
    }

    try {
      setPasswordLoading(true)

      // 调用数据库服务更新密码
      const success = await db.updateAdminPassword(user.id, currentPassword, newPassword)

      if (success) {
        // 更新本地设置中的管理员密码
        updateSettings('security', 'adminPassword', newPassword)

        alert('密码修改成功！\n\n新密码已生效，请妥善保管。')

        // 重置表单
        setCurrentPassword("")
        setNewPassword("")
        setConfirmPassword("")
        setShowPasswordDialog(false)
      } else {
        alert('密码修改失败，请检查当前密码是否正确')
      }
    } catch (error) {
      console.error('修改密码失败:', error)
      alert('密码修改失败，请重试')
    } finally {
      setPasswordLoading(false)
    }
  }

  // 打开密码修改对话框
  const handleOpenPasswordDialog = () => {
    setCurrentPassword("")
    setNewPassword("")
    setConfirmPassword("")
    setShowCurrentPassword(false)
    setShowNewPassword(false)
    setShowPasswordDialog(true)
  }

  if (loading) {
    return (
      <DashboardLayout role="admin">
        <div className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">正在加载系统设置...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!settings) {
    return (
      <DashboardLayout role="admin">
        <div className="p-8">
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">加载设置失败</h3>
            <p className="text-gray-600">无法加载系统设置，请稍后重试</p>
            <Button onClick={loadSettings} className="mt-4">
              重新加载
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout role="admin">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Settings className="h-6 w-6 mr-2 text-red-600" />
              系统设置
            </h1>
            <p className="text-gray-600">配置平台全局设置和系统参数</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={handleResetSettings}>
              <RefreshCw className="h-4 w-4 mr-2" />
              重置默认
            </Button>
            <Button variant="outline" onClick={handleExportSettings}>
              <Download className="h-4 w-4 mr-2" />
              导出设置
            </Button>
            <label className="cursor-pointer">
              <Button variant="outline" asChild>
                <span>
                  <Upload className="h-4 w-4 mr-2" />
                  导入设置
                </span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={handleImportSettings}
                className="hidden"
              />
            </label>
            <Button onClick={handleSaveSettings} disabled={saving}>
              {saving ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存设置
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 设置标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="platform">平台信息</TabsTrigger>
            <TabsTrigger value="security">安全设置</TabsTrigger>
            <TabsTrigger value="backup">备份设置</TabsTrigger>
            <TabsTrigger value="notifications">通知设置</TabsTrigger>
            <TabsTrigger value="business">业务设置</TabsTrigger>
            <TabsTrigger value="appearance">外观设置</TabsTrigger>
          </TabsList>

          {/* 平台信息 */}
          <TabsContent value="platform" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="h-5 w-5 mr-2" />
                  平台基本信息
                </CardTitle>
                <CardDescription>配置平台的基本信息和联系方式</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="platformName">平台名称</Label>
                    <Input
                      id="platformName"
                      value={settings.platform.name}
                      onChange={(e) => updateSettings('platform', 'name', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="platformVersion">版本号</Label>
                    <Input
                      id="platformVersion"
                      value={settings.platform.version}
                      onChange={(e) => updateSettings('platform', 'version', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="platformDescription">平台描述</Label>
                  <Textarea
                    id="platformDescription"
                    value={settings.platform.description}
                    onChange={(e) => updateSettings('platform', 'description', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">联系邮箱</Label>
                    <Input
                      id="contactEmail"
                      type="email"
                      value={settings.platform.contactEmail}
                      onChange={(e) => updateSettings('platform', 'contactEmail', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">联系电话</Label>
                    <Input
                      id="contactPhone"
                      value={settings.platform.contactPhone}
                      onChange={(e) => updateSettings('platform', 'contactPhone', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website">官方网站</Label>
                    <Input
                      id="website"
                      value={settings.platform.website}
                      onChange={(e) => updateSettings('platform', 'website', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 安全设置 */}
          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  安全配置
                </CardTitle>
                <CardDescription>配置系统安全策略和认证设置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="sessionTimeout">会话超时时间（小时）</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      value={settings.security.sessionTimeout}
                      onChange={(e) => updateSettings('security', 'sessionTimeout', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="passwordMinLength">密码最小长度</Label>
                    <Input
                      id="passwordMinLength"
                      type="number"
                      value={settings.security.passwordMinLength}
                      onChange={(e) => updateSettings('security', 'passwordMinLength', parseInt(e.target.value))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="adminPassword">管理员密码</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="adminPassword"
                      type="password"
                      value={settings.security.adminPassword}
                      onChange={(e) => updateSettings('security', 'adminPassword', e.target.value)}
                      className="flex-1"
                      readOnly
                    />
                    <Button
                      variant="outline"
                      onClick={handleOpenPasswordDialog}
                      className="flex items-center space-x-2"
                    >
                      <Key className="h-4 w-4" />
                      <span>修改密码</span>
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500">用于工厂删除等敏感操作的确认</p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>强制密码更改</Label>
                      <p className="text-sm text-gray-500">要求用户定期更改密码</p>
                    </div>
                    <Switch
                      checked={settings.security.requirePasswordChange}
                      onCheckedChange={(checked) => updateSettings('security', 'requirePasswordChange', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>允许多设备登录</Label>
                      <p className="text-sm text-gray-500">允许同一账号在多个设备同时登录</p>
                    </div>
                    <Switch
                      checked={settings.security.allowMultipleLogin}
                      onCheckedChange={(checked) => updateSettings('security', 'allowMultipleLogin', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>启用双因素认证</Label>
                      <p className="text-sm text-gray-500">为管理员账号启用双因素认证</p>
                    </div>
                    <Switch
                      checked={settings.security.enableTwoFactor}
                      onCheckedChange={(checked) => updateSettings('security', 'enableTwoFactor', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 备份设置 */}
          <TabsContent value="backup" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="h-5 w-5 mr-2" />
                  数据备份配置
                </CardTitle>
                <CardDescription>配置自动备份策略和数据保留设置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用自动备份</Label>
                    <p className="text-sm text-gray-500">定期自动备份系统数据</p>
                  </div>
                  <Switch
                    checked={settings.backup.autoBackup}
                    onCheckedChange={(checked) => updateSettings('backup', 'autoBackup', checked)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="backupFrequency">备份频率</Label>
                    <Select
                      value={settings.backup.backupFrequency}
                      onValueChange={(value) => updateSettings('backup', 'backupFrequency', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">每日备份</SelectItem>
                        <SelectItem value="weekly">每周备份</SelectItem>
                        <SelectItem value="monthly">每月备份</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="retentionDays">保留天数</Label>
                    <Input
                      id="retentionDays"
                      type="number"
                      value={settings.backup.retentionDays}
                      onChange={(e) => updateSettings('backup', 'retentionDays', parseInt(e.target.value))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="backupLocation">备份位置</Label>
                  <Select
                    value={settings.backup.backupLocation}
                    onValueChange={(value) => updateSettings('backup', 'backupLocation', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="local">本地存储</SelectItem>
                      <SelectItem value="cloud">云端存储</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {settings.backup.lastBackupTime && (
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">
                      最后备份时间: {new Date(settings.backup.lastBackupTime).toLocaleString('zh-CN')}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 通知设置 */}
          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="h-5 w-5 mr-2" />
                  通知配置
                </CardTitle>
                <CardDescription>配置系统通知和消息推送设置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>邮件通知</Label>
                      <p className="text-sm text-gray-500">启用邮件通知功能</p>
                    </div>
                    <Switch
                      checked={settings.notifications.emailEnabled}
                      onCheckedChange={(checked) => updateSettings('notifications', 'emailEnabled', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>短信通知</Label>
                      <p className="text-sm text-gray-500">启用短信通知功能</p>
                    </div>
                    <Switch
                      checked={settings.notifications.smsEnabled}
                      onCheckedChange={(checked) => updateSettings('notifications', 'smsEnabled', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>推送通知</Label>
                      <p className="text-sm text-gray-500">启用浏览器推送通知</p>
                    </div>
                    <Switch
                      checked={settings.notifications.pushEnabled}
                      onCheckedChange={(checked) => updateSettings('notifications', 'pushEnabled', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>订单通知</Label>
                      <p className="text-sm text-gray-500">新订单和订单状态变更通知</p>
                    </div>
                    <Switch
                      checked={settings.notifications.orderNotifications}
                      onCheckedChange={(checked) => updateSettings('notifications', 'orderNotifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>系统通知</Label>
                      <p className="text-sm text-gray-500">系统维护和更新通知</p>
                    </div>
                    <Switch
                      checked={settings.notifications.systemNotifications}
                      onCheckedChange={(checked) => updateSettings('notifications', 'systemNotifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>公告通知</Label>
                      <p className="text-sm text-gray-500">系统公告发布通知</p>
                    </div>
                    <Switch
                      checked={settings.notifications.announcementNotifications}
                      onCheckedChange={(checked) => updateSettings('notifications', 'announcementNotifications', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 业务设置 */}
          <TabsContent value="business" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="h-5 w-5 mr-2" />
                  业务配置
                </CardTitle>
                <CardDescription>配置业务相关的参数和规则</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="defaultCurrency">默认货币</Label>
                    <Select
                      value={settings.business.defaultCurrency}
                      onValueChange={(value) => updateSettings('business', 'defaultCurrency', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                        <SelectItem value="USD">美元 (USD)</SelectItem>
                        <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="taxRate">税率 (%)</Label>
                    <Input
                      id="taxRate"
                      type="number"
                      step="0.01"
                      value={settings.business.taxRate * 100}
                      onChange={(e) => updateSettings('business', 'taxRate', parseFloat(e.target.value) / 100)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxFactories">最大工厂数量</Label>
                    <Input
                      id="maxFactories"
                      type="number"
                      value={settings.business.maxFactories}
                      onChange={(e) => updateSettings('business', 'maxFactories', parseInt(e.target.value))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="orderNumberPrefix">订单号前缀</Label>
                    <Input
                      id="orderNumberPrefix"
                      value={settings.business.orderNumberPrefix}
                      onChange={(e) => updateSettings('business', 'orderNumberPrefix', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="invoiceNumberPrefix">发票号前缀</Label>
                    <Input
                      id="invoiceNumberPrefix"
                      value={settings.business.invoiceNumberPrefix}
                      onChange={(e) => updateSettings('business', 'invoiceNumberPrefix', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>启用奖励系统</Label>
                      <p className="text-sm text-gray-500">为客户推荐启用奖励机制</p>
                    </div>
                    <Switch
                      checked={settings.business.enableRewards}
                      onCheckedChange={(checked) => updateSettings('business', 'enableRewards', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>启用数据同步</Label>
                      <p className="text-sm text-gray-500">工厂与总部之间的数据同步</p>
                    </div>
                    <Switch
                      checked={settings.business.enableDataSync}
                      onCheckedChange={(checked) => updateSettings('business', 'enableDataSync', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 外观设置 */}
          <TabsContent value="appearance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Palette className="h-5 w-5 mr-2" />
                  外观配置
                </CardTitle>
                <CardDescription>自定义系统界面的外观和主题</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="theme">界面主题</Label>
                    <Select
                      value={settings.appearance.theme}
                      onValueChange={(value) => updateSettings('appearance', 'theme', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">浅色主题</SelectItem>
                        <SelectItem value="dark">深色主题</SelectItem>
                        <SelectItem value="auto">跟随系统</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">主题色</Label>
                    <Input
                      id="primaryColor"
                      type="color"
                      value={settings.appearance.primaryColor}
                      onChange={(e) => updateSettings('appearance', 'primaryColor', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="logoUrl">Logo URL</Label>
                    <Input
                      id="logoUrl"
                      value={settings.appearance.logoUrl}
                      onChange={(e) => updateSettings('appearance', 'logoUrl', e.target.value)}
                      placeholder="https://example.com/logo.png"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="faviconUrl">Favicon URL</Label>
                    <Input
                      id="faviconUrl"
                      value={settings.appearance.faviconUrl}
                      onChange={(e) => updateSettings('appearance', 'faviconUrl', e.target.value)}
                      placeholder="https://example.com/favicon.ico"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 密码修改对话框 */}
        <Dialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5 text-blue-600" />
                <span>修改管理员密码</span>
              </DialogTitle>
              <DialogDescription>
                为了系统安全，请定期更换管理员密码
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 text-yellow-600">
                  <AlertCircle className="h-5 w-5" />
                  <span className="font-medium">安全提醒</span>
                </div>
                <div className="mt-2 text-sm text-yellow-600">
                  <p>• 新密码长度至少6位</p>
                  <p>• 建议使用字母、数字和符号组合</p>
                  <p>• 修改后请妥善保管新密码</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="current-password" className="text-sm font-medium">
                    当前密码
                  </Label>
                  <div className="relative mt-1">
                    <Input
                      id="current-password"
                      type={showCurrentPassword ? "text" : "password"}
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      placeholder="请输入当前密码"
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    >
                      {showCurrentPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="new-password" className="text-sm font-medium">
                    新密码
                  </Label>
                  <div className="relative mt-1">
                    <Input
                      id="new-password"
                      type={showNewPassword ? "text" : "password"}
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      placeholder="请输入新密码（至少6位）"
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="confirm-password" className="text-sm font-medium">
                    确认新密码
                  </Label>
                  <Input
                    id="confirm-password"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="请再次输入新密码"
                    className="mt-1"
                  />
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowPasswordDialog(false)
                  setCurrentPassword("")
                  setNewPassword("")
                  setConfirmPassword("")
                }}
                disabled={passwordLoading}
              >
                取消
              </Button>
              <Button
                onClick={handleChangePassword}
                disabled={passwordLoading || !currentPassword || !newPassword || !confirmPassword}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {passwordLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    修改中...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    确认修改
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
