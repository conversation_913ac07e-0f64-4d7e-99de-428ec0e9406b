/**
 * 🇨🇳 风口云平台 - 客户服务
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 客户数据管理
 * - 替代localStorage操作
 * - 统一的客户API调用
 */

import { api } from '@/lib/api/client'
import type { Client } from '@/types'
import { AuthService } from './auth.service'

// 客户查询参数
export interface ClientQueryParams {
  factoryId?: string
  phone?: string
  name?: string
  company?: string
  status?: string
  limit?: number
  offset?: number
}

// 创建客户请求
export interface CreateClientRequest {
  factoryId?: string
  name: string
  phone: string
  email?: string
  company?: string
  address?: string
  referrerId?: string
  referrerName?: string
}

/**
 * 客户服务类
 */
export class ClientService {
  /**
   * 获取工厂客户列表
   */
  static async getFactoryClients(factoryId: string): Promise<Client[]> {
    try {
      const response = await api.get(`/api/clients?factoryId=${factoryId}`)
      
      if (response.success && response.data) {
        return response.data.clients || []
      }

      console.error('❌ 获取工厂客户失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 获取工厂客户异常:', error)
      return []
    }
  }

  /**
   * 获取当前用户工厂的客户
   */
  static async getCurrentFactoryClients(): Promise<Client[]> {
    const factoryId = AuthService.getCurrentFactoryId()
    
    if (!factoryId) {
      console.error('❌ 无法获取当前工厂ID')
      return []
    }

    return this.getFactoryClients(factoryId)
  }

  /**
   * 根据ID获取客户详情
   */
  static async getClientById(clientId: string): Promise<Client | null> {
    try {
      const response = await api.get(`/api/clients/${clientId}`)
      
      if (response.success && response.data) {
        return response.data.client || null
      }

      console.error('❌ 获取客户详情失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 获取客户详情异常:', error)
      return null
    }
  }

  /**
   * 根据手机号查询客户
   */
  static async getClientByPhone(phone: string): Promise<Client | null> {
    try {
      const response = await api.get(`/api/clients/search?phone=${phone}`)
      
      if (response.success && response.data) {
        const clients = response.data.clients || []
        return clients.length > 0 ? clients[0] : null
      }

      console.error('❌ 根据手机号查询客户失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 根据手机号查询客户异常:', error)
      return null
    }
  }

  /**
   * 创建客户
   */
  static async createClient(clientData: CreateClientRequest): Promise<Client | null> {
    try {
      // 确保有工厂ID
      if (!clientData.factoryId) {
        const factoryId = AuthService.getCurrentFactoryId()
        if (!factoryId) {
          console.error('❌ 无法获取工厂ID')
          return null
        }
        clientData.factoryId = factoryId
      }

      const response = await api.post('/api/clients', clientData)
      
      if (response.success && response.data) {
        console.log('✅ 客户创建成功:', response.data.client?.id)
        return response.data.client || null
      }

      console.error('❌ 创建客户失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 创建客户异常:', error)
      return null
    }
  }

  /**
   * 更新客户
   */
  static async updateClient(clientId: string, updates: Partial<Client>): Promise<Client | null> {
    try {
      const response = await api.put(`/api/clients/${clientId}`, updates)
      
      if (response.success && response.data) {
        console.log('✅ 客户更新成功:', clientId)
        return response.data.client || null
      }

      console.error('❌ 更新客户失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 更新客户异常:', error)
      return null
    }
  }

  /**
   * 删除客户
   */
  static async deleteClient(clientId: string): Promise<boolean> {
    try {
      const response = await api.delete(`/api/clients/${clientId}`)
      
      if (response.success) {
        console.log('✅ 客户删除成功:', clientId)
        return true
      }

      console.error('❌ 删除客户失败:', response.error)
      return false
    } catch (error) {
      console.error('❌ 删除客户异常:', error)
      return false
    }
  }

  /**
   * 搜索客户
   */
  static async searchClients(params: ClientQueryParams): Promise<Client[]> {
    try {
      const queryString = new URLSearchParams()
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryString.append(key, String(value))
        }
      })

      const response = await api.get(`/api/clients/search?${queryString.toString()}`)
      
      if (response.success && response.data) {
        return response.data.clients || []
      }

      console.error('❌ 搜索客户失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 搜索客户异常:', error)
      return []
    }
  }

  /**
   * 获取客户统计信息
   */
  static async getClientStats(factoryId?: string): Promise<unknown> {
    try {
      const targetFactoryId = factoryId || AuthService.getCurrentFactoryId()
      
      if (!targetFactoryId) {
        console.error('❌ 无法获取工厂ID')
        return null
      }

      const response = await api.get(`/api/clients/stats?factoryId=${targetFactoryId}`)
      
      if (response.success && response.data) {
        return response.data.stats || null
      }

      console.error('❌ 获取客户统计失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 获取客户统计异常:', error)
      return null
    }
  }

  /**
   * 获取客户推荐关系
   */
  static async getClientReferrals(clientId: string): Promise<Client[]> {
    try {
      const response = await api.get(`/api/clients/${clientId}/referrals`)
      
      if (response.success && response.data) {
        return response.data.referrals || []
      }

      console.error('❌ 获取客户推荐关系失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 获取客户推荐关系异常:', error)
      return []
    }
  }

  /**
   * 获取客户奖励记录
   */
  static async getClientRewards(clientId: string): Promise<unknown[]> {
    try {
      const response = await api.get(`/api/clients/${clientId}/rewards`)
      
      if (response.success && response.data) {
        return response.data.rewards || []
      }

      console.error('❌ 获取客户奖励记录失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 获取客户奖励记录异常:', error)
      return []
    }
  }
}

export default ClientService
