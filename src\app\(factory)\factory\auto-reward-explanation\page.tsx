'use client'

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON>  } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, ArrowRight, Zap, RefreshCw, Settings, Users } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"

export default function AutoRewardExplanationPage() {
  return (
    <DashboardLayout role="factory">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">自动奖励机制说明</h1>
            <p className="text-muted-foreground">了解推荐奖励如何自动计算和更新</p>
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={() => window.open('/factory/test-auto-reward', '_blank')}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Zap className="h-4 w-4 mr-2" />
              测试自动奖励
            </Button>
            <Button
              onClick={() => window.open('/factory/clients', '_blank')}
              variant="outline"
            >
              <Users className="h-4 w-4 mr-2" />
              返回客户管理
            </Button>
          </div>
        </div>

        {/* 核心概念 */}
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>好消息！</strong> 推荐奖励是完全自动计算的，无需手动同步。每次创建订单时，系统会自动更新相关推荐人的奖励。
          </AlertDescription>
        </Alert>

        {/* 工作流程 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-blue-600" />
              <span>自动奖励工作流程</span>
            </CardTitle>
            <CardDescription>每次创建订单时的自动处理流程</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold text-blue-600">1</span>
                </div>
                <div className="flex-1">
                  <div className="font-medium">创建订单</div>
                  <div className="text-sm text-gray-600">用户在系统中为客户创建新订单</div>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold text-green-600">2</span>
                </div>
                <div className="flex-1">
                  <div className="font-medium">自动触发统计更新</div>
                  <div className="text-sm text-gray-600">系统自动调用 <code>updateClientStatistics()</code> 方法</div>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold text-purple-600">3</span>
                </div>
                <div className="flex-1">
                  <div className="font-medium">检查推荐关系</div>
                  <div className="text-sm text-gray-600">检查下单客户是否有推荐人</div>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold text-orange-600">4</span>
                </div>
                <div className="flex-1">
                  <div className="font-medium">重新计算推荐奖励</div>
                  <div className="text-sm text-gray-600">如果有推荐人，自动重新计算推荐人的总奖励</div>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold text-red-600">5</span>
                </div>
                <div className="flex-1">
                  <div className="font-medium">更新数据库</div>
                  <div className="text-sm text-gray-600">推荐人的奖励金额立即更新到数据库</div>
                </div>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 技术细节 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-gray-600" />
                <span>技术实现</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Badge variant="outline" className="mb-2">数据库触发器</Badge>
                <p className="text-sm text-gray-600">
                  在 <code>createOrder()</code> 方法中，每次创建订单后会自动调用客户统计更新。
                </p>
              </div>
              <div>
                <Badge variant="outline" className="mb-2">实时计算</Badge>
                <p className="text-sm text-gray-600">
                  推荐奖励基于推荐人所有被推荐客户的订单总额实时计算，确保数据准确。
                </p>
              </div>
              <div>
                <Badge variant="outline" className="mb-2">事务安全</Badge>
                <p className="text-sm text-gray-600">
                  所有数据更新都在数据库事务中进行，确保数据一致性。
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <RefreshCw className="h-5 w-5 text-blue-600" />
                <span>何时需要手动同步</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Badge variant="destructive" className="mb-2">数据修复</Badge>
                <p className="text-sm text-gray-600">
                  当发现历史数据不一致时，可以使用手动同步工具进行批量修复。
                </p>
              </div>
              <div>
                <Badge variant="destructive" className="mb-2">系统升级后</Badge>
                <p className="text-sm text-gray-600">
                  系统升级或奖励计算规则变更后，可能需要重新计算所有奖励。
                </p>
              </div>
              <div>
                <Badge variant="destructive" className="mb-2">异常情况</Badge>
                <p className="text-sm text-gray-600">
                  如果自动机制出现故障，可以使用手动工具进行修复。
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 奖励计算规则 */}
        <Card>
          <CardHeader>
            <CardTitle>奖励计算规则</CardTitle>
            <CardDescription>了解推荐奖励的具体计算方式</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">基础计算公式</h4>
                <p className="text-sm text-blue-800">
                  推荐奖励 = 被推荐客户所有订单总额 × 奖励比例（默认2%）
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-3 border rounded">
                  <h5 className="font-medium mb-1">普通风口</h5>
                  <p className="text-sm text-gray-600">按订单金额的固定比例计算</p>
                </div>
                <div className="p-3 border rounded">
                  <h5 className="font-medium mb-1">高端风口</h5>
                  <p className="text-sm text-gray-600">可能有不同的计算规则（如阶梯比例）</p>
                </div>
              </div>

              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">实时更新</h4>
                <p className="text-sm text-green-800">
                  每次为被推荐客户创建新订单时，推荐人的奖励会立即重新计算并更新。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 常见问题 */}
        <Card>
          <CardHeader>
            <CardTitle>常见问题解答</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Q: 为什么我看到的奖励数字不对？</h4>
              <p className="text-sm text-gray-600 mb-2">
                A: 可能的原因包括：
              </p>
              <ul className="text-sm text-gray-600 list-disc list-inside ml-4 space-y-1">
                <li>浏览器缓存了旧数据，尝试刷新页面</li>
                <li>历史数据存在不一致，使用"同步奖励"功能修复</li>
                <li>推荐关系设置有误，检查客户的推荐人信息</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Q: 什么时候需要手动同步？</h4>
              <p className="text-sm text-gray-600">
                A: 正常情况下不需要手动同步。只有在发现数据异常或进行系统维护时才需要使用手动同步工具。
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Q: 如何验证自动机制是否正常工作？</h4>
              <p className="text-sm text-gray-600">A: 可以使用"测试自动奖励"功能，系统会创建一个测试订单来验证自动机制，测试完成后会自动清理数据。
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 操作建议 */}
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800">操作建议</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-green-700">
              <p>✅ <strong>正常使用：</strong>直接创建订单，系统会自动处理推荐奖励</p>
              <p>✅ <strong>数据检查：</strong>定期查看推荐系统页面，确认奖励数据正确</p>
              <p>✅ <strong>问题排查：</strong>如发现异常，先尝试刷新页面，再考虑使用修复工具</p>
              <p>✅ <strong>测试验证：</strong>使用测试工具验证自动机制是否正常工作</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
