/**
 * 百度OCR服务模块
 * 提供文字识别功能，支持通用文字识别和高精度文字识别
 */

// 百度OCR配置
const BAIDU_OCR_CONFIG = {
  API_KEY: 'XYi8m9nIgFvHQ1jOpr20TpAr',
  SECRET_KEY: 'P7ieKMlibFjDu0jXObf2frJ1wx3IXh9g',
  APP_ID: '119490576',
  // API端点
  TOKEN_URL: 'https://aip.baidubce.com/oauth/2.0/token',
  OCR_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic', // 通用文字识别
  OCR_ACCURATE_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic', // 高精度文字识别
}

// OCR识别结果接口
export interface OCRResult {
  success: boolean
  text: string
  words: Array<{
    words: string
    location: {
      left: number
      top: number
      width: number
      height: number
    }
  }>
  error?: string
}

// Access Token缓存
let cachedToken: string | null = null
let tokenExpireTime: number = 0

/**
 * 获取百度API访问令牌
 */
async function getAccessToken(): Promise<string> {
  try {
    // 检查缓存的token是否还有效（提前5分钟刷新）
    const now = Date.now()
    if (cachedToken && now < tokenExpireTime - 5 * 60 * 1000) {
      console.log('🔑 使用缓存的access_token')
      return cachedToken
    }

    console.log('🔑 获取新的access_token...')
    
    const params = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: BAIDU_OCR_CONFIG.API_KEY,
      client_secret: BAIDU_OCR_CONFIG.SECRET_KEY
    })

    const response = await fetch(BAIDU_OCR_CONFIG.TOKEN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params
    })

    if (!response.ok) {
      throw new Error(`获取token失败: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(`百度API错误: ${data.error} - ${data.error_description}`)
    }

    // 缓存token（有效期通常为30天）
    cachedToken = data.access_token
    tokenExpireTime = now + (data.expires_in * 1000)
    
    console.log('✅ access_token获取成功')
    return cachedToken

  } catch (error) {
    console.error('❌ 获取access_token失败:', error)
    throw new Error(`获取百度API访问令牌失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 将图片文件转换为Base64编码
 */
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const result = reader.result as string
      // 移除data:image/xxx;base64,前缀
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsDataURL(file)
  })
}

/**
 * 调用百度OCR API进行文字识别
 */
async function callOCRAPI(imageBase64: string, useAccurate: boolean = false): Promise<any> {
  try {
    const accessToken = await getAccessToken()
    const ocrUrl = useAccurate ? BAIDU_OCR_CONFIG.OCR_ACCURATE_URL : BAIDU_OCR_CONFIG.OCR_URL
    
    const params = new URLSearchParams({
      access_token: accessToken
    })

    const formData = new URLSearchParams({
      image: imageBase64,
      language_type: 'CHN_ENG', // 中英文混合
      detect_direction: 'true', // 检测图像朝向
      paragraph: 'false', // 是否输出段落信息
      probability: 'false' // 是否返回识别结果中每一行的置信度
    })

    console.log(`🔍 调用百度OCR API (${useAccurate ? '高精度' : '通用'})...`)

    const response = await fetch(`${ocrUrl}?${params}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: formData
    })

    if (!response.ok) {
      throw new Error(`OCR API调用失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.error_code) {
      throw new Error(`百度OCR错误: ${result.error_code} - ${result.error_msg}`)
    }

    return result

  } catch (error) {
    console.error('❌ OCR API调用失败:', error)
    throw error
  }
}

/**
 * 主要的OCR识别函数
 */
export async function recognizeText(file: File, useAccurate: boolean = false): Promise<OCRResult> {
  try {
    console.log('🖼️ 开始OCR文字识别...')
    console.log(`  - 文件名: ${file.name}`)
    console.log(`  - 文件大小: ${(file.size / 1024).toFixed(2)}KB`)
    console.log(`  - 识别模式: ${useAccurate ? '高精度' : '通用'}`)

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      throw new Error('请上传图片文件')
    }

    // 验证文件大小（百度OCR限制4MB）
    if (file.size > 4 * 1024 * 1024) {
      throw new Error('图片文件大小不能超过4MB')
    }

    // 转换为Base64
    const imageBase64 = await fileToBase64(file)
    
    // 调用OCR API
    const ocrResult = await callOCRAPI(imageBase64, useAccurate)
    
    // 处理识别结果
    const words = ocrResult.words_result || []
    const allText = words.map((item: any) => item.words).join('\n')
    
    console.log('✅ OCR识别完成')
    console.log(`  - 识别到 ${words.length} 行文字`)
    console.log(`  - 总字符数: ${allText.length}`)
    
    return {
      success: true,
      text: allText,
      words: words.map((item: any) => ({
        words: item.words,
        location: item.location || { left: 0, top: 0, width: 0, height: 0 }
      }))
    }

  } catch (error) {
    console.error('❌ OCR识别失败:', error)
    return {
      success: false,
      text: '',
      words: [],
      error: error instanceof Error ? error.message : '识别失败'
    }
  }
}

/**
 * 批量OCR识别（支持多张图片）
 */
export async function recognizeMultipleImages(files: File[], useAccurate: boolean = false): Promise<OCRResult[]> {
  console.log(`🖼️ 开始批量OCR识别 (${files.length}张图片)...`)
  
  const results: OCRResult[] = []
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    console.log(`📸 处理第${i + 1}张图片: ${file.name}`)
    
    try {
      const result = await recognizeText(file, useAccurate)
      results.push(result)
      
      // 添加延迟避免API调用过于频繁
      if (i < files.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    } catch (error) {
      console.error(`❌ 第${i + 1}张图片识别失败:`, error)
      results.push({
        success: false,
        text: '',
        words: [],
        error: error instanceof Error ? error.message : '识别失败'
      })
    }
  }
  
  console.log(`✅ 批量OCR识别完成，成功: ${results.filter(r => r.success).length}/${files.length}`)
  return results
}

/**
 * 合并多个OCR结果的文本
 */
export function mergeOCRResults(results: OCRResult[]): string {
  return results
    .filter(result => result.success && result.text.trim())
    .map(result => result.text.trim())
    .join('\n\n')
}

/**
 * 检查百度OCR服务状态
 */
export async function checkOCRServiceStatus(): Promise<{ available: boolean; error?: string }> {
  try {
    await getAccessToken()
    return { available: true }
  } catch (error) {
    return { 
      available: false, 
      error: error instanceof Error ? error.message : '服务不可用' 
    }
  }
}
