'use client'

import React, { useState, useEffect } from 'react'
import { Factory } from '@/types'
import { api } from '@/lib/api/client'

interface FactoryStatusInfo {
  canLogin: boolean
  reason?: string
  status: string
  isExpired: boolean
  remainingDays?: number
  remainingHours?: number
  warningLevel?: 'none' | 'warning' | 'critical' | 'expired'
  autoSuspended?: boolean
}

interface FactoryManagementData {
  factory: Factory
  statusCheck: FactoryStatusInfo
  userCount: number
  recentLogins: any[]
}

interface FactorySubscriptionManagerProps {
  factoryId: string
  onClose: () => void
  onUpdate: () => void
}

export default function FactorySubscriptionManager({ 
  factoryId, 
  onClose, 
  onUpdate 
}: FactorySubscriptionManagerProps) {
  const [data, setData] = useState<FactoryManagementData | null>(null)
  const [loading, setLoading] = useState(true)
  const [operating, setOperating] = useState(false)
  const [action, setAction] = useState<string>('')
  const [reason, setReason] = useState('')
  const [subscriptionType, setSubscriptionType] = useState('monthly')
  const [customEndDate, setCustomEndDate] = useState('')
  const [subscriptionDuration, setSubscriptionDuration] = useState(30)

  useEffect(() => {
    loadFactoryData()
  }, [factoryId])

  const loadFactoryData = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/api/admin/factories/manage?factoryId=${factoryId}`)
      
      if (response.success) {
        setData(response.data)
      } else {
        console.error('获取工厂数据失败:', response.error)
      }
    } catch (error) {
      console.error('获取工厂数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleOperation = async (operationType: string) => {
    if (!data) return

    try {
      setOperating(true)
      setAction(operationType)

      const requestData: any = {
        factoryId,
        action: operationType,
        reason
      }

      if (operationType === 'renew') {
        requestData.subscriptionType = subscriptionType
        if (customEndDate) {
          requestData.customEndDate = customEndDate
        }
      } else if (operationType === 'extend') {
        requestData.subscriptionDuration = subscriptionDuration
      }

      const response = await api.post('/api/admin/factories/manage', requestData)

      if (response.success) {
        alert(`操作成功：${response.message}`)
        setReason('')
        setCustomEndDate('')
        await loadFactoryData()
        onUpdate()
      } else {
        alert(`操作失败：${response.error}`)
      }
    } catch (error) {
      console.error('操作失败:', error)
      alert('操作失败，请重试')
    } finally {
      setOperating(false)
      setAction('')
    }
  }

  const getStatusColor = (status: string, warningLevel?: string) => {
    if (status === 'suspended') return 'text-red-600 bg-red-50'
    if (status === 'expired') return 'text-red-600 bg-red-50'
    if (status === 'inactive') return 'text-gray-600 bg-gray-50'
    if (warningLevel === 'critical') return 'text-orange-600 bg-orange-50'
    if (warningLevel === 'warning') return 'text-yellow-600 bg-yellow-50'
    return 'text-green-600 bg-green-50'
  }

  const getStatusText = (statusCheck: FactoryStatusInfo) => {
    if (!statusCheck.canLogin) {
      return statusCheck.reason || '无法登录'
    }
    
    if (statusCheck.remainingDays !== undefined) {
      if (statusCheck.remainingDays <= 1) {
        return `剩余 ${statusCheck.remainingHours || 0} 小时`
      }
      return `剩余 ${statusCheck.remainingDays} 天`
    }
    
    return '正常运行'
  }

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6">
          <div className="text-center">加载中...</div>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6">
          <div className="text-center text-red-600">加载工厂数据失败</div>
          <div className="mt-4 text-center">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    )
  }

  const { factory, statusCheck, userCount, recentLogins } = data

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* 标题 */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">工厂订阅管理</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          {/* 工厂基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3">基本信息</h3>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">工厂名称：</span>{factory.name}</div>
                <div><span className="font-medium">工厂编码：</span>{factory.code}</div>
                <div><span className="font-medium">用户数量：</span>{userCount} 人</div>
                <div><span className="font-medium">创建时间：</span>{new Date(factory.createdAt).toLocaleString('zh-CN')}</div>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3">订阅信息</h3>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">订阅类型：</span>{factory.subscriptionType}</div>
                <div><span className="font-medium">开始时间：</span>
                  {factory.subscriptionStart ? new Date(factory.subscriptionStart).toLocaleString('zh-CN') : '未设置'}
                </div>
                <div><span className="font-medium">结束时间：</span>
                  {factory.isPermanent ? '永久' : 
                   factory.subscriptionEnd ? new Date(factory.subscriptionEnd).toLocaleString('zh-CN') : '未设置'}
                </div>
                <div><span className="font-medium">首次登录：</span>
                  {factory.firstLoginAt ? new Date(factory.firstLoginAt).toLocaleString('zh-CN') : '未登录'}
                </div>
              </div>
            </div>
          </div>

          {/* 状态信息 */}
          <div className="mb-6">
            <h3 className="font-semibold mb-3">当前状态</h3>
            <div className={`p-4 rounded-lg ${getStatusColor(statusCheck.status, statusCheck.warningLevel)}`}>
              <div className="flex justify-between items-center">
                <div>
                  <div className="font-medium">
                    状态：{factory.status} 
                    {statusCheck.autoSuspended && <span className="ml-2 text-xs">(自动暂停)</span>}
                  </div>
                  <div className="text-sm mt-1">{getStatusText(statusCheck)}</div>
                </div>
                <div className="text-right text-sm">
                  {statusCheck.warningLevel === 'critical' && <div className="text-red-600 font-medium">⚠️ 即将到期</div>}
                  {statusCheck.warningLevel === 'warning' && <div className="text-yellow-600 font-medium">⚠️ 注意到期</div>}
                </div>
              </div>
            </div>
          </div>

          {/* 操作区域 */}
          <div className="mb-6">
            <h3 className="font-semibold mb-3">管理操作</h3>
            
            {/* 操作原因 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">操作原因</label>
              <input
                type="text"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="请输入操作原因（可选）"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* 启动/暂停 */}
              {factory.status === 'suspended' ? (
                <button
                  onClick={() => handleOperation('activate')}
                  disabled={operating}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                >
                  {operating && action === 'activate' ? '启动中...' : '启动工厂'}
                </button>
              ) : (
                <button
                  onClick={() => handleOperation('suspend')}
                  disabled={operating}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                >
                  {operating && action === 'suspend' ? '暂停中...' : '暂停工厂'}
                </button>
              )}

              {/* 续费 */}
              <div className="space-y-2">
                <select
                  value={subscriptionType}
                  onChange={(e) => setSubscriptionType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="monthly">按月订阅</option>
                  <option value="quarterly">按季度订阅</option>
                  <option value="yearly">按年订阅</option>
                  <option value="permanent">永久订阅</option>
                </select>
                <button
                  onClick={() => handleOperation('renew')}
                  disabled={operating}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                >
                  {operating && action === 'renew' ? '续费中...' : '续费'}
                </button>
              </div>

              {/* 延长 */}
              <div className="space-y-2">
                <input
                  type="number"
                  value={subscriptionDuration}
                  onChange={(e) => setSubscriptionDuration(Number(e.target.value))}
                  placeholder="延长天数"
                  min="1"
                  max="365"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <button
                  onClick={() => handleOperation('extend')}
                  disabled={operating || !factory.subscriptionEnd}
                  className="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
                >
                  {operating && action === 'extend' ? '延长中...' : '延长订阅'}
                </button>
              </div>

              {/* 自定义到期时间 */}
              <div className="space-y-2">
                <input
                  type="datetime-local"
                  value={customEndDate}
                  onChange={(e) => setCustomEndDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <button
                  onClick={() => {
                    if (customEndDate) {
                      handleOperation('renew')
                    } else {
                      alert('请选择自定义到期时间')
                    }
                  }}
                  disabled={operating || !customEndDate}
                  className="w-full px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 disabled:opacity-50"
                >
                  自定义续费
                </button>
              </div>
            </div>
          </div>

          {/* 最近登录记录 */}
          {recentLogins.length > 0 && (
            <div>
              <h3 className="font-semibold mb-3">最近登录记录</h3>
              <div className="bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto">
                <div className="space-y-2 text-sm">
                  {recentLogins.map((login, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <div>
                        <span className="font-medium">{login.userName}</span>
                        <span className="text-gray-500 ml-2">({login.username})</span>
                      </div>
                      <div className="text-gray-500">
                        {new Date(login.loginTime).toLocaleString('zh-CN')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
