# 📁 风口云平台 - 项目结构说明

## 🏗️ 项目概览

风口云平台是一个基于Next.js 15的现代化SaaS平台，专为中央空调风口加工厂设计，提供多工厂管理、订单处理、客户管理、股东管理等完整功能。

## 📂 目录结构

```
factorysystem/
├── 📁 src/                          # 源代码目录
│   ├── 📁 app/                      # Next.js App Router
│   │   ├── 📁 (auth)/              # 认证相关页面
│   │   ├── 📁 (factory)/           # 工厂管理页面
│   │   ├── 📁 (headquarters)/      # 总部管理页面
│   │   └── 📁 api/                 # API路由
│   ├── 📁 components/              # React组件
│   │   ├── 📁 ui/                  # 基础UI组件
│   │   ├── 📁 layout/              # 布局组件
│   │   ├── 📁 shareholders/        # 股东管理组件
│   │   └── 📁 ...                  # 其他业务组件
│   ├── 📁 lib/                     # 工具库和配置
│   │   ├── 📁 database/            # 数据库操作
│   │   ├── 📁 utils/               # 工具函数
│   │   └── 📁 ...                  # 其他库文件
│   └── 📁 types/                   # TypeScript类型定义
├── 📁 prisma/                      # 数据库相关
│   ├── 📄 schema.prisma            # 数据库模式
│   └── 📁 migrations/              # 数据库迁移
├── 📁 public/                      # 静态资源
│   └── 📁 images/                  # 图片资源
├── 📁 docs/                        # 文档目录
│   └── 📄 database-setup.md        # 数据库设置文档
├── 📁 scripts/                     # 脚本文件
│   └── 📄 init-database.sql        # 数据库初始化脚本
├── 📄 docker-compose.yml           # Docker编排文件
├── 📄 Dockerfile                   # Docker镜像构建文件
├── 📄 nginx.conf                   # Nginx配置文件
├── 📄 deploy.sh                    # 部署脚本
├── 📄 backup.sh                    # 备份脚本
├── 📄 monitor.sh                   # 监控脚本
├── 📄 .env.production              # 生产环境变量模板
├── 📄 package.json                 # 项目依赖
├── 📄 next.config.ts               # Next.js配置
├── 📄 tailwind.config.js           # Tailwind CSS配置
├── 📄 tsconfig.json                # TypeScript配置
├── 📄 DEPLOYMENT_GUIDE.md          # 部署指南
├── 📄 PROJECT_STRUCTURE.md         # 项目结构说明
└── 📄 README.md                    # 项目说明
```

## 🔧 核心技术栈

### 前端技术
- **Next.js 15**: React全栈框架
- **React 19**: 用户界面库
- **TypeScript**: 类型安全的JavaScript
- **Tailwind CSS**: 原子化CSS框架
- **Radix UI**: 无障碍UI组件库
- **Lucide React**: 图标库

### 后端技术
- **Next.js API Routes**: 服务端API
- **Prisma**: 数据库ORM
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和会话存储

### 部署技术
- **Docker**: 容器化部署
- **Docker Compose**: 多容器编排
- **Nginx**: 反向代理和负载均衡

## 📋 主要功能模块

### 1. 认证系统 (`src/app/(auth)/`)
- 用户登录/注册
- 多角色权限管理
- 会话管理

### 2. 工厂管理 (`src/app/(factory)/`)
- 订单管理
- 客户管理
- 员工管理
- 股东管理
- 利润分析
- 推荐奖励系统

### 3. 总部管理 (`src/app/(headquarters)/`)
- 多工厂监控
- 数据统计分析
- 系统设置
- 公告管理

### 4. API接口 (`src/app/api/`)
- RESTful API设计
- 数据验证和处理
- 错误处理
- 认证中间件

## 🗄️ 数据库设计

### 核心数据表
- **users**: 用户信息
- **factories**: 工厂信息
- **orders**: 订单数据
- **clients**: 客户信息
- **employees**: 员工信息
- **shareholders**: 股东信息
- **dividends**: 分红记录
- **rewards**: 推荐奖励

### 关系设计
- 用户-工厂：多对多关系
- 工厂-订单：一对多关系
- 客户-订单：一对多关系
- 股东-分红：一对多关系

## 🎨 UI组件架构

### 基础组件 (`src/components/ui/`)
- Button, Input, Card等基础组件
- 基于Radix UI构建
- 完全可定制的样式

### 业务组件
- 订单管理组件
- 客户管理组件
- 股东管理组件
- 数据可视化组件

### 布局组件 (`src/components/layout/`)
- DashboardLayout: 仪表板布局
- AuthLayout: 认证页面布局
- 响应式设计

## 🔧 工具和配置

### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型检查

### 构建工具
- **Next.js**: 自动优化和构建
- **Tailwind CSS**: 样式处理
- **PostCSS**: CSS后处理

### 部署工具
- **Docker**: 容器化
- **Docker Compose**: 服务编排
- **Nginx**: Web服务器

## 📊 数据流架构

```
用户请求 → Nginx → Next.js App → API Routes → Prisma ORM → PostgreSQL
                                      ↓
                                   Redis Cache
```

## 🔐 安全特性

### 认证和授权
- JWT令牌认证
- 基于角色的访问控制
- 会话管理

### 数据安全
- SQL注入防护
- XSS攻击防护
- CSRF保护
- 数据加密存储

### 网络安全
- HTTPS强制重定向
- 安全头设置
- 防火墙配置

## 📈 性能优化

### 前端优化
- 代码分割和懒加载
- 图片优化
- 缓存策略
- 服务端渲染(SSR)

### 后端优化
- 数据库查询优化
- Redis缓存
- API响应压缩
- 连接池管理

### 部署优化
- Docker镜像优化
- Nginx配置优化
- 负载均衡
- CDN集成

## 🧪 测试策略

### 单元测试
- 组件测试
- 工具函数测试
- API接口测试

### 集成测试
- 数据库操作测试
- API端到端测试
- 用户流程测试

## 📝 开发规范

### 代码规范
- TypeScript严格模式
- ESLint规则检查
- 统一的命名约定
- 组件和函数注释

### Git工作流
- 功能分支开发
- 代码审查流程
- 自动化CI/CD

### 文档规范
- API文档
- 组件文档
- 部署文档
- 用户手册

## 🚀 部署架构

### 开发环境
```
本地开发 → npm run dev → localhost:3000
```

### 生产环境
```
代码仓库 → Docker构建 → 阿里云ECS → 用户访问
```

### 监控和维护
- 系统监控脚本
- 自动备份策略
- 日志管理
- 性能监控

---

这个项目结构设计遵循了现代Web应用的最佳实践，确保了代码的可维护性、可扩展性和性能优化。
