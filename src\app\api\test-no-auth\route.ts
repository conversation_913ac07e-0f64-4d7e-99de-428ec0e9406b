/**
 * 测试API - 不需要认证
 */

import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  return NextResponse.json({
    success: true,
    message: '这是一个不需要认证的测试API',
    timestamp: new Date().toISOString()
  })
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    return NextResponse.json({
      success: true,
      message: '这是一个不需要认证的POST测试API',
      receivedData: body,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '解析请求体失败',
      message: error instanceof Error ? error.message : '未知错误'
    }, { status: 400 })
  }
}
