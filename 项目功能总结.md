# 🇨🇳 风口云平台 - 功能总结

## 项目概述
风口云平台是一个专为风口制造工厂设计的智能化管理系统，集成了OCR识别、NLP解析、订单管理、财务管理等核心功能，实现了从图片识别到订单生成的全流程自动化。

## 🌟 核心特色功能

### 1. 智能图片识别系统
- **多策略OCR识别**: 表格识别、文字识别、手写识别
- **NLP智能解析**: 5层解析架构，支持复杂文本格式
- **空间感知识别**: 基于坐标的智能布局分析
- **多尺寸解析**: 支持一行多个尺寸（15.5/266.5 25.5/152.5）
- **智能单位识别**: 自动识别cm/mm，智能转换

### 2. 风口类型识别
- **支持20+种风口类型**: 出风口、回风口、线型风口、检修口等
- **统一判断标准**: 回风口≥255mm，出风口160-255mm，线型风口≤160mm
- **特殊材质识别**: 腻子粉预埋、石膏板预埋、木纹风口等
- **颜色识别**: 白色、黑色、双色风口自动识别

### 3. 订单管理系统
- **智能录单**: 表格式快速录单，支持楼层分组
- **自动编号**: 工厂前缀-日期-序号格式
- **实时计算**: 5种计价模式，实时价格计算
- **批量操作**: 批量设价、楼层复制、快速添加
- **状态管理**: 待确认→已确认→生产中→已完成

### 4. 财务管理系统
- **付款状态追踪**: 未付款、部分付款、已付款
- **推荐奖励系统**: 阶梯式奖励（2%-5%）
- **财务报表**: 多维度分析报表
- **Excel导出**: 客户账单、财务汇总等

## 📊 支持的数据格式

### 文本格式
```
标准格式: 出风口300×1500mm
斜杠格式: 15.5/266.5    25.5/152.5歺
复杂格式: 大厅出风口:3090✘150分段
多项目格式: 支持多个项目批量识别
```

### 图片格式
- JPG、PNG、PDF等主流格式
- 最大文件大小: 10MB
- 支持手机拍照和扫描件

## 🔧 技术架构

### 前端技术栈
- Next.js 14 (App Router)
- TypeScript + Tailwind CSS
- React Hook Form + Zod验证

### 后端技术栈
- PostgreSQL数据库
- Prisma ORM
- 百度OCR API

### 智能解析架构
```
用户输入 → OCR识别 → 空间分析 → 表格重组 → NLP解析 → 数据验证 → 订单生成
```

## 📈 项目统计

### 代码规模
- 总代码行数: 约50,000行
- TypeScript文件: 200+个
- React组件: 150+个
- API接口: 80+个

### 功能特性
- 识别准确率: 95%+
- 支持风口类型: 20+种
- 支持文本格式: 10+种
- 响应时间: <3秒

## 🚀 最新更新 (2025年1月)

### 新增功能
- **多尺寸解析引擎**: 一行多个尺寸智能分割
- **回风口判断统一**: 所有模块统一≥255mm标准
- **NLP智能解析**: 完整的多层次文本处理
- **空间感知识别**: 基于坐标的布局分析
- **智能备注组合**: 房间信息+技术规格自动组合

### 技术优化
- 斜杠格式专门支持
- 单位智能识别优化
- 错误处理机制完善
- 性能优化提升

## 🎯 应用场景

### 适用行业
- 风口制造工厂
- 暖通空调工程
- 建筑装修行业
- 工程设备供应商

### 典型用户
- 工厂管理员
- 订单录入员
- 财务人员
- 销售人员

### 使用流程
1. 上传风口清单图片或粘贴文本
2. 系统自动识别并解析
3. 确认或修正识别结果
4. 一键生成订单
5. 管理订单状态和财务

## 📞 技术支持

### 部署环境
- 开发环境: Windows/macOS/Linux
- 生产环境: 阿里云ECS
- 数据库: PostgreSQL 13+
- 域名: fengkouyun.cn

### 维护更新
- 代码仓库: Gitee
- 自动部署: Git推送触发
- 监控日志: PM2进程管理
- 数据备份: 自动定期备份

---

**项目地址**: https://gitee.com/lao62025/factorysystem  
**在线演示**: https://fengkouyun.cn  
**技术文档**: 详见《项目需求文档.md》
