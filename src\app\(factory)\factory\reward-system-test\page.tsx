'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  RefreshCw,
  TestTube,
  Wrench,
  CheckCircle,
  AlertCircle,
  XCircle
} from "lucide-react"
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { safeNumber, safeAmountFormat } from '@/lib/utils/number-utils'
import { calculateTotalReferralReward, isPremiumVent } from '@/lib/utils/reward-calculator'
import { getFactorySettings } from '@/lib/utils/factory-settings'

interface TestResult {
  testName: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  details?: unknown
}

interface RewardTestData {
  clientId: string
  clientName: string
  clientPhone: string
  referredClients: Array<{
    id: string
    name: string
    phone: string
    orders: Array<{
      id: string
      orderNumber?: string
      totalAmount: number
      items: Array<{
        productType: string
        productName: string
        totalPrice: number
      }>
    }>
  }>
  calculatedReward: number
  databaseReward: number
  isCorrect: boolean
  discrepancy: number
}

export default function RewardSystemTestPage() {
  const [loading, setLoading] = useState(false)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [rewardTestData, setRewardTestData] = useState<RewardTestData[]>([])
  const [summary, setSummary] = useState({
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    warningTests: 0,
    totalClients: 0,
    correctClients: 0,
    incorrectClients: 0,
    totalDiscrepancy: 0
  })

  const runComprehensiveTest = async () => {
    try {
      setLoading(true)
      setTestResults([])
      setRewardTestData([])
      
      console.log('🧪 开始全面奖励系统测试...')

      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        console.error('❌ 无法获取工厂ID')
        return
      }

      const results: TestResult[] = []
      const rewardData: RewardTestData[] = []

      // 测试1: 检查工厂设置
      try {
        const settings = getFactorySettings(factoryId)
        if (settings && settings.rewardSettings) {
          results.push({
            testName: '工厂奖励设置',
            status: 'pass',
            message: '工厂奖励设置正常',
            details: settings.rewardSettings
          })
        } else {
          results.push({
            testName: '工厂奖励设置',
            status: 'fail',
            message: '工厂奖励设置缺失或无效'
          })
        }
      } catch (error) {
        results.push({
          testName: '工厂奖励设置',
          status: 'fail',
          message: `工厂设置检查失败: ${error.message}`
        })
      }

      // 测试2: 检查数据库连接和数据
      try {
        const clients = await db.getClientsByFactoryId(factoryId)
        const orders = await db.getOrdersByFactoryId(factoryId)
        
        results.push({
          testName: '数据库连接',
          status: 'pass',
          message: `数据库连接正常，找到 ${clients.length} 个客户，${orders.length} 个订单`
        })

        // 测试3: 检查数据类型转换
        const sampleClient = clients.find(c => c.referralReward !== undefined)
        if (sampleClient) {
          const rewardType = typeof sampleClient.referralReward
          if (rewardType === 'number') {
            results.push({
              testName: '数据类型转换',
              status: 'pass',
              message: '奖励字段已正确转换为数字类型'
            })
          } else {
            results.push({
              testName: '数据类型转换',
              status: 'fail',
              message: `奖励字段类型错误: ${rewardType}，应为 number`
            })
          }
        }

        // 测试4: 检查推荐关系
        const referrers = clients.filter(client => 
          clients.some(c => c.referrerId === client.id)
        )
        
        results.push({
          testName: '推荐关系检查',
          status: referrers.length > 0 ? 'pass' : 'warning',
          message: `找到 ${referrers.length} 个推荐人`
        })

        // 测试5: 详细奖励计算测试
        let correctClients = 0
        let incorrectClients = 0
        let totalDiscrepancy = 0

        for (const referrer of referrers) {
          const referredClients = clients.filter(c => c.referrerId === referrer.id)
          const referredData = []
          let totalCalculatedReward = 0

          for (const referred of referredClients) {
            const clientOrders = orders.filter(order => order.clientId === referred.id)
            const rewardResult = calculateTotalReferralReward(clientOrders, factoryId)
            totalCalculatedReward += rewardResult.totalReward

            referredData.push({
              id: referred.id,
              name: referred.name,
              phone: referred.phone,
              orders: clientOrders.map(order => ({
                id: order.id,
                orderNumber: order.orderNumber,
                totalAmount: safeNumber(order.totalAmount),
                items: order.items || []
              }))
            })
          }

          const databaseReward = safeNumber(referrer.referralReward || 0)
          const discrepancy = Math.abs(totalCalculatedReward - databaseReward)
          const isCorrect = discrepancy < 0.01 // 允许1分钱误差

          if (isCorrect) {
            correctClients++
          } else {
            incorrectClients++
            totalDiscrepancy += discrepancy
          }

          rewardData.push({
            clientId: referrer.id,
            clientName: referrer.name,
            clientPhone: referrer.phone,
            referredClients: referredData,
            calculatedReward: totalCalculatedReward,
            databaseReward,
            isCorrect,
            discrepancy
          })
        }

        results.push({
          testName: '奖励计算准确性',
          status: incorrectClients === 0 ? 'pass' : 'fail',
          message: `${correctClients} 个客户奖励正确，${incorrectClients} 个客户奖励有误`
        })

        // 测试6: 高端风口识别测试
        const allOrderItems = orders.flatMap(order => order.items || [])
        const premiumItems = allOrderItems.filter(item => 
          isPremiumVent(item.productType, item.productName)
        )
        
        results.push({
          testName: '高端风口识别',
          status: 'pass',
          message: `在 ${allOrderItems.length} 个订单项中识别出 ${premiumItems.length} 个高端风口产品`
        })

        setRewardTestData(rewardData)
        setSummary({
          totalTests: results.length,
          passedTests: results.filter(r => r.status === 'pass').length,
          failedTests: results.filter(r => r.status === 'fail').length,
          warningTests: results.filter(r => r.status === 'warning').length,
          totalClients: referrers.length,
          correctClients,
          incorrectClients,
          totalDiscrepancy
        })

      } catch (error) {
        results.push({
          testName: '数据库连接',
          status: 'fail',
          message: `数据库连接失败: ${error.message}`
        })
      }

      setTestResults(results)
      console.log('✅ 全面奖励系统测试完成')

    } catch (error) {
      console.error('❌ 奖励系统测试失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const fixAllRewards = async () => {
    try {
      setLoading(true)
      console.log('🔧 开始修复所有奖励...')

      const response = await fetch('/api/clients/fix-reward-display', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const data = await response.json()

      if (data.success) {
        console.log('✅ 奖励修复完成')
        alert(`修复完成！成功修复 ${data.summary.successCount} 个客户的奖励`)
        // 重新运行测试
        await runComprehensiveTest()
      } else {
        console.error('❌ 修复失败:', data.error)
        alert(`修复失败: ${data.error}`)
      }

    } catch (error) {
      console.error('❌ 修复失败:', error)
      alert(`修复失败: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    runComprehensiveTest()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'fail': return <XCircle className="h-5 w-5 text-red-600" />
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-600" />
      default: return <AlertCircle className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'bg-green-100 text-green-800'
      case 'fail': return 'bg-red-100 text-red-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">奖励系统全面测试</h1>
            <p className="text-gray-600">全面测试和修复客户奖励系统的各个组件</p>
          </div>
          <div className="flex space-x-4">
            <Button onClick={runComprehensiveTest} disabled={loading} variant="outline">
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  测试中...
                </>
              ) : (
                <>
                  <TestTube className="h-4 w-4 mr-2" />
                  重新测试
                </>
              )}
            </Button>
            <Button onClick={fixAllRewards} disabled={loading}>
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  修复中...
                </>
              ) : (
                <>
                  <Wrench className="h-4 w-4 mr-2" />
                  修复所有问题
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 测试结果概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-blue-600">{summary.totalTests}</div>
              <p className="text-sm text-gray-600">总测试数</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-green-600">{summary.passedTests}</div>
              <p className="text-sm text-gray-600">通过测试</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-red-600">{summary.failedTests}</div>
              <p className="text-sm text-gray-600">失败测试</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-yellow-600">{summary.warningTests}</div>
              <p className="text-sm text-gray-600">警告测试</p>
            </CardContent>
          </Card>
        </div>

        {/* 测试结果详情 */}
        <Tabs defaultValue="results" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="results">测试结果</TabsTrigger>
            <TabsTrigger value="rewards">奖励详情</TabsTrigger>
          </TabsList>

          <TabsContent value="results" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>测试结果详情</CardTitle>
              </CardHeader>
              <CardContent>
                {testResults.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    暂无测试结果
                  </div>
                ) : (
                  <div className="space-y-3">
                    {testResults.map((result, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(result.status)}
                          <div>
                            <div className="font-medium">{result.testName}</div>
                            <div className="text-sm text-gray-600">{result.message}</div>
                          </div>
                        </div>
                        <Badge className={getStatusColor(result.status)}>
                          {result.status === 'pass' ? '通过' :
                           result.status === 'fail' ? '失败' : '警告'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rewards" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>奖励计算详情</CardTitle>
              </CardHeader>
              <CardContent>
                {rewardTestData.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    暂无奖励数据
                  </div>
                ) : (
                  <div className="space-y-4">
                    {rewardTestData.map((data, index) => (
                      <div key={index} className={`p-4 border rounded-lg ${data.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <div className="font-medium">{data.clientName}</div>
                            <div className="text-sm text-gray-600">{data.clientPhone}</div>
                          </div>
                          <div className="text-right">
                            <div className={`text-sm font-medium ${data.isCorrect ? 'text-green-600' : 'text-red-600'}`}>
                              {data.isCorrect ? '✓ 正确' : '✗ 错误'}
                            </div>
                            {!data.isCorrect && (
                              <div className="text-xs text-red-600">
                                差异: ¥{safeAmountFormat(data.discrepancy, 2)}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">计算奖励:</span>
                            <span className="ml-2 font-medium">¥{safeAmountFormat(data.calculatedReward, 2)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">数据库奖励:</span>
                            <span className="ml-2 font-medium">¥{safeAmountFormat(data.databaseReward, 2)}</span>
                          </div>
                        </div>
                        <div className="mt-3">
                          <div className="text-sm text-gray-600 mb-2">推荐客户 ({data.referredClients.length}):</div>
                          <div className="space-y-2">
                            {data.referredClients.map((referred, refIndex) => (
                              <div key={refIndex} className="text-xs bg-white p-2 rounded border">
                                <div className="font-medium">{referred.name} ({referred.phone})</div>
                                <div className="text-gray-600">
                                  {referred.orders.length} 个订单，总额 ¥{safeAmountFormat(
                                    referred.orders.reduce((sum, order) => sum + order.totalAmount, 0), 2
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
