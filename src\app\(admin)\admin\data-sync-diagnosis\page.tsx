"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from "@/lib/database"
import { dataSyncService } from "@/lib/services/data-sync"
import { api } from "@/lib/api/client"
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Database,
  Server,
  Network,
  Factory,
  Users,
  ShoppingCart
} from "lucide-react"

interface DiagnosisResult {
  step: string
  status: 'success' | 'error' | 'warning'
  message: string
  details?: any
  timestamp: Date
}

export default function DataSyncDiagnosis() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<DiagnosisResult[]>([])
  const [summary, setSummary] = useState({
    totalTests: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  })

  const addResult = (step: string, status: 'success' | 'error' | 'warning', message: string, details?: unknown) => {
    const result: DiagnosisResult = {
      step,
      status,
      message,
      details,
      timestamp: new Date()
    }
    setResults(prev => [...prev, result])
    
    setSummary(prev => ({
      totalTests: prev.totalTests + 1,
      passed: prev.passed + (status === 'success' ? 1 : 0),
      failed: prev.failed + (status === 'error' ? 1 : 0),
      warnings: prev.warnings + (status === 'warning' ? 1 : 0)
    }))
  }

  const runDiagnosis = async () => {
    try {
      setLoading(true)
      setResults([])
      setSummary({ totalTests: 0, passed: 0, failed: 0, warnings: 0 })

      console.log('🔍 开始数据同步诊断...')

      // 步骤1: 检查数据库连接
      addResult('数据库连接', 'success', '开始检查数据库连接...')
      try {
        const factories = await db.getFactories()
        addResult('数据库连接', 'success', `数据库连接正常，发现 ${factories.length} 个工厂`, { factoryCount: factories.length })
        
        if (factories.length === 0) {
          addResult('工厂数据', 'warning', '数据库中没有工厂数据，请先添加工厂')
          return
        }

        // 步骤2: 检查API端点
        addResult('API端点', 'success', '开始检查API端点...')
        try {
          const apiResponse = await api.get('/api/factories', { requireAuth: false })
          if (apiResponse.success) {
            addResult('API端点', 'success', 'API端点响应正常', { response: apiResponse })
          } else {
            addResult('API端点', 'error', `API端点响应异常: ${apiResponse.error}`, { response: apiResponse })
          }
        } catch (apiError) {
          addResult('API端点', 'error', `API端点连接失败: ${apiError instanceof Error ? apiError.message : String(apiError)}`, { error: apiError })
        }

        // 步骤3: 逐个检查工厂数据
        addResult('工厂数据检查', 'success', '开始检查各工厂数据...')
        
        for (const factory of factories) {
          try {
            // 检查客户数据
            const clients = await db.getClientsByFactoryId(factory.id)
            addResult(`工厂${factory.name}-客户`, 'success', `客户数据正常: ${clients.length} 个客户`, { 
              factoryId: factory.id, 
              clientCount: clients.length 
            })

            // 检查订单数据
            const orders = await db.getOrdersByFactoryId(factory.id)
            addResult(`工厂${factory.name}-订单`, 'success', `订单数据正常: ${orders.length} 个订单`, { 
              factoryId: factory.id, 
              orderCount: orders.length 
            })

            // 检查统计数据
            const stats = await dataSyncService.getFactoryStatistics(factory.id)
            addResult(`工厂${factory.name}-统计`, 'success', `统计数据正常`, { 
              factoryId: factory.id, 
              stats 
            })

          } catch (factoryError) {
            addResult(`工厂${factory.name}`, 'error', `数据读取失败: ${factoryError instanceof Error ? factoryError.message : String(factoryError)}`, {
              factoryId: factory.id,
              error: factoryError
            })
          }
        }

        // 步骤4: 检查总部统计
        addResult('总部统计', 'success', '开始检查总部统计数据...')
        try {
          const headquartersStats = await dataSyncService.getHeadquartersStatistics()
          addResult('总部统计', 'success', '总部统计数据正常', { stats: headquartersStats })
        } catch (statsError) {
          addResult('总部统计', 'error', `总部统计失败: ${statsError instanceof Error ? statsError.message : String(statsError)}`, { error: statsError })
        }

        // 步骤5: 检查数据同步服务
        addResult('同步服务', 'success', '开始检查数据同步服务...')
        try {
          const syncStatus = dataSyncService.getSyncStatus()
          addResult('同步服务', 'success', '同步服务状态正常', { status: syncStatus })
          
          const syncEvents = dataSyncService.getSyncEvents()
          addResult('同步事件', 'success', `同步事件记录正常: ${syncEvents.length} 个事件`, { eventCount: syncEvents.length })
        } catch (syncError) {
          addResult('同步服务', 'error', `同步服务异常: ${syncError instanceof Error ? syncError.message : String(syncError)}`, { error: syncError })
        }

      } catch (dbError) {
        addResult('数据库连接', 'error', `数据库连接失败: ${dbError instanceof Error ? dbError.message : String(dbError)}`, { error: dbError })
      }

      addResult('诊断完成', 'success', '数据同步诊断已完成')
      console.log('✅ 数据同步诊断完成')

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      addResult('诊断异常', 'error', `诊断过程异常: ${errorMessage}`, { error })
      console.error('❌ 诊断过程异常:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      default:
        return <RefreshCw className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'error':
        return 'border-red-200 bg-red-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  useEffect(() => {
    runDiagnosis()
  }, [])

  return (
    <DashboardLayout role="admin">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">数据同步诊断</h1>
            <p className="text-gray-600">检查总部与加工厂之间的数据连接状态</p>
          </div>
          <Button
            onClick={runDiagnosis}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            重新诊断
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总测试数</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalTests}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">通过</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{summary.passed}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">失败</CardTitle>
              <XCircle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{summary.failed}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">警告</CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{summary.warnings}</div>
            </CardContent>
          </Card>
        </div>

        {/* Diagnosis Results */}
        <Card>
          <CardHeader>
            <CardTitle>诊断结果</CardTitle>
            <CardDescription>详细的数据连接检查结果</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 border rounded-lg ${getStatusColor(result.status)}`}
                >
                  <div className="flex items-start space-x-3">
                    {getStatusIcon(result.status)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{result.step}</h4>
                        <span className="text-sm text-gray-500">
                          {result.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700 mt-1">{result.message}</p>
                      {result.details && (
                        <details className="mt-2">
                          <summary className="text-sm text-gray-600 cursor-pointer">查看详情</summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {results.length === 0 && !loading && (
                <div className="text-center py-8 text-gray-500">点击"重新诊断"开始检查数据连接状态
                </div>
              )}
              
              {loading && (
                <div className="text-center py-8">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400" />
                  <p className="text-gray-500 mt-2">正在进行诊断...</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
