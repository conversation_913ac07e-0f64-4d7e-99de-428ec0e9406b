"use client"

import { useState } from "react"
import { Button  } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Plus, Calculator } from "lucide-react"

// 成本类型定义
const COST_TYPES = [
  { id: 'material', name: '原材料成本', description: '批量采购的原材料费用（通常不关联订单）' },
  { id: 'labor', name: '人工成本', description: '员工工资等固定人工费用（不关联订单）' },
  { id: 'equipment', name: '设备折旧', description: '生产设备的折旧费用（不关联订单）' },
  { id: 'energy', name: '水电费用', description: '工厂基础运营的水电费用（不关联订单）' },
  { id: 'transport', name: '运输费用', description: '送货费、物流费等（建议关联订单）' },
  { id: 'overhead', name: '管理费用', description: '办公、行政等管理费用（不关联订单）' },
  { id: 'other', name: '其他费用', description: '出差费、特殊工艺费等（可选择关联订单）' }
]

interface AddCostDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  orders: unknown[]
  onCostAdded: (cost: unknown) => void
}

export function AddCostDialog({ open, onOpenChange, orders, onCostAdded }: AddCostDialogProps) {
  const [formData, setFormData] = useState({
    orderId: "no-order",
    productName: '',
    costType: undefined,
    costName: '',
    amount: '',
    quantity: '1',
    unit: '元',
    notes: ''
  })

  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.costType || !formData.costName || !formData.amount) {
      alert('请填写必填字段：成本类型、成本项目名称、单价')
      return
    }

    setLoading(true)

    try {
      const newCost = {
        id: `cost-${Date.now()}`,
        orderId: (formData.orderId && formData.orderId !== "no-order") ? formData.orderId : null, // 允许为空，表示不关联订单
        productName: formData.productName,
        costType: formData.costType,
        costName: formData.costName,
        amount: parseFloat(formData.amount),
        unit: formData.unit,
        quantity: parseInt(formData.quantity) || 1,
        totalCost: parseFloat(formData.amount) * (parseInt(formData.quantity) || 1),
        date: new Date(),
        notes: formData.notes,
        createdBy: 'current-user', // 实际应用中从用户状态获取
        factoryId: 'current-factory' // 实际应用中从工厂状态获取
      }

      onCostAdded(newCost)
      
      // 重置表单
      setFormData({
        orderId: "no-order",
        productName: '',
        costType: undefined,
        costName: '',
        amount: '',
        quantity: '1',
        unit: '元',
        notes: ''
      })
      
      onOpenChange(false)
      
      console.log('✅ 成本添加成功:', newCost)
    } catch (error) {
      console.error('❌ 添加成本失败:', error)
      alert('添加成本失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const selectedOrder = (formData.orderId && formData.orderId !== "no-order") ? orders.find(order => order.id === formData.orderId) : null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] bg-white border-2 border-gray-200 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center text-gray-900">
            <Calculator className="h-5 w-5 mr-2 text-green-600" />
            添加成本项目
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            添加工厂成本项目。固定成本（工资、原材料采购）无需关联订单，变动成本（送货费、出差费）可关联具体订单
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 选择订单 */}
            <div className="space-y-2">
              <Label htmlFor="orderId">关联订单 <span className="text-xs text-gray-500">(可选，仅送货费等直接费用需要关联)</span></Label>
              <Select value={formData.orderId || "no-order"} onValueChange={(value) => handleInputChange('orderId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择订单（可不选）" />
                </SelectTrigger>
                <SelectContent className="max-h-80 bg-white border-2 border-gray-200 shadow-xl">
                  <SelectItem value="no-order" className="py-3 hover:bg-gray-100 focus:bg-gray-100">
                    <div className="flex flex-col space-y-1 w-full">
                      <div className="flex items-center">
                        <span className="font-medium text-gray-700">💼 不关联订单</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        工资、原材料采购、水电费等固定成本
                      </div>
                    </div>
                  </SelectItem>
                  {orders.map((order) => (
                    <SelectItem key={order.id} value={order.id} className="py-3 hover:bg-gray-100 focus:bg-gray-100">
                      <div className="flex flex-col space-y-1 w-full">
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-gray-900">
                            {order.orderNumber || `订单-${order.id.slice(-6)}`}
                          </span>
                          <span className="text-sm font-semibold text-green-600">
                            ¥{order.totalAmount?.toLocaleString() || '0'}
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>{order.clientName || '未知客户'}</span>
                          <span>{order.createdAt ? new Date(order.createdAt).toLocaleDateString('zh-CN') : ''}</span>
                        </div>
                        {order.projectAddress && (
                          <div className="text-xs text-gray-500 truncate">
                            📍 {order.projectAddress}
                          </div>
                        )}
                        <div className="text-xs text-blue-600">
                          {order.items?.length || 0}个产品 • {order.items?.reduce((sum: number, item: unknown) => sum + (item.quantity || 0), 0) || 0}件
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 产品名称 */}
            <div className="space-y-2">
              <Label htmlFor="productName">产品名称</Label>
              <Input
                id="productName"
                value={formData.productName}
                onChange={(e) => handleInputChange('productName', e.target.value)}
                placeholder="输入产品名称"
              />
              {selectedOrder && selectedOrder.items && selectedOrder.items.length > 0 && (
                <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="text-xs font-medium text-blue-700 mb-2">订单产品列表:</div>
                  <div className="space-y-1">
                    {selectedOrder.items.slice(0, 3).map((item: unknown, index: number) => (
                      <div key={index} className="text-xs text-blue-600 flex justify-between">
                        <span>{item.productName} ({item.specifications || ''})</span>
                        <span>{item.quantity}件</span>
                      </div>
                    ))}
                    {selectedOrder.items.length > 3 && (
                      <div className="text-xs text-blue-500">
                        ...还有{selectedOrder.items.length - 3}个产品
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 成本类型 */}
            <div className="space-y-2">
              <Label htmlFor="costType">成本类型 *</Label>
              <Select value={formData.costType || ""} onValueChange={(value) => handleInputChange('costType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择成本类型" />
                </SelectTrigger>
                <SelectContent className="bg-white border-2 border-gray-200 shadow-xl">
                  {COST_TYPES.map((type) => (
                    <SelectItem key={type.id} value={type.id} className="py-3 hover:bg-gray-100 focus:bg-gray-100">
                      <div className="flex flex-col space-y-1 w-full">
                        <div className="font-medium text-gray-900">{type.name}</div>
                        <div className="text-xs text-gray-500">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 成本项目名称 */}
            <div className="space-y-2">
              <Label htmlFor="costName">成本项目名称 *</Label>
              <Input
                id="costName"
                value={formData.costName}
                onChange={(e) => handleInputChange('costName', e.target.value)}
                placeholder="例如：铝合金材料费"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 单价 */}
            <div className="space-y-2">
              <Label htmlFor="amount">单价 *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                value={formData.amount}
                onChange={(e) => handleInputChange('amount', e.target.value)}
                placeholder="0.00"
                required
              />
            </div>

            {/* 数量 */}
            <div className="space-y-2">
              <Label htmlFor="quantity">数量</Label>
              <Input
                id="quantity"
                type="number"
                value={formData.quantity}
                onChange={(e) => handleInputChange('quantity', e.target.value)}
                placeholder="1"
              />
            </div>

            {/* 单位 */}
            <div className="space-y-2">
              <Label htmlFor="unit">单位</Label>
              <Select value={formData.unit} onValueChange={(value) => handleInputChange('unit', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-white border-2 border-gray-200 shadow-xl">
                  <SelectItem value="元" className="py-2 hover:bg-gray-100 focus:bg-gray-100">
                    <div className="flex items-center space-x-2">
                      <span>💰</span>
                      <span>元 (固定费用)</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="元/个" className="py-2 hover:bg-gray-100 focus:bg-gray-100">
                    <div className="flex items-center space-x-2">
                      <span>📦</span>
                      <span>元/个 (按件计费)</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="元/kg" className="py-2 hover:bg-gray-100 focus:bg-gray-100">
                    <div className="flex items-center space-x-2">
                      <span>⚖️</span>
                      <span>元/kg (按重量)</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="元/m" className="py-2 hover:bg-gray-100 focus:bg-gray-100">
                    <div className="flex items-center space-x-2">
                      <span>📏</span>
                      <span>元/m (按长度)</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="元/m²" className="py-2 hover:bg-gray-100 focus:bg-gray-100">
                    <div className="flex items-center space-x-2">
                      <span>📐</span>
                      <span>元/m² (按面积)</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="元/小时" className="py-2 hover:bg-gray-100 focus:bg-gray-100">
                    <div className="flex items-center space-x-2">
                      <span>⏰</span>
                      <span>元/小时 (按工时)</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 总成本预览 */}
          {formData.amount && formData.quantity && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm text-green-700">总成本:</span>
                <span className="text-lg font-bold text-green-800">
                  ¥{(parseFloat(formData.amount) * parseInt(formData.quantity || '1')).toFixed(2)}
                </span>
              </div>
            </div>
          )}

          {/* 备注 */}
          <div className="space-y-2">
            <Label htmlFor="notes">备注说明</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="添加成本说明或备注信息..."
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit" disabled={loading} className="bg-green-600 hover:bg-green-700">
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  添加中...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  添加成本
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
