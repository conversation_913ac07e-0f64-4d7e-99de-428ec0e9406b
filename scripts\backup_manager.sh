#!/bin/bash
# backup_manager.sh - 备份管理脚本
# 使用方法: ./backup_manager.sh {list|create|restore|clean} [参数]

set -e

# 配置
BACKUP_BASE_DIR="/backups"
PROJECT_DIR="/opt/factorysystem"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：彩色输出
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：显示帮助
show_help() {
    echo "风口云平台备份管理脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 {list|create|restore|clean} [参数]"
    echo ""
    echo "命令:"
    echo "  list                    列出所有备份"
    echo "  create [备份名称]       创建新备份"
    echo "  restore <备份名称>      恢复指定备份"
    echo "  clean [天数]            清理指定天数前的备份（默认30天）"
    echo ""
    echo "示例:"
    echo "  $0 list"
    echo "  $0 create manual_backup"
    echo "  $0 restore auto_20250622_140000"
    echo "  $0 clean 7"
}

# 函数：列出备份
list_backups() {
    print_status $BLUE "📋 可用备份列表:"
    echo ""
    
    if [ ! -d "$BACKUP_BASE_DIR" ]; then
        print_status $YELLOW "⚠️ 备份目录不存在: $BACKUP_BASE_DIR"
        return 0
    fi
    
    # 检查是否有备份
    if [ -z "$(ls -A $BACKUP_BASE_DIR 2>/dev/null)" ]; then
        print_status $YELLOW "📭 没有找到任何备份"
        return 0
    fi
    
    # 显示备份列表
    printf "%-25s %-15s %-10s %s\n" "备份名称" "创建时间" "大小" "状态"
    printf "%-25s %-15s %-10s %s\n" "------------------------" "--------------" "---------" "------"
    
    for backup_dir in $BACKUP_BASE_DIR/*/; do
        if [ -d "$backup_dir" ]; then
            backup_name=$(basename "$backup_dir")
            
            # 获取创建时间
            create_time=$(stat -c %y "$backup_dir" 2>/dev/null | cut -d' ' -f1 | tr -d '-' || echo "未知")
            
            # 计算大小
            size=$(du -sh "$backup_dir" 2>/dev/null | cut -f1 || echo "未知")
            
            # 检查备份完整性
            status="✅ 完整"
            if [ ! -f "$backup_dir/database.sql" ]; then
                status="❌ 缺少数据库"
            elif [ ! -s "$backup_dir/database.sql" ]; then
                status="⚠️ 数据库为空"
            fi
            
            printf "%-25s %-15s %-10s %s\n" "$backup_name" "$create_time" "$size" "$status"
        fi
    done
    
    echo ""
    print_status $BLUE "💾 备份目录: $BACKUP_BASE_DIR"
    print_status $BLUE "📊 总备份数: $(ls -1 $BACKUP_BASE_DIR | wc -l)"
}

# 函数：创建备份
create_backup() {
    local backup_name="$1"
    
    if [ -z "$backup_name" ]; then
        backup_name="manual_$(date +%Y%m%d_%H%M%S)"
    fi
    
    local backup_dir="$BACKUP_BASE_DIR/$backup_name"
    
    print_status $BLUE "💾 创建备份: $backup_name"
    
    # 检查备份是否已存在
    if [ -d "$backup_dir" ]; then
        print_status $RED "❌ 备份已存在: $backup_name"
        exit 1
    fi
    
    # 创建备份目录
    mkdir -p "$backup_dir"
    
    # 检查项目目录
    if [ ! -d "$PROJECT_DIR" ]; then
        print_status $RED "❌ 项目目录不存在: $PROJECT_DIR"
        exit 1
    fi
    
    cd "$PROJECT_DIR"
    
    # 备份数据库
    print_status $YELLOW "  📊 备份数据库..."
    if docker-compose exec -T postgres pg_dump -U factorysystem factorysystem > "$backup_dir/database.sql" 2>/dev/null; then
        print_status $GREEN "  ✅ 数据库备份完成"
    else
        print_status $RED "  ❌ 数据库备份失败"
        rm -rf "$backup_dir"
        exit 1
    fi
    
    # 备份应用文件
    print_status $YELLOW "  📁 备份应用文件..."
    if tar -czf "$backup_dir/app_files.tar.gz" ./uploads ./logs 2>/dev/null; then
        print_status $GREEN "  ✅ 应用文件备份完成"
    else
        print_status $YELLOW "  ⚠️ 应用文件备份部分失败（可能某些目录不存在）"
    fi
    
    # 备份代码
    print_status $YELLOW "  💻 备份代码..."
    if tar -czf "$backup_dir/code.tar.gz" \
        --exclude='node_modules' \
        --exclude='.next' \
        --exclude='uploads' \
        --exclude='logs' \
        --exclude='.git' \
        . 2>/dev/null; then
        print_status $GREEN "  ✅ 代码备份完成"
    else
        print_status $RED "  ❌ 代码备份失败"
        rm -rf "$backup_dir"
        exit 1
    fi
    
    # 备份配置文件
    print_status $YELLOW "  ⚙️ 备份配置文件..."
    cp .env.production "$backup_dir/" 2>/dev/null || cp .env.local "$backup_dir/" 2>/dev/null || true
    cp docker-compose.yml "$backup_dir/" 2>/dev/null || true
    cp nginx.conf "$backup_dir/" 2>/dev/null || true
    
    # 创建备份信息文件
    cat > "$backup_dir/backup_info.txt" << EOF
备份名称: $backup_name
创建时间: $(date)
项目目录: $PROJECT_DIR
备份类型: 完整备份
创建者: $(whoami)
主机名: $(hostname)
EOF
    
    # 验证备份
    if [ -f "$backup_dir/database.sql" ] && [ -s "$backup_dir/database.sql" ]; then
        print_status $GREEN "✅ 备份创建成功: $backup_name"
        print_status $BLUE "📍 备份位置: $backup_dir"
        
        # 显示备份大小
        backup_size=$(du -sh "$backup_dir" | cut -f1)
        print_status $BLUE "📊 备份大小: $backup_size"
        
        # 记录到历史
        echo "$(date): 创建备份 $backup_name" >> "$PROJECT_DIR/.backup_history"
    else
        print_status $RED "❌ 备份验证失败"
        rm -rf "$backup_dir"
        exit 1
    fi
}

# 函数：恢复备份
restore_backup() {
    local backup_name="$1"
    
    if [ -z "$backup_name" ]; then
        print_status $RED "❌ 请指定要恢复的备份名称"
        echo ""
        list_backups
        exit 1
    fi
    
    local backup_dir="$BACKUP_BASE_DIR/$backup_name"
    
    if [ ! -d "$backup_dir" ]; then
        print_status $RED "❌ 备份不存在: $backup_name"
        echo ""
        list_backups
        exit 1
    fi
    
    print_status $YELLOW "🔄 恢复备份: $backup_name"
    
    # 显示备份信息
    if [ -f "$backup_dir/backup_info.txt" ]; then
        echo ""
        print_status $BLUE "📋 备份信息:"
        cat "$backup_dir/backup_info.txt"
        echo ""
    fi
    
    # 确认恢复
    read -p "确定要恢复此备份吗？这将覆盖当前系统！(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status $YELLOW "恢复已取消"
        exit 0
    fi
    
    cd "$PROJECT_DIR"
    
    # 停止所有服务
    print_status $YELLOW "  🛑 停止服务..."
    docker-compose down
    
    # 恢复数据库
    print_status $YELLOW "  🗄️ 恢复数据库..."
    docker-compose up -d postgres
    sleep 10
    
    # 重建数据库
    docker-compose exec -T postgres psql -U postgres -c "DROP DATABASE IF EXISTS factorysystem;" 2>/dev/null || true
    docker-compose exec -T postgres psql -U postgres -c "CREATE DATABASE factorysystem;" 2>/dev/null || true
    
    # 导入数据库
    if docker-compose exec -T postgres psql -U factorysystem factorysystem < "$backup_dir/database.sql" 2>/dev/null; then
        print_status $GREEN "  ✅ 数据库恢复完成"
    else
        print_status $RED "  ❌ 数据库恢复失败"
        exit 1
    fi
    
    # 恢复代码
    print_status $YELLOW "  💻 恢复代码..."
    if tar -xzf "$backup_dir/code.tar.gz" 2>/dev/null; then
        print_status $GREEN "  ✅ 代码恢复完成"
    else
        print_status $RED "  ❌ 代码恢复失败"
        exit 1
    fi
    
    # 恢复应用文件
    print_status $YELLOW "  📁 恢复应用文件..."
    if tar -xzf "$backup_dir/app_files.tar.gz" 2>/dev/null; then
        print_status $GREEN "  ✅ 应用文件恢复完成"
    else
        print_status $YELLOW "  ⚠️ 应用文件恢复失败（可能备份中不存在）"
    fi
    
    # 恢复配置文件
    print_status $YELLOW "  ⚙️ 恢复配置文件..."
    cp "$backup_dir/.env.production" .env.production 2>/dev/null || cp "$backup_dir/.env.local" .env.local 2>/dev/null || true
    cp "$backup_dir/docker-compose.yml" . 2>/dev/null || true
    cp "$backup_dir/nginx.conf" . 2>/dev/null || true
    
    # 重新构建和启动服务
    print_status $YELLOW "  🏗️ 重新构建..."
    docker-compose build
    
    print_status $YELLOW "  🚀 启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    print_status $YELLOW "  ⏳ 等待服务启动..."
    sleep 30
    
    # 验证恢复
    print_status $YELLOW "  ✅ 验证恢复..."
    if curl -f -s http://localhost:3000/api/health > /dev/null 2>&1; then
        print_status $GREEN "✅ 备份恢复成功！"
        
        # 记录恢复历史
        echo "$(date): 恢复备份 $backup_name" >> .backup_history
        
        # 显示服务状态
        echo ""
        print_status $BLUE "📊 服务状态:"
        docker-compose ps
    else
        print_status $RED "❌ 恢复后验证失败，请检查日志"
        exit 1
    fi
}

# 函数：清理旧备份
clean_backups() {
    local days="${1:-30}"
    
    print_status $BLUE "🧹 清理 $days 天前的备份..."
    
    if [ ! -d "$BACKUP_BASE_DIR" ]; then
        print_status $YELLOW "⚠️ 备份目录不存在: $BACKUP_BASE_DIR"
        return 0
    fi
    
    # 查找要删除的备份
    local old_backups=$(find "$BACKUP_BASE_DIR" -type d -mtime +$days -name "*_*" 2>/dev/null)
    
    if [ -z "$old_backups" ]; then
        print_status $GREEN "✅ 没有需要清理的备份"
        return 0
    fi
    
    echo "将要删除的备份:"
    echo "$old_backups"
    echo ""
    
    read -p "确定要删除这些备份吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status $YELLOW "清理已取消"
        return 0
    fi
    
    # 删除旧备份
    local deleted_count=0
    while IFS= read -r backup_dir; do
        if [ -d "$backup_dir" ]; then
            backup_name=$(basename "$backup_dir")
            print_status $YELLOW "  🗑️ 删除备份: $backup_name"
            rm -rf "$backup_dir"
            deleted_count=$((deleted_count + 1))
        fi
    done <<< "$old_backups"
    
    print_status $GREEN "✅ 清理完成，删除了 $deleted_count 个备份"
}

# 主函数
main() {
    case "$1" in
        list)
            list_backups
            ;;
        create)
            create_backup "$2"
            ;;
        restore)
            restore_backup "$2"
            ;;
        clean)
            clean_backups "$2"
            ;;
        -h|--help|help)
            show_help
            ;;
        *)
            print_status $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
