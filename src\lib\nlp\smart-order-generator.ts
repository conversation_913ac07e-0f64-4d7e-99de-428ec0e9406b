/**
 * 🇨🇳 风口云平台 - 智能订单生成器
 * 
 * 基于意图识别结果自动生成风口订单
 */

import { IntentRecognitionResult, VentItemIntent } from './intent-recognition'
import { OrderItem } from '@/types'
import { calculateVentPrice } from '@/lib/utils/vent-type-classifier'

// 默认单价配置（元/㎡ 或 元/m）
const DEFAULT_UNIT_PRICES = {
  // 常规风口 (元/㎡)
  double_white_outlet: 280,
  double_black_outlet: 300,
  white_black_bottom_outlet: 320,
  white_black_bottom_return: 320,
  white_return: 280,
  black_return: 300,
  white_linear: 280,
  black_linear: 300,
  white_linear_return: 280,
  black_linear_return: 300,
  maintenance: 280,
  
  // 高端风口 (元/m)
  high_end_white_outlet: 150,
  high_end_black_outlet: 160,
  high_end_white_return: 150,
  high_end_black_return: 160,
  arrow_outlet: 180,
  arrow_return: 180,
  claw_outlet: 200,
  claw_return: 200
}

// 智能订单生成结果
export interface SmartOrderResult {
  success: boolean
  orderData?: {
    projectAddress: string
    items: OrderItem[]
    totalAmount: number
    notes: string
    estimatedFloor?: string
  }
  suggestions?: string[]
  warnings?: string[]
  confidence: number
  originalIntent: IntentRecognitionResult
}

/**
 * 智能生成订单
 */
export function generateSmartOrder(
  intentResult: IntentRecognitionResult,
  factoryId: string,
  createdBy: string
): SmartOrderResult {
  console.log('🤖 开始智能订单生成...')
  console.log('📊 意图识别结果:', intentResult)
  
  if (intentResult.intent !== 'create_order') {
    return {
      success: false,
      confidence: 0,
      originalIntent: intentResult,
      warnings: ['未识别到订单创建意图']
    }
  }
  
  if (intentResult.entities.ventItems.length === 0) {
    return {
      success: false,
      confidence: 0,
      originalIntent: intentResult,
      warnings: ['未识别到有效的风口项目']
    }
  }
  
  // 生成订单项目
  const { items, warnings, suggestions } = generateOrderItems(intentResult.entities.ventItems)
  
  // 计算总金额
  const totalAmount = items.reduce((sum, item) => sum + item.totalPrice, 0)
  
  // 生成项目地址
  const projectAddress = generateProjectAddress(intentResult)
  
  // 生成备注
  const notes = generateOrderNotes(intentResult)
  
  const orderData = {
    projectAddress,
    items,
    totalAmount: Math.round(totalAmount * 10) / 10, // 保留一位小数
    notes,
    estimatedFloor: intentResult.entities.floorInfo
  }
  
  const result: SmartOrderResult = {
    success: true,
    orderData,
    suggestions,
    warnings,
    confidence: intentResult.confidence,
    originalIntent: intentResult
  }
  
  console.log('✅ 智能订单生成完成:', result)
  return result
}

/**
 * 生成订单项目
 */
function generateOrderItems(ventItems: VentItemIntent[]): {
  items: OrderItem[]
  warnings: string[]
  suggestions: string[]
} {
  const items: OrderItem[] = []
  const warnings: string[] = []
  const suggestions: string[] = []
  
  ventItems.forEach((ventItem, index) => {
    try {
      // 获取单价
      const unitPrice = getUnitPrice(ventItem.type)
      
      // 计算总价
      const totalPrice = calculateVentPrice({
        productType: ventItem.type,
        length: ventItem.length,
        width: ventItem.width,
        quantity: ventItem.quantity,
        unitPrice
      })
      
      // 生成产品名称
      const productName = generateProductName(ventItem)
      
      // 生成规格说明
      const specifications = `${ventItem.length}×${ventItem.width}mm`
      
      const orderItem: OrderItem = {
        id: `item_${Date.now()}_${index}`,
        productName,
        productType: ventItem.type as any,
        specifications,
        dimensions: {
          length: ventItem.length,
          width: ventItem.width
        },
        quantity: ventItem.quantity,
        unitPrice,
        totalPrice,
        notes: ventItem.notes || '',
        calculatedBy: getCalculationMethod(ventItem.type)
      }
      
      items.push(orderItem)
      
      // 添加建议
      if (ventItem.confidence < 0.7) {
        suggestions.push(`项目${index + 1}识别置信度较低(${(ventItem.confidence * 100).toFixed(1)}%)，请核实`)
      }
      
      if (ventItem.width > ventItem.length) {
        suggestions.push(`项目${index + 1}已自动调整尺寸顺序(长×宽)`)
      }
      
    } catch (error) {
      warnings.push(`项目${index + 1}生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  })
  
  return { items, warnings, suggestions }
}

/**
 * 获取单价
 */
function getUnitPrice(productType: string): number {
  return DEFAULT_UNIT_PRICES[productType as keyof typeof DEFAULT_UNIT_PRICES] || 280
}

/**
 * 生成产品名称
 */
function generateProductName(ventItem: VentItemIntent): string {
  const typeNames = {
    double_white_outlet: '双层白色出风口',
    double_black_outlet: '双层黑色出风口',
    white_black_bottom_outlet: '白色黑底出风口',
    white_black_bottom_return: '白色黑底回风口',
    white_return: '白色回风口',
    black_return: '黑色回风口',
    white_linear: '白色线型风口',
    black_linear: '黑色线型风口',
    white_linear_return: '白色线型回风口',
    black_linear_return: '黑色线型回风口',
    maintenance: '检修口'
  }
  
  let name = typeNames[ventItem.type as keyof typeof typeNames] || '出风口'
  
  // 根据颜色调整名称
  if (ventItem.color === 'black' && !name.includes('黑')) {
    name = name.replace('白色', '黑色')
  }
  
  return name
}

/**
 * 获取计算方法说明
 */
function getCalculationMethod(productType: string): string {
  const isRegular = [
    'double_white_outlet', 'double_black_outlet', 'white_black_bottom_outlet',
    'white_black_bottom_return', 'white_return', 'black_return',
    'white_linear', 'black_linear', 'white_linear_return', 'black_linear_return',
    'maintenance'
  ].includes(productType)
  
  if (isRegular) {
    return '(长+60mm)×(宽+60mm)÷1,000,000×单价×数量'
  } else {
    return '(长+120mm)÷1,000×单价×数量'
  }
}

/**
 * 生成项目地址
 */
function generateProjectAddress(intentResult: IntentRecognitionResult): string {
  const { projectName, floorInfo } = intentResult.entities
  
  let address = ''
  
  if (projectName) {
    address = projectName
  } else {
    address = '智能识别项目'
  }
  
  if (floorInfo) {
    address += ` ${floorInfo}`
  }
  
  return address
}

/**
 * 生成订单备注
 */
function generateOrderNotes(intentResult: IntentRecognitionResult): string {
  const notes: string[] = []
  
  notes.push(`🤖 智能识别生成 (置信度: ${(intentResult.confidence * 100).toFixed(1)}%)`)
  
  if (intentResult.entities.ventItems.length > 0) {
    notes.push(`📊 识别到 ${intentResult.entities.ventItems.length} 个风口项目`)
  }
  
  // 添加原始文本摘要
  const textSummary = intentResult.rawText.substring(0, 50)
  if (textSummary.length < intentResult.rawText.length) {
    notes.push(`📝 原文: ${textSummary}...`)
  } else {
    notes.push(`📝 原文: ${textSummary}`)
  }
  
  return notes.join('\n')
}

/**
 * 验证订单数据
 */
export function validateSmartOrder(orderData: SmartOrderResult['orderData']): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  if (!orderData) {
    errors.push('订单数据为空')
    return { isValid: false, errors }
  }
  
  if (!orderData.projectAddress || orderData.projectAddress.trim().length === 0) {
    errors.push('项目地址不能为空')
  }
  
  if (!orderData.items || orderData.items.length === 0) {
    errors.push('订单项目不能为空')
  }
  
  orderData.items.forEach((item, index) => {
    if (!item.productName || item.productName.trim().length === 0) {
      errors.push(`项目${index + 1}: 产品名称不能为空`)
    }
    
    if (!item.dimensions?.length || item.dimensions.length <= 0) {
      errors.push(`项目${index + 1}: 长度必须大于0`)
    }
    
    if (!item.dimensions?.width || item.dimensions.width <= 0) {
      errors.push(`项目${index + 1}: 宽度必须大于0`)
    }
    
    if (!item.quantity || item.quantity <= 0) {
      errors.push(`项目${index + 1}: 数量必须大于0`)
    }
    
    if (!item.unitPrice || item.unitPrice <= 0) {
      errors.push(`项目${index + 1}: 单价必须大于0`)
    }
  })
  
  if (orderData.totalAmount <= 0) {
    errors.push('订单总金额必须大于0')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 格式化智能订单摘要
 */
export function formatOrderSummary(result: SmartOrderResult): string {
  if (!result.success || !result.orderData) {
    return '❌ 订单生成失败'
  }
  
  const { orderData } = result
  const summary: string[] = []
  
  summary.push(`📍 项目: ${orderData.projectAddress}`)
  summary.push(`📊 项目数: ${orderData.items.length}个`)
  summary.push(`💰 总金额: ¥${orderData.totalAmount.toFixed(1)}`)
  summary.push(`🎯 置信度: ${(result.confidence * 100).toFixed(1)}%`)
  
  if (result.suggestions && result.suggestions.length > 0) {
    summary.push(`💡 建议: ${result.suggestions.length}条`)
  }
  
  if (result.warnings && result.warnings.length > 0) {
    summary.push(`⚠️ 警告: ${result.warnings.length}条`)
  }
  
  return summary.join('\n')
}
