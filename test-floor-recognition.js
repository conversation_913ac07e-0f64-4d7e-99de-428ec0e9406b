// 测试楼层识别逻辑
const testText = `江苏·淮安市安豪栅栏有限公司
一楼厨房出风口125x1435
回风口245x1120
客厅出风口130×2770
回风口150x800243x1750
次卧出风口150x800
回风口245x950
主卧出风口150x900
回风口250x.1020
二楼客厅出凤口175x1300
回风口295x1650
主卧出风口140x1500
回风口250x1500
次卧出风口140x800
同风口245x850`;

console.log('🧪 测试楼层识别逻辑');
console.log('📝 测试文本:');
console.log(testText);

console.log('\n🔍 楼层识别分析:');

// 模拟楼层识别逻辑
const lines = testText.split('\n').map(line => line.trim()).filter(line => line);
let currentFloor = '';
let floorChanges = [];

// 楼层提取函数（模拟）
function extractFloorFromLine(line) {
  const trimmed = line.trim();
  
  // 匹配行首的中文楼层
  const floorAtStartMatch = trimmed.match(/^(一|二|三|四|五|六|七|八|九|十)楼/);
  if (floorAtStartMatch) {
    const chineseFloor = floorAtStartMatch[1];
    const floorNumber = convertChineseFloorToNumber(chineseFloor);
    return floorNumber;
  }
  
  // 匹配行首的数字楼层
  const numericFloorMatch = trimmed.match(/^(\d+)[楼层F]/);
  if (numericFloorMatch) {
    return numericFloorMatch[1];
  }
  
  return null;
}

function convertChineseFloorToNumber(chineseFloor) {
  const chineseNumbers = {
    '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
    '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
  };
  return chineseNumbers[chineseFloor] || chineseFloor;
}

// 分析每一行
lines.forEach((line, index) => {
  console.log(`\n第${index + 1}行: "${line}"`);
  
  // 检查项目名称
  if (index === 0) {
    console.log('  → 项目名称，无楼层信息');
    return;
  }
  
  // 提取楼层信息
  const floorFromLine = extractFloorFromLine(line);
  if (floorFromLine && floorFromLine !== currentFloor) {
    currentFloor = floorFromLine;
    floorChanges.push({
      line: index + 1,
      text: line,
      floor: currentFloor
    });
    console.log(`  → 🏢 楼层变更: "${currentFloor}楼"`);
  } else if (currentFloor) {
    console.log(`  → 🏠 属于: "${currentFloor}楼"`);
  } else {
    console.log('  → ❓ 未确定楼层');
  }
});

console.log('\n📊 楼层变更总结:');
floorChanges.forEach((change, index) => {
  console.log(`${index + 1}. 第${change.line}行: "${change.text}" → ${change.floor}楼`);
});

console.log('\n🎯 预期结果:');
console.log('- 应该识别出2个楼层：1楼和2楼');
console.log('- 第2行开始的风口应归属1楼');
console.log('- 第10行开始的风口应归属2楼');
console.log(`\n✅ 实际识别楼层数: ${floorChanges.length}`);
