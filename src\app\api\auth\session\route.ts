/**
 * 🇨🇳 风口云平台 - 会话状态检查API
 */

import { NextRequest, NextResponse } from 'next/server'
import { validateSession } from '@/lib/middleware/session'

/**
 * 检查当前会话状态
 */
export async function GET(request: NextRequest) {
  try {
    const sessionResult = await validateSession(request)
    
    if (!sessionResult.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: sessionResult.error,
          shouldLogout: sessionResult.shouldLogout || false
        },
        { status: 401 }
      )
    }

    // 会话有效，返回用户信息
    return NextResponse.json({
      success: true,
      user: sessionResult.user,
      message: '会话有效'
    })

  } catch (error) {
    console.error('❌ 会话状态检查失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '会话检查异常',
        shouldLogout: true
      },
      { status: 500 }
    )
  }
}

/**
 * 登出当前会话
 */
export async function DELETE(request: NextRequest) {
  try {
    const sessionResult = await validateSession(request)
    
    if (sessionResult.isValid && sessionResult.user?.sessionId) {
      // 记录登出
      const { db } = await import('@/lib/database')
      await db.recordLogout(sessionResult.user.sessionId)
      
      console.log('✅ 用户登出成功:', sessionResult.user.username)
    }

    return NextResponse.json({
      success: true,
      message: '登出成功'
    })

  } catch (error) {
    console.error('❌ 登出失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '登出异常'
      },
      { status: 500 }
    )
  }
}
