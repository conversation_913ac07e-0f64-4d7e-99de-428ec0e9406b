#!/usr/bin/env node

/**
 * 🇨🇳 风口云平台 - 问题诊断脚本
 */

const fs = require('fs')

console.log('🔍 开始诊断系统问题...\n')

// 1. 检查环境变量文件
console.log('1️⃣ 检查环境变量文件...')
const envFiles = ['.env.local', '.env', '.env.production']
let envFileExists = false

for (const envFile of envFiles) {
  if (fs.existsSync(envFile)) {
    console.log(`✅ 找到环境变量文件: ${envFile}`)
    envFileExists = true
    
    const envContent = fs.readFileSync(envFile, 'utf8')
    console.log(`   内容预览: ${envContent.substring(0, 100)}...`)
    if (envContent.includes('DATABASE_URL')) {
      console.log('✅ DATABASE_URL 配置存在')
    } else {
      console.log('❌ DATABASE_URL 配置缺失')
    }
  }
}

// 2. 检查 Prisma 相关文件
console.log('\n2️⃣ 检查 Prisma 相关文件...')

if (fs.existsSync('prisma/schema.prisma')) {
  console.log('✅ Prisma schema 文件存在')
} else {
  console.log('❌ Prisma schema 文件不存在')
}

if (fs.existsSync('node_modules/.prisma/client')) {
  console.log('✅ Prisma 客户端已生成')
} else {
  console.log('❌ Prisma 客户端未生成')
}

// 3. 检查数据库文件
console.log('\n3️⃣ 检查数据库文件...')
const dbFiles = ['dev.db', 'database.db', 'factorysystem.db', 'prisma/dev.db']
let dbFileExists = false

for (const dbFile of dbFiles) {
  if (fs.existsSync(dbFile)) {
    console.log(`✅ 找到数据库文件: ${dbFile}`)
    const stats = fs.statSync(dbFile)
    console.log(`   文件大小: ${stats.size} 字节`)
    dbFileExists = true
  }
}

if (!dbFileExists) {
  console.log('❌ 未找到数据库文件')
}

// 4. 检查构建文件
console.log('\n4️⃣ 检查构建文件...')

if (fs.existsSync('.next')) {
  console.log('✅ Next.js 构建目录存在')
  const buildId = fs.existsSync('.next/BUILD_ID') ? fs.readFileSync('.next/BUILD_ID', 'utf8').trim() : '未知'
  console.log(`   构建ID: ${buildId}`)
} else {
  console.log('❌ Next.js 构建目录不存在')
}

// 5. 尝试测试 Prisma 客户端
console.log('\n5️⃣ 测试 Prisma 客户端...')

try {
  const { PrismaClient } = require('@prisma/client')
  console.log('✅ Prisma 客户端模块加载成功')
  
  const prisma = new PrismaClient()
  console.log('✅ Prisma 客户端实例创建成功')
  
} catch (error) {
  console.log('❌ Prisma 客户端问题:', error.message)
}

console.log('\n🏁 诊断完成！')
