/**
 * 🇨🇳 风口云平台 - 用户登出API
 * 
 * 功能说明：
 * - 处理用户登出请求
 * - 更新会话状态为已登出
 * - 清理服务器端会话记录
 */

import { NextRequest, NextResponse } from 'next/server'
import { SingleSessionService } from '@/lib/services/single-session.service'
import { decodeJWTPayload } from '@/lib/auth/jwt'

export async function POST(request: NextRequest) {
  try {
    console.log('📝 处理用户登出请求')

    // 从请求体中获取sessionId或从token中提取
    const body = await request.json().catch(() => ({}))
    let sessionId = body.sessionId

    // 如果没有提供sessionId，尝试从Authorization头中提取
    if (!sessionId) {
      const authHeader = request.headers.get('Authorization')
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7)
        try {
          const payload = decodeJWTPayload(token)
          sessionId = payload?.sessionId
        } catch (error) {
          console.error('❌ 解析token失败:', error)
        }
      }
    }

    if (sessionId) {
      console.log('📝 更新会话记录为登出状态:', sessionId)
      await SingleSessionService.logoutSession(sessionId)
      console.log('✅ 会话登出成功')
    } else {
      console.log('⚠️ 未找到sessionId，跳过会话更新')
    }

    return NextResponse.json({
      success: true,
      message: '登出成功'
    })

  } catch (error) {
    console.error('❌ 登出处理失败:', error)
    
    // 即使服务器端处理失败，也返回成功，不影响客户端登出
    return NextResponse.json({
      success: true,
      message: '登出成功',
      warning: '服务器端会话清理可能未完成'
    })
  }
}
