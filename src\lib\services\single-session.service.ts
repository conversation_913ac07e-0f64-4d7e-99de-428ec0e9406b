/**
 * 🇨🇳 风口云平台 - 智能会话管理服务
 *
 * 功能说明：
 * - 智能设备识别和会话管理
 * - 支持同设备多标签页共存
 * - 不同设备间的单点登录控制
 * - 提供灵活的会话策略配置
 */

import { v4 as uuidv4 } from 'uuid'
import { db } from '@/lib/database'

// 获取Prisma客户端实例
const getPrismaClient = async () => {
  const { PrismaClient } = await import('@prisma/client')
  const globalForPrisma = globalThis as unknown as {
    prisma: typeof PrismaClient.prototype | undefined
  }
  return globalForPrisma.prisma ?? new PrismaClient()
}

export interface SessionInfo {
  sessionId: string
  userId: string
  userType: 'admin' | 'factory_user'
  username: string
  ipAddress?: string
  userAgent?: string
  deviceInfo?: string
  deviceFingerprint?: string
}

export interface ActiveSessionCheck {
  hasActiveSession: boolean
  activeSessionId?: string
  activeSessionInfo?: {
    loginTime: Date
    ipAddress?: string
    deviceInfo?: string
    deviceFingerprint?: string
  }
  isSameDevice?: boolean
}

// 会话管理策略
export interface SessionPolicy {
  allowMultipleTabsOnSameDevice: boolean  // 允许同设备多标签页
  allowMultipleDevices: boolean           // 允许多设备登录
  maxConcurrentSessions: number           // 最大并发会话数
  sessionTimeoutMinutes: number           // 会话超时时间（分钟）
}

// 默认会话策略 - 修正：智能单设备多标签页模式
export const DEFAULT_SESSION_POLICY: SessionPolicy = {
  allowMultipleTabsOnSameDevice: true,    // 🔧 修正：允许同设备多标签页
  allowMultipleDevices: false,            // 不允许多设备登录
  maxConcurrentSessions: 5,               // 🔧 修正：同设备最多5个标签页
  sessionTimeoutMinutes: 480              // 8小时超时
}

/**
 * 智能会话管理服务
 */
export class SingleSessionService {

  /**
   * 生成新的会话ID
   */
  static generateSessionId(): string {
    return uuidv4()
  }

  /**
   * 生成设备指纹
   * 基于IP地址和User-Agent生成设备唯一标识
   */
  static generateDeviceFingerprint(ipAddress: string, userAgent: string): string {
    // 简化的设备指纹：IP + 浏览器类型 + 操作系统
    const browserInfo = this.extractBrowserInfo(userAgent)
    const osInfo = this.extractOSInfo(userAgent)

    // 对于本地开发环境，使用更宽松的指纹策略
    if (ipAddress === '127.0.0.1' || ipAddress === '::1' || ipAddress.includes('localhost')) {
      return `local-${browserInfo}-${osInfo}`
    }

    return `${ipAddress}-${browserInfo}-${osInfo}`
  }

  /**
   * 提取浏览器信息
   */
  private static extractBrowserInfo(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'chrome'
    if (userAgent.includes('Firefox')) return 'firefox'
    if (userAgent.includes('Safari')) return 'safari'
    if (userAgent.includes('Edge')) return 'edge'
    return 'unknown'
  }

  /**
   * 提取操作系统信息
   */
  private static extractOSInfo(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'windows'
    if (userAgent.includes('Mac')) return 'mac'
    if (userAgent.includes('Linux')) return 'linux'
    if (userAgent.includes('Android')) return 'android'
    if (userAgent.includes('iOS')) return 'ios'
    return 'unknown'
  }

  /**
   * 检查两个设备指纹是否来自同一设备
   */
  static isSameDevice(fingerprint1: string, fingerprint2: string): boolean {
    // 直接相等的情况
    if (fingerprint1 === fingerprint2) {
      console.log('🔍 设备指纹完全匹配:', { fingerprint1, fingerprint2 })
      return true
    }

    // 对于本地开发环境，认为所有local-开头的指纹都是同一设备
    if (fingerprint1.startsWith('local-') && fingerprint2.startsWith('local-')) {
      console.log('🔍 本地开发环境，认为是同一设备:', { fingerprint1, fingerprint2 })
      return true
    }

    // 🔧 新增：兼容旧格式的设备指纹识别
    // 处理新格式 (local-chrome-windows) 与旧格式 (Windows PC) 的兼容性
    const isCompatible = this.isCompatibleDeviceFingerprint(fingerprint1, fingerprint2)
    if (isCompatible) {
      console.log('🔍 兼容格式设备指纹匹配:', { fingerprint1, fingerprint2 })
      return true
    }

    console.log('🔍 设备指纹不匹配:', { fingerprint1, fingerprint2 })
    return false
  }

  /**
   * 检查两个设备指纹是否兼容（处理新旧格式兼容性）
   */
  private static isCompatibleDeviceFingerprint(fingerprint1: string, fingerprint2: string): boolean {
    // 定义设备类型映射
    const deviceTypeMap = {
      'Windows PC': ['local-chrome-windows', 'local-firefox-windows', 'local-edge-windows', 'local-safari-windows'],
      'Mac': ['local-chrome-mac', 'local-firefox-mac', 'local-safari-mac'],
      'Mac Computer': ['local-chrome-mac', 'local-firefox-mac', 'local-safari-mac'],
      'Linux': ['local-chrome-linux', 'local-firefox-linux'],
      'Linux Computer': ['local-chrome-linux', 'local-firefox-linux'],
      'Mobile Device': ['local-chrome-android', 'local-safari-ios'],
      'Tablet': ['local-chrome-android', 'local-safari-ios'],
      'Desktop': ['local-chrome-windows', 'local-firefox-windows', 'local-edge-windows']
    }

    // 检查是否为兼容的设备类型
    for (const [oldFormat, newFormats] of Object.entries(deviceTypeMap)) {
      if ((fingerprint1 === oldFormat && newFormats.some(nf => fingerprint2.includes(nf.split('-')[1]) && fingerprint2.includes(nf.split('-')[2]))) ||
          (fingerprint2 === oldFormat && newFormats.some(nf => fingerprint1.includes(nf.split('-')[1]) && fingerprint1.includes(nf.split('-')[2])))) {
        return true
      }
    }

    // 特殊处理：本地开发环境的Windows设备
    if ((fingerprint1 === 'Windows PC' && fingerprint2.startsWith('local-') && fingerprint2.includes('windows')) ||
        (fingerprint2 === 'Windows PC' && fingerprint1.startsWith('local-') && fingerprint1.includes('windows'))) {
      return true
    }

    return false
  }

  /**
   * 智能检查用户活跃会话
   * 支持设备识别和会话策略
   */
  static async checkActiveSession(
    userId: string,
    userType: 'admin' | 'factory_user',
    currentDeviceFingerprint?: string,
    policy: SessionPolicy = DEFAULT_SESSION_POLICY
  ): Promise<{
    hasActiveSession: boolean
    isSameDevice: boolean
    hasMultipleDevices: boolean
    activeSessionInfo?: any
    sameDeviceSessionCount: number
    otherDeviceSessionCount: number
  }> {
    try {
      console.log('🔍 智能检查用户活跃会话:', { userId, userType, currentDeviceFingerprint })

      // 获取Prisma客户端
      const prisma = await getPrismaClient()

      // 查找用户的所有活跃会话
      const activeSessions = await prisma.loginRecord.findMany({
        where: {
          userId,
          userType,
          loginStatus: 'success',
          isActive: true,
          sessionStatus: 'ACTIVE'
        },
        orderBy: {
          loginTime: 'desc'
        }
      })

      if (activeSessions.length === 0) {
        console.log('✅ 没有活跃会话')
        return {
          hasActiveSession: false,
          isSameDevice: false,
          hasMultipleDevices: false,
          sameDeviceSessionCount: 0,
          otherDeviceSessionCount: 0
        }
      }

      console.log(`📊 发现 ${activeSessions.length} 个活跃会话`)

      // 🔧 修正：智能分析会话设备分布
      let sameDeviceSessionCount = 0
      let otherDeviceSessionCount = 0
      let sameDeviceSessions: any[] = []
      let otherDeviceSessions: any[] = []

      if (currentDeviceFingerprint) {
        console.log('🔍 分析会话设备分布，当前设备指纹:', currentDeviceFingerprint)

        activeSessions.forEach(session => {
          const isSame = session.deviceInfo && this.isSameDevice(session.deviceInfo, currentDeviceFingerprint)
          console.log('🔍 会话设备比较:', {
            sessionId: session.sessionId,
            sessionDevice: session.deviceInfo,
            currentDevice: currentDeviceFingerprint,
            isSame
          })

          if (isSame) {
            sameDeviceSessionCount++
            sameDeviceSessions.push(session)
          } else {
            otherDeviceSessionCount++
            otherDeviceSessions.push(session)
          }
        })

        console.log('📊 会话分布统计:', {
          同设备会话: sameDeviceSessionCount,
          其他设备会话: otherDeviceSessionCount,
          总会话数: activeSessions.length
        })

        // 判断是否有多设备登录
        const hasMultipleDevices = otherDeviceSessionCount > 0
        const isSameDevice = sameDeviceSessionCount > 0

        return {
          hasActiveSession: true,
          isSameDevice,
          hasMultipleDevices,
          activeSessionInfo: activeSessions[0] ? {
            loginTime: activeSessions[0].loginTime,
            ipAddress: activeSessions[0].ipAddress || undefined,
            deviceInfo: activeSessions[0].deviceInfo || undefined,
            deviceFingerprint: currentDeviceFingerprint
          } : undefined,
          sameDeviceSessionCount,
          otherDeviceSessionCount
        }
      } else {
        console.log('⚠️ 没有提供设备指纹，无法进行设备识别')

        // 没有设备指纹时，按传统方式处理
        return {
          hasActiveSession: true,
          isSameDevice: false,
          hasMultipleDevices: true,
          activeSessionInfo: activeSessions[0] ? {
            loginTime: activeSessions[0].loginTime,
            ipAddress: activeSessions[0].ipAddress || undefined,
            deviceInfo: activeSessions[0].deviceInfo || undefined
          } : undefined,
          sameDeviceSessionCount: 0,
          otherDeviceSessionCount: activeSessions.length
        }
      }

    } catch (error) {
      console.error('❌ 检查活跃会话失败:', error)
      return {
        hasActiveSession: false,
        isSameDevice: false,
        hasMultipleDevices: false,
        sameDeviceSessionCount: 0,
        otherDeviceSessionCount: 0
      }
    }
  }

  /**
   * 🔧 新增：只踢出其他设备的会话，保留同设备会话
   */
  static async invalidateOtherDeviceSessions(
    userId: string,
    userType: 'admin' | 'factory_user',
    currentDeviceFingerprint: string,
    newSessionId: string,
    reason: string = '新设备登录'
  ): Promise<void> {
    try {
      console.log('🔄 踢出其他设备会话:', { userId, userType, currentDeviceFingerprint, newSessionId })

      // 获取Prisma客户端
      const prisma = await getPrismaClient()
      const now = new Date()

      // 查找其他设备的活跃会话
      const otherDeviceSessions = await prisma.loginRecord.findMany({
        where: {
          userId,
          userType,
          loginStatus: 'success',
          isActive: true,
          sessionStatus: 'ACTIVE',
          NOT: {
            deviceInfo: currentDeviceFingerprint
          }
        }
      })

      if (otherDeviceSessions.length === 0) {
        console.log('✅ 没有其他设备的活跃会话需要踢出')
        return
      }

      console.log(`🔄 准备踢出 ${otherDeviceSessions.length} 个其他设备的会话`)

      // 使其他设备的会话失效
      const updateResult = await prisma.loginRecord.updateMany({
        where: {
          userId,
          userType,
          loginStatus: 'success',
          isActive: true,
          sessionStatus: 'ACTIVE',
          NOT: {
            deviceInfo: currentDeviceFingerprint
          }
        },
        data: {
          isActive: false,
          sessionStatus: 'INVALIDATED',
          logoutTime: now,
          invalidateReason: reason
        }
      })

      console.log(`✅ 已踢出 ${updateResult.count} 个其他设备的会话`)

    } catch (error) {
      console.error('❌ 踢出其他设备会话失败:', error)
      throw error
    }
  }

  /**
   * 使旧会话失效
   */
  static async invalidateOldSessions(
    userId: string,
    userType: 'admin' | 'factory_user',
    newSessionId: string,
    reason: string = '新设备登录'
  ): Promise<void> {
    try {
      console.log('🔄 使旧会话失效:', { userId, userType, newSessionId })

      // 获取Prisma客户端
      const prisma = await getPrismaClient()
      const now = new Date()

      // 更新所有旧的活跃会话为失效状态
      const result = await prisma.loginRecord.updateMany({
        where: {
          userId,
          userType,
          isActive: true,
          sessionStatus: 'ACTIVE',
          sessionId: {
            not: newSessionId // 排除当前新会话
          }
        },
        data: {
          isActive: false,
          sessionStatus: 'INVALIDATED',
          invalidatedBy: newSessionId,
          invalidatedAt: now,
          invalidateReason: reason,
          logoutTime: now
        }
      })

      console.log(`✅ 已使 ${result.count} 个旧会话失效`)

    } catch (error) {
      console.error('❌ 使旧会话失效失败:', error)
      throw error
    }
  }

  /**
   * 创建新会话记录
   */
  static async createSessionRecord(sessionInfo: SessionInfo): Promise<string> {
    try {
      console.log('📝 创建新会话记录:', sessionInfo.sessionId)

      // 先检查并使旧会话失效
      await this.invalidateOldSessions(
        sessionInfo.userId,
        sessionInfo.userType,
        sessionInfo.sessionId,
        '新设备登录'
      )

      // 创建新的会话记录
      const loginRecord = await db.recordLogin({
        userId: sessionInfo.userId,
        userType: sessionInfo.userType,
        username: sessionInfo.username,
        userName: sessionInfo.username, // 临时使用username，后续可以传入真实姓名
        loginStatus: 'success',
        sessionId: sessionInfo.sessionId,  // 🔧 确保传入正确的sessionId
        ipAddress: sessionInfo.ipAddress,
        userAgent: sessionInfo.userAgent,
        deviceInfo: sessionInfo.deviceInfo
      })

      // 🔧 新增：确保会话记录状态正确
      if (loginRecord?.id) {
        const prisma = await getPrismaClient()
        await prisma.loginRecord.update({
          where: { id: loginRecord.id },
          data: {
            isActive: true,
            sessionStatus: 'ACTIVE'
          }
        })
        console.log('✅ 会话状态已更新为活跃')
      }

      console.log('✅ 会话记录创建成功:', loginRecord?.id)
      return sessionInfo.sessionId

    } catch (error) {
      console.error('❌ 创建会话记录失败:', error)
      throw error
    }
  }

  /**
   * 验证会话是否有效
   */
  static async validateSession(sessionId: string): Promise<boolean> {
    try {
      if (!sessionId) {
        console.log('❌ 会话验证失败: sessionId为空')
        return false
      }

      // 获取Prisma客户端
      const prisma = await getPrismaClient()

      const session = await prisma.loginRecord.findFirst({
        where: {
          sessionId,
          isActive: true,
          sessionStatus: 'ACTIVE'
        }
      })

      if (!session) {
        console.log('❌ 会话验证失败: 未找到活跃会话', sessionId)
        return false
      }

      console.log('✅ 会话验证成功:', sessionId)
      return true

    } catch (error) {
      console.error('❌ 验证会话失败:', error)
      return false
    }
  }

  /**
   * 主动登出会话
   */
  static async logoutSession(sessionId: string): Promise<void> {
    try {
      console.log('🚪 主动登出会话:', sessionId)

      // 获取Prisma客户端
      const prisma = await getPrismaClient()
      const now = new Date()

      await prisma.loginRecord.updateMany({
        where: {
          sessionId,
          isActive: true
        },
        data: {
          isActive: false,
          sessionStatus: 'LOGOUT',
          logoutTime: now
        }
      })

      console.log('✅ 会话登出成功')

    } catch (error) {
      console.error('❌ 会话登出失败:', error)
      throw error
    }
  }

  /**
   * 获取用户的所有会话历史
   */
  static async getUserSessions(userId: string, userType: 'admin' | 'factory_user', limit: number = 10) {
    try {
      // 获取Prisma客户端
      const prisma = await getPrismaClient()

      return await prisma.loginRecord.findMany({
        where: {
          userId,
          userType,
          loginStatus: 'success'
        },
        orderBy: {
          loginTime: 'desc'
        },
        take: limit
      })

    } catch (error) {
      console.error('❌ 获取用户会话历史失败:', error)
      return []
    }
  }

  /**
   * 清理过期会话（定期任务）
   */
  static async cleanupExpiredSessions(): Promise<void> {
    try {
      console.log('🧹 清理过期会话...')

      // 获取Prisma客户端
      const prisma = await getPrismaClient()
      const expiredTime = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30天前

      const result = await prisma.loginRecord.updateMany({
        where: {
          isActive: true,
          sessionStatus: 'ACTIVE',
          loginTime: {
            lt: expiredTime
          }
        },
        data: {
          isActive: false,
          sessionStatus: 'EXPIRED',
          logoutTime: new Date()
        }
      })

      console.log(`✅ 清理了 ${result.count} 个过期会话`)

    } catch (error) {
      console.error('❌ 清理过期会话失败:', error)
    }
  }

  /**
   * 获取用户的活跃会话列表
   */
  static async getActiveSessions(userId: string, userType: 'admin' | 'factory_user') {
    try {
      const prisma = await getPrismaClient()

      const sessions = await prisma.loginRecord.findMany({
        where: {
          userId,
          userType,
          isActive: true,
          sessionStatus: 'ACTIVE'
        },
        orderBy: {
          loginTime: 'desc'
        }
      })

      return sessions.map(session => ({
        sessionId: session.sessionId,
        loginTime: session.loginTime,
        ipAddress: session.ipAddress,
        deviceInfo: session.deviceInfo,
        deviceFingerprint: session.deviceFingerprint
      }))

    } catch (error) {
      console.error('❌ 获取活跃会话失败:', error)
      return []
    }
  }

  /**
   * 更新会话活跃时间
   */
  static async updateSessionActivity(sessionId: string) {
    try {
      const prisma = await getPrismaClient()

      const result = await prisma.loginRecord.updateMany({
        where: {
          sessionId,
          isActive: true,
          sessionStatus: 'ACTIVE'
        },
        data: {
          loginTime: new Date()  // 更新登录时间作为活跃时间
        }
      })

      console.log(`✅ 更新会话活跃时间: ${sessionId}, 影响记录数: ${result.count}`)
      return result.count > 0

    } catch (error) {
      console.error('❌ 更新会话活跃时间失败:', error)
      return false
    }
  }
}
