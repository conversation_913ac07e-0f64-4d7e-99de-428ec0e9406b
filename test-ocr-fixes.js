// 测试OCR识别修复效果
const testText = `江苏·淮安市安豪栅栏有限公司
一楼厨房出风口125x1435
回风口245x1120
客厅出风口130×2770
回风口150x800243x1750
次卧出风口150x800
回风口245x950
主卧出风口150x900
回风口250x.1020
二楼客厅出凤口175x1300
回风口295x1650
主卧出风口140x1500
回风口250x1500
次卧出风口140x800
同风口245x850`;

console.log('🧪 测试OCR识别修复效果');
console.log('📝 测试文本:');
console.log(testText);
console.log('\n🔍 预期修复效果:');
console.log('1. 连续尺寸"150x800243x1750"应识别为两个尺寸：150x800 和 243x1750');
console.log('2. 楼层应正确识别为"一楼"和"二楼"，而不是都归到"1楼"');
console.log('3. 备注"一楼厨房 厨房"应去重为"一楼厨房"');
console.log('4. 总共应识别14个尺寸，而不是13个');

// 分析文本中的尺寸
const dimensionMatches = testText.match(/\d+[xX×]\d+/g);
console.log('\n📊 文本中的尺寸匹配:');
dimensionMatches?.forEach((match, index) => {
  console.log(`  ${index + 1}. ${match}`);
});

console.log(`\n📈 总匹配数: ${dimensionMatches?.length || 0}`);

// 分析楼层信息
const floorMatches = testText.match(/(一楼|二楼|三楼|四楼|五楼|六楼|七楼|八楼|九楼|十楼)/g);
console.log('\n🏢 楼层信息匹配:');
floorMatches?.forEach((match, index) => {
  console.log(`  ${index + 1}. ${match}`);
});

// 分析连续尺寸
const continuousMatch = testText.match(/(\d+)\s*[xX×]\s*(\d+)(\d{3,4})\s*[xX×]\s*(\d+)/);
if (continuousMatch) {
  console.log('\n🔧 连续尺寸检测:');
  console.log(`  原始: ${continuousMatch[0]}`);
  console.log(`  第一个尺寸: ${continuousMatch[1]}x${continuousMatch[2]}`);
  console.log(`  第二个尺寸: ${continuousMatch[3]}x${continuousMatch[4]}`);
}
