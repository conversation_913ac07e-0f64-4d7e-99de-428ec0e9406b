/**
 * 🇨🇳 风口云平台 - 会话状态通知组件
 */

'use client'

import React, { useEffect, useState } from 'react'
import { AlertTriangle, X } from 'lucide-react'

interface SessionNotificationProps {
  message: string
  type: 'warning' | 'error' | 'info'
  onClose?: () => void
  autoClose?: boolean
  duration?: number
}

export function SessionNotification({
  message,
  type = 'warning',
  onClose,
  autoClose = true,
  duration = 5000
}: SessionNotificationProps) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (autoClose) {
      const timer = setTimeout(() => {
        setIsVisible(false)
        onClose?.()
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [autoClose, duration, onClose])

  if (!isVisible) return null

  const getStyles = () => {
    switch (type) {
      case 'error':
        return {
          bg: 'bg-red-500',
          border: 'border-red-600',
          text: 'text-white'
        }
      case 'warning':
        return {
          bg: 'bg-yellow-500',
          border: 'border-yellow-600',
          text: 'text-white'
        }
      case 'info':
        return {
          bg: 'bg-blue-500',
          border: 'border-blue-600',
          text: 'text-white'
        }
      default:
        return {
          bg: 'bg-gray-500',
          border: 'border-gray-600',
          text: 'text-white'
        }
    }
  }

  const styles = getStyles()

  const handleClose = () => {
    setIsVisible(false)
    onClose?.()
  }

  return (
    <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-right duration-300">
      <div className={`
        ${styles.bg} ${styles.border} ${styles.text}
        border rounded-lg shadow-lg p-4 max-w-md
        flex items-start gap-3
      `}>
        <AlertTriangle className="w-5 h-5 mt-0.5 flex-shrink-0" />
        
        <div className="flex-1">
          <div className="font-semibold text-sm mb-1">
            {type === 'error' ? '会话已失效' : '登录提醒'}
          </div>
          <div className="text-sm opacity-90">
            {message}
          </div>
        </div>

        <button
          onClick={handleClose}
          className="flex-shrink-0 p-1 hover:bg-white/20 dark:hover:bg-black/20 rounded transition-colors"
          aria-label="关闭通知"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

/**
 * 全局会话通知管理器
 */
export class SessionNotificationManager {
  private static container: HTMLDivElement | null = null

  static show(message: string, type: 'warning' | 'error' | 'info' = 'warning') {
    if (typeof window === 'undefined') return

    // 创建容器
    if (!this.container) {
      this.container = document.createElement('div')
      this.container.id = 'session-notifications'
      document.body.appendChild(this.container)
    }

    // 清除现有通知
    this.container.innerHTML = ''

    // 创建通知元素
    const notification = document.createElement('div')
    notification.className = 'fixed top-4 right-4 z-50'
    
    const getStyles = () => {
      switch (type) {
        case 'error':
          return 'bg-red-500 border-red-600'
        case 'warning':
          return 'bg-yellow-500 border-yellow-600'
        case 'info':
          return 'bg-blue-500 border-blue-600'
        default:
          return 'bg-gray-500 border-gray-600'
      }
    }

    notification.innerHTML = `
      <div class="${getStyles()}border rounded-lg shadow-lg p-4 max-w-md text-white">
        <div class="flex items-start gap-3">
          <svg class="w-5 h-5 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <div class="flex-1">
            <div class="font-semibold text-sm mb-1">
              ${type === 'error' ? '会话已失效' : '登录提醒'}
            </div>
            <div class="text-sm opacity-90">
              ${message}
            </div>
          </div>
          <button onclick="this.parentElement.parentElement.parentElement.remove()" class="flex-shrink-0 p-1 hover:bg-white/20 rounded transition-colors">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    `

    this.container.appendChild(notification)

    // 自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 5000)
  }

  static clear() {
    if (this.container) {
      this.container.innerHTML = ''
    }
  }
}
