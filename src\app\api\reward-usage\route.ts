/**
 * 🎁 推荐奖励使用API
 *
 * 处理奖励使用请求，包括条件验证、记录创建和奖励清零
 */

import { NextRequest, NextResponse } from 'next/server'
import { db, prisma } from '@/lib/database'
import { validateRewardUsage } from '@/lib/utils/reward-usage-validator'
import { safeNumber } from '@/lib/utils/number-utils'
import { withAdminOrFactoryAuth } from '@/lib/middleware/auth'
import { validateSession } from '@/lib/middleware/session'

// 混合认证函数 - 支持JWT令牌和会话认证
async function getAuthenticatedUser(request: NextRequest) {
  // 首先尝试JWT令牌认证
  try {
    const authHeader = request.headers.get('authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      // 使用JWT认证
      const { getUserFromRequest } = await import('@/lib/auth/jwt')
      const authResult = getUserFromRequest(request)
      if (authResult.success && authResult.user) {
        return {
          success: true,
          user: {
            id: authResult.user.userId,
            userType: authResult.user.userType,
            factoryId: authResult.user.factoryId,
            username: authResult.user.username,
            name: authResult.user.name
          }
        }
      }
    }
  } catch (error) {
    console.log('JWT认证失败，尝试会话认证')
  }

  // 如果JWT认证失败，尝试会话认证
  try {
    const sessionResult = await validateSession(request)
    if (sessionResult.isValid && sessionResult.user) {
      return {
        success: true,
        user: {
          id: sessionResult.user.userId,
          userType: sessionResult.user.userType,
          factoryId: sessionResult.user.factoryId,
          username: sessionResult.user.username,
          name: sessionResult.user.name
        }
      }
    }
  } catch (error) {
    console.log('会话认证失败')
  }

  return {
    success: false,
    error: '认证失败'
  }
}

// 创建奖励使用记录
export async function POST(request: NextRequest) {
  try {
    // 认证检查
    const authResult = await getAuthenticatedUser(request)
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: '认证失败，请重新登录'
      }, { status: 401 })
    }

    const user = authResult.user

    const body = await request.json()
    const { clientId, usageType, amount, description, relatedOrderId, paymentMethod, notes, factoryId: requestFactoryId } = body

    console.log('🎁 收到奖励使用请求:', {
      clientId,
      usageType,
      amount,
      description,
      relatedOrderId,
      requestFactoryId,
      userType: user?.userType,
      userFactoryId: user?.factoryId
    })

    // 确定工厂ID
    let factoryId = requestFactoryId
    if (!factoryId) {
      // 如果请求中没有工厂ID，使用用户的工厂ID
      factoryId = user?.factoryId
    }

    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '无法获取工厂信息'
      }, { status: 400 })
    }

    // 验证工厂访问权限
    if (user?.userType !== 'admin' && user?.factoryId !== factoryId) {
      return NextResponse.json({
        success: false,
        error: '无权访问该工厂数据'
      }, { status: 403 })
    }

    const currentUserId = user?.id

    // 验证必填参数
    if (!clientId || !usageType || !amount) {
      return NextResponse.json({
        success: false,
        error: '缺少必填参数：客户ID、使用方式或使用金额'
      }, { status: 400 })
    }

    // 验证使用金额
    const usageAmount = parseFloat(amount)
    if (isNaN(usageAmount) || usageAmount <= 0) {
      return NextResponse.json({
        success: false,
        error: '使用金额必须大于0'
      }, { status: 400 })
    }

    // 验证使用方式
    const validUsageTypes = ['CASH_OUT', 'ORDER_DISCOUNT', 'TRANSFER', 'OTHER']
    if (!validUsageTypes.includes(usageType)) {
      return NextResponse.json({
        success: false,
        error: '无效的使用方式'
      }, { status: 400 })
    }

    // 验证客户是否存在且属于当前工厂
    const client = await db.getClientById(clientId)
    if (!client) {
      return NextResponse.json({
        success: false,
        error: '客户不存在'
      }, { status: 404 })
    }

    if (client.factoryId !== factoryId) {
      return NextResponse.json({
        success: false,
        error: '客户不属于当前工厂'
      }, { status: 403 })
    }

    // 验证奖励使用条件
    const validationResult = await validateRewardUsage(clientId, usageAmount)
    if (!validationResult.canUse) {
      return NextResponse.json({
        success: false,
        error: validationResult.reason,
        validationDetails: validationResult.details
      }, { status: 400 })
    }

    // 如果是订单抵扣，验证订单是否存在
    if (usageType === 'ORDER_DISCOUNT' && relatedOrderId) {
      const order = await db.getOrderById(relatedOrderId)
      if (!order) {
        return NextResponse.json({
          success: false,
          error: '关联订单不存在'
        }, { status: 404 })
      }

      if (order.factoryId !== factoryId) {
        return NextResponse.json({
          success: false,
          error: '订单不属于当前工厂'
        }, { status: 403 })
      }
    }

    // 创建奖励使用记录
    const rewardUsage = await prisma.rewardUsage.create({
      data: {
        clientId,
        factoryId,
        usageType,
        amount: usageAmount,
        description: description || null,
        relatedOrderId: relatedOrderId || null,
        paymentMethod: paymentMethod || null,
        notes: notes || null,
        status: 'approved', // 直接批准，也可以设置为pending需要审批
        approvedBy: currentUserId,
        approvedAt: new Date(),
        createdBy: currentUserId
      }
    })

    // 更新客户的奖励余额
    const currentAvailableReward = safeNumber(client.availableReward)
    const currentUsedReward = safeNumber(client.usedReward)

    const newAvailableReward = currentAvailableReward - usageAmount
    const newUsedReward = currentUsedReward + usageAmount

    await db.updateClient(clientId, {
      availableReward: Math.max(0, newAvailableReward),
      usedReward: newUsedReward
    })

    console.log('✅ 奖励使用记录创建成功:', {
      rewardUsageId: rewardUsage.id,
      clientName: client.name,
      usageAmount,
      newAvailableReward: Math.max(0, newAvailableReward),
      newUsedReward
    })

    // 如果是订单抵扣，可以在这里更新订单的付款信息
    if (usageType === 'ORDER_DISCOUNT' && relatedOrderId) {
      // 这里可以添加订单付款更新逻辑
      console.log('💰 订单抵扣处理:', { orderId: relatedOrderId, discountAmount: usageAmount })
    }

    // 🆕 获取更新后的实时奖励状态
    const { calculateProportionalReward } = await import('@/lib/utils/reward-calculator')
    const updatedRewardStatus = await calculateProportionalReward(clientId, factoryId)

    console.log('🔄 奖励使用后的实时状态:', {
      totalReward: updatedRewardStatus.totalReward,
      availableReward: updatedRewardStatus.availableReward,
      usedReward: updatedRewardStatus.usedReward,
      pendingReward: updatedRewardStatus.pendingReward
    })

    return NextResponse.json({
      success: true,
      message: '奖励使用成功',
      data: {
        rewardUsage: {
          id: rewardUsage.id,
          usageType: rewardUsage.usageType,
          amount: usageAmount,
          status: rewardUsage.status,
          createdAt: rewardUsage.createdAt
        },
        updatedBalance: {
          availableReward: Math.max(0, newAvailableReward),
          usedReward: newUsedReward
        },
        // 🆕 返回实时计算的完整奖励状态
        realtimeRewardStatus: updatedRewardStatus
      }
    })

  } catch (error) {
    console.error('❌ 创建奖励使用记录失败:', error)
    return NextResponse.json({
      success: false,
      error: '创建奖励使用记录失败，请重试'
    }, { status: 500 })
  }
}

// 获取奖励使用记录列表
export async function GET(request: NextRequest) {
  try {
    console.log('🎁 开始获取奖励使用记录')

    // 认证检查
    const authResult = await getAuthenticatedUser(request)
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: '认证失败，请重新登录'
      }, { status: 401 })
    }

    const user = authResult.user

    const { searchParams } = new URL(request.url)
    const clientId = searchParams.get('clientId')
    const factoryIdParam = searchParams.get('factoryId')

    console.log('📋 请求参数:', {
      clientId,
      factoryIdParam,
      userType: user?.userType,
      userFactoryId: user?.factoryId
    })

    // 确定工厂ID
    let factoryId = factoryIdParam
    if (!factoryId) {
      // 如果请求中没有工厂ID，使用用户的工厂ID
      factoryId = user?.factoryId
      console.log('🏭 使用用户工厂ID:', factoryId)
    }

    if (!factoryId) {
      console.error('❌ 无法获取工厂信息')
      return NextResponse.json({
        success: false,
        error: '无法获取工厂信息'
      }, { status: 400 })
    }

    // 验证工厂访问权限
    if (user?.userType !== 'admin' && user?.factoryId !== factoryId) {
      return NextResponse.json({
        success: false,
        error: '无权访问该工厂数据'
      }, { status: 403 })
    }

    // 构建查询条件
    const whereCondition: unknown = {
      factoryId
    }

    if (clientId) {
      whereCondition.clientId = clientId
    }

    console.log('🔍 查询条件:', whereCondition)

    // 测试数据库连接
    try {
      await prisma.$connect()
      console.log('✅ 数据库连接成功')
    } catch (dbError) {
      console.error('❌ 数据库连接失败:', dbError)
      throw new Error('数据库连接失败')
    }

    // 获取奖励使用记录
    console.log('📋 开始查询奖励使用记录...')
    const rewardUsages = await prisma.rewardUsage.findMany({
      where: whereCondition,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true
          }
        },
        order: {
          select: {
            id: true,
            orderNumber: true,
            totalAmount: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log('📊 查询结果:', {
      count: rewardUsages.length,
      records: rewardUsages.map(r => ({ id: r.id, amount: r.amount, usageType: r.usageType }))
    })

    // 转换数据格式
    const formattedUsages = rewardUsages.map(usage => ({
      id: usage.id,
      clientId: usage.clientId,
      clientName: usage.client?.name,
      clientPhone: usage.client?.phone,
      usageType: usage.usageType,
      amount: Number(usage.amount),
      description: usage.description,
      relatedOrderId: usage.relatedOrderId,
      relatedOrderNumber: usage.order?.orderNumber,
      status: usage.status,
      paymentMethod: usage.paymentMethod,
      paymentReference: usage.paymentReference,
      paidAt: usage.paidAt,
      notes: usage.notes,
      approvedBy: usage.approvedBy,
      approvedAt: usage.approvedAt,
      createdBy: usage.createdBy,
      createdAt: usage.createdAt,
      updatedAt: usage.updatedAt
    }))

    return NextResponse.json({
      success: true,
      data: {
        rewardUsages: formattedUsages,
        total: formattedUsages.length
      }
    })

  } catch (error) {
    console.error('❌ 获取奖励使用记录失败:', error)

    // 提供更详细的错误信息用于调试
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    const errorDetails = {
      message: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined
    }

    console.error('错误详情:', errorDetails)

    return NextResponse.json({
      success: false,
      error: '获取奖励使用记录失败，请重试',
      details: process.env.NODE_ENV === 'development' ? errorDetails : undefined
    }, { status: 500 })
  }
}