/**
 * 🔧 管理员工具 - 修复订单付款状态
 * 
 * 修复数据库中所有订单的付款状态，确保状态与实际付款金额一致
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database/index'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 开始修复订单付款状态...')

    // 获取所有订单
    const allOrders = await db.getAllOrders()
    console.log(`📋 找到 ${allOrders.length} 个订单需要检查`)

    let fixedCount = 0
    let errorCount = 0

    for (const order of allOrders) {
      try {
        const totalAmount = Number(order.totalAmount || 0)
        const paidAmount = Number(order.paidAmount || 0)
        
        // 计算正确的付款状态
        let correctStatus = 'unpaid'
        if (paidAmount <= 0) {
          correctStatus = 'unpaid'
        } else if (Math.abs(paidAmount - totalAmount) < 0.01 || paidAmount >= totalAmount) {
          correctStatus = 'paid'
        } else {
          correctStatus = 'partial'
        }

        // 检查当前状态是否正确
        if (order.paymentStatus !== correctStatus) {
          console.log(`🔄 修复订单 ${order.orderNumber || order.id}:`, {
            订单总额: totalAmount,
            已付金额: paidAmount,
            当前状态: order.paymentStatus,
            正确状态: correctStatus
          })

          // 更新订单付款状态
          const success = await db.updateOrderPayment(order.id, {
            paymentStatus: correctStatus,
            paidAmount: paidAmount
          })

          if (success) {
            fixedCount++
            console.log(`✅ 订单 ${order.orderNumber || order.id} 状态已修复`)
          } else {
            errorCount++
            console.error(`❌ 订单 ${order.orderNumber || order.id} 状态修复失败`)
          }
        }
      } catch (error) {
        errorCount++
        console.error(`❌ 处理订单 ${order.id} 时出错:`, error)
      }
    }

    console.log('🔧 付款状态修复完成:', {
      总订单数: allOrders.length,
      修复数量: fixedCount,
      错误数量: errorCount
    })

    return NextResponse.json({
      success: true,
      message: '付款状态修复完成',
      data: {
        totalOrders: allOrders.length,
        fixedCount,
        errorCount
      }
    })

  } catch (error) {
    console.error('❌ 修复付款状态失败:', error)
    return NextResponse.json({
      success: false,
      error: `修复失败: ${error instanceof Error ? error.message : String(error)}`
    }, { status: 500 })
  }
}
