/**
 * BigInt序列化工具
 * 解决JSON.stringify无法序列化BigInt的问题
 */

/**
 * 将对象中的BigInt字段转换为number
 */
export function serializeBigInt<T extends Record<string, any>>(obj: T): T {
  if (!obj || typeof obj !== 'object') {
    return obj
  }

  const result = { ...obj }
  
  for (const [key, value] of Object.entries(result)) {
    if (typeof value === 'bigint') {
      result[key] = Number(value)
    } else if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[key] = serializeBigInt(value)
    } else if (Array.isArray(value)) {
      result[key] = value.map(item => 
        typeof item === 'object' ? serializeBigInt(item) : item
      )
    }
  }
  
  return result
}

/**
 * 专门处理工厂对象的BigInt序列化
 */
export function serializeFactory(factory: any) {
  if (!factory) return factory
  
  return {
    ...factory,
    totalSuspendedMs: factory.totalSuspendedMs ? Number(factory.totalSuspendedMs) : 0
  }
}

/**
 * 处理工厂列表的BigInt序列化
 */
export function serializeFactories(factories: any[]) {
  return factories.map(serializeFactory)
}
