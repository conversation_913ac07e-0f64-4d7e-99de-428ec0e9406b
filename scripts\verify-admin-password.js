/**
 * 🔍 风口云平台 - 管理员密码验证脚本
 * 
 * 功能说明：
 * - 验证管理员密码是否正确
 * - 检查密码哈希是否匹配
 * - 提供密码强度检查
 */

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')
const readline = require('readline')

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// 隐藏密码输入
function askPassword(question) {
  return new Promise((resolve) => {
    process.stdout.write(question)
    process.stdin.setRawMode(true)
    process.stdin.resume()
    process.stdin.setEncoding('utf8')
    
    let password = ''
    
    process.stdin.on('data', function(char) {
      char = char + ''
      
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false)
          process.stdin.pause()
          process.stdout.write('\n')
          resolve(password)
          break
        case '\u0003':
          process.exit()
          break
        case '\u007f': // backspace
          if (password.length > 0) {
            password = password.slice(0, -1)
            process.stdout.write('\b \b')
          }
          break
        default:
          password += char
          process.stdout.write('*')
          break
      }
    })
  })
}

// 密码强度检查
function checkPasswordStrength(password) {
  const checks = {
    length: password.length >= 8,
    hasLower: /[a-z]/.test(password),
    hasUpper: /[A-Z]/.test(password),
    hasNumber: /\d/.test(password),
    hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
  
  const score = Object.values(checks).filter(Boolean).length
  
  let strength = '弱'
  if (score >= 4) strength = '强'
  else if (score >= 3) strength = '中'
  
  return { checks, score, strength }
}

async function verifyAdminPassword() {
  const prisma = new PrismaClient()
  
  try {
    console.log('🔍 风口云平台 - 管理员密码验证工具')
    console.log('=' .repeat(50))
    
    // 检查环境
    const isProduction = process.env.NODE_ENV === 'production'
    console.log(`📍 当前环境: ${isProduction ? '生产环境' : '开发环境'}`)
    console.log('')
    
    // 获取所有管理员
    const admins = await prisma.admin.findMany({
      select: {
        id: true,
        username: true,
        name: true,
        passwordHash: true,
        isActive: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true
      }
    })
    
    if (admins.length === 0) {
      console.log('❌ 未找到管理员账户')
      return
    }
    
    console.log('📋 管理员账户列表:')
    admins.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.username} (${admin.name})`)
      console.log(`   状态: ${admin.isActive ? '✅ 激活' : '❌ 禁用'}`)
      console.log(`   创建时间: ${admin.createdAt.toLocaleString('zh-CN')}`)
      console.log(`   更新时间: ${admin.updatedAt.toLocaleString('zh-CN')}`)
      if (admin.lastLoginAt) {
        console.log(`   最后登录: ${admin.lastLoginAt.toLocaleString('zh-CN')}`)
      }
      console.log('')
    })
    
    // 选择管理员
    let selectedAdmin
    if (admins.length === 1) {
      selectedAdmin = admins[0]
      console.log(`🎯 自动选择管理员: ${selectedAdmin.username}`)
    } else {
      const choice = await new Promise((resolve) => {
        rl.question('请选择要验证的管理员 (输入序号): ', resolve)
      })
      
      const index = parseInt(choice) - 1
      if (index < 0 || index >= admins.length) {
        console.log('❌ 无效的选择')
        return
      }
      
      selectedAdmin = admins[index]
    }
    
    console.log('')
    console.log(`🔐 验证管理员 "${selectedAdmin.username}" 的密码`)
    console.log('')
    
    // 输入密码进行验证
    const inputPassword = await askPassword('请输入密码: ')
    console.log('')
    
    // 验证密码
    console.log('🔄 正在验证密码...')
    const isValid = await bcrypt.compare(inputPassword, selectedAdmin.passwordHash)
    
    if (isValid) {
      console.log('✅ 密码验证成功！')
      
      // 检查密码强度
      const strength = checkPasswordStrength(inputPassword)
      console.log('')
      console.log('🔒 密码强度分析:')
      console.log(`强度等级: ${strength.strength} (${strength.score}/5)`)
      console.log(`长度检查: ${strength.checks.length ? '✅' : '❌'} (至少8位)`)
      console.log(`小写字母: ${strength.checks.hasLower ? '✅' : '❌'}`)
      console.log(`大写字母: ${strength.checks.hasUpper ? '✅' : '❌'}`)
      console.log(`数字: ${strength.checks.hasNumber ? '✅' : '❌'}`)
      console.log(`特殊字符: ${strength.checks.hasSpecial ? '✅' : '❌'}`)
      
      if (strength.score < 3) {
        console.log('')
        console.log('⚠️  建议使用更强的密码，包含大小写字母、数字和特殊字符')
      }
      
    } else {
      console.log('❌ 密码验证失败！')
      console.log('')
      console.log('💡 可能的原因:')
      console.log('1. 密码输入错误')
      console.log('2. 密码已被修改')
      console.log('3. 账户状态异常')
    }
    
    // 显示密码哈希信息（仅开发环境）
    if (!isProduction) {
      console.log('')
      console.log('🔧 调试信息 (仅开发环境):')
      console.log(`密码哈希: ${selectedAdmin.passwordHash.substring(0, 20)}...`)
      console.log(`哈希长度: ${selectedAdmin.passwordHash.length}`)
      console.log(`哈希算法: ${selectedAdmin.passwordHash.startsWith('$2b$') ? 'bcrypt' : '未知'}`)
    }
    
  } catch (error) {
    console.error('❌ 验证过程出错:', error)
    
    if (error.code === 'P1001') {
      console.log('💡 提示: 数据库连接失败，请检查数据库配置')
    }
    
  } finally {
    await prisma.$disconnect()
    rl.close()
  }
}

// 运行脚本
if (require.main === module) {
  verifyAdminPassword()
    .then(() => {
      console.log('\n🎉 验证完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 验证失败:', error.message)
      process.exit(1)
    })
}

module.exports = { verifyAdminPassword }
