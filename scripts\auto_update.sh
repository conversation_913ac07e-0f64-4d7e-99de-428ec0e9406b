#!/bin/bash
# auto_update.sh - 风口云平台一键自动更新脚本
# 使用方法: ./auto_update.sh [--force] [--no-backup]

set -e  # 遇到错误立即退出

# 配置
BACKUP_RETENTION_DAYS=30
NOTIFICATION_WEBHOOK=""  # 可选：通知webhook
FORCE_UPDATE=false
SKIP_BACKUP=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --force)
      FORCE_UPDATE=true
      shift
      ;;
    --no-backup)
      SKIP_BACKUP=true
      shift
      ;;
    -h|--help)
      echo "使用方法: $0 [选项]"
      echo "选项:"
      echo "  --force      强制更新，跳过确认"
      echo "  --no-backup  跳过备份（不推荐）"
      echo "  -h, --help   显示帮助信息"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      exit 1
      ;;
  esac
done

echo "🚀 风口云平台自动更新开始..."
echo "时间: $(date)"
echo "================================"

# 函数：发送通知
send_notification() {
  local message="$1"
  local status="$2"  # success, warning, error
  
  echo "📢 $message"
  
  if [ -n "$NOTIFICATION_WEBHOOK" ]; then
    curl -X POST "$NOTIFICATION_WEBHOOK" \
      -H "Content-Type: application/json" \
      -d "{\"text\":\"[$status] 风口云平台: $message\"}" \
      2>/dev/null || true
  fi
}

# 函数：检查先决条件
check_prerequisites() {
  echo "🔍 检查更新先决条件..."
  
  # 检查是否在正确的目录
  if [ ! -f "package.json" ] || [ ! -f "docker-compose.yml" ]; then
    echo "❌ 请在项目根目录执行此脚本"
    exit 1
  fi
  
  # 检查磁盘空间（至少需要2GB）
  AVAILABLE_SPACE=$(df . | awk 'NR==2 {print $4}')
  if [ $AVAILABLE_SPACE -lt 2097152 ]; then
    send_notification "磁盘空间不足，无法执行更新" "error"
    exit 1
  fi
  
  # 检查Docker服务
  if ! docker-compose ps >/dev/null 2>&1; then
    echo "❌ Docker Compose服务未运行"
    exit 1
  fi
  
  # 检查Git状态
  if [ -n "$(git status --porcelain)" ]; then
    echo "⚠️ 工作目录有未提交的更改"
    if [ "$FORCE_UPDATE" = false ]; then
      read -p "是否继续？(y/N): " -n 1 -r
      echo
      if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
      fi
    fi
  fi
  
  echo "✅ 先决条件检查通过"
}

# 函数：创建备份
create_backup() {
  if [ "$SKIP_BACKUP" = true ]; then
    echo "⚠️ 跳过备份（--no-backup选项）"
    return 0
  fi
  
  echo "💾 创建系统备份..."
  
  BACKUP_DIR="/backups/auto_$(date +%Y%m%d_%H%M%S)"
  mkdir -p $BACKUP_DIR
  
  # 数据库备份
  echo "  📊 备份数据库..."
  if docker-compose exec -T postgres pg_dump -U factorysystem factorysystem > $BACKUP_DIR/database.sql; then
    echo "  ✅ 数据库备份完成"
  else
    echo "  ❌ 数据库备份失败"
    exit 1
  fi
  
  # 应用文件备份
  echo "  📁 备份应用文件..."
  tar -czf $BACKUP_DIR/app_files.tar.gz ./uploads ./logs 2>/dev/null || true
  
  # 配置文件备份
  echo "  ⚙️ 备份配置文件..."
  cp .env.production $BACKUP_DIR/ 2>/dev/null || cp .env.local $BACKUP_DIR/ 2>/dev/null || true
  cp docker-compose.yml $BACKUP_DIR/
  cp nginx.conf $BACKUP_DIR/ 2>/dev/null || true
  
  # 验证备份
  if [ -f "$BACKUP_DIR/database.sql" ] && [ -s "$BACKUP_DIR/database.sql" ]; then
    echo "✅ 备份创建成功: $BACKUP_DIR"
    echo $BACKUP_DIR > .last_backup
  else
    send_notification "备份创建失败" "error"
    exit 1
  fi
}

# 函数：检查更新
check_updates() {
  echo "🔍 检查代码更新..."
  
  git fetch origin
  LOCAL_COMMIT=$(git rev-parse HEAD)
  REMOTE_COMMIT=$(git rev-parse origin/main)
  
  if [ "$LOCAL_COMMIT" = "$REMOTE_COMMIT" ]; then
    echo "📋 没有新的更新"
    return 1
  fi
  
  echo "📋 发现新的更新:"
  git log --oneline --graph $LOCAL_COMMIT..$REMOTE_COMMIT
  
  if [ "$FORCE_UPDATE" = false ]; then
    echo ""
    read -p "是否继续更新？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      echo "更新已取消"
      exit 0
    fi
  fi
  
  return 0
}

# 函数：执行更新
perform_update() {
  echo "🔄 执行更新..."
  
  # 拉取最新代码
  echo "  📥 拉取最新代码..."
  git pull origin main
  
  # 检查依赖变化
  if git diff HEAD~1 HEAD --name-only | grep -q "package.json"; then
    echo "  📦 检测到依赖变化，更新依赖..."
    npm ci --production
  fi
  
  # 检查Prisma schema变化
  if git diff HEAD~1 HEAD --name-only | grep -q "prisma/schema.prisma"; then
    echo "  🔄 重新生成Prisma客户端..."
    docker-compose exec -T app npx prisma generate
  fi
  
  # 检查数据库迁移
  if [ -n "$(find prisma/migrations -name "*.sql" -newer .last_migration 2>/dev/null)" ]; then
    echo "  🗄️ 执行数据库迁移..."
    docker-compose exec -T app npx prisma migrate deploy
    touch .last_migration
  fi
  
  # 构建和部署
  echo "  🏗️ 构建新版本..."
  docker-compose build app
  
  echo "  🚀 部署新版本..."
  docker-compose up -d --no-deps app
  
  # 等待服务启动
  echo "  ⏳ 等待服务启动..."
  sleep 30
}

# 函数：验证更新
verify_update() {
  echo "✅ 验证更新..."
  
  # 健康检查
  local max_attempts=10
  local attempt=1
  
  while [ $attempt -le $max_attempts ]; do
    if curl -f -s http://localhost:3000/api/health > /dev/null; then
      echo "  ✅ 健康检查通过"
      break
    fi
    
    echo "  ⏳ 等待服务响应... ($attempt/$max_attempts)"
    sleep 10
    attempt=$((attempt + 1))
  done
  
  if [ $attempt -gt $max_attempts ]; then
    send_notification "更新后健康检查失败，可能需要回滚" "error"
    return 1
  fi
  
  # 检查服务状态
  echo "  🔍 检查服务状态..."
  if ! docker-compose ps | grep -q "Up"; then
    send_notification "服务状态异常" "error"
    return 1
  fi
  
  # 检查数据库连接
  echo "  🗄️ 检查数据库连接..."
  if ! docker-compose exec -T postgres psql -U factorysystem -c "SELECT 1;" > /dev/null 2>&1; then
    send_notification "数据库连接异常" "error"
    return 1
  fi
  
  echo "✅ 更新验证成功"
  return 0
}

# 函数：清理旧备份
cleanup_old_backups() {
  echo "🧹 清理旧备份..."
  if [ -d "/backups" ]; then
    find /backups -type d -mtime +$BACKUP_RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true
    echo "✅ 清理完成"
  fi
}

# 函数：显示更新摘要
show_summary() {
  echo ""
  echo "📊 更新摘要"
  echo "================================"
  echo "更新时间: $(date)"
  echo "当前版本: $(git rev-parse --short HEAD)"
  echo "服务状态:"
  docker-compose ps
  echo ""
  echo "🎉 更新完成！"
}

# 主函数
main() {
  local start_time=$(date)
  
  send_notification "开始自动更新流程" "info"
  
  # 执行更新流程
  check_prerequisites
  create_backup
  
  if check_updates; then
    perform_update
    
    if verify_update; then
      cleanup_old_backups
      show_summary
      send_notification "自动更新成功完成 (开始时间: $start_time)" "success"
    else
      send_notification "更新验证失败，请检查系统状态" "error"
      echo "❌ 更新验证失败"
      exit 1
    fi
  else
    send_notification "没有新的更新" "info"
    echo "📋 系统已是最新版本"
  fi
}

# 执行主函数
main "$@"
