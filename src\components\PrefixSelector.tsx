/**
 * 🎯 智能前缀选择器组件
 * 
 * 支持智能生成、自定义输入、冲突检测等功能
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { AlertCircle, CheckCircle, Sparkles, Settings, Copy } from 'lucide-react'
import { checkPrefixConflict } from '@/lib/utils/factory-settings'

interface PrefixSelectorProps {
  factoryId: string
  factoryName: string
  currentPrefix: string
  onPrefixChange: (prefix: string) => void
  minLength?: number
  maxLength?: number
  includeNumbers?: boolean
  preferLonger?: boolean
}

export default function PrefixSelector({
  factoryId,
  factoryName,
  currentPrefix,
  onPrefixChange,
  minLength = 3,
  maxLength = 8,
  includeNumbers = false,
  preferLonger = true
}: PrefixSelectorProps) {
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [customPrefix, setCustomPrefix] = useState(currentPrefix)
  const [conflictCheck, setConflictCheck] = useState<{ [key: string]: boolean }>({})
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('smart')

  // 生成智能建议
  const generateSuggestions = async () => {
    try {
      setLoading(true)
      const { suggestOrderPrefixes } = await import("@/lib/utils/factory-settings")
      const newSuggestions = suggestOrderPrefixes(factoryId, factoryName, {
        minLength,
        maxLength,
        includeNumbers,
        preferLonger
      })
      
      setSuggestions(newSuggestions)
      
      // 检查每个建议的冲突状态
      const conflicts: { [key: string]: boolean } = {}
      const { checkPrefixConflict } = await import("@/lib/utils/factory-settings")

      newSuggestions.forEach((prefix: string) => {
        conflicts[prefix] = checkPrefixConflict(prefix, factoryId)
      })
      
      setConflictCheck(conflicts)
    } catch (error) {
      console.error('❌ 生成建议失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 检查自定义前缀冲突
  const checkCustomPrefix = (prefix: string) => {
    if (!prefix) return false
    try {
      return checkPrefixConflict(prefix, factoryId)
    } catch (error) {
      console.error('❌ 检查前缀冲突失败:', error)
      return false
    }
  }

  // 验证前缀格式
  const validatePrefix = (prefix: string): { valid: boolean; message: string } => {
    if (!prefix) {
      return { valid: false, message: '前缀不能为空' }
    }
    
    if (prefix.length < minLength) {
      return { valid: false, message: `前缀长度不能少于${minLength}位` }
    }
    
    if (prefix.length > maxLength) {
      return { valid: false, message: `前缀长度不能超过${maxLength}位` }
    }
    
    if (!/^[A-Z]+$/.test(prefix)) {
      return { valid: false, message: '前缀只能包含大写字母' }
    }
    
    if (checkCustomPrefix(prefix)) {
      return { valid: false, message: '此前缀已被其他工厂使用' }
    }
    
    return { valid: true, message: '前缀可用' }
  }

  // 选择建议的前缀
  const selectSuggestion = (prefix: string) => {
    setCustomPrefix(prefix)
    onPrefixChange(prefix)
    setActiveTab('custom')
  }

  // 应用自定义前缀
  const applyCustomPrefix = () => {
    const validation = validatePrefix(customPrefix)
    if (validation.valid) {
      onPrefixChange(customPrefix)
      alert(`✅ 前缀设置成功: ${customPrefix}`)
    } else {
      alert(`❌ ${validation.message}`)
    }
  }

  // 复制前缀到剪贴板
  const copyToClipboard = async (prefix: string) => {
    try {
      const { copyToClipboard: copy } = await import('@/utils/clipboard')
      const success = await copy(prefix, `✅ 已复制前缀: ${prefix}`, '')
      if (!success) {
        // 备用方案：显示前缀供用户手动复制
        alert(`复制功能不可用，请手动复制前缀：${prefix}`)
      }
    } catch (error) {
      console.error('复制前缀失败:', error)
      // 备用方案：显示前缀供用户手动复制
      alert(`复制功能不可用，请手动复制前缀：${prefix}`)
    }
  }

  useEffect(() => {
    generateSuggestions()
  }, [factoryId, factoryName, minLength, maxLength, includeNumbers, preferLonger])

  useEffect(() => {
    setCustomPrefix(currentPrefix)
  }, [currentPrefix])

  const customValidation = validatePrefix(customPrefix)

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Sparkles className="h-5 w-5 mr-2 text-blue-600" />
          智能前缀选择器
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="smart">智能建议</TabsTrigger>
            <TabsTrigger value="custom">自定义</TabsTrigger>
          </TabsList>

          {/* 智能建议标签页 */}
          <TabsContent value="smart" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-600">
                <span>基于工厂名称</span>
                <Badge variant="outline" className="mx-1">{factoryName}</Badge>
                <span>的智能建议</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={generateSuggestions}
                disabled={loading}
              >
                {loading ? '生成中...' : '重新生成'}
              </Button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {suggestions.map((prefix, index) => {
                const hasConflict = conflictCheck[prefix]
                const isSelected = prefix === currentPrefix
                
                return (
                  <div
                    key={index}
                    className={`p-3 border rounded-lg cursor-pointer transition-all ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50' 
                        : hasConflict 
                        ? 'border-red-300 bg-red-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => !hasConflict && selectSuggestion(prefix)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-mono font-bold text-lg">{prefix}</span>
                      <div className="flex items-center space-x-1">
                        {hasConflict ? (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        ) : (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            copyToClipboard(prefix)
                          }}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {hasConflict ? '已被使用' : `${prefix.length}位 · 可用`}
                    </p>
                    {isSelected && (
                      <Badge variant="default" className="mt-2 text-xs">
                        当前使用
                      </Badge>
                    )}
                  </div>
                )
              })}
            </div>

            {suggestions.length === 0 && !loading && (
              <div className="text-center py-8 text-gray-500">
                <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>暂无可用建议，请尝试调整配置或使用自定义前缀</p>
              </div>
            )}
          </TabsContent>

          {/* 自定义标签页 */}
          <TabsContent value="custom" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="customPrefix">自定义前缀</Label>
              <div className="flex gap-2">
                <Input
                  id="customPrefix"
                  value={customPrefix}
                  onChange={(e) => setCustomPrefix(e.target.value.toUpperCase().replace(/[^A-Z]/g, ''))}
                  placeholder="输入自定义前缀"
                  maxLength={maxLength}
                  className={`font-mono ${
                    customValidation.valid ? 'border-green-500' : 'border-red-500'
                  }`}
                />
                <Button
                  onClick={applyCustomPrefix}
                  disabled={!customValidation.valid}
                  className="whitespace-nowrap"
                >
                  应用
                </Button>
              </div>
              
              <div className="flex items-center space-x-2">
                {customValidation.valid ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                )}
                <span className={`text-sm ${
                  customValidation.valid ? 'text-green-600' : 'text-red-600'
                }`}>
                  {customValidation.message}
                </span>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-sm mb-2">前缀要求</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 长度: {minLength}-{maxLength} 位</li>
                <li>• 格式: 仅大写字母</li>
                <li>• 唯一性: 不与其他工厂冲突</li>
                <li>• 建议: 使用工厂名称拼音首字母</li>
              </ul>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-sm mb-2">示例预览</h4>
              <p className="text-sm text-gray-600">
                订单号格式: <span className="font-mono font-bold">{customPrefix || 'PREFIX'}-{new Date().toISOString().slice(0, 10).replace(/-/g, '')}-01</span>
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
