/**
 * 🇨🇳 风口云平台 - 标签页级别会话管理器
 * 
 * 功能说明：
 * - 为每个浏览器标签页创建独立的用户会话
 * - 解决本地开发环境中用户会话混乱的问题
 * - 确保不同标签页的用户状态完全独立
 */

import type { AuthRole } from '@/lib/store/auth'

// 生成标签页唯一ID
function generateTabId(): string {
  if (typeof window === 'undefined') return 'server'
  
  // 检查是否已有标签页ID
  let tabId = sessionStorage.getItem('tab_session_id')
  if (!tabId) {
    tabId = `tab_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
    sessionStorage.setItem('tab_session_id', tabId)
  }
  
  return tabId
}

// 生成用户标签页会话键
function generateUserTabSessionKey(userId: string, role: AuthRole): string {
  const tabId = generateTabId()
  return `tab_user_session_${userId}_${role}_${tabId}`
}

// 标签页用户会话数据
interface TabUserSessionData {
  userId: string
  username: string
  role: AuthRole
  factoryId?: string
  accessToken: string
  refreshToken: string
  user: any
  loginTime: number
  lastActiveTime: number
  tabId: string
}

// 标签页会话管理器
export class TabSessionManager {
  private static instance: TabSessionManager
  private currentTabId: string
  private currentSessionKey: string | null = null

  static getInstance(): TabSessionManager {
    if (!TabSessionManager.instance) {
      TabSessionManager.instance = new TabSessionManager()
    }
    return TabSessionManager.instance
  }

  constructor() {
    this.currentTabId = generateTabId()
    this.initializeTabSession()
  }

  /**
   * 初始化标签页会话
   */
  private initializeTabSession(): void {
    if (typeof window === 'undefined') return

    try {
      // 设置标签页关闭时的清理
      window.addEventListener('beforeunload', () => {
        this.cleanupCurrentTabSession()
      })

      // 设置页面可见性变化时的处理
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          this.updateLastActiveTime()
        }
      })

      console.log('✅ 标签页会话管理器初始化完成:', this.currentTabId)
    } catch (error) {
      console.error('❌ 标签页会话管理器初始化失败:', error)
    }
  }

  /**
   * 创建用户标签页会话
   */
  createUserTabSession(
    user: any,
    role: AuthRole,
    tokens: { accessToken: string; refreshToken: string },
    factoryId?: string
  ): string {
    if (typeof window === 'undefined') return ''

    try {
      const sessionKey = generateUserTabSessionKey(user.id, role)
      const sessionData: TabUserSessionData = {
        userId: user.id,
        username: user.username || user.name,
        role,
        factoryId,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user,
        loginTime: Date.now(),
        lastActiveTime: Date.now(),
        tabId: this.currentTabId
      }

      // 保存到sessionStorage（标签页级别）
      sessionStorage.setItem(sessionKey, JSON.stringify(sessionData))
      
      // 同时保存到localStorage（持久化）
      localStorage.setItem(sessionKey, JSON.stringify(sessionData))
      
      // 设置当前会话
      this.currentSessionKey = sessionKey
      sessionStorage.setItem('current_tab_session_key', sessionKey)
      
      // 添加到标签页会话列表
      this.addToTabSessionList(sessionKey)
      
      console.log('✅ 标签页用户会话已创建:', {
        sessionKey,
        userId: user.id,
        username: sessionData.username,
        role,
        tabId: this.currentTabId
      })
      
      return sessionKey
    } catch (error) {
      console.error('❌ 创建标签页用户会话失败:', error)
      return ''
    }
  }

  /**
   * 获取当前标签页的用户会话
   */
  getCurrentTabSession(): TabUserSessionData | null {
    if (typeof window === 'undefined') return null

    try {
      // 优先从sessionStorage获取（标签页级别）
      const currentSessionKey = sessionStorage.getItem('current_tab_session_key')
      if (currentSessionKey) {
        const sessionData = sessionStorage.getItem(currentSessionKey)
        if (sessionData) {
          const session = JSON.parse(sessionData) as TabUserSessionData
          
          // 更新最后活跃时间
          session.lastActiveTime = Date.now()
          sessionStorage.setItem(currentSessionKey, JSON.stringify(session))
          
          return session
        }
      }

      // 如果sessionStorage没有，尝试从localStorage恢复
      const allKeys = Object.keys(localStorage)
      const tabSessionKeys = allKeys.filter(key => 
        key.startsWith('tab_user_session_') && key.includes(this.currentTabId)
      )

      if (tabSessionKeys.length > 0) {
        const sessionKey = tabSessionKeys[0] // 取第一个匹配的会话
        const sessionData = localStorage.getItem(sessionKey)
        if (sessionData) {
          const session = JSON.parse(sessionData) as TabUserSessionData
          
          // 恢复到sessionStorage
          sessionStorage.setItem(sessionKey, sessionData)
          sessionStorage.setItem('current_tab_session_key', sessionKey)
          this.currentSessionKey = sessionKey
          
          console.log('✅ 从localStorage恢复标签页会话:', session.username)
          return session
        }
      }

      return null
    } catch (error) {
      console.error('❌ 获取当前标签页会话失败:', error)
      return null
    }
  }

  /**
   * 删除当前标签页会话
   */
  removeCurrentTabSession(): void {
    if (typeof window === 'undefined') return

    try {
      const currentSessionKey = this.currentSessionKey || sessionStorage.getItem('current_tab_session_key')
      if (currentSessionKey) {
        // 从sessionStorage删除
        sessionStorage.removeItem(currentSessionKey)
        sessionStorage.removeItem('current_tab_session_key')
        
        // 从localStorage删除
        localStorage.removeItem(currentSessionKey)
        
        // 从会话列表中移除
        this.removeFromTabSessionList(currentSessionKey)
        
        this.currentSessionKey = null
        
        console.log('✅ 标签页用户会话已删除:', currentSessionKey)
      }
    } catch (error) {
      console.error('❌ 删除标签页用户会话失败:', error)
    }
  }

  /**
   * 获取所有标签页会话
   */
  getAllTabSessions(): TabUserSessionData[] {
    if (typeof window === 'undefined') return []

    try {
      const sessions: TabUserSessionData[] = []
      const allKeys = Object.keys(localStorage)
      const tabSessionKeys = allKeys.filter(key => key.startsWith('tab_user_session_'))

      for (const key of tabSessionKeys) {
        try {
          const sessionData = localStorage.getItem(key)
          if (sessionData) {
            sessions.push(JSON.parse(sessionData))
          }
        } catch (error) {
          console.warn('⚠️ 解析标签页会话数据失败:', key, error)
        }
      }

      return sessions
    } catch (error) {
      console.error('❌ 获取标签页会话列表失败:', error)
      return []
    }
  }

  /**
   * 清理当前标签页会话
   */
  private cleanupCurrentTabSession(): void {
    try {
      // 标签页关闭时，清理sessionStorage中的数据
      // localStorage中的数据保留，用于下次恢复
      const currentSessionKey = sessionStorage.getItem('current_tab_session_key')
      if (currentSessionKey) {
        sessionStorage.removeItem(currentSessionKey)
        sessionStorage.removeItem('current_tab_session_key')
        sessionStorage.removeItem('tab_session_id')
      }
      
      console.log('🧹 标签页会话已清理')
    } catch (error) {
      console.error('❌ 清理标签页会话失败:', error)
    }
  }

  /**
   * 更新最后活跃时间
   */
  private updateLastActiveTime(): void {
    try {
      const currentSessionKey = sessionStorage.getItem('current_tab_session_key')
      if (currentSessionKey) {
        const sessionData = sessionStorage.getItem(currentSessionKey)
        if (sessionData) {
          const session = JSON.parse(sessionData) as TabUserSessionData
          session.lastActiveTime = Date.now()
          sessionStorage.setItem(currentSessionKey, JSON.stringify(session))
          localStorage.setItem(currentSessionKey, JSON.stringify(session))
        }
      }
    } catch (error) {
      console.error('❌ 更新活跃时间失败:', error)
    }
  }

  /**
   * 添加到标签页会话列表
   */
  private addToTabSessionList(sessionKey: string): void {
    try {
      const sessionList = localStorage.getItem('tab_session_list')
      const sessionKeys = sessionList ? JSON.parse(sessionList) : []
      
      if (!sessionKeys.includes(sessionKey)) {
        sessionKeys.push(sessionKey)
        localStorage.setItem('tab_session_list', JSON.stringify(sessionKeys))
      }
    } catch (error) {
      console.error('❌ 添加到标签页会话列表失败:', error)
    }
  }

  /**
   * 从标签页会话列表中移除
   */
  private removeFromTabSessionList(sessionKey: string): void {
    try {
      const sessionList = localStorage.getItem('tab_session_list')
      if (sessionList) {
        const sessionKeys = JSON.parse(sessionList) as string[]
        const updatedKeys = sessionKeys.filter(key => key !== sessionKey)
        localStorage.setItem('tab_session_list', JSON.stringify(updatedKeys))
      }
    } catch (error) {
      console.error('❌ 从标签页会话列表移除失败:', error)
    }
  }
}

// 导出单例实例
export const tabSessionManager = TabSessionManager.getInstance()
