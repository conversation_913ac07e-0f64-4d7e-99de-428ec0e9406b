@import "tailwindcss";

/* CSS变量定义 - 浅色主题 */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
}

/* CSS变量定义 - 深色主题 */
.dark {
  --background: 222.2 84% 4.9%;        /* 深色背景 */
  --foreground: 210 40% 98%;           /* 亮色文字 */
  --card: 222.2 84% 4.9%;              /* 卡片背景 */
  --card-foreground: 210 40% 98%;      /* 卡片文字 */
  --popover: 222.2 84% 4.9%;           /* 弹出层背景 */
  --popover-foreground: 210 40% 98%;   /* 弹出层文字 */
  --primary: 210 40% 98%;              /* 主色调 */
  --primary-foreground: 222.2 84% 4.9%; /* 主色调文字 */
  --secondary: 217.2 32.6% 17.5%;      /* 次要背景 */
  --secondary-foreground: 210 40% 98%; /* 次要文字 */
  --muted: 217.2 32.6% 17.5%;          /* 静音背景 */
  --muted-foreground: 215 20.2% 65.1%; /* 静音文字 */
  --accent: 217.2 32.6% 17.5%;         /* 强调背景 */
  --accent-foreground: 210 40% 98%;    /* 强调文字 */
  --destructive: 0 62.8% 30.6%;        /* 危险色 */
  --destructive-foreground: 210 40% 98%; /* 危险色文字 */
  --border: 217.2 32.6% 17.5%;         /* 边框颜色 */
  --input: 217.2 32.6% 17.5%;          /* 输入框背景 */
  --ring: 212.7 26.8% 83.9%;           /* 焦点环 */
}

* {
  border-color: hsl(var(--border));
}

body {
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  transition: background-color 0.3s ease, color 0.3s ease;
  font-size: 16px;  /* 从14px增加到16px */
  line-height: 1.6;  /* 从1.5增加到1.6，提高行间距 */
}

/* 深色主题样式 - 使用CSS类而不是CSS变量避免水合错误 */
.dark {
  color-scheme: dark;
}

body.admin-dark {
  background-color: hsl(222.2 84% 4.9%) !important;  /* 深色背景 */
  color: hsl(210 40% 98%) !important;                 /* 亮色文字 */
}

body.admin-dark * {
  border-color: hsl(217.2 32.6% 17.5%);  /* 深色主题边框 */
}

/* 深色主题只在管理界面生效 - 通过body类名控制 */
body.admin-dark,
body.admin-dark html,
body.admin-dark div,
body.admin-dark main,
body.admin-dark section,
body.admin-dark article,
body.admin-dark aside,
body.admin-dark header,
body.admin-dark footer {
  background-color: hsl(222.2 84% 4.9%) !important;
  color: hsl(210 40% 98%) !important;
}

/* 管理界面深色主题的强制覆盖 */
body.admin-dark *:not(.bg-primary):not(.bg-secondary):not(.bg-destructive):not(.bg-green-500):not(.bg-blue-500):not(.bg-red-500):not(.bg-yellow-500) {
  background-color: hsl(222.2 84% 4.9%) !important;
  color: hsl(210 40% 98%) !important;
}

/* Portal容器主题同步 */
#portal-root.dark {
  color-scheme: dark;
}

/* 管理界面深色主题下强制覆盖所有白色和浅色背景 */
body.admin-dark .bg-white,
body.admin-dark .bg-gray-50,
body.admin-dark .bg-gray-100,
body.admin-dark .bg-gray-200,
body.admin-dark [style*="background-color: white"],
body.admin-dark [style*="background-color: #fff"],
body.admin-dark [style*="background-color: #ffffff"],
body.admin-dark [style*="background: white"],
body.admin-dark [style*="background: #fff"] {
  background-color: hsl(222.2 84% 4.9%) !important;
  color: hsl(210 40% 98%) !important;
}

/* 管理界面深色主题下的文字颜色适配 */
body.admin-dark .text-gray-900,
body.admin-dark .text-gray-800,
body.admin-dark .text-gray-700 {
  color: hsl(var(--foreground)) !important;
}

body.admin-dark .text-gray-600 {
  color: hsl(var(--muted-foreground)) !important;
}

body.admin-dark .text-gray-500 {
  color: hsl(0 0% 63.9%) !important;
}

body.admin-dark .text-gray-400 {
  color: hsl(0 0% 71%) !important;
}

/* 深色主题下的卡片和容器 */
.dark .bg-card,
.dark [data-radix-collection-item],
.dark [role="dialog"],
.dark [role="menu"],
.dark .card,
.dark .container,
.dark .content,
.dark .main,
.dark .wrapper {
  background-color: hsl(222.2 84% 4.9%) !important;
  color: hsl(210 40% 98%) !important;
}

/* 深色主题下的背景色适配 */
.dark .bg-gray-50 {
  background-color: hsl(var(--muted)) !important;
}

.dark .bg-gray-100 {
  background-color: hsl(var(--accent)) !important;
}

/* 深色主题下的状态色调整 */
.dark .bg-red-50 {
  background-color: hsl(0 62.8% 15%) !important;
}

.dark .bg-yellow-50 {
  background-color: hsl(47.9 95.8% 15%) !important;
}

.dark .bg-green-50 {
  background-color: hsl(142.1 76.2% 15%) !important;
}

.dark .border-red-200 {
  border-color: hsl(0 62.8% 25%) !important;
}

.dark .border-yellow-200 {
  border-color: hsl(47.9 95.8% 25%) !important;
}

.dark .border-green-200 {
  border-color: hsl(142.1 76.2% 25%) !important;
}

.dark .text-red-800 {
  color: hsl(0 62.8% 85%) !important;
}

.dark .text-yellow-800 {
  color: hsl(47.9 95.8% 85%) !important;
}

.dark .text-green-800 {
  color: hsl(142.1 76.2% 85%) !important;
}

.dark .text-red-600 {
  color: hsl(0 62.8% 70%) !important;
}

.dark .text-yellow-600 {
  color: hsl(47.9 95.8% 70%) !important;
}

.dark .text-green-600 {
  color: hsl(142.1 76.2% 70%) !important;
}

/* 全局字体大小优化 - 让文字更容易阅读 */
.text-xs {
  font-size: 0.8rem !important;  /* 从0.75rem增加到0.8rem */
}

.text-sm {
  font-size: 0.95rem !important;  /* 从0.875rem增加到0.95rem */
}

.text-base {
  font-size: 1.1rem !important;  /* 从1rem增加到1.1rem */
}

.text-lg {
  font-size: 1.25rem !important;  /* 从1.125rem增加到1.25rem */
}

.text-xl {
  font-size: 1.4rem !important;  /* 从1.25rem增加到1.4rem */
}

.text-2xl {
  font-size: 1.7rem !important;  /* 从1.5rem增加到1.7rem */
}

.text-3xl {
  font-size: 2.1rem !important;  /* 从1.875rem增加到2.1rem */
}

/* 表格文字大小优化 */
table {
  font-size: 1rem !important;  /* 确保表格文字足够大 */
}

td, th {
  font-size: 1rem !important;
  line-height: 1.6 !important;
}

/* 按钮文字大小优化 */
button {
  font-size: 1rem !important;
}

/* 输入框文字大小优化 */
input, textarea, select {
  font-size: 1rem !important;
}

/* 导航链接文字大小优化 */
nav a {
  font-size: 1rem !important;
}

/* 边框 */
.dark .border,
.dark .border-gray-200,
.dark .border-gray-300 {
  border-color: hsl(var(--border)) !important;
}

/* 输入框 */
.dark input,
.dark textarea,
.dark select {
  background-color: hsl(0 0% 14.9%) !important;
  color: hsl(0 0% 98%) !important;
  border-color: hsl(0 0% 20%) !important;
}

/* 悬停效果修复 */
.dark .hover\:bg-gray-50:hover,
.dark .hover\:bg-white:hover,
.dark .hover\:bg-gray-100:hover {
  background-color: hsl(0 0% 14.9%) !important;
  color: hsl(0 0% 98%) !important;
}

/* 下拉菜单和弹出层 */
.dark .dropdown-menu,
.dark .popover,
.dark .tooltip {
  background-color: hsl(0 0% 3.9%) !important;
  color: hsl(0 0% 98%) !important;
  border-color: hsl(0 0% 14.9%) !important;
}

/* 表格行悬停效果 */
.dark .hover\:bg-gray-50:hover,
.dark tr:hover {
  background-color: hsl(0 0% 14.9%) !important;
}

/* 按钮悬停效果 */
.dark button:hover {
  background-color: hsl(0 0% 14.9%) !important;
}

/* 卡片悬停效果 */
.dark .hover\:bg-gray-50:hover,
.dark .hover\:shadow-md:hover {
  background-color: hsl(0 0% 14.9%) !important;
}

/* Alert 组件深色主题修复 */
.dark .bg-red-50 {
  background-color: hsl(0 62.8% 15%) !important;
  color: hsl(0 0% 98%) !important;
}

.dark .bg-yellow-50 {
  background-color: hsl(45 100% 15%) !important;
  color: hsl(0 0% 98%) !important;
}

.dark .bg-green-50 {
  background-color: hsl(142 76% 15%) !important;
  color: hsl(0 0% 98%) !important;
}

.dark .bg-blue-50 {
  background-color: hsl(221 83% 15%) !important;
  color: hsl(0 0% 98%) !important;
}

.dark .bg-purple-50 {
  background-color: hsl(262 83% 15%) !important;
  color: hsl(0 0% 98%) !important;
}

/* 边框颜色修复 */
.dark .border-red-200 {
  border-color: hsl(0 62.8% 25%) !important;
}

.dark .border-yellow-200 {
  border-color: hsl(45 100% 25%) !important;
}

.dark .border-green-200 {
  border-color: hsl(142 76% 25%) !important;
}

.dark .border-blue-200 {
  border-color: hsl(221 83% 25%) !important;
}

.dark .border-purple-200 {
  border-color: hsl(262 83% 25%) !important;
}

/* 文字颜色修复 */
.dark .text-red-700,
.dark .text-red-800 {
  color: hsl(0 84% 70%) !important;
}

.dark .text-yellow-700,
.dark .text-yellow-800 {
  color: hsl(45 100% 70%) !important;
}

.dark .text-green-700,
.dark .text-green-800 {
  color: hsl(142 76% 70%) !important;
}

.dark .text-blue-700,
.dark .text-blue-800 {
  color: hsl(221 83% 70%) !important;
}

.dark .text-purple-700,
.dark .text-purple-800 {
  color: hsl(262 83% 70%) !important;
}

/* 渐变背景修复 */
.dark .bg-gradient-to-br {
  background: linear-gradient(to bottom right, hsl(0 0% 3.9%), hsl(0 0% 7%)) !important;
}

.dark .bg-gradient-to-r {
  background: linear-gradient(to right, hsl(0 0% 3.9%), hsl(0 0% 7%)) !important;
}

/* 阴影效果调整 */
.dark .shadow-lg,
.dark .shadow-xl {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3) !important;
}

/* 全局深色主题强制修复 - 确保所有白色背景都被覆盖 */
.dark *[class*="bg-white"],
.dark *[class*="bg-gray-50"],
.dark *[class*="bg-gray-100"] {
  background-color: hsl(0 0% 3.9%) !important;
  color: hsl(0 0% 98%) !important;
}

/* 悬停状态强制修复 */
.dark *[class*="hover:bg-white"]:hover,
.dark *[class*="hover:bg-gray-50"]:hover,
.dark *[class*="hover:bg-gray-100"]:hover {
  background-color: hsl(0 0% 14.9%) !important;
  color: hsl(0 0% 98%) !important;
}

/* 表格和列表项悬停效果 */
.dark table tr:hover,
.dark .table-row:hover,
.dark li:hover {
  background-color: hsl(0 0% 14.9%) !important;
}

/* 下拉菜单和弹出层通用修复 */
.dark .dropdown,
.dark .menu,
.dark .popover-content,
.dark .tooltip-content,
.dark [role="menu"],
.dark [role="listbox"],
.dark [role="option"] {
  background-color: hsl(0 0% 3.9%) !important;
  color: hsl(0 0% 98%) !important;
  border-color: hsl(0 0% 14.9%) !important;
}

/* 按钮和交互元素 */
.dark button:not([class*="bg-"]):hover,
.dark .clickable:hover {
  background-color: hsl(0 0% 14.9%) !important;
  color: hsl(0 0% 98%) !important;
}

/* 确保所有文字在深色背景下可见 */
.dark .text-gray-900,
.dark .text-gray-800,
.dark .text-gray-700,
.dark .text-black,
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6,
.dark p, .dark span, .dark div {
  color: hsl(0 0% 98%) !important;
}

/* Radix UI 组件深色主题修复 */
.dark [data-radix-popper-content-wrapper],
.dark [data-radix-dropdown-menu-content],
.dark [data-radix-select-content],
.dark [data-radix-popover-content],
.dark [data-radix-dialog-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Radix UI 菜单项悬停效果 */
.dark [data-radix-dropdown-menu-item]:hover,
.dark [data-radix-select-item]:hover,
.dark [data-radix-menu-item]:hover {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

/* 确保所有可能的白色背景都被覆盖 */
.dark [class*="bg-white"],
.dark [class*="bg-gray-50"],
.dark [class*="bg-gray-100"],
.dark [style*="background: white"],
.dark [style*="background: #fff"],
.dark [style*="background: rgb(255, 255, 255)"] {
  background-color: hsl(0 0% 3.9%) !important;
  color: hsl(0 0% 98%) !important;
}

/* 表单元素深色主题 */
.dark input[type="text"],
.dark input[type="email"],
.dark input[type="password"],
.dark input[type="number"],
.dark input[type="search"],
.dark textarea,
.dark select {
  background-color: hsl(0 0% 14.9%) !important;
  color: hsl(0 0% 98%) !important;
  border-color: hsl(0 0% 20%) !important;
}

/* 对话框和模态框样式优化 */
/* 浅色主题 - 确保足够的对比度 */
[data-radix-dialog-content] {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

/* 浅色主题下的对话框背景 */
:root [data-radix-dialog-content] {
  background-color: hsl(0 0% 100%) !important;
  color: hsl(222.2 84% 4.9%) !important;
  border-color: hsl(214.3 31.8% 91.4%) !important;
}

/* 深色主题下的对话框背景 */
.dark [data-radix-dialog-content] {
  background-color: hsl(0 0% 3.9%) !important;
  color: hsl(0 0% 98%) !important;
  border-color: hsl(0 0% 14.9%) !important;
}

/* 对话框覆盖层优化 */
[data-radix-dialog-overlay] {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 确保所有卡片在浅色主题下有明确的背景 */
:root .bg-card {
  background-color: hsl(0 0% 100%) !important;
  color: hsl(222.2 84% 4.9%) !important;
}

/* 确保所有卡片在深色主题下有明确的背景 */
.dark .bg-card {
  background-color: hsl(0 0% 3.9%) !important;
  color: hsl(0 0% 98%) !important;
}

/* 下拉菜单和选择器内容样式优化 */
/* 浅色主题 */
:root [data-radix-dropdown-menu-content],
:root [data-radix-select-content] {
  background-color: hsl(0 0% 100%) !important;
  color: hsl(222.2 84% 4.9%) !important;
  border-color: hsl(214.3 31.8% 91.4%) !important;
}

/* 深色主题 */
.dark [data-radix-dropdown-menu-content],
.dark [data-radix-select-content] {
  background-color: hsl(0 0% 3.9%) !important;
  color: hsl(0 0% 98%) !important;
  border-color: hsl(0 0% 14.9%) !important;
}

/* 菜单项悬停效果 - 浅色主题 */
:root [data-radix-dropdown-menu-item]:hover,
:root [data-radix-select-item]:hover {
  background-color: hsl(210 40% 96%) !important;
  color: hsl(222.2 84% 4.9%) !important;
}

/* 菜单项悬停效果 - 深色主题 */
.dark [data-radix-dropdown-menu-item]:hover,
.dark [data-radix-select-item]:hover {
  background-color: hsl(0 0% 14.9%) !important;
  color: hsl(0 0% 98%) !important;
}

/* 确保所有弹出层组件都有正确的背景 */
/* 浅色主题 */
:root .bg-popover {
  background-color: hsl(0 0% 100%) !important;
  color: hsl(222.2 84% 4.9%) !important;
}

/* 深色主题 */
.dark .bg-popover {
  background-color: hsl(0 0% 3.9%) !important;
  color: hsl(0 0% 98%) !important;
}

/* 修复所有可能的透明背景问题 */
/* 浅色主题下确保所有背景都是不透明的 */
:root [class*="bg-white"],
:root [class*="bg-gray-50"],
:root [class*="bg-background"] {
  background-color: hsl(0 0% 100%) !important;
  color: hsl(222.2 84% 4.9%) !important;
}

.dark input[type="text"]:focus,
.dark input[type="email"]:focus,
.dark input[type="password"]:focus,
.dark input[type="number"]:focus,
.dark input[type="search"]:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: hsl(221 83% 53%) !important;
  box-shadow: 0 0 0 2px hsl(221 83% 53% / 0.2) !important;
}

/* 按钮 */
.dark button {
  background-color: hsl(0 0% 14.9%);
  color: hsl(0 0% 98%);
  border-color: hsl(0 0% 20%);
}

/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* 玻璃态效果 */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 悬浮卡片效果 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 脉冲动画 */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 字体大小标准化 - 确保跨环境一致性 */
html {
  font-size: 16px; /* 设置根字体大小 */
}

/* 标准化文本大小 */
.text-xs { font-size: 0.75rem !important; }
.text-sm { font-size: 0.875rem !important; }
.text-base { font-size: 1rem !important; }
.text-lg { font-size: 1.125rem !important; }
.text-xl { font-size: 1.25rem !important; }
.text-2xl { font-size: 1.5rem !important; }
.text-3xl { font-size: 1.875rem !important; }

/* 确保按钮文字大小一致 */
button {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 确保输入框文字大小一致 */
input, textarea, select {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 确保表格文字大小一致 */
table, th, td {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 确保卡片标题大小一致 */
h1 { font-size: 1.875rem !important; line-height: 2.25rem !important; }
h2 { font-size: 1.5rem !important; line-height: 2rem !important; }
h3 { font-size: 1.25rem !important; line-height: 1.75rem !important; }
h4 { font-size: 1.125rem !important; line-height: 1.5rem !important; }
h5 { font-size: 1rem !important; line-height: 1.5rem !important; }
h6 { font-size: 0.875rem !important; line-height: 1.25rem !important; }

/* 确保导航栏文字大小一致 */
nav, nav a, nav button {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 确保侧边栏文字大小一致 - 增大字体 */
aside, aside a, aside button {
  font-size: 1.1rem !important;  /* 从0.875rem增加到1.1rem */
  line-height: 1.5rem !important;  /* 从1.25rem增加到1.5rem */
}

/* 强制字体渲染一致性 */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 特定组件字体大小控制 */
/* 卡片组件 */
.card-title {
  font-size: 1.125rem !important;
  line-height: 1.5rem !important;
}

.card-description {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 表单标签 */
label {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 下拉选择框 */
select {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 表格单元格内容 */
td input, th input, td select, th select, td textarea, th textarea {
  font-size: 0.8rem !important;
  line-height: 1.1rem !important;
}

/* 按钮内的图标和文字 */
button svg {
  width: 1rem !important;
  height: 1rem !important;
}

/* 导航链接 */
nav a {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 侧边栏特定样式 - 增大字体 */
.sidebar-title {
  font-size: 1.3rem !important;  /* 从1.125rem增加到1.3rem */
  line-height: 1.7rem !important;  /* 从1.5rem增加到1.7rem */
}

.sidebar-subtitle {
  font-size: 1rem !important;  /* 从0.875rem增加到1rem */
  line-height: 1.4rem !important;  /* 从1.25rem增加到1.4rem */
}

.sidebar-nav-item {
  font-size: 1.1rem !important;  /* 从0.875rem增加到1.1rem */
  line-height: 1.5rem !important;  /* 从1.25rem增加到1.5rem */
}

/* 对话框和模态框内容 */
[data-radix-dialog-content] * {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

[data-radix-dialog-title] {
  font-size: 1.125rem !important;
  line-height: 1.5rem !important;
}

/* 下拉菜单项 */
[data-radix-dropdown-menu-item] {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 工具提示 */
[data-radix-tooltip-content] {
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}

/* 响应式字体大小调整 - 确保在不同屏幕尺寸下保持一致 */
@media (max-width: 768px) {
  body {
    font-size: 13px !important;
  }

  button {
    font-size: 0.8rem !important;
  }

  input, textarea, select {
    font-size: 0.8rem !important;
  }
}

@media (min-width: 1920px) {
  body {
    font-size: 14px !important;
  }
}

/* 确保表格在不同环境下的一致性 */
.table-container {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

.table-container th,
.table-container td {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  padding: 0.5rem !important;
}

.table-container input,
.table-container select,
.table-container textarea {
  font-size: 0.8rem !important;
  line-height: 1.1rem !important;
  padding: 0.25rem 0.5rem !important;
}

/* 智能录单页面特定样式 */
.order-form-container {
  font-size: 0.875rem !important;
}

.order-form-container .card-title {
  font-size: 1.125rem !important;
  line-height: 1.5rem !important;
}

.order-form-container .card-description {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 侧边栏导航项目特定样式 - 增大字体和图标 */
.sidebar-nav-link {
  font-size: 1.1rem !important;  /* 从0.875rem增加到1.1rem */
  line-height: 1.5rem !important;  /* 从1.25rem增加到1.5rem */
  padding: 0.75rem 1rem !important;  /* 增加内边距 */
}

.sidebar-nav-link svg {
  width: 1.4rem !important;  /* 从1.25rem增加到1.4rem */
  height: 1.4rem !important;  /* 从1.25rem增加到1.4rem */
}

/* 确保所有文本输入框的一致性 */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="search"],
textarea {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-family: var(--font-geist-sans), system-ui, sans-serif !important;
}

/* 按钮文字和图标大小统一 */
button {
  font-family: var(--font-geist-sans), system-ui, sans-serif !important;
}

button svg {
  flex-shrink: 0 !important;
}

/* 确保下拉菜单的一致性 */
select {
  font-family: var(--font-geist-sans), system-ui, sans-serif !important;
}

/* 表格头部样式 */
thead th {
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  line-height: 1.25rem !important;
}

/* 表格数据行样式 */
tbody td {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* 工具栏响应式布局优化 */
.toolbar-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

@media (max-width: 1024px) {
  .toolbar-responsive {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-responsive > div {
    justify-content: center;
  }
}

/* 楼层标签页优化 */
.floor-tabs-container {
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.floor-tabs-container::-webkit-scrollbar {
  height: 4px;
}

.floor-tabs-container::-webkit-scrollbar-track {
  background: #f7fafc;
}

.floor-tabs-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 2px;
}

.floor-tabs-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 批量操作按钮组优化 */
.batch-actions-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

@media (max-width: 768px) {
  .batch-actions-group {
    width: 100%;
    justify-content: center;
  }

  .batch-actions-group button {
    flex: 1;
    min-width: 120px;
  }
}

/* 工具栏紧凑布局 */
.toolbar-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 1280px) {
  .toolbar-compact {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .toolbar-compact > div {
    justify-content: center;
  }
}

/* 批量备注按钮响应式 */
@media (max-width: 1024px) {
  .batch-notes-container {
    border-left: none !important;
    padding-left: 0 !important;
    width: 100%;
    justify-content: center;
  }
}

/* 渐变边框动画 */
@keyframes gradient-border {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-border {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient-border 4s ease infinite;
}

/* 打字机效果 */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid;
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: currentColor;
  }
}
