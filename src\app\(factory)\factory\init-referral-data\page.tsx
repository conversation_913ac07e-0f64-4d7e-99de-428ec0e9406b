'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { db } from '@/lib/database/index'
import { getCurrentFactoryId } from '@/lib/utils/factory'

export default function InitReferralDataPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<string[]>([])

  const addResult = (message: string) => {
    setResults(prev => [...prev, message])
    console.log(message)
  }

  const initializeReferralData = async () => {
    try {
      setLoading(true)
      setResults([])
      addResult('🔄 开始初始化推荐关系数据...')

      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        addResult('❌ 无法获取工厂ID')
        return
      }

      // 获取所有客户
      const clients = await db.getClientsByFactoryId(factoryId)
      addResult(`📋 获取到 ${clients.length} 个客户`)

      if (clients.length < 3) {
        addResult('⚠️ 客户数量不足，需要至少3个客户才能设置推荐关系')
        return
      }

      // 设置推荐关系
      const [client1, client2, client3, ...otherClients] = clients

      // 让第一个客户推荐第二个和第三个客户
      if (client2 && !client2.referrerId) {
        try {
          await db.updateClient(client2.id, {
            referrerId: client1.id,
            referrerName: client1.name
          })
          addResult(`✅ 设置推荐关系: ${client1.name} 推荐了 ${client2.name}`)
        } catch (error) {
          addResult(`❌ 设置推荐关系失败: ${error}`)
        }
      } else {
        addResult(`ℹ️ ${client2.name} 已有推荐人: ${client2.referrerName || client2.referrerId}`)
      }

      if (client3 && !client3.referrerId) {
        try {
          await db.updateClient(client3.id, {
            referrerId: client1.id,
            referrerName: client1.name
          })
          addResult(`✅ 设置推荐关系: ${client1.name} 推荐了 ${client3.name}`)
        } catch (error) {
          addResult(`❌ 设置推荐关系失败: ${error}`)
        }
      } else {
        addResult(`ℹ️ ${client3.name} 已有推荐人: ${client3.referrerName || client3.referrerId}`)
      }

      // 让第二个客户推荐其他客户
      if (otherClients.length > 0 && !otherClients[0].referrerId) {
        try {
          await db.updateClient(otherClients[0].id, {
            referrerId: client2.id,
            referrerName: client2.name
          })
          addResult(`✅ 设置推荐关系: ${client2.name} 推荐了 ${otherClients[0].name}`)
        } catch (error) {
          addResult(`❌ 设置推荐关系失败: ${error}`)
        }
      } else if (otherClients.length > 0) {
        addResult(`ℹ️ ${otherClients[0].name} 已有推荐人: ${otherClients[0].referrerName || otherClients[0].referrerId}`)
      }

      // 更新所有客户的统计信息
      addResult('🔄 更新客户统计信息...')
      for (const client of clients) {
        if (typeof db.updateClientStatistics === 'function') {
          await db.updateClientStatistics(client.id)
          addResult(`✅ 更新 ${client.name} 的统计信息`)
        }
      }

      addResult('🎉 推荐关系数据初始化完成!')

    } catch (error) {
      addResult(`❌ 初始化失败: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const checkReferralData = async () => {
    try {
      setLoading(true)
      setResults([])
      addResult('🔍 检查推荐关系数据...')

      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        addResult('❌ 无法获取工厂ID')
        return
      }

      const clients = await db.getClientsByFactoryId(factoryId)
      addResult(`📋 获取到 ${clients.length} 个客户`)

      clients.forEach(client => {
        addResult(`\n客户: ${client.name} (${client.id})`)
        addResult(`  推荐人ID: ${client.referrerId || '无'}`)
        addResult(`  推荐人姓名: ${client.referrerName || '无'}`)
        addResult(`  存储的推荐数量: ${client.referralCount || 0}`)
        
        // 实时计算推荐数量
        const actualReferralCount = clients.filter(c => c.referrerId === client.id).length
        addResult(`  实际推荐数量: ${actualReferralCount}`)
        
        if (actualReferralCount > 0) {
          const referredClients = clients.filter(c => c.referrerId === client.id)
          addResult(`  推荐的客户:`)
          referredClients.forEach(referred => {
            addResult(`    - ${referred.name} (${referred.phone})`)
          })
        }
      })

    } catch (error) {
      addResult(`❌ 检查失败: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">初始化推荐关系数据</h1>
          <p className="text-gray-600">为客户设置推荐关系，解决介绍排行榜无数据问题</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>操作面板</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={initializeReferralData}
                disabled={loading}
                className="w-full"
              >
                {loading ? '处理中...' : '初始化推荐关系'}
              </Button>
              
              <Button
                onClick={checkReferralData}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                {loading ? '检查中...' : '检查推荐数据'}
              </Button>

              <Button
                onClick={() => window.open('/factory/clients', '_blank')}
                variant="outline"
                className="w-full"
              >
                返回客户管理
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>执行结果</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                {results.length > 0 ? (
                  <div className="space-y-1">
                    {results.map((result, index) => (
                      <div key={index} className="text-sm font-mono">
                        {result}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">暂无执行结果</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
