// 🇨🇳 风口云平台 - Prisma Schema
// 生产环境使用 PostgreSQL

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 工厂状态枚举
enum FactoryStatus {
  active      // 正常运行
  inactive    // 未激活
  suspended   // 暂停使用
  expired     // 已过期
}

// 订阅类型枚举
enum SubscriptionType {
  trial       // 试用期
  monthly     // 按月
  quarterly   // 按季度
  yearly      // 按年
  permanent   // 永久
}

// 管理员角色枚举
enum AdminRole {
  admin
  super_admin
}

// 工厂用户角色枚举
enum FactoryUserRole {
  owner
  manager
  employee
}

// 客户状态枚举
enum ClientStatus {
  active
  inactive
  blacklisted
}

// 订单状态枚举
enum OrderStatus {
  pending
  confirmed
  in_production
  completed
  cancelled
}

// 订单付款状态枚举
enum OrderPaymentStatus {
  unpaid
  partial
  paid
  overdue
}

// 付款方式枚举
enum PaymentMethod {
  cash
  bank_transfer
  alipay
  wechat_pay
  other
}

// 付款状态枚举
enum PaymentStatus {
  pending
  completed
  failed
  refunded
}

// 公告类型枚举
enum AnnouncementType {
  system
  maintenance
  feature
  warning
}

// 公告优先级枚举
enum AnnouncementPriority {
  low
  medium
  high
  urgent
}

// 工厂信息表
model Factory {
  id        String        @id @default(cuid())
  name      String
  code      String        @unique
  address   String?
  phone     String?
  email     String?
  status    FactoryStatus @default(active)
  settings  String? // JSON字符串

  // 使用时间管理字段
  subscriptionType    SubscriptionType @default(trial) @map("subscription_type")
  subscriptionStart   DateTime?        @map("subscription_start")
  subscriptionEnd     DateTime?        @map("subscription_end")
  firstLoginAt        DateTime?        @map("first_login_at")
  isPermanent         Boolean          @default(false) @map("is_permanent")
  suspendedAt         DateTime?        @map("suspended_at")
  suspendedReason     String?          @map("suspended_reason")

  // 订阅管理增强字段
  lastStatusCheck     DateTime?        @map("last_status_check")
  autoSuspendEnabled  Boolean          @default(true) @map("auto_suspend_enabled")
  suspendedBy         String?          @map("suspended_by")
  reactivatedAt       DateTime?        @map("reactivated_at")
  reactivatedBy       String?          @map("reactivated_by")
  totalSuspendedMs    BigInt           @default(0) @map("total_suspended_ms")

  createdAt DateTime      @default(now()) @map("created_at")
  updatedAt DateTime      @updatedAt @map("updated_at")

  // 关联关系
  users                  FactoryUser[]
  clients                Client[]
  orders                 Order[]
  announcements          AnnouncementTarget[]
  announcementReadStatus AnnouncementReadStatus[]
  shareholders           Shareholder[]
  dividends              Dividend[]
  rewardUsages           RewardUsage[]

  @@index([status])
  @@index([subscriptionEnd])
  @@index([subscriptionType])
  @@index([suspendedAt])
  @@map("factories")
}

// 管理员表
model Admin {
  id           String    @id @default(cuid())
  username     String    @unique
  passwordHash String    @map("password_hash")
  name         String
  email        String?   @unique
  role         AdminRole @default(admin)
  isActive     Boolean   @default(true) @map("is_active")
  lastLoginAt  DateTime? @map("last_login_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // 关联关系
  announcements Announcement[]

  @@map("admins")
}

// 工厂用户表
model FactoryUser {
  id           String            @id @default(cuid())
  factoryId    String            @map("factory_id")
  username     String            @unique
  passwordHash String            @map("password_hash")
  name         String
  role         FactoryUserRole
  permissions  String
  isActive     Boolean           @default(true) @map("is_active")
  lastLoginAt  DateTime?         @map("last_login_at")
  firstLoginAt DateTime?         @map("first_login_at")
  createdAt    DateTime          @default(now()) @map("created_at")
  updatedAt    DateTime          @updatedAt @map("updated_at")

  // 关联关系
  factory                Factory                  @relation(fields: [factoryId], references: [id])
  orders                 Order[]
  announcementReadStatus AnnouncementReadStatus[]

  @@map("factory_users")
}

// 客户信息表
model Client {
  id           String       @id @default(cuid())
  factoryId    String       @map("factory_id")
  name         String
  phone        String
  email        String?
  company      String?
  address      String?
  referrerId   String?      @map("referrer_id")
  referrerName String?      @map("referrer_name")
  status       ClientStatus @default(active)
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @updatedAt @map("updated_at")

  // 统计字段
  totalOrders    Int?   @default(0) @map("total_orders")
  totalAmount    Float? @default(0) @map("total_amount")
  paidAmount     Float? @default(0) @map("paid_amount")
  unpaidAmount   Float? @default(0) @map("unpaid_amount")
  overdueAmount  Float? @default(0) @map("overdue_amount")
  lastOrderDate  DateTime? @map("last_order_date")
  lastPaymentDate DateTime? @map("last_payment_date")

  // 推荐奖励字段
  referralCount    Int?   @default(0) @map("referral_count")
  referralReward   Float? @default(0) @map("referral_reward")
  availableReward  Float? @default(0) @map("available_reward")
  usedReward       Float? @default(0) @map("used_reward")
  pendingReward    Float? @default(0) @map("pending_reward")

  // 关联关系
  factory      Factory       @relation(fields: [factoryId], references: [id])
  orders       Order[]
  payments     Payment[]
  rewardUsages RewardUsage[]

  @@unique([factoryId, phone], name: "unique_factory_phone")
  @@map("clients")
}

// 订单信息表
model Order {
  id             String           @id @default(cuid())
  factoryId      String           @map("factory_id")
  clientId       String           @map("client_id")
  orderNumber    String           @unique @map("order_number")
  items          String // JSON字符串
  totalAmount    Float            @map("total_amount")
  status         OrderStatus      @default(pending)
  projectAddress String?          @map("project_address")
  clientName     String?          @map("client_name")
  clientPhone    String?          @map("client_phone")
  notes          String?
  paymentStatus  OrderPaymentStatus @default(unpaid) @map("payment_status")
  paidAmount     Float            @default(0) @map("paid_amount")
  dueDate        DateTime?        @map("due_date")
  paymentNotes   String?          @map("payment_notes")
  completedAt    DateTime?        @map("completed_at")
  createdBy      String           @map("created_by")
  createdByName  String?          @map("created_by_name")
  createdAt      DateTime         @default(now()) @map("created_at")
  updatedAt      DateTime         @updatedAt @map("updated_at")

  // 关联关系
  factory     Factory       @relation(fields: [factoryId], references: [id])
  client      Client        @relation(fields: [clientId], references: [id])
  creator     FactoryUser   @relation(fields: [createdBy], references: [id])
  payments    Payment[]
  rewardUsages RewardUsage[]

  @@map("orders")
}

// 付款记录表
model Payment {
  id            String        @id @default(cuid())
  orderId       String        @map("order_id")
  clientId      String        @map("client_id")
  amount        Float
  paymentMethod PaymentMethod @map("payment_method")
  status        PaymentStatus @default(pending)
  paidAt        DateTime?     @map("paid_at")
  notes         String?
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")

  // 关联关系
  order  Order  @relation(fields: [orderId], references: [id])
  client Client @relation(fields: [clientId], references: [id])

  @@map("payments")
}

// 公告表
model Announcement {
  id          String               @id @default(cuid())
  title       String
  content     String
  type        AnnouncementType     @default(system)
  priority    AnnouncementPriority @default(medium)
  isActive    Boolean              @default(true) @map("is_active")
  publishedAt DateTime?            @map("published_at")
  expiresAt   DateTime?            @map("expires_at")
  createdBy   String               @map("created_by")
  createdAt   DateTime             @default(now()) @map("created_at")
  updatedAt   DateTime             @updatedAt @map("updated_at")

  // 关联关系
  creator     Admin                    @relation(fields: [createdBy], references: [id])
  targets     AnnouncementTarget[]
  readStatus  AnnouncementReadStatus[]

  @@map("announcements")
}

// 公告目标表（指定哪些工厂可以看到公告）
model AnnouncementTarget {
  id             String @id @default(cuid())
  announcementId String @map("announcement_id")
  factoryId      String @map("factory_id")

  // 关联关系
  announcement Announcement @relation(fields: [announcementId], references: [id], onDelete: Cascade)
  factory      Factory      @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  @@unique([announcementId, factoryId])
  @@map("announcement_targets")
}

// 公告阅读状态表
model AnnouncementReadStatus {
  id             String   @id @default(cuid())
  announcementId String   @map("announcement_id")
  factoryId      String   @map("factory_id")
  userId         String   @map("user_id")
  readAt         DateTime @default(now()) @map("read_at")

  // 关联关系
  announcement Announcement @relation(fields: [announcementId], references: [id], onDelete: Cascade)
  factory      Factory      @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  user         FactoryUser  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([announcementId, userId])
  @@map("announcement_read_status")
}

// 股东信息表
model Shareholder {
  id               String   @id @default(cuid())
  factoryId        String   @map("factory_id")
  name             String
  investmentAmount Float    @map("investment_amount")
  sharePercentage  Float    @map("share_percentage")
  joinDate         DateTime @map("join_date")
  isActive         Boolean  @default(true) @map("is_active")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // 关联关系
  factory         Factory          @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  dividendRecords DividendRecord[]

  @@map("shareholders")
}

// 分红记录表
model Dividend {
  id              String   @id @default(cuid())
  factoryId       String   @map("factory_id")
  totalAmount     Float    @map("total_amount")
  distributionDate DateTime @map("distribution_date")
  period          String // 分红周期，如 "2024Q1"
  notes           String?
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // 关联关系
  factory         Factory          @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  dividendRecords DividendRecord[]

  @@map("dividends")
}

// 分红详细记录表
model DividendRecord {
  id            String   @id @default(cuid())
  dividendId    String   @map("dividend_id")
  shareholderId String   @map("shareholder_id")
  amount        Float
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关联关系
  dividend    Dividend    @relation(fields: [dividendId], references: [id], onDelete: Cascade)
  shareholder Shareholder @relation(fields: [shareholderId], references: [id], onDelete: Cascade)

  @@map("dividend_records")
}

// 奖励使用记录表
model RewardUsage {
  id              String   @id @default(cuid())
  factoryId       String   @map("factory_id")
  clientId        String   @map("client_id")
  orderId         String?  @map("order_id")
  amount          Float
  type            String // 奖励类型：referral, loyalty 等
  description     String?
  relatedOrderId  String?  @map("related_order_id")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // 关联关系
  factory Factory @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  client  Client  @relation(fields: [clientId], references: [id], onDelete: Cascade)
  order   Order?  @relation(fields: [orderId], references: [id])

  @@map("reward_usages")
}

// 轮播图表
model CarouselImage {
  id          String   @id @default(cuid())
  title       String
  description String?
  imageUrl    String   @map("image_url")
  linkUrl     String?  @map("link_url")
  order       Int      @default(0)
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("carousel_images")
}

// 系统设置表
model SystemSetting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      String   @default("string") // string, number, boolean, json
  category  String   @default("general")
  description String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("system_settings")
}

// 审计日志表
model AuditLog {
  id         String   @id @default(cuid())
  userId     String?  @map("user_id")
  userType   String   @map("user_type") // admin, factory_user
  action     String
  resource   String
  resourceId String?  @map("resource_id")
  details    String? // JSON字符串
  ipAddress  String?  @map("ip_address")
  userAgent  String?  @map("user_agent")
  createdAt  DateTime @default(now()) @map("created_at")

  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([createdAt])
  @@map("audit_logs")
}

// 登录记录表
model LoginRecord {
  id              String    @id @default(cuid())
  userId          String    @map("user_id")
  userType        String    @map("user_type") // admin, factory_user
  username        String    // 登录用户名
  userName        String    @map("user_name") // 用户真实姓名
  factoryId       String?   @map("factory_id") // 工厂ID（仅工厂用户）
  factoryName     String?   @map("factory_name") // 工厂名称

  // 登录信息
  loginTime       DateTime  @default(now()) @map("login_time")
  ipAddress       String?   @map("ip_address")
  userAgent       String?   @map("user_agent")
  deviceInfo      String?   @map("device_info") // 设备信息
  location        String?   // 登录地点

  // 登录状态
  loginStatus     String    @map("login_status") // success, failed, blocked
  failReason      String?   @map("fail_reason") // 失败原因

  // 会话信息
  sessionId       String?   @map("session_id")
  logoutTime      DateTime? @map("logout_time")
  sessionDuration Int?      @map("session_duration") // 会话时长（秒）

  createdAt       DateTime  @default(now()) @map("created_at")

  @@index([userId])
  @@index([userType])
  @@index([loginTime])
  @@index([loginStatus])
  @@index([factoryId])
  @@map("login_records")
}

// 数据同步事件表
model SyncEvent {
  id          String   @id @default(cuid())
  eventType   String   @map("event_type")
  resourceId  String   @map("resource_id")
  resourceType String  @map("resource_type")
  action      String // create, update, delete
  data        String? // JSON字符串
  processed   Boolean  @default(false)
  createdAt   DateTime @default(now()) @map("created_at")
  processedAt DateTime? @map("processed_at")

  @@index([processed])
  @@index([eventType])
  @@index([createdAt])
  @@map("sync_events")
}
