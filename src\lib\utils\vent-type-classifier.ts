// 风口类型分类工具函数
// 统一管理所有风口类型的分类逻辑，确保系统中所有地方使用相同的分类标准

/**
 * 常规风口类型列表 (元/㎡ 计价)
 * 计算公式：(长+60) × (宽+60) ÷ 1,000,000 × 单价 × 数量
 */
export const REGULAR_VENT_TYPES = [
  'double_white_outlet',        // 双层白色出风口
  'double_black_outlet',        // 双层黑色出风口
  'white_black_bottom_outlet',  // 白色黑底出风口
  'white_black_bottom_return',  // 白色黑底回风口
  'white_return',               // 白色回风口
  'black_return',               // 黑色回风口
  'white_linear',               // 白色线型风口
  'black_linear',               // 黑色线型风口
  'white_linear_return',        // 白色线型回风口
  'black_linear_return',        // 黑色线型回风口
  'maintenance'                 // 检修口
] as const

/**
 * 高端风口类型列表 (元/m 计价)
 * 计算公式：(长+120) ÷ 1,000 × 单价 × 数量
 */
export const PREMIUM_VENT_TYPES = [
  'high_end_white_outlet',    // 高端白色出风口
  'high_end_black_outlet',    // 高端黑色出风口
  'high_end_white_return',    // 高端白色回风口
  'high_end_black_return',    // 高端黑色回风口
  'arrow_outlet',             // 箭型出风口
  'arrow_return',             // 箭型回风口
  'claw_outlet',              // 爪型出风口
  'claw_return',              // 爪型回风口
  'black_white_outlet',       // 黑白双色出风口 (实际使用)
  'black_white_return',       // 黑白双色回风口 (实际使用)
  'black_white_dual_outlet',  // 黑白双色出风口 (兼容)
  'black_white_dual_return',  // 黑白双色回风口 (兼容)
  'wood_outlet',              // 木纹出风口 (实际使用)
  'wood_return',              // 木纹回风口 (实际使用)
  'wood_grain_outlet',        // 木纹出风口 (兼容)
  'wood_grain_return',        // 木纹回风口 (兼容)
  'white_putty_outlet',       // 白色腻子粉出风口
  'white_putty_return',       // 白色腻子粉回风口
  'black_putty_outlet',       // 黑色腻子粉出风口
  'black_putty_return',       // 黑色腻子粉回风口
  'white_gypsum_outlet',      // 白色石膏板出风口
  'white_gypsum_return',      // 白色石膏板回风口
  'black_gypsum_outlet',      // 黑色石膏板出风口
  'black_gypsum_return'       // 黑色石膏板回风口
] as const

/**
 * 判断是否为常规风口
 * @param productType 产品类型
 * @returns 是否为常规风口
 */
export const isRegularVent = (productType: string): boolean => {
  return REGULAR_VENT_TYPES.includes(productType as any)
}

/**
 * 判断是否为高端风口
 * @param productType 产品类型
 * @param productName 产品名称（可选，用于更精确的判断）
 * @returns 是否为高端风口
 */
export const isPremiumVent = (productType: string, productName?: string): boolean => {
  // 首先检查产品类型
  if (PREMIUM_VENT_TYPES.includes(productType as any)) {
    return true
  }

  // 如果有产品名称，检查关键词
  if (productName) {
    const premiumKeywords = ['高端', '定制', '木纹', '石膏板']
    return premiumKeywords.some(keyword => productName.includes(keyword))
  }

  return false
}

/**
 * 获取风口类型的计价方式
 * @param productType 产品类型
 * @returns 'area' | 'length' - 面积计价或长度计价
 */
export const getVentPricingMethod = (productType: string): 'area' | 'length' => {
  return isRegularVent(productType) ? 'area' : 'length'
}

/**
 * 获取风口类型的单位
 * @param productType 产品类型
 * @returns '元/㎡' | '元/m'
 */
export const getVentPricingUnit = (productType: string): '元/㎡' | '元/m' => {
  return isRegularVent(productType) ? '元/㎡' : '元/m'
}

/**
 * 计算风口价格
 * @param params 计算参数
 * @returns 计算后的价格
 */
export const calculateVentPrice = (params: {
  productType: string
  length: number
  width: number
  quantity: number
  unitPrice: number
}): number => {
  const { productType, length, width, quantity, unitPrice } = params

  if (unitPrice <= 0) return 0

  let calculatedPrice = 0

  if (isRegularVent(productType)) {
    // 常规风口 (元/㎡)：(长+60) × (宽+60) ÷ 1,000,000 × 单价 × 数量
    if (length > 0 && width > 0) {
      const area = (length + 60) * (width + 60) / 1000000
      calculatedPrice = area * unitPrice * quantity
    }
  } else {
    // 高端风口 (元/m)：(长+120) ÷ 1,000 × 单价 × 数量
    if (length > 0) {
      calculatedPrice = ((length + 120) / 1000) * unitPrice * quantity
    }
  }

  // 🔧 使用统一的价格精度处理函数
  return Math.round(calculatedPrice * 10) / 10
}

/**
 * 所有风口类型列表（用于类型检查和选项列表）
 */
export const ALL_VENT_TYPES = [...REGULAR_VENT_TYPES, ...PREMIUM_VENT_TYPES] as const

/**
 * 风口类型定义（TypeScript 类型）
 */
export type VentType = typeof ALL_VENT_TYPES[number]
