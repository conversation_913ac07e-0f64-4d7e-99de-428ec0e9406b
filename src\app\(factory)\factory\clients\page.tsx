"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import {
  Users,
  Plus,
  Search,
  Phone,
  Mail,
  MapPin,
  Building,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Loader2,
  AlertCircle,
  DollarSign,
  RefreshCw,
  Trophy,
  TrendingUp,
  Clock,
  UserPlus,
  Crown,
  Target
} from "lucide-react"
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { db } from '@/lib/database/client'
import type { Client, Order } from '@/types'
import { AddClientDialog } from '@/components/clients/add-client-dialog'
import { ClientDetailDialog } from '@/components/clients/client-detail-dialog'
import { ClientPaymentDialog } from '@/components/clients/client-payment-dialog'
import { DeleteClientDialog } from '@/components/clients/delete-client-dialog'
import { FactoryAnnouncements } from '@/components/announcements/factory-announcements'
import { safeNumber, safeLocaleString, safeWanYuan, safeAmountFormat, safeAmountSum } from '@/lib/utils/number-utils'



const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'text-green-600 bg-green-100'
    case 'inactive': return 'text-gray-600 bg-gray-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'inactive': return '非活跃'
    default: return '未知'
  }
}

// 格式化金额显示，去除前导零
const formatAmount = (amount: unknown): string => {
  return safeAmountFormat(amount, 1)
}

export default function ClientsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [clients, setClients] = useState<Client[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [showClientDetail, setShowClientDetail] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)

  // 实时推荐奖励状态
  const [clientRewards, setClientRewards] = useState<Record<string, {
    totalReward: number
    availableReward: number
    pendingReward: number
    usedReward: number
  }>>({})
  const [rewardsLoading, setRewardsLoading] = useState(false)

  // 加载客户实时推荐奖励
  const loadClientRewards = async (clientList?: Client[]) => {
    const targetClients = clientList || clients
    if (!targetClients.length) return

    setRewardsLoading(true)
    const factoryId = getCurrentFactoryId()
    if (!factoryId) return

    try {
      console.log('🎁 开始加载客户实时推荐奖励...')
      const rewardPromises = targetClients.map(async (client) => {
        try {
          const response = await fetch(`/api/clients/${client.id}/reward-status?factoryId=${factoryId}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
            }
          })
          if (response.ok) {
            const result = await response.json()
            if (result.success) {
              console.log(`✅ 客户 ${client.name} 推荐奖励:`, result.data)
              return {
                clientId: client.id,
                rewards: {
                  totalReward: result.data.totalReward,
                  availableReward: result.data.availableReward,
                  pendingReward: result.data.pendingReward,
                  usedReward: result.data.usedReward || 0
                }
              }
            } else {
              console.warn(`⚠️ 客户 ${client.name} API返回失败:`, result.error)
            }
          } else {
            console.warn(`⚠️ 客户 ${client.name} API请求失败:`, response.status)
          }
        } catch (error) {
          console.warn(`⚠️ 获取客户 ${client.name} 推荐奖励失败:`, error)
        }

        // 返回默认值
        return {
          clientId: client.id,
          rewards: {
            totalReward: 0,
            availableReward: 0,
            pendingReward: 0,
            usedReward: 0
          }
        }
      })

      const results = await Promise.all(rewardPromises)
      const rewardsMap: Record<string, any> = {}

      results.forEach(result => {
        rewardsMap[result.clientId] = result.rewards
      })

      setClientRewards(rewardsMap)
      console.log('✅ 客户实时推荐奖励加载完成')

    } catch (error) {
      console.error('❌ 加载客户推荐奖励失败:', error)
    } finally {
      setRewardsLoading(false)
    }
  }

  // 实时计算客户统计信息
  const getClientStats = (clientId: string) => {
    // 确保orders数组已加载
    if (!orders || orders.length === 0) {
      console.log(`客户 ${clientId}: 订单数据未加载，返回默认值`)
      return {
        totalOrders: 0,
        totalAmount: 0,
        paidAmount: 0,
        unpaidAmount: 0
      }
    }

    const clientOrders = orders.filter(order => order.clientId === clientId)
    const totalOrders = clientOrders.length

    // 🔧 修复：使用专门的累加函数处理精度问题
    const totalAmounts = clientOrders.map(order => {
      const amount = safeNumber(order.totalAmount)
      console.log(`订单 ${order.id}: 金额 ¥${amount}`)
      return amount
    })
    const paidAmounts = clientOrders.map(order => safeNumber(order.paidAmount))

    const finalTotalAmount = safeAmountSum(totalAmounts)
    const finalPaidAmount = safeAmountSum(paidAmounts)
    const finalUnpaidAmount = Math.round((finalTotalAmount - finalPaidAmount) * 10) / 10

    console.log(`客户 ${clientId}: ${totalOrders}个订单, 总金额 ¥${finalTotalAmount}`)

    return {
      totalOrders,
      totalAmount: finalTotalAmount,
      paidAmount: finalPaidAmount,
      unpaidAmount: finalUnpaidAmount
    }
  }

  // 计算排行榜数据
  const getRankingData = () => {
    if (!clients.length || !orders.length) {
      return {
        orderRanking: [],
        amountRanking: [],
        debtRanking: [],
        referralRanking: [],
        recentActiveClients: []
      }
    }

    // 为每个客户计算统计信息
    const clientsWithStats = clients.map(client => {
      const stats = getClientStats(client.id)
      const clientOrders = orders.filter(order => order.clientId === client.id)
      const lastOrderDate = clientOrders.length > 0
        ? Math.max(...clientOrders.map(order => new Date(order.createdAt || 0).getTime()))
        : 0

      // 实时计算推荐数量
      const referralCount = clients.filter(c => c.referrerId === client.id).length

      return {
        ...client,
        ...stats,
        lastOrderDate,
        referralCount // 使用实时计算的推荐数量
      }
    })

    // 订单数量排行榜 (前5名)
    const orderRanking = clientsWithStats
      .filter(client => client.totalOrders > 0)
      .sort((a, b) => b.totalOrders - a.totalOrders)
      .slice(0, 5)

    // 金额排行榜 (前5名)
    const amountRanking = clientsWithStats
      .filter(client => safeNumber(client.totalAmount) > 0)
      .sort((a, b) => safeNumber(b.totalAmount) - safeNumber(a.totalAmount))
      .slice(0, 5)

    // 欠款排行榜 (前5名)
    const debtRanking = clientsWithStats
      .filter(client => safeNumber(client.unpaidAmount) > 0)
      .sort((a, b) => safeNumber(b.unpaidAmount) - safeNumber(a.unpaidAmount))
      .slice(0, 5)

    // 介绍排行榜 (前5名) - 使用实时计算的推荐数量
    const referralRanking = clientsWithStats
      .filter(client => client.referralCount > 0)
      .sort((a, b) => b.referralCount - a.referralCount)
      .slice(0, 5)

    // 调试信息
    console.log('🔍 推荐排行榜调试信息:')
    clientsWithStats.forEach(client => {
      if (client.referralCount > 0) {
        console.log(`  ${client.name}: 推荐了 ${client.referralCount} 个客户`)
        const referredClients = clients.filter(c => c.referrerId === client.id)
        referredClients.forEach(referred => {
          console.log(`    - ${referred.name} (${referred.phone})`)
        })
      }
    })

    // 最近活跃客户 (前5名)
    const recentActiveClients = clientsWithStats
      .filter(client => client.lastOrderDate > 0)
      .sort((a, b) => b.lastOrderDate - a.lastOrderDate)
      .slice(0, 5)

    return {
      orderRanking,
      amountRanking,
      debtRanking,
      referralRanking,
      recentActiveClients
    }
  }

  // 加载客户数据
  const loadClients = async () => {
    try {
      setLoading(true)
      setError("")
      console.log('🔄 开始加载客户数据...')

      const factoryId = getCurrentFactoryId()
      console.log('📋 工厂ID:', factoryId)

      if (!factoryId) {
        setError("无法获取工厂信息")
        return
      }

      // 检查数据库服务是否可用
      if (!db || typeof db.getClientsByFactoryId !== 'function') {
        console.warn('⚠️ 数据库服务不可用，显示空列表')
        setClients([])
        return
      }

      console.log('🗄️ 调用数据库服务获取客户...')
      const factoryClients = await db.getClientsByFactoryId(factoryId)
      console.log('✅ 客户数据加载成功:', factoryClients)

      console.log('🗄️ 调用数据库服务获取订单...')
      const factoryOrders = await db.getOrdersByFactoryId(factoryId)
      console.log('✅ 订单数据加载成功:', factoryOrders)

      // 打印订单详细信息用于调试
      factoryOrders.forEach(order => {
        console.log(`订单 ${order.id}: clientId=${order.clientId}, 金额=¥${order.totalAmount}`)
      })

      setOrders(factoryOrders)

      // 检查是否需要更新统计信息
      console.log('🔄 检查客户统计信息...')
      let needsUpdate = false

      for (const client of factoryClients) {
        if (!client.totalOrders || client.totalOrders === 0) {
          needsUpdate = true
          break
        }
      }

      if (needsUpdate) {
        console.log('🔄 需要更新客户统计信息，开始更新...')
        for (const client of factoryClients) {
          console.log(`🔄 更新客户 ${client.name} 的统计信息...`)
          if (typeof db.updateClientStatistics === 'function') {
            await db.updateClientStatistics(client.id)
          }
        }

        // 重新获取更新后的客户数据
        const updatedClients = await db.getClientsByFactoryId(factoryId)
        console.log('✅ 客户统计信息更新完成')

        // 打印每个客户的统计信息用于调试
        updatedClients.forEach(client => {
          console.log(`客户 ${client.name}: 订单${client.totalOrders || 0}个, 总额¥${(client.totalAmount || 0).toLocaleString()}`)
        })

        setClients(updatedClients)
        // 加载实时推荐奖励
        setTimeout(() => loadClientRewards(updatedClients), 100)
      } else {
        console.log('✅ 客户统计信息已是最新，无需更新')
        setClients(factoryClients)
        // 加载实时推荐奖励
        setTimeout(() => loadClientRewards(factoryClients), 100)
      }
    } catch (error) {
      console.error('❌ 加载客户数据失败:', error)
      setError("加载客户数据失败，请重试")
    } finally {
      setLoading(false)
    }
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadClients()

    // 设置自动刷新机制，每30秒检查一次数据变化
    const interval = setInterval(() => {
      checkForDataUpdates()
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  // 当客户数据加载完成后，加载推荐奖励
  useEffect(() => {
    if (clients.length > 0) {
      loadClientRewards()
    }
  }, [clients])

  // 检查数据更新
  const checkForDataUpdates = async () => {
    try {
      const syncEvents = JSON.parse(localStorage.getItem('sync_events') || '[]')
      const lastEvent = syncEvents[syncEvents.length - 1]

      if (lastEvent && (!lastSyncTime || new Date(lastEvent.timestamp) > lastSyncTime)) {
        console.log('🔄 检测到数据更新，重新加载客户数据...')
        await loadClients()
        setLastSyncTime(new Date())
      }
    } catch (error) {
      console.error('❌ 检查数据更新失败:', error)
    }
  }

  // 处理客户添加成功
  const handleClientAdded = (newClient: Client) => {
    setClients(prev => [newClient, ...prev])
  }

  // 处理查看客户详情
  const handleViewClient = (client: Client) => {
    setSelectedClient(client)
    setShowClientDetail(true)
  }

  // 处理客户付款管理
  const handleManagePayment = (client: Client) => {
    setSelectedClient(client)
    setShowPaymentDialog(true)
  }

  // 处理付款更新后的回调
  const handlePaymentUpdated = () => {
    // 重新加载客户数据以更新统计信息
    loadClients()
  }

  // 处理删除客户
  const handleDeleteClient = (client: Client) => {
    setSelectedClient(client)
    setShowDeleteDialog(true)
  }

  // 处理客户删除完成
  const handleClientDeleted = (clientId: string) => {
    console.log('🗑️ 客户已删除，刷新客户列表')
    loadClients()
  }

  // 手动刷新所有客户统计信息
  const refreshAllClientStatistics = async () => {
    try {
      setLoading(true)
      console.log('🔄 手动刷新所有客户统计信息...')

      const factoryId = getCurrentFactoryId()
      if (!factoryId) return

      const factoryClients = await db.getClientsByFactoryId(factoryId)

      // 更新所有客户的统计信息
      for (const client of factoryClients) {
        console.log(`🔄 更新客户 ${client.name} 的统计信息...`)
        if (typeof db.updateClientStatistics === 'function') {
          await db.updateClientStatistics(client.id)
        }
      }

      // 重新加载客户数据
      await loadClients()
      console.log('✅ 所有客户统计信息刷新完成')
    } catch (error) {
      console.error('❌ 刷新客户统计信息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 调试推荐关系数据
  const debugReferralData = () => {
    console.log('🔍 调试推荐关系数据:')
    console.log('📋 所有客户数据:', clients)

    clients.forEach(client => {
      console.log(`\n客户: ${client.name} (${client.id})`)
      console.log(`  推荐人ID: ${client.referrerId || '无'}`)
      console.log(`  推荐人姓名: ${client.referrerName || '无'}`)
      console.log(`  存储的推荐数量: ${client.referralCount || 0}`)

      // 实时计算推荐数量
      const actualReferralCount = clients.filter(c => c.referrerId === client.id).length
      console.log(`  实际推荐数量: ${actualReferralCount}`)

      if (actualReferralCount > 0) {
        const referredClients = clients.filter(c => c.referrerId === client.id)
        console.log(`  推荐的客户:`)
        referredClients.forEach(referred => {
          console.log(`    - ${referred.name} (${referred.phone})`)
        })
      }
    })
  }

  // 同步推荐奖励数据
  const syncReferralRewards = async () => {
    if (!confirm('确定要同步推荐奖励数据吗？这将修复数据库中的奖励字段。')) {
      return
    }

    setLoading(true)
    try {
      console.log('🔄 开始同步推荐奖励数据...')

      const response = await fetch('/api/clients/sync-rewards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const result = await response.json()

      if (result.success) {
        console.log('✅ 推荐奖励数据同步成功:', result.data)
        alert(`同步成功！更新了 ${result.data.updatedCount} 个客户的推荐奖励数据。`)

        // 重新加载客户数据
        await loadClients()
      } else {
        console.error('❌ 同步失败:', result.error)
        alert(`同步失败: ${result.error}`)
      }
    } catch (error) {
      console.error('❌ 同步推荐奖励数据失败:', error)
      alert('同步失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.phone.includes(searchTerm) ||
    (client.company && client.company.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (client.address && client.address.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* 总部公告推送 */}
        <FactoryAnnouncements />

        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">客户管理</h1>
            <p className="text-gray-600">管理工厂的所有客户信息</p>
          </div>
          <AddClientDialog
            onClientAdded={handleClientAdded}
            existingClients={clients}
          />
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索客户名称、电话、公司或地址..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                onClick={refreshAllClientStatistics}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    刷新中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    刷新统计
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">加载客户数据中...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2 text-red-600">
                <AlertCircle className="h-5 w-5" />
                <span>{error}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadClients}
                  className="ml-4"
                >
                  重试
                </Button>
              </div>
            </CardContent>
          </Card>
        )}



        {/* 根据搜索状态动态调整布局 */}
        {!loading && !error && (
          <>
            {/* 当有搜索词时，客户列表显示在上面 */}
            {searchTerm && (
              <div className="mb-8">
                <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                  <Users className="h-6 w-6 mr-2 text-blue-600" />
                  搜索结果 ({filteredClients.length})
                </h2>
                <div className="space-y-4">
                  {filteredClients.map((client) => (
                    <Card key={client.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="bg-blue-100 p-3 rounded-full">
                              <Users className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="text-lg font-semibold text-gray-900">{client.name}</h3>
                                <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(client.status)}`}>
                                  {getStatusText(client.status)}
                                </span>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                                <div className="flex items-center space-x-2">
                                  <Phone className="h-4 w-4" />
                                  <span>{client.phone}</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Mail className="h-4 w-4" />
                                  <span>{client.email || '未填写'}</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Building className="h-4 w-4" />
                                  <span>{client.company || '未填写'}</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <MapPin className="h-4 w-4" />
                                  <span>{client.address}</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-6">
                            {/* Stats - 使用实时计算 */}
                            <div className="text-center">
                              <div className="text-lg font-bold text-blue-600">{getClientStats(client.id).totalOrders}</div>
                              <div className="text-xs text-gray-500">订单</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-bold text-green-600">¥{formatAmount(getClientStats(client.id).totalAmount)}</div>
                              <div className="text-xs text-gray-500">总额</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-bold text-red-600">¥{formatAmount(getClientStats(client.id).unpaidAmount)}</div>
                              <div className="text-xs text-gray-500">欠款</div>
                            </div>

                            {/* Actions */}
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedClient(client)
                                  setShowClientDetail(true)
                                }}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                查看
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedClient(client)
                                  setShowPaymentDialog(true)
                                }}
                              >
                                <DollarSign className="h-4 w-4 mr-1" />
                                付款
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedClient(client)
                                  setShowDeleteDialog(true)
                                }}
                              >
                                <Trash2 className="h-4 w-4 mr-1" />
                                删除
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* 排行榜面板 - 当没有搜索词时显示在上面，有搜索词时显示在下面 */}
            {clients.length > 0 && orders.length > 0 && (
              <div className="mb-8">
                <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                  <Trophy className="h-6 w-6 mr-2 text-yellow-600" />
                  客户排行榜
                </h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {(() => {
                const rankings = getRankingData()

                return (
                  <>
                    {/* 订单数量排行榜 */}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center">
                          <Target className="h-5 w-5 mr-2 text-blue-600" />
                          订单排行榜
                        </CardTitle>
                        <CardDescription>按订单数量排序</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {rankings.orderRanking.length > 0 ? rankings.orderRanking.map((client, index) => (
                            <div key={client.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                                  index === 0 ? 'bg-yellow-500 text-white' :
                                  index === 1 ? 'bg-gray-400 text-white' :
                                  index === 2 ? 'bg-orange-500 text-white' :
                                  'bg-blue-100 text-blue-600'
                                }`}>
                                  {index + 1}
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">{client.name}</p>
                                  <p className="text-sm text-gray-600">{client.phone}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-blue-600">{client.totalOrders}</p>
                                <p className="text-xs text-gray-600">订单</p>
                              </div>
                            </div>
                          )) : (
                            <p className="text-gray-500 text-center py-4">暂无数据</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* 金额排行榜 */}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center">
                          <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                          金额排行榜
                        </CardTitle>
                        <CardDescription>按总金额排序</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {rankings.amountRanking.length > 0 ? rankings.amountRanking.map((client, index) => (
                            <div key={client.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                                  index === 0 ? 'bg-yellow-500 text-white' :
                                  index === 1 ? 'bg-gray-400 text-white' :
                                  index === 2 ? 'bg-orange-500 text-white' :
                                  'bg-green-100 text-green-600'
                                }`}>
                                  {index + 1}
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">{client.name}</p>
                                  <p className="text-sm text-gray-600">{client.phone}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-green-600">¥{formatAmount(client.totalAmount)}</p>
                                <p className="text-xs text-gray-600">总额</p>
                              </div>
                            </div>
                          )) : (
                            <p className="text-gray-500 text-center py-4">暂无数据</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* 欠款排行榜 */}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center">
                          <AlertCircle className="h-5 w-5 mr-2 text-red-600" />
                          欠款排行榜
                        </CardTitle>
                        <CardDescription>按欠款金额排序</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {rankings.debtRanking.length > 0 ? rankings.debtRanking.map((client, index) => (
                            <div key={client.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                                  index === 0 ? 'bg-red-500 text-white' :
                                  index === 1 ? 'bg-orange-500 text-white' :
                                  index === 2 ? 'bg-yellow-500 text-white' :
                                  'bg-red-100 text-red-600'
                                }`}>
                                  {index + 1}
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">{client.name}</p>
                                  <p className="text-sm text-gray-600">{client.phone}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-red-600">¥{formatAmount(client.unpaidAmount)}</p>
                                <p className="text-xs text-gray-600">欠款</p>
                              </div>
                            </div>
                          )) : (
                            <p className="text-gray-500 text-center py-4">暂无欠款</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* 介绍排行榜 */}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center">
                          <UserPlus className="h-5 w-5 mr-2 text-purple-600" />
                          介绍排行榜
                        </CardTitle>
                        <CardDescription>按推荐客户数量排序</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {rankings.referralRanking.length > 0 ? rankings.referralRanking.map((client, index) => (
                            <div key={client.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                                  index === 0 ? 'bg-purple-500 text-white' :
                                  index === 1 ? 'bg-indigo-500 text-white' :
                                  index === 2 ? 'bg-blue-500 text-white' :
                                  'bg-purple-100 text-purple-600'
                                }`}>
                                  {index + 1}
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">{client.name}</p>
                                  <p className="text-sm text-gray-600">{client.phone}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-purple-600">{client.referralCount || 0}</p>
                                <p className="text-xs text-gray-600">推荐</p>
                              </div>
                            </div>
                          )) : (
                            <p className="text-gray-500 text-center py-4">暂无推荐记录</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* 最近活跃客户 */}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center">
                          <Clock className="h-5 w-5 mr-2 text-indigo-600" />
                          最近活跃
                        </CardTitle>
                        <CardDescription>按最近订单时间排序</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {rankings.recentActiveClients.length > 0 ? rankings.recentActiveClients.map((client, index) => (
                            <div key={client.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className="w-6 h-6 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center text-xs font-bold">
                                  {index + 1}
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">{client.name}</p>
                                  <p className="text-sm text-gray-600">{client.phone}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-indigo-600">
                                  {new Date(client.lastOrderDate).toLocaleDateString()}
                                </p>
                                <p className="text-xs text-gray-600">最近订单</p>
                              </div>
                            </div>
                          )) : (
                            <p className="text-gray-500 text-center py-4">暂无活跃记录</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </>
                )
              })()}
            </div>
          </div>
        )}

            {/* 当没有搜索词时，显示所有客户列表 */}
            {!searchTerm && (
              <div className="space-y-4">
                <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                  <Users className="h-6 w-6 mr-2 text-blue-600" />
                  所有客户 ({filteredClients.length})
                </h2>
                {filteredClients.map((client) => (
            <Card key={client.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-100 p-3 rounded-full">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{client.name}</h3>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(client.status)}`}>
                          {getStatusText(client.status)}
                        </span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4" />
                          <span>{client.phone}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Mail className="h-4 w-4" />
                          <span>{client.email || '未填写'}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Building className="h-4 w-4" />
                          <span>{client.company || '未填写'}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4" />
                          <span>{client.address}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-6">
                    {/* Stats - 使用实时计算 */}
                    {(() => {
                      const stats = getClientStats(client.id)
                      return (
                        <>
                          <div className="text-center">
                            <p className="text-lg font-bold text-blue-600">{stats.totalOrders}</p>
                            <p className="text-xs text-gray-600">订单数</p>
                          </div>
                          <div className="text-center">
                            <p className="text-lg font-bold text-green-600">¥{formatAmount(stats.totalAmount)}</p>
                            <p className="text-xs text-gray-600">总金额</p>
                          </div>
                          <div className="text-center">
                            <p className="text-lg font-bold text-purple-600">
                              {rewardsLoading ? (
                                <span className="text-sm">加载中...</span>
                              ) : (
                                `¥${formatAmount(clientRewards[client.id]?.totalReward || 0)}`
                              )}
                            </p>
                            <p className="text-xs text-gray-600">推荐奖励</p>
                          </div>
                        </>
                      )
                    })()}
                    <div className="text-center">
                      <p className="text-sm font-medium">
                        {client.createdAt ? new Date(client.createdAt).toLocaleDateString() : '未知'}
                      </p>
                      <p className="text-xs text-gray-600">创建时间</p>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewClient(client)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        查看详情
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleManagePayment(client)}
                      >
                        <DollarSign className="h-4 w-4 mr-1" />
                        付款管理
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteClient(client)}
                        className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        删除
                      </Button>
                      <Button size="sm" variant="ghost">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
                ))}
              </div>
            )}
          </>
        )}

        {!loading && !error && filteredClients.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到客户</h3>
            <p className="text-gray-600">尝试调整搜索条件或添加新的客户</p>
          </div>
        )}

        {/* 客户详情对话框 */}
        <ClientDetailDialog
          client={selectedClient}
          open={showClientDetail}
          onOpenChange={setShowClientDetail}
        />

        {/* 客户付款管理对话框 */}
        <ClientPaymentDialog
          client={selectedClient}
          open={showPaymentDialog}
          onOpenChange={setShowPaymentDialog}
          onPaymentUpdated={handlePaymentUpdated}
        />

        {/* 删除客户对话框 */}
        <DeleteClientDialog
          client={selectedClient}
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          onClientDeleted={handleClientDeleted}
        />
      </div>
    </DashboardLayout>
  )
}
