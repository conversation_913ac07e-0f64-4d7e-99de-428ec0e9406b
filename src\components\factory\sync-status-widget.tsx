"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { dataSyncService, type SyncStatus } from "@/lib/services/data-sync"
import { getCurrentFactoryId } from "@/lib/middleware/data-isolation"
import {
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  Database,
  Wifi,
  WifiOff
} from "lucide-react"

export function SyncStatusWidget() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(dataSyncService.getSyncStatus())
  const [lastSyncData, setLastSyncData] = useState<unknown>(null)
  const [loading, setLoading] = useState(false)

  // 监听同步状态变化
  useEffect(() => {
    const handleSyncStatusChange = (status: SyncStatus) => {
      setSyncStatus(status)
    }

    dataSyncService.addSyncListener(handleSyncStatusChange)
    
    return () => {
      dataSyncService.removeSyncListener(handleSyncStatusChange)
    }
  }, [])

  // 获取同步状态图标
  const getSyncStatusIcon = () => {
    if (syncStatus.syncInProgress) {
      return <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
    }
    if (syncStatus.errorCount > 0) {
      return <AlertCircle className="h-4 w-4 text-red-600" />
    }
    return <CheckCircle className="h-4 w-4 text-green-600" />
  }

  // 获取同步状态文本
  const getSyncStatusText = () => {
    if (syncStatus.syncInProgress) return "同步中..."
    if (syncStatus.errorCount > 0) return "同步异常"
    return "同步正常"
  }

  // 获取同步状态颜色
  const getSyncStatusColor = () => {
    if (syncStatus.syncInProgress) return "bg-blue-100 text-blue-800"
    if (syncStatus.errorCount > 0) return "bg-red-100 text-red-800"
    return "bg-green-100 text-green-800"
  }

  // 手动触发同步
  const handleManualSync = async () => {
    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()
      
      if (!factoryId) {
        console.error('无法获取工厂ID')
        return
      }

      // 模拟同步操作
      await dataSyncService.syncFactoryDataToHeadquarters(
        factoryId,
        'factory_updated',
        { timestamp: new Date(), manual: true }
      )

      console.log('✅ 手动同步完成')
    } catch (error) {
      console.error('❌ 手动同步失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Database className="h-5 w-5 text-gray-600" />
            <CardTitle className="text-lg">数据同步状态</CardTitle>
          </div>
          <Badge className={getSyncStatusColor()}>
            {getSyncStatusIcon()}
            <span className="ml-2">{getSyncStatusText()}</span>
          </Badge>
        </div>
        <CardDescription>
          与总部的数据同步状态和连接信息
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 连接状态 */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            {syncStatus.isOnline ? (
              <Wifi className="h-5 w-5 text-green-600" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-600" />
            )}
            <div>
              <p className="font-medium text-sm">连接状态</p>
              <p className="text-xs text-gray-600">
                {syncStatus.isOnline ? "已连接到总部" : "连接断开"}
              </p>
            </div>
          </div>
          <Badge variant={syncStatus.isOnline ? "default" : "destructive"}>
            {syncStatus.isOnline ? "在线" : "离线"}
          </Badge>
        </div>

        {/* 最后同步时间 */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <Clock className="h-5 w-5 text-blue-600" />
            <div>
              <p className="font-medium text-sm">最后同步</p>
              <p className="text-xs text-gray-600">
                {syncStatus.lastSyncTime 
                  ? new Date(syncStatus.lastSyncTime).toLocaleString()
                  : "尚未同步"
                }
              </p>
            </div>
          </div>
        </div>

        {/* 错误信息 */}
        {syncStatus.errorCount > 0 && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <p className="font-medium text-sm text-red-800">同步错误</p>
            </div>
            <p className="text-xs text-red-700">
              错误次数: {syncStatus.errorCount}
            </p>
            {syncStatus.lastError && (
              <p className="text-xs text-red-700 mt-1">
                最后错误: {syncStatus.lastError}
              </p>
            )}
          </div>
        )}

        {/* 同步操作 */}
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleManualSync}
            disabled={loading || syncStatus.syncInProgress}
            className="flex-1"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            手动同步
          </Button>
        </div>

        {/* 同步说明 */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• 数据会自动与总部同步</p>
          <p>• 新增客户和订单会立即同步</p>
          <p>• 如遇问题可手动触发同步</p>
        </div>
      </CardContent>
    </Card>
  )
}
