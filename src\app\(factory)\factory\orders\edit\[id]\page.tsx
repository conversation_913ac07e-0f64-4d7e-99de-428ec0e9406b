"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button  } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from "@/lib/database"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import { smartDimensionRecognition } from "@/lib/utils/dimension-utils"
import type { Order } from "@/types"
import {
  ArrowLeft,
  Save,
  X,
  Plus,
  Trash2
} from "lucide-react"

export default function EditOrderPage() {
  const params = useParams()
  const router = useRouter()
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    clientName: '',
    clientPhone: '',
    status: 'pending',
    notes: '',
    items: [] as unknown[]
  })

  useEffect(() => {
    loadOrderForEdit()
  }, [params.id])

  const loadOrderForEdit = async () => {
    try {
      setLoading(true)
      console.log('🔄 开始加载订单编辑数据...', params.id)

      const factoryId = getCurrentFactoryId()
      
      if (factoryId && db && typeof db.getOrdersByFactoryId === 'function') {
        const orders = await db.getOrdersByFactoryId(factoryId)
        const foundOrder = orders.find(o => o.id === params.id)
        
        if (foundOrder) {
          setOrder(foundOrder)
          setFormData({
            clientName: foundOrder.clientName || '',
            clientPhone: foundOrder.clientPhone || '',
            status: foundOrder.status,
            notes: foundOrder.notes || '',
            items: foundOrder.items.map(item => ({
              ...item,
              id: item.id || Math.random().toString(36).substr(2, 9)
            }))
          })
        }
      }
    } catch (error) {
      console.error('❌ 加载订单编辑数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveOrder = async () => {
    try {
      setSaving(true)
      console.log('💾 开始保存订单修改...')

      // 计算总金额
      const totalAmount = formData.items.reduce((sum, item) => sum + (item.totalPrice || 0), 0)

      const updatedOrder = {
        ...order,
        clientName: formData.clientName,
        clientPhone: formData.clientPhone,
        status: formData.status,
        notes: formData.notes,
        items: formData.items,
        totalAmount,
        updatedAt: new Date()
      }

      // 这里应该调用数据库更新方法
      // await db.updateOrder(updatedOrder)

      console.log('✅ 订单修改保存成功')
      alert('订单修改保存成功！')
      router.push(`/factory/orders/${params.id}`)
    } catch (error) {
      console.error('❌ 保存订单修改失败:', error)
      alert('保存失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleAddItem = () => {
    const newItem = {
      id: Math.random().toString(36).substr(2, 9),
      productName: '双层白色出风口',
      productType: 'double_white_outlet',
      specifications: '',
      dimensions: smartDimensionRecognition(0, 0),
      floor: '',
      quantity: 1,
      unitPrice: 150,
      totalPrice: 0,
      notes: ''
    }
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }))
  }

  const handleRemoveItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  const handleItemChange = (index: number, field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  if (loading) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载订单编辑页面中...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!order) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">订单不存在</h3>
            <p className="text-gray-600 mb-4">未找到指定的订单信息</p>
            <Link href="/factory/orders">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回订单列表
              </Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href={`/factory/orders/${params.id}`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回详情
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">编辑订单</h1>
              <p className="text-gray-600">修改订单信息和项目详情</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Link href={`/factory/orders/${params.id}`}>
              <Button variant="outline">
                <X className="h-4 w-4 mr-2" />
                取消
              </Button>
            </Link>
            <Button 
              onClick={handleSaveOrder}
              disabled={saving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              {saving ? '保存中...' : '保存修改'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要编辑区域 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 订单基本信息编辑 */}
            <Card>
              <CardHeader>
                <CardTitle>订单基本信息</CardTitle>
                <CardDescription>编辑订单的基本信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-600 mb-2 block">客户名称</label>
                    <Input
                      value={formData.clientName}
                      onChange={(e) => setFormData(prev => ({ ...prev, clientName: e.target.value }))}
                      placeholder="请输入客户名称"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 mb-2 block">客户电话</label>
                    <Input
                      value={formData.clientPhone}
                      onChange={(e) => setFormData(prev => ({ ...prev, clientPhone: e.target.value }))}
                      placeholder="请输入客户电话"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 mb-2 block">订单状态</label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="pending">待确认</option>
                      <option value="production">生产中</option>
                      <option value="completed">已完成</option>
                      <option value="cancelled">已取消</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-gray-600 mb-2 block">订单备注</label>
                    <Textarea
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="请输入订单备注"
                      rows={3}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 订单项目编辑 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>订单项目</CardTitle>
                    <CardDescription>编辑订单中的产品项目</CardDescription>
                  </div>
                  <Button onClick={handleAddItem} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    添加项目
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {formData.items.map((item, index) => (
                    <div key={item.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium text-gray-900">项目 {index + 1}</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveItem(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600 mb-1 block">产品名称</label>
                          <Input
                            value={item.productName}
                            onChange={(e) => handleItemChange(index, 'productName', e.target.value)}
                            placeholder="产品名称"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600 mb-1 block">规格</label>
                          <Input
                            value={item.specifications}
                            onChange={(e) => handleItemChange(index, 'specifications', e.target.value)}
                            placeholder="规格"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600 mb-1 block">楼层</label>
                          <Input
                            value={item.floor}
                            onChange={(e) => handleItemChange(index, 'floor', e.target.value)}
                            placeholder="楼层"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600 mb-1 block">数量</label>
                          <Input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 0)}
                            placeholder="数量"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600 mb-1 block">单价</label>
                          <Input
                            type="number"
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                            placeholder="单价"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600 mb-1 block">小计</label>
                          <Input
                            type="number"
                            value={item.totalPrice}
                            onChange={(e) => handleItemChange(index, 'totalPrice', parseFloat(e.target.value) || 0)}
                            placeholder="小计"
                          />
                        </div>
                        <div className="md:col-span-3">
                          <label className="text-sm font-medium text-gray-600 mb-1 block">备注</label>
                          <Input
                            value={item.notes}
                            onChange={(e) => handleItemChange(index, 'notes', e.target.value)}
                            placeholder="项目备注"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {formData.items.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <p>暂无订单项目</p>
                      <Button onClick={handleAddItem} className="mt-4">
                        <Plus className="h-4 w-4 mr-2" />
                        添加第一个项目
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 - 订单摘要 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>订单摘要</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">订单号:</span>
                    <span className="font-medium">{order.orderNumber || order.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">项目数量:</span>
                    <span className="font-medium">{formData.items.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">总数量:</span>
                    <span className="font-medium">
                      {formData.items.reduce((sum, item) => sum + (item.quantity || 0), 0)}
                    </span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between">
                      <span className="font-medium">预计总额:</span>
                      <span className="font-bold text-lg text-green-600">
                        ¥{formData.items.reduce((sum, item) => sum + (item.totalPrice || 0), 0).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
