/**
 * 简单的豆包AI密钥测试脚本
 */

const API_KEY = '20dc1a0c-8219-4280-9441-901efe0d7637'
const BASE_URL = 'https://ark.cn-beijing.volces.com/api/v3'

async function testDoubaoAPI() {
  console.log('🚀 开始测试豆包AI API...')
  console.log('🔑 API Key:', API_KEY.substring(0, 20) + '...')
  console.log('🌐 Base URL:', BASE_URL)
  
  const requestBody = {
    model: 'doubao-seed-1-6-250615',
    messages: [
      { role: 'user', content: '你好，请简单回复' }
    ],
    max_tokens: 20,
    temperature: 0.1
  }
  
  try {
    console.log('📤 发送请求...')
    const response = await fetch(`${BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify(requestBody)
    })
    
    console.log('📥 响应状态:', response.status)
    console.log('📥 响应头:', Object.fromEntries(response.headers.entries()))
    
    const responseText = await response.text()
    console.log('📥 响应内容:', responseText)
    
    if (response.ok) {
      console.log('✅ API调用成功！')
      const data = JSON.parse(responseText)
      console.log('💬 AI回复:', data.choices?.[0]?.message?.content)
    } else {
      console.log('❌ API调用失败')
      
      // 尝试解析错误信息
      try {
        const errorData = JSON.parse(responseText)
        console.log('🔍 错误详情:', errorData)
      } catch (e) {
        console.log('🔍 原始错误:', responseText)
      }
    }
    
  } catch (error) {
    console.error('💥 请求异常:', error.message)
  }
}

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
  testDoubaoAPI()
}

// 如果在浏览器中运行
if (typeof window !== 'undefined') {
  window.testDoubaoAPI = testDoubaoAPI
  console.log('在浏览器控制台中运行: testDoubaoAPI()')
}
