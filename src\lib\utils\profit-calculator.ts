/**
 * 🇨🇳 风口云平台 - 股东利润分配计算器
 */

import { Shareholder } from '@/types'

// 股东利润分配结果接口
export interface ShareholderProfitAllocation {
  id: string
  name: string
  shareholderType: string
  sharePercentage: number
  investmentAmount: number
  profitShare: number
  profitPercentage: number
  roi: number // 投资回报率
}

// 利润分配汇总信息
export interface ProfitAllocationSummary {
  totalProfit: number
  totalAllocated: number
  totalShareholders: number
  activeShareholders: number
  averageROI: number
  topPerformer: ShareholderProfitAllocation | null
  allocationDate: Date
}

// 利润分配详细结果
export interface ProfitAllocationResult {
  allocations: ShareholderProfitAllocation[]
  summary: ProfitAllocationSummary
  period: {
    type: 'monthly' | 'quarterly' | 'yearly'
    year: number
    month?: number
    quarter?: number
  }
}

/**
 * 计算股东利润分配
 * @param totalProfit 总利润
 * @param shareholders 股东列表
 * @param period 期间信息
 * @returns 利润分配结果
 */
export function calculateShareholderProfitAllocation(
  totalProfit: number,
  shareholders: Shareholder[],
  period: {
    type: 'monthly' | 'quarterly' | 'yearly'
    year: number
    month?: number
    quarter?: number
  }
): ProfitAllocationResult {
  // 过滤活跃股东
  const activeShareholders = shareholders.filter(s => s.status === 'active')
  
  // 计算每个股东的利润分配
  const allocations: ShareholderProfitAllocation[] = activeShareholders.map(shareholder => {
    const sharePercentage = parseFloat(shareholder.sharePercentage.toString())
    const investmentAmount = parseFloat(shareholder.investmentAmount.toString())
    const profitShare = (totalProfit * sharePercentage) / 100
    const roi = investmentAmount > 0 ? (profitShare / investmentAmount) * 100 : 0

    return {
      id: shareholder.id,
      name: shareholder.name,
      shareholderType: shareholder.shareholderType,
      sharePercentage,
      investmentAmount,
      profitShare,
      profitPercentage: sharePercentage,
      roi
    }
  }).sort((a, b) => b.profitShare - a.profitShare) // 按利润分配金额降序排列

  // 计算汇总信息
  const totalAllocated = allocations.reduce((sum, allocation) => sum + allocation.profitShare, 0)
  const averageROI = allocations.length > 0 
    ? allocations.reduce((sum, allocation) => sum + allocation.roi, 0) / allocations.length 
    : 0
  const topPerformer = allocations.length > 0 ? allocations[0] : null

  const summary: ProfitAllocationSummary = {
    totalProfit,
    totalAllocated,
    totalShareholders: shareholders.length,
    activeShareholders: activeShareholders.length,
    averageROI,
    topPerformer,
    allocationDate: new Date()
  }

  return {
    allocations,
    summary,
    period
  }
}

/**
 * 按股东类型分组利润分配
 * @param allocations 利润分配列表
 * @returns 按类型分组的结果
 */
export function groupAllocationsByType(allocations: ShareholderProfitAllocation[]): Record<string, {
  shareholders: ShareholderProfitAllocation[]
  totalProfit: number
  totalPercentage: number
  count: number
}> {
  const groups: Record<string, {
    shareholders: ShareholderProfitAllocation[]
    totalProfit: number
    totalPercentage: number
    count: number
  }> = {}

  allocations.forEach(allocation => {
    const type = allocation.shareholderType
    if (!groups[type]) {
      groups[type] = {
        shareholders: [],
        totalProfit: 0,
        totalPercentage: 0,
        count: 0
      }
    }

    groups[type].shareholders.push(allocation)
    groups[type].totalProfit += allocation.profitShare
    groups[type].totalPercentage += allocation.sharePercentage
    groups[type].count += 1
  })

  return groups
}

/**
 * 计算利润分配趋势
 * @param currentAllocation 当前期间分配
 * @param previousAllocation 上期分配
 * @returns 趋势分析结果
 */
export function calculateProfitTrend(
  currentAllocation: ProfitAllocationResult,
  previousAllocation?: ProfitAllocationResult
): {
  profitGrowth: number
  profitGrowthPercentage: number
  roiChange: number
  shareholderChanges: {
    added: number
    removed: number
    unchanged: number
  }
} {
  if (!previousAllocation) {
    return {
      profitGrowth: 0,
      profitGrowthPercentage: 0,
      roiChange: 0,
      shareholderChanges: {
        added: currentAllocation.summary.activeShareholders,
        removed: 0,
        unchanged: 0
      }
    }
  }

  const profitGrowth = currentAllocation.summary.totalProfit - previousAllocation.summary.totalProfit
  const profitGrowthPercentage = previousAllocation.summary.totalProfit > 0 
    ? (profitGrowth / previousAllocation.summary.totalProfit) * 100 
    : 0
  const roiChange = currentAllocation.summary.averageROI - previousAllocation.summary.averageROI

  // 计算股东变化
  const currentIds = new Set(currentAllocation.allocations.map(a => a.id))
  const previousIds = new Set(previousAllocation.allocations.map(a => a.id))
  
  const added = currentAllocation.allocations.filter(a => !previousIds.has(a.id)).length
  const removed = previousAllocation.allocations.filter(a => !currentIds.has(a.id)).length
  const unchanged = currentAllocation.allocations.filter(a => previousIds.has(a.id)).length

  return {
    profitGrowth,
    profitGrowthPercentage,
    roiChange,
    shareholderChanges: {
      added,
      removed,
      unchanged
    }
  }
}

/**
 * 获取股东类型显示文本
 * @param type 股东类型
 * @returns 显示文本
 */
export function getShareholderTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    'FOUNDER': '创始股东',
    'INVESTOR': '投资股东',
    'EMPLOYEE': '员工股东',
    'PARTNER': '合伙人股东'
  }
  return typeMap[type] || type
}

/**
 * 格式化金额显示 - 默认使用一位小数
 * @param amount 金额
 * @param precision 精度，默认1位小数
 * @returns 格式化后的金额字符串
 */
export function formatAmount(amount: number, precision: number = 1): string {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  })
}

/**
 * 格式化百分比显示
 * @param percentage 百分比
 * @param precision 精度
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(percentage: number, precision: number = 2): string {
  return `${percentage.toFixed(precision)}%`
}

/**
 * 验证利润分配数据
 * @param totalProfit 总利润
 * @param shareholders 股东列表
 * @returns 验证结果
 */
export function validateProfitAllocation(
  totalProfit: number,
  shareholders: Shareholder[]
): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  // 检查总利润
  if (totalProfit < 0) {
    warnings.push('总利润为负数，将按负数进行分配')
  }

  // 检查股东列表
  if (shareholders.length === 0) {
    errors.push('没有股东信息')
    return { isValid: false, errors, warnings }
  }

  const activeShareholders = shareholders.filter(s => s.status === 'active')
  if (activeShareholders.length === 0) {
    errors.push('没有活跃股东')
    return { isValid: false, errors, warnings }
  }

  // 检查股权比例总和
  const totalPercentage = activeShareholders.reduce(
    (sum, s) => sum + parseFloat(s.sharePercentage.toString()), 
    0
  )

  if (Math.abs(totalPercentage - 100) > 0.01) {
    warnings.push(`活跃股东股权比例总和为 ${totalPercentage.toFixed(2)}%，不等于100%`)
  }

  // 检查投资金额
  const zeroInvestmentShareholders = activeShareholders.filter(
    s => parseFloat(s.investmentAmount.toString()) <= 0
  )
  if (zeroInvestmentShareholders.length > 0) {
    warnings.push(`${zeroInvestmentShareholders.length} 个股东的投资金额为0或负数，ROI计算可能不准确`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}
