/**
 * 🇨🇳 风口云平台 - 轮播图片API
 * 
 * 功能说明：
 * - 处理轮播图片的上传、获取、删除等操作
 * - 支持数据库持久化存储
 * - 自动图片压缩和格式验证
 */

import { NextRequest, NextResponse } from 'next/server'
import { imageDatabase } from '@/lib/image-database'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 获取轮播图片列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') as 'product' | 'process' | 'other' || 'product'

    console.log('📷 获取轮播图片:', category)

    const images = await imageDatabase.getImages(category)
    
    console.log(`✅ 获取到 ${images.length} 张图片`)

    return NextResponse.json({
      success: true,
      images,
      count: images.length
    })

  } catch (error) {
    console.error('❌ 获取轮播图片失败:', error)
    return NextResponse.json(
      { error: `获取图片失败: ${error.message || '未知错误'}` },
      { status: 500 }
    )
  }
}

// 上传轮播图片
export async function POST(request: NextRequest) {
  try {
    console.log('📤 接收到图片上传请求')

    const requestData = await request.json()

    // 支持两种格式：单个图片和批量图片
    let imagesToProcess = []
    let category = 'product'

    if (requestData.images && Array.isArray(requestData.images)) {
      // 批量上传格式
      imagesToProcess = requestData.images
      category = requestData.category || 'product'
    } else if (requestData.name && requestData.imageData) {
      // 单个图片上传格式
      imagesToProcess = [requestData]
      category = requestData.category || 'product'
    } else {
      return NextResponse.json(
        { error: '没有选择图片或格式不正确' },
        { status: 400 }
      )
    }

    console.log(`📁 处理 ${imagesToProcess.length} 张图片，分类: ${category}`)

    const uploadedImages: unknown[] = []
    const errors: unknown[] = []

    for (const imageData of imagesToProcess) {
      try {
        console.log(`📷 保存图片: ${imageData.name} (${imageData.size} bytes)`)

        // 从imageData中提取MIME类型
        const mimeType = imageData.imageData?.match(/data:([^;]+);/)?.[1] || 'image/jpeg'

        // 直接保存到数据库（图片已在客户端处理）
        const savedImage = await prisma.carouselImage.create({
          data: {
            name: imageData.name,
            category: category,
            imageData: imageData.imageData || imageData.base64Data,
            mimeType: mimeType,
            size: imageData.size,
            width: imageData.width || null,
            height: (imageData as unknown).height || null,
            isActive: true
          }
        })

        const uploadedImage = {
          id: savedImage.id,
          name: savedImage.name,
          category: savedImage.category as 'product' | 'process' | 'other',
          imageData: savedImage.imageData,
          mimeType: savedImage.mimeType,
          size: savedImage.size,
          width: savedImage.width || undefined,
          height: savedImage.height || undefined,
          uploadDate: savedImage.createdAt,
          isActive: savedImage.isActive
        }

        uploadedImages.push(uploadedImage)
        console.log(`✅ 图片保存成功: ${uploadedImage.name}`)

      } catch (error) {
        console.error(`❌ 保存图片 ${(imageData as unknown).name} 失败:`, error)
        errors.push(`图片 ${(imageData as unknown).name} 保存失败: ${(error as Error).message}`)
      }
    }

    const response = {
      success: uploadedImages.length > 0,
      uploaded: uploadedImages.length,
      total: imagesToProcess.length,
      images: uploadedImages,
      errors: errors.length > 0 ? errors : undefined
    }

    console.log(`🎉 上传完成: ${uploadedImages.length}/${imagesToProcess.length} 成功`)

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ 图片上传失败:', error)
    return NextResponse.json(
      { error: `上传失败: ${(error as Error).message || '未知错误'}` },
      { status: 500 }
    )
  }
}

// 删除轮播图片
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const imageId = searchParams.get('id')

    if (!imageId) {
      return NextResponse.json(
        { error: '缺少图片ID' },
        { status: 400 }
      )
    }

    console.log('🗑️ 删除图片:', imageId)

    const success = await imageDatabase.deleteImage(imageId)

    if (success) {
      console.log('✅ 图片删除成功')
      return NextResponse.json({ success: true })
    } else {
      console.log('❌ 图片删除失败')
      return NextResponse.json(
        { error: '删除失败' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ 删除图片失败:', error)
    return NextResponse.json(
      { error: `删除失败: ${(error as Error).message || '未知错误'}` },
      { status: 500 }
    )
  }
}

// 获取存储统计信息
export async function PATCH(request: NextRequest) {
  try {
    console.log('📊 获取存储统计信息')

    const stats = await imageDatabase.getStorageStats()
    
    console.log('✅ 统计信息获取成功')

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error('❌ 获取统计信息失败:', error)
    return NextResponse.json(
      { error: `获取统计失败: ${(error as Error).message || '未知错误'}` },
      { status: 500 }
    )
  }
}
