"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle  } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DollarSign, Loader2, Calendar, CheckCircle } from "lucide-react"
import { db } from "@/lib/database/client"
import { safeAmount } from "@/lib/utils/number-utils"
import type { Order, PaymentStatus } from "@/types"

// 安全的数字转换函数，避免在客户端导入Prisma
function safeToNumber(value: unknown): number {
  if (value === null || value === undefined) return 0
  if (typeof value === 'number') return value
  return Number(value.toString())
}

interface PaymentStatusDialogProps {
  order: Order | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onPaymentUpdated?: (updatedOrder: Order) => void
}

export function PaymentStatusDialog({ order, open, onOpenChange, onPaymentUpdated }: PaymentStatusDialogProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    paymentStatus: order?.paymentStatus || 'unpaid',
    paidAmount: safeToNumber(order?.paidAmount) || 0,
    dueDate: order?.dueDate ? new Date(order.dueDate).toISOString().split('T')[0] : '',
    paymentNotes: order?.paymentNotes || ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [successMessage, setSuccessMessage] = useState("")

  // 当订单变化时更新表单数据
  useState(() => {
    if (order) {
      setFormData({
        paymentStatus: order.paymentStatus || 'unpaid',
        paidAmount: safeToNumber(order.paidAmount) || 0,
        dueDate: order.dueDate ? new Date(order.dueDate).toISOString().split('T')[0] : '',
        paymentNotes: order.paymentNotes || ''
      })
    }
  })

  // 验证表单数据
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (formData.paidAmount < 0) {
      newErrors.paidAmount = "已付金额不能为负数"
    }

    if (order && formData.paidAmount > safeToNumber(order.totalAmount)) {
      newErrors.paidAmount = "已付金额不能超过订单总金额"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 自动计算付款状态 - 🔧 修复：使用精度处理避免浮点数比较问题
  const calculatePaymentStatus = (paidAmount: number, totalAmount: number): PaymentStatus => {
    if (paidAmount <= 0) return 'unpaid'
    // 使用精度处理避免浮点数比较问题
    if (Math.abs(paidAmount - totalAmount) < 0.01 || paidAmount >= totalAmount) return 'paid'
    return 'partial'
  }

  // 处理已付金额变化
  const handlePaidAmountChange = (value: string) => {
    const paidAmount = parseFloat(value) || 0
    const newPaymentStatus = order ? calculatePaymentStatus(paidAmount, safeToNumber(order.totalAmount)) : 'unpaid'

    setFormData(prev => ({
      ...prev,
      paidAmount,
      paymentStatus: newPaymentStatus
    }))
  }

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!order || !validateForm()) {
      return
    }

    try {
      setLoading(true)

      // 构建更新的订单数据
      const updatedOrder: Order = {
        ...order,
        paymentStatus: formData.paymentStatus as PaymentStatus,
        paidAmount: formData.paidAmount,
        dueDate: formData.dueDate ? new Date(formData.dueDate) : undefined,
        paymentNotes: formData.paymentNotes,
        updatedAt: new Date()
      }

      // 更新订单付款信息
      const success = await db.updateOrderPayment(order.id, {
        paymentStatus: updatedOrder.paymentStatus,
        paidAmount: Number(updatedOrder.paidAmount),
        dueDate: updatedOrder.dueDate,
        paymentNotes: updatedOrder.paymentNotes
      })

      if (success) {
        console.log('✅ 订单付款状态更新成功')

        // 显示成功消息
        setSuccessMessage("付款状态更新成功！")

        // 同步更新客户统计信息
        const statsUpdateSuccess = await db.updateClientStatistics(order.clientId)
        if (!statsUpdateSuccess) {
          console.warn('⚠️ 客户统计信息同步失败，但订单更新成功')
          setSuccessMessage("付款状态更新成功！（注意：客户统计可能需要手动刷新）")
        }

        // 通知父组件
        if (onPaymentUpdated) {
          onPaymentUpdated(updatedOrder)
        }

        // 延迟关闭对话框，让用户看到成功消息
        setTimeout(() => {
          onOpenChange(false)
          setSuccessMessage("")
        }, 1500)
      } else {
        setErrors({ general: "更新失败，请重试" })
      }
    } catch (error) {
      console.error('更新订单付款状态失败:', error)
      setErrors({ general: `更新失败: ${(error as Error).message}` })
    } finally {
      setLoading(false)
    }
  }

  if (!order) return null

  // 获取付款状态文本
  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'paid': return '已付清'
      case 'partial': return '部分付款'
      case 'unpaid': return '未付款'
      case 'overdue': return '逾期'
      default: return '未知'
    }
  }

  // 获取付款状态颜色
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800 border-green-200'
      case 'partial': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'unpaid': return 'bg-red-100 text-red-800 border-red-200'
      case 'overdue': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const unpaidAmount = safeToNumber(order.totalAmount) - formData.paidAmount

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>订单付款管理</span>
          </DialogTitle>
          <DialogDescription>
            管理订单 {order.orderNumber || order.id} 的付款状态
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto space-y-3 pr-2">
          {/* 成功提示 */}
          {successMessage && (
            <div className="p-2 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md flex items-center space-x-2">
              <CheckCircle className="h-4 w-4" />
              <span>{successMessage}</span>
            </div>
          )}

          {/* 错误提示 */}
          {errors.general && (
            <div className="p-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {errors.general}
            </div>
          )}

          {/* 订单金额信息 */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">订单金额概览</h4>
            <div className="grid grid-cols-1 gap-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">订单总金额：</span>
                <span className="text-base font-bold text-gray-900">{safeAmount(order.totalAmount)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">已付金额：</span>
                <span className="text-base font-semibold text-green-600">{safeAmount(formData.paidAmount)}</span>
              </div>
              <div className="flex justify-between items-center border-t pt-1">
                <span className="text-sm font-medium text-gray-700">剩余未付：</span>
                <span className={`text-base font-bold ${unpaidAmount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {safeAmount(unpaidAmount)}
                </span>
              </div>
            </div>
          </div>

          {/* 付款金额设置 */}
          <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">付款金额设置</h4>
            <div className="space-y-2">
              <div>
                <Label htmlFor="paidAmount" className="text-sm font-medium">已付金额 *</Label>
                <div className="mt-1">
                  <Input
                    id="paidAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    max={safeToNumber(order.totalAmount)}
                    value={formData.paidAmount}
                    onChange={(e) => handlePaidAmountChange(e.target.value)}
                    placeholder="请输入已付金额"
                    className={`text-base font-medium ${errors.paidAmount ? "border-red-500" : ""}`}
                  />
                  {errors.paidAmount && <p className="text-sm text-red-600 mt-1">{errors.paidAmount}</p>}
                </div>
              </div>

              {/* 快捷金额按钮 */}
              <div className="flex flex-wrap gap-1">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handlePaidAmountChange("0")}
                  className="text-xs px-2 py-1"
                >
                  清零
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handlePaidAmountChange((safeToNumber(order.totalAmount) / 2).toString())}
                  className="text-xs px-2 py-1"
                >
                  付一半
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handlePaidAmountChange(safeToNumber(order.totalAmount).toString())}
                  className="text-xs px-2 py-1"
                >
                  付清全款
                </Button>
              </div>
            </div>
          </div>

          {/* 付款状态确认 */}
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="text-sm font-medium text-yellow-900 mb-2">付款状态确认</h4>
            <div className="space-y-2">
              <div>
                <Label className="text-sm font-medium">系统自动判断：</Label>
                <div className="mt-1 p-2 bg-white border rounded-md">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPaymentStatusColor(calculatePaymentStatus(formData.paidAmount, safeToNumber(order.totalAmount)))}`}>
                    {getPaymentStatusText(calculatePaymentStatus(formData.paidAmount, safeToNumber(order.totalAmount)))}
                  </span>
                </div>
              </div>

              <div>
                <Label htmlFor="paymentStatus" className="text-sm font-medium">手动调整（特殊情况）：</Label>
                <div className="mt-1">
                  <Select
                    value={formData.paymentStatus}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, paymentStatus: value as PaymentStatus }))}
                  >
                    <SelectTrigger className="w-full h-8">
                      <SelectValue placeholder="选择付款状态" />
                    </SelectTrigger>
                    <SelectContent className="!bg-white border border-gray-300 shadow-xl z-[100] backdrop-blur-none opacity-100">
                      <SelectItem value="unpaid" className="!bg-white hover:!bg-gray-100 opacity-100">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                          <span>未付款</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="partial" className="!bg-white hover:!bg-gray-100 opacity-100">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                          <span>部分付款</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="paid" className="!bg-white hover:!bg-gray-100 opacity-100">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span>已付清</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="overdue" className="!bg-white hover:!bg-gray-100 opacity-100">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                          <span>逾期</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>

          {/* 其他设置 */}
          <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">其他设置</h4>
            <div className="space-y-3">
              {/* 应付日期 */}
              <div>
                <Label htmlFor="dueDate" className="text-sm font-medium">应付日期</Label>
                <div className="mt-1">
                  <Input
                    id="dueDate"
                    type="date"
                    value={formData.dueDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                    className="w-full h-8"
                  />
                </div>
              </div>

              {/* 付款备注 */}
              <div>
                <Label htmlFor="paymentNotes" className="text-sm font-medium">付款备注</Label>
                <div className="mt-1">
                  <Textarea
                    id="paymentNotes"
                    value={formData.paymentNotes}
                    onChange={(e) => setFormData(prev => ({ ...prev, paymentNotes: e.target.value }))}
                    placeholder="付款方式、分期安排等"
                    rows={2}
                    className="w-full text-sm"
                  />
                </div>
              </div>
            </div>
          </div>

        </form>

        {/* 操作确认 - 固定在底部 */}
        <div className="flex-shrink-0 border-t pt-3 bg-white">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 mb-3">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full flex-shrink-0"></div>
              <div className="text-xs text-blue-800">
                <span className="font-medium">提醒：</span>
                <span>已付金额自动计算状态，特殊情况可手动调整</span>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
              className="px-4 h-8"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-2 border-blue-400"
              onClick={handleSubmit}
              size="lg"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  <span className="text-base">更新中...</span>
                </>
              ) : (
                <>
                  <DollarSign className="h-4 w-4 mr-2" />
                  <span className="text-base">确认更新</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
