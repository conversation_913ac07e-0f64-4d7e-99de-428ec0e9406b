/**
 * 🇨🇳 风口云平台 - 公告关闭API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 关闭公告（不再显示在弹窗中）
export async function POST(request: NextRequest) {
  try {
    console.log('📢 接收到关闭公告请求')
    const { factoryId, announcementId } = await request.json()

    // 验证必需参数
    if (!factoryId || !announcementId) {
      console.log('❌ 缺少必要参数')
      return NextResponse.json(
        { error: '缺少工厂ID或公告ID' },
        { status: 400 }
      )
    }

    console.log('📝 关闭公告:', { factoryId, announcementId })

    // 调用数据库服务关闭公告
    await db.dismissAnnouncement(factoryId, announcementId)

    console.log('✅ 公告关闭成功')

    return NextResponse.json({
      success: true,
      message: '公告已关闭'
    })

  } catch (error) {
    console.error('❌ 关闭公告失败:', error)
    return NextResponse.json(
      { error: `关闭公告失败: ${error.message || '未知错误'}` },
      { status: 500 }
    )
  }
}
