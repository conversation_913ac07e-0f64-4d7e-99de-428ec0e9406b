"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  ShoppingCart,
  DollarSign,
  TrendingUp,
  Package,
  Plus,
  FileText,
  AlertTriangle,
  Clock,
  CheckCircle,
  BarChart3,
  Target,
  Award,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  Bell
} from "lucide-react"
import {
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend
} from 'recharts'
import { getCurrentFactoryId, getCurrentUser } from "@/lib/utils/factory"
import { db } from "@/lib/database"
import { safeAmountFormat, safeAmount, safeNumber as utilSafeNumber, safeAmountSum } from "@/lib/utils/number-utils"
import { getProductTypeName } from "@/lib/pricing"

import { AnnouncementPopup } from "@/components/announcements/announcement-popup"
import { FactoryRouteGuard } from "@/components/auth/route-guard"
import Link from "next/link"

// 颜色配置
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

// 数据接口定义
interface DashboardData {
  // 基础统计
  totalClients: number
  totalOrders: number
  monthlyRevenue: number
  pendingOrders: number
  completedOrders: number
  monthlyGrowth: number

  // 趋势数据
  monthlyTrends: Array<{
    month: string
    orders: number
    revenue: number
    clients: number
  }>

  // 订单状态分布
  orderStatusStats: Array<{
    status: string
    count: number
    percentage: number
  }>

  // 产品销量统计
  productStats: Array<{
    name: string
    quantity: number
    revenue: number
  }>

  // 最近订单
  recentOrders: Array<{
    id: string
    client: string
    amount: number
    status: string
    date: string
    createdBy: string
  }>

  // 主要客户
  topClients: Array<{
    id: string
    name: string
    orders: number
    amount: number
    lastOrder: string
  }>

  // 员工绩效
  employeeStats: Array<{
    name: string
    orders: number
    revenue: number
  }>

  // 预警信息
  alerts: Array<{
    id: string
    type: 'warning' | 'info' | 'success'
    message: string
    time: string
  }>
}

// 使用统一的安全数值转换函数（带精度处理）
const safeNumber = utilSafeNumber

// 数据处理函数
const processDashboardData = (orders: unknown[], clients: unknown[]): DashboardData => {
  // 根据业务逻辑：部分付款或付清的订单视为已完成
  const getEffectiveStatus = (order: unknown) => {
    // 如果付款状态为部分付款或已付清，则视为已完成
    if (order.paymentStatus === 'partial' || order.paymentStatus === 'paid') {
      return 'completed'
    }
    // 否则使用原始状态
    return order.status
  }

  // 基础统计 - 🔧 修复：使用安全累加函数
  const totalOrders = orders.length
  const totalClients = clients.length
  const totalRevenue = safeAmountSum(orders.map(order => order.totalAmount))

  // 订单状态统计（使用有效状态）
  const pendingOrders = orders.filter(o => getEffectiveStatus(o) === 'pending').length
  const completedOrders = orders.filter(o => getEffectiveStatus(o) === 'completed').length

  // 月度趋势（最近6个月）
  const monthlyTrends = generateMonthlyTrends(orders)

  // 订单状态分布
  const orderStatusStats = generateOrderStatusStats(orders)

  // 产品统计
  const productStats = generateProductStats(orders)

  // 最近订单（最新5个）
  const recentOrders = orders
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5)
    .map(order => ({
      id: order.orderNumber || order.id,
      client: order.clientName,
      amount: safeNumber(order.totalAmount),
      status: order.status,
      date: new Date(order.createdAt).toLocaleDateString(),
      createdBy: order.createdByName || order.createdBy || '系统'
    }))

  // 主要客户（按订单金额排序）
  const clientStats = generateClientStats(orders, clients)

  // 员工绩效统计
  const employeeStats = generateEmployeeStats(orders)

  // 预警信息
  const alerts = generateAlerts(orders, clients)

  return {
    totalClients,
    totalOrders,
    monthlyRevenue: totalRevenue,
    pendingOrders,
    completedOrders,
    monthlyGrowth: calculateMonthlyGrowth(orders),
    monthlyTrends,
    orderStatusStats,
    productStats,
    recentOrders,
    topClients: clientStats,
    employeeStats,
    alerts
  }
}

// 生成月度趋势数据
const generateMonthlyTrends = (orders: unknown[]) => {
  const months: Array<{
    month: string;
    orders: number;
    revenue: number;
    clients: number;
  }> = []
  const now = new Date()

  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    const monthStr = date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit' })

    const monthOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt)
      return orderDate.getFullYear() === date.getFullYear() &&
             orderDate.getMonth() === date.getMonth()
    })

    months.push({
      month: monthStr,
      orders: monthOrders.length,
      revenue: safeAmountSum(monthOrders.map(order => order.totalAmount)),
      clients: new Set(monthOrders.map(order => order.clientId)).size
    })
  }

  return months
}

// 生成订单状态统计
const generateOrderStatusStats = (orders: unknown[]) => {
  // 根据业务逻辑：部分付款或付清的订单视为已完成
  const getEffectiveStatus = (order: unknown) => {
    // 如果付款状态为部分付款或已付清，则视为已完成
    if (order.paymentStatus === 'partial' || order.paymentStatus === 'paid') {
      return 'completed'
    }
    // 否则使用原始状态
    return order.status
  }

  const statusCounts = {
    pending: orders.filter(o => getEffectiveStatus(o) === 'pending').length,
    production: orders.filter(o => getEffectiveStatus(o) === 'production').length,
    completed: orders.filter(o => getEffectiveStatus(o) === 'completed').length,
    cancelled: orders.filter(o => getEffectiveStatus(o) === 'cancelled').length
  }

  const total = orders.length

  return Object.entries(statusCounts).map(([status, count]) => ({
    status: getStatusText(status),
    count,
    percentage: total > 0 ? Math.round((count / total) * 100) : 0
  }))
}

// 生成产品统计
const generateProductStats = (orders: unknown[]) => {
  const productMap = new Map()

  orders.forEach(order => {
    order.items?.forEach((item: unknown) => {
      // 优先使用统一的产品类型名称转换函数，确保显示中文名称
      let productName = '未知产品'

      if (item.productType) {
        // 使用统一的产品类型名称转换函数
        productName = getProductTypeName(item.productType)
      } else if (item.productName) {
        // 如果没有productType，检查productName是否为英文代码
        const chineseName = getProductTypeName(item.productName)
        productName = chineseName !== item.productName ? chineseName : item.productName
      }

      if (productMap.has(productName)) {
        const existing = productMap.get(productName)
        productMap.set(productName, {
          name: productName,
          quantity: existing.quantity + safeNumber(item.quantity),
          revenue: existing.revenue + safeNumber(item.totalPrice)
        })
      } else {
        productMap.set(productName, {
          name: productName,
          quantity: safeNumber(item.quantity),
          revenue: safeNumber(item.totalPrice)
        })
      }
    })
  })

  return Array.from(productMap.values())
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 6)
}

// 生成客户统计
const generateClientStats = (orders: unknown[], clients: unknown[]) => {
  const clientMap = new Map()

  orders.forEach(order => {
    const clientId = order.clientId
    if (clientMap.has(clientId)) {
      const existing = clientMap.get(clientId)
      clientMap.set(clientId, {
        ...existing,
        orders: existing.orders + 1,
        amount: existing.amount + safeNumber(order.totalAmount),
        lastOrder: order.createdAt > existing.lastOrder ? order.createdAt : existing.lastOrder
      })
    } else {
      const client = clients.find(c => c.id === clientId)
      clientMap.set(clientId, {
        id: clientId,
        name: client?.name || order.clientName || '未知客户',
        orders: 1,
        amount: safeNumber(order.totalAmount),
        lastOrder: order.createdAt
      })
    }
  })

  return Array.from(clientMap.values())
    .sort((a, b) => b.amount - a.amount)
    .slice(0, 5)
    .map(client => ({
      ...client,
      lastOrder: new Date(client.lastOrder).toLocaleDateString()
    }))
}

// 生成员工绩效统计
const generateEmployeeStats = (orders: unknown[]) => {
  const employeeMap = new Map()

  orders.forEach(order => {
    // 优先使用录单员姓名，如果没有则使用ID，最后回退到'系统'
    const employeeId = order.createdBy || 'system'
    const employeeName = order.createdByName || order.createdBy || '系统'

    if (employeeMap.has(employeeId)) {
      const existing = employeeMap.get(employeeId)
      employeeMap.set(employeeId, {
        name: employeeName,
        orders: existing.orders + 1,
        revenue: existing.revenue + safeNumber(order.totalAmount)
      })
    } else {
      employeeMap.set(employeeId, {
        name: employeeName,
        orders: 1,
        revenue: safeNumber(order.totalAmount)
      })
    }
  })

  return Array.from(employeeMap.values())
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5)
}

// 生成预警信息
const generateAlerts = (orders: unknown[], clients: unknown[]) => {
  const alerts: unknown[] = []
  const now = new Date()

  // 根据业务逻辑：部分付款或付清的订单视为已完成
  const getEffectiveStatus = (order: unknown) => {
    // 如果付款状态为部分付款或已付清，则视为已完成
    if (order.paymentStatus === 'partial' || order.paymentStatus === 'paid') {
      return 'completed'
    }
    // 否则使用原始状态
    return order.status
  }

  // 检查待处理订单（使用有效状态）
  const pendingOrders = orders.filter(o => getEffectiveStatus(o) === 'pending')
  if (pendingOrders.length > 10) {
    alerts.push({
      id: 'pending-orders',
      type: 'warning' as const,
      message: `有 ${pendingOrders.length} 个订单待处理，请及时确认`,
      time: '刚刚'
    })
  }

  // 检查本月新客户
  const thisMonthClients = clients.filter(client => {
    const clientDate = new Date(client.createdAt || client.registeredAt || now)
    return clientDate.getMonth() === now.getMonth() &&
           clientDate.getFullYear() === now.getFullYear()
  })

  if (thisMonthClients.length > 0) {
    alerts.push({
      id: 'new-clients',
      type: 'success' as const,
      message: `本月新增 ${thisMonthClients.length} 个客户`,
      time: '今天'
    })
  }

  // 检查营收增长
  const thisMonth = orders.filter(order => {
    const orderDate = new Date(order.createdAt)
    return orderDate.getMonth() === now.getMonth() &&
           orderDate.getFullYear() === now.getFullYear()
  })

  const lastMonth = orders.filter(order => {
    const orderDate = new Date(order.createdAt)
    const lastMonthDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    return orderDate.getMonth() === lastMonthDate.getMonth() &&
           orderDate.getFullYear() === lastMonthDate.getFullYear()
  })

  const thisMonthRevenue = safeAmountSum(thisMonth.map(order => order.totalAmount))
  const lastMonthRevenue = safeAmountSum(lastMonth.map(order => order.totalAmount))

  // 营收变化提醒（使用安全的除法运算）
  if (thisMonthRevenue > 0 || lastMonthRevenue > 0) {
    if (lastMonthRevenue === 0 && thisMonthRevenue > 0) {
      // 上月无营收，本月有营收
      alerts.push({
        id: 'revenue-growth',
        type: 'success' as const,
        message: `本月营收 ¥${(thisMonthRevenue / 10000).toFixed(1)}万，较上月实现突破`,
        time: '今天'
      })
    } else if (lastMonthRevenue > 0) {
      // 正常的增长率计算
      const growth = ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100)
      if (growth > 0) {
        alerts.push({
          id: 'revenue-growth',
          type: 'info' as const,
          message: `本月营收较上月增长 ${growth.toFixed(1)}%`,
          time: '今天'
        })
      } else if (growth < -10) {
        // 下降超过10%时给出警告
        alerts.push({
          id: 'revenue-decline',
          type: 'warning' as const,
          message: `本月营收较上月下降 ${Math.abs(growth).toFixed(1)}%`,
          time: '今天'
        })
      }
    }
  }

  return alerts.slice(0, 5)
}

// 计算月度增长率
const calculateMonthlyGrowth = (orders: unknown[]) => {
  const now = new Date()
  const thisMonth = orders.filter(order => {
    const orderDate = new Date(order.createdAt)
    return orderDate.getMonth() === now.getMonth() &&
           orderDate.getFullYear() === now.getFullYear()
  })

  const lastMonth = orders.filter(order => {
    const orderDate = new Date(order.createdAt)
    const lastMonthDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    return orderDate.getMonth() === lastMonthDate.getMonth() &&
           orderDate.getFullYear() === lastMonthDate.getFullYear()
  })

  const thisMonthRevenue = safeAmountSum(thisMonth.map(order => order.totalAmount))
  const lastMonthRevenue = safeAmountSum(lastMonth.map(order => order.totalAmount))

  // 使用安全的除法运算，避免除零错误和无穷大
  if (lastMonthRevenue === 0) {
    // 如果上月营收为0，本月有营收，返回一个合理的正值表示增长
    return thisMonthRevenue > 0 ? 100 : 0
  }

  const growth = ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100)

  // 确保返回值是有限数字，并限制在合理范围内
  if (!isFinite(growth)) return 0

  // 限制增长率在 -999% 到 999% 之间，避免极端值
  const clampedGrowth = Math.max(-999, Math.min(999, growth))

  return Number(clampedGrowth.toFixed(1))
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'text-yellow-600 bg-yellow-100'
    case 'production': return 'text-blue-600 bg-blue-100'
    case 'completed': return 'text-green-600 bg-green-100'
    case 'cancelled': return 'text-red-600 bg-red-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待确认'
    case 'production': return '生产中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

export default function FactoryDashboard() {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [currentUser, setCurrentUser] = useState<unknown>(null)
  const [showAnnouncementPopup, setShowAnnouncementPopup] = useState(false)
  const [hasCheckedAnnouncements, setHasCheckedAnnouncements] = useState(false)

  // 检查是否在当前会话中已经检查过公告（避免重复弹窗）
  const hasCheckedAnnouncementsInSession = () => {
    try {
      const factoryId = getCurrentFactoryId()
      if (!factoryId) return false

      const sessionKey = `announcement_session_checked_${factoryId}`
      const lastCheckTime = sessionStorage.getItem(sessionKey)

      if (!lastCheckTime) return false

      // 检查是否在当前会话中（30分钟内）
      const checkTime = new Date(lastCheckTime)
      const now = new Date()
      const diffMinutes = (now.getTime() - checkTime.getTime()) / (1000 * 60)

      // 如果超过30分钟，认为是新会话
      if (diffMinutes > 30) {
        sessionStorage.removeItem(sessionKey)
        return false
      }

      return true
    } catch (error) {
      console.error('❌ 检查会话公告状态失败:', error)
      return false
    }
  }

  // 标记当前会话已检查公告
  const markAnnouncementsCheckedInSession = () => {
    try {
      const factoryId = getCurrentFactoryId()
      if (!factoryId) return

      const sessionKey = `announcement_session_checked_${factoryId}`
      const currentTime = new Date().toISOString()
      sessionStorage.setItem(sessionKey, currentTime)
      console.log('✅ 已标记当前会话公告检查状态:', currentTime)
    } catch (error) {
      console.error('❌ 标记会话公告检查状态失败:', error)
    }
  }

  // 加载仪表板数据
  const loadDashboardData = async () => {
    try {
      setLoading(true)
      console.log('🔄 开始加载仪表板数据...')

      const factoryId = getCurrentFactoryId()
      const user = getCurrentUser()
      setCurrentUser(user)

      if (!factoryId) {
        console.warn('⚠️ 无法获取工厂ID')
        return
      }

      // 并行加载订单和客户数据
      const [orders, clients] = await Promise.all([
        db.getOrdersByFactoryId(factoryId),
        db.getClientsByFactoryId(factoryId)
      ])

      console.log('📊 数据加载完成:', {
        orders: orders.length,
        clients: clients.length
      })

      // 处理数据
      const dashboardData = processDashboardData(orders, clients)
      setData(dashboardData)

    } catch (error) {
      console.error('❌ 加载仪表板数据失败:', error)
      // 使用模拟数据作为后备
      setData(generateMockData())
    } finally {
      setLoading(false)
    }
  }

  // 刷新数据
  const refreshData = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  // 检查未读公告（登录后弹窗）
  const checkUnreadAnnouncements = async () => {
    try {
      const factoryId = getCurrentFactoryId()

      if (!factoryId) {
        console.log('⚠️ 无法获取工厂ID，跳过公告检查（可能正在状态恢复中）')
        return
      }

      // 检查是否在当前会话中已经检查过公告（避免同一会话重复弹窗）
      if (hasCheckedAnnouncements || hasCheckedAnnouncementsInSession()) {
        console.log('✅ 当前会话已检查过公告，跳过重复检查')
        setHasCheckedAnnouncements(true)
        return
      }

      console.log('🔔 开始检查未读公告...', { factoryId, timestamp: new Date().toISOString() })

      // 使用API获取未读公告，确保状态过滤正确
      const response = await fetch(`/api/announcements/active?factoryId=${factoryId}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || '获取未读公告失败')
      }

      const unreadAnnouncements = result.data?.announcements || []

      console.log('📊 未读公告检查结果:', {
        factoryId,
        unreadCount: unreadAnnouncements.length,
        announcements: unreadAnnouncements.map((a: unknown) => ({
          id: a.id,
          title: a.title,
          type: a.type,
          publishedAt: a.publishedAt
        })),
        timestamp: new Date().toISOString()
      })

      // 无论是否有未读公告，都标记当前会话已检查，避免重复检查
      markAnnouncementsCheckedInSession()
      setHasCheckedAnnouncements(true)

      if (unreadAnnouncements.length > 0) {
        console.log(`📢 发现 ${unreadAnnouncements.length} 条未读公告，显示弹窗`)
        setShowAnnouncementPopup(true)
      } else {
        console.log('✅ 没有未读公告，不显示弹窗')
      }
    } catch (error) {
      console.error('❌ 检查未读公告失败:', error)
      // 即使出错也要标记已检查，避免无限重试
      markAnnouncementsCheckedInSession()
      setHasCheckedAnnouncements(true)
    }
  }

  // 处理公告已读回调
  const handleAnnouncementRead = (announcementId: string) => {
    console.log('✅ 公告已读:', announcementId)
    // 这里可以添加其他处理逻辑，比如更新公告栏显示
  }

  // 处理公告弹窗关闭
  const handleAnnouncementPopupClose = (open: boolean) => {
    setShowAnnouncementPopup(open)

    // 当弹窗关闭时，标记当前会话已检查过公告
    if (!open) {
      console.log('📢 公告弹窗已关闭，标记当前会话已检查')
      markAnnouncementsCheckedInSession()
    }
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadDashboardData()
  }, [])

  // 数据加载完成后检查未读公告
  useEffect(() => {
    console.log('📊 公告检查useEffect触发:', { loading, hasCheckedAnnouncements })

    if (!loading) {
      console.log('⏰ 设置公告检查定时器...')
      // 延迟3秒后检查公告，确保页面已完全加载且状态已恢复
      const timer = setTimeout(() => {
        console.log('⏰ 定时器触发，开始检查公告')
        checkUnreadAnnouncements()
      }, 3000)

      return () => {
        console.log('🧹 清理公告检查定时器')
        clearTimeout(timer)
      }
    }
  }, [loading, hasCheckedAnnouncements])

  // 生成模拟数据（作为后备）
  const generateMockData = (): DashboardData => ({
    totalClients: 156,
    totalOrders: 234,
    monthlyRevenue: 285000,
    pendingOrders: 12,
    completedOrders: 222,
    monthlyGrowth: 12.5,
    monthlyTrends: [
      { month: '2024-01', orders: 45, revenue: 180000, clients: 32 },
      { month: '2024-02', orders: 52, revenue: 210000, clients: 38 },
      { month: '2024-03', orders: 48, revenue: 195000, clients: 35 },
      { month: '2024-04', orders: 61, revenue: 245000, clients: 42 },
      { month: '2024-05', orders: 58, revenue: 235000, clients: 40 },
      { month: '2024-06', orders: 65, revenue: 285000, clients: 45 }
    ],
    orderStatusStats: [
      { status: '待确认', count: 12, percentage: 5 },
      { status: '生产中', count: 28, percentage: 12 },
      { status: '已完成', count: 194, percentage: 83 }
    ],
    productStats: [
      { name: '普通出风口', quantity: 1250, revenue: 187500 },
      { name: '普通回风口', quantity: 980, revenue: 147000 },
      { name: '黑白双色风口', quantity: 560, revenue: 112000 },
      { name: '高端风口系列', quantity: 340, revenue: 102000 }
    ],
    recentOrders: [
      { id: "ORD-001", client: "上海建筑公司", amount: 15800, status: "pending", date: "2024-01-15", createdBy: "张小明" },
      { id: "ORD-002", client: "北京装饰工程", amount: 23400, status: "production", date: "2024-01-14", createdBy: "李经理" },
      { id: "ORD-003", client: "广州空调安装", amount: 8900, status: "completed", date: "2024-01-13", createdBy: "王小红" },
      { id: "ORD-004", client: "深圳建设集团", amount: 45600, status: "completed", date: "2024-01-12", createdBy: "张小明" }
    ],
    topClients: [
      { id: "1", name: "上海建筑公司", orders: 8, amount: 125000, lastOrder: "2024-01-15" },
      { id: "2", name: "北京装饰工程", orders: 12, amount: 189000, lastOrder: "2024-01-14" },
      { id: "3", name: "广州空调安装", orders: 6, amount: 78000, lastOrder: "2024-01-13" },
      { id: "4", name: "深圳建设集团", orders: 15, amount: 234000, lastOrder: "2024-01-12" }
    ],
    employeeStats: [
      { name: "张小明", orders: 45, revenue: 180000 },
      { name: "李经理", orders: 38, revenue: 152000 },
      { name: "王小红", orders: 32, revenue: 128000 }
    ],
    alerts: [
      { id: '1', type: 'warning', message: '有 12 个订单待处理，请及时确认', time: '刚刚' },
      { id: '2', type: 'success', message: '本月新增 8 个客户', time: '今天' },
      { id: '3', type: 'info', message: '本月营收较上月增长 12.5%', time: '今天' }
    ]
  })

  if (loading) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Activity className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
              <p className="text-gray-600">正在加载仪表板数据...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!data) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="text-center py-12">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">数据加载失败</h3>
            <p className="text-gray-600 mb-4">无法加载仪表板数据，请稍后重试</p>
            <Button onClick={refreshData} disabled={refreshing}>
              {refreshing ? '刷新中...' : '重新加载'}
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <FactoryRouteGuard>
      <DashboardLayout role="factory">
        <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <p className="text-lg text-gray-600">
              {currentUser?.factoryName || '工厂'} 数据概览
              <span className="ml-2 text-base text-blue-600">
                最后更新: {new Date().toLocaleTimeString()}
              </span>
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="default"
              onClick={refreshData}
              disabled={refreshing}
              className="text-base font-medium px-6 py-3 border-2 hover:bg-gray-50 transition-all duration-200"
            >
              <Activity className={`h-5 w-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? '刷新中' : '刷新数据'}
            </Button>
            <Link href="/factory/orders/create-table">
              <Button
                size="default"
                className="text-base font-semibold px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                <Plus className="h-5 w-5 mr-2" />
                新建订单
              </Button>
            </Link>
          </div>
        </div>

        {/* 总部公告推送功能已集成到弹窗中 */}

        {/* 预警信息 */}
        {data.alerts.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center space-x-2 mb-3">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              <h2 className="text-lg font-semibold">重要提醒</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {data.alerts.slice(0, 3).map((alert) => (
                <Card key={alert.id} className={`border-l-4 ${
                  alert.type === 'warning' ? 'border-l-yellow-500 bg-yellow-50' :
                  alert.type === 'success' ? 'border-l-green-500 bg-green-50' :
                  'border-l-blue-500 bg-blue-50'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      {alert.type === 'warning' && <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />}
                      {alert.type === 'success' && <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />}
                      {alert.type === 'info' && <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5" />}
                      <div className="flex-1">
                        <p className="text-sm font-medium">{alert.message}</p>
                        <p className="text-xs text-gray-500 mt-1">{alert.time}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-medium">总客户数</CardTitle>
              <Users className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{data.totalClients}</div>
              <p className="text-sm text-muted-foreground">
                活跃客户占比: {data.totalOrders > 0 ? Math.round((data.topClients.length / data.totalClients) * 100) : 0}%
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-medium">总订单数</CardTitle>
              <ShoppingCart className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{data.totalOrders}</div>
              <p className="text-sm text-muted-foreground">
                待处理: <span className="text-yellow-600 font-medium">{data.pendingOrders}</span>
              </p>
            </CardContent>
          </Card>



          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-medium">月度营收</CardTitle>
              <DollarSign className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">¥{(safeNumber(data.monthlyRevenue) / 10000).toFixed(1)}万</div>
              <p className={`text-sm flex items-center ${
                safeNumber(data.monthlyGrowth) >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {safeNumber(data.monthlyGrowth) >= 0 ? (
                  <ArrowUpRight className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDownRight className="h-4 w-4 mr-1" />
                )}
                {Math.abs(safeNumber(data.monthlyGrowth))}% 环比上月
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-medium">完成率</CardTitle>
              <TrendingUp className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">
                {data.totalOrders > 0 ? ((data.completedOrders / data.totalOrders) * 100).toFixed(1) : 0}%
              </div>
              <div className="mt-2">
                <Progress
                  value={data.totalOrders > 0 ? (data.completedOrders / data.totalOrders) * 100 : 0}
                  className="h-3"
                />
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                已完成: {data.completedOrders} / {data.totalOrders}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 数据分析标签页 */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 h-12">
            <TabsTrigger value="overview" className="text-base font-medium">业务概览</TabsTrigger>
            <TabsTrigger value="trends" className="text-base font-medium">趋势分析</TabsTrigger>
            <TabsTrigger value="products" className="text-base font-medium">产品分析</TabsTrigger>
            <TabsTrigger value="performance" className="text-base font-medium">绩效分析</TabsTrigger>
          </TabsList>

          {/* 业务概览 */}
          <TabsContent value="overview" className="space-y-6">

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 最近订单 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Clock className="h-6 w-6 mr-2" />
                    最近订单
                  </CardTitle>
                  <CardDescription className="text-base">最新的订单记录</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.recentOrders.map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                        <div className="flex items-center space-x-3">
                          <Package className="h-6 w-6 text-gray-400" />
                          <div>
                            <p className="font-medium text-base">{order.id}</p>
                            <p className="text-sm text-gray-600">{order.client}</p>
                            <p className="text-sm text-blue-600">录单员: {order.createdBy}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">¥{order.amount.toLocaleString()}</p>
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(order.status)}`}>
                            {getStatusText(order.status)}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 pt-4 border-t">
                    <Link href="/factory/orders">
                      <Button variant="outline" size="sm" className="w-full">
                        查看所有订单
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              {/* 主要客户 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Award className="h-6 w-6 mr-2" />
                    主要客户
                  </CardTitle>
                  <CardDescription className="text-base">订单金额最高的客户</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.topClients.map((client, index) => (
                      <div key={client.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                        <div className="flex items-center space-x-3">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white text-base font-bold ${
                            index === 0 ? 'bg-yellow-500' :
                            index === 1 ? 'bg-gray-400' :
                            index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-medium text-base">{client.name}</p>
                            <p className="text-sm text-gray-600">{client.orders} 个订单</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-base">¥{(safeNumber(client.amount) / 10000).toFixed(1)}万</p>
                          <p className="text-sm text-gray-600">{client.lastOrder}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 pt-4 border-t">
                    <Link href="/factory/clients">
                      <Button variant="outline" size="sm" className="w-full">
                        客户管理
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 订单状态分布 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>订单状态分布</CardTitle>
                  <CardDescription>各状态订单的数量分布</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={data.orderStatusStats}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={({ status, percentage, cx, cy, midAngle, outerRadius }) => {
                          // 计算标签位置，避免重叠
                          const RADIAN = Math.PI / 180
                          const radius = outerRadius + 30
                          const x = cx + radius * Math.cos(-midAngle * RADIAN)
                          const y = cy + radius * Math.sin(-midAngle * RADIAN)

                          return (
                            <text
                              x={x}
                              y={y}
                              fill="#374151"
                              textAnchor={x > cx ? 'start' : 'end'}
                              dominantBaseline="central"
                              fontSize="12"
                              fontWeight="500"
                            >
                              {`${status}: ${percentage}%`}
                            </text>
                          )
                        }}
                        outerRadius={70}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {data.orderStatusStats.map((entry, index) => (
                          <Cell key={`order-status-cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => [`${value} 个`, '订单数']}
                        contentStyle={{
                          backgroundColor: '#f9fafb',
                          border: '1px solid #e5e7eb',
                          borderRadius: '6px',
                          fontSize: '12px'
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 员工绩效排行 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2" />
                    员工绩效排行
                  </CardTitle>
                  <CardDescription>按营收排序的员工绩效</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.employeeStats.map((employee, index) => (
                      <div key={employee.name} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                            index === 0 ? 'bg-yellow-500' :
                            index === 1 ? 'bg-gray-400' :
                            index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-medium">{employee.name}</p>
                            <p className="text-sm text-gray-600">{employee.orders} 个订单</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">¥{(safeNumber(employee.revenue) / 10000).toFixed(1)}万</p>
                          <div className="w-20 bg-gray-200 rounded-full h-2 mt-1">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{
                                width: `${Math.min((safeNumber(employee.revenue) / Math.max(...data.employeeStats.map(e => safeNumber(e.revenue)))) * 100, 100)}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 趋势分析 */}
          <TabsContent value="trends" className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              {/* 月度趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2" />
                    月度业务趋势
                  </CardTitle>
                  <CardDescription>最近6个月的订单和营收趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={data.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="month"
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <YAxis
                        yAxisId="left"
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <YAxis
                        yAxisId="right"
                        orientation="right"
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <Tooltip
                        formatter={(value, name) => [
                          name === 'orders' ? `${value} 个` :
                          name === 'clients' ? `${value} 个` :
                          `¥${Number(value).toLocaleString()}`,
                          name === 'orders' ? '订单数' :
                          name === 'clients' ? '客户数' : '营收'
                        ]}
                        contentStyle={{
                          backgroundColor: '#f9fafb',
                          border: '1px solid #e5e7eb',
                          borderRadius: '6px',
                          fontSize: '12px'
                        }}
                      />
                      <Legend
                        wrapperStyle={{ fontSize: '12px', paddingTop: '10px' }}
                      />
                      <Bar yAxisId="left" dataKey="orders" fill="#8884d8" name="订单数" radius={[2, 2, 0, 0]} />
                      <Line yAxisId="right" type="monotone" dataKey="revenue" stroke="#82ca9d" strokeWidth={3} name="营收" dot={{ r: 4 }} />
                      <Line yAxisId="left" type="monotone" dataKey="clients" stroke="#ffc658" strokeWidth={2} name="客户数" dot={{ r: 3 }} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 营收趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle>营收增长趋势</CardTitle>
                  <CardDescription>月度营收变化情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={data.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="month"
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <Tooltip
                        formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']}
                        contentStyle={{
                          backgroundColor: '#f9fafb',
                          border: '1px solid #e5e7eb',
                          borderRadius: '6px',
                          fontSize: '12px'
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="revenue"
                        stroke="#8884d8"
                        fill="#8884d8"
                        fillOpacity={0.6}
                        dot={{ r: 3 }}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 产品分析 */}
          <TabsContent value="products" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 产品销量排行 */}
              <Card>
                <CardHeader>
                  <CardTitle>产品销量排行</CardTitle>
                  <CardDescription>各类产品的销量统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.productStats} layout="horizontal" margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        type="number"
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <YAxis
                        dataKey="name"
                        type="category"
                        width={90}
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <Tooltip
                        formatter={(value) => [`${value} 个`, '销量']}
                        contentStyle={{
                          backgroundColor: '#f9fafb',
                          border: '1px solid #e5e7eb',
                          borderRadius: '6px',
                          fontSize: '12px'
                        }}
                      />
                      <Bar
                        dataKey="quantity"
                        fill="#8884d8"
                        radius={[0, 4, 4, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 产品营收分布 */}
              <Card>
                <CardHeader>
                  <CardTitle>产品营收分布</CardTitle>
                  <CardDescription>各类产品的营收占比</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={data.productStats}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={({ name, revenue, cx, cy, midAngle, outerRadius }) => {
                          // 计算标签位置，避免重叠
                          const RADIAN = Math.PI / 180
                          const radius = outerRadius + 35
                          const x = cx + radius * Math.cos(-midAngle * RADIAN)
                          const y = cy + radius * Math.sin(-midAngle * RADIAN)

                          return (
                            <text
                              x={x}
                              y={y}
                              fill="#374151"
                              textAnchor={x > cx ? 'start' : 'end'}
                              dominantBaseline="central"
                              fontSize="11"
                              fontWeight="500"
                            >
                              {`${name}: ¥${(safeNumber(revenue) / 10000).toFixed(1)}万`}
                            </text>
                          )
                        }}
                        outerRadius={65}
                        fill="#8884d8"
                        dataKey="revenue"
                      >
                        {data.productStats.map((entry, index) => (
                          <Cell key={`product-revenue-cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']}
                        contentStyle={{
                          backgroundColor: '#f9fafb',
                          border: '1px solid #e5e7eb',
                          borderRadius: '6px',
                          fontSize: '12px'
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 产品详细统计 */}
            <Card>
              <CardHeader>
                <CardTitle>产品详细统计</CardTitle>
                <CardDescription>各产品的详细销售数据</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">产品名称</th>
                        <th className="text-right py-2">销量</th>
                        <th className="text-right py-2">营收</th>
                        <th className="text-right py-2">平均单价</th>
                        <th className="text-right py-2">占比</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.productStats.map((product, index) => {
                        const totalRevenue = safeAmountSum(data.productStats.map(p => p.revenue))
                        const percentage = totalRevenue > 0 ? (safeNumber(product.revenue) / totalRevenue * 100).toFixed(1) : '0'
                        const avgPrice = safeNumber(product.quantity) > 0 ? safeAmountFormat(safeNumber(product.revenue) / safeNumber(product.quantity)) : '0.00'

                        return (
                          <tr key={product.name} className="border-b hover:bg-gray-50">
                            <td className="py-3">
                              <div className="flex items-center space-x-2">
                                <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: COLORS[index % COLORS.length] }}></div>
                                <span className="font-medium">{product.name}</span>
                              </div>
                            </td>
                            <td className="text-right py-3">{safeNumber(product.quantity).toLocaleString()}</td>
                            <td className="text-right py-3">{safeAmount(product.revenue)}</td>
                            <td className="text-right py-3">¥{avgPrice}</td>
                            <td className="text-right py-3">{percentage}%</td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 绩效分析 */}
          <TabsContent value="performance" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 员工绩效对比 */}
              <Card>
                <CardHeader>
                  <CardTitle>员工绩效对比</CardTitle>
                  <CardDescription>员工订单数量对比</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.employeeStats} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="name"
                        tick={{ fontSize: 12 }}
                        height={60}
                        interval={0}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <Tooltip
                        formatter={(value) => [`${value} 个`, '订单数']}
                        contentStyle={{
                          backgroundColor: '#f9fafb',
                          border: '1px solid #e5e7eb',
                          borderRadius: '6px',
                          fontSize: '12px'
                        }}
                      />
                      <Bar
                        dataKey="orders"
                        fill="#8884d8"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 员工营收对比 */}
              <Card>
                <CardHeader>
                  <CardTitle>员工营收对比</CardTitle>
                  <CardDescription>员工营收贡献对比</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.employeeStats} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="name"
                        tick={{ fontSize: 12 }}
                        height={60}
                        interval={0}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#e5e7eb' }}
                      />
                      <Tooltip
                        formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']}
                        contentStyle={{
                          backgroundColor: '#f9fafb',
                          border: '1px solid #e5e7eb',
                          borderRadius: '6px',
                          fontSize: '12px'
                        }}
                      />
                      <Bar
                        dataKey="revenue"
                        fill="#82ca9d"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 绩效详细统计 */}
            <Card>
              <CardHeader>
                <CardTitle>绩效详细统计</CardTitle>
                <CardDescription>员工详细绩效数据</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">员工姓名</th>
                        <th className="text-right py-2">订单数</th>
                        <th className="text-right py-2">营收</th>
                        <th className="text-right py-2">平均订单金额</th>
                        <th className="text-right py-2">营收占比</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.employeeStats.map((employee, index) => {
                        const totalRevenue = safeAmountSum(data.employeeStats.map(e => e.revenue))
                        const percentage = totalRevenue > 0 ? (safeNumber(employee.revenue) / totalRevenue * 100).toFixed(1) : '0'
                        const avgOrderValue = safeNumber(employee.orders) > 0 ? (safeNumber(employee.revenue) / safeNumber(employee.orders)).toFixed(2) : '0'

                        return (
                          <tr key={employee.name} className="border-b hover:bg-gray-50">
                            <td className="py-3">
                              <div className="flex items-center space-x-2">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                                  index === 0 ? 'bg-yellow-500' :
                                  index === 1 ? 'bg-gray-400' :
                                  index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                                }`}>
                                  {index + 1}
                                </div>
                                <span className="font-medium">{employee.name}</span>
                              </div>
                            </td>
                            <td className="text-right py-3">{safeNumber(employee.orders)}</td>
                            <td className="text-right py-3">¥{safeNumber(employee.revenue).toLocaleString()}</td>
                            <td className="text-right py-3">¥{avgOrderValue}</td>
                            <td className="text-right py-3">{percentage}%</td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 公告弹窗 */}
        <AnnouncementPopup
          open={showAnnouncementPopup}
          onOpenChange={handleAnnouncementPopupClose}
          onAnnouncementRead={handleAnnouncementRead}
        />
        </div>
      </DashboardLayout>
    </FactoryRouteGuard>
  )
}
