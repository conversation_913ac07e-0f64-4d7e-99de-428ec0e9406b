"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn  } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  LayoutDashboard,
  Factory,
  Users,
  ShoppingCart,
  FileText,
  BarChart3,
  Settings,
  LogOut,
  Building2,
  Calculator,
  Database,
  Bell,
  Image,
  TrendingUp,
  Shield
} from "lucide-react"
import { useAuthStore } from "@/lib/store/auth"
import { ThemeToggle } from "@/components/theme/theme-toggle"

interface SidebarProps {
  role: 'admin' | 'factory'
}

const adminNavItems = [
  { href: "/admin/dashboard", label: "仪表板", icon: LayoutDashboard },
  { href: "/admin/factories", label: "工厂管理", icon: Factory },
  { href: "/admin/carousel-management", label: "轮播图管理", icon: Image },
  { href: "/admin/data-sync", label: "数据同步", icon: Database },
  { href: "/admin/analytics", label: "数据分析", icon: BarChart3 },
  { href: "/admin/announcements", label: "系统公告", icon: FileText },
  { href: "/admin/settings", label: "系统设置", icon: Settings },
]

const factoryNavItems = [
  { href: "/factory/dashboard", label: "数据概览", icon: LayoutDashboard },
  { href: "/factory/announcements", label: "公告通知", icon: Bell },
  { href: "/factory/orders", label: "订单管理", icon: ShoppingCart },
  { href: "/factory/clients", label: "客户管理", icon: Users },
  { href: "/factory/employees", label: "员工管理", icon: Users },
  { href: "/factory/shareholders", label: "股东管理", icon: TrendingUp },
  { href: "/factory/bills", label: "账单管理", icon: FileText },
  { href: "/factory/costs", label: "成本管理", icon: Calculator },
  { href: "/login-records", label: "登录记录", icon: Shield },
  { href: "/factory/analytics", label: "数据分析", icon: BarChart3 },
  { href: "/factory/settings", label: "工厂设置", icon: Settings },
]

export function Sidebar({ role }: SidebarProps) {
  const pathname = usePathname()
  const { logout, user } = useAuthStore()
  
  const navItems = role === 'admin' ? adminNavItems : factoryNavItems
  const title = role === 'admin' ? '平台总部' : '工厂管理'
  const subtitle = role === 'admin' ? '管理中心' : user?.name || '工厂系统'

  return (
    <div className="flex flex-col h-full bg-background border-r border-border">
      {/* Logo */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${role === 'admin' ? 'bg-red-600 dark:bg-red-700' : 'bg-blue-600 dark:bg-blue-700'}`}>
            <Building2 className="h-7 w-7 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground" style={{ fontSize: '1.3rem', lineHeight: '1.7rem' }}>{title}</h1>
            <p className="text-base text-muted-foreground" style={{ fontSize: '1rem', lineHeight: '1.4rem' }}>{subtitle}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navItems.map((item) => {
            const isActive = pathname === item.href
            return (
              <li key={item.href}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-3 px-3 py-3 rounded-lg text-base font-medium transition-colors",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                  style={{ fontSize: '1.1rem', lineHeight: '1.5rem' }}
                >
                  <item.icon className="h-6 w-6" />
                  <span>{item.label}</span>
                </Link>
              </li>
            )
          })}
        </ul>
      </nav>

      {/* User Info & Logout */}
      <div className="p-4 border-t border-border">
        <div className="mb-3">
          <p className="text-base font-medium text-foreground" style={{ fontSize: '1.1rem', lineHeight: '1.5rem' }}>{user?.name}</p>
          <p className="text-sm text-muted-foreground" style={{ fontSize: '0.95rem', lineHeight: '1.3rem' }}>
            {role === 'admin' ? '系统管理员' : '工厂管理员'}
          </p>
        </div>

        {/* 主题切换和退出登录 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-base text-muted-foreground" style={{ fontSize: '1rem', lineHeight: '1.4rem' }}>主题设置</span>
            <ThemeToggle variant="outline" size="sm" />
          </div>

          <Button
            variant="outline"
            size="sm"
            className="w-full text-base"
            style={{ fontSize: '1rem', lineHeight: '1.4rem', padding: '0.6rem 1rem' }}
            onClick={() => logout()}
          >
            <LogOut className="h-5 w-5 mr-2" />
            退出登录
          </Button>
        </div>
      </div>
    </div>
  )
}
