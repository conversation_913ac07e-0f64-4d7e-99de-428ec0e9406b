/**
 * 🇨🇳 风口云平台 - 令牌刷新API
 */

import { NextRequest, NextResponse } from 'next/server'
import { verifyRefreshToken, generateTokenPair } from '@/lib/auth/jwt'

export async function POST(request: NextRequest) {
  try {
    const { refreshToken } = await request.json()

    if (!refreshToken) {
      return NextResponse.json(
        { error: '缺少刷新令牌' },
        { status: 400 }
      )
    }

    // 验证刷新令牌
    const verifyResult = verifyRefreshToken(refreshToken)

    if (!verifyResult.success || !verifyResult.user) {
      return NextResponse.json(
        { 
          error: '刷新令牌无效',
          message: verifyResult.error
        },
        { status: 401 }
      )
    }

    const user = verifyResult.user

    // 生成新的令牌对
    const newTokens = generateTokenPair({
      userId: user.userId,
      username: user.username,
      name: user.name || user.username, // 确保包含用户名称
      userType: user.userType,
      factoryId: user.factoryId,
      role: user.role,
      sessionId: user.sessionId // 保持会话ID
    })

    return NextResponse.json({
      success: true,
      message: '令牌刷新成功',
      ...newTokens
    })

  } catch (error) {
    console.error('❌ 令牌刷新失败:', error)
    return NextResponse.json(
      { error: '令牌刷新服务异常' },
      { status: 500 }
    )
  }
}
