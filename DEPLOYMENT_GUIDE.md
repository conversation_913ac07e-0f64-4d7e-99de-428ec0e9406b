# 🚀 风口云平台 - 阿里云部署完整教程

## 📋 目录
- [系统要求](#系统要求)
- [阿里云资源准备](#阿里云资源准备)
- [服务器环境配置](#服务器环境配置)
- [项目部署](#项目部署)
- [域名和SSL配置](#域名和ssl配置)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 🔧 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 40GB SSD
- **带宽**: 5Mbps
- **操作系统**: Ubuntu 22.04 LTS / CentOS 8

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **带宽**: 10Mbps

## ☁️ 阿里云资源准备

### 1. 购买ECS实例

1. 登录阿里云控制台
2. 选择 **云服务器ECS** > **实例**
3. 点击 **创建实例**
4. 配置选择：
   ```
   地域: 选择离用户最近的地域
   实例规格: ecs.c6.xlarge (4核8GB) 或更高
   镜像: Ubuntu 22.04 64位
   存储: 系统盘 100GB SSD
   网络: 专有网络VPC
   安全组: 新建安全组
   ```

### 2. 配置安全组

在ECS控制台 > 网络与安全 > 安全组中添加规则：

| 方向 | 协议 | 端口范围 | 授权对象 | 描述 |
|------|------|----------|----------|------|
| 入方向 | TCP | 22 | 0.0.0.0/0 | SSH |
| 入方向 | TCP | 80 | 0.0.0.0/0 | HTTP |
| 入方向 | TCP | 443 | 0.0.0.0/0 | HTTPS |
| 入方向 | TCP | 3000 | 0.0.0.0/0 | 应用端口(可选) |

### 3. 购买域名（可选）

1. 在阿里云域名服务中购买域名
2. 完成域名实名认证
3. 申请ICP备案（中国大陆必需）

## 🖥️ 服务器环境配置

### 1. 连接服务器

```bash
# 使用SSH连接服务器
ssh root@你的服务器IP

# 首次连接需要确认指纹，输入yes
```

### 2. 更新系统

```bash
# Ubuntu系统
apt update && apt upgrade -y

# CentOS系统
yum update -y
```

### 3. 安装Docker

```bash
# 安装Docker
curl -fsSL https://get.docker.com | bash

# 启动Docker服务
systemctl start docker
systemctl enable docker

# 验证安装
docker --version
```

### 4. 安装Docker Compose

```bash
# 下载Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

### 5. 安装其他必要工具

```bash
# 安装Git
apt install git -y

# 安装Node.js (用于本地构建，可选)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# 安装Nginx (如果不使用Docker中的Nginx)
apt install nginx -y
```

## 📦 项目部署

### 1. 下载项目代码

#### 方法一：使用Git（推荐）
```bash
# 创建项目目录
mkdir -p /opt/factorysystem
cd /opt/factorysystem

# 克隆项目代码
git clone https://github.com/你的用户名/factorysystem.git .
```

#### 方法二：手动上传文件包（无Git）
```bash
# 创建项目目录
mkdir -p /opt/factorysystem
cd /opt/factorysystem

# 方式1：使用scp上传
scp -r ./factorysystem root@服务器IP:/opt/factorysystem

# 方式2：使用rsync上传
rsync -avz --exclude 'node_modules' --exclude '.git' ./factorysystem/ root@服务器IP:/opt/factorysystem/

# 方式3：打包上传
# 在本地打包
tar -czf factorysystem.tar.gz --exclude='node_modules' --exclude='.git' ./factorysystem
# 上传到服务器
scp factorysystem.tar.gz root@服务器IP:/opt/
# 在服务器解压
cd /opt && tar -xzf factorysystem.tar.gz && mv factorysystem/* /opt/factorysystem/
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.production .env

# 编辑环境变量
nano .env
```

**重要配置项说明：**

```bash
# 数据库配置
DATABASE_URL="***********************************************************/factorysystem"
DB_USER=factoryuser
DB_PASSWORD=your_strong_password_here

# NextAuth配置
NEXTAUTH_SECRET=your_nextauth_secret_key_32_chars_long
NEXTAUTH_URL=https://yourdomain.com

# Redis配置
REDIS_PASSWORD=your_redis_password_here

# 应用配置
NODE_ENV=production
PORT=3000
```

### 3. 修改Docker Compose配置

编辑 `docker-compose.yml` 文件，确保域名配置正确：

```bash
nano docker-compose.yml
```

### 4. 修改Nginx配置

编辑 `nginx.conf` 文件，替换域名：

```bash
nano nginx.conf

# 将 yourdomain.com 替换为你的实际域名
sed -i 's/yourdomain.com/你的域名.com/g' nginx.conf
```

### 5. 执行部署

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

部署脚本会自动执行以下步骤：
1. 构建Docker镜像
2. 启动数据库服务
3. 运行数据库迁移
4. 启动所有服务

### 6. 验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看应用日志
docker-compose logs app

# 测试应用访问
curl http://localhost:3000
```

## 🌐 域名和SSL配置

### 1. 配置DNS解析

在阿里云域名控制台添加DNS记录：

| 记录类型 | 主机记录 | 记录值 | TTL |
|----------|----------|--------|-----|
| A | @ | 服务器IP | 600 |
| A | www | 服务器IP | 600 |

### 2. 申请SSL证书

#### 方法一：使用Let's Encrypt免费证书

```bash
# 安装Certbot
apt install certbot python3-certbot-nginx -y

# 申请证书
certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 设置自动续期
crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 方法二：使用阿里云SSL证书

1. 在阿里云SSL证书服务中申请免费证书
2. 下载证书文件
3. 将证书文件上传到服务器 `/opt/factorysystem/ssl/` 目录

### 3. 更新Nginx配置

确保SSL证书路径正确：

```bash
# 编辑nginx.conf
nano nginx.conf

# 确认证书路径
ssl_certificate /etc/nginx/ssl/fullchain.pem;
ssl_certificate_key /etc/nginx/ssl/privkey.pem;
```

### 4. 重启服务

```bash
# 重启Nginx容器
docker-compose restart nginx

# 验证HTTPS访问
curl https://yourdomain.com
```

## 📊 监控和维护

### 1. 系统监控

```bash
# 给监控脚本执行权限
chmod +x monitor.sh

# 运行监控检查
./monitor.sh

# 设置定时监控
crontab -e
# 添加以下行：
# 0 * * * * /opt/factorysystem/monitor.sh >> /var/log/monitor.log
```

### 2. 数据备份

```bash
# 给备份脚本执行权限
chmod +x backup.sh

# 执行备份
./backup.sh

# 设置定时备份
crontab -e
# 添加以下行：
# 0 2 * * * /opt/factorysystem/backup.sh
```

### 3. 日志管理

```bash
# 查看应用日志
docker-compose logs -f app

# 查看Nginx日志
docker-compose logs -f nginx

# 查看数据库日志
docker-compose logs -f postgres

# 清理旧日志
docker system prune -f
```

### 4. 更新应用

```bash
# 拉取最新代码
git pull origin main

# 重新部署
./deploy.sh
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 应用无法启动

```bash
# 检查容器状态
docker-compose ps

# 查看错误日志
docker-compose logs app

# 重启应用
docker-compose restart app
```

#### 2. 数据库连接失败

```bash
# 检查数据库状态
docker-compose exec postgres pg_isready

# 检查数据库连接
docker-compose exec postgres psql -U factoryuser -d factorysystem

# 重启数据库
docker-compose restart postgres
```

#### 3. 内存不足

```bash
# 检查内存使用
free -h

# 清理Docker缓存
docker system prune -a

# 重启服务
docker-compose restart
```

#### 4. 端口被占用

```bash
# 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :443

# 杀死占用进程
kill -9 PID
```

#### 5. SSL证书问题

```bash
# 检查证书有效期
openssl x509 -in /path/to/cert.pem -text -noout

# 测试证书续期
certbot renew --dry-run

# 手动续期
certbot renew
```

## ⚡ 性能优化

### 1. 系统优化

```bash
# 优化系统参数
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'fs.file-max=65536' >> /etc/sysctl.conf
sysctl -p

# 配置防火墙
ufw enable
ufw allow ssh
ufw allow http
ufw allow https
```

### 2. Docker优化

```bash
# 配置Docker日志轮转
cat > /etc/docker/daemon.json << EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

# 重启Docker
systemctl restart docker
```

### 3. 数据库优化

在 `docker-compose.yml` 中添加PostgreSQL优化配置：

```yaml
postgres:
  command: >
    postgres
    -c shared_buffers=256MB
    -c effective_cache_size=1GB
    -c maintenance_work_mem=64MB
    -c checkpoint_completion_target=0.9
    -c wal_buffers=16MB
    -c default_statistics_target=100
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 检查配置文件是否正确
3. 确认网络和防火墙设置
4. 参考故障排除章节

---

**部署完成后，您的风口云平台将在以下地址可用：**
- HTTP: `http://你的域名.com`
- HTTPS: `https://你的域名.com`

🎉 **恭喜！您已成功部署风口云平台！**
