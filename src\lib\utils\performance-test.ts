/**
 * 性能测试工具
 * 用于测试优化后的DeepSeek API性能
 */

import { deepSeekAPI } from '../services/deepseek-api'

export interface PerformanceTestResult {
  testName: string
  inputText: string
  fastMode: boolean
  responseTime: number
  success: boolean
  confidence: number
  ventCount: number
  error?: string
}

/**
 * 性能测试套件
 */
export class PerformanceTestSuite {
  
  /**
   * 测试用例
   */
  private testCases = [
    {
      name: '简单风口识别',
      text: '2665×155  1525×255  3805×155'
    },
    {
      name: '复杂订单识别',
      text: `金色时代10栋1901号房
      客厅回风141*30     141*30      
      客出风568.2*14.5
      主卧出风300×150
      餐厅回风400×300`
    },
    {
      name: '多楼层识别',
      text: `19楼：客厅回风300×400  主卧出风200×150
      20楼：餐厅回风350×450  次卧出风180×120
      21楼：大厅回风500×600  小房间出风150×100`
    }
  ]

  /**
   * 运行性能测试
   */
  async runPerformanceTest(): Promise<PerformanceTestResult[]> {
    console.log('🚀 开始性能测试...')
    const results: PerformanceTestResult[] = []

    for (const testCase of this.testCases) {
      console.log(`📝 测试案例: ${testCase.name}`)

      // 测试快速模式
      const fastResult = await this.testSingleCase(
        testCase.name + ' (快速模式)',
        testCase.text,
        true
      )
      results.push(fastResult)

      // 测试详细模式
      const detailedResult = await this.testSingleCase(
        testCase.name + ' (详细模式)',
        testCase.text,
        false
      )
      results.push(detailedResult)

      // 等待一秒避免API限制
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    return results
  }

  /**
   * 测试单个案例
   */
  private async testSingleCase(
    testName: string,
    inputText: string,
    fastMode: boolean
  ): Promise<PerformanceTestResult> {
    const startTime = Date.now()

    try {
      console.log(`⚡ 测试 ${testName}...`)
      
      const result = await deepSeekAPI.analyzeVentOrder(inputText, 'deepseek-chat', fastMode)
      const responseTime = Date.now() - startTime

      // 计算风口数量
      let ventCount = 0
      result.projects.forEach(project => {
        project.floors.forEach(floor => {
          floor.rooms.forEach(room => {
            ventCount += room.vents.length
          })
        })
      })

      console.log(`✅ ${testName} 完成: ${responseTime}ms, 识别${ventCount}个风口`)

      return {
        testName,
        inputText,
        fastMode,
        responseTime,
        success: true,
        confidence: result.confidence,
        ventCount
      }

    } catch (error) {
      const responseTime = Date.now() - startTime
      console.error(`❌ ${testName} 失败: ${error}`)

      return {
        testName,
        inputText,
        fastMode,
        responseTime,
        success: false,
        confidence: 0,
        ventCount: 0,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 生成性能报告
   */
  generateReport(results: PerformanceTestResult[]): string {
    const fastResults = results.filter(r => r.fastMode)
    const detailedResults = results.filter(r => !r.fastMode)

    const avgFastTime = fastResults.reduce((sum, r) => sum + r.responseTime, 0) / fastResults.length
    const avgDetailedTime = detailedResults.reduce((sum, r) => sum + r.responseTime, 0) / detailedResults.length

    const speedImprovement = ((avgDetailedTime - avgFastTime) / avgDetailedTime * 100).toFixed(1)

    return `
📊 性能测试报告
================

🚀 快速模式平均响应时间: ${avgFastTime.toFixed(0)}ms
🔍 详细模式平均响应时间: ${avgDetailedTime.toFixed(0)}ms
⚡ 性能提升: ${speedImprovement}%

详细结果:
${results.map(r => 
  `${r.testName}: ${r.responseTime}ms ${r.success ? '✅' : '❌'} (${r.ventCount}个风口)`
).join('\n')}
    `.trim()
  }
}

/**
 * 快速性能测试
 */
export async function quickPerformanceTest(): Promise<string> {
  const testSuite = new PerformanceTestSuite()
  const results = await testSuite.runPerformanceTest()
  return testSuite.generateReport(results)
}
