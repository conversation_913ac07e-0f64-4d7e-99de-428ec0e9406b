/**
 * 🇨🇳 风口云平台 - 工厂设置API路由
 *
 * 处理工厂设置的获取和保存
 */

import { NextRequest, NextResponse } from 'next/server'
import { getFactorySettingsFromDB, saveFactorySettingsToDB, getDefaultSettingsForFactory } from '@/lib/utils/factory-settings'
import { db } from '@/lib/database'

/**
 * 获取工厂设置
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ factoryId: string }> }
) {
  try {
    const { factoryId } = await params

    // 首先从数据库获取工厂的基本信息
    const factory = await db.getFactoryById(factoryId)
    if (!factory) {
      return NextResponse.json(
        { error: '工厂不存在' },
        { status: 404 }
      )
    }

    // 获取工厂管理员信息
    const factoryUsers = await db.getEmployeesByFactoryId(factoryId)
    const owner = factoryUsers.find(user => user.role === 'owner')

    // 尝试从数据库获取设置
    const dbSettings = await getFactorySettingsFromDB(factoryId)

    if (dbSettings) {
      // 使用数据库中的工厂信息更新设置
      const updatedSettings = {
        ...dbSettings,
        factoryName: factory.name,
        factoryCode: factory.code,
        address: factory.address || dbSettings.address,
        contactPhone: factory.phone || dbSettings.contactPhone,
        email: factory.email || dbSettings.email,
        contactPerson: owner?.name || dbSettings.contactPerson
      }

      return NextResponse.json({
        success: true,
        settings: updatedSettings,
        factory: {
          id: factoryId,
          name: factory.name,
          code: factory.code,
          address: factory.address,
          phone: factory.phone,
          email: factory.email,
          ownerName: owner?.name
        }
      })
    }

    // 如果数据库中没有设置，使用工厂基本信息创建默认设置
    const defaultSettings = getDefaultSettingsForFactory(factoryId)
    const factoryBasedSettings = {
      ...defaultSettings,
      factoryName: factory.name,
      factoryCode: factory.code,
      address: factory.address || defaultSettings.address,
      contactPhone: factory.phone || defaultSettings.contactPhone,
      email: factory.email || defaultSettings.email,
      contactPerson: owner?.name || defaultSettings.contactPerson
    }

    return NextResponse.json({
      success: true,
      settings: factoryBasedSettings,
      factory: {
        id: factoryId,
        name: factory.name,
        code: factory.code,
        address: factory.address,
        phone: factory.phone,
        email: factory.email,
        ownerName: owner?.name
      }
    })
  } catch (error) {
    console.error('❌ 获取工厂设置失败:', error)
    return NextResponse.json(
      { error: '获取工厂设置失败' },
      { status: 500 }
    )
  }
}

/**
 * 保存工厂设置
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ factoryId: string }> }
) {
  try {
    const { factoryId } = await params
    const { settings } = await request.json()

    if (!settings) {
      return NextResponse.json(
        { error: '设置数据不能为空' },
        { status: 400 }
      )
    }

    // 更新设置的时间戳
    const updatedSettings = {
      ...settings,
      lastUpdated: new Date().toISOString(),
      updatedBy: 'api-update'
    }

    // 保存到数据库
    const success = await saveFactorySettingsToDB(factoryId, updatedSettings)

    if (success) {
      console.log(`✅ 工厂设置已保存: ${updatedSettings.factoryName}(${factoryId})`)
      console.log(`📝 订单号前缀: ${updatedSettings.orderNumberPrefix}`)

      return NextResponse.json({
        success: true,
        message: '工厂设置保存成功',
        factory: {
          id: factoryId,
          name: updatedSettings.factoryName,
          code: updatedSettings.factoryCode,
          address: updatedSettings.address,
          phone: updatedSettings.contactPhone,
          email: updatedSettings.email
        },
        settings: updatedSettings
      })
    } else {
      throw new Error('保存到数据库失败')
    }
  } catch (error) {
    console.error('❌ 保存工厂设置失败:', error)
    return NextResponse.json(
      { error: '保存工厂设置失败' },
      { status: 500 }
    )
  }
}
