# 豆包AI配置指南

## ❌ 当前问题

您遇到的错误：
```
Error: ❌ [豆包AI] API请求失败: 404 "InvalidEndpointOrModel.NotFound"
```

这个错误表明豆包AI的API Key不正确或模型无权限访问。

## 🔧 解决方案

### 步骤1：访问火山引擎控制台
1. 打开浏览器，访问：https://console.volcengine.com/ark
2. 使用您的火山引擎账号登录

### 步骤2：创建API Key
1. 在左侧菜单中找到"API管理"
2. 点击"创建API Key"按钮
3. 设置API Key名称（可以自定义）
4. 选择权限范围（确保包含豆包模型）
5. 点击"创建"按钮

### 步骤3：获取API Key
1. 创建成功后，您会看到一个以 `sk-` 开头的API Key
2. 格式类似：`sk-xxxxxxxxxxxxxxxxxxxxxxxx`
3. 复制这个完整的API Key（注意保密）

### 步骤4：更新项目配置
有两种方式配置API Key：

#### 方式1：环境变量配置（推荐）
在项目根目录创建或编辑 `.env.local` 文件：
```bash
NEXT_PUBLIC_DOUBAO_API_KEY=sk-你的实际api-key
```

#### 方式2：直接修改配置文件
编辑 `src/lib/config/api-keys.ts` 文件：
```typescript
export const API_KEYS = {
  // 豆包AI（火山引擎）API密钥
  DOUBAO: process.env.NEXT_PUBLIC_DOUBAO_API_KEY || 'sk-你的实际api-key'
}
```

## 🎯 重要说明

### 关于API Key
- 豆包AI使用标准的API Key格式（类似OpenAI）
- API Key格式：`sk-xxxxxxxxxxxxxxxxxxxxxxxx`
- API Key用于身份验证和权限控制
- 需要在火山引擎控制台中创建

### 关于费用
- 豆包AI按使用量计费
- 创建Endpoint本身不收费
- 只有实际调用API时才会产生费用
- 建议先小量测试，确认功能正常后再大量使用

## 🔍 验证配置

### 方法1：使用测试页面
1. 配置完成后，访问：`/test-doubao`
2. 点击"开始测试豆包AI"
3. 如果配置正确，应该能看到成功的响应

### 方法2：查看控制台日志
1. 打开浏览器开发者工具（F12）
2. 查看Console标签页
3. 正确配置时应该看到：
   ```
   🚀 [豆包AI] 开始API调用
   🚀 [豆包AI] 使用模型: ep-20250128-xxxxx
   ✅ [豆包AI] 连接测试成功
   ```

## 🚨 常见问题

### Q1: 找不到"模型推理"菜单
**A**: 确保您的火山引擎账号已开通方舟大模型服务。如果没有，需要先申请开通。

### Q2: 创建Endpoint时提示权限不足
**A**: 检查您的账号是否有足够的权限，可能需要联系管理员开通相关权限。

### Q3: Endpoint创建成功但仍然报错
**A**: 
1. 检查Endpoint ID是否完整复制
2. 确认Endpoint状态是"运行中"
3. 等待几分钟让Endpoint完全启动

### Q4: 想要使用不同的模型
**A**: 可以创建多个Endpoint，每个对应不同的模型，然后在代码中指定使用哪个Endpoint。

## 🔄 临时解决方案

如果暂时无法配置豆包AI，系统会自动回退到其他AI服务：

1. **通义千问**：阿里云的大模型服务
2. **DeepSeek**：深度求索的开源模型
3. **硅基流动**：提供多种模型的API服务

您可以先使用这些服务，等豆包AI配置完成后再切换。

## 📞 技术支持

如果遇到问题，可以：
1. 查看火山引擎官方文档：https://www.volcengine.com/docs/82379
2. 联系火山引擎技术支持
3. 在项目中提交Issue

## ✅ 配置检查清单

- [ ] 已访问火山引擎控制台
- [ ] 已开通方舟大模型服务
- [ ] 已创建推理接入点
- [ ] 已获得ep-开头的Endpoint ID
- [ ] 已更新项目配置文件
- [ ] 已通过测试页面验证配置
- [ ] 已确认API调用正常工作

完成以上步骤后，豆包AI应该能够正常工作了！
