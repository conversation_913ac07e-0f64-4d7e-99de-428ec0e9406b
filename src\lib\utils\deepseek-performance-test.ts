/**
 * DeepSeek模型性能测试工具
 */

interface PerformanceResult {
  model: string
  success: boolean
  responseTime: number
  tokenCount?: number
  error?: string
  responseSize?: number
  hasReasoningContent?: boolean
}

export class DeepSeekPerformanceTest {
  private apiKey: string
  private baseURL: string = 'https://api.deepseek.com/v1/chat/completions'

  constructor(apiKey: string = 'sk-c9047196d51d4f6c99dc94f9fbef602c') {
    this.apiKey = apiKey
  }

  /**
   * 测试单个模型的性能
   */
  async testModel(model: string, prompt: string, timeout: number = 60000): Promise<PerformanceResult> {
    console.log(`🧪 开始测试模型: ${model}`)
    console.log(`⏱️ 超时设置: ${timeout/1000}秒`)
    
    const startTime = Date.now()
    
    // 根据模型调整参数
    const isReasonerModel = model === 'deepseek-reasoner'
    
    const requestBody: any = {
      model: model,
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: isReasonerModel ? 4000 : 2000, // R1减少token数
      stream: false
    }

    // 只有非R1模型才添加temperature
    if (!isReasonerModel) {
      requestBody.temperature = 0.1
    }

    console.log(`📡 请求参数:`, {
      model: requestBody.model,
      max_tokens: requestBody.max_tokens,
      temperature: requestBody.temperature,
      prompt_length: prompt.length
    })

    // 添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => {
      console.log(`⏰ ${model} 模型超时 (${timeout/1000}秒)`)
      controller.abort()
    }, timeout)

    try {
      const response = await fetch(this.baseURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      const responseTime = Date.now() - startTime

      console.log(`📊 ${model} 响应时间: ${responseTime}ms (${(responseTime/1000).toFixed(1)}秒)`)

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`❌ ${model} API错误:`, response.status, errorText)
        
        return {
          model,
          success: false,
          responseTime,
          error: `HTTP ${response.status}: ${errorText}`
        }
      }

      const data = await response.json()
      console.log(`📦 ${model} 响应数据:`, {
        choices_count: data.choices?.length,
        has_reasoning: !!data.choices?.[0]?.message?.reasoning_content,
        content_length: data.choices?.[0]?.message?.content?.length,
        reasoning_length: data.choices?.[0]?.message?.reasoning_content?.length
      })

      // 计算响应大小
      const responseSize = JSON.stringify(data).length

      return {
        model,
        success: true,
        responseTime,
        tokenCount: data.usage?.total_tokens,
        responseSize,
        hasReasoningContent: !!data.choices?.[0]?.message?.reasoning_content
      }

    } catch (error: any) {
      clearTimeout(timeoutId)
      const responseTime = Date.now() - startTime

      console.error(`❌ ${model} 请求失败:`, error.message)

      let errorMessage = error.message
      if (error.name === 'AbortError') {
        errorMessage = `请求超时 (${timeout/1000}秒)`
      }

      return {
        model,
        success: false,
        responseTime,
        error: errorMessage
      }
    }
  }

  /**
   * 对比测试V3和R1模型
   */
  async compareModels(prompt: string): Promise<{
    v3: PerformanceResult
    r1: PerformanceResult
    comparison: {
      speedDifference: number
      r1SlowerBy: string
    }
  }> {
    console.log('🔄 开始对比测试 V3 vs R1 模型')
    console.log('📝 测试prompt长度:', prompt.length)

    // 测试V3模型 (30秒超时)
    const v3Result = await this.testModel('deepseek-chat', prompt, 30000)
    
    // 测试R1模型 (120秒超时，因为R1确实很慢)
    const r1Result = await this.testModel('deepseek-reasoner', prompt, 120000)

    // 计算性能差异
    let speedDifference = 0
    let r1SlowerBy = '无法计算'
    
    if (v3Result.success && r1Result.success) {
      speedDifference = r1Result.responseTime / v3Result.responseTime
      r1SlowerBy = `${speedDifference.toFixed(1)}倍`
    }

    const comparison = {
      speedDifference,
      r1SlowerBy
    }

    console.log('📊 对比结果:')
    console.log(`- V3模型: ${v3Result.success ? v3Result.responseTime + 'ms' : '失败'}`)
    console.log(`- R1模型: ${r1Result.success ? r1Result.responseTime + 'ms' : '失败'}`)
    console.log(`- R1比V3慢: ${r1SlowerBy}`)

    return {
      v3: v3Result,
      r1: r1Result,
      comparison
    }
  }

  /**
   * 简单的连通性测试
   */
  async testConnectivity(): Promise<boolean> {
    console.log('🔗 测试API连通性...')
    
    try {
      const result = await this.testModel('deepseek-chat', '你好', 10000)
      console.log('✅ API连通性正常')
      return result.success
    } catch (error) {
      console.error('❌ API连通性测试失败:', error)
      return false
    }
  }
}

// 导出测试函数供控制台使用
export async function testDeepSeekPerformance() {
  const tester = new DeepSeekPerformanceTest()
  
  // 先测试连通性
  const isConnected = await tester.testConnectivity()
  if (!isConnected) {
    console.error('❌ API连接失败，请检查网络和API Key')
    return
  }

  // 使用简单的测试prompt
  const testPrompt = `分析以下风口订单：
客厅回风141*30
客出风568*145
请返回JSON格式结果。`

  const results = await tester.compareModels(testPrompt)
  
  console.log('🎯 最终测试报告:')
  console.table([
    {
      模型: 'DeepSeek V3',
      成功: results.v3.success ? '✅' : '❌',
      响应时间: results.v3.success ? `${results.v3.responseTime}ms` : '失败',
      错误: results.v3.error || '-'
    },
    {
      模型: 'DeepSeek R1', 
      成功: results.r1.success ? '✅' : '❌',
      响应时间: results.r1.success ? `${results.r1.responseTime}ms` : '失败',
      错误: results.r1.error || '-',
      推理内容: results.r1.hasReasoningContent ? '✅' : '❌'
    }
  ])

  return results
}

// 在浏览器控制台中可以调用
if (typeof window !== 'undefined') {
  (window as any).testDeepSeekPerformance = testDeepSeekPerformance
}
