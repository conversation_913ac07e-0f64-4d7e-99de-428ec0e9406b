/**
 * 🇨🇳 风口云平台 - 主题选择器组件
 * 
 * 功能说明：
 * - 提供主题模式选择（浅色/深色/自动）
 * - 提供主题颜色选择
 * - 实时预览主题效果
 */

'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useTheme } from './theme-provider-simple'
import { useMounted } from '@/hooks/use-mounted'
import { THEME_PRESETS, type ThemeMode } from '@/lib/store/theme'
import {
  Sun,
  Moon,
  Monitor,
  Palette,
  Check,
  Eye
} from 'lucide-react'

interface ThemeSelectorProps {
  className?: string
}

export function ThemeSelector({ className }: ThemeSelectorProps) {
  const { mode, setTheme } = useTheme()
  const [previewMode, setPreviewMode] = useState<ThemeMode | null>(null)
  const mounted = useMounted()

  // 防止水合错误
  if (!mounted) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="h-5 w-5 mr-2" />
              主题设置
            </CardTitle>
            <CardDescription>正在加载主题设置...</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="animate-pulse space-y-4">
              <div className="h-20 bg-gray-200 rounded"></div>
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const themeOptions = [
    {
      value: 'light' as ThemeMode,
      label: '浅色主题',
      description: '适合白天使用',
      icon: Sun,
      preview: 'bg-white text-gray-900 border-gray-200'
    },
    {
      value: 'dark' as ThemeMode,
      label: '深色主题',
      description: '黑色背景，护眼舒适',
      icon: Moon,
      preview: 'bg-black text-white border-gray-800'
    },
    {
      value: 'auto' as ThemeMode,
      label: '跟随系统',
      description: '自动切换',
      icon: Monitor,
      preview: 'bg-gradient-to-r from-white to-gray-900 text-gray-900 border-gray-400'
    }
  ]

  const handleThemeChange = (newMode: ThemeMode) => {
    setTheme(newMode)
    setPreviewMode(null)
  }

  return (
    <div className={className}>
      {/* 主题模式选择 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Palette className="h-5 w-5 mr-2" />
            主题模式
          </CardTitle>
          <CardDescription>
            选择适合您工作环境的主题模式
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {themeOptions.map((option) => {
              const Icon = option.icon
              const isSelected = mode === option.value
              const isPreview = previewMode === option.value

              return (
                <div
                  key={option.value}
                  className={`
                    relative p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${isSelected
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }
                    ${isPreview ? 'ring-2 ring-blue-200 dark:ring-blue-800' : ''}
                  `}
                  onClick={() => handleThemeChange(option.value)}
                  onMouseEnter={() => setPreviewMode(option.value)}
                  onMouseLeave={() => setPreviewMode(null)}
                >
                  {isSelected && (
                    <div className="absolute top-2 right-2">
                      <Check className="h-4 w-4 text-blue-500" />
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`p-2 rounded-lg ${option.preview}`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-medium">{option.label}</h3>
                      <p className="text-sm text-gray-500">{option.description}</p>
                    </div>
                  </div>

                  {/* 主题预览 */}
                  <div className={`h-16 rounded border-2 ${option.preview} flex items-center justify-center`}>
                    <div className="text-xs font-medium">预览效果</div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 主题说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            主题说明
          </CardTitle>
          <CardDescription>
            了解不同主题模式的特点和使用场景
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">🌅 浅色主题</h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                适合白天使用，界面明亮清晰，减少屏幕反光影响，提供专业的工作体验。
              </p>
            </div>

            <div className="p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
              <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">🌙 深色主题</h4>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                纯黑色背景，白色文字，特别适合夜班工作环境，显著减少眼部疲劳和蓝光伤害。
              </p>
            </div>

            <div className="p-4 bg-purple-50 dark:bg-purple-950 border border-purple-200 dark:border-purple-800 rounded-lg">
              <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">🔄 跟随系统</h4>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                根据操作系统的主题设置自动切换，白天使用浅色主题，夜间自动切换到深色主题。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 当前主题信息 */}
      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <h4 className="font-medium mb-2">当前主题设置</h4>
        <div className="flex flex-wrap gap-2">
          <Badge variant="outline">
            模式: {themeOptions.find(t => t.value === mode)?.label}
          </Badge>
          <Badge variant="outline">
            状态: {mode === 'dark' ? '🌙 深色模式' : mode === 'light' ? '🌅 浅色模式' : '🔄 自动模式'}
          </Badge>
        </div>
      </div>
    </div>
  )
}
