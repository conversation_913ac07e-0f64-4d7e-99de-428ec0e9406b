// 导入Prisma类型
import type { Decimal } from '@prisma/client/runtime/library'

// 用户角色类型 - 与Prisma schema匹配
export type UserRole = 'owner' | 'manager' | 'employee'

// 平台总部管理员
export interface Admin {
  id: string
  username: string
  email: string
  name: string
  createdAt: Date
  updatedAt: Date
  userType: 'admin'
}

// 工厂信息
export interface Factory {
  id: string
  name: string
  code: string // 工厂编码
  address?: string | null
  phone?: string | null
  email?: string | null
  contactPhone?: string | null
  status: 'active' | 'inactive' | 'suspended' | 'expired'
  settings?: unknown // JSON字段

  // 使用时间管理字段
  subscriptionType: 'trial' | 'monthly' | 'quarterly' | 'yearly' | 'permanent'
  subscriptionStart?: Date | null
  subscriptionEnd?: Date | null
  firstLoginAt?: Date | null
  isPermanent: boolean
  suspendedAt?: Date | null
  suspendedReason?: string | null

  // 订阅管理增强字段
  lastStatusCheck?: Date | null
  autoSuspendEnabled?: boolean
  suspendedBy?: string | null
  reactivatedAt?: Date | null
  reactivatedBy?: string | null
  totalSuspendedMs?: bigint | number

  createdAt: Date
  updatedAt: Date
  // 统计数据（扩展字段，不在数据库中）
  totalClients?: number
  totalOrders?: number
  monthlyRevenue?: number
  // 管理员信息（扩展字段）
  ownerName?: string
}

// 工厂账号
export interface FactoryUser {
  id: string
  factoryId: string
  username: string
  passwordHash: string
  name: string // 真实姓名（老板名字）
  email?: string | null
  phone?: string | null
  role: UserRole
  permissions: unknown // JSON数组
  isActive: boolean
  lastLoginAt?: Date | null
  firstLoginAt?: Date | null // 首次登录时间
  createdAt: Date
  updatedAt: Date
  createdBy?: string | null
  userType: 'factory_user'
  // 统计数据（扩展字段，不在数据库中）
  totalOrders?: number
  thisMonthOrders?: number
}

// 员工角色类型
export type EmployeeRole = 'owner' | 'manager' | 'employee'

// 员工接口（继承自FactoryUser，用于员工管理界面）
export interface Employee extends FactoryUser {
  // 继承所有FactoryUser属性
}

// 客户信息
export interface Client {
  id: string
  factoryId: string
  name: string
  phone: string
  email?: string | null
  company?: string | null
  address?: string | null
  referrerId?: string | null
  referrerName?: string | null
  status: 'active' | 'inactive' | 'blacklisted'
  createdAt: Date
  updatedAt: Date
  userType: 'client'
  // 统计数据
  totalOrders?: number
  totalAmount?: number | Decimal
  referralCount?: number
  referralReward?: number | Decimal
  // 财务统计
  paidAmount?: number | Decimal // 已付总金额
  unpaidAmount?: number | Decimal // 未付总金额
  overdueAmount?: number | Decimal // 逾期金额
  lastOrderDate?: Date // 最后订单日期
  lastPaymentDate?: Date // 最后付款日期
  // 奖励相关
  availableReward?: number | Decimal // 可用奖励金额
  usedReward?: number | Decimal // 已使用奖励金额
  pendingReward?: number | Decimal // 待结算奖励金额
  canUseReward?: boolean // 是否可以使用奖励
  rewardHistory?: RewardRecord[] // 奖励记录
}

// 奖励记录
export interface RewardRecord {
  id: string
  clientId: string
  referredClientId: string
  referredClientName: string
  orderId: string
  orderAmount: number
  rewardAmount: number
  rewardType: 'regular' | 'premium' // 普通风口或高端风口奖励
  rewardDetails: string // 奖励详情说明
  status: 'pending' | 'available' | 'used' // 待结算、可用、已使用
  createdAt: Date
  settledAt?: Date // 结算时间
  usedAt?: Date // 使用时间
}

// 订单状态
export type OrderStatus = 'pending' | 'confirmed' | 'production' | 'completed' | 'cancelled'

// 付款状态
export type PaymentStatus = 'unpaid' | 'partial' | 'paid' | 'overdue'

// 付款方式 - 与Prisma schema匹配
export type PaymentMethod = 'CASH' | 'BANK_TRANSFER' | 'ALIPAY' | 'WECHAT' | 'CHECK' | 'OTHER'

// 订单信息
export interface Order {
  id: string
  factoryId: string
  clientId: string
  orderNumber: string
  items: OrderItem[]
  totalAmount: number | Decimal
  status: OrderStatus
  projectAddress?: string // 项目地址
  clientName?: string // 客户姓名（用于新客户）
  clientPhone?: string // 客户电话（用于新客户）
  notes?: string
  createdBy: string // 录单员工ID
  createdByName?: string // 录单员工姓名（计算字段，从关联的用户获取）
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  // 财务相关字段
  paymentStatus?: PaymentStatus // 付款状态
  paidAmount?: number | Decimal // 已付金额
  dueDate?: Date // 应付日期
  paymentNotes?: string // 付款备注
}

// 订单项目
export interface OrderItem {
  id: string
  productName: string
  productType: // 统一的产品类型系统
               // 常规风口类型 (元/㎡)
               'double_white_outlet' | 'double_black_outlet' | 'white_black_bottom_outlet' | 'white_black_bottom_return' |
               'white_return' | 'black_return' | 'white_linear' | 'black_linear' |
               'white_linear_return' | 'black_linear_return' | 'maintenance' |
               // 高端风口系列 (元/m)
               'high_end_white_outlet' | 'high_end_black_outlet' | 'high_end_white_return' | 'high_end_black_return' |
               'arrow_outlet' | 'arrow_return' | 'claw_outlet' | 'claw_return' |
               'black_white_dual_outlet' | 'black_white_dual_return' |
               'wood_grain_outlet' | 'wood_grain_return' |
               'white_putty_outlet' | 'white_putty_return' | 'black_putty_outlet' | 'black_putty_return' |
               // 石膏板风口 (元/m)
               'white_gypsum_outlet' | 'white_gypsum_return' | 'black_gypsum_outlet' | 'black_gypsum_return' |
               // 兼容字段
               string
  specifications: string
  // 尺寸信息
  dimensions: {
    length?: number // 长度(mm)
    width?: number  // 宽度(mm)
    height?: number // 高度(mm)
    diameter?: number // 直径(mm)
    thickness?: number // 厚度(mm)
  }
  // 兼容旧版本字段
  length?: number // 长度(mm) - 兼容字段
  width?: number  // 宽度(mm) - 兼容字段
  price?: number  // 单价 - 兼容字段
  type?: string   // 类型 - 兼容字段
  floor?: string // 楼层信息
  quantity: number
  unitPrice: number
  totalPrice: number
  calculatedBy?: string // 计算公式
  notes?: string
}

// 产品信息
export interface Product {
  id: string
  factoryId: string
  name: string
  category: string
  specifications: string[]
  unitPrice: number
  unit: string // 单位：个、米、平方米等
  description?: string
  status: 'active' | 'inactive'
  createdAt: Date
  updatedAt: Date
}

// 账单信息
export interface Bill {
  id: string
  factoryId: string
  clientId: string
  orderId: string
  billNumber: string
  amount: number
  status: 'unpaid' | 'paid' | 'overdue'
  dueDate: Date
  paidAt?: Date
  createdAt: Date
  updatedAt: Date
}

// 奖励使用记录接口
export interface RewardUsage {
  id: string
  clientId: string
  factoryId: string
  usageType: 'CASH_OUT' | 'ORDER_DISCOUNT' | 'TRANSFER' | 'OTHER'
  amount: number | Decimal
  description?: string | null
  relatedOrderId?: string | null
  approvedBy?: string | null
  approvedAt?: Date | null
  status: 'pending' | 'approved' | 'completed' | 'rejected' | 'cancelled'
  paymentMethod?: 'CASH' | 'BANK_TRANSFER' | 'ALIPAY' | 'WECHAT' | 'CHECK' | 'OTHER' | null
  paymentReference?: string | null
  paidAt?: Date | null
  notes?: string | null
  createdBy: string
  createdAt: Date
  updatedAt: Date
  // 关联数据
  client?: Client
  factory?: Factory
  relatedOrder?: Order
}

// 奖励使用类型 - 与Prisma schema匹配
export type RewardUsageType = 'CASH_OUT' | 'ORDER_DISCOUNT' | 'TRANSFER' | 'OTHER'

// 奖励使用状态
export type RewardUsageStatus = 'pending' | 'approved' | 'completed' | 'rejected' | 'cancelled'

// 股东管理相关类型
export interface Shareholder {
  id: string
  factoryId: string
  name: string
  idCard?: string
  phone?: string
  email?: string
  address?: string
  shareholderType: ShareholderType
  shareCount: number
  sharePercentage: number | Decimal
  investmentAmount: number | Decimal
  joinDate: Date
  exitDate?: Date
  status: ShareholderStatus
  notes?: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export type ShareholderType = 'FOUNDER' | 'INVESTOR' | 'EMPLOYEE' | 'PARTNER'
export type ShareholderStatus = 'active' | 'inactive' | 'exited'

export interface Dividend {
  id: string
  factoryId: string
  title: string
  description?: string
  totalAmount: number | Decimal
  baseAmount: number | Decimal
  dividendRate: number | Decimal
  periodStart: Date
  periodEnd: Date
  status: DividendStatus
  approvedAt?: Date
  distributedAt?: Date
  createdBy: string
  approvedBy?: string
  createdAt: Date
  updatedAt: Date
  records?: DividendRecord[]
}

export type DividendStatus = 'draft' | 'pending' | 'approved' | 'distributed' | 'cancelled'

export interface DividendRecord {
  id: string
  dividendId: string
  shareholderId: string
  shareCount: number
  sharePercentage: number | Decimal
  dividendAmount: number | Decimal
  taxAmount: number | Decimal
  actualAmount: number | Decimal
  paymentMethod?: PaymentMethod
  paymentReference?: string
  paidAt?: Date
  status: DividendRecordStatus
  notes?: string
  createdAt: Date
  updatedAt: Date
  shareholder?: Shareholder
}

export type DividendRecordStatus = 'pending' | 'paid' | 'cancelled'

// 股东架构统计
export interface ShareholderStats {
  totalShareholders: number
  totalShares: number
  totalInvestment: number
  shareholdersByType: {
    type: ShareholderType
    count: number
    percentage: number
    totalShares: number
  }[]
  shareholdersByStatus: {
    status: ShareholderStatus
    count: number
  }[]
}

// 成本分析
export interface CostAnalysis {
  id: string
  factoryId: string
  period: string // 统计周期：2024-01, 2024-Q1等
  materialCost: number
  laborCost: number
  overheadCost: number
  totalCost: number
  revenue: number
  profit: number
  profitMargin: number
  createdAt: Date
}

// 系统公告
export interface Announcement {
  id: string
  title: string
  content: string
  type: 'info' | 'warning' | 'urgent'
  targetFactories: string[] // 目标工厂ID列表，空数组表示全部
  publishedAt: Date
  expiresAt?: Date | null
  createdBy: string
}

// 数据统计
export interface DashboardStats {
  totalFactories: number
  activeFactories: number
  totalClients: number
  totalOrders: number
  totalRevenue: number
  monthlyGrowth: number
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}



// 员工权限
export interface EmployeePermission {
  module: string // 模块名称
  actions: string[] // 允许的操作：create, read, update, delete
}

// 操作日志
export interface OperationLog {
  id: string
  factoryId: string
  userId: string
  userName: string
  action: string // 操作类型：create_order, update_order, delete_order等
  targetType: string // 目标类型：order, client, product等
  targetId: string // 目标ID
  details: Record<string, unknown> // 操作详情
  ipAddress?: string
  userAgent?: string
  createdAt: Date
}

// 用户联合类型
export type User = Admin | FactoryUser | Client

// 用户类型检查函数
export function isAdmin(user: User): user is Admin {
  return user.userType === 'admin'
}

export function isFactoryUser(user: User): user is FactoryUser {
  return user.userType === 'factory_user'
}

export function isClient(user: User): user is Client {
  return user.userType === 'client'
}

// 获取用户名的辅助函数
export function getUserUsername(user: User): string | undefined {
  if (isAdmin(user) || isFactoryUser(user)) {
    return user.username
  }
  return undefined
}

// Decimal类型处理工具函数
export function toNumber(value: number | Decimal | null | undefined): number {
  if (value === null || value === undefined) return 0
  if (typeof value === 'number') return value
  return Number(value.toString())
}

export function toDecimal(value: number | Decimal | null | undefined): Decimal {
  if (value === null || value === undefined) {
    return 0 as any as Decimal
  }
  if (typeof value === 'number') {
    return value as any as Decimal
  }
  return value as Decimal
}

// 登录记录相关类型
export type UserType = 'admin' | 'factory_user' | 'client'
export type LoginStatus = 'success' | 'failed' | 'blocked'

// 登录记录
export interface LoginRecord {
  id: string
  userId: string
  userType: UserType
  username: string
  userName: string // 用户真实姓名
  factoryId?: string // 工厂ID（仅工厂用户）
  factoryName?: string // 工厂名称

  // 登录信息
  loginTime: Date
  ipAddress?: string
  userAgent?: string
  deviceInfo?: string // 设备信息
  location?: string // 登录地点

  // 登录状态
  loginStatus: LoginStatus
  failReason?: string // 失败原因

  // 会话信息
  sessionId?: string
  logoutTime?: Date
  sessionDuration?: number // 会话时长（秒）

  createdAt: Date
}

// 登录统计
export interface LoginStats {
  totalLogins: number
  successfulLogins: number
  failedLogins: number
  uniqueUsers: number
  averageSessionDuration: number
  peakLoginHour: number
  loginsByHour: { hour: number; count: number }[]
  loginsByDay: { date: string; count: number }[]
  topUsers: { userId: string; userName: string; loginCount: number }[]
}

// 在线用户
export interface OnlineUser {
  userId: string
  userName: string
  userType: UserType
  factoryId?: string
  factoryName?: string
  loginTime: Date
  lastActivity: Date
  ipAddress?: string
  deviceInfo?: string
}
