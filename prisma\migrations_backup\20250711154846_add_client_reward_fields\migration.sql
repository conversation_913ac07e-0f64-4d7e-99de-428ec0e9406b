/*
  Warnings:

  - A unique constraint covering the columns `[factory_id,phone]` on the table `clients` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "clients" ADD COLUMN "available_reward" REAL DEFAULT 0;
ALTER TABLE "clients" ADD COLUMN "last_order_date" DATETIME;
ALTER TABLE "clients" ADD COLUMN "last_payment_date" DATETIME;
ALTER TABLE "clients" ADD COLUMN "overdue_amount" REAL DEFAULT 0;
ALTER TABLE "clients" ADD COLUMN "paid_amount" REAL DEFAULT 0;
ALTER TABLE "clients" ADD COLUMN "pending_reward" REAL DEFAULT 0;
ALTER TABLE "clients" ADD COLUMN "referral_count" INTEGER DEFAULT 0;
ALTER TABLE "clients" ADD COLUMN "referral_reward" REAL DEFAULT 0;
ALTER TABLE "clients" ADD COLUMN "total_amount" REAL DEFAULT 0;
ALTER TABLE "clients" ADD COLUMN "total_orders" INTEGER DEFAULT 0;
ALTER TABLE "clients" ADD COLUMN "unpaid_amount" REAL DEFAULT 0;
ALTER TABLE "clients" ADD COLUMN "used_reward" REAL DEFAULT 0;

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_announcement_read_status" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "announcement_id" TEXT NOT NULL,
    "factory_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "is_read" BOOLEAN NOT NULL DEFAULT true,
    "is_dismissed" BOOLEAN NOT NULL DEFAULT false,
    "read_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "announcement_read_status_announcement_id_fkey" FOREIGN KEY ("announcement_id") REFERENCES "announcements" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "announcement_read_status_factory_id_fkey" FOREIGN KEY ("factory_id") REFERENCES "factories" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "announcement_read_status_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "factory_users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_announcement_read_status" ("announcement_id", "factory_id", "id", "read_at", "user_id") SELECT "announcement_id", "factory_id", "id", "read_at", "user_id" FROM "announcement_read_status";
DROP TABLE "announcement_read_status";
ALTER TABLE "new_announcement_read_status" RENAME TO "announcement_read_status";
CREATE UNIQUE INDEX "announcement_read_status_announcement_id_user_id_key" ON "announcement_read_status"("announcement_id", "user_id");
CREATE TABLE "new_factories" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "address" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "settings" TEXT,
    "subscription_type" TEXT NOT NULL DEFAULT 'trial',
    "subscription_start" DATETIME,
    "subscription_end" DATETIME,
    "first_login_at" DATETIME,
    "is_permanent" BOOLEAN NOT NULL DEFAULT false,
    "suspended_at" DATETIME,
    "suspended_reason" TEXT,
    "last_status_check" DATETIME,
    "auto_suspend_enabled" BOOLEAN NOT NULL DEFAULT true,
    "suspended_by" TEXT,
    "reactivated_at" DATETIME,
    "reactivated_by" TEXT,
    "total_suspended_ms" BIGINT NOT NULL DEFAULT 0,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);
INSERT INTO "new_factories" ("address", "code", "created_at", "email", "first_login_at", "id", "is_permanent", "name", "phone", "settings", "status", "subscription_end", "subscription_start", "subscription_type", "suspended_at", "suspended_reason", "updated_at") SELECT "address", "code", "created_at", "email", "first_login_at", "id", "is_permanent", "name", "phone", "settings", "status", "subscription_end", "subscription_start", "subscription_type", "suspended_at", "suspended_reason", "updated_at" FROM "factories";
DROP TABLE "factories";
ALTER TABLE "new_factories" RENAME TO "factories";
CREATE UNIQUE INDEX "factories_code_key" ON "factories"("code");
CREATE INDEX "factories_status_idx" ON "factories"("status");
CREATE INDEX "factories_subscription_end_idx" ON "factories"("subscription_end");
CREATE INDEX "factories_subscription_type_idx" ON "factories"("subscription_type");
CREATE INDEX "factories_suspended_at_idx" ON "factories"("suspended_at");
CREATE TABLE "new_login_records" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "user_id" TEXT NOT NULL,
    "user_type" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "user_name" TEXT NOT NULL,
    "factory_id" TEXT,
    "factory_name" TEXT,
    "login_time" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "device_info" TEXT,
    "location" TEXT,
    "login_status" TEXT NOT NULL,
    "fail_reason" TEXT,
    "session_id" TEXT,
    "logout_time" DATETIME,
    "session_duration" INTEGER,
    "session_status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "invalidated_by" TEXT,
    "invalidated_at" DATETIME,
    "invalidate_reason" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO "new_login_records" ("created_at", "device_info", "factory_id", "factory_name", "fail_reason", "id", "ip_address", "location", "login_status", "login_time", "logout_time", "session_duration", "session_id", "user_agent", "user_id", "user_name", "user_type", "username") SELECT "created_at", "device_info", "factory_id", "factory_name", "fail_reason", "id", "ip_address", "location", "login_status", "login_time", "logout_time", "session_duration", "session_id", "user_agent", "user_id", "user_name", "user_type", "username" FROM "login_records";
DROP TABLE "login_records";
ALTER TABLE "new_login_records" RENAME TO "login_records";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE UNIQUE INDEX "clients_factory_id_phone_key" ON "clients"("factory_id", "phone");
