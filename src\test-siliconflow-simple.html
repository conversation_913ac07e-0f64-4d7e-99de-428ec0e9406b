<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硅基流动 API 快速测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover { background: #1976D2; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #4CAF50; background: #e8f5e8; }
        .error { border-color: #f44336; background: #ffe8e8; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        .highlight { background: #ffeb3b; padding: 2px 4px; border-radius: 2px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 硅基流动 DeepSeek V3 快速测试</h1>
        <p>测试硅基流动的 DeepSeek V3 模型，验证速度和准确性。</p>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 4px; margin: 15px 0;">
            <strong>🎯 测试目标：</strong><br>
            • 验证API连接和响应速度<br>
            • 测试风口类型识别准确性<br>
            • 对比与DeepSeek官方API的性能差异<br>
            • 验证临界值处理（255mm边界情况）
        </div>
        
        <button class="button" onclick="testSiliconFlowSpeed()">⚡ 测试硅基流动速度</button>
        <button class="button" onclick="testAccuracy()">🎯 测试识别准确性</button>
        <button class="button" onclick="testBoundaryCase()">🔍 测试边界情况</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_KEY = 'sk-szczomdkrprhzlzuzwlblenwfvvuuuyxbxnjgmrcetorftth';
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}] ${title}</strong>\n${content}`;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function callSiliconFlowAPI(prompt, testName) {
            const startTime = performance.now();
            
            const requestBody = {
                model: 'deepseek-ai/DeepSeek-V3',
                messages: [{ role: 'user', content: prompt }],
                max_tokens: 1500,
                temperature: 0.1,
                stream: false
            };

            try {
                const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify(requestBody)
                });

                const endTime = performance.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0]?.message?.content;
                    
                    return {
                        success: true,
                        duration: duration,
                        content: content,
                        usage: data.usage,
                        model: data.model
                    };
                } else {
                    const errorText = await response.text();
                    return {
                        success: false,
                        duration: duration,
                        error: `API请求失败: ${response.status} - ${errorText}`
                    };
                }
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                return {
                    success: false,
                    duration: duration,
                    error: `网络错误: ${error.message}`
                };
            }
        }
        
        async function testSiliconFlowSpeed() {
            addResult('🚀 硅基流动速度测试', '开始测试API响应速度...', 'info');
            
            const testText = `项目：速度测试项目
一楼：
出风口 2665×155 白色 1个
回风口 1200×300 白色 1个`;

            const prompt = `识别风口订单，返回JSON。

文本：${testText}

规则：
1. 风口类型：宽度≤254mm为出风口，宽度≥255mm为回风口
2. 必须返回完整JSON格式

返回格式：
{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet","dimensions":{"length":2665,"width":155}}]}]}]}]}`;

            const result = await callSiliconFlowAPI(prompt, '速度测试');
            
            if (result.success) {
                const speedRating = result.duration < 5000 ? '🟢 极快' : 
                                  result.duration < 10000 ? '🟡 较快' : 
                                  result.duration < 30000 ? '🟠 一般' : '🔴 较慢';
                
                const cost = result.usage ? 
                    ((result.usage.prompt_tokens * 2.00 + result.usage.completion_tokens * 8.00) / 1000000).toFixed(4) : 'N/A';
                
                addResult(
                    '✅ 速度测试成功',
                    `响应时间: ${result.duration.toFixed(2)}ms (${(result.duration/1000).toFixed(2)}秒)
速度评级: ${speedRating}
使用模型: ${result.model}
Token使用: ${result.usage?.total_tokens || 'N/A'}
预估费用: ¥${cost}

AI响应内容:
${result.content}`,
                    'success'
                );
            } else {
                addResult('❌ 速度测试失败', result.error, 'error');
            }
        }
        
        async function testAccuracy() {
            addResult('🎯 准确性测试', '测试风口类型识别准确性...', 'info');
            
            const testText = `项目：准确性测试
一楼：
出风口 2665×155 白色 1个
出风口 1600×200 白色 1个
回风口 1200×300 白色 1个
回风口 800×400 白色 1个`;

            const prompt = `识别风口订单，返回JSON。

文本：${testText}

规则：
1. 风口类型（严格按尺寸判断）：
   - 宽度≤254mm → "systemType": "double_white_outlet", "originalType": "出风口"
   - 宽度≥255mm → "systemType": "white_return", "originalType": "回风口"

必须返回完整JSON：
{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet","originalType":"出风口","dimensions":{"length":2665,"width":155,"unit":"mm"},"color":"white","quantity":1}]}]}]}]}`;

            const result = await callSiliconFlowAPI(prompt, '准确性测试');
            
            if (result.success) {
                try {
                    // 解析JSON
                    let jsonStr = result.content.trim();
                    jsonStr = jsonStr.replace(/^```json\s*/i, '');
                    jsonStr = jsonStr.replace(/\s*```\s*$/, '');
                    
                    const firstBrace = jsonStr.indexOf('{');
                    const lastBrace = jsonStr.lastIndexOf('}');
                    
                    if (firstBrace !== -1 && lastBrace !== -1) {
                        jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
                    }
                    
                    const parsed = JSON.parse(jsonStr);
                    const vents = parsed.projects?.[0]?.floors?.[0]?.rooms?.[0]?.vents || [];
                    
                    let correctCount = 0;
                    let validationResults = [];
                    
                    const expectedResults = [
                        { width: 155, expected: 'double_white_outlet' },
                        { width: 200, expected: 'double_white_outlet' },
                        { width: 300, expected: 'white_return' },
                        { width: 400, expected: 'white_return' }
                    ];
                    
                    vents.forEach((vent, index) => {
                        const width = vent.dimensions?.width || 0;
                        const systemType = vent.systemType;
                        const expected = expectedResults[index];
                        
                        if (expected && width === expected.width) {
                            const isCorrect = systemType === expected.expected;
                            if (isCorrect) correctCount++;
                            
                            validationResults.push(
                                `风口${index + 1}: ${vent.dimensions?.length}×${width}mm → ${systemType} ${isCorrect ? '✅' : '❌'}`
                            );
                        }
                    });
                    
                    const accuracy = vents.length > 0 ? (correctCount / vents.length * 100).toFixed(1) : 0;
                    const accuracyRating = accuracy == 100 ? '🟢 完美' : 
                                         accuracy >= 75 ? '🟡 良好' : '🔴 需改进';
                    
                    addResult(
                        '🎯 准确性测试结果',
                        `识别准确性: ${correctCount}/${vents.length} (${accuracy}%)
准确性评级: ${accuracyRating}

详细验证结果:
${validationResults.join('\n')}

解析后的数据:
${JSON.stringify(parsed, null, 2)}`,
                        accuracy >= 75 ? 'success' : 'warning'
                    );
                    
                } catch (parseError) {
                    addResult('❌ JSON解析失败', `解析错误: ${parseError.message}\n原始内容: ${result.content}`, 'error');
                }
            } else {
                addResult('❌ 准确性测试失败', result.error, 'error');
            }
        }
        
        async function testBoundaryCase() {
            addResult('🔍 边界情况测试', '测试255mm临界值处理...', 'info');
            
            const testText = `项目：边界测试
一楼：
出风口 1600×254 白色 1个
回风口 1200×255 白色 1个
出风口 800×100 白色 1个`;

            const prompt = `识别风口订单，返回JSON。

文本：${testText}

规则：
1. 风口类型（严格按尺寸判断）：
   - 宽度≤254mm → "systemType": "double_white_outlet", "originalType": "出风口"
   - 宽度≥255mm → "systemType": "white_return", "originalType": "回风口"

必须返回完整JSON：
{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet","originalType":"出风口","dimensions":{"length":1600,"width":254,"unit":"mm"},"color":"white","quantity":1}]}]}]}]}`;

            const result = await callSiliconFlowAPI(prompt, '边界测试');
            
            if (result.success) {
                try {
                    let jsonStr = result.content.trim();
                    jsonStr = jsonStr.replace(/^```json\s*/i, '');
                    jsonStr = jsonStr.replace(/\s*```\s*$/, '');
                    
                    const firstBrace = jsonStr.indexOf('{');
                    const lastBrace = jsonStr.lastIndexOf('}');
                    
                    if (firstBrace !== -1 && lastBrace !== -1) {
                        jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
                    }
                    
                    const parsed = JSON.parse(jsonStr);
                    const vents = parsed.projects?.[0]?.floors?.[0]?.rooms?.[0]?.vents || [];
                    
                    let boundaryResults = [];
                    const testCases = [
                        { width: 254, expected: 'double_white_outlet', description: '254mm (边界-出风口)' },
                        { width: 255, expected: 'white_return', description: '255mm (边界-回风口)' },
                        { width: 100, expected: 'double_white_outlet', description: '100mm (明确出风口)' }
                    ];
                    
                    let correctBoundary = 0;
                    
                    vents.forEach((vent, index) => {
                        const width = vent.dimensions?.width || 0;
                        const systemType = vent.systemType;
                        const testCase = testCases[index];
                        
                        if (testCase && width === testCase.width) {
                            const isCorrect = systemType === testCase.expected;
                            if (isCorrect) correctBoundary++;
                            
                            boundaryResults.push(
                                `${testCase.description}: ${systemType} ${isCorrect ? '✅' : '❌'}`
                            );
                        }
                    });
                    
                    const boundaryAccuracy = testCases.length > 0 ? (correctBoundary / testCases.length * 100).toFixed(1) : 0;
                    
                    addResult(
                        '🔍 边界测试结果',
                        `边界处理准确性: ${correctBoundary}/${testCases.length} (${boundaryAccuracy}%)

关键测试点:
${boundaryResults.join('\n')}

${boundaryAccuracy == 100 ? '🎉 边界处理完美！' : '⚠️ 边界处理需要注意'}`,
                        boundaryAccuracy >= 66 ? 'success' : 'warning'
                    );
                    
                } catch (parseError) {
                    addResult('❌ 边界测试解析失败', `解析错误: ${parseError.message}`, 'error');
                }
            } else {
                addResult('❌ 边界测试失败', result.error, 'error');
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult(
                '📋 测试说明',
                `硅基流动 API Key: ${API_KEY.substring(0, 20)}...
测试模型: deepseek-ai/DeepSeek-V3
测试内容: 速度、准确性、边界情况

点击上方按钮开始测试！`,
                'info'
            );
        };
    </script>
</body>
</html>
