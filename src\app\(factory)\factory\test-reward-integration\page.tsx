'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Calculator, CheckCircle, XCircle, Loader2 } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'

interface TestResult {
  testName: string
  status: 'pass' | 'fail' | 'running'
  expected: string
  actual: string
  description: string
}

export default function TestRewardIntegrationPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [orderAmount, setOrderAmount] = useState('150000') // 15万元，应该是3%

  const updateResult = (testName: string, status: TestResult['status'], expected: string, actual: string, description: string) => {
    setTestResults(prev => {
      const newResults = [...prev]
      const existingIndex = newResults.findIndex(r => r.testName === testName)
      if (existingIndex >= 0) {
        newResults[existingIndex] = { testName, status, expected, actual, description }
      } else {
        newResults.push({ testName, status, expected, actual, description })
      }
      return newResults
    })
  }

  const runIntegrationTest = async () => {
    setIsRunning(true)
    setTestResults([])

    try {
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        throw new Error('无法获取工厂ID')
      }

      const amount = parseFloat(orderAmount)
      const expectedRate = amount >= 200000 ? 0.04 : amount >= 100000 ? 0.03 : 0.02
      const expectedReward = amount * expectedRate

      // 测试1: 创建推荐人
      updateResult('创建推荐人', 'running', '成功创建', '进行中...', '创建一个推荐人客户')
      
      const referrerData = {
        factoryId,
        name: `测试推荐人-${Date.now()}`,
        phone: `138${Date.now().toString().slice(-8)}`,
        email: '',
        company: '',
        address: '测试地址',
        status: 'active' as const,
        totalOrders: 0,
        totalAmount: 0,
        referralCount: 0,
        referralReward: 0
      }

      const referrer = await db.createClient(referrerData)
      if (!referrer) {
        updateResult('创建推荐人', 'fail', '成功创建', '创建失败', '无法创建推荐人客户')
        return
      }

      updateResult('创建推荐人', 'pass', '成功创建', `${referrer.name} (${referrer.id})`, '推荐人创建成功')

      // 测试2: 创建被推荐人
      updateResult('创建被推荐人', 'running', '成功创建', '进行中...', '创建一个被推荐人客户')
      
      const referredData = {
        factoryId,
        name: `测试被推荐人-${Date.now()}`,
        phone: `139${Date.now().toString().slice(-8)}`,
        email: '',
        company: '',
        address: '测试地址',
        status: 'active' as const,
        totalOrders: 0,
        totalAmount: 0,
        referralCount: 0,
        referralReward: 0,
        referrerId: referrer.id,
        referrerName: referrer.name
      }

      const referred = await db.createClient(referredData)
      if (!referred) {
        updateResult('创建被推荐人', 'fail', '成功创建', '创建失败', '无法创建被推荐人客户')
        return
      }

      updateResult('创建被推荐人', 'pass', '成功创建', `${referred.name} (${referred.id})`, '被推荐人创建成功')

      // 测试3: 创建订单
      updateResult('创建订单', 'running', '成功创建', '进行中...', '为被推荐人创建订单')
      
      const orderData = {
        factoryId,
        clientId: referred.id,
        clientName: referred.name,
        clientPhone: referred.phone,
        orderNumber: `TEST-${Date.now()}`,
        items: [
          {
            productType: 'square',
            productName: '普通出风口',
            specifications: '600×400mm',
            quantity: 1,
            unitPrice: amount,
            totalPrice: amount
          }
        ],
        totalAmount: amount,
        status: 'pending',
        projectAddress: '测试地址',
        notes: '阶梯式奖励测试订单',
        createdBy: 'test-user',
        createdByName: '测试用户'
      }

      const order = await db.createOrder(orderData)
      if (!order) {
        updateResult('创建订单', 'fail', '成功创建', '创建失败', '无法创建订单')
        return
      }

      updateResult('创建订单', 'pass', '成功创建', `${order.orderNumber} (¥${amount.toLocaleString()})`, '订单创建成功')

      // 测试4: 等待奖励计算
      updateResult('奖励计算', 'running', `¥${expectedReward.toFixed(2)}`, '计算中...', '等待系统自动计算推荐奖励')
      
      // 等待3秒确保自动更新完成
      await new Promise(resolve => setTimeout(resolve, 3000))

      const updatedReferrer = await db.getClientById(referrer.id)
      if (!updatedReferrer) {
        updateResult('奖励计算', 'fail', `¥${expectedReward.toFixed(2)}`, '无法获取数据', '无法获取更新后的推荐人信息')
        return
      }

      const actualReward = updatedReferrer.referralReward || 0
      const isCorrect = Math.abs(actualReward - expectedReward) < 1

      updateResult(
        '奖励计算', 
        isCorrect ? 'pass' : 'fail',
        `¥${expectedReward.toFixed(2)} (${(expectedRate * 100).toFixed(1)}%)`,
        `¥${actualReward.toFixed(2)}`,
        `验证阶梯式奖励计算是否正确`
      )

      // 测试5: 清理测试数据
      updateResult('清理数据', 'running', '成功清理', '清理中...', '清理测试数据')
      
      try {
        await db.deleteOrder(order.id)
        await db.deleteClient(referred.id)
        await db.deleteClient(referrer.id)
        updateResult('清理数据', 'pass', '成功清理', '已清理', '测试数据清理完成')
      } catch (error) {
        updateResult('清理数据', 'fail', '成功清理', `清理失败: ${error.message}`, '清理测试数据时出错')
      }

    } catch (error) {
      console.error('❌ 集成测试失败:', error)
      updateResult('测试执行', 'fail', '正常执行', `错误: ${error.message}`, '测试执行过程中发生错误')
    } finally {
      setIsRunning(false)
    }
  }

  const passedTests = testResults.filter(r => r.status === 'pass').length
  const totalTests = testResults.filter(r => r.status !== 'running').length

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">阶梯式奖励集成测试</h1>
            <p className="text-muted-foreground">测试完整的推荐奖励流程，验证阶梯式计算是否正确</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              测试参数
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Label htmlFor="orderAmount">订单金额</Label>
                <Input
                  id="orderAmount"
                  value={orderAmount}
                  onChange={(e) => setOrderAmount(e.target.value)}
                  placeholder="订单金额"
                  type="number"
                />
              </div>
              <div className="flex items-end">
                <Button 
                  onClick={runIntegrationTest} 
                  disabled={isRunning}
                  className="flex items-center gap-2"
                >
                  {isRunning ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      测试中...
                    </>
                  ) : (
                    <>
                      <Calculator className="h-4 w-4" />
                      开始集成测试
                    </>
                  )}
                </Button>
              </div>
            </div>
            
            {testResults.length > 0 && (
              <div className="mt-4 flex items-center gap-2">
                <Badge variant={passedTests === totalTests && totalTests >0 ? "default" : "destructive"}>
                  {passedTests}/{totalTests} 通过
                </Badge>
                {passedTests === totalTests && totalTests > 0 && (
                  <span className="text-green-600 text-sm">✅ 集成测试通过！</span>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>测试结果</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div 
                    key={index}
                    className={`p-4 rounded-lg border ${
                      result.status === 'pass' 
                        ? 'border-green-200 bg-green-50' 
                        : result.status === 'fail'
                        ? 'border-red-200 bg-red-50'
                        : 'border-blue-200 bg-blue-50'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      {result.status === 'pass' ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : result.status === 'fail' ? (
                        <XCircle className="h-5 w-5 text-red-600" />
                      ) : (
                        <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
                      )}
                      <h3 className="font-semibold">{result.testName}</h3>
                      <Badge variant={
                        result.status === 'pass' ? "default" : 
                        result.status === 'fail' ? "destructive" : "secondary"
                      }>
                        {result.status === 'pass' ? '通过' : 
                         result.status === 'fail' ? '失败' : '进行中'}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{result.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">期望结果: </span>
                        <span className="text-green-700">{result.expected}</span>
                      </div>
                      <div>
                        <span className="font-medium">实际结果: </span>
                        <span className={result.status === 'pass' ? 'text-green-700' : 
                                       result.status === 'fail' ? 'text-red-700' : 'text-blue-700'}>
                          {result.actual}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>测试说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>测试流程：</strong></p>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>创建推荐人客户</li>
                <li>创建被推荐人客户（设置推荐关系）</li>
                <li>为被推荐人创建指定金额的订单</li>
                <li>验证推荐人的奖励是否按阶梯式规则正确计算</li>
                <li>清理测试数据</li>
              </ol>
              
              <p className="mt-4"><strong>阶梯式奖励规则：</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>&lt; 10万：2%</li>
                <li>≥ 10万：3%</li>
                <li>≥ 20万：4%</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
