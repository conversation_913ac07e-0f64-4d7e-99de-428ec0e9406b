<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Prompt 查看器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .prompt-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
        }
        .prompt-header {
            background: #2196F3;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 18px;
        }
        .prompt-content {
            background: #f8f9fa;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 600px;
            overflow-y: auto;
            border-top: 1px solid #ddd;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .warning {
            background: #f8d7da;
            padding: 2px 4px;
            border-radius: 3px;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            padding: 2px 4px;
            border-radius: 3px;
            color: #155724;
        }
        .tabs {
            display: flex;
            background: #f1f1f1;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }
        .tab {
            flex: 1;
            padding: 15px 20px;
            background: #e9ecef;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background 0.3s;
        }
        .tab.active {
            background: #2196F3;
            color: white;
        }
        .tab:hover {
            background: #1976D2;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .test-input {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Prompt 查看器</h1>
        <p>查看当前系统使用的 AI Prompt 模板，了解 AI 是如何识别风口订单的。</p>
        
        <div class="info-box">
            <strong>📋 当前配置：</strong><br>
            • 默认使用：<span class="highlight">快速模式 Prompt</span><br>
            • 模型：<span class="success">deepseek-chat (V3)</span><br>
            • Max Tokens：<span class="highlight">1500</span><br>
            • 后处理：<span class="success">启用风口类型验证</span>
        </div>

        <div class="prompt-section">
            <div class="tabs">
                <button class="tab active" onclick="showTab('fast')">快速模式 Prompt</button>
                <button class="tab" onclick="showTab('detailed')">详细模式 Prompt</button>
                <button class="tab" onclick="showTab('test')">测试 Prompt</button>
            </div>
            
            <div id="fast" class="tab-content active">
                <div class="prompt-content">识别风口订单，返回JSON。

文本：${text}

规则：
1. 风口类型：
   - 出风口或宽度&lt;255mm → "systemType": "double_white_outlet", "originalType": "出风口"
   - 回风口或宽度≥255mm → "systemType": "white_return", "originalType": "回风口"

2. 尺寸：数字&lt;100为厘米需×10，大值为length，小值为width

3. 房间：提取具体房间名（大厅、办公室、餐厅等），无明确房间用"房间"

必须返回完整JSON：
{
  "projects": [{
    "projectName": "",
    "clientInfo": "",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          }
        ]
      }]
    }]
  }]
}</div>
            </div>
            
            <div id="detailed" class="tab-content">
                <div class="prompt-content">你是专业的风口订单识别专家。请仔细分析文本，识别出每一个风口。

⚠️ 重要要求：
1. 必须识别文本中的每一个风口，包括重复的尺寸
2. 相同尺寸出现多次时，每次都要单独识别
3. 必须进行智能单位转换：小于100的数字推断为厘米，需转换为毫米
4. 处理异常小数点：如568.2×14.5可能是5682×145的识别错误

文本：
${text}

🔍 风口类型识别规则（严格按照以下规则判断）：

📤 出风口识别：
- 关键词：出风口、出风、送风口、送风、客出风、主出风、餐出风、卧出风
- 特征：通常宽度较小（100-250mm），用于送风
- 默认类型：双层白色出风口（double_white_outlet）

📥 回风口识别：
- 关键词：回风口、回风、进气口、进气、客回风、主回风、餐回风、卧回风
- 特征：通常宽度较大（≥255mm），用于回风
- 默认类型：白色回风口（white_return）

<span class="warning">⚠️ 重要：如果文本中没有明确的"回风"关键词，不要默认识别为回风口！
- 只有明确包含"回风"、"回风口"等关键词时才识别为回风口
- 没有明确类型标识时，根据尺寸判断：宽度&lt;255mm识别为出风口，≥255mm识别为回风口</span>

<span class="success">🎯 识别示例：
输入："2665×155  1525×255  3805×155"
正确处理：
1. "2665×155" → systemType: "double_white_outlet", originalType: "出风口"（宽度&lt;255mm）
2. "1525×255" → systemType: "white_return", originalType: "回风口"（宽度≥255mm）
3. "3805×155" → systemType: "double_white_outlet", originalType: "出风口"（宽度&lt;255mm）</span>

[... 完整的详细规则 ...]</div>
            </div>
            
            <div id="test" class="tab-content">
                <div style="padding: 20px;">
                    <h3>🧪 测试 Prompt 效果</h3>
                    <p>输入测试文本，查看生成的完整 Prompt：</p>
                    
                    <textarea id="testInput" class="test-input" placeholder="输入测试文本，例如：
项目：测试项目
一楼大厅：
出风口 2665×155 白色 1个
回风口 1525×255 白色 1个 餐厅"></textarea>
                    
                    <br>
                    <button class="button" onclick="generatePrompt('fast')">生成快速模式 Prompt</button>
                    <button class="button" onclick="generatePrompt('detailed')">生成详细模式 Prompt</button>
                    
                    <div id="generatedPrompt" style="margin-top: 20px;"></div>
                </div>
            </div>
        </div>
        
        <div class="info-box">
            <strong>🔧 最近的优化：</strong><br>
            • ✅ 强化了风口类型识别规则（宽度≥255mm必须是回风口）<br>
            • ✅ 简化了 Prompt 以提高识别速度<br>
            • ✅ 添加了客户端后处理验证<br>
            • ✅ 改进了房间信息提取逻辑<br>
            • ✅ 过滤无意义的备注信息
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的活动状态
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
        }
        
        function generatePrompt(mode) {
            const testInput = document.getElementById('testInput').value.trim();
            if (!testInput) {
                alert('请输入测试文本');
                return;
            }
            
            let prompt = '';
            
            if (mode === 'fast') {
                prompt = `识别风口订单，返回JSON。

文本：${testInput}

规则：
1. 风口类型：
   - 出风口或宽度<255mm → "systemType": "double_white_outlet", "originalType": "出风口"
   - 回风口或宽度≥255mm → "systemType": "white_return", "originalType": "回风口"

2. 尺寸：数字<100为厘米需×10，大值为length，小值为width

3. 房间：提取具体房间名（大厅、办公室、餐厅等），无明确房间用"房间"

必须返回完整JSON：
{
  "projects": [{
    "projectName": "",
    "clientInfo": "",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          }
        ]
      }]
    }]
  }]
}`;
            } else {
                prompt = `你是专业的风口订单识别专家。请仔细分析文本，识别出每一个风口。

文本：
${testInput}

[详细规则...]`;
            }
            
            const resultDiv = document.getElementById('generatedPrompt');
            resultDiv.innerHTML = `
                <div class="prompt-section">
                    <div class="prompt-header">生成的 ${mode === 'fast' ? '快速' : '详细'}模式 Prompt</div>
                    <div class="prompt-content">${prompt}</div>
                </div>
            `;
        }
    </script>
</body>
</html>
