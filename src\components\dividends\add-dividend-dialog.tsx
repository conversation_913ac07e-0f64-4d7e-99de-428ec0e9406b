"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger  } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Loader2, TrendingUp } from "lucide-react"
import { getCurrentFactoryId, getCurrentUser } from "@/lib/utils/factory"
import type { Dividend, DividendStatus } from "@/types"

interface AddDividendDialogProps {
  onDividendAdded?: (dividend: Dividend) => void
}

export function AddDividendDialog({ onDividendAdded }: AddDividendDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    totalAmount: "",
    baseAmount: "",
    dividendRate: "",
    periodStart: "",
    periodEnd: "",
    status: "draft" as DividendStatus
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // 分红状态选项
  const dividendStatusOptions = [
    { value: "draft", label: "草稿" },
    { value: "pending", label: "待审批" },
    { value: "approved", label: "已批准" }
  ]

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = "分红标题不能为空"
    }

    if (!formData.totalAmount.trim()) {
      newErrors.totalAmount = "总分红金额不能为空"
    } else if (isNaN(Number(formData.totalAmount)) || Number(formData.totalAmount) <= 0) {
      newErrors.totalAmount = "总分红金额必须是正数"
    }

    if (!formData.baseAmount.trim()) {
      newErrors.baseAmount = "分红基数不能为空"
    } else if (isNaN(Number(formData.baseAmount)) || Number(formData.baseAmount) <= 0) {
      newErrors.baseAmount = "分红基数必须是正数"
    }

    if (!formData.dividendRate.trim()) {
      newErrors.dividendRate = "分红比例不能为空"
    } else if (isNaN(Number(formData.dividendRate)) || Number(formData.dividendRate) <= 0 || Number(formData.dividendRate) > 100) {
      newErrors.dividendRate = "分红比例必须在0-100之间"
    }

    if (!formData.periodStart) {
      newErrors.periodStart = "分红期间开始日期不能为空"
    }

    if (!formData.periodEnd) {
      newErrors.periodEnd = "分红期间结束日期不能为空"
    }

    // 验证日期逻辑
    if (formData.periodStart && formData.periodEnd) {
      const startDate = new Date(formData.periodStart)
      const endDate = new Date(formData.periodEnd)
      
      if (startDate >= endDate) {
        newErrors.periodEnd = "结束日期必须晚于开始日期"
      }
    }

    // 验证分红金额与基数的关系
    if (formData.totalAmount && formData.baseAmount && formData.dividendRate) {
      const totalAmount = Number(formData.totalAmount)
      const baseAmount = Number(formData.baseAmount)
      const dividendRate = Number(formData.dividendRate)
      
      const calculatedAmount = (baseAmount * dividendRate) / 100
      
      // 允许一定的误差范围（1%）
      if (Math.abs(totalAmount - calculatedAmount) > calculatedAmount * 0.01) {
        newErrors.totalAmount = `根据分红基数和比例，建议分红金额为 ${calculatedAmount.toFixed(2)} 元`
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 自动计算分红金额
  const handleCalculateAmount = () => {
    if (formData.baseAmount && formData.dividendRate) {
      const baseAmount = Number(formData.baseAmount)
      const dividendRate = Number(formData.dividendRate)
      
      if (!isNaN(baseAmount) && !isNaN(dividendRate)) {
        const calculatedAmount = (baseAmount * dividendRate) / 100
        setFormData(prev => ({ 
          ...prev, 
          totalAmount: calculatedAmount.toFixed(2) 
        }))
      }
    }
  }

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()
      const currentUser = getCurrentUser()

      if (!factoryId) {
        setErrors({ general: "无法获取工厂信息" })
        return
      }

      if (!currentUser) {
        setErrors({ general: "无法获取用户信息" })
        return
      }

      const dividendData = {
        factoryId,
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        totalAmount: Number(formData.totalAmount),
        baseAmount: Number(formData.baseAmount),
        dividendRate: Number(formData.dividendRate),
        periodStart: formData.periodStart,
        periodEnd: formData.periodEnd,
        status: formData.status,
        createdBy: currentUser.id
      }

      // 调用API创建分红
      const response = await fetch('/api/dividends', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dividendData),
      })

      const result = await response.json()

      if (result.success) {
        console.log('✅ 分红创建成功:', result.dividend)
        
        // 重置表单
        setFormData({
          title: "",
          description: "",
          totalAmount: "",
          baseAmount: "",
          dividendRate: "",
          periodStart: "",
          periodEnd: "",
          status: "draft"
        })
        setErrors({})
        setOpen(false)

        // 通知父组件
        if (onDividendAdded) {
          onDividendAdded(result.dividend)
        }
      } else {
        setErrors({ general: result.error || "创建分红失败，请重试" })
      }
    } catch (error) {
      console.error('创建分红失败:', error)
      setErrors({ general: `创建失败: ${error.message}` })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          创建分红
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>创建分红计划</span>
          </DialogTitle>
          <DialogDescription>
            创建新的分红计划，系统将自动为所有活跃股东分配分红
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 错误提示 */}
          {errors.general && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {errors.general}
            </div>
          )}

          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">基本信息</h3>
            
            {/* 分红标题 */}
            <div className="space-y-2">
              <Label htmlFor="title">分红标题 *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="如：2024年第一季度分红"
                className={errors.title ? "border-red-500" : ""}
              />
              {errors.title && <p className="text-sm text-red-600">{errors.title}</p>}
            </div>

            {/* 分红说明 */}
            <div className="space-y-2">
              <Label htmlFor="description">分红说明</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="请输入分红说明（可选）"
                rows={3}
              />
            </div>

            {/* 分红状态 */}
            <div className="space-y-2">
              <Label htmlFor="status">分红状态</Label>
              <Select
                value={formData.status}
                onValueChange={(value: DividendStatus) => 
                  setFormData(prev => ({ ...prev, status: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dividendStatusOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 分红计算 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">分红计算</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 分红基数 */}
              <div className="space-y-2">
                <Label htmlFor="baseAmount">分红基数 (元) *</Label>
                <Input
                  id="baseAmount"
                  type="number"
                  step="0.01"
                  value={formData.baseAmount}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, baseAmount: e.target.value }))
                    // 自动计算分红金额
                    setTimeout(handleCalculateAmount, 100)
                  }}
                  placeholder="如净利润金额"
                  className={errors.baseAmount ? "border-red-500" : ""}
                />
                {errors.baseAmount && <p className="text-sm text-red-600">{errors.baseAmount}</p>}
              </div>

              {/* 分红比例 */}
              <div className="space-y-2">
                <Label htmlFor="dividendRate">分红比例 (%) *</Label>
                <Input
                  id="dividendRate"
                  type="number"
                  step="0.01"
                  max="100"
                  value={formData.dividendRate}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, dividendRate: e.target.value }))
                    // 自动计算分红金额
                    setTimeout(handleCalculateAmount, 100)
                  }}
                  placeholder="如20表示20%"
                  className={errors.dividendRate ? "border-red-500" : ""}
                />
                {errors.dividendRate && <p className="text-sm text-red-600">{errors.dividendRate}</p>}
              </div>
            </div>

            {/* 总分红金额 */}
            <div className="space-y-2">
              <Label htmlFor="totalAmount">总分红金额 (元) *</Label>
              <div className="flex space-x-2">
                <Input
                  id="totalAmount"
                  type="number"
                  step="0.01"
                  value={formData.totalAmount}
                  onChange={(e) => setFormData(prev => ({ ...prev, totalAmount: e.target.value }))}
                  placeholder="请输入总分红金额"
                  className={errors.totalAmount ? "border-red-500" : ""}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCalculateAmount}
                  disabled={!formData.baseAmount || !formData.dividendRate}
                >
                  自动计算
                </Button>
              </div>
              {errors.totalAmount && <p className="text-sm text-red-600">{errors.totalAmount}</p>}
            </div>
          </div>

          {/* 分红期间 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">分红期间</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 开始日期 */}
              <div className="space-y-2">
                <Label htmlFor="periodStart">开始日期 *</Label>
                <Input
                  id="periodStart"
                  type="date"
                  value={formData.periodStart}
                  onChange={(e) => setFormData(prev => ({ ...prev, periodStart: e.target.value }))}
                  className={errors.periodStart ? "border-red-500" : ""}
                />
                {errors.periodStart && <p className="text-sm text-red-600">{errors.periodStart}</p>}
              </div>

              {/* 结束日期 */}
              <div className="space-y-2">
                <Label htmlFor="periodEnd">结束日期 *</Label>
                <Input
                  id="periodEnd"
                  type="date"
                  value={formData.periodEnd}
                  onChange={(e) => setFormData(prev => ({ ...prev, periodEnd: e.target.value }))}
                  className={errors.periodEnd ? "border-red-500" : ""}
                />
                {errors.periodEnd && <p className="text-sm text-red-600">{errors.periodEnd}</p>}
              </div>
            </div>
          </div>

          {/* 按钮 */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  创建中...
                </>) : (
                "创建分红"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
