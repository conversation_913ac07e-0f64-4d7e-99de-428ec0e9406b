"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from "@/lib/database"
import type { Announcement, Factory } from "@/types"
import {
  FileText,
  Plus,
  Edit,
  Trash2,
  Send,
  AlertCircle,
  Info,
  AlertTriangle,
  Calendar,
  Users,
  RefreshCw
} from "lucide-react"

export default function AnnouncementsPage() {
  const [loading, setLoading] = useState(true)
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [factories, setFactories] = useState<Factory[]>([])
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingAnnouncement, setEditingAnnouncement] = useState<Announcement | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    type: 'info' as 'info' | 'warning' | 'urgent',
    targetFactories: [] as string[],
    expiresAt: ''
  })

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true)
      console.log('🔄 加载公告和工厂数据...')

      const [announcementsData, factoriesData] = await Promise.all([
        db.getAnnouncements(),
        db.getFactories()
      ])

      setAnnouncements(announcementsData)
      setFactories(factoriesData)
      console.log('✅ 数据加载完成')
    } catch (error) {
      console.error('❌ 加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [])

  // 创建公告
  const handleCreateAnnouncement = async () => {
    try {
      if (!formData.title.trim() || !formData.content.trim()) {
        alert('请填写标题和内容')
        return
      }

      const announcementData = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        type: formData.type,
        targetFactories: formData.targetFactories,
        publishedAt: new Date(),
        expiresAt: formData.expiresAt ? new Date(formData.expiresAt) : undefined,
        createdBy: 'admin'
      }

      await db.createAnnouncement(announcementData)
      
      // 重置表单
      setFormData({
        title: '',
        content: '',
        type: 'info',
        targetFactories: [],
        expiresAt: ''
      })
      setShowCreateForm(false)
      
      // 重新加载数据
      await loadData()
      
      console.log('✅ 公告创建成功')
    } catch (error) {
      console.error('❌ 创建公告失败:', error)
      alert('创建公告失败，请重试')
    }
  }

  // 更新公告
  const handleUpdateAnnouncement = async () => {
    try {
      if (!editingAnnouncement || !formData.title.trim() || !formData.content.trim()) {
        alert('请填写标题和内容')
        return
      }

      const updates = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        type: formData.type,
        targetFactories: formData.targetFactories,
        expiresAt: formData.expiresAt ? new Date(formData.expiresAt) : undefined
      }

      await db.updateAnnouncement(editingAnnouncement.id, updates)
      
      // 重置表单
      setFormData({
        title: '',
        content: '',
        type: 'info',
        targetFactories: [],
        expiresAt: ''
      })
      setEditingAnnouncement(null)
      
      // 重新加载数据
      await loadData()
      
      console.log('✅ 公告更新成功')
    } catch (error) {
      console.error('❌ 更新公告失败:', error)
      alert('更新公告失败，请重试')
    }
  }

  // 删除公告
  const handleDeleteAnnouncement = async (id: string) => {
    try {
      if (!confirm('确定要删除这个公告吗？')) {
        return
      }

      await db.deleteAnnouncement(id)
      await loadData()
      
      console.log('✅ 公告删除成功')
    } catch (error) {
      console.error('❌ 删除公告失败:', error)
      alert('删除公告失败，请重试')
    }
  }

  // 编辑公告
  const handleEditAnnouncement = (announcement: Announcement) => {
    setEditingAnnouncement(announcement)
    setFormData({
      title: announcement.title,
      content: announcement.content,
      type: announcement.type,
      targetFactories: announcement.targetFactories,
      expiresAt: announcement.expiresAt ? announcement.expiresAt.toISOString().split('T')[0] : ''
    })
    setShowCreateForm(true)
  }

  // 取消编辑
  const handleCancelEdit = () => {
    setShowCreateForm(false)
    setEditingAnnouncement(null)
    setFormData({
      title: '',
      content: '',
      type: 'info',
      targetFactories: [],
      expiresAt: ''
    })
  }

  // 获取公告类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'urgent':
        return <AlertCircle className="h-4 w-4" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  // 获取公告类型颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'urgent':
        return 'bg-red-100 text-red-800'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  // 获取公告类型文本
  const getTypeText = (type: string) => {
    switch (type) {
      case 'urgent':
        return '紧急'
      case 'warning':
        return '警告'
      default:
        return '信息'
    }
  }

  if (loading) {
    return (
      <DashboardLayout role="admin">
        <div className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">正在加载公告数据...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout role="admin">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <FileText className="h-6 w-6 mr-2 text-red-600" />
              系统公告管理
            </h1>
            <p className="text-gray-600">发布和管理平台公告，支持推送到指定工厂</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={loadData}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? '刷新中...' : '刷新数据'}
            </Button>
            <Button onClick={() => setShowCreateForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              发布公告
            </Button>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">总公告数</p>
                  <p className="text-2xl font-bold text-gray-900">{announcements.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">紧急公告</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {announcements.filter(a => a.type === 'urgent').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">目标工厂</p>
                  <p className="text-2xl font-bold text-gray-900">{factories.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">今日发布</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {announcements.filter(a => {
                      const today = new Date()
                      const publishDate = new Date(a.publishedAt)
                      return publishDate.toDateString() === today.toDateString()
                    }).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 创建/编辑公告表单 */}
        {showCreateForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>
                {editingAnnouncement ? '编辑公告' : '发布新公告'}
              </CardTitle>
              <CardDescription>
                {editingAnnouncement ? '修改公告内容和设置' : '创建新的系统公告并选择目标工厂'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">公告标题</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="请输入公告标题"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">公告类型</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value: 'info' | 'warning' | 'urgent') =>
                      setFormData(prev => ({ ...prev, type: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">信息公告</SelectItem>
                      <SelectItem value="warning">警告公告</SelectItem>
                      <SelectItem value="urgent">紧急公告</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">公告内容</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="请输入公告详细内容..."
                  rows={6}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="targetFactories">目标工厂</Label>
                  <Select
                    value={formData.targetFactories.length === 0 ? 'all' : 'selected'}
                    onValueChange={(value) => {
                      if (value === 'all') {
                        setFormData(prev => ({ ...prev, targetFactories: [] }))
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有工厂</SelectItem>
                      <SelectItem value="selected">指定工厂</SelectItem>
                    </SelectContent>
                  </Select>
                  {formData.targetFactories.length === 0 && (
                    <p className="text-sm text-gray-500">将发送给所有工厂</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expiresAt">过期时间（可选）</Label>
                  <Input
                    id="expiresAt"
                    type="date"
                    value={formData.expiresAt}
                    onChange={(e) => setFormData(prev => ({ ...prev, expiresAt: e.target.value }))}
                  />
                </div>
              </div>

              {/* 工厂选择（如果选择了指定工厂） */}
              {formData.targetFactories.length > 0 && (
                <div className="space-y-2">
                  <Label>选择工厂</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-lg p-4">
                    {factories.map(factory => (
                      <label key={factory.id} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.targetFactories.includes(factory.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData(prev => ({
                                ...prev,
                                targetFactories: [...prev.targetFactories, factory.id]
                              }))
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                targetFactories: prev.targetFactories.filter(id => id !== factory.id)
                              }))
                            }
                          }}
                          className="rounded"
                        />
                        <span className="text-sm">{factory.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-4">
                <Button variant="outline" onClick={handleCancelEdit}>
                  取消
                </Button>
                <Button onClick={editingAnnouncement ? handleUpdateAnnouncement : handleCreateAnnouncement}>
                  <Send className="h-4 w-4 mr-2" />
                  {editingAnnouncement ? '更新公告' : '发布公告'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 公告列表 */}
        <Card>
          <CardHeader>
            <CardTitle>公告列表</CardTitle>
            <CardDescription>管理所有已发布的系统公告</CardDescription>
          </CardHeader>
          <CardContent>
            {announcements.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无公告</h3>
                <p className="text-gray-600 mb-4">还没有发布任何公告</p>
                <Button onClick={() => setShowCreateForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  发布第一个公告
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {announcements.map((announcement) => (
                  <div key={announcement.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{announcement.title}</h3>
                          <Badge className={`${getTypeColor(announcement.type)} flex items-center space-x-1`}>
                            {getTypeIcon(announcement.type)}
                            <span>{getTypeText(announcement.type)}</span>
                          </Badge>
                        </div>

                        <p className="text-gray-600 mb-4 whitespace-pre-wrap">{announcement.content}</p>

                        <div className="flex items-center space-x-6 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>发布时间: {announcement.publishedAt.toLocaleString('zh-CN')}</span>
                          </div>

                          {announcement.expiresAt && (
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4" />
                              <span>过期时间: {announcement.expiresAt.toLocaleString('zh-CN')}</span>
                            </div>
                          )}

                          <div className="flex items-center space-x-1">
                            <Users className="h-4 w-4" />
                            <span>
                              目标工厂: {announcement.targetFactories.length === 0
                                ? '所有工厂'
                                : `${announcement.targetFactories.length} 个工厂`}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditAnnouncement(announcement)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteAnnouncement(announcement.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
