/**
 * 🇨🇳 风口云平台 - 客户端图片处理服务
 * 
 * 功能说明：
 * - 在客户端进行图片压缩和处理
 * - 获取图片尺寸信息
 * - 转换为Base64格式
 */

export interface ProcessedImageData {
  name: string
  base64Data: string
  mimeType: string
  size: number
  originalSize: number
  width: number
  height: number
  compressed: boolean
}

export class ClientImageProcessor {
  private static instance: ClientImageProcessor
  
  static getInstance(): ClientImageProcessor {
    if (!ClientImageProcessor.instance) {
      ClientImageProcessor.instance = new ClientImageProcessor()
    }
    return ClientImageProcessor.instance
  }

  /**
   * 处理图片文件（压缩 + 获取信息）
   */
  async processImage(file: File): Promise<ProcessedImageData> {
    try {
      // 获取原始图片信息
      const originalDimensions = await this.getImageDimensions(file)
      const originalSize = file.size

      // 压缩图片
      const compressedFile = await this.compressImage(file)
      
      // 转换为Base64
      const base64Data = await this.fileToBase64(compressedFile)

      return {
        name: file.name,
        base64Data,
        mimeType: file.type,
        size: compressedFile.size,
        originalSize,
        width: originalDimensions.width,
        height: originalDimensions.height,
        compressed: compressedFile.size < originalSize
      }
    } catch (error) {
      console.error('处理图片失败:', error)
      throw new Error('图片处理失败')
    }
  }

  /**
   * 压缩图片
   */
  private async compressImage(file: File): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // 设置最大尺寸
        const maxWidth = 1200
        const maxHeight = 800
        
        let { width, height } = img
        
        // 计算缩放比例
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width *= ratio
          height *= ratio
        }

        canvas.width = width
        canvas.height = height

        // 绘制压缩后的图片
        ctx?.drawImage(img, 0, 0, width, height)

        // 转换为Blob
        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            resolve(file) // 如果压缩失败，返回原文件
          }
        }, file.type, 0.8) // 80%质量
      }

      img.onerror = () => {
        resolve(file) // 如果加载失败，返回原文件
      }

      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * 获取图片尺寸
   */
  private async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        resolve({ width: img.width, height: img.height })
      }
      img.onerror = () => {
        reject(new Error('无法获取图片尺寸'))
      }
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * 将文件转换为Base64
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result)
        } else {
          reject(new Error('文件读取失败'))
        }
      }
      reader.onerror = () => reject(new Error('文件读取错误'))
      reader.readAsDataURL(file)
    })
  }

  /**
   * 验证文件
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      return { valid: false, error: '文件必须是图片格式' }
    }

    // 检查文件大小（10MB限制）
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      return { valid: false, error: '文件大小不能超过10MB' }
    }

    // 检查文件名
    if (!file.name || file.name.trim() === '') {
      return { valid: false, error: '文件名不能为空' }
    }

    return { valid: true }
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 批量处理图片
   */
  async processImages(files: File[]): Promise<{
    processed: ProcessedImageData[]
    errors: string[]
  }> {
    const processed: ProcessedImageData[] = []
    const errors: string[] = []

    for (const file of files) {
      try {
        // 验证文件
        const validation = this.validateFile(file)
        if (!validation.valid) {
          errors.push(`${file.name}: ${validation.error}`)
          continue
        }

        // 处理图片
        const processedImage = await this.processImage(file)
        processed.push(processedImage)

      } catch (error) {
        errors.push(`${file.name}: 处理失败 - ${error.message}`)
      }
    }

    return { processed, errors }
  }
}

// 导出单例实例
export const clientImageProcessor = ClientImageProcessor.getInstance()
