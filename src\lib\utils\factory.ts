/**
 * 🇨🇳 风口云平台 - 工厂工具函数
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 提供工厂相关的工具函数
 * - 获取当前登录用户和工厂信息
 * - 处理工厂数据的常用操作
 */

import { useAuthStore } from "@/lib/store/auth"

/**
 * 获取当前工厂ID
 */
export const getCurrentFactoryId = (): string | null => {
  const { factoryId } = useAuthStore.getState()
  return factoryId
}

/**
 * 获取当前登录用户信息
 */
export const getCurrentUser = () => {
  const { user } = useAuthStore.getState()
  return user
}

/**
 * 检查用户是否已登录
 */
export const isUserLoggedIn = (): boolean => {
  const { isAuthenticated } = useAuthStore.getState()
  return isAuthenticated
}

/**
 * 检查用户是否为工厂用户
 */
export const isFactoryUser = (): boolean => {
  const { role } = useAuthStore.getState()
  return role === 'factory'
}

/**
 * 为数据添加工厂ID（用于数据隔离）
 */
export const addFactoryId = <T extends Record<string, any>>(data: T): T & { factoryId: string } => {
  const factoryId = getCurrentFactoryId()
  if (!factoryId) {
    throw new Error('无法获取工厂ID，请重新登录')
  }
  return { ...data, factoryId }
}

/**
 * 生成订单号
 * 格式：前缀-日期-序号 (例如: NNJGC-20250612-01)
 * 支持并发安全和重试机制
 * @param externalAttempt 外部重试次数（用于客户端重试时传入）
 */
export const generateOrderNumber = async (externalAttempt: number = 0): Promise<string> => {
  const maxRetries = 5
  let attempt = 0

  while (attempt < maxRetries) {
    try {
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        throw new Error('无法获取工厂ID')
      }

      // 🔧 获取工厂设置中的自定义订单号前缀
      let prefix = getDefaultOrderPrefix(factoryId) // 默认前缀作为后备

      try {
        // 尝试从工厂设置中获取自定义前缀
        const { getOrderNumberPrefixAsync } = await import('@/lib/utils/factory-settings')
        const customPrefix = await getOrderNumberPrefixAsync(factoryId)
        if (customPrefix && customPrefix !== 'ORD') {
          prefix = customPrefix
          console.log(`✅ 使用自定义订单前缀: ${prefix}`)
        } else {
          console.log(`ℹ️ 使用默认订单前缀: ${prefix}`)
        }
      } catch (error) {
        console.warn('⚠️ 获取自定义前缀失败，使用默认前缀:', error)
      }

      // 生成日期字符串 (YYYYMMDD)
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const dateStr = `${year}${month}${day}`

      // 获取当日订单序号（带并发安全）
      // 使用外部重试次数和内部重试次数的组合
      const totalAttempt = externalAttempt + attempt
      const sequence = await getDailyOrderSequenceWithLock(factoryId, dateStr, totalAttempt)
      const sequenceStr = String(sequence).padStart(2, '0')

      const orderNumber = `${prefix}-${dateStr}-${sequenceStr}`

      // 验证订单号是否已存在
      if (typeof window !== 'undefined') {
        const isUnique = await verifyOrderNumberUnique(orderNumber)
        if (!isUnique) {
          console.warn(`⚠️ 订单号 ${orderNumber} 已存在，重试第 ${attempt + 1} 次`)
          attempt++
          continue
        }
      }

      console.log(`✅ 订单号生成成功: ${orderNumber}`)
      return orderNumber
    } catch (error) {
      console.error(`❌ 生成订单号失败 (尝试 ${attempt + 1}/${maxRetries}):`, error)
      attempt++

      if (attempt >= maxRetries) {
        console.error('❌ 达到最大重试次数，使用回退方案')
        break
      }

      // 等待一小段时间再重试，避免并发冲突
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200))
    }
  }

  // 回退到时间戳 + 随机数方式，确保唯一性
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  const fallbackOrderNumber = `ORD-${timestamp}-${random}`
  console.log(`🔄 使用回退订单号: ${fallbackOrderNumber}`)
  return fallbackOrderNumber
}

/**
 * 获取工厂默认订单号前缀（基于工厂ID生成唯一前缀）
 */
export const getDefaultOrderPrefix = (factoryId: string): string => {
  // 根据工厂ID返回默认前缀，确保每个工厂都有唯一前缀
  switch (factoryId) {
    case 'lin001':
      return 'NNJGC' // 南宁加工厂
    case 'lzfxc':
    case 'LZFXC':
      return 'LZFXC' // 柳州风向厂（或其他LZFXC工厂）
    default:
      // 为其他工厂生成基于factoryId的唯一前缀
      return generateUniquePrefix(factoryId)
  }
}

/**
 * 基于工厂ID生成唯一前缀
 * @param factoryId 工厂ID
 * @returns 唯一的订单号前缀
 */
const generateUniquePrefix = (factoryId: string): string => {
  // 将factoryId转换为大写字母前缀
  const cleanId = factoryId.toUpperCase().replace(/[^A-Z0-9]/g, '')

  if (cleanId.length >= 3) {
    // 如果ID本身就是3位以上字母，直接使用（最多取前6位）
    return cleanId.substring(0, 6)
  } else if (cleanId.length >= 2) {
    // 如果是2位，添加'JGC'后缀
    return cleanId + 'JGC'
  } else {
    // 如果太短，使用哈希方式生成
    const hash = factoryId.split('').reduce((acc, char) => {
      return acc + char.charCodeAt(0)
    }, 0)
    const hashStr = hash.toString(36).toUpperCase().replace(/[^A-Z]/g, '')
    return (hashStr + 'JGC').substring(0, 6)
  }
}

/**
 * 获取当日订单序号（并发安全版本）
 */
const getDailyOrderSequenceWithLock = async (factoryId: string, dateStr: string, attempt: number): Promise<number> => {
  try {
    // 暂时禁用数据库查询，避免chunk加载问题
    if (false) { // 禁用数据库查询
      try {
        // const { db } = await import('@/lib/database/client')
        // const orders = await db.getOrdersByFactoryId(factoryId)

        // 筛选出当日订单并提取序号
        const todayOrderNumbers = orders
          .filter(order => {
            const orderDate = new Date(order.createdAt)
            const orderDateStr = `${orderDate.getFullYear()}${String(orderDate.getMonth() + 1).padStart(2, '0')}${String(orderDate.getDate()).padStart(2, '0')}`
            return orderDateStr === dateStr
          })
          .map(order => {
            // 从订单号中提取序号部分，例如 "JGC-20250615-03" -> 3
            const match = order.orderNumber?.match(/-(\d+)$/)
            return match ? parseInt(match[1], 10) : 0
          })
          .filter(num => !isNaN(num))

        // 找到最大序号，然后 +1，并加上重试次数以避免冲突
        const maxSequence = todayOrderNumbers.length > 0 ? Math.max(...todayOrderNumbers) : 0
        return maxSequence + 1 + attempt
      } catch (dbError) {
        console.warn('⚠️ 无法从数据库获取订单序号，使用时间戳:', dbError)
      }
    }

    // 使用时间戳作为序号，确保唯一性
    const now = new Date()
    const timeSequence = parseInt(`${now.getHours()}${now.getMinutes()}${now.getSeconds()}${now.getMilliseconds()}`.slice(-3))
    return Math.max(timeSequence + attempt, 1) // 确保至少为1，并加上重试次数
  } catch (error) {
    console.error('❌ 获取订单序号失败:', error)
    // 最终回退到时间戳 + 重试次数
    const fallbackSequence = parseInt(Date.now().toString().slice(-3)) + attempt
    return Math.max(fallbackSequence, 1)
  }
}

/**
 * 验证订单号是否唯一
 */
const verifyOrderNumberUnique = async (orderNumber: string): Promise<boolean> => {
  try {
    // 暂时禁用唯一性验证，避免chunk加载问题
    console.log('ℹ️ 订单号唯一性验证已禁用，避免动态导入问题')
    return true // 暂时总是返回true

    if (false) { // 禁用数据库查询
      // const { db } = await import('@/lib/database/client')
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        console.warn('⚠️ 无法获取工厂ID，跳过唯一性验证')
        return true
      }

      // 获取工厂的所有订单来检查重复
      const factoryOrders = await db.getOrdersByFactoryId(factoryId)

      // 检查是否有重复的订单号
      const existingOrder = factoryOrders.find(order => order.orderNumber === orderNumber)
      return !existingOrder
    }
    return true // 如果无法验证，假设唯一
  } catch (error) {
    console.error('❌ 验证订单号唯一性失败:', error)
    return true // 如果验证失败，假设唯一
  }
}

/**
 * 获取当日订单序号（旧版本，保留用于兼容性）
 * @deprecated 请使用 getDailyOrderSequenceWithLock
 */
const getDailyOrderSequence = async (factoryId: string, dateStr: string): Promise<number> => {
  return getDailyOrderSequenceWithLock(factoryId, dateStr, 0)
}

/**
 * 生成客户编号
 */
export const generateClientNumber = (): string => {
  const now = new Date()
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '')
  const timeStr = Date.now().toString().slice(-6)
  return `CLI-${dateStr}-${timeStr}`
}
