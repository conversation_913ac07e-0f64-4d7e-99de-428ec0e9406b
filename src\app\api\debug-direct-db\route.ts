/**
 * 直接测试数据库服务类
 */

import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  console.log('🔍 直接测试数据库服务类')

  try {
    // 检查环境
    console.log('🌍 环境检查:', {
      isServer: typeof window === 'undefined',
      nodeEnv: process.env.NODE_ENV
    })

    // 动态导入数据库模块
    const dbModule = await import('@/lib/database/index')
    console.log('✅ 数据库模块导入成功')

    // 检查导出的内容
    console.log('🔍 模块导出:', Object.keys(dbModule))

    // 检查db对象的详细信息
    const db = dbModule.db
    console.log('🔍 db对象类型:', typeof db)
    console.log('🔍 db对象构造函数:', db?.constructor?.name)

    // 尝试调用authenticateFactoryUser方法
    if (typeof db.authenticateFactoryUser === 'function') {
      console.log('✅ db有authenticateFactoryUser方法')

      // 尝试调用并捕获警告
      try {
        console.log('🔐 尝试认证用户 gxsfkc...')
        const result = await db.authenticateFactoryUser('gxsfkc', 'gxsfkc', {
          ipAddress: '127.0.0.1',
          userAgent: 'test',
          deviceInfo: 'test'
        })
        console.log('🔍 认证结果:', result ? '成功' : '失败')
        if (result) {
          console.log('👤 用户信息:', {
            username: result.username,
            name: result.name,
            factoryName: result.factory?.name
          })
        }
      } catch (authError) {
        console.log('🔍 认证出错:', authError)
      }
    } else {
      console.log('❌ db没有authenticateFactoryUser方法')
    }

    return NextResponse.json({
      success: true,
      environment: {
        isServer: typeof window === 'undefined',
        nodeEnv: process.env.NODE_ENV
      },
      moduleExports: Object.keys(dbModule),
      dbInfo: {
        type: typeof db,
        constructor: db?.constructor?.name,
        hasAuthMethod: typeof db?.authenticateFactoryUser === 'function'
      }
    })

  } catch (error) {
    console.error('❌ 直接测试数据库服务失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      stack: error instanceof Error ? error.stack : '无堆栈信息'
    }, { status: 500 })
  }
}
