/**
 * 🔥 激进会话监控服务
 * 
 * 功能：
 * - 更频繁的会话状态检查（每10秒）
 * - 立即检测会话冲突
 * - 强制踢出重复登录
 */

import { useAuthStore } from '@/lib/store/auth'
import { api } from '@/lib/api/client'

export interface AggressiveSessionConfig {
  checkInterval: number    // 检查间隔（毫秒）
  maxRetries: number       // 最大重试次数
  onSessionKicked?: (reason: string) => void // 被踢出回调
}

const DEFAULT_CONFIG: AggressiveSessionConfig = {
  checkInterval: 10000,    // 10秒检查一次
  maxRetries: 3
}

/**
 * 激进会话监控服务
 */
export class AggressiveSessionMonitor {
  private static instance: AggressiveSessionMonitor | null = null
  private checkTimer: NodeJS.Timeout | null = null
  private isChecking = false
  private retryCount = 0
  private config: AggressiveSessionConfig
  private lastSessionId: string | null = null

  private constructor(config: AggressiveSessionConfig) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * 获取单例实例
   */
  static getInstance(config?: Partial<AggressiveSessionConfig>): AggressiveSessionMonitor {
    if (!this.instance) {
      this.instance = new AggressiveSessionMonitor(config || DEFAULT_CONFIG)
    }
    return this.instance
  }

  /**
   * 启动激进监控
   */
  startMonitoring(): void {
    if (this.checkTimer) {
      console.log('⚠️ 激进会话监控已在运行')
      return
    }

    console.log('🔥 启动激进会话监控')
    console.log('   检查间隔:', this.config.checkInterval / 1000, '秒')
    console.log('   最大重试次数:', this.config.maxRetries)

    // 记录当前会话ID
    const { user } = useAuthStore.getState()
    this.lastSessionId = (user as any)?.sessionId || null
    console.log('   当前会话ID:', this.lastSessionId)

    // 立即检查一次
    this.checkSessionStatus()

    // 设置定时检查
    this.checkTimer = setInterval(() => {
      this.checkSessionStatus()
    }, this.config.checkInterval)

    console.log('✅ 激进会话监控已启动')
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
      this.checkTimer = null
      console.log('⏹️ 激进会话监控已停止')
    }
    this.retryCount = 0
    this.lastSessionId = null
  }

  /**
   * 检查会话状态
   */
  private async checkSessionStatus(): Promise<void> {
    // 避免并发检查
    if (this.isChecking) {
      return
    }

    const { isAuthenticated, user } = useAuthStore.getState()

    // 如果用户未登录，停止监控
    if (!isAuthenticated) {
      console.log('❌ 用户未登录，停止激进会话监控')
      this.stopMonitoring()
      return
    }

    this.isChecking = true

    try {
      console.log('🔥 激进检查会话状态...')

      // 🔧 修复：直接调用服务器端会话验证API，而不是客户端检查
      const response = await fetch('/api/auth/session', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          console.log('✅ 激进监控：会话状态正常')
          this.retryCount = 0 // 重置重试计数

          // 检查会话ID是否发生变化（可能被踢出后重新登录）
          const currentSessionId = (user as any)?.sessionId
          if (this.lastSessionId && currentSessionId && this.lastSessionId !== currentSessionId) {
            console.log('⚠️ 检测到会话ID变化，可能发生了会话切换')
            this.lastSessionId = currentSessionId
          }
        } else {
          console.log('❌ 激进监控：服务器返回会话无效:', data.error)
          await this.handleSessionKicked(data.error || '会话已失效')
        }
      } else if (response.status === 401) {
        console.log('🚨 激进监控：检测到401未授权，会话已被踢出')
        await this.handleSessionKicked('会话已被其他登录踢出')
      } else {
        console.log('❌ 激进监控：服务器响应异常:', response.status)

        // 增加重试计数
        this.retryCount++
        if (this.retryCount >= this.config.maxRetries) {
          await this.handleSessionKicked('会话检查失败次数过多')
        }
      }

    } catch (error) {
      console.error('❌ 激进会话检查失败:', error)

      // 增加重试计数
      this.retryCount++
      if (this.retryCount >= this.config.maxRetries) {
        await this.handleSessionKicked('网络连接异常')
      }
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 处理会话被踢出
   */
  private async handleSessionKicked(reason: string): Promise<void> {
    console.log('🚨 激进监控检测到会话被踢出:', reason)

    // 停止监控
    this.stopMonitoring()

    // 调用回调函数
    if (this.config.onSessionKicked) {
      this.config.onSessionKicked(reason)
    }

    // 清除本地状态
    useAuthStore.getState().logout()

    // 显示强制下线提示
    if (typeof window !== 'undefined') {
      const kickedMessage = `🚨 账号安全提示：您已被强制下线\n\n` +
        `原因：${reason}\n\n` +
        `这通常意味着：\n` +
        `• 您的账号在其他地方登录了\n` +
        `• 系统检测到异常登录活动\n` +
        `• 为保护账号安全，旧会话已被终止\n\n` +
        `即将跳转到登录页面...`
      
      alert(kickedMessage)
    }

    // 跳转到登录页
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname
        if (currentPath.startsWith('/admin')) {
          window.location.href = '/admin/login'
        } else {
          window.location.href = '/factory/login'
        }
      }
    }, 2000)
  }

  /**
   * 手动触发检查
   */
  async checkNow(): Promise<boolean> {
    try {
      await this.checkSessionStatus()
      return true
    } catch (error) {
      console.error('❌ 手动激进检查失败:', error)
      return false
    }
  }

  /**
   * 获取监控状态
   */
  getStatus() {
    return {
      isRunning: !!this.checkTimer,
      checkInterval: this.config.checkInterval,
      retryCount: this.retryCount,
      lastSessionId: this.lastSessionId
    }
  }
}

/**
 * 启动激进会话监控
 */
export function startAggressiveSessionMonitoring(config?: Partial<AggressiveSessionConfig>) {
  const monitor = AggressiveSessionMonitor.getInstance(config)
  monitor.startMonitoring()
  return monitor
}

/**
 * 停止激进会话监控
 */
export function stopAggressiveSessionMonitoring() {
  const monitor = AggressiveSessionMonitor.getInstance()
  monitor.stopMonitoring()
}

/**
 * 获取激进监控状态
 */
export function getAggressiveMonitorStatus() {
  const monitor = AggressiveSessionMonitor.getInstance()
  return monitor.getStatus()
}
