/**
 * 🔍 认证状态调试工具
 * 
 * 用于诊断和修复认证状态问题
 */

import { useAuthStore } from '@/lib/store/auth'
import { isJWTExpired } from '@/lib/auth/jwt'

export interface AuthDebugInfo {
  // Zustand状态
  zustandState: {
    isAuthenticated: boolean
    user: any
    role: string | null
    factoryId: string | null
    accessToken: string | null
    hasValidToken: boolean
  }
  
  // localStorage状态
  localStorageState: {
    hasAccessToken: boolean
    hasRefreshToken: boolean
    hasUser: boolean
    hasRole: boolean
    hasFactoryId: boolean
    hasAuthStorage: boolean
  }
  
  // Token状态
  tokenState: {
    isExpired: boolean
    tokenLength: number
    tokenPreview: string
  }
  
  // 诊断结果
  diagnosis: {
    issue: string | null
    recommendation: string | null
    canAutoFix: boolean
  }
}

/**
 * 获取完整的认证状态调试信息
 */
export function getAuthDebugInfo(): AuthDebugInfo {
  const authState = useAuthStore.getState()
  
  // 检查localStorage
  const localStorage = typeof window !== 'undefined' ? window.localStorage : null
  const localStorageState = {
    hasAccessToken: !!localStorage?.getItem('accessToken'),
    hasRefreshToken: !!localStorage?.getItem('refreshToken'),
    hasUser: !!localStorage?.getItem('user'),
    hasRole: !!localStorage?.getItem('role'),
    hasFactoryId: !!localStorage?.getItem('factoryId'),
    hasAuthStorage: !!localStorage?.getItem('auth-storage')
  }
  
  // 检查Token状态
  const accessToken = authState.accessToken || localStorage?.getItem('accessToken')
  const tokenState = {
    isExpired: accessToken ? isJWTExpired(accessToken) : true,
    tokenLength: accessToken?.length || 0,
    tokenPreview: accessToken ? `${accessToken.substring(0, 20)}...` : 'null'
  }
  
  // Zustand状态
  const zustandState = {
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    role: authState.role,
    factoryId: authState.factoryId,
    accessToken: authState.accessToken,
    hasValidToken: !!authState.accessToken && !tokenState.isExpired
  }
  
  // 诊断问题
  const diagnosis = diagnoseAuthIssues(zustandState, localStorageState, tokenState)
  
  return {
    zustandState,
    localStorageState,
    tokenState,
    diagnosis
  }
}

/**
 * 诊断认证问题
 */
function diagnoseAuthIssues(
  zustand: any,
  localStorage: any,
  token: any
): { issue: string | null; recommendation: string | null; canAutoFix: boolean } {
  
  // 1. Token过期
  if (token.isExpired && localStorage.hasAccessToken) {
    return {
      issue: 'Token已过期',
      recommendation: '清理过期的认证数据并重新登录',
      canAutoFix: true
    }
  }
  
  // 2. Zustand状态丢失但localStorage有数据
  if (!zustand.isAuthenticated && localStorage.hasAccessToken && localStorage.hasUser && !token.isExpired) {
    return {
      issue: 'Zustand状态丢失，但localStorage有有效数据',
      recommendation: '手动触发状态恢复',
      canAutoFix: true
    }
  }
  
  // 3. 数据不一致
  if (zustand.isAuthenticated && !zustand.hasValidToken) {
    return {
      issue: '认证状态不一致：已认证但Token无效',
      recommendation: '清理认证状态并重新登录',
      canAutoFix: true
    }
  }
  
  // 4. 部分数据缺失
  if (localStorage.hasAccessToken && (!localStorage.hasUser || !localStorage.hasRole)) {
    return {
      issue: '认证数据不完整',
      recommendation: '清理不完整的认证数据',
      canAutoFix: true
    }
  }
  
  // 5. 工厂用户缺少factoryId
  if (zustand.isAuthenticated && zustand.role === 'factory' && !zustand.factoryId) {
    return {
      issue: '工厂用户缺少factoryId',
      recommendation: '重新登录以获取完整的工厂信息',
      canAutoFix: false
    }
  }
  
  return {
    issue: null,
    recommendation: null,
    canAutoFix: false
  }
}

/**
 * 自动修复认证问题
 */
export function autoFixAuthIssues(): boolean {
  const debugInfo = getAuthDebugInfo()
  
  if (!debugInfo.diagnosis.canAutoFix) {
    console.log('❌ 无法自动修复认证问题:', debugInfo.diagnosis.issue)
    return false
  }
  
  console.log('🔧 开始自动修复认证问题:', debugInfo.diagnosis.issue)
  
  const { logout, hydrate } = useAuthStore.getState()
  
  switch (debugInfo.diagnosis.issue) {
    case 'Token已过期':
    case '认证状态不一致：已认证但Token无效':
    case '认证数据不完整':
      // 清理所有认证数据
      clearAllAuthData()
      console.log('✅ 已清理损坏的认证数据')
      return true
      
    case 'Zustand状态丢失，但localStorage有有效数据':
      // 手动触发状态恢复
      hydrate()
      console.log('✅ 已触发状态恢复')
      return true
      
    default:
      console.log('❓ 未知的认证问题，无法自动修复')
      return false
  }
}

/**
 * 清理所有认证数据
 */
export function clearAllAuthData(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
    localStorage.removeItem('role')
    localStorage.removeItem('factoryId')
    localStorage.removeItem('auth-storage')
  }
  
  // 重置Zustand状态
  useAuthStore.getState().logout()
}

/**
 * 打印认证状态调试信息
 */
export function printAuthDebugInfo(): void {
  const debugInfo = getAuthDebugInfo()
  
  console.group('🔍 认证状态调试信息')
  console.log('📊 Zustand状态:', debugInfo.zustandState)
  console.log('💾 localStorage状态:', debugInfo.localStorageState)
  console.log('🎫 Token状态:', debugInfo.tokenState)
  console.log('🩺 诊断结果:', debugInfo.diagnosis)
  console.groupEnd()
}
