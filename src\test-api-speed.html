<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 速度测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #1976D2;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 API 速度测试工具</h1>
        <p>测试 DeepSeek API 的连接速度和响应时间，帮助诊断性能问题。</p>
        
        <div class="status info">
            <strong>📋 测试项目：</strong><br>
            • 网络连接测试<br>
            • API 响应时间测试<br>
            • 简单请求测试<br>
            • 复杂请求测试
        </div>
        
        <button class="button" onclick="testNetworkSpeed()">🌐 测试网络连接</button>
        <button class="button" onclick="testSimpleAPI()">⚡ 测试简单API</button>
        <button class="button" onclick="testComplexAPI()">🔧 测试复杂API</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        let testResults = [];
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const result = `[${timestamp}] ${title}\n${content}\n`;
            testResults.push(result);
            
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = result;
            resultsDiv.appendChild(resultDiv);
            
            // 滚动到底部
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function testNetworkSpeed() {
            addResult('🌐 网络连接测试', '开始测试到 DeepSeek API 的网络连接...', 'info');
            
            const startTime = performance.now();
            
            try {
                // 测试基本连接
                const response = await fetch('https://api.deepseek.com/v1/models', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer sk-test' // 使用无效key测试连接
                    }
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '✅ 网络连接测试完成',
                    `连接时间: ${duration.toFixed(2)}ms\n状态码: ${response.status}\n${duration < 1000 ? '网络连接良好' : '网络连接较慢'}`,
                    duration < 1000 ? 'success' : 'warning'
                );
                
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '❌ 网络连接测试失败',
                    `耗时: ${duration.toFixed(2)}ms\n错误: ${error.message}\n可能是网络问题或防火墙阻止`,
                    'error'
                );
            }
        }
        
        async function testSimpleAPI() {
            addResult('⚡ 简单API测试', '测试最简单的API请求...', 'info');
            
            const startTime = performance.now();
            
            const requestBody = {
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'user',
                        content: '你好'
                    }
                ],
                max_tokens: 10,
                stream: false
            };
            
            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer YOUR_API_KEY_HERE' // 需要替换为真实的API Key
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(
                        '✅ 简单API测试成功',
                        `响应时间: ${duration.toFixed(2)}ms\n响应长度: ${JSON.stringify(data).length} 字符\n${duration < 5000 ? 'API响应速度正常' : 'API响应较慢'}`,
                        duration < 5000 ? 'success' : 'warning'
                    );
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ 简单API测试失败',
                        `响应时间: ${duration.toFixed(2)}ms\n状态码: ${response.status}\n错误: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '❌ 简单API测试异常',
                    `耗时: ${duration.toFixed(2)}ms\n错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        async function testComplexAPI() {
            addResult('🔧 复杂API测试', '测试风口识别的完整请求...', 'info');
            
            const startTime = performance.now();
            
            const complexPrompt = `识别风口订单，返回JSON。

文本：项目：测试项目
一楼：
出风口 2665×155 白色 1个
回风口 1200×300 白色 1个

规则：
1. 风口类型：
   - 宽度≤254mm → "systemType": "double_white_outlet", "originalType": "出风口"
   - 宽度≥255mm → "systemType": "white_return", "originalType": "回风口"

必须返回完整JSON：
{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet","dimensions":{"length":2665,"width":155,"unit":"mm"}}]}]}]}]}`;
            
            const requestBody = {
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'user',
                        content: complexPrompt
                    }
                ],
                max_tokens: 1500,
                stream: false
            };
            
            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer YOUR_API_KEY_HERE' // 需要替换为真实的API Key
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(
                        '✅ 复杂API测试成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)\n请求大小: ${JSON.stringify(requestBody).length} 字符\n响应大小: ${JSON.stringify(data).length} 字符\n${duration < 15000 ? 'API性能正常' : 'API性能较慢，可能需要优化'}`,
                        duration < 15000 ? 'success' : 'warning'
                    );
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ 复杂API测试失败',
                        `响应时间: ${duration.toFixed(2)}ms\n状态码: ${response.status}\n错误: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '❌ 复杂API测试异常',
                    `耗时: ${duration.toFixed(2)}ms\n错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = [];
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult(
                '📋 使用说明',
                '1. 首先测试网络连接\n2. 如果需要测试API，请先在代码中替换真实的API Key\n3. 观察各项测试的响应时间\n4. 正常情况下：网络连接<1秒，简单API<5秒，复杂API<15秒',
                'info'
            );
        };
    </script>
</body>
</html>
