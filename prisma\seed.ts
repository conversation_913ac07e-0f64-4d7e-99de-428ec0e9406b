/**
 * 🇨🇳 风口云平台 - 数据库种子数据
 * 用于初始化开发环境的基础数据
 */

import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 开始初始化数据库种子数据...')

  // 创建管理员账户
  const adminPassword = await bcrypt.hash('admin@yhg2025', 12)
  const admin = await prisma.admin.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      passwordHash: adminPassword,
      name: '系统管理员',
      email: '<EMAIL>',
      role: 'admin',
      isActive: true
    }
  })
  console.log('✅ 管理员账户创建成功:', admin.username)

  // 创建测试工厂
  const factory = await prisma.factory.upsert({
    where: { code: 'lin001' },
    update: {},
    create: {
      id: 'lin001',
      name: '林氏风口厂',
      code: 'lin001',
      address: '广东省佛山市南海区',
      phone: '13800138000',
      email: '<EMAIL>',
      status: 'active',
      settings: JSON.stringify({
        factoryName: '林氏风口厂',
        factoryCode: 'lin001',
        address: '广东省佛山市南海区',
        contactPhone: '13800138000',
        email: '<EMAIL>',
        orderNumberPrefix: 'LIN',
        defaultUnitPrices: {
          // 常规风口 (元/㎡)
          double_white_outlet: 150,
          double_black_outlet: 150,
          white_black_bottom_outlet: 150,
          white_black_bottom_return: 150,
          white_return: 150,
          black_return: 150,
          white_linear: 150,
          black_linear: 150,
          white_linear_return: 150,
          black_linear_return: 150,
          maintenance: 150,
          // 高端风口 (元/m) - 统一命名
          high_end_white_outlet: 80,
          high_end_black_outlet: 80,
          high_end_white_return: 80,
          high_end_black_return: 80,
          arrow_outlet: 90,
          arrow_return: 90,
          claw_outlet: 100,
          claw_return: 100,
          black_white_dual_outlet: 85,
          black_white_dual_return: 85,
          wood_grain_outlet: 120,
          wood_grain_return: 120,
          white_putty_outlet: 110,
          white_putty_return: 110,
          black_putty_outlet: 110,
          black_putty_return: 110,
          white_gypsum_outlet: 130,
          white_gypsum_return: 130,
          black_gypsum_outlet: 130,
          black_gypsum_return: 130,
          wood_grain_outlet: 100,
          wood_grain_return: 100,
          white_putty_outlet: 100,
          white_putty_return: 100,
          black_putty_outlet: 100,
          black_putty_return: 100,
          white_gypsum_outlet: 100,
          white_gypsum_return: 100,
          black_gypsum_outlet: 100,
          black_gypsum_return: 100
        },
        lastUpdated: new Date().toISOString(),
        updatedBy: 'seed'
      })
    }
  })
  console.log('✅ 测试工厂创建成功:', factory.name)

  // 创建工厂用户
  const factoryUserPassword = await bcrypt.hash('lin123', 10)
  const factoryUser = await prisma.factoryUser.upsert({
    where: { username: 'lin001' },
    update: {},
    create: {
      username: 'lin001',
      passwordHash: factoryUserPassword,
      name: '林老板',
      role: 'owner',
      permissions: '[]',
      isActive: true,
      factoryId: 'lin001'
    }
  })
  console.log('✅ 工厂用户创建成功:', factoryUser.username)

  // 创建用户记忆中的测试账户
  const testUserPassword = await bcrypt.hash('shh@2025', 10)
  const testUser = await prisma.factoryUser.upsert({
    where: { username: 'sh002' },
    update: {},
    create: {
      username: 'sh002',
      passwordHash: testUserPassword,
      name: '测试用户',
      role: 'owner',
      permissions: '[]',
      isActive: true,
      factoryId: 'lin001'
    }
  })
  console.log('✅ 测试用户创建成功:', testUser.username)

  // 创建额外的测试账户 sh001
  const testUser2Password = await bcrypt.hash('shh@2025', 10)
  const testUser2 = await prisma.factoryUser.upsert({
    where: { username: 'sh001' },
    update: {},
    create: {
      username: 'sh001',
      passwordHash: testUser2Password,
      name: '测试用户001',
      role: 'owner',
      permissions: '[]',
      isActive: true,
      factoryId: 'lin001'
    }
  })
  console.log('✅ 测试用户001创建成功:', testUser2.username)

  // 创建测试客户
  const client = await prisma.client.upsert({
    where: {
      id: 'test-client-001'
    },
    update: {},
    create: {
      id: 'test-client-001',
      name: '张三',
      phone: '13900139000',
      address: '广东省广州市天河区',
      status: 'active',
      factoryId: 'lin001'
    }
  })
  console.log('✅ 测试客户创建成功:', client.name)

  console.log('🎉 数据库种子数据初始化完成！')
  console.log('')
  console.log('📋 登录信息:')
  console.log('管理员: admin / admin@yhg2025')
  console.log('工厂用户: lin001 / lin123')
  console.log('测试用户: sh002 / shh@2025')
  console.log('测试用户: sh001 / shh@2025')
}

main()
  .catch((e) => {
    console.error('❌ 种子数据初始化失败:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
