import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 测试工厂管理员数据...')
    
    // 获取所有工厂及其管理员信息
    const factories = await db.getFactories()
    
    const result = factories.map(factory => ({
      id: factory.id,
      name: factory.name,
      code: factory.code,
      ownerName: factory.ownerName,
      address: factory.address,
      phone: factory.phone
    }))
    
    console.log('📋 工厂管理员数据:', result)
    
    return NextResponse.json({
      success: true,
      data: result
    })
    
  } catch (error) {
    console.error('❌ 获取工厂管理员数据失败:', error)
    return NextResponse.json({
      success: false,
      error: '获取数据失败'
    }, { status: 500 })
  }
}
