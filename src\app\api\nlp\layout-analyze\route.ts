import { NextRequest, NextResponse } from 'next/server'
import { recognizeLayout } from '@/lib/nlp/layout-recognition'

/**
 * 🇨🇳 排版识别分析API
 * 
 * 专门分析师傅手写习惯和排版模式
 */
export async function POST(request: NextRequest) {
  try {
    console.log('📐 开始排版识别分析...')
    
    const body = await request.json()
    const { text } = body
    
    if (!text || typeof text !== 'string') {
      return NextResponse.json({
        success: false,
        error: '缺少文本内容'
      }, { status: 400 })
    }
    
    console.log('📝 输入文本长度:', text.length)
    console.log('👨‍🔧 分析师傅手写习惯...')
    
    const startTime = Date.now()
    
    // 执行排版识别
    const layoutResult = recognizeLayout(text)
    
    const processingTime = Date.now() - startTime
    
    console.log('✅ 排版识别完成')
    console.log(`📊 排版类型: ${layoutResult.layoutType}`)
    console.log(`🎯 置信度: ${(layoutResult.confidence * 100).toFixed(1)}%`)
    console.log(`📦 分组数: ${layoutResult.ventGroups.length}`)
    console.log(`⏱️ 处理时间: ${processingTime}ms`)
    
    // 生成详细分析报告
    const analysisReport = generateAnalysisReport(layoutResult, text)
    
    return NextResponse.json({
      success: true,
      data: {
        ...layoutResult,
        analysisReport,
        processingTime
      },
      metadata: {
        processingTime,
        timestamp: new Date().toISOString(),
        textLength: text.length,
        linesCount: text.split('\n').filter(line => line.trim()).length
      },
      message: '排版识别分析完成'
    })
    
  } catch (error) {
    console.error('❌ 排版识别分析失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '排版识别分析失败',
      message: '排版识别处理失败'
    }, { status: 500 })
  }
}

/**
 * 生成详细分析报告
 */
function generateAnalysisReport(layoutResult: any, originalText: string): any {
  const lines = originalText.split('\n').filter(line => line.trim())
  
  const report = {
    masterHabits: analyzeMasterHabits(layoutResult, lines),
    layoutFeatures: analyzeLayoutFeatures(layoutResult),
    qualityAssessment: assessQuality(layoutResult),
    recommendations: generateRecommendations(layoutResult)
  }
  
  return report
}

/**
 * 分析师傅手写习惯
 */
function analyzeMasterHabits(layoutResult: any, lines: string[]): any {
  const habits = {
    writingStyle: 'unknown',
    organization: 'unknown',
    consistency: 0,
    patterns: []
  }
  
  // 分析写作风格
  if (layoutResult.layoutType === 'column_based') {
    habits.writingStyle = 'column_writer'
    habits.organization = '师傅习惯分列写作，左边出风口，右边回风口'
    habits.patterns.push('分列排版习惯')
  } else if (layoutResult.layoutType === 'sequence_based') {
    habits.writingStyle = 'sequence_writer'
    habits.organization = '师傅习惯按序号顺序写作，从左到右，从上到下'
    habits.patterns.push('序号排版习惯')
  }
  
  // 分析一致性
  const totalItems = layoutResult.ventGroups.reduce((sum: number, group: any) => sum + group.items.length, 0)
  const highConfidenceItems = layoutResult.ventGroups.reduce((sum: number, group: any) => 
    sum + group.items.filter((item: any) => item.confidence > 0.7).length, 0)
  
  habits.consistency = totalItems > 0 ? highConfidenceItems / totalItems : 0
  
  // 检测楼层习惯
  const hasFloorInfo = layoutResult.ventGroups.some((group: any) => 
    group.items.some((item: any) => item.floor))
  if (hasFloorInfo) {
    habits.patterns.push('楼层标记习惯')
  }
  
  // 检测数量习惯
  const hasQuantityInfo = layoutResult.ventGroups.some((group: any) => 
    group.items.some((item: any) => item.quantity && item.quantity > 1))
  if (hasQuantityInfo) {
    habits.patterns.push('数量标记习惯')
  }
  
  return habits
}

/**
 * 分析排版特征
 */
function analyzeLayoutFeatures(layoutResult: any): any {
  const features = {
    structure: layoutResult.structure,
    complexity: 'simple',
    organization: 'good',
    readability: 'high'
  }
  
  // 评估复杂度
  const totalItems = layoutResult.ventGroups.reduce((sum: number, group: any) => sum + group.items.length, 0)
  if (totalItems > 20) {
    features.complexity = 'complex'
  } else if (totalItems > 10) {
    features.complexity = 'medium'
  }
  
  // 评估组织性
  if (layoutResult.layoutType === 'unknown') {
    features.organization = 'poor'
    features.readability = 'low'
  } else if (layoutResult.confidence < 0.7) {
    features.organization = 'fair'
    features.readability = 'medium'
  }
  
  return features
}

/**
 * 评估质量
 */
function assessQuality(layoutResult: any): any {
  const assessment = {
    overall: 'good',
    accuracy: layoutResult.confidence,
    completeness: 1.0,
    consistency: 0.8,
    issues: [],
    strengths: []
  }
  
  // 评估准确性
  if (layoutResult.confidence > 0.8) {
    assessment.strengths.push('识别准确性高')
  } else if (layoutResult.confidence < 0.6) {
    assessment.issues.push('识别准确性较低')
    assessment.overall = 'fair'
  }
  
  // 评估完整性
  const totalItems = layoutResult.ventGroups.reduce((sum: number, group: any) => sum + group.items.length, 0)
  if (totalItems === 0) {
    assessment.completeness = 0
    assessment.issues.push('未识别到任何风口项目')
    assessment.overall = 'poor'
  }
  
  // 检查警告
  if (layoutResult.warnings.length > 0) {
    assessment.issues.push(...layoutResult.warnings)
    if (assessment.overall === 'good') {
      assessment.overall = 'fair'
    }
  }
  
  // 检查优势
  if (layoutResult.layoutType !== 'unknown') {
    assessment.strengths.push('成功识别排版模式')
  }
  
  if (layoutResult.ventGroups.length > 1) {
    assessment.strengths.push('成功进行智能分组')
  }
  
  return assessment
}

/**
 * 生成建议
 */
function generateRecommendations(layoutResult: any): string[] {
  const recommendations: string[] = []
  
  // 基于排版类型的建议
  switch (layoutResult.layoutType) {
    case 'column_based':
      recommendations.push('检测到分列排版，建议保持左右分列的清晰性')
      recommendations.push('可以在列头添加"出风口"、"回风口"标识')
      break
    case 'sequence_based':
      recommendations.push('检测到序号排版，建议保持序号的连续性')
      recommendations.push('可以在序号前添加楼层信息便于管理')
      break
    case 'unknown':
      recommendations.push('建议采用更规范的排版格式')
      recommendations.push('推荐使用序号或分列的方式组织内容')
      break
  }
  
  // 基于质量的建议
  if (layoutResult.confidence < 0.7) {
    recommendations.push('建议提高手写清晰度，避免字迹模糊')
    recommendations.push('建议使用标准的尺寸格式，如"长x宽"')
  }
  
  // 基于项目数量的建议
  const totalItems = layoutResult.ventGroups.reduce((sum: number, group: any) => sum + group.items.length, 0)
  if (totalItems > 30) {
    recommendations.push('项目较多，建议分页或分区域记录')
    recommendations.push('建议添加小计和总计信息')
  }
  
  // 基于警告的建议
  if (layoutResult.warnings.length > 0) {
    recommendations.push('请仔细检查标记的异常项目')
    recommendations.push('建议人工确认识别结果的准确性')
  }
  
  return recommendations
}

/**
 * 获取支持的排版类型信息
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    data: {
      supportedLayouts: [
        {
          type: 'column_based',
          name: '分列排版',
          description: '师傅习惯左边写出风口，右边写回风口',
          example: '出风口    回风口\n140x240  300x1600',
          features: ['列头关键词', '左右分列', '类型分组']
        },
        {
          type: 'sequence_based',
          name: '序号排版',
          description: '师傅习惯按数字顺序编号，从左到右，从上到下',
          example: '1. 140x240 出风口\n2. 300x1600 回风口',
          features: ['数字序号', '顺序排列', '楼层标记']
        },
        {
          type: 'table_based',
          name: '表格排版',
          description: '规整的表格形式',
          example: '序号 | 类型 | 尺寸 | 数量',
          features: ['表格结构', '列分隔符', '规整排列']
        }
      ],
      masterHabits: [
        '分列写作习惯：左边出风口，右边回风口',
        '序号编排习惯：按1、2、3...顺序编号',
        '楼层标记习惯：3楼1、4楼2等楼层序号',
        '数量标记习惯：尺寸后面标注个数',
        '从左到右，从上到下的书写顺序'
      ],
      qualityIndicators: [
        '识别准确性：基于关键词和模式匹配',
        '排版一致性：结构规整程度评估',
        '内容完整性：项目信息完整度检查',
        '异常检测：超大尺寸和异常数量提醒'
      ]
    },
    message: '排版识别API信息'
  })
}
