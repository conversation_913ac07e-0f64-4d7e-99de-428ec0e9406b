<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 复杂尺寸识别测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover { background: #1976D2; }
        .button.success { background: #4CAF50; }
        .button.success:hover { background: #45a049; }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #4CAF50; background: #e8f5e8; }
        .error { border-color: #f44336; background: #ffe8e8; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        .test-input {
            width: 100%;
            height: 80px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .expected-list {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 复杂尺寸识别测试</h1>
        <p>测试改进后的尺寸识别逻辑，特别是小数点和紧密排列的情况</p>
        
        <div class="expected-list">
            <strong>📋 预期识别的9个风口：</strong><br>
            1. 客厅回风 141×30 → 1410×300mm (回风口)<br>
            2. 141×30 → 1410×300mm (回风口，重复尺寸)<br>
            3. 客出风 568.2×14.5 → 5682×145mm (出风口)<br>
            4. 房间出风 205.2×14.7 → 2052×147mm (出风口)<br>
            5. 房间回风 93.7×30 → 937×300mm (回风口，单边)<br>
            6. 房间回风 98×30 → 980×300mm (回风口，单边)<br>
            7. 出风 271.8×15.1 → 2718×151mm (出风口)<br>
            8. 房间回风 88×30 → 880×300mm (回风口，单边)<br>
            9. 房间出风口 243.2×15.1 → 2432×151mm (出风口)
        </div>
        
        <div>
            <label for="testInput"><strong>测试文本：</strong></label>
            <textarea id="testInput" class="test-input">客厅回风141*30     141*30      客出风568.2*14.5    房间出风205.2*14.7     房间回风93.7*30（单边）  房间回风98*30（单边）   出风271.8*15.1    房间回风88*30（单边）房间出风口243.2*15.1</textarea>
        </div>
        
        <button class="button success" onclick="testComplexDimensions()">🧪 测试复杂尺寸识别</button>
        <button class="button" onclick="testRegexExtraction()">🔧 测试正则提取</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_KEY = 'sk-c9047196d51d4f6c99dc94f9fbef602c';
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}] ${title}</strong>\n${content}`;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function testRegexExtraction() {
            const testText = document.getElementById('testInput').value.trim();
            addResult('🔧 正则表达式测试', '测试改进后的尺寸提取逻辑...', 'info');
            
            // 改进的正则表达式：支持小数点
            const dimensionRegex = /(\d+(?:\.\d+)?)\s*[×xX*✘]\s*(\d+(?:\.\d+)?)/g;
            const dimensions = testText.match(dimensionRegex) || [];
            
            addResult(
                '📊 正则提取结果',
                `找到 ${dimensions.length} 个尺寸匹配:
${dimensions.map((dim, i) => `${i + 1}. ${dim}`).join('\n')}

预期: 9个尺寸
实际: ${dimensions.length}个尺寸
状态: ${dimensions.length === 9 ? '✅ 完美' : '❌ 有遗漏'}`,
                dimensions.length === 9 ? 'success' : 'warning'
            );
            
            // 处理每个尺寸
            const processedVents = [];
            dimensions.forEach((dim, index) => {
                const match = dim.match(/(\d+(?:\.\d+)?)\s*[×xX*✘]\s*(\d+(?:\.\d+)?)/);
                if (match) {
                    const [, lengthStr, widthStr] = match;
                    let l = parseFloat(lengthStr);
                    let w = parseFloat(widthStr);
                    
                    // 处理小数点异常
                    if (l % 1 !== 0 || w % 1 !== 0) {
                        const lengthFixed = lengthStr.replace('.', '');
                        const widthFixed = widthStr.replace('.', '');
                        const lFixed = parseInt(lengthFixed);
                        const wFixed = parseInt(widthFixed);
                        
                        if (lFixed > 50 && lFixed < 10000 && wFixed > 10 && wFixed < 1000) {
                            l = lFixed;
                            w = wFixed;
                        } else {
                            l = Math.round(l);
                            w = Math.round(w);
                        }
                    }
                    
                    // 智能单位转换
                    const correctedL = l < 100 ? l * 10 : l;
                    const correctedW = w < 100 ? w * 10 : w;
                    
                    // 确保大值作为长度
                    const finalLength = Math.max(correctedL, correctedW);
                    const finalWidth = Math.min(correctedL, correctedW);
                    
                    processedVents.push({
                        original: dim,
                        processed: `${finalLength}×${finalWidth}mm`,
                        type: finalWidth < 255 ? '出风口' : '回风口'
                    });
                }
            });
            
            addResult(
                '🔄 尺寸处理结果',
                processedVents.map((vent, i) => 
                    `${i + 1}. ${vent.original} → ${vent.processed} (${vent.type})`
                ).join('\n'),
                'info'
            );
        }
        
        async function testComplexDimensions() {
            const testText = document.getElementById('testInput').value.trim();
            if (!testText) {
                addResult('❌ 错误', '请输入测试文本', 'error');
                return;
            }
            
            addResult('🧪 复杂尺寸识别测试', '使用改进后的DeepSeek API测试...', 'info');
            
            const improvedPrompt = `解析风口订单为JSON格式。

输入文本：
${testText}

解析规则：
- 提取项目名称和客户信息
- 宽度≤254mm = 出风口 (systemType: "double_white_outlet")
- 宽度≥255mm = 回风口 (systemType: "white_return")
- 数字<100当作厘米，需要×10转换为毫米
- 小数点尺寸修正：568.2×14.5 → 5682×145mm
- 必须识别每一个风口，包括重复尺寸

返回格式（必须是有效的JSON）：
{
  "projects": [{
    "projectName": "项目名称",
    "clientInfo": "客户信息",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间名称",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": "备注信息"
          }
        ]
      }]
    }]
  }]
}

请确保返回完整有效的JSON，识别所有9个风口。`;

            const startTime = performance.now();
            
            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [{ role: 'user', content: improvedPrompt }],
                        max_tokens: 1500,
                        temperature: 0.2,
                        stream: false
                    }),
                    signal: AbortSignal.timeout(30000)
                });
                
                const duration = performance.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0]?.message?.content;
                    
                    addResult(
                        '✅ API调用成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
Token使用: ${data.usage?.total_tokens || 'N/A'}
响应长度: ${content?.length || 0} 字符`,
                        'success'
                    );
                    
                    // 解析JSON并验证
                    try {
                        let jsonStr = content.trim();
                        jsonStr = jsonStr.replace(/^```json\s*/i, '');
                        jsonStr = jsonStr.replace(/^```\s*/i, '');
                        jsonStr = jsonStr.replace(/\s*```\s*$/g, '');
                        
                        const firstBrace = jsonStr.indexOf('{');
                        const lastBrace = jsonStr.lastIndexOf('}');
                        
                        if (firstBrace !== -1 && lastBrace !== -1) {
                            jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
                        }
                        
                        const parsed = JSON.parse(jsonStr);
                        const project = parsed.projects?.[0];
                        
                        if (project) {
                            const ventCount = project.floors?.[0]?.rooms?.reduce((total, room) => 
                                total + (room.vents?.length || 0), 0) || 0;
                            
                            // 提取所有风口信息
                            const allVents = [];
                            project.floors?.[0]?.rooms?.forEach(room => {
                                room.vents?.forEach(vent => {
                                    allVents.push({
                                        type: vent.originalType || vent.systemType,
                                        dimensions: `${vent.dimensions.length}×${vent.dimensions.width}mm`,
                                        notes: vent.notes || ''
                                    });
                                });
                            });
                            
                            addResult(
                                '🎯 识别结果分析',
                                `识别的风口数量: ${ventCount}/9 ${ventCount === 9 ? '✅' : '❌'}

详细风口列表:
${allVents.map((vent, i) => 
    `${i + 1}. ${vent.type} ${vent.dimensions} ${vent.notes ? `(${vent.notes})` : ''}`
).join('\n')}

完整数据:
${JSON.stringify(parsed, null, 2)}`,
                                ventCount === 9 ? 'success' : 'warning'
                            );
                        }
                        
                    } catch (parseError) {
                        addResult(
                            '❌ JSON解析失败',
                            `解析错误: ${parseError.message}
原始响应: ${content}`,
                            'error'
                        );
                    }
                    
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ API调用失败',
                        `状态码: ${response.status}
错误信息: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const duration = performance.now() - startTime;
                addResult(
                    '❌ API调用异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult(
                '📋 复杂尺寸识别测试说明',
                `🎯 目标: 验证复杂尺寸识别修复
🔧 修复内容:
  • 支持小数点尺寸 (568.2×14.5)
  • 改进正则表达式匹配
  • 智能小数点修正
  • 重复尺寸处理

点击"🔧 测试正则提取"先验证正则表达式！`,
                'info'
            );
        };
    </script>
</body>
</html>
