/**
 * 🇨🇳 风口云平台 - NLP学习系统
 * 
 * 模拟机器学习的学习和优化过程
 */

// 学习记录接口
export interface LearningRecord {
  id: string
  timestamp: Date
  inputText: string
  processingResult: {
    intent: string
    confidence: number
    itemsFound: number
    processingTime: number
  }
  learningPoints: LearningPoint[]
  optimizations: OptimizationSuggestion[]
}

// 学习点接口
export interface LearningPoint {
  type: 'pattern' | 'keyword' | 'context' | 'error'
  description: string
  example: string
  confidence: number
  frequency: number
}

// 优化建议接口
export interface OptimizationSuggestion {
  type: 'preprocessing' | 'classification' | 'extraction' | 'validation'
  priority: 'high' | 'medium' | 'low'
  description: string
  expectedImprovement: string
  implementation: string
}

// 学习统计接口
export interface LearningStats {
  totalProcessed: number
  averageConfidence: number
  commonPatterns: string[]
  improvementTrends: {
    accuracy: number[]
    speed: number[]
    confidence: number[]
  }
  errorPatterns: string[]
}

// 模拟学习历史存储
let learningHistory: LearningRecord[] = []
let learningStats: LearningStats = {
  totalProcessed: 0,
  averageConfidence: 0,
  commonPatterns: [],
  improvementTrends: {
    accuracy: [],
    speed: [],
    confidence: []
  },
  errorPatterns: []
}

/**
 * 记录学习数据
 */
export function recordLearning(
  inputText: string,
  intentResult: any,
  processingTime: number
): LearningRecord {
  const learningRecord: LearningRecord = {
    id: `learning_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date(),
    inputText,
    processingResult: {
      intent: intentResult.intent,
      confidence: intentResult.confidence,
      itemsFound: intentResult.entities.ventItems.length,
      processingTime
    },
    learningPoints: extractLearningPoints(inputText, intentResult),
    optimizations: generateOptimizations(inputText, intentResult)
  }

  // 添加到历史记录
  learningHistory.push(learningRecord)
  
  // 更新统计信息
  updateLearningStats(learningRecord)
  
  console.log('📚 学习记录已保存:', learningRecord.id)
  console.log('🎯 学习点数量:', learningRecord.learningPoints.length)
  console.log('💡 优化建议数量:', learningRecord.optimizations.length)
  
  return learningRecord
}

/**
 * 提取学习点
 */
function extractLearningPoints(inputText: string, intentResult: any): LearningPoint[] {
  const learningPoints: LearningPoint[] = []
  
  // 模式学习点
  const dimensionMatches = inputText.match(/\d+\s*[xX×*]\s*\d+/g) || []
  if (dimensionMatches.length > 0) {
    learningPoints.push({
      type: 'pattern',
      description: '尺寸模式识别',
      example: dimensionMatches[0],
      confidence: 0.9,
      frequency: dimensionMatches.length
    })
  }
  
  // 关键词学习点
  const ventKeywords = ['出风', '回风', '线型', '检修']
  const foundKeywords = ventKeywords.filter(keyword => inputText.includes(keyword))
  if (foundKeywords.length > 0) {
    learningPoints.push({
      type: 'keyword',
      description: '风口类型关键词',
      example: foundKeywords.join(', '),
      confidence: 0.8,
      frequency: foundKeywords.length
    })
  }
  
  // 上下文学习点
  if (intentResult.entities.projectName || intentResult.entities.floorInfo) {
    learningPoints.push({
      type: 'context',
      description: '项目上下文信息',
      example: intentResult.entities.projectName || intentResult.entities.floorInfo,
      confidence: 0.7,
      frequency: 1
    })
  }
  
  // 错误学习点
  const lowConfidenceItems = intentResult.entities.ventItems.filter((item: any) => item.confidence < 0.6)
  if (lowConfidenceItems.length > 0) {
    learningPoints.push({
      type: 'error',
      description: '低置信度识别项目',
      example: lowConfidenceItems[0].originalText,
      confidence: lowConfidenceItems[0].confidence,
      frequency: lowConfidenceItems.length
    })
  }
  
  return learningPoints
}

/**
 * 生成优化建议
 */
function generateOptimizations(inputText: string, intentResult: any): OptimizationSuggestion[] {
  const optimizations: OptimizationSuggestion[] = []
  
  // 预处理优化
  if (inputText.includes('×') || inputText.includes('X') || inputText.includes('*')) {
    optimizations.push({
      type: 'preprocessing',
      priority: 'medium',
      description: '符号标准化优化',
      expectedImprovement: '提升5-10%识别准确率',
      implementation: '增强符号替换规则'
    })
  }
  
  // 分类优化
  if (intentResult.confidence < 0.8) {
    optimizations.push({
      type: 'classification',
      priority: 'high',
      description: '意图分类置信度提升',
      expectedImprovement: '提升15-20%分类准确率',
      implementation: '调整关键词权重和阈值'
    })
  }
  
  // 提取优化
  const avgItemConfidence = intentResult.entities.ventItems.reduce(
    (sum: number, item: any) => sum + item.confidence, 0
  ) / intentResult.entities.ventItems.length
  
  if (avgItemConfidence < 0.7) {
    optimizations.push({
      type: 'extraction',
      priority: 'high',
      description: '实体提取准确性改进',
      expectedImprovement: '提升10-15%提取准确率',
      implementation: '优化正则表达式和上下文分析'
    })
  }
  
  // 验证优化
  if (intentResult.entities.ventItems.length === 0) {
    optimizations.push({
      type: 'validation',
      priority: 'high',
      description: '输入验证增强',
      expectedImprovement: '减少50%无效输入处理',
      implementation: '添加输入格式预检查'
    })
  }
  
  return optimizations
}

/**
 * 更新学习统计
 */
function updateLearningStats(record: LearningRecord): void {
  learningStats.totalProcessed++
  
  // 更新平均置信度
  const totalConfidence = learningStats.averageConfidence * (learningStats.totalProcessed - 1) + 
                         record.processingResult.confidence
  learningStats.averageConfidence = totalConfidence / learningStats.totalProcessed
  
  // 更新趋势数据
  learningStats.improvementTrends.confidence.push(record.processingResult.confidence)
  learningStats.improvementTrends.speed.push(record.processingResult.processingTime)
  learningStats.improvementTrends.accuracy.push(record.processingResult.confidence)
  
  // 保持最近20条记录
  if (learningStats.improvementTrends.confidence.length > 20) {
    learningStats.improvementTrends.confidence.shift()
    learningStats.improvementTrends.speed.shift()
    learningStats.improvementTrends.accuracy.shift()
  }
  
  // 更新常见模式
  const patterns = record.learningPoints
    .filter(point => point.type === 'pattern')
    .map(point => point.example)
  
  patterns.forEach(pattern => {
    if (!learningStats.commonPatterns.includes(pattern)) {
      learningStats.commonPatterns.push(pattern)
    }
  })
  
  // 更新错误模式
  const errors = record.learningPoints
    .filter(point => point.type === 'error')
    .map(point => point.description)
  
  errors.forEach(error => {
    if (!learningStats.errorPatterns.includes(error)) {
      learningStats.errorPatterns.push(error)
    }
  })
}

/**
 * 获取学习历史
 */
export function getLearningHistory(limit: number = 10): LearningRecord[] {
  return learningHistory
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, limit)
}

/**
 * 获取学习统计
 */
export function getLearningStats(): LearningStats {
  return { ...learningStats }
}

/**
 * 获取改进建议
 */
export function getImprovementSuggestions(): OptimizationSuggestion[] {
  const allOptimizations = learningHistory.flatMap(record => record.optimizations)
  
  // 按优先级和频率排序
  const optimizationCounts = new Map<string, { count: number, optimization: OptimizationSuggestion }>()
  
  allOptimizations.forEach(opt => {
    const key = opt.description
    if (optimizationCounts.has(key)) {
      optimizationCounts.get(key)!.count++
    } else {
      optimizationCounts.set(key, { count: 1, optimization: opt })
    }
  })
  
  return Array.from(optimizationCounts.values())
    .sort((a, b) => {
      // 先按优先级排序
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      const priorityDiff = priorityOrder[b.optimization.priority] - priorityOrder[a.optimization.priority]
      if (priorityDiff !== 0) return priorityDiff
      
      // 再按频率排序
      return b.count - a.count
    })
    .slice(0, 5)
    .map(item => ({
      ...item.optimization,
      description: `${item.optimization.description} (出现${item.count}次)`
    }))
}

/**
 * 模拟学习进度
 */
export function simulateLearningProgress(): {
  currentAccuracy: number
  projectedImprovement: number
  learningRate: number
  nextMilestone: string
} {
  const recentConfidences = learningStats.improvementTrends.confidence.slice(-10)
  const currentAccuracy = recentConfidences.length > 0 
    ? recentConfidences.reduce((sum, conf) => sum + conf, 0) / recentConfidences.length
    : 0.5
  
  const learningRate = recentConfidences.length > 1
    ? (recentConfidences[recentConfidences.length - 1] - recentConfidences[0]) / recentConfidences.length
    : 0.01
  
  const projectedImprovement = Math.min(currentAccuracy + learningRate * 10, 0.95)
  
  let nextMilestone = '达到90%准确率'
  if (currentAccuracy >= 0.9) nextMilestone = '优化处理速度'
  if (currentAccuracy >= 0.95) nextMilestone = '增强复杂场景处理'
  
  return {
    currentAccuracy,
    projectedImprovement,
    learningRate,
    nextMilestone
  }
}
