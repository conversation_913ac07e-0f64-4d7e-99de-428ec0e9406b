import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 追加投资
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: shareholderId } = await params
    const body = await request.json()
    
    console.log('💰 收到追加投资请求:', {
      shareholderId,
      amount: body.amount,
      notes: body.notes
    })

    // 验证必填字段
    if (!body.amount || body.amount <= 0) {
      return NextResponse.json({
        success: false,
        error: '投资金额必须大于0'
      }, { status: 400 })
    }

    // 获取股东信息
    const shareholder = await db.getShareholderById(shareholderId)
    if (!shareholder) {
      return NextResponse.json({
        success: false,
        error: '股东不存在'
      }, { status: 404 })
    }

    // 追加投资
    const result = await db.addShareholderInvestment(shareholderId, {
      amount: parseFloat(body.amount),
      notes: body.notes?.trim() || null
    })

    if (result.success) {
      console.log('✅ 追加投资成功')
      return NextResponse.json({
        success: true,
        message: '追加投资记录成功',
        shareholder: result.shareholder
      })
    } else {
      console.error('❌ 追加投资失败:', result.error)
      return NextResponse.json({
        success: false,
        error: result.error || '追加投资失败'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('❌ 追加投资API错误:', error)
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
}
