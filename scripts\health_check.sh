#!/bin/bash
# health_check.sh - 风口云平台健康检查脚本
# 使用方法: ./health_check.sh [--detailed] [--json]

DETAILED=false
JSON_OUTPUT=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --detailed)
      DETAILED=true
      shift
      ;;
    --json)
      JSON_OUTPUT=true
      shift
      ;;
    -h|--help)
      echo "使用方法: $0 [选项]"
      echo "选项:"
      echo "  --detailed   显示详细信息"
      echo "  --json       JSON格式输出"
      echo "  -h, --help   显示帮助信息"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      exit 1
      ;;
  esac
done

# 健康检查结果
declare -A health_status
health_status[overall]="healthy"

# 函数：记录检查结果
record_check() {
  local component="$1"
  local status="$2"
  local message="$3"
  
  health_status[$component]="$status"
  
  if [ "$status" != "healthy" ]; then
    health_status[overall]="unhealthy"
  fi
  
  if [ "$JSON_OUTPUT" = false ]; then
    local icon="✅"
    case $status in
      "unhealthy") icon="❌" ;;
      "warning") icon="⚠️" ;;
    esac
    echo "$icon $component: $message"
  fi
}

# 函数：检查Docker服务
check_docker_services() {
  if [ "$JSON_OUTPUT" = false ]; then
    echo "🐳 检查Docker服务..."
  fi
  
  if ! command -v docker-compose &> /dev/null; then
    record_check "docker_compose" "unhealthy" "Docker Compose未安装"
    return
  fi
  
  # 检查服务状态
  local services_output=$(docker-compose ps 2>/dev/null)
  if [ $? -ne 0 ]; then
    record_check "docker_services" "unhealthy" "无法获取服务状态"
    return
  fi
  
  # 检查各个服务
  local app_status=$(echo "$services_output" | grep "app" | grep -c "Up")
  local postgres_status=$(echo "$services_output" | grep "postgres" | grep -c "Up")
  local nginx_status=$(echo "$services_output" | grep "nginx" | grep -c "Up")
  
  if [ "$app_status" -eq 1 ]; then
    record_check "app_service" "healthy" "应用服务运行正常"
  else
    record_check "app_service" "unhealthy" "应用服务未运行"
  fi
  
  if [ "$postgres_status" -eq 1 ]; then
    record_check "database_service" "healthy" "数据库服务运行正常"
  else
    record_check "database_service" "unhealthy" "数据库服务未运行"
  fi
  
  if [ "$nginx_status" -eq 1 ]; then
    record_check "nginx_service" "healthy" "Nginx服务运行正常"
  else
    record_check "nginx_service" "warning" "Nginx服务未运行（可选）"
  fi
}

# 函数：检查应用健康
check_app_health() {
  if [ "$JSON_OUTPUT" = false ]; then
    echo "🏥 检查应用健康..."
  fi
  
  # HTTP健康检查
  local health_response=$(curl -s -w "%{http_code}" http://localhost:3000/api/health 2>/dev/null)
  local http_code="${health_response: -3}"
  
  if [ "$http_code" = "200" ]; then
    record_check "http_health" "healthy" "HTTP健康检查通过"
  else
    record_check "http_health" "unhealthy" "HTTP健康检查失败 (状态码: $http_code)"
  fi
  
  # 检查应用响应时间
  local response_time=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:3000/api/health 2>/dev/null)
  if [ -n "$response_time" ]; then
    local response_ms=$(echo "$response_time * 1000" | bc 2>/dev/null || echo "0")
    if (( $(echo "$response_time < 2.0" | bc -l 2>/dev/null || echo 0) )); then
      record_check "response_time" "healthy" "响应时间正常 (${response_ms}ms)"
    else
      record_check "response_time" "warning" "响应时间较慢 (${response_ms}ms)"
    fi
  fi
}

# 函数：检查数据库连接
check_database() {
  if [ "$JSON_OUTPUT" = false ]; then
    echo "🗄️ 检查数据库..."
  fi
  
  # 检查数据库连接
  if docker-compose exec -T postgres psql -U factorysystem -c "SELECT 1;" > /dev/null 2>&1; then
    record_check "database_connection" "healthy" "数据库连接正常"
  else
    record_check "database_connection" "unhealthy" "数据库连接失败"
    return
  fi
  
  # 检查关键表
  local table_count=$(docker-compose exec -T postgres psql -U factorysystem -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
  if [ "$table_count" -gt 10 ]; then
    record_check "database_tables" "healthy" "数据库表结构正常 ($table_count 个表)"
  else
    record_check "database_tables" "warning" "数据库表数量异常 ($table_count 个表)"
  fi
  
  # 检查数据库大小
  if [ "$DETAILED" = true ]; then
    local db_size=$(docker-compose exec -T postgres psql -U factorysystem -t -c "SELECT pg_size_pretty(pg_database_size('factorysystem'));" 2>/dev/null | tr -d ' ')
    if [ -n "$db_size" ]; then
      record_check "database_size" "healthy" "数据库大小: $db_size"
    fi
  fi
}

# 函数：检查系统资源
check_system_resources() {
  if [ "$JSON_OUTPUT" = false ]; then
    echo "💻 检查系统资源..."
  fi
  
  # 检查磁盘空间
  local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
  if [ "$disk_usage" -lt 80 ]; then
    record_check "disk_space" "healthy" "磁盘使用率: ${disk_usage}%"
  elif [ "$disk_usage" -lt 90 ]; then
    record_check "disk_space" "warning" "磁盘使用率较高: ${disk_usage}%"
  else
    record_check "disk_space" "unhealthy" "磁盘空间不足: ${disk_usage}%"
  fi
  
  # 检查内存使用
  local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
  if [ "$memory_usage" -lt 80 ]; then
    record_check "memory_usage" "healthy" "内存使用率: ${memory_usage}%"
  elif [ "$memory_usage" -lt 90 ]; then
    record_check "memory_usage" "warning" "内存使用率较高: ${memory_usage}%"
  else
    record_check "memory_usage" "unhealthy" "内存使用率过高: ${memory_usage}%"
  fi
  
  # 检查CPU负载
  local cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
  local cpu_cores=$(nproc)
  local load_percentage=$(echo "scale=0; $cpu_load * 100 / $cpu_cores" | bc 2>/dev/null || echo "0")
  
  if [ "$load_percentage" -lt 70 ]; then
    record_check "cpu_load" "healthy" "CPU负载正常: ${load_percentage}%"
  elif [ "$load_percentage" -lt 90 ]; then
    record_check "cpu_load" "warning" "CPU负载较高: ${load_percentage}%"
  else
    record_check "cpu_load" "unhealthy" "CPU负载过高: ${load_percentage}%"
  fi
}

# 函数：检查日志错误
check_logs() {
  if [ "$JSON_OUTPUT" = false ]; then
    echo "📋 检查应用日志..."
  fi
  
  # 检查最近的错误日志
  local error_count=$(docker-compose logs --tail=100 app 2>/dev/null | grep -i error | wc -l)
  
  if [ "$error_count" -eq 0 ]; then
    record_check "error_logs" "healthy" "没有发现错误日志"
  elif [ "$error_count" -lt 5 ]; then
    record_check "error_logs" "warning" "发现少量错误日志 ($error_count 条)"
  else
    record_check "error_logs" "unhealthy" "发现大量错误日志 ($error_count 条)"
  fi
  
  # 检查最近的警告日志
  if [ "$DETAILED" = true ]; then
    local warning_count=$(docker-compose logs --tail=100 app 2>/dev/null | grep -i warning | wc -l)
    if [ "$warning_count" -gt 0 ]; then
      record_check "warning_logs" "warning" "发现警告日志 ($warning_count 条)"
    fi
  fi
}

# 函数：检查备份状态
check_backup_status() {
  if [ "$JSON_OUTPUT" = false ]; then
    echo "💾 检查备份状态..."
  fi
  
  if [ -d "/backups" ]; then
    local backup_count=$(ls -1 /backups | wc -l)
    local latest_backup=$(ls -1t /backups | head -1)
    
    if [ "$backup_count" -gt 0 ]; then
      local backup_age=$(find "/backups/$latest_backup" -mtime +1 | wc -l)
      if [ "$backup_age" -eq 0 ]; then
        record_check "backup_status" "healthy" "最新备份: $latest_backup (24小时内)"
      else
        record_check "backup_status" "warning" "最新备份: $latest_backup (超过24小时)"
      fi
    else
      record_check "backup_status" "warning" "没有找到备份文件"
    fi
  else
    record_check "backup_status" "warning" "备份目录不存在"
  fi
}

# 函数：输出JSON格式结果
output_json() {
  echo "{"
  echo "  \"timestamp\": \"$(date -Iseconds)\","
  echo "  \"overall_status\": \"${health_status[overall]}\","
  echo "  \"checks\": {"
  
  local first=true
  for component in "${!health_status[@]}"; do
    if [ "$component" != "overall" ]; then
      if [ "$first" = false ]; then
        echo ","
      fi
      echo -n "    \"$component\": \"${health_status[$component]}\""
      first=false
    fi
  done
  
  echo ""
  echo "  }"
  echo "}"
}

# 函数：输出摘要
output_summary() {
  if [ "$JSON_OUTPUT" = true ]; then
    output_json
    return
  fi
  
  echo ""
  echo "📊 健康检查摘要"
  echo "================================"
  echo "检查时间: $(date)"
  
  local healthy_count=0
  local warning_count=0
  local unhealthy_count=0
  
  for component in "${!health_status[@]}"; do
    if [ "$component" != "overall" ]; then
      case "${health_status[$component]}" in
        "healthy") healthy_count=$((healthy_count + 1)) ;;
        "warning") warning_count=$((warning_count + 1)) ;;
        "unhealthy") unhealthy_count=$((unhealthy_count + 1)) ;;
      esac
    fi
  done
  
  echo "✅ 正常: $healthy_count"
  echo "⚠️ 警告: $warning_count"
  echo "❌ 异常: $unhealthy_count"
  echo ""
  
  case "${health_status[overall]}" in
    "healthy")
      echo "🎉 系统整体状态: 健康"
      ;;
    "unhealthy")
      echo "🚨 系统整体状态: 异常，需要立即处理"
      ;;
  esac
}

# 主函数
main() {
  if [ "$JSON_OUTPUT" = false ]; then
    echo "🏥 风口云平台健康检查"
    echo "时间: $(date)"
    echo "================================"
  fi
  
  check_docker_services
  check_app_health
  check_database
  check_system_resources
  check_logs
  check_backup_status
  
  output_summary
  
  # 设置退出码
  if [ "${health_status[overall]}" = "unhealthy" ]; then
    exit 1
  fi
}

# 执行主函数
main "$@"
