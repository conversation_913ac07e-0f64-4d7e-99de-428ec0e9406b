"use client"

import { useState } from "react"
import { But<PERSON>  } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  X,
  Factory,
  User
} from "lucide-react"

interface AddFactoryDialogProps {
  isOpen: boolean
  onClose: () => void
  onSave: (factoryData: unknown) => void
}

interface FactoryFormData {
  name: string
  code: string
  address: string
  contactPerson: string
  contactPhone: string
  email: string
  // 管理员账号信息
  adminName: string
  adminUsername: string
  adminPassword: string
  adminConfirmPassword: string
  adminEmail: string
  adminPhone: string
}

export function AddFactoryDialog({ isOpen, onClose, onSave }: AddFactoryDialogProps) {
  const [formData, setFormData] = useState<FactoryFormData>({
    name: "",
    code: "",
    address: "",
    contactPerson: "",
    contactPhone: "",
    email: "",
    adminName: "",
    adminUsername: "",
    adminPassword: "",
    adminConfirmPassword: "",
    adminEmail: "",
    adminPhone: ""
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // 工厂信息验证
    if (!formData.name.trim()) newErrors.name = "工厂名称不能为空"
    if (!formData.code.trim()) newErrors.code = "工厂编码不能为空"
    if (!formData.address.trim()) newErrors.address = "工厂地址不能为空"
    if (!formData.contactPerson.trim()) newErrors.contactPerson = "联系人不能为空"
    if (!formData.contactPhone.trim()) newErrors.contactPhone = "联系电话不能为空"
    if (!formData.email.trim()) newErrors.email = "邮箱不能为空"
    
    // 电话号码验证
    const phoneRegex = /^1[3-9]\d{9}$/
    if (formData.contactPhone && !phoneRegex.test(formData.contactPhone)) {
      newErrors.contactPhone = "请输入正确的手机号码"
    }
    
    // 邮箱验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = "请输入正确的邮箱地址"
    }

    // 管理员信息验证
    if (!formData.adminName.trim()) newErrors.adminName = "管理员姓名不能为空"
    if (!formData.adminUsername.trim()) newErrors.adminUsername = "管理员用户名不能为空"
    if (!formData.adminPassword.trim()) newErrors.adminPassword = "管理员密码不能为空"
    if (formData.adminPassword.length < 6) newErrors.adminPassword = "密码至少6位"
    if (formData.adminPassword !== formData.adminConfirmPassword) {
      newErrors.adminConfirmPassword = "两次密码输入不一致"
    }
    if (!formData.adminEmail.trim()) newErrors.adminEmail = "管理员邮箱不能为空"
    if (formData.adminEmail && !emailRegex.test(formData.adminEmail)) {
      newErrors.adminEmail = "请输入正确的邮箱地址"
    }
    if (!formData.adminPhone.trim()) newErrors.adminPhone = "管理员手机号不能为空"
    if (formData.adminPhone && !phoneRegex.test(formData.adminPhone)) {
      newErrors.adminPhone = "请输入正确的手机号码"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!validateForm()) return

    setIsSubmitting(true)
    try {
      // 生成工厂ID
      const factoryId = `factory-${Date.now()}`
      
      // 构造工厂数据
      const factoryData = {
        id: factoryId,
        name: formData.name,
        code: formData.code,
        address: formData.address,
        contactPerson: formData.contactPerson,
        contactPhone: formData.contactPhone,
        email: formData.email,
        status: 'active' as const,
        createdAt: new Date(),
        updatedAt: new Date(),
        totalClients: 0,
        totalOrders: 0,
        monthlyRevenue: 0
      }

      // 构造管理员账号数据
      const adminData = {
        id: `admin-${Date.now()}`,
        factoryId: factoryId,
        username: formData.adminUsername,
        password: formData.adminPassword, // 实际应用中需要加密
        name: formData.adminName,
        email: formData.adminEmail,
        phone: formData.adminPhone,
        role: 'owner' as const,
        permissions: ['all'], // 工厂管理员拥有所有权限
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'admin' // 由总部创建
      }

      // 调用保存函数
      await onSave({ factory: factoryData, admin: adminData })
      
      // 重置表单
      setFormData({
        name: "",
        code: "",
        address: "",
        contactPerson: "",
        contactPhone: "",
        email: "",
        adminName: "",
        adminUsername: "",
        adminPassword: "",
        adminConfirmPassword: "",
        adminEmail: "",
        adminPhone: ""
      })
      setErrors({})
      onClose()
    } catch (error) {
      console.error('创建工厂失败:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">添加新工厂</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* 工厂基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Factory className="h-5 w-5" />
                <span>工厂基本信息</span>
              </CardTitle>
              <CardDescription>填写工厂的基本信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    工厂名称 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="如：华东风口制造厂"
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    工厂编码 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData.code}
                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                    placeholder="如：HD001"
                    className={errors.code ? "border-red-500" : ""}
                  />
                  {errors.code && <p className="text-red-500 text-xs mt-1">{errors.code}</p>}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  工厂地址 <span className="text-red-500">*</span>
                </label>
                <Input
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="如：上海市浦东新区张江高科技园区"
                  className={errors.address ? "border-red-500" : ""}
                />
                {errors.address && <p className="text-red-500 text-xs mt-1">{errors.address}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    联系人 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData.contactPerson}
                    onChange={(e) => setFormData(prev => ({ ...prev, contactPerson: e.target.value }))}
                    placeholder="张经理"
                    className={errors.contactPerson ? "border-red-500" : ""}
                  />
                  {errors.contactPerson && <p className="text-red-500 text-xs mt-1">{errors.contactPerson}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    联系电话 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="tel"
                    value={formData.contactPhone}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '') // 只允许数字
                      setFormData(prev => ({ ...prev, contactPhone: value }))
                    }}
                    placeholder="13800138001"
                    maxLength={11}
                    pattern="[0-9]*"
                    className={errors.contactPhone ? "border-red-500" : ""}
                  />
                  {errors.contactPhone && <p className="text-red-500 text-xs mt-1">{errors.contactPhone}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    邮箱 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                    className={errors.email ? "border-red-500" : ""}
                  />
                  {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 管理员账号信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>工厂管理员账号</span>
              </CardTitle>
              <CardDescription>为工厂创建管理员账号，该账号将拥有工厂的完整管理权限</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    管理员姓名 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData.adminName}
                    onChange={(e) => setFormData(prev => ({ ...prev, adminName: e.target.value }))}
                    placeholder="张经理"
                    className={errors.adminName ? "border-red-500" : ""}
                  />
                  {errors.adminName && <p className="text-red-500 text-xs mt-1">{errors.adminName}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    登录用户名 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData.adminUsername}
                    onChange={(e) => setFormData(prev => ({ ...prev, adminUsername: e.target.value }))}
                    placeholder="factory_admin"
                    className={errors.adminUsername ? "border-red-500" : ""}
                  />
                  {errors.adminUsername && <p className="text-red-500 text-xs mt-1">{errors.adminUsername}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    登录密码 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="password"
                    value={formData.adminPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, adminPassword: e.target.value }))}
                    placeholder="至少6位密码"
                    className={errors.adminPassword ? "border-red-500" : ""}
                  />
                  {errors.adminPassword && <p className="text-red-500 text-xs mt-1">{errors.adminPassword}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    确认密码 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="password"
                    value={formData.adminConfirmPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, adminConfirmPassword: e.target.value }))}
                    placeholder="再次输入密码"
                    className={errors.adminConfirmPassword ? "border-red-500" : ""}
                  />
                  {errors.adminConfirmPassword && <p className="text-red-500 text-xs mt-1">{errors.adminConfirmPassword}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    管理员邮箱 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="email"
                    value={formData.adminEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, adminEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                    className={errors.adminEmail ? "border-red-500" : ""}
                  />
                  {errors.adminEmail && <p className="text-red-500 text-xs mt-1">{errors.adminEmail}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    管理员手机 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="tel"
                    value={formData.adminPhone}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '') // 只允许数字
                      setFormData(prev => ({ ...prev, adminPhone: value }))
                    }}
                    placeholder="13800138001"
                    maxLength={11}
                    pattern="[0-9]*"
                    className={errors.adminPhone ? "border-red-500" : ""}
                  />
                  {errors.adminPhone && <p className="text-red-500 text-xs mt-1">{errors.adminPhone}</p>}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end space-x-3 p-6 border-t">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={isSubmitting}>
            {isSubmitting ? "创建中..." : "创建工厂"}
          </Button>
        </div>
      </div>
    </div>
  )
}
