/**
 * 🗺️ 空间文本分析器
 * 基于OCR坐标信息进行空间感知的智能识别
 */

export interface TextPosition {
  left: number
  top: number
  width: number
  height: number
}

export interface SpatialTextItem {
  id: string
  text: string
  position: TextPosition
  type: 'title' | 'floor' | 'room' | 'dimension' | 'note' | 'quantity' | 'unknown'
  confidence: number
  relatedItems: string[] // 相关文本项的ID
}

export interface SpatialLayout {
  width: number
  height: number
  items: SpatialTextItem[]
  regions: SpatialRegion[]
  relationships: SpatialRelationship[]
}

export interface SpatialRegion {
  id: string
  type: 'header' | 'content' | 'footer' | 'sidebar'
  bounds: TextPosition
  items: string[] // 包含的文本项ID
}

export interface SpatialRelationship {
  fromId: string
  toId: string
  type: 'above' | 'below' | 'left' | 'right' | 'contains' | 'adjacent'
  distance: number
  confidence: number
}

export class SpatialTextAnalyzer {
  /**
   * 分析OCR结果的空间布局
   */
  static analyzeSpatialLayout(ocrResult: any): SpatialLayout {
    console.log('🗺️ 开始空间文本分析...')
    
    const words = ocrResult.words_result || []
    if (words.length === 0) {
      return this.createEmptyLayout()
    }

    // 1. 创建空间文本项
    const items = this.createSpatialItems(words)
    console.log(`📍 创建了 ${items.length} 个空间文本项`)

    // 2. 计算图像边界
    const bounds = this.calculateImageBounds(items)
    console.log(`📐 图像边界: ${bounds.width}x${bounds.height}`)

    // 3. 识别空间区域
    const regions = this.identifyRegions(items, bounds)
    console.log(`🏗️ 识别了 ${regions.length} 个空间区域`)

    // 4. 建立空间关系
    const relationships = this.buildSpatialRelationships(items)
    console.log(`🔗 建立了 ${relationships.length} 个空间关系`)

    // 5. 基于空间位置重新分类文本
    this.reclassifyByPosition(items, relationships)

    return {
      width: bounds.width,
      height: bounds.height,
      items,
      regions,
      relationships
    }
  }

  /**
   * 创建空间文本项
   */
  private static createSpatialItems(words: any[]): SpatialTextItem[] {
    return words.map((word, index) => {
      const location = word.location || { left: 0, top: 0, width: 0, height: 0 }
      
      return {
        id: `text_${index}`,
        text: word.words.trim(),
        position: {
          left: location.left,
          top: location.top,
          width: location.width,
          height: location.height
        },
        type: this.classifyTextType(word.words),
        confidence: word.probability || 0.8,
        relatedItems: []
      }
    })
  }

  /**
   * 基于内容分类文本类型
   */
  private static classifyTextType(text: string): SpatialTextItem['type'] {
    // 楼层识别
    if (/^([一二三四五六七八九十]+|第?\d+)[楼层]$|^[FB]?\d+楼?$|^地下|^负\d+|^顶楼$/.test(text)) {
      return 'floor'
    }
    
    // 尺寸识别
    if (/\d+\s*[x×*+\-]\s*\d+/.test(text)) {
      return 'dimension'
    }
    
    // 数量识别
    if (/^\d+\s*[个只套件]$|^数量\s*[:：]\s*\d+$/.test(text)) {
      return 'quantity'
    }
    
    // 房间识别
    const roomKeywords = [
      '客厅', '餐厅', '厨房', '卫生间', '主卧', '次卧', '书房', '阳台',
      '活动室', '茶室', '棋牌室', 'KTV', '检修口', '维修口'
    ]
    if (roomKeywords.some(keyword => text.includes(keyword)) || 
        /([A-Z]\d{2,4})|(\d+号?房)|(\d+楼\d+室?)/.test(text)) {
      return 'room'
    }
    
    // 标题识别（通常在顶部）
    if (text.length > 6 && (text.includes('项目') || text.includes('工程') || text.includes('清单'))) {
      return 'title'
    }
    
    return 'note'
  }

  /**
   * 计算图像边界
   */
  private static calculateImageBounds(items: SpatialTextItem[]): { width: number, height: number } {
    if (items.length === 0) return { width: 0, height: 0 }

    const rights = items.map(item => item.position.left + item.position.width)
    const bottoms = items.map(item => item.position.top + item.position.height)

    return {
      width: Math.max(...rights),
      height: Math.max(...bottoms)
    }
  }

  /**
   * 识别空间区域
   */
  private static identifyRegions(items: SpatialTextItem[], bounds: { width: number, height: number }): SpatialRegion[] {
    const regions: SpatialRegion[] = []
    
    // 按垂直位置分组
    const verticalGroups = this.groupByVerticalPosition(items, bounds.height)
    
    verticalGroups.forEach((group, index) => {
      let regionType: SpatialRegion['type'] = 'content'
      
      // 判断区域类型
      if (index === 0) {
        regionType = 'header' // 顶部区域
      } else if (index === verticalGroups.length - 1) {
        regionType = 'footer' // 底部区域
      }
      
      // 计算区域边界
      const groupBounds = this.calculateGroupBounds(group)
      
      regions.push({
        id: `region_${index}`,
        type: regionType,
        bounds: groupBounds,
        items: group.map(item => item.id)
      })
    })

    return regions
  }

  /**
   * 按垂直位置分组
   */
  private static groupByVerticalPosition(items: SpatialTextItem[], imageHeight: number): SpatialTextItem[][] {
    // 按Y坐标排序
    const sortedItems = [...items].sort((a, b) => a.position.top - b.position.top)
    
    const groups: SpatialTextItem[][] = []
    let currentGroup: SpatialTextItem[] = []
    let lastY = -1
    
    const lineThreshold = imageHeight * 0.05 // 5%的图像高度作为行间距阈值
    
    for (const item of sortedItems) {
      if (lastY === -1 || Math.abs(item.position.top - lastY) <= lineThreshold) {
        currentGroup.push(item)
      } else {
        if (currentGroup.length > 0) {
          groups.push(currentGroup)
        }
        currentGroup = [item]
      }
      lastY = item.position.top
    }
    
    if (currentGroup.length > 0) {
      groups.push(currentGroup)
    }
    
    return groups
  }

  /**
   * 计算组边界
   */
  private static calculateGroupBounds(group: SpatialTextItem[]): TextPosition {
    if (group.length === 0) {
      return { left: 0, top: 0, width: 0, height: 0 }
    }

    const lefts = group.map(item => item.position.left)
    const tops = group.map(item => item.position.top)
    const rights = group.map(item => item.position.left + item.position.width)
    const bottoms = group.map(item => item.position.top + item.position.height)

    const left = Math.min(...lefts)
    const top = Math.min(...tops)
    const right = Math.max(...rights)
    const bottom = Math.max(...bottoms)

    return {
      left,
      top,
      width: right - left,
      height: bottom - top
    }
  }

  /**
   * 建立空间关系
   */
  private static buildSpatialRelationships(items: SpatialTextItem[]): SpatialRelationship[] {
    const relationships: SpatialRelationship[] = []

    for (let i = 0; i < items.length; i++) {
      for (let j = i + 1; j < items.length; j++) {
        const item1 = items[i]
        const item2 = items[j]
        
        const relationship = this.analyzeRelationship(item1, item2)
        if (relationship) {
          relationships.push(relationship)
          
          // 建立双向关联
          item1.relatedItems.push(item2.id)
          item2.relatedItems.push(item1.id)
        }
      }
    }

    return relationships
  }

  /**
   * 分析两个文本项的空间关系
   */
  private static analyzeRelationship(item1: SpatialTextItem, item2: SpatialTextItem): SpatialRelationship | null {
    const pos1 = item1.position
    const pos2 = item2.position
    
    // 计算中心点
    const center1 = {
      x: pos1.left + pos1.width / 2,
      y: pos1.top + pos1.height / 2
    }
    const center2 = {
      x: pos2.left + pos2.width / 2,
      y: pos2.top + pos2.height / 2
    }
    
    // 计算距离
    const distance = Math.sqrt(
      Math.pow(center2.x - center1.x, 2) + Math.pow(center2.y - center1.y, 2)
    )
    
    // 距离太远则不建立关系
    if (distance > 300) return null
    
    // 判断关系类型
    let relationType: SpatialRelationship['type']
    let confidence = 0.5
    
    const horizontalDistance = Math.abs(center2.x - center1.x)
    const verticalDistance = Math.abs(center2.y - center1.y)
    
    if (verticalDistance > horizontalDistance) {
      // 垂直关系
      if (center2.y > center1.y) {
        relationType = 'below'
      } else {
        relationType = 'above'
      }
      confidence = 0.8
    } else {
      // 水平关系
      if (center2.x > center1.x) {
        relationType = 'right'
      } else {
        relationType = 'left'
      }
      confidence = 0.7
    }
    
    // 相邻关系（距离很近）
    if (distance < 100) {
      relationType = 'adjacent'
      confidence = 0.9
    }

    return {
      fromId: item1.id,
      toId: item2.id,
      type: relationType,
      distance,
      confidence
    }
  }

  /**
   * 基于空间位置重新分类文本
   */
  private static reclassifyByPosition(items: SpatialTextItem[], relationships: SpatialRelationship[]): void {
    console.log('🔄 基于空间位置重新分类文本...')
    
    // 找到楼层信息
    const floorItems = items.filter(item => item.type === 'floor')
    
    // 找到尺寸信息
    const dimensionItems = items.filter(item => item.type === 'dimension')
    
    // 为每个尺寸项查找相关的备注信息
    dimensionItems.forEach(dimItem => {
      const relatedNotes = this.findRelatedNotes(dimItem, items, relationships)
      
      if (relatedNotes.length > 0) {
        console.log(`📐 尺寸 "${dimItem.text}" 找到相关备注: ${relatedNotes.map(n => n.text).join(', ')}`)
        
        // 将相关备注标记为note类型
        relatedNotes.forEach(note => {
          if (note.type === 'unknown') {
            note.type = 'note'
            note.confidence = Math.min(note.confidence + 0.2, 1.0)
          }
        })
      }
    })
    
    // 为楼层信息查找相关的房间信息
    floorItems.forEach(floorItem => {
      const relatedRooms = this.findRelatedRooms(floorItem, items, relationships)
      
      if (relatedRooms.length > 0) {
        console.log(`🏢 楼层 "${floorItem.text}" 找到相关房间: ${relatedRooms.map(r => r.text).join(', ')}`)
      }
    })
  }

  /**
   * 查找与尺寸相关的备注
   */
  private static findRelatedNotes(dimItem: SpatialTextItem, allItems: SpatialTextItem[], relationships: SpatialRelationship[]): SpatialTextItem[] {
    const relatedNotes: SpatialTextItem[] = []
    
    // 查找在同一行或相邻行的文本
    const sameLineItems = allItems.filter(item => {
      if (item.id === dimItem.id) return false
      
      const verticalDistance = Math.abs(item.position.top - dimItem.position.top)
      const horizontalDistance = Math.abs(item.position.left - dimItem.position.left)
      
      // 同一行：垂直距离小于30像素
      const sameLine = verticalDistance < 30
      
      // 水平相邻：在尺寸右侧200像素内
      const horizontallyAdjacent = item.position.left > dimItem.position.left && 
                                   horizontalDistance < 200
      
      return sameLine && horizontallyAdjacent
    })
    
    // 过滤出可能的备注文本
    sameLineItems.forEach(item => {
      // 排除明显的数量信息
      if (item.type === 'quantity') return
      
      // 排除纯数字
      if (/^\d+$/.test(item.text)) return
      
      // 包含有意义的文字
      if (item.text.length >= 2) {
        relatedNotes.push(item)
      }
    })
    
    return relatedNotes
  }

  /**
   * 查找与楼层相关的房间
   */
  private static findRelatedRooms(floorItem: SpatialTextItem, allItems: SpatialTextItem[], relationships: SpatialRelationship[]): SpatialTextItem[] {
    const relatedRooms: SpatialTextItem[] = []
    
    // 查找在楼层下方的房间信息
    const belowItems = allItems.filter(item => {
      if (item.id === floorItem.id) return false
      
      const verticalDistance = item.position.top - floorItem.position.top
      const horizontalOverlap = this.calculateHorizontalOverlap(floorItem.position, item.position)
      
      // 在楼层下方且有水平重叠
      return verticalDistance > 0 && verticalDistance < 200 && horizontalOverlap > 0.3
    })
    
    // 筛选房间类型的文本
    belowItems.forEach(item => {
      if (item.type === 'room' || this.isLikelyRoom(item.text)) {
        relatedRooms.push(item)
      }
    })
    
    return relatedRooms
  }

  /**
   * 判断文本是否像房间名称
   */
  private static isLikelyRoom(text: string): boolean {
    const roomIndicators = ['室', '厅', '房', '间', 'KTV', '口']
    return roomIndicators.some(indicator => text.includes(indicator))
  }

  /**
   * 计算水平重叠度
   */
  private static calculateHorizontalOverlap(pos1: TextPosition, pos2: TextPosition): number {
    const left1 = pos1.left
    const right1 = pos1.left + pos1.width
    const left2 = pos2.left
    const right2 = pos2.left + pos2.width
    
    const overlapStart = Math.max(left1, left2)
    const overlapEnd = Math.min(right1, right2)
    const overlapWidth = Math.max(0, overlapEnd - overlapStart)
    
    const totalWidth = Math.max(right1, right2) - Math.min(left1, left2)
    
    return overlapWidth / totalWidth
  }

  /**
   * 创建空布局
   */
  private static createEmptyLayout(): SpatialLayout {
    return {
      width: 0,
      height: 0,
      items: [],
      regions: [],
      relationships: []
    }
  }

  /**
   * 获取空间感知的识别结果
   */
  static getSpatialRecognitionResult(layout: SpatialLayout): any {
    console.log('🎯 生成空间感知识别结果...')
    
    const result = {
      layout: {
        width: layout.width,
        height: layout.height,
        totalItems: layout.items.length,
        regions: layout.regions.length,
        relationships: layout.relationships.length
      },
      items: layout.items.map(item => ({
        id: item.id,
        text: item.text,
        type: item.type,
        position: item.position,
        confidence: item.confidence,
        relatedCount: item.relatedItems.length
      })),
      spatialGroups: this.generateSpatialGroups(layout),
      suggestions: this.generateSpatialSuggestions(layout)
    }
    
    console.log(`✅ 生成了 ${result.spatialGroups.length} 个空间分组`)
    return result
  }

  /**
   * 生成空间分组
   */
  private static generateSpatialGroups(layout: SpatialLayout): any[] {
    const groups: any[] = []
    
    // 按楼层分组
    const floorItems = layout.items.filter(item => item.type === 'floor')
    
    floorItems.forEach(floorItem => {
      const group = {
        type: 'floor_group',
        floor: floorItem.text,
        items: [floorItem],
        bounds: { ...floorItem.position }
      }
      
      // 查找该楼层下的所有相关项目
      const relatedItems = layout.items.filter(item => {
        if (item.id === floorItem.id) return false
        
        const verticalDistance = item.position.top - floorItem.position.top
        return verticalDistance > 0 && verticalDistance < 300
      })
      
      group.items.push(...relatedItems)
      
      // 更新边界
      if (relatedItems.length > 0) {
        const allItems = group.items
        const lefts = allItems.map(item => item.position.left)
        const tops = allItems.map(item => item.position.top)
        const rights = allItems.map(item => item.position.left + item.position.width)
        const bottoms = allItems.map(item => item.position.top + item.position.height)
        
        group.bounds = {
          left: Math.min(...lefts),
          top: Math.min(...tops),
          width: Math.max(...rights) - Math.min(...lefts),
          height: Math.max(...bottoms) - Math.min(...tops)
        }
      }
      
      groups.push(group)
    })
    
    return groups
  }

  /**
   * 生成空间建议
   */
  private static generateSpatialSuggestions(layout: SpatialLayout): string[] {
    const suggestions: string[] = []
    
    const floorCount = layout.items.filter(item => item.type === 'floor').length
    const roomCount = layout.items.filter(item => item.type === 'room').length
    const dimensionCount = layout.items.filter(item => item.type === 'dimension').length
    const noteCount = layout.items.filter(item => item.type === 'note').length
    
    if (floorCount === 0 && roomCount > 0) {
      suggestions.push('建议添加楼层信息以提高空间定位准确性')
    }
    
    if (noteCount < dimensionCount * 0.5) {
      suggestions.push('检测到较少备注信息，可能需要调整图像质量或识别参数')
    }
    
    if (layout.relationships.length < layout.items.length * 0.3) {
      suggestions.push('文本项之间的空间关系较少，建议检查图像布局')
    }
    
    return suggestions
  }
}
