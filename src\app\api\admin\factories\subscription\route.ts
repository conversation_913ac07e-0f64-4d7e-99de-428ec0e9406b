/**
 * 🇨🇳 风口云平台 - 工厂订阅管理 API
 * 处理工厂订阅配置的更新
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware/auth'

export const PUT = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { 
      factoryId, 
      subscriptionType, 
      subscriptionStart, 
      subscriptionEnd, 
      isPermanent 
    } = body

    console.log('🔄 更新工厂订阅配置:', {
      factoryId,
      subscriptionType,
      subscriptionStart,
      subscriptionEnd,
      isPermanent
    })

    // 验证必需字段
    if (!factoryId || !subscriptionType) {
      return NextResponse.json({
        success: false,
        error: '缺少必需的字段'
      }, { status: 400 })
    }

    // 更新工厂订阅信息
    const updatedFactory = await db.updateFactory(factoryId, {
      subscriptionType,
      subscriptionStart: subscriptionStart ? new Date(subscriptionStart) : null,
      subscriptionEnd: subscriptionEnd ? new Date(subscriptionEnd) : null,
      isPermanent: isPermanent || false,
      updatedAt: new Date()
    })

    if (!updatedFactory) {
      return NextResponse.json({
        success: false,
        error: '工厂不存在或更新失败'
      }, { status: 404 })
    }

    console.log('✅ 工厂订阅配置更新成功:', updatedFactory.id)

    return NextResponse.json({
      success: true,
      factory: updatedFactory
    })

  } catch (error) {
    console.error('❌ 更新工厂订阅配置失败:', error)
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
})

// 获取工厂订阅状态
export const GET = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')

    if (!factoryId) {
      return NextResponse.json(
        { error: '缺少工厂ID参数' },
        { status: 400 }
      )
    }

    // 获取工厂信息
    const factory = await db.getFactoryById(factoryId)
    if (!factory) {
      return NextResponse.json(
        { error: '工厂不存在' },
        { status: 404 }
      )
    }

    // 检查工厂状态
    const dbService = db as any
    const statusCheck = await dbService.checkFactoryStatus(factory)

    // 🔧 计算剩余天数（考虑暂停时间）
    let remainingDays = null
    if (factory.subscriptionEnd && !factory.isPermanent && factory.subscriptionType !== 'permanent') {
      const now = new Date()
      const endDate = new Date(factory.subscriptionEnd)

      // 计算暂停时间累计，调整实际结束时间
      let totalSuspendedMs = Number(factory.totalSuspendedMs || 0)

      // 如果当前正在暂停中，加上当前暂停的时间
      if (factory.status === 'suspended' && factory.suspendedAt) {
        const suspendedAt = new Date(factory.suspendedAt)
        const currentSuspendedMs = now.getTime() - suspendedAt.getTime()
        if (currentSuspendedMs > 0) {
          totalSuspendedMs += currentSuspendedMs
        }
      }

      // 实际结束时间 = 原结束时间 + 暂停时间累计
      const adjustedEndDate = new Date(endDate.getTime() + totalSuspendedMs)
      const remainingMs = adjustedEndDate.getTime() - now.getTime()
      remainingDays = Math.ceil(remainingMs / (1000 * 60 * 60 * 24))
    }

    return NextResponse.json({
      success: true,
      factory: {
        id: factory.id,
        name: factory.name,
        code: factory.code,
        status: factory.status,
        subscriptionType: factory.subscriptionType,
        subscriptionStart: factory.subscriptionStart,
        subscriptionEnd: factory.subscriptionEnd,
        firstLoginAt: factory.firstLoginAt,
        isPermanent: factory.isPermanent,
        suspendedAt: factory.suspendedAt,
        suspendedReason: factory.suspendedReason
      },
      statusCheck,
      remainingDays
    })

  } catch (error) {
    console.error('❌ 获取工厂订阅状态失败:', error)
    return NextResponse.json(
      { error: '获取工厂订阅状态失败' },
      { status: 500 }
    )
  }
})
