/**
 * 🇨🇳 风口云平台 - 轮播图片统计API
 * 
 * 功能说明：
 * - 获取轮播图片的存储统计信息
 * - 包括总数量、总大小、分类统计等
 */

import { NextRequest, NextResponse } from 'next/server'
import { imageDatabase } from '@/lib/image-database'

// 获取存储统计信息
export async function GET(request: NextRequest) {
  try {
    console.log('📊 获取存储统计信息')

    const rawStats = await imageDatabase.getStorageStats()

    // 转换为页面期望的格式
    const stats = {
      totalImages: rawStats.totalImages,
      totalSize: rawStats.totalSize,
      categories: {
        product: rawStats.byCategory.product?.count || 0,
        process: rawStats.byCategory.process?.count || 0,
        other: rawStats.byCategory.other?.count || 0
      }
    }

    console.log('✅ 统计信息获取成功:', stats)

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error('❌ 获取统计信息失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: `获取统计失败: ${(error as Error).message || '未知错误'}`
      },
      { status: 500 }
    )
  }
}
