# 🇨🇳 风口云平台 - 环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# 数据库配置
DATABASE_URL="mysql://username:password@localhost:3306/factorysystem"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-change-in-production-min-32-chars"
JWT_EXPIRES_IN="7d"
REFRESH_TOKEN_EXPIRES_IN="30d"

# API配置
NEXT_PUBLIC_API_BASE_URL="http://localhost:3000"
INTERNAL_API_KEY="your-internal-api-key-for-service-communication"

# 应用配置
NEXT_PUBLIC_APP_NAME="风口云平台"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# 安全配置
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-key"

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,application/pdf"

# 邮件配置（可选）
SMTP_HOST=""
SMTP_PORT=""
SMTP_USER=""
SMTP_PASS=""
FROM_EMAIL=""

# 短信配置（可选）
SMS_API_KEY=""
SMS_SECRET=""

# 日志配置
LOG_LEVEL="info"
LOG_FILE_PATH="./logs"

# 缓存配置（可选）
REDIS_URL=""

# 监控配置（可选）
SENTRY_DSN=""
