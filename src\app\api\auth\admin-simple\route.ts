/**
 * 🇨🇳 风口云平台 - 简化管理员认证API
 */

import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { generateTokenPair } from '@/lib/auth/jwt'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json()

    if (!username || !password) {
      return NextResponse.json(
        { error: '用户名和密码不能为空' },
        { status: 400 }
      )
    }

    console.log('🔐 简化管理员认证:', username)

    // 查找管理员
    const admin = await prisma.admin.findFirst({
      where: { 
        username,
        isActive: true
      }
    })

    if (!admin) {
      console.log('❌ 管理员不存在或已禁用')
      return NextResponse.json(
        { error: '用户名或密码错误' },
        { status: 401 }
      )
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, admin.passwordHash)
    if (!isValidPassword) {
      console.log('❌ 密码错误')
      return NextResponse.json(
        { error: '用户名或密码错误' },
        { status: 401 }
      )
    }

    console.log('✅ 管理员认证成功:', admin.name)

    // 生成JWT令牌
    const tokens = generateTokenPair({
      userId: admin.id,
      username: admin.username,
      name: admin.name,
      userType: 'admin',
      role: admin.role
    })

    // 更新最后登录时间
    await prisma.admin.update({
      where: { id: admin.id },
      data: { lastLoginAt: new Date() }
    })

    // 记录简单的登录记录
    try {
      await prisma.loginRecord.create({
        data: {
          userId: admin.id,
          userType: 'admin',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown',
          success: true
        }
      })
    } catch (error) {
      console.warn('⚠️ 登录记录创建失败:', error)
      // 不影响登录流程
    }

    // 返回管理员信息和令牌（不包含密码）
    const { passwordHash, ...adminInfo } = admin

    return NextResponse.json({
      success: true,
      user: adminInfo,
      ...tokens
    })

  } catch (error) {
    console.error('❌ 管理员认证失败:', error)
    return NextResponse.json(
      { error: '认证服务异常' },
      { status: 500 }
    )
  }
}

// 获取客户端IP地址
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (realIP) return realIP.trim()
  if (forwarded) {
    const ips = forwarded.split(',').map(ip => ip.trim())
    return ips[0]
  }
  
  return '127.0.0.1'
}
