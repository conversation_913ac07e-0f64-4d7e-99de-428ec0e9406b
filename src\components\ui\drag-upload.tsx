'use client'

import React, { useRef, useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Upload, File, X, CheckCircle, AlertCircle, Loader2, Cloud, Image } from 'lucide-react'
import { cn } from '@/lib/utils'

interface DragUploadProps {
  onFileSelect?: (files: File[]) => void
  onUploadComplete?: (results: any[]) => void
  onUploadError?: (error: string) => void
  accept?: string
  multiple?: boolean
  maxSize?: number // MB
  maxFiles?: number
  disabled?: boolean
  className?: string
  uploadUrl?: string
  title?: string
  description?: string
  showFileList?: boolean
}

interface UploadedFile {
  file: File
  id: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
  result?: any
  preview?: string
}

export function DragUpload({
  onFileSelect,
  onUploadComplete,
  onUploadError,
  accept = '*/*',
  multiple = false,
  maxSize = 10,
  maxFiles = 5,
  disabled = false,
  className,
  uploadUrl,
  title = '拖拽文件到此处或点击上传',
  description = '支持多种文件格式',
  showFileList = true
}: DragUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)

  // 生成唯一ID
  const generateId = () => Math.random().toString(36).substr(2, 9)

  // 创建文件预览
  const createPreview = (file: File): Promise<string | null> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => resolve(e.target?.result as string)
        reader.onerror = () => resolve(null)
        reader.readAsDataURL(file)
      } else {
        resolve(null)
      }
    })
  }

  // 验证文件
  const validateFile = (file: File): string | null => {
    if (file.size > maxSize * 1024 * 1024) {
      return `文件大小不能超过 ${maxSize}MB`
    }

    if (accept !== '*/*') {
      const acceptedTypes = accept.split(',').map(type => type.trim())
      const fileType = file.type
      const fileName = file.name.toLowerCase()
      
      const isAccepted = acceptedTypes.some(acceptType => {
        if (acceptType.startsWith('.')) {
          return fileName.endsWith(acceptType.toLowerCase())
        }
        if (acceptType.includes('*')) {
          const baseType = acceptType.split('/')[0]
          return fileType.startsWith(baseType)
        }
        return fileType === acceptType
      })

      if (!isAccepted) {
        return `不支持的文件类型，仅支持: ${accept}`
      }
    }

    return null
  }

  // 处理文件
  const handleFiles = useCallback(async (files: FileList | File[]) => {
    const fileArray = Array.from(files)
    const newFiles: UploadedFile[] = []

    if (uploadedFiles.length + fileArray.length > maxFiles) {
      onUploadError?.(`最多只能上传 ${maxFiles} 个文件`)
      return
    }

    for (const file of fileArray) {
      const error = validateFile(file)
      if (error) {
        onUploadError?.(error)
        continue
      }

      const isDuplicate = uploadedFiles.some(uf => 
        uf.file.name === file.name && uf.file.size === file.size
      )
      if (isDuplicate) {
        onUploadError?.(`文件 ${file.name} 已存在`)
        continue
      }

      const preview = await createPreview(file)
      
      newFiles.push({
        file,
        id: generateId(),
        status: 'pending',
        progress: 0,
        preview
      })
    }

    if (newFiles.length > 0) {
      setUploadedFiles(prev => [...prev, ...newFiles])
      onFileSelect?.(newFiles.map(f => f.file))

      if (uploadUrl) {
        uploadFiles(newFiles)
      }
    }
  }, [uploadedFiles, maxFiles, maxSize, accept, onFileSelect, onUploadError, uploadUrl])

  // 拖拽处理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (disabled) return
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files)
    }
  }, [disabled, handleFiles])

  // 上传文件
  const uploadFiles = async (filesToUpload: UploadedFile[]) => {
    if (!uploadUrl) return

    setIsUploading(true)
    const results: any[] = []

    try {
      for (const fileItem of filesToUpload) {
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { ...f, status: 'uploading', progress: 0 } : f
        ))

        try {
          const formData = new FormData()
          formData.append('file', fileItem.file)

          // 模拟进度更新
          const progressInterval = setInterval(() => {
            setUploadedFiles(prev => prev.map(f => 
              f.id === fileItem.id && f.progress < 90 ? 
                { ...f, progress: f.progress + 10 } : f
            ))
          }, 200)

          const response = await fetch(uploadUrl, {
            method: 'POST',
            body: formData,
          })

          clearInterval(progressInterval)

          if (!response.ok) {
            throw new Error(`上传失败: ${response.status}`)
          }

          const result = await response.json()
          results.push(result)

          setUploadedFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { 
              ...f, 
              status: 'success', 
              progress: 100,
              result 
            } : f
          ))

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '上传失败'
          
          setUploadedFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { 
              ...f, 
              status: 'error', 
              error: errorMessage 
            } : f
          ))
        }
      }

      onUploadComplete?.(results)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败'
      onUploadError?.(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }

  // 文件输入处理
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files)
    }
  }

  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click()
    }
  }

  // 移除文件
  const removeFile = (id: string) => {
    setUploadedFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id)
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  // 清空文件
  const clearFiles = () => {
    uploadedFiles.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview)
      }
    })
    setUploadedFiles([])
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <File className="h-4 w-4 text-gray-400" />
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* 拖拽上传区域 */}
      <div
        className={cn(
          'border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer',
          dragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <div className="space-y-4">
          {/* 图标 */}
          <div className="flex justify-center">
            {isUploading ? (
              <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />
            ) : dragActive ? (
              <Cloud className="h-12 w-12 text-blue-500" />
            ) : (
              <Upload className="h-12 w-12 text-gray-400" />
            )}
          </div>

          {/* 标题和描述 */}
          <div>
            <p className="text-lg font-medium text-gray-700 mb-2">
              {isUploading ? '上传中...' : title}
            </p>
            <p className="text-sm text-gray-500">
              {description}
              {maxSize && ` • 最大 ${maxSize}MB`}
              {maxFiles > 1 && ` • 最多 ${maxFiles} 个文件`}
            </p>
          </div>

          {/* 上传按钮 */}
          <Button 
            variant="outline" 
            disabled={disabled || isUploading}
            onClick={(e) => {
              e.stopPropagation()
              handleClick()
            }}
          >
            <Upload className="h-4 w-4 mr-2" />
            选择文件
          </Button>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* 文件列表 */}
      {showFileList && uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">
              已选择文件 ({uploadedFiles.length}/{maxFiles})
            </h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFiles}
              className="h-6 text-xs"
            >
              清空全部
            </Button>
          </div>
          
          <div className="grid grid-cols-1 gap-3 max-h-60 overflow-y-auto">
            {uploadedFiles.map((fileItem) => (
              <div
                key={fileItem.id}
                className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
              >
                {/* 文件预览或图标 */}
                <div className="flex-shrink-0">
                  {fileItem.preview ? (
                    <img
                      src={fileItem.preview}
                      alt={fileItem.file.name}
                      className="w-10 h-10 object-cover rounded"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                      <File className="h-5 w-5 text-gray-400" />
                    </div>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(fileItem.status)}
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {fileItem.file.name}
                    </p>
                  </div>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(fileItem.file.size)}
                  </p>
                  
                  {/* 进度条 */}
                  {fileItem.status === 'uploading' && (
                    <div className="mt-2">
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${fileItem.progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                  
                  {fileItem.error && (
                    <p className="text-xs text-red-500 mt-1">{fileItem.error}</p>
                  )}
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(fileItem.id)}
                  className="h-6 w-6 p-0 text-gray-400 hover:text-red-500"
                  disabled={fileItem.status === 'uploading'}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
