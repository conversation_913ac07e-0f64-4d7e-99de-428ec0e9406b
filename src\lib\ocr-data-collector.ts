/**
 * OCR数据收集工具
 * 用于收集和分析OCR识别的真实数据，为优化提供基础
 */

"use client"

// 数据收集接口定义
interface OCRAnalysisData {
  timestamp: string
  sessionId: string
  
  // OCR原始数据分析
  ocrAnalysis: {
    textLength: number
    wordCount: number
    coordinateQuality: 'excellent' | 'good' | 'fair' | 'poor'
    layoutType: 'standard_table' | 'left_right_pairs' | 'single_column' | 'mixed_format' | 'unknown'
    recognitionConfidence: number
    imageQuality: 'high' | 'medium' | 'low'
    commonIssues: string[]
  }
  
  // 解析结果分析
  parseAnalysis: {
    projectCount: number
    floorRecognitionRate: number
    ventTypeAccuracy: number
    dimensionParseRate: number
    notesQuality: number
    overallSuccess: boolean
  }
  
  // 原始数据（用于后续分析）
  rawData: {
    recognizedText: string
    ocrResult: any
    parseResult: any[]
  }
}

// 用户反馈数据
interface UserFeedback {
  sessionId: string
  timestamp: string
  accuracy: number // 1-5分
  issues: string[]
  suggestions: string
  wouldRecommend: boolean
}

export class OCRDataCollector {
  private static instance: OCRDataCollector
  private collectedData: OCRAnalysisData[] = []
  private feedbackData: UserFeedback[] = []
  
  static getInstance(): OCRDataCollector {
    if (!OCRDataCollector.instance) {
      OCRDataCollector.instance = new OCRDataCollector()
      // 初始化时加载本地数据
      OCRDataCollector.instance.loadFromLocalStorage()
    }
    return OCRDataCollector.instance
  }
  
  // 生成会话ID
  private generateSessionId(): string {
    return `ocr_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
  }
  
  // 分析OCR识别质量
  private analyzeOCRQuality(ocrResult: any): OCRAnalysisData['ocrAnalysis'] {
    const words = ocrResult?.words_result || []
    
    // 计算坐标质量
    const validCoords = words.filter((w: any) => 
      w.location && 
      w.location.left >= 0 && 
      w.location.top >= 0 &&
      w.location.width > 0 && 
      w.location.height > 0
    )
    const coordinateCompleteness = words.length > 0 ? validCoords.length / words.length : 0
    
    let coordinateQuality: 'excellent' | 'good' | 'fair' | 'poor'
    if (coordinateCompleteness > 0.95) coordinateQuality = 'excellent'
    else if (coordinateCompleteness > 0.8) coordinateQuality = 'good'
    else if (coordinateCompleteness > 0.6) coordinateQuality = 'fair'
    else coordinateQuality = 'poor'
    
    // 计算识别置信度
    const confidences = words
      .filter((w: any) => w.probability)
      .map((w: any) => w.probability)
    const avgConfidence = confidences.length > 0 
      ? confidences.reduce((sum: number, conf: number) => sum + conf, 0) / confidences.length 
      : 0
    
    // 检测布局类型
    const layoutType = this.detectLayoutType(words)
    
    // 检测图片质量问题
    const imageQuality = this.assessImageQuality(words, avgConfidence)
    
    // 识别常见问题
    const commonIssues = this.identifyCommonIssues(words)
    
    return {
      textLength: words.reduce((sum: number, w: any) => sum + w.words.length, 0),
      wordCount: words.length,
      coordinateQuality,
      layoutType,
      recognitionConfidence: avgConfidence,
      imageQuality,
      commonIssues
    }
  }
  
  // 检测布局类型
  private detectLayoutType(words: any[]): OCRAnalysisData['ocrAnalysis']['layoutType'] {
    if (words.length === 0) return 'unknown'
    
    // 检测表格表头关键词
    const headerKeywords = ['楼层', '类型', '尺寸', '数量', '备注', '风口']
    const hasHeaders = words.some(w => 
      headerKeywords.some(keyword => w.words.includes(keyword))
    )
    
    // 按Y坐标分组检测行
    const rows = this.groupByRows(words)
    
    // 检测列对齐
    const hasAlignedColumns = this.checkColumnAlignment(rows)
    
    if (hasHeaders && hasAlignedColumns && rows.length > 2) {
      return 'standard_table'
    }
    
    // 检测左右对应格式
    if (this.detectLeftRightPairs(words)) {
      return 'left_right_pairs'
    }
    
    // 检测单列格式
    if (this.detectSingleColumn(words)) {
      return 'single_column'
    }
    
    return 'mixed_format'
  }
  
  // 按行分组
  private groupByRows(words: any[], threshold = 15): any[][] {
    const sortedWords = [...words].sort((a, b) => a.location.top - b.location.top)
    const rows: any[][] = []
    let currentRow: any[] = []
    let lastY = -1
    
    for (const word of sortedWords) {
      if (lastY === -1 || Math.abs(word.location.top - lastY) > threshold) {
        if (currentRow.length > 0) {
          rows.push([...currentRow])
        }
        currentRow = [word]
        lastY = word.location.top
      } else {
        currentRow.push(word)
      }
    }
    
    if (currentRow.length > 0) {
      rows.push(currentRow)
    }
    
    return rows
  }
  
  // 检测列对齐
  private checkColumnAlignment(rows: any[][]): boolean {
    if (rows.length < 2) return false
    
    // 简化的列对齐检测
    const firstRowXPositions = rows[0].map(w => w.location.left).sort((a, b) => a - b)
    
    for (let i = 1; i < rows.length; i++) {
      const currentRowXPositions = rows[i].map(w => w.location.left).sort((a, b) => a - b)
      
      // 检查X位置是否大致对齐（允许30像素误差）
      if (Math.abs(firstRowXPositions.length - currentRowXPositions.length) > 1) {
        return false
      }
    }
    
    return true
  }
  
  // 检测左右对应格式
  private detectLeftRightPairs(words: any[]): boolean {
    // 简化检测：看是否有明显的左右两列分布
    const xPositions = words.map(w => w.location.left)
    const minX = Math.min(...xPositions)
    const maxX = Math.max(...xPositions)
    const midX = (minX + maxX) / 2
    
    const leftWords = words.filter(w => w.location.left < midX)
    const rightWords = words.filter(w => w.location.left >= midX)
    
    // 如果左右两侧都有相当数量的文字，可能是左右对应格式
    return leftWords.length > 0 && rightWords.length > 0 && 
           Math.abs(leftWords.length - rightWords.length) <= 2
  }
  
  // 检测单列格式
  private detectSingleColumn(words: any[]): boolean {
    // 检测是否大部分文字都在相似的X位置（单列）
    const xPositions = words.map(w => w.location.left)
    const avgX = xPositions.reduce((sum, x) => sum + x, 0) / xPositions.length
    
    const alignedWords = words.filter(w => Math.abs(w.location.left - avgX) < 50)
    
    return alignedWords.length / words.length > 0.8
  }
  
  // 评估图片质量
  private assessImageQuality(words: any[], avgConfidence: number): 'high' | 'medium' | 'low' {
    // 基于识别置信度和文字密度评估
    if (avgConfidence > 0.9 && words.length > 10) return 'high'
    if (avgConfidence > 0.7 && words.length > 5) return 'medium'
    return 'low'
  }
  
  // 识别常见问题
  private identifyCommonIssues(words: any[]): string[] {
    const issues: string[] = []
    
    // 检测单字符过多（可能是分割问题）
    const singleChars = words.filter(w => w.words.length === 1)
    if (singleChars.length > words.length * 0.3) {
      issues.push('文字分割过度')
    }
    
    // 检测长文本（可能是合并问题）
    const longTexts = words.filter(w => w.words.length > 20)
    if (longTexts.length > 0) {
      issues.push('文字合并过度')
    }
    
    // 检测特殊字符过多
    const specialChars = words.filter(w => /[^\u4e00-\u9fa5\w\s\d\-×x*+]/.test(w.words))
    if (specialChars.length > words.length * 0.2) {
      issues.push('特殊字符识别错误')
    }
    
    // 检测数字识别问题
    const numbers = words.filter(w => /\d/.test(w.words))
    const suspiciousNumbers = numbers.filter(w => /[oO0]/.test(w.words))
    if (suspiciousNumbers.length > numbers.length * 0.3) {
      issues.push('数字识别混淆')
    }
    
    return issues
  }
  
  // 分析解析结果
  private analyzeParseResult(parseResult: any[]): OCRAnalysisData['parseAnalysis'] {
    if (parseResult.length === 0) {
      return {
        projectCount: 0,
        floorRecognitionRate: 0,
        ventTypeAccuracy: 0,
        dimensionParseRate: 0,
        notesQuality: 0,
        overallSuccess: false
      }
    }
    
    let totalFloors = 0
    let recognizedFloors = 0
    let totalVents = 0
    let correctVentTypes = 0
    let totalDimensions = 0
    let parsedDimensions = 0
    let totalNotes = 0
    let cleanNotes = 0
    
    parseResult.forEach(project => {
      project.floors?.forEach((floor: any) => {
        totalFloors++
        if (floor.floorName && floor.floorName !== '未指定楼层') {
          recognizedFloors++
        }
        
        floor.vents?.forEach((vent: any) => {
          totalVents++
          if (vent.type && vent.type !== 'unknown') {
            correctVentTypes++
          }
          
          totalDimensions++
          if (vent.length > 0 && vent.width > 0) {
            parsedDimensions++
          }
          
          if (vent.notes) {
            totalNotes++
            // 简单的备注质量检测
            if (vent.notes.length > 2 && !vent.notes.includes('￥') && !vent.notes.includes('mm')) {
              cleanNotes++
            }
          }
        })
      })
    })
    
    return {
      projectCount: parseResult.length,
      floorRecognitionRate: totalFloors > 0 ? recognizedFloors / totalFloors : 0,
      ventTypeAccuracy: totalVents > 0 ? correctVentTypes / totalVents : 0,
      dimensionParseRate: totalDimensions > 0 ? parsedDimensions / totalDimensions : 0,
      notesQuality: totalNotes > 0 ? cleanNotes / totalNotes : 0,
      overallSuccess: parseResult.length > 0 && recognizedFloors > 0 && correctVentTypes > 0
    }
  }
  
  // 主要的数据收集方法
  public collectOCRData(recognizedText: string, ocrResult: any, parseResult: any[]): string {
    const sessionId = this.generateSessionId()
    
    const analysisData: OCRAnalysisData = {
      timestamp: new Date().toISOString(),
      sessionId,
      ocrAnalysis: this.analyzeOCRQuality(ocrResult),
      parseAnalysis: this.analyzeParseResult(parseResult),
      rawData: {
        recognizedText,
        ocrResult,
        parseResult
      }
    }
    
    this.collectedData.push(analysisData)
    
    // 保存到本地存储
    this.saveToLocalStorage()
    
    // 输出分析结果到控制台
    console.log('📊 OCR数据收集完成:', {
      sessionId,
      layoutType: analysisData.ocrAnalysis.layoutType,
      coordinateQuality: analysisData.ocrAnalysis.coordinateQuality,
      overallSuccess: analysisData.parseAnalysis.overallSuccess,
      floorRecognitionRate: (analysisData.parseAnalysis.floorRecognitionRate * 100).toFixed(1) + '%',
      ventTypeAccuracy: (analysisData.parseAnalysis.ventTypeAccuracy * 100).toFixed(1) + '%'
    })
    
    return sessionId
  }

  // 收集用户反馈
  public collectUserFeedback(sessionId: string, feedback: Omit<UserFeedback, 'sessionId' | 'timestamp'>): void {
    const feedbackData: UserFeedback = {
      sessionId,
      timestamp: new Date().toISOString(),
      ...feedback
    }

    this.feedbackData.push(feedbackData)
    this.saveToLocalStorage()

    console.log('📝 用户反馈收集完成:', {
      sessionId,
      accuracy: feedback.accuracy,
      issues: feedback.issues
    })
  }

  // 保存到本地存储
  private saveToLocalStorage(): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem('ocr_analysis_data', JSON.stringify(this.collectedData))
      localStorage.setItem('ocr_feedback_data', JSON.stringify(this.feedbackData))
    } catch (error) {
      console.warn('保存OCR分析数据到本地存储失败:', error)
    }
  }

  // 从本地存储加载数据
  public loadFromLocalStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const analysisData = localStorage.getItem('ocr_analysis_data')
      const feedbackData = localStorage.getItem('ocr_feedback_data')

      if (analysisData) {
        this.collectedData = JSON.parse(analysisData)
      }

      if (feedbackData) {
        this.feedbackData = JSON.parse(feedbackData)
      }
    } catch (error) {
      console.warn('从本地存储加载OCR数据失败:', error)
    }
  }

  // 生成数据分析报告
  public generateAnalysisReport(): any {
    const totalSessions = this.collectedData.length

    if (totalSessions === 0) {
      return { message: '暂无数据' }
    }

    // 布局类型分布
    const layoutTypes = this.collectedData.reduce((acc, data) => {
      acc[data.ocrAnalysis.layoutType] = (acc[data.ocrAnalysis.layoutType] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // 坐标质量分布
    const coordinateQuality = this.collectedData.reduce((acc, data) => {
      acc[data.ocrAnalysis.coordinateQuality] = (acc[data.ocrAnalysis.coordinateQuality] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // 平均识别准确率
    const avgFloorRecognition = this.collectedData.reduce((sum, data) =>
      sum + data.parseAnalysis.floorRecognitionRate, 0) / totalSessions

    const avgVentTypeAccuracy = this.collectedData.reduce((sum, data) =>
      sum + data.parseAnalysis.ventTypeAccuracy, 0) / totalSessions

    const avgDimensionParseRate = this.collectedData.reduce((sum, data) =>
      sum + data.parseAnalysis.dimensionParseRate, 0) / totalSessions

    const overallSuccessRate = this.collectedData.filter(data =>
      data.parseAnalysis.overallSuccess).length / totalSessions

    return {
      totalSessions,
      layoutTypeDistribution: layoutTypes,
      coordinateQualityDistribution: coordinateQuality,
      averageAccuracy: {
        floorRecognition: (avgFloorRecognition * 100).toFixed(1) + '%',
        ventTypeAccuracy: (avgVentTypeAccuracy * 100).toFixed(1) + '%',
        dimensionParseRate: (avgDimensionParseRate * 100).toFixed(1) + '%',
        overallSuccess: (overallSuccessRate * 100).toFixed(1) + '%'
      },
      userFeedback: {
        totalFeedbacks: this.feedbackData.length,
        averageRating: this.feedbackData.length > 0
          ? (this.feedbackData.reduce((sum, f) => sum + f.accuracy, 0) / this.feedbackData.length).toFixed(1)
          : 'N/A'
      }
    }
  }

  // 导出数据
  public exportData(): { analysisData: OCRAnalysisData[], feedbackData: UserFeedback[] } {
    return {
      analysisData: this.collectedData,
      feedbackData: this.feedbackData
    }
  }

  // 清空数据
  public clearData(): void {
    this.collectedData = []
    this.feedbackData = []
    if (typeof window !== 'undefined') {
      localStorage.removeItem('ocr_analysis_data')
      localStorage.removeItem('ocr_feedback_data')
    }
  }
}

// 导出单例实例
export const ocrDataCollector = OCRDataCollector.getInstance()
