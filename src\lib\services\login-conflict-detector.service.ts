/**
 * 🔐 登录冲突检测服务
 * 
 * 功能：
 * - 检测同一账号的多重登录
 * - 提供详细的冲突信息
 * - 支持强制踢出策略
 */

import { db } from '@/lib/database'

export interface LoginConflictInfo {
  hasConflict: boolean
  conflictSessions: Array<{
    sessionId: string
    loginTime: Date
    ipAddress?: string
    deviceInfo?: string
    userAgent?: string
    isCurrentDevice: boolean
  }>
  totalActiveSessions: number
  recommendation: 'allow' | 'warn' | 'block'
  message: string
}

export interface ConflictDetectionOptions {
  allowSameDevice: boolean
  allowMultipleDevices: boolean
  maxConcurrentSessions: number
  deviceFingerprintTolerance: number // 设备指纹相似度容忍度 (0-1)
}

export class LoginConflictDetector {
  
  /**
   * 检测登录冲突
   */
  static async detectConflict(
    userId: string,
    userType: 'admin' | 'factory_user',
    currentDeviceFingerprint: string,
    currentIP: string,
    options: ConflictDetectionOptions
  ): Promise<LoginConflictInfo> {
    try {
      console.log('🔍 开始检测登录冲突:', { userId, userType, currentIP })

      // 获取所有活跃会话
      const activeSessions = await this.getActiveSessions(userId, userType)
      
      if (activeSessions.length === 0) {
        return {
          hasConflict: false,
          conflictSessions: [],
          totalActiveSessions: 0,
          recommendation: 'allow',
          message: '无活跃会话，允许登录'
        }
      }

      console.log(`📊 发现 ${activeSessions.length} 个活跃会话`)

      // 分析会话冲突
      const conflictAnalysis = this.analyzeSessionConflicts(
        activeSessions,
        currentDeviceFingerprint,
        currentIP,
        options
      )

      // 生成冲突信息
      const conflictInfo: LoginConflictInfo = {
        hasConflict: conflictAnalysis.hasConflict,
        conflictSessions: activeSessions.map(session => ({
          sessionId: session.sessionId || '',
          loginTime: session.loginTime,
          ipAddress: session.ipAddress || undefined,
          deviceInfo: session.deviceInfo || undefined,
          userAgent: session.userAgent || undefined,
          isCurrentDevice: this.isSameDevice(
            session.deviceInfo || '',
            currentDeviceFingerprint,
            options.deviceFingerprintTolerance
          )
        })),
        totalActiveSessions: activeSessions.length,
        recommendation: conflictAnalysis.recommendation,
        message: conflictAnalysis.message
      }

      console.log('🔍 冲突检测结果:', {
        hasConflict: conflictInfo.hasConflict,
        totalSessions: conflictInfo.totalActiveSessions,
        recommendation: conflictInfo.recommendation
      })

      return conflictInfo

    } catch (error) {
      console.error('❌ 登录冲突检测失败:', error)
      return {
        hasConflict: false,
        conflictSessions: [],
        totalActiveSessions: 0,
        recommendation: 'allow',
        message: '冲突检测失败，默认允许登录'
      }
    }
  }

  /**
   * 获取用户的所有活跃会话
   */
  private static async getActiveSessions(userId: string, userType: 'admin' | 'factory_user') {
    try {
      const sessions = await db.getActiveUserSessions(userId, userType)
      return sessions || []
    } catch (error) {
      console.error('❌ 获取活跃会话失败:', error)
      return []
    }
  }

  /**
   * 分析会话冲突
   */
  private static analyzeSessionConflicts(
    activeSessions: any[],
    currentDeviceFingerprint: string,
    currentIP: string,
    options: ConflictDetectionOptions
  ) {
    // 检查是否有同设备会话
    const sameDeviceSessions = activeSessions.filter(session =>
      this.isSameDevice(
        session.deviceInfo || '',
        currentDeviceFingerprint,
        options.deviceFingerprintTolerance
      )
    )

    // 检查是否有同IP会话
    const sameIPSessions = activeSessions.filter(session =>
      session.ipAddress === currentIP
    )

    // 决策逻辑
    if (sameDeviceSessions.length > 0 && options.allowSameDevice) {
      return {
        hasConflict: false,
        recommendation: 'allow' as const,
        message: '检测到同设备登录，允许共存'
      }
    }

    if (activeSessions.length >= options.maxConcurrentSessions) {
      return {
        hasConflict: true,
        recommendation: 'block' as const,
        message: `超过最大并发会话数限制 (${options.maxConcurrentSessions})`
      }
    }

    if (!options.allowMultipleDevices && activeSessions.length > 0) {
      return {
        hasConflict: true,
        recommendation: 'block' as const,
        message: '检测到多设备登录，系统配置为单设备模式'
      }
    }

    return {
      hasConflict: true,
      recommendation: 'warn' as const,
      message: '检测到可疑登录活动，建议验证'
    }
  }

  /**
   * 判断是否为同一设备
   */
  private static isSameDevice(
    deviceInfo1: string,
    deviceInfo2: string,
    tolerance: number = 0.8
  ): boolean {
    if (!deviceInfo1 || !deviceInfo2) return false
    
    // 简单的字符串相似度比较
    const similarity = this.calculateStringSimilarity(deviceInfo1, deviceInfo2)
    return similarity >= tolerance
  }

  /**
   * 计算字符串相似度
   */
  private static calculateStringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1.0
    if (str1.length === 0 || str2.length === 0) return 0.0

    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1

    if (longer.length === 0) return 1.0

    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  /**
   * 计算编辑距离
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }

    return matrix[str2.length][str1.length]
  }

  /**
   * 生成冲突报告
   */
  static generateConflictReport(conflictInfo: LoginConflictInfo): string {
    if (!conflictInfo.hasConflict) {
      return '✅ 无登录冲突'
    }

    let report = `⚠️ 检测到登录冲突\n\n`
    report += `活跃会话数：${conflictInfo.totalActiveSessions}\n`
    report += `建议操作：${conflictInfo.recommendation}\n`
    report += `详细信息：${conflictInfo.message}\n\n`

    if (conflictInfo.conflictSessions.length > 0) {
      report += `会话详情：\n`
      conflictInfo.conflictSessions.forEach((session, index) => {
        report += `${index + 1}. 会话ID: ${session.sessionId.substring(0, 8)}...\n`
        report += `   登录时间: ${session.loginTime.toLocaleString()}\n`
        report += `   IP地址: ${session.ipAddress || '未知'}\n`
        report += `   设备信息: ${session.deviceInfo || '未知'}\n`
        report += `   是否同设备: ${session.isCurrentDevice ? '是' : '否'}\n\n`
      })
    }

    return report
  }
}
