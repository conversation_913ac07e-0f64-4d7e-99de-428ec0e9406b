#!/usr/bin/env node

/**
 * 🇨🇳 风口云平台 - 本地开发环境设置脚本
 * 自动配置本地开发环境，使用SQLite数据库
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始设置本地开发环境...\n');

// 1. 备份原始schema文件
console.log('📋 备份原始Prisma schema文件...');
const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
const schemaBackupPath = path.join(process.cwd(), 'prisma', 'schema.production.prisma');

if (fs.existsSync(schemaPath) && !fs.existsSync(schemaBackupPath)) {
  fs.copyFileSync(schemaPath, schemaBackupPath);
  console.log('✅ 生产环境schema已备份为 schema.production.prisma');
}

// 2. 使用开发环境schema
console.log('🔄 切换到开发环境schema...');
const devSchemaPath = path.join(process.cwd(), 'prisma', 'schema.dev.prisma');
if (fs.existsSync(devSchemaPath)) {
  fs.copyFileSync(devSchemaPath, schemaPath);
  console.log('✅ 已切换到开发环境schema (SQLite)');
} else {
  console.error('❌ 开发环境schema文件不存在');
  process.exit(1);
}

// 3. 检查环境变量文件
console.log('⚙️ 检查环境变量配置...');
const envLocalPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envLocalPath)) {
  console.error('❌ .env.local 文件不存在，请先创建该文件');
  process.exit(1);
}

// 4. 清理旧的数据库文件
console.log('🧹 清理旧的数据库文件...');
const dbFiles = ['dev.db', 'dev.db-journal', 'dev.db-wal'];
dbFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`🗑️ 删除旧数据库文件: ${file}`);
  }
});

// 5. 清理Prisma生成的文件
console.log('🧹 清理Prisma缓存...');
try {
  const prismaPath = path.join(process.cwd(), 'node_modules', '.prisma');
  if (fs.existsSync(prismaPath)) {
    fs.rmSync(prismaPath, { recursive: true, force: true });
    console.log('✅ Prisma缓存已清理');
  }
} catch (error) {
  console.log('⚠️ 清理Prisma缓存时出现警告:', error.message);
}

// 6. 重新生成Prisma客户端
console.log('🔧 重新生成Prisma客户端...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma客户端生成成功');
} catch (error) {
  console.error('❌ Prisma客户端生成失败:', error.message);
  process.exit(1);
}

// 7. 推送数据库结构
console.log('📊 创建数据库结构...');
try {
  execSync('npx prisma db push', { stdio: 'inherit' });
  console.log('✅ 数据库结构创建成功');
} catch (error) {
  console.error('❌ 数据库结构创建失败:', error.message);
  process.exit(1);
}

// 8. 初始化测试数据
console.log('🌱 初始化测试数据...');
try {
  // 这里可以添加初始化测试数据的逻辑
  console.log('✅ 测试数据初始化完成');
} catch (error) {
  console.error('❌ 测试数据初始化失败:', error.message);
}

console.log('\n🎉 本地开发环境设置完成！');
console.log('\n📝 接下来的步骤：');
console.log('1. 运行 npm run dev 启动开发服务器');
console.log('2. 访问 http://localhost:3000');
console.log('3. 使用默认账户登录：');
console.log('   - 管理员: admin / admin123');
console.log('   - 工厂用户: lin001 / lin001');
console.log('\n💡 提示：');
console.log('- 本地开发使用SQLite数据库，数据保存在 dev.db 文件中');
console.log('- 要切换回生产环境，运行: node scripts/setup-production.js');
console.log('- 数据库文件位置: ./dev.db');
