/**
 * 最简单的调试API
 */

import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  console.log('🔍 简单调试API被调用')
  
  try {
    console.log('📝 开始解析请求体...')
    const body = await request.json()
    console.log('✅ 请求体解析成功:', body)
    
    return NextResponse.json({
      success: true,
      message: '简单调试API工作正常',
      receivedData: body,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('❌ 简单调试API错误:', error)
    return NextResponse.json({
      success: false,
      error: '解析请求体失败'
    }, { status: 400 })
  }
}

export async function GET(request: NextRequest) {
  console.log('🔍 简单调试API GET被调用')
  
  return NextResponse.json({
    success: true,
    message: '简单调试API GET工作正常',
    timestamp: new Date().toISOString()
  })
}
