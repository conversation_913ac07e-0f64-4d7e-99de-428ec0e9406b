"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { X, User, Lock, Shield } from "lucide-react"

interface AddEmployeeDialogProps {
  isOpen: boolean
  onClose: () => void
  onSave: (employee: unknown) => void
}

export function AddEmployeeDialog({ isOpen, onClose, onSave }: AddEmployeeDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    username: "",
    password: "",
    confirmPassword: "",
    role: "employee",
    permissions: [] as string[]
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const roleOptions = [
    { value: "manager", label: "管理员", description: "可以管理所有功能" },
    { value: "employee", label: "员工", description: "可以录入和查看订单" }
  ]

  const permissionOptions = [
    { id: "orders:create", label: "创建订单", category: "订单管理" },
    { id: "orders:read", label: "查看订单", category: "订单管理" },
    { id: "orders:update", label: "修改订单", category: "订单管理" },
    { id: "orders:delete", label: "删除订单", category: "订单管理" },
    { id: "clients:create", label: "创建客户", category: "客户管理" },
    { id: "clients:read", label: "查看客户", category: "客户管理" },
    { id: "clients:update", label: "修改客户", category: "客户管理" },
    { id: "clients:delete", label: "删除客户", category: "客户管理" },
    { id: "reports:read", label: "查看报表", category: "数据分析" },
    { id: "settings:read", label: "查看设置", category: "系统设置" },
    { id: "settings:update", label: "修改设置", category: "系统设置" }
  ]

  const handleRoleChange = (role: string) => {
    setFormData(prev => ({ ...prev, role }))
    
    // 根据角色自动设置权限
    let defaultPermissions: string[] = []
    if (role === "manager") {
      defaultPermissions = permissionOptions.map(p => p.id)
    } else if (role === "employee") {
      defaultPermissions = ["orders:create", "orders:read", "clients:read"]
    }
    
    setFormData(prev => ({ ...prev, permissions: defaultPermissions }))
  }

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked 
        ? [...prev.permissions, permissionId]
        : prev.permissions.filter(p => p !== permissionId)
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "请输入员工姓名"
    }

    if (!formData.username.trim()) {
      newErrors.username = "请输入用户名"
    } else if (formData.username.length < 3) {
      newErrors.username = "用户名至少3个字符"
    }

    if (!formData.password) {
      newErrors.password = "请输入密码"
    } else if (formData.password.length < 6) {
      newErrors.password = "密码至少6个字符"
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "两次输入的密码不一致"
    }

    if (formData.permissions.length === 0) {
      newErrors.permissions = "请至少选择一个权限"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (!validateForm()) return

    // 传递员工数据给父组件处理
    onSave(formData)
    onClose()

    // 重置表单
    setFormData({
      name: "",
      username: "",
      password: "",
      confirmPassword: "",
      role: "employee",
      permissions: []
    })
    setErrors({})
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>添加员工</CardTitle>
              <CardDescription>创建新的员工账号并设置权限</CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* 基本信息 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center">
                <User className="h-5 w-5 mr-2" />
                基本信息
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">员工姓名 *</label>
                  <Input
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="请输入员工姓名"
                  />
                  {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">用户名 *</label>
                  <Input
                    value={formData.username}
                    onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                    placeholder="请输入用户名"
                  />
                  {errors.username && <p className="text-sm text-red-600 mt-1">{errors.username}</p>}
                </div>
              </div>
            </div>

            {/* 密码设置 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center">
                <Lock className="h-5 w-5 mr-2" />
                密码设置
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">密码 *</label>
                  <Input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="请输入密码"
                  />
                  {errors.password && <p className="text-sm text-red-600 mt-1">{errors.password}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">确认密码 *</label>
                  <Input
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    placeholder="请再次输入密码"
                  />
                  {errors.confirmPassword && <p className="text-sm text-red-600 mt-1">{errors.confirmPassword}</p>}
                </div>
              </div>
            </div>

            {/* 角色选择 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                角色权限
              </h3>
              
              <div>
                <label className="block text-sm font-medium mb-2">选择角色 *</label>
                <div className="space-y-2">
                  {roleOptions.map(option => (
                    <label key={option.value} className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="role"
                        value={option.value}
                        checked={formData.role === option.value}
                        onChange={(e) => handleRoleChange(e.target.value)}
                        className="text-blue-600"
                      />
                      <div>
                        <p className="font-medium">{option.label}</p>
                        <p className="text-sm text-gray-600">{option.description}</p>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* 权限详细设置 */}
              <div>
                <label className="block text-sm font-medium mb-2">具体权限 *</label>
                <div className="border rounded-lg p-4 max-h-48 overflow-y-auto">
                  {["订单管理", "客户管理", "数据分析", "系统设置"].map(category => (
                    <div key={category} className="mb-4">
                      <h4 className="font-medium text-gray-900 mb-2">{category}</h4>
                      <div className="space-y-2">
                        {permissionOptions
                          .filter(p => p.category === category)
                          .map(permission => (
                            <label key={permission.id} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                checked={formData.permissions.includes(permission.id)}
                                onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}
                                className="text-blue-600"
                              />
                              <span className="text-sm">{permission.label}</span>
                            </label>
                          ))}
                      </div>
                    </div>
                  ))}
                </div>
                {errors.permissions && <p className="text-sm text-red-600 mt-1">{errors.permissions}</p>}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button onClick={handleSave}>
                保存员工
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
