// 验证OCR修复效果
const testText = `江苏·淮安市安豪栅栏有限公司
一楼厨房出风口125x1435
回风口245x1120
客厅出风口130×2770
回风口150x800243x1750
次卧出风口150x800
回风口245x950
主卧出风口150x900
回风口250x.1020
二楼客厅出凤口175x1300
回风口295x1650
主卧出风口140x1500
回风口250x1500
次卧出风口140x800
同风口245x850`;

console.log('🧪 验证OCR修复效果');
console.log('📝 测试文本:');
console.log(testText);

console.log('\n🔍 问题分析:');
console.log('1. ❌ 系统报错: ReferenceError: ventResult is not defined');
console.log('2. 🔧 已修复: 移除了错误的 ventResult.floorInfo 引用');
console.log('3. ✅ 楼层识别: 用户已手动修正"楼上"为"二楼"');

console.log('\n📊 预期修复效果:');
console.log('- 不再出现 ventResult 未定义错误');
console.log('- 正确识别14个尺寸（包括连续尺寸分割）');
console.log('- 正确识别1楼和2楼');
console.log('- 去除重复备注');

// 分析连续尺寸
const continuousPattern = /(\d+)\s*[xX×]\s*(\d+)(\d{3,4})\s*[xX×]\s*(\d+)/;
const lines = testText.split('\n');

console.log('\n🔧 连续尺寸检测:');
lines.forEach((line, index) => {
  const match = line.match(continuousPattern);
  if (match) {
    console.log(`第${index + 1}行: "${line}"`);
    console.log(`  原始: ${match[0]}`);
    console.log(`  分割为: ${match[1]}x${match[2]} 和 ${match[3]}x${match[4]}`);
  }
});

// 分析楼层
console.log('\n🏢 楼层识别:');
lines.forEach((line, index) => {
  const floorMatch = line.match(/^(一楼|二楼|三楼|四楼|五楼)/);
  if (floorMatch) {
    const chineseFloor = floorMatch[1];
    const floorNumber = chineseFloor === '一楼' ? '1' : chineseFloor === '二楼' ? '2' : chineseFloor;
    console.log(`第${index + 1}行: "${line}" → ${floorNumber}楼`);
  }
});

console.log('\n✅ 修复状态: 代码已修复，等待测试验证');
