/**
 * 🇨🇳 风口云平台 - Prisma 客户端配置
 * 
 * 这个文件提供了 Prisma 客户端的单例实例
 * 确保在整个应用程序中使用同一个数据库连接
 */

import { PrismaClient } from '@prisma/client'

// 声明全局变量类型
declare global {
   
  var __prisma: PrismaClient | undefined
}

// 创建 Prisma 客户端实例
const createPrismaClient = () => {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  })
}

// 在开发环境中使用全局变量避免热重载时创建多个实例
// 在生产环境中直接创建新实例
const prisma = globalThis.__prisma ?? createPrismaClient()

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma
}

// 导出 Prisma 客户端实例
export { prisma }

// 默认导出
export default prisma

// 在应用程序关闭时断开连接
if (typeof window === 'undefined') {
  // 只在服务端环境中注册关闭事件
  process.on('beforeExit', async () => {
    await prisma.$disconnect()
  })

  process.on('SIGINT', async () => {
    await prisma.$disconnect()
    process.exit(0)
  })

  process.on('SIGTERM', async () => {
    await prisma.$disconnect()
    process.exit(0)
  })
}
