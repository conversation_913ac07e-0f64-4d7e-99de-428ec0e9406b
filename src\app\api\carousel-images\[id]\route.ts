/**
 * 🇨🇳 风口云平台 - 轮播图片删除API
 * 
 * 功能说明：
 * - 删除指定ID的轮播图片
 */

import { NextRequest, NextResponse } from 'next/server'
import { imageDatabase } from '@/lib/image-database'

// 删除指定ID的轮播图片
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const imageId = resolvedParams.id

    if (!imageId) {
      return NextResponse.json(
        { error: '缺少图片ID' },
        { status: 400 }
      )
    }

    console.log('🗑️ 删除图片:', imageId)

    const success = await imageDatabase.deleteImage(imageId)

    if (success) {
      console.log('✅ 图片删除成功')
      return NextResponse.json({ success: true })
    } else {
      console.log('❌ 图片删除失败')
      return NextResponse.json(
        { error: '删除失败' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ 删除图片失败:', error)
    return NextResponse.json(
      { error: `删除失败: ${error.message || '未知错误'}` },
      { status: 500 }
    )
  }
}
