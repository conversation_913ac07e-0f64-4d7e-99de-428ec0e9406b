'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from './button'
import { imageStorage } from '@/lib/image-storage'

interface CarouselProps {
  images: string[]
  autoPlay?: boolean
  interval?: number
  showDots?: boolean
  showArrows?: boolean
  className?: string
  imageClassName?: string
}

export function Carousel({
  images,
  autoPlay = true,
  interval = 5000,
  showDots = true,
  showArrows = true,
  className = '',
  imageClassName = ''
}: CarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)

  // 自动播放
  useEffect(() => {
    if (!autoPlay || images.length <= 1) return

    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === images.length - 1 ? 0 : prevIndex + 1
      )
    }, interval)

    return () => clearInterval(timer)
  }, [autoPlay, interval, images.length])

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? images.length - 1 : currentIndex - 1)
  }

  const goToNext = () => {
    setCurrentIndex(currentIndex === images.length - 1 ? 0 : currentIndex + 1)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
  }

  if (images.length === 0) {
    return (
      <div className={`bg-gray-200 rounded-lg flex items-center justify-center ${className}`}>
        <p className="text-gray-500">暂无图片</p>
      </div>
    )
  }

  return (
    <div className={`relative overflow-hidden rounded-lg shadow-lg ${className}`}>
      {/* 图片容器 */}
      <div
        className="flex transition-transform duration-500 ease-in-out h-full"
        style={{ transform: `translateX(-${currentIndex * 100}%)` }}
      >
        {images.map((image, index) => (
          <div key={index} className="w-full flex-shrink-0 h-full relative">
            {image.startsWith('data:') ? (
              // Base64图片使用img标签
              <img
                src={image}
                alt={`轮播图 ${index + 1}`}
                className={`w-full h-full object-contain bg-gradient-to-br from-gray-50 to-gray-100 ${imageClassName}`}
                style={{
                  objectPosition: 'center',
                  borderRadius: '8px'
                }}
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.src = '/images/products/placeholder.svg'
                }}
              />
            ) : (
              // 外部URL使用Next.js Image组件
              <Image
                src={image}
                alt={`轮播图 ${index + 1}`}
                fill
                className={`object-contain bg-gradient-to-br from-gray-50 to-gray-100 ${imageClassName}`}
                style={{
                  objectPosition: 'center',
                  borderRadius: '8px'
                }}
                sizes="100vw"
                onError={() => {
                  console.log('图片加载失败，使用默认图片')
                }}
              />
            )}
          </div>
        ))}
      </div>

      {/* 左右箭头 */}
      {showArrows && images.length > 1 && (
        <>
          <Button
            variant="outline"
            size="icon"
            className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white/90"
            onClick={goToPrevious}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white/90"
            onClick={goToNext}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </>
      )}

      {/* 指示点 */}
      {showDots && images.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {images.map((_, index) => (
            <button
              key={index}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentIndex 
                  ? 'bg-white' 
                  : 'bg-white/50 hover:bg-white/70'
              }`}
              onClick={() => goToSlide(index)}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// 产品轮播图组件
interface ProductCarouselProps {
  title?: string
  className?: string
}

export function ProductCarousel({ title = "产品展示", className = "" }: ProductCarouselProps) {
  const [carouselImages, setCarouselImages] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // 从数据库获取图片
  useEffect(() => {
    const category = title === "工艺展示" ? 'process' : 'product'

    const loadImages = async () => {
      try {
        setIsLoading(true)

        // 首先尝试从数据库获取图片
        const response = await fetch(`/api/carousel-images?category=${category}`)
        const data = await response.json()

        if (data.success && data.images.length > 0) {
          // 使用数据库中的图片
          const imageUrls = data.images.map((img: unknown) => img.imageData)
          setCarouselImages(imageUrls)
          console.log(`✅ 从数据库加载了 ${imageUrls.length} 张${title}图片`)
        } else {
          // 如果数据库没有图片，尝试从localStorage获取
          const localImages = imageStorage.getImageUrls(category)

          if (localImages.length > 0) {
            setCarouselImages(localImages)
            console.log(`✅ 从本地存储加载了 ${localImages.length} 张${title}图片`)
          } else {
            // 最后使用默认图片
            setCarouselImages(getDefaultImages())
            console.log(`✅ 使用默认${title}图片`)
          }
        }
      } catch (error) {
        console.error('加载图片失败:', error)

        // 出错时尝试本地存储
        try {
          const localImages = imageStorage.getImageUrls(category)
          if (localImages.length > 0) {
            setCarouselImages(localImages)
          } else {
            setCarouselImages(getDefaultImages())
          }
        } catch {
          setCarouselImages(getDefaultImages())
        }
      } finally {
        setIsLoading(false)
      }
    }

    // 初始加载
    loadImages()

    // 监听本地存储更新事件（向后兼容）
    try {
      const cleanup = imageStorage.onImagesUpdated((updatedCategory) => {
        if (updatedCategory === category) {
          loadImages()
        }
      })

      return cleanup
    } catch (error) {
      console.error('监听图片更新失败:', error)
      return () => {}
    }
  }, [title])

  // 默认产品图片（备用）
  const getDefaultImages = () => [
    '/images/products/vent-1.svg',
    '/images/products/vent-2.svg',
    '/images/products/vent-3.svg',
    '/images/products/vent-4.svg',
    '/images/products/vent-5.svg'
  ]

  // 产品描述信息
  const getProductDescription = (title: string) => {
    switch (title) {
      case "产品展示":
        return "优质风口产品，精工制造"
      case "工艺展示":
        return "先进生产工艺，品质保证"
      default:
        return "专业风口加工，品质保证"
    }
  }

  // 显示加载状态
  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {title && (
          <h3 className="text-lg font-semibold text-gray-800 text-center">
            {title}
          </h3>
        )}
        <div className="h-64 sm:h-80 md:h-96 lg:h-[32rem] w-full bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-500">加载图片中...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-800 text-center">
          {title}
        </h3>
      )}
      <Carousel
        images={carouselImages}
        autoPlay={true}
        interval={4000}
        showDots={true}
        showArrows={true}
        className="h-64 sm:h-80 md:h-96 lg:h-[32rem] w-full"
        imageClassName="object-contain"
      />
      <div className="text-center">
        <p className="text-sm text-gray-600">
          {getProductDescription(title)}
        </p>
        <div className="mt-2 flex justify-center space-x-4 text-xs text-gray-500">
          <span>• 精密加工</span>
          <span>• 质量保证</span>
          <span>• 快速交付</span>
          <span>• 持久存储</span>
        </div>
      </div>
    </div>
  )
}
