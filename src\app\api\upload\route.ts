import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

// 配置 API 路由的请求体大小限制
export const runtime = 'nodejs'
export const maxDuration = 60 // 60秒超时
export const dynamic = 'force-dynamic'

// 配置请求体大小限制
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
    responseLimit: '50mb',
  },
}

export async function POST(request: NextRequest) {
  try {
    console.log('📁 文件上传API被调用')
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json({
        success: false,
        error: '没有找到文件'
      }, { status: 400 })
    }

    console.log(`📄 接收到文件: ${file.name}, 大小: ${file.size} bytes`)

    // 验证文件大小 (10MB限制)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({
        success: false,
        error: '文件大小不能超过10MB'
      }, { status: 400 })
    }

    // 验证文件类型
    const allowedTypes = [
      'image/jpeg',
      'image/png', 
      'image/gif',
      'image/bmp',
      'text/plain',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        success: false,
        error: `不支持的文件类型: ${file.type}`
      }, { status: 400 })
    }

    // 创建上传目录
    const uploadDir = join(process.cwd(), 'public', 'uploads')
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
      console.log('📁 创建上传目录:', uploadDir)
    }

    // 生成唯一文件名
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const fileExtension = file.name.split('.').pop()
    const fileName = `${timestamp}_${randomString}.${fileExtension}`
    const filePath = join(uploadDir, fileName)

    console.log(`💾 保存文件到: ${filePath}`)

    // 将文件保存到磁盘
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // 生成文件访问URL
    const fileUrl = `/uploads/${fileName}`

    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 1000))

    const result = {
      success: true,
      data: {
        fileName: file.name,
        originalName: file.name,
        savedName: fileName,
        size: file.size,
        type: file.type,
        url: fileUrl,
        uploadTime: new Date().toISOString(),
        path: filePath
      },
      message: '文件上传成功'
    }

    console.log('✅ 文件上传成功:', result.data)

    return NextResponse.json(result)

  } catch (error) {
    console.error('❌ 文件上传失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '文件上传失败',
      message: '服务器内部错误'
    }, { status: 500 })
  }
}

// 获取上传的文件列表
export async function GET(request: NextRequest) {
  try {
    console.log('📋 获取上传文件列表')
    
    const uploadDir = join(process.cwd(), 'public', 'uploads')
    
    if (!existsSync(uploadDir)) {
      return NextResponse.json({
        success: true,
        data: [],
        message: '上传目录不存在'
      })
    }

    // 这里可以实现获取文件列表的逻辑
    // 为了演示，返回空列表
    return NextResponse.json({
      success: true,
      data: [],
      message: '获取文件列表成功'
    })

  } catch (error) {
    console.error('❌ 获取文件列表失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取文件列表失败'
    }, { status: 500 })
  }
}
