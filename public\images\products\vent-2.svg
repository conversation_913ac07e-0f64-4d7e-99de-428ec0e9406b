<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad2)"/>
  <circle cx="200" cy="150" r="80" fill="none" stroke="white" stroke-width="4"/>
  <circle cx="200" cy="150" r="60" fill="none" stroke="white" stroke-width="2"/>
  <circle cx="200" cy="150" r="40" fill="none" stroke="white" stroke-width="2"/>
  <circle cx="200" cy="150" r="20" fill="white" opacity="0.8"/>
  <text x="200" y="40" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle" font-weight="bold">
    圆形风口产品
  </text>
  <text x="200" y="280" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">
    圆形设计 | 美观实用 | 高效通风
  </text>
</svg>
