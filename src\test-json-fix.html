<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 JSON解析修复验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover { background: #1976D2; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .button.success { background: #4CAF50; }
        .button.success:hover { background: #45a049; }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #4CAF50; background: #e8f5e8; }
        .error { border-color: #f44336; background: #ffe8e8; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JSON解析修复验证</h1>
        <p>验证修复后的硅基流动API JSON解析功能</p>
        
        <div style="background: #e8f5e8; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #4CAF50;">
            <strong>✅ 已修复的问题：</strong><br>
            • 增加Token数量：300 → 800<br>
            • 改进Prompt格式，明确要求返回有效JSON<br>
            • 添加JSON错误自动修复功能<br>
            • 增强错误处理和日志记录
        </div>
        
        <button class="button success" onclick="testFixedAPI()">🧪 测试修复后的API</button>
        <button class="button" onclick="testJSONParsing()">🔧 测试JSON解析修复</button>
        <button class="button" onclick="simulateJSONErrors()">⚠️ 模拟JSON错误修复</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_KEY = 'sk-szczomdkrprhzlzuzwlblenwfvvuuuyxbxnjgmrcetorftth';
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}] ${title}</strong>\n${content}`;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function testFixedAPI() {
            addResult('🧪 测试修复后的API', '使用优化后的参数和Prompt测试...', 'info');
            
            const testText = `项目：JSON修复测试
一楼：
出风口 2665×155 白色 1个
回风口 1200×300 白色 1个`;

            const optimizedPrompt = `解析风口订单为JSON格式。

输入文本：
${testText}

解析规则：
- 宽度≤254mm = 出风口 (systemType: "double_white_outlet")
- 宽度≥255mm = 回风口 (systemType: "white_return")
- 数字<100当作厘米，需要×10转换为毫米

返回格式（必须是有效的JSON）：
{
  "projects": [{
    "floors": [{
      "rooms": [{
        "vents": [
          {
            "systemType": "double_white_outlet",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1
          }
        ]
      }]
    }]
  }]
}

请确保返回完整有效的JSON，不要添加任何解释文字。`;

            const startTime = performance.now();
            
            try {
                const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'deepseek-ai/DeepSeek-V3',
                        messages: [{ role: 'user', content: optimizedPrompt }],
                        max_tokens: 800,
                        temperature: 0.2,
                        stream: false
                    }),
                    signal: AbortSignal.timeout(30000)
                });
                
                const duration = performance.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0]?.message?.content;
                    
                    addResult(
                        '✅ API调用成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
Token使用: ${data.usage?.total_tokens || 'N/A'}
响应长度: ${content?.length || 0} 字符

原始响应:
${content}`,
                        'success'
                    );
                    
                    // 测试JSON解析
                    testJSONParsingWithContent(content);
                    
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ API调用失败',
                        `状态码: ${response.status}
响应时间: ${duration.toFixed(2)}ms
错误信息: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const duration = performance.now() - startTime;
                addResult(
                    '❌ API调用异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        function testJSONParsingWithContent(content) {
            addResult('🔧 JSON解析测试', '测试修复后的JSON解析逻辑...', 'info');
            
            try {
                // 模拟修复逻辑
                let jsonStr = content.trim();
                
                // 移除markdown代码块标记
                jsonStr = jsonStr.replace(/^```json\s*/i, '');
                jsonStr = jsonStr.replace(/^```\s*/i, '');
                jsonStr = jsonStr.replace(/\s*```\s*$/g, '');
                
                // 移除可能的解释文字
                jsonStr = jsonStr.replace(/^[^{]*/, '');
                jsonStr = jsonStr.replace(/[^}]*$/, '');
                
                // 查找JSON内容
                const firstBrace = jsonStr.indexOf('{');
                const lastBrace = jsonStr.lastIndexOf('}');
                
                if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
                    jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
                }
                
                // 修复常见错误
                jsonStr = fixCommonJSONErrors(jsonStr);
                
                addResult(
                    '🧹 JSON清理结果',
                    `清理后长度: ${jsonStr.length} 字符
清理后内容:
${jsonStr}`,
                    'info'
                );
                
                // 尝试解析
                const parsed = JSON.parse(jsonStr);
                
                addResult(
                    '✅ JSON解析成功',
                    `解析成功！
项目数量: ${parsed.projects?.length || 0}
风口数量: ${parsed.projects?.[0]?.floors?.[0]?.rooms?.[0]?.vents?.length || 0}

解析结果:
${JSON.stringify(parsed, null, 2)}`,
                    'success'
                );
                
            } catch (error) {
                addResult(
                    '❌ JSON解析失败',
                    `解析错误: ${error.message}
错误位置: ${error.message.match(/position (\d+)/) ? error.message.match(/position (\d+)/)[1] : '未知'}`,
                    'error'
                );
            }
        }
        
        function fixCommonJSONErrors(jsonStr) {
            // 修复尾随逗号
            jsonStr = jsonStr.replace(/,(\s*[}\]])/g, '$1');
            
            // 修复缺少逗号的情况
            jsonStr = jsonStr.replace(/}(\s*){/g, '},$1{');
            jsonStr = jsonStr.replace(/](\s*){/g, '],$1{');
            
            // 修复单引号为双引号
            jsonStr = jsonStr.replace(/'/g, '"');
            
            // 修复属性名没有引号的情况
            jsonStr = jsonStr.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');
            
            return jsonStr;
        }
        
        function testJSONParsing() {
            addResult('🔧 JSON解析修复测试', '测试各种JSON错误的修复能力...', 'info');
            
            const testCases = [
                {
                    name: '正常JSON',
                    json: '{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet"}]}]}]}]}'
                },
                {
                    name: '尾随逗号',
                    json: '{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet",}]}]}]}]}'
                },
                {
                    name: '缺少逗号',
                    json: '{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet"}{"systemType":"white_return"}]}]}]}]}'
                },
                {
                    name: '截断的JSON',
                    json: '{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet"'
                }
            ];
            
            testCases.forEach(testCase => {
                try {
                    let fixedJson = fixCommonJSONErrors(testCase.json);
                    
                    // 如果是截断的，尝试补全
                    if (!fixedJson.endsWith('}')) {
                        const openBraces = (fixedJson.match(/{/g) || []).length;
                        const closeBraces = (fixedJson.match(/}/g) || []).length;
                        const openBrackets = (fixedJson.match(/\[/g) || []).length;
                        const closeBrackets = (fixedJson.match(/]/g) || []).length;
                        
                        for (let i = 0; i < openBrackets - closeBrackets; i++) {
                            fixedJson += ']';
                        }
                        for (let i = 0; i < openBraces - closeBraces; i++) {
                            fixedJson += '}';
                        }
                    }
                    
                    const parsed = JSON.parse(fixedJson);
                    
                    addResult(
                        `✅ ${testCase.name}`,
                        `原始: ${testCase.json}
修复: ${fixedJson}
结果: 解析成功`,
                        'success'
                    );
                    
                } catch (error) {
                    addResult(
                        `❌ ${testCase.name}`,
                        `原始: ${testCase.json}
错误: ${error.message}`,
                        'error'
                    );
                }
            });
        }
        
        function simulateJSONErrors() {
            addResult('⚠️ 模拟JSON错误', '模拟常见的JSON错误情况...', 'warning');
            
            const errorCases = [
                'Expected \',\' or \']\' after array element',
                'Unexpected token } in JSON',
                'Unterminated string in JSON',
                'Unexpected end of JSON input'
            ];
            
            errorCases.forEach(errorCase => {
                addResult(
                    `🔧 错误类型: ${errorCase}`,
                    `这种错误通常由以下原因造成:
- JSON格式不正确
- 缺少逗号或括号
- 字符串未正确闭合
- JSON被截断

修复策略:
- 自动补全缺少的括号
- 修复尾随逗号
- 规范化引号格式`,
                    'warning'
                );
            });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult(
                '📋 JSON修复验证说明',
                `🎯 目标: 验证JSON解析错误修复
🔧 修复内容:
  • Token数量: 300 → 800
  • 改进Prompt格式
  • 添加JSON自动修复
  • 增强错误处理

点击"🧪 测试修复后的API"开始验证！`,
                'info'
            );
        };
    </script>
</body>
</html>
