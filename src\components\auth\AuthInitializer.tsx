"use client"

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/store/auth'

interface AuthInitializerProps {
  children: React.ReactNode
}

/**
 * 认证初始化组件
 * 确保在应用启动时正确恢复认证状态
 */
export function AuthInitializer({ children }: AuthInitializerProps) {
  const [isInitialized, setIsInitialized] = useState(false)
  const { hydrate, isAuthenticated, accessToken } = useAuthStore()

  useEffect(() => {
    const initializeAuth = async () => {
      console.log('🚀 开始初始化认证状态...')
      
      try {
        // 手动调用hydrate来恢复状态
        hydrate()
        
        // 等待状态恢复
        await new Promise(resolve => setTimeout(resolve, 100))
        
        // 检查localStorage中的数据
        const storedToken = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null
        const storedUser = typeof window !== 'undefined' ? localStorage.getItem('user') : null
        const storedRole = typeof window !== 'undefined' ? localStorage.getItem('role') : null
        
        console.log('📋 认证状态检查:', {
          isAuthenticated,
          hasAccessToken: !!accessToken,
          hasStoredToken: !!storedToken,
          hasStoredUser: !!storedUser,
          storedRole
        })
        
        // 如果localStorage有数据但Zustand状态没有，再次尝试恢复
        if (storedToken && storedUser && !isAuthenticated) {
          console.log('🔄 重新尝试恢复认证状态...')
          hydrate()
          await new Promise(resolve => setTimeout(resolve, 200))
        }
        
        console.log('✅ 认证状态初始化完成')
      } catch (error) {
        console.error('❌ 认证状态初始化失败:', error)
      } finally {
        setIsInitialized(true)
      }
    }

    initializeAuth()
  }, [hydrate])

  // 在认证状态初始化完成前显示加载状态
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在初始化...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
