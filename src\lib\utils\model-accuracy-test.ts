/**
 * 模型准确率对比测试
 */

export interface AccuracyTestCase {
  name: string
  input: string
  expected: {
    ventCount: number
    firstVentDimensions: { length: number, width: number }
    hasRepeatedDimensions: boolean
  }
}

export const testCases: AccuracyTestCase[] = [
  {
    name: "简单单个风口",
    input: "客厅出风口300×150",
    expected: {
      ventCount: 1,
      firstVentDimensions: { length: 300, width: 150 },
      hasRepeatedDimensions: false
    }
  },
  {
    name: "重复尺寸",
    input: "客厅回风141*30     141*30",
    expected: {
      ventCount: 2,
      firstVentDimensions: { length: 1410, width: 300 }, // 应该转换单位
      hasRepeatedDimensions: true
    }
  },
  {
    name: "小数点尺寸",
    input: "客出风568.2*14.5",
    expected: {
      ventCount: 1,
      firstVentDimensions: { length: 5682, width: 145 }, // 应该去除小数点
      hasRepeatedDimensions: false
    }
  },
  {
    name: "混合格式",
    input: "房间回风93.7*30（单边）房间出风98*30（单边）",
    expected: {
      ventCount: 2,
      firstVentDimensions: { length: 937, width: 300 },
      hasRepeatedDimensions: false
    }
  }
]

export async function testModelAccuracy(model: 'deepseek-chat' | 'deepseek-reasoner') {
  console.log(`🎯 开始测试 ${model} 模型准确率`)
  
  const apiKey = 'sk-c9047196d51d4f6c99dc94f9fbef602c'
  const baseURL = 'https://api.deepseek.com/v1/chat/completions'
  
  const results = []
  
  for (const testCase of testCases) {
    console.log(`📝 测试案例: ${testCase.name}`)
    console.log(`📄 输入: ${testCase.input}`)
    
    const prompt = `分析风口订单文本，返回JSON格式结果。

文本：${testCase.input}

要求：
1. 识别所有风口，包括重复的尺寸
2. 智能单位转换：<100的数字推断为厘米，转换为毫米
3. 去除异常小数点
4. 确保大值作为长度，小值作为宽度

返回JSON格式：
{
  "projects": [
    {
      "floors": [
        {
          "rooms": [
            {
              "roomName": "房间名",
              "vents": [
                {
                  "originalType": "原始类型",
                  "dimensions": {
                    "length": 长度数字,
                    "width": 宽度数字,
                    "unit": "mm"
                  }
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}`

    const startTime = Date.now()
    
    try {
      const requestBody: any = {
        model: model,
        messages: [{ role: "user", content: prompt }],
        max_tokens: model === 'deepseek-reasoner' ? 4000 : 2000,
        stream: false
      }
      
      if (model === 'deepseek-chat') {
        requestBody.temperature = 0.1
      }
      
      const response = await fetch(baseURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify(requestBody)
      })
      
      const responseTime = Date.now() - startTime
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error(`❌ ${testCase.name} 请求失败:`, errorText)
        results.push({
          testCase: testCase.name,
          success: false,
          error: errorText,
          responseTime
        })
        continue
      }
      
      const data = await response.json()
      const content = data.choices?.[0]?.message?.content
      const reasoningContent = data.choices?.[0]?.message?.reasoning_content
      
      console.log(`⏱️ 响应时间: ${responseTime}ms`)
      if (reasoningContent) {
        console.log(`🧠 推理长度: ${reasoningContent.length} 字符`)
      }
      
      // 解析JSON结果
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/)
        if (!jsonMatch) {
          throw new Error('未找到JSON格式')
        }
        
        const parsed = JSON.parse(jsonMatch[0])
        const vents = parsed.projects?.[0]?.floors?.[0]?.rooms?.flatMap((room: any) => room.vents) || []
        
        // 验证结果
        const actualVentCount = vents.length
        const firstVent = vents[0]
        const actualFirstDimensions = {
          length: firstVent?.dimensions?.length || 0,
          width: firstVent?.dimensions?.width || 0
        }
        
        // 检查是否有重复尺寸
        const dimensions = vents.map((v: any) => `${v.dimensions?.length}×${v.dimensions?.width}`)
        const hasRepeatedDimensions = dimensions.length !== new Set(dimensions).size
        
        // 计算准确率
        const ventCountCorrect = actualVentCount === testCase.expected.ventCount
        const dimensionsCorrect = 
          actualFirstDimensions.length === testCase.expected.firstVentDimensions.length &&
          actualFirstDimensions.width === testCase.expected.firstVentDimensions.width
        const repeatCorrect = hasRepeatedDimensions === testCase.expected.hasRepeatedDimensions
        
        const accuracy = [ventCountCorrect, dimensionsCorrect, repeatCorrect].filter(Boolean).length / 3
        
        console.log(`📊 结果分析:`)
        console.log(`- 风口数量: ${actualVentCount} (期望: ${testCase.expected.ventCount}) ${ventCountCorrect ? '✅' : '❌'}`)
        console.log(`- 首个尺寸: ${actualFirstDimensions.length}×${actualFirstDimensions.width} (期望: ${testCase.expected.firstVentDimensions.length}×${testCase.expected.firstVentDimensions.width}) ${dimensionsCorrect ? '✅' : '❌'}`)
        console.log(`- 重复识别: ${hasRepeatedDimensions} (期望: ${testCase.expected.hasRepeatedDimensions}) ${repeatCorrect ? '✅' : '❌'}`)
        console.log(`- 准确率: ${(accuracy * 100).toFixed(1)}%`)
        
        results.push({
          testCase: testCase.name,
          success: true,
          responseTime,
          accuracy,
          details: {
            ventCountCorrect,
            dimensionsCorrect,
            repeatCorrect,
            actualVentCount,
            actualFirstDimensions,
            hasRepeatedDimensions
          },
          hasReasoning: !!reasoningContent,
          reasoningLength: reasoningContent?.length || 0
        })
        
      } catch (parseError) {
        console.error(`❌ ${testCase.name} JSON解析失败:`, parseError)
        console.log(`📄 原始响应:`, content)
        
        results.push({
          testCase: testCase.name,
          success: false,
          error: 'JSON解析失败',
          responseTime,
          rawResponse: content
        })
      }
      
    } catch (error: any) {
      const responseTime = Date.now() - startTime
      console.error(`❌ ${testCase.name} 测试失败:`, error.message)
      
      results.push({
        testCase: testCase.name,
        success: false,
        error: error.message,
        responseTime
      })
    }
    
    console.log('---')
  }
  
  // 计算总体统计
  const successfulTests = results.filter(r => r.success && r.accuracy !== undefined)
  const averageAccuracy = successfulTests.length > 0 
    ? successfulTests.reduce((sum, r) => sum + r.accuracy, 0) / successfulTests.length 
    : 0
  const averageResponseTime = results.length > 0
    ? results.reduce((sum, r) => sum + r.responseTime, 0) / results.length
    : 0
  
  console.log(`🎯 ${model} 模型测试总结:`)
  console.log(`- 总体准确率: ${(averageAccuracy * 100).toFixed(1)}%`)
  console.log(`- 平均响应时间: ${averageResponseTime.toFixed(0)}ms`)
  console.log(`- 成功测试数: ${successfulTests.length}/${results.length}`)
  
  return {
    model,
    results,
    summary: {
      averageAccuracy,
      averageResponseTime,
      successfulTests: successfulTests.length,
      totalTests: results.length
    }
  }
}

export async function compareModelAccuracy() {
  console.log('🔄 开始V3 vs R1准确率对比测试')
  
  const v3Results = await testModelAccuracy('deepseek-chat')
  console.log('\n' + '='.repeat(50) + '\n')
  const r1Results = await testModelAccuracy('deepseek-reasoner')
  
  console.log('\n🏆 最终对比结果:')
  console.table([
    {
      模型: 'DeepSeek V3',
      准确率: `${(v3Results.summary.averageAccuracy * 100).toFixed(1)}%`,
      响应时间: `${v3Results.summary.averageResponseTime.toFixed(0)}ms`,
      成功率: `${v3Results.summary.successfulTests}/${v3Results.summary.totalTests}`
    },
    {
      模型: 'DeepSeek R1',
      准确率: `${(r1Results.summary.averageAccuracy * 100).toFixed(1)}%`,
      响应时间: `${r1Results.summary.averageResponseTime.toFixed(0)}ms`,
      成功率: `${r1Results.summary.successfulTests}/${r1Results.summary.totalTests}`
    }
  ])
  
  return { v3Results, r1Results }
}

// 在浏览器控制台中可以调用
if (typeof window !== 'undefined') {
  (window as any).compareModelAccuracy = compareModelAccuracy
  (window as any).testModelAccuracy = testModelAccuracy
}
