'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON>  } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Play, CheckCircle, AlertTriangle } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'

interface TestResult {
  step: string
  status: 'pending' | 'running' | 'success' | 'error'
  message: string
  data?: unknown
}

export default function TestRewardCalculationPage() {
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<TestResult[]>([])
  const [clientName, setClientName] = useState('测试推荐人')
  const [clientP<PERSON>, setClientPhone] = useState('13800000001')
  const [referredName, setReferredName] = useState('测试被推荐人')
  const [referredPhone, setReferredPhone] = useState('13800000002')
  const [orderAmount, setOrderAmount] = useState('1000')

  const updateResult = (step: string, status: TestResult['status'], message: string, data?: unknown) => {
    setResults(prev => {
      const newResults = [...prev]
      const existingIndex = newResults.findIndex(r => r.step === step)
      if (existingIndex >= 0) {
        newResults[existingIndex] = { step, status, message, data }
      } else {
        newResults.push({ step, status, message, data })
      }
      return newResults
    })
  }

  const testRewardCalculation = async () => {
    setTesting(true)
    setResults([])

    try {
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        throw new Error('无法获取工厂ID')
      }

      // 步骤1：创建推荐人
      updateResult('step1', 'running', '创建推荐人客户...')
      
      const referrerData = {
        factoryId,
        name: clientName,
        phone: clientPhone,
        email: '',
        company: '',
        address: '测试地址',
        status: 'active' as const,
        totalOrders: 0,
        totalAmount: 0,
        referralCount: 0,
        referralReward: 0
      }

      const referrer = await db.createClient(referrerData)
      if (!referrer) {
        updateResult('step1', 'error', '创建推荐人失败')
        return
      }

      updateResult('step1', 'success', `推荐人创建成功: ${referrer.name} (${referrer.id})`)

      // 步骤2：创建被推荐人
      updateResult('step2', 'running', '创建被推荐人客户...')
      
      const referredData = {
        factoryId,
        name: referredName,
        phone: referredPhone,
        email: '',
        company: '',
        address: '测试地址',
        status: 'active' as const,
        totalOrders: 0,
        totalAmount: 0,
        referralCount: 0,
        referralReward: 0,
        referrerId: referrer.id,
        referrerName: referrer.name
      }

      const referred = await db.createClient(referredData)
      if (!referred) {
        updateResult('step2', 'error', '创建被推荐人失败')
        return
      }

      updateResult('step2', 'success', `被推荐人创建成功: ${referred.name} (${referred.id})`)

      // 步骤3：检查推荐人初始奖励
      updateResult('step3', 'running', '检查推荐人初始奖励...')
      
      const initialReferrer = await db.getClientById(referrer.id)
      if (!initialReferrer) {
        updateResult('step3', 'error', '无法获取推荐人信息')
        return
      }

      updateResult('step3', 'success', `推荐人初始奖励: ¥${initialReferrer.referralReward || 0}`)

      // 步骤4：为被推荐人创建订单
      updateResult('step4', 'running', '为被推荐人创建订单...')
      
      const orderData = {
        factoryId,
        clientId: referred.id,
        clientName: referred.name,
        clientPhone: referred.phone,
        orderNumber: `TEST-${Date.now()}`,
        items: [
          {
            productType: 'double_white_outlet',
            productName: '双层白色出风口',
            specifications: '测试规格',
            quantity: 1,
            unitPrice: parseFloat(orderAmount),
            totalPrice: parseFloat(orderAmount)
          }
        ],
        totalAmount: parseFloat(orderAmount),
        status: 'pending',
        projectAddress: '测试地址',
        notes: '推荐奖励测试订单'
        // createdBy 字段会在API中自动设置
      }

      const order = await db.createOrder(orderData)
      if (!order) {
        updateResult('step4', 'error', '创建订单失败')
        return
      }

      updateResult('step4', 'success', `订单创建成功: ${order.orderNumber} (金额: ¥${orderAmount})`)

      // 步骤5：等待并检查推荐人奖励更新
      updateResult('step5', 'running', '检查推荐人奖励是否自动更新...')
      
      // 等待3秒确保自动更新完成
      await new Promise(resolve => setTimeout(resolve, 3000))

      const updatedReferrer = await db.getClientById(referrer.id)
      if (!updatedReferrer) {
        updateResult('step5', 'error', '无法获取更新后的推荐人信息')
        return
      }

      const expectedReward = parseFloat(orderAmount) * 0.02 // 2%
      const actualReward = updatedReferrer.referralReward || 0

      if (actualReward > 0) {
        updateResult('step5', 'success', `✅ 自动奖励计算成功！推荐人奖励: ¥${actualReward} (预期: ¥${expectedReward})`)
      } else {
        updateResult('step5', 'error', `❌ 自动奖励计算失败！推荐人奖励仍为: ¥${actualReward}`)
      }

      // 步骤6：清理测试数据
      updateResult('step6', 'running', '清理测试数据...')
      
      try {
        await db.deleteOrder(order.id)
        await db.deleteClient(referred.id)
        await db.deleteClient(referrer.id)
        updateResult('step6', 'success', '测试数据清理完成')
      } catch (error) {
        updateResult('step6', 'error', `清理测试数据失败: ${error.message}`)
      }

    } catch (error) {
      console.error('❌ 测试推荐奖励计算失败:', error)
      updateResult('error', 'error', `测试失败: ${error.message}`)
    } finally {
      setTesting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">推荐奖励计算测试</h1>
            <p className="text-muted-foreground">测试推荐奖励的自动计算机制</p>
          </div>
        </div>

        {/* 测试参数设置 */}
        <Card>
          <CardHeader>
            <CardTitle>测试参数设置</CardTitle>
            <CardDescription>设置测试用的客户和订单信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="clientName">推荐人姓名</Label>
                <Input
                  id="clientName"
                  value={clientName}
                  onChange={(e) => setClientName(e.target.value)}
                  placeholder="推荐人姓名"
                />
              </div>
              <div>
                <Label htmlFor="clientPhone">推荐人电话</Label>
                <Input
                  id="clientPhone"
                  value={clientPhone}
                  onChange={(e) => setClientPhone(e.target.value)}
                  placeholder="推荐人电话"
                />
              </div>
              <div>
                <Label htmlFor="referredName">被推荐人姓名</Label>
                <Input
                  id="referredName"
                  value={referredName}
                  onChange={(e) => setReferredName(e.target.value)}
                  placeholder="被推荐人姓名"
                />
              </div>
              <div>
                <Label htmlFor="referredPhone">被推荐人电话</Label>
                <Input
                  id="referredPhone"
                  value={referredPhone}
                  onChange={(e) => setReferredPhone(e.target.value)}
                  placeholder="被推荐人电话"
                />
              </div>
              <div>
                <Label htmlFor="orderAmount">订单金额</Label>
                <Input
                  id="orderAmount"
                  value={orderAmount}
                  onChange={(e) => setOrderAmount(e.target.value)}
                  placeholder="订单金额"
                  type="number"
                />
              </div>
              <div className="flex items-end">
                <Button
                  onClick={testRewardCalculation}
                  disabled={testing}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  {testing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      测试中...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      开始测试
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 预期结果 */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>预期结果：</strong> 创建订单后，推荐人的奖励应该自动增加 ¥{(parseFloat(orderAmount || '0') * 0.02).toFixed(2)} (订单金额的2%)
          </AlertDescription>
        </Alert>

        {/* 测试步骤结果 */}
        <div className="space-y-4">
          {results.map((result, index) => (
            <Card key={index} className={
              result.status === 'success' ? 'border-green-200 bg-green-50' :
              result.status === 'error' ? 'border-red-200 bg-red-50' :
              result.status === 'running' ? 'border-blue-200 bg-blue-50' :
              'border-gray-200'
            }>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {result.status === 'success' && <CheckCircle className="h-5 w-5 text-green-600" />}
                    {result.status === 'error' && <AlertTriangle className="h-5 w-5 text-red-600" />}
                    {result.status === 'running' && <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />}
                    {result.status === 'pending' && <div className="h-5 w-5 rounded-full border-2 border-gray-300" />}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">步骤 {index + 1}</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        result.status === 'success' ? 'bg-green-100 text-green-800' :
                        result.status === 'error' ? 'bg-red-100 text-red-800' :
                        result.status === 'running' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {result.status === 'success' ? '成功' :
                         result.status === 'error' ? '失败' :
                         result.status === 'running' ? '进行中' : '等待'}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">{result.message}</div>
                    {result.data && (
                      <div className="text-xs text-gray-500 mt-1 bg-gray-100 p-2 rounded">
                        <pre>{JSON.stringify(result.data, null, 2)}</pre>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 说明文档 */}
        <Card>
          <CardHeader>
            <CardTitle>测试说明</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm">
            <div>
              <strong>测试流程：</strong>
              <ol className="list-decimal list-inside ml-4 space-y-1">
                <li>创建一个推荐人客户</li>
                <li>创建一个被推荐人客户（设置推荐关系）</li>
                <li>检查推荐人的初始奖励（应该为0）</li>
                <li>为被推荐人创建一个订单</li>
                <li>检查推荐人的奖励是否自动更新</li>
                <li>清理测试数据</li>
              </ol>
            </div>
            <div>
              <strong>判断标准：</strong>
              <p>如果步骤5显示推荐人奖励大于0，说明自动奖励机制正常工作。</p>
              <p>如果步骤5显示推荐人奖励仍为0，说明自动奖励机制有问题。</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
