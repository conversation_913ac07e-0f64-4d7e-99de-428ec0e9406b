/**
 * 网络速度测试脚本
 * 在浏览器控制台中运行，测试到 DeepSeek API 的连接速度
 */

async function testNetworkSpeed() {
  console.log('🚀 开始网络速度测试...');
  
  const tests = [
    {
      name: '基础连接测试',
      url: 'https://api.deepseek.com/v1/models',
      method: 'GET',
      headers: {
        'Authorization': 'Bearer sk-test'
      }
    },
    {
      name: '简单API测试',
      url: 'https://api.deepseek.com/v1/chat/completions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-c9047196d51d4f6c99dc94f9fbef602c' // 替换为真实API Key
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: '你好' }],
        max_tokens: 10
      })
    },
    {
      name: '复杂API测试',
      url: 'https://api.deepseek.com/v1/chat/completions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-c9047196d51d4f6c99dc94f9fbef602c' // 替换为真实API Key
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{
          role: 'user',
          content: `识别风口订单，返回JSON。

文本：项目：测试项目
一楼：
出风口 2665×155 白色 1个
回风口 1200×300 白色 1个

规则：
1. 风口类型：
   - 宽度≤254mm → "systemType": "double_white_outlet", "originalType": "出风口"
   - 宽度≥255mm → "systemType": "white_return", "originalType": "回风口"

必须返回完整JSON：
{"projects":[{"floors":[{"rooms":[{"vents":[{"systemType":"double_white_outlet","dimensions":{"length":2665,"width":155,"unit":"mm"}}]}]}]}]}`
        }],
        max_tokens: 1500
      })
    }
  ];

  for (const test of tests) {
    console.log(`\n🧪 测试: ${test.name}`);
    const startTime = performance.now();
    
    try {
      const response = await fetch(test.url, {
        method: test.method,
        headers: test.headers,
        body: test.body
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      console.log(`⏱️ 响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)`);
      console.log(`📊 状态码: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`📦 响应大小: ${JSON.stringify(data).length} 字符`);
        console.log(`✅ 测试成功`);
        
        if (duration > 30000) {
          console.warn(`⚠️ 响应时间过长 (${(duration/1000).toFixed(2)}秒)，可能存在网络问题`);
        } else if (duration > 15000) {
          console.warn(`⚠️ 响应时间较慢 (${(duration/1000).toFixed(2)}秒)`);
        } else {
          console.log(`🚀 响应速度正常`);
        }
      } else {
        const errorText = await response.text();
        console.error(`❌ 请求失败: ${response.status} - ${errorText}`);
      }
      
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.error(`❌ 网络错误 (${duration.toFixed(2)}ms): ${error.message}`);
    }
  }
  
  console.log('\n📋 测试完成！');
  console.log('💡 如果所有测试都很慢（>30秒），可能是：');
  console.log('   1. 网络连接问题');
  console.log('   2. 防火墙或代理设置');
  console.log('   3. DNS解析问题');
  console.log('   4. API服务器响应慢');
}

// 导出函数供控制台使用
window.testNetworkSpeed = testNetworkSpeed;

console.log('🔧 网络速度测试工具已加载');
console.log('💡 在控制台中运行: testNetworkSpeed()');
