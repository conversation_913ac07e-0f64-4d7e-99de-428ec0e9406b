/**
 * 🎁 客户推荐奖励同步API
 * 
 * 修复客户推荐奖励显示不同步的问题
 */

import { NextRequest, NextResponse } from 'next/server'
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { db } from '@/lib/database'
import { calculateProportionalReward } from '@/lib/utils/reward-calculator'
import { safeNumber } from '@/lib/utils/number-utils'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 开始同步客户推荐奖励数据')

    // 从请求体或URL参数获取工厂ID
    const body = await request.json().catch(() => ({}))
    const { searchParams } = new URL(request.url)

    let factoryId = body.factoryId || searchParams.get('factoryId')

    // 如果没有提供工厂ID，尝试从客户端状态获取（可能在服务端不可用）
    if (!factoryId) {
      try {
        factoryId = getCurrentFactoryId()
      } catch (error) {
        console.warn('⚠️ 无法从客户端状态获取工厂ID:', error)
      }
    }

    // 如果还是没有工厂ID，使用默认值（用于测试）
    if (!factoryId) {
      factoryId = 'cmbx0onx0000gun48lje6r02s' // 默认工厂ID
      console.warn('⚠️ 使用默认工厂ID:', factoryId)
    }

    console.log('🏭 工厂ID:', factoryId)

    // 获取所有客户
    const clients = await db.getClientsByFactoryId(factoryId)
    console.log(`📋 找到 ${clients.length} 个客户`)

    // 获取所有订单
    const orders = await db.getOrdersByFactoryId(factoryId)
    console.log(`📦 找到 ${orders.length} 个订单`)

    let updatedCount = 0
    let errorCount = 0

    // 为每个客户同步奖励数据
    for (const client of clients) {
      try {
        console.log(`🔄 处理客户: ${client.name}`)

        // 计算客户的订单统计
        const clientOrders = orders.filter(order => order.clientId === client.id)
        const totalOrders = clientOrders.length
        const totalAmount = clientOrders.reduce((sum, order) => sum + safeNumber(order.totalAmount), 0)
        const paidAmount = clientOrders.reduce((sum, order) => sum + safeNumber(order.paidAmount), 0)
        const unpaidAmount = Math.max(0, totalAmount - paidAmount)

        // 获取最后订单日期
        const lastOrderDate = clientOrders.length > 0 
          ? new Date(Math.max(...clientOrders.map(order => new Date(order.createdAt).getTime())))
          : null

        // 计算推荐数量
        const referralCount = clients.filter(c => c.referrerId === client.id).length

        // 计算推荐奖励（如果该客户是推荐人）
        let rewardData = {
          totalReward: 0,
          availableReward: 0,
          pendingReward: 0
        }

        if (referralCount > 0) {
          try {
            const rewardResult = await calculateProportionalReward(client.id, factoryId)
            rewardData = {
              totalReward: rewardResult.totalReward,
              availableReward: rewardResult.availableReward,
              pendingReward: rewardResult.pendingReward
            }
            console.log(`💰 客户 ${client.name} 推荐奖励:`, rewardData)
          } catch (rewardError) {
            console.warn(`⚠️ 计算客户 ${client.name} 推荐奖励失败:`, rewardError)
          }
        }

        // 更新客户数据
        await db.updateClient(client.id, {
          // 订单统计
          totalOrders,
          totalAmount,
          paidAmount,
          unpaidAmount,
          lastOrderDate,
          
          // 推荐统计
          referralCount,
          referralReward: rewardData.totalReward,
          availableReward: rewardData.availableReward,
          pendingReward: rewardData.pendingReward
        })

        console.log(`✅ 客户 ${client.name} 数据同步完成`)
        updatedCount++

      } catch (clientError) {
        console.error(`❌ 处理客户 ${client.name} 失败:`, clientError)
        errorCount++
      }
    }

    console.log('🎉 客户奖励数据同步完成:', {
      总客户数: clients.length,
      成功更新: updatedCount,
      失败数量: errorCount
    })

    return NextResponse.json({
      success: true,
      message: '客户奖励数据同步完成',
      data: {
        totalClients: clients.length,
        updatedCount,
        errorCount
      }
    })

  } catch (error) {
    console.error('❌ 同步客户奖励数据失败:', error)
    return NextResponse.json({
      success: false,
      error: `同步失败: ${error.message}`
    }, { status: 500 })
  }
}
