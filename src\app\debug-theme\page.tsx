/**
 * 🇨🇳 风口云平台 - 主题调试页面
 */

'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function DebugThemePage() {
  const [htmlClass, setHtmlClass] = useState('')
  const [bodyStyle, setBodyStyle] = useState('')
  const [localStorage, setLocalStorage] = useState('')

  useEffect(() => {
    const updateDebugInfo = () => {
      if (typeof window !== 'undefined') {
        setHtmlClass(document.documentElement.className)
        setBodyStyle(window.getComputedStyle(document.body).backgroundColor + ' / ' + window.getComputedStyle(document.body).color)
        setLocalStorage(window.localStorage.getItem('theme-storage') || '无')
      }
    }

    updateDebugInfo()
    const interval = setInterval(updateDebugInfo, 1000)
    return () => clearInterval(interval)
  }, [])

  const toggleDarkClass = () => {
    if (typeof window !== 'undefined') {
      if (document.documentElement.classList.contains('dark')) {
        document.documentElement.classList.remove('dark')
        console.log('🌅 移除dark类')
      } else {
        document.documentElement.classList.add('dark')
        console.log('🌙 添加dark类')
      }
    }
  }

  const clearStorage = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('theme-storage')
      window.location.reload()
    }
  }

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold">🔧 主题调试页面</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 当前状态 */}
          <Card>
            <CardHeader>
              <CardTitle>当前状态</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="font-medium">HTML类名:</p>
                <code className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded block">
                  {htmlClass || '无'}
                </code>
              </div>
              
              <div>
                <p className="font-medium">Body样式:</p>
                <code className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded block">
                  {bodyStyle}
                </code>
              </div>
              
              <div>
                <p className="font-medium">LocalStorage:</p>
                <code className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded block">
                  {localStorage}
                </code>
              </div>
            </CardContent>
          </Card>

          {/* 测试按钮 */}
          <Card>
            <CardHeader>
              <CardTitle>测试操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={toggleDarkClass} className="w-full">
                切换dark类
              </Button>
              
              <Button onClick={clearStorage} variant="outline" className="w-full">
                清除存储并刷新
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 颜色测试 */}
        <Card>
          <CardHeader>
            <CardTitle>颜色测试</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-4 bg-background border rounded">
                <p className="text-foreground">前景色文字</p>
                <p className="text-muted-foreground">静音前景色</p>
              </div>
              
              <div className="p-4 bg-card border rounded">
                <p className="text-card-foreground">卡片文字</p>
                <p className="text-sm text-muted-foreground">卡片描述</p>
              </div>
              
              <div className="p-4 bg-muted border rounded">
                <p className="text-muted-foreground">静音背景</p>
              </div>
              
              <div className="p-4 bg-accent border rounded">
                <p className="text-accent-foreground">强调背景</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 硬编码颜色测试 */}
        <Card>
          <CardHeader>
            <CardTitle>硬编码颜色测试</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-gray-900">text-gray-900 (应该在深色主题下变亮)</p>
              <p className="text-gray-700">text-gray-700 (应该在深色主题下变亮)</p>
              <p className="text-gray-600">text-gray-600 (应该在深色主题下变亮)</p>
              <p className="text-gray-500">text-gray-500 (应该在深色主题下变亮)</p>
              <div className="p-4 bg-white border rounded">
                <p>bg-white (应该在深色主题下变暗)</p>
              </div>
              <div className="p-4 bg-gray-50 border rounded">
                <p>bg-gray-50 (应该在深色主题下变暗)</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
