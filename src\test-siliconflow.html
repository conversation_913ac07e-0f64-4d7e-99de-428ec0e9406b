<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硅基流动 API 测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        textarea {
            height: 120px;
            resize: vertical;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #1976D2;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #4CAF50; background: #e8f5e8; }
        .error { border-color: #f44336; background: #ffe8e8; }
        .info { border-color: #2196F3; background: #e3f2fd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 硅基流动 API 测试工具</h1>
        <p>测试硅基流动的 DeepSeek V3 模型，验证速度和准确性。</p>
        
        <div class="input-group">
            <label for="apiKey">硅基流动 API Key:</label>
            <input type="password" id="apiKey" value="sk-szczomdkrprhzlzuzwlblenwfvvuuuyxbxnjgmrcetorftth" />
            <small>已预填充提供的API Key</small>
        </div>
        
        <div class="input-group">
            <label for="testText">测试文本:</label>
            <textarea id="testText" placeholder="项目：测试项目
一楼：
出风口 2665×155 白色 1个
回风口 1200×300 白色 1个">项目：测试项目
一楼：
出风口 2665×155 白色 1个
回风口 1200×300 白色 1个</textarea>
        </div>
        
        <button class="button" onclick="testSiliconFlowAPI()">🧪 开始测试</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function testSiliconFlowAPI() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const testText = document.getElementById('testText').value.trim();
            
            if (!apiKey) {
                addResult('❌ 错误', '请输入API Key', 'error');
                return;
            }
            
            if (!testText) {
                addResult('❌ 错误', '请输入测试文本', 'error');
                return;
            }
            
            addResult('🚀 开始测试', '正在连接硅基流动API...', 'info');
            
            const startTime = performance.now();
            
            const prompt = `识别风口订单，返回JSON。

文本：${testText}

规则：
1. 风口类型（严格按尺寸判断）：
   - 宽度≤254mm → "systemType": "double_white_outlet", "originalType": "出风口"
   - 宽度≥255mm → "systemType": "white_return", "originalType": "回风口"

2. 尺寸：数字<100为厘米需×10，大值为length，小值为width

3. 房间：提取具体房间名（大厅、办公室、餐厅等），无明确房间用"房间"

必须返回完整JSON：
{
  "projects": [{
    "projectName": "",
    "clientInfo": "",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          }
        ]
      }]
    }]
  }]
}`;

            const requestBody = {
                model: 'deepseek-ai/DeepSeek-V3',
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 1500,
                temperature: 0.1,
                stream: false
            };

            try {
                const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify(requestBody)
                });

                const endTime = performance.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0]?.message?.content;
                    
                    addResult(
                        '✅ 测试成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
模型: ${data.model}
Token使用: ${data.usage?.total_tokens || 'N/A'}
输入Token: ${data.usage?.prompt_tokens || 'N/A'}
输出Token: ${data.usage?.completion_tokens || 'N/A'}

AI响应内容:
${content}`,
                        'success'
                    );
                    
                    // 尝试解析JSON
                    try {
                        let jsonStr = content.trim();
                        jsonStr = jsonStr.replace(/^```json\s*/i, '');
                        jsonStr = jsonStr.replace(/\s*```\s*$/, '');
                        
                        const firstBrace = jsonStr.indexOf('{');
                        const lastBrace = jsonStr.lastIndexOf('}');
                        
                        if (firstBrace !== -1 && lastBrace !== -1) {
                            jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
                        }
                        
                        const parsed = JSON.parse(jsonStr);
                        
                        addResult(
                            '🎯 JSON解析成功',
                            `解析后的数据结构:
${JSON.stringify(parsed, null, 2)}`,
                            'success'
                        );
                        
                        // 验证风口类型
                        const vents = parsed.projects?.[0]?.floors?.[0]?.rooms?.[0]?.vents || [];
                        let validationResults = [];
                        
                        vents.forEach((vent, index) => {
                            const width = vent.dimensions?.width || 0;
                            const systemType = vent.systemType;
                            const expectedType = width >= 255 ? 'white_return' : 'double_white_outlet';
                            const isCorrect = systemType === expectedType;
                            
                            validationResults.push(
                                `风口${index + 1}: ${vent.dimensions?.length}×${width}mm → ${systemType} ${isCorrect ? '✅' : '❌'}`
                            );
                        });
                        
                        addResult(
                            '🔍 风口类型验证',
                            validationResults.join('\n'),
                            validationResults.every(r => r.includes('✅')) ? 'success' : 'error'
                        );
                        
                    } catch (parseError) {
                        addResult(
                            '❌ JSON解析失败',
                            `解析错误: ${parseError.message}
原始内容: ${content}`,
                            'error'
                        );
                    }
                    
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ API请求失败',
                        `状态码: ${response.status}
响应时间: ${duration.toFixed(2)}ms
错误信息: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addResult(
                    '❌ 网络错误',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${timestamp}] ${title}\n${content}`;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
