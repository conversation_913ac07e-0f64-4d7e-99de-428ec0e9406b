'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Loader2, History, Eye, Download } from 'lucide-react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { api } from '@/lib/api/client'
import type { RewardUsage } from '@/types'

interface RewardUsageHistoryProps {
  clientId?: string
  factoryId?: string
  showClientInfo?: boolean
}

interface RewardUsageRecord extends Omit<RewardUsage, 'amount'> {
  amount: number
  clientName?: string
  clientPhone?: string
  relatedOrderNumber?: string
}

export function RewardUsageHistory({ clientId, factoryId, showClientInfo = false }: RewardUsageHistoryProps) {
  const [records, setRecords] = useState<RewardUsageRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<RewardUsageRecord | null>(null)
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)

  // 加载奖励使用记录
  const loadRecords = async () => {
    try {
      setLoading(true)

      const params = new URLSearchParams()
      if (clientId) {
        params.append('clientId', clientId)
      }
      if (factoryId) {
        params.append('factoryId', factoryId)
      }

      console.log('🎁 开始获取奖励使用记录，参数:', { clientId, factoryId })

      const response = await api.get(`/api/reward-usage?${params}`)

      if (response.success) {
        setRecords(response.data?.rewardUsages || [])
        console.log('✅ 奖励使用记录获取成功:', response.data?.rewardUsages?.length || 0, '条')
      } else {
        console.error('❌ 获取奖励使用记录失败:', response.error)
      }
    } catch (error) {
      console.error('❌ 获取奖励使用记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRecords()
  }, [clientId, factoryId])

  // 格式化金额 - 使用一位小数
  const formatAmount = (amount: number) => `¥${amount.toFixed(1)}`

  // 格式化日期
  const formatDate = (date: Date | string) => {
    const d = new Date(date)
    return d.toLocaleString('zh-CN')
  }

  // 获取使用方式标签
  const getUsageTypeLabel = (type: string) => {
    const labels = {
      'CASH_OUT': '现金兑现',
      'ORDER_DISCOUNT': '抵扣货款',
      'TRANSFER': '转账兑现',
      'OTHER': '其他方式'
    }
    return labels[type as keyof typeof labels] || type
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'pending': { label: '待处理', variant: 'secondary' as const },
      'approved': { label: '已批准', variant: 'default' as const },
      'completed': { label: '已完成', variant: 'default' as const },
      'rejected': { label: '已拒绝', variant: 'destructive' as const },
      'cancelled': { label: '已取消', variant: 'outline' as const }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'outline' as const }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // 查看详情
  const viewDetail = (record: RewardUsageRecord) => {
    setSelectedRecord(record)
    setDetailDialogOpen(true)
  }

  // 导出记录
  const exportRecords = () => {
    // 这里可以实现导出功能
    console.log('导出奖励使用记录')
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>加载奖励使用记录...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <History className="mr-2 h-5 w-5" />
              奖励使用记录
            </CardTitle>
            {records.length > 0 && (
              <Button variant="outline" size="sm" onClick={exportRecords}>
                <Download className="mr-2 h-4 w-4" />
                导出
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {records.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无奖励使用记录
            </div>
          ) : (
            <div className="space-y-4">
              {records.map((record) => (
                <div key={record.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{getUsageTypeLabel(record.usageType)}</span>
                      {getStatusBadge(record.status)}
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-lg text-red-600">
                        -{formatAmount(record.amount)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatDate(record.createdAt)}
                      </div>
                    </div>
                  </div>

                  {showClientInfo && record.clientName && (
                    <div className="text-sm text-gray-600 mb-2">
                      客户: {record.clientName} ({record.clientPhone})
                    </div>
                  )}

                  {record.description && (
                    <div className="text-sm text-gray-600 mb-2">
                      说明: {record.description}
                    </div>
                  )}

                  {record.relatedOrderNumber && (
                    <div className="text-sm text-gray-600 mb-2">
                      关联订单: {record.relatedOrderNumber}
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500">
                      {record.paymentMethod && `兑现方式: ${record.paymentMethod}`}
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => viewDetail(record)}>
                      <Eye className="mr-1 h-3 w-3" />
                      详情
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 详情对话框 */}
      <Dialog open={detailDialogOpen} onOpenChange={setDetailDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>奖励使用详情</DialogTitle>
            <DialogDescription>
              查看奖励使用记录的详细信息，包括使用方式、金额、状态等。
            </DialogDescription>
          </DialogHeader>
          
          {selectedRecord && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">使用方式</label>
                  <div>{getUsageTypeLabel(selectedRecord.usageType)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">使用金额</label>
                  <div className="font-bold text-red-600">{formatAmount(selectedRecord.amount)}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">状态</label>
                  <div>{getStatusBadge(selectedRecord.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">使用时间</label>
                  <div className="text-sm">{formatDate(selectedRecord.createdAt)}</div>
                </div>
              </div>

              {selectedRecord.clientName && (
                <div>
                  <label className="text-sm font-medium text-gray-500">客户信息</label>
                  <div>{selectedRecord.clientName} ({selectedRecord.clientPhone})</div>
                </div>
              )}

              {selectedRecord.description && (
                <div>
                  <label className="text-sm font-medium text-gray-500">使用说明</label>
                  <div>{selectedRecord.description}</div>
                </div>
              )}

              {selectedRecord.relatedOrderNumber && (
                <div>
                  <label className="text-sm font-medium text-gray-500">关联订单</label>
                  <div>{selectedRecord.relatedOrderNumber}</div>
                </div>
              )}

              {selectedRecord.paymentMethod && (
                <div>
                  <label className="text-sm font-medium text-gray-500">兑现方式</label>
                  <div>{selectedRecord.paymentMethod}</div>
                </div>
              )}

              {selectedRecord.paymentReference && (
                <div>
                  <label className="text-sm font-medium text-gray-500">支付凭证</label>
                  <div>{selectedRecord.paymentReference}</div>
                </div>
              )}

              {selectedRecord.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">备注</label>
                  <div className="text-sm text-gray-600">{selectedRecord.notes}</div>
                </div>
              )}

              {selectedRecord.approvedAt && (
                <div>
                  <label className="text-sm font-medium text-gray-500">批准时间</label>
                  <div className="text-sm">{formatDate(selectedRecord.approvedAt)}</div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
