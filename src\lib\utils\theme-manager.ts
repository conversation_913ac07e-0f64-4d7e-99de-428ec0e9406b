/**
 * 🇨🇳 风口云平台 - 用户主题管理工具
 * 
 * 功能说明：
 * - 管理每个用户独立的主题设置
 * - 处理用户切换时的主题迁移
 * - 清理旧的全局主题数据
 * - 确保主题设置不会在用户间污染
 */

import type { ThemeMode } from '@/lib/store/theme'

/**
 * 获取用户特定的主题存储键
 */
export function getUserThemeKey(): string {
  if (typeof window === 'undefined') return 'default'

  try {
    // 动态导入避免循环依赖
    const authData = localStorage.getItem('auth-storage')
    if (authData) {
      const parsed = JSON.parse(authData)
      const { user, role, factoryId } = parsed.state || {}

      if (user && user.id) {
        // 使用用户ID + 角色 + 工厂ID（如果有）作为唯一标识
        const userKey = `${user.id}_${role}`
        return factoryId ? `${userKey}_${factoryId}` : userKey
      }
    }
  } catch (error) {
    console.warn('⚠️ 获取用户主题键失败:', error)
  }

  // 如果无法获取用户信息，使用默认键（兼容未登录状态）
  return 'default'
}

/**
 * 获取用户的主题设置
 */
export function getUserThemeSettings(userKey?: string): { mode: ThemeMode } | null {
  if (typeof window === 'undefined') return null

  try {
    const key = userKey || getUserThemeKey()
    const storageKey = `theme-storage-${key}`
    const stored = localStorage.getItem(storageKey)

    if (stored) {
      // 检查是否是有效的JSON字符串
      if (stored.startsWith('{') || stored.startsWith('[')) {
        const parsed = JSON.parse(stored)
        return parsed.state || parsed || null
      } else {
        // 如果不是JSON格式，可能是旧的简单字符串格式
        console.warn('⚠️ 发现旧格式的主题设置，将被忽略:', stored)
        localStorage.removeItem(storageKey)
        return null
      }
    }
  } catch (error) {
    console.warn('⚠️ 获取用户主题设置失败:', error)
    // 清理损坏的数据
    try {
      const key = userKey || getUserThemeKey()
      const storageKey = `theme-storage-${key}`
      localStorage.removeItem(storageKey)
    } catch (cleanupError) {
      console.warn('⚠️ 清理损坏的主题数据失败:', cleanupError)
    }
  }

  return null
}

/**
 * 保存用户的主题设置
 */
export function saveUserThemeSettings(mode: ThemeMode, userKey?: string): void {
  if (typeof window === 'undefined') return
  
  try {
    const key = userKey || getUserThemeKey()
    const storageKey = `theme-storage-${key}`
    
    const themeData = {
      state: {
        mode,
        smartThemeSettings: {
          lightStartHour: 6,
          darkStartHour: 18
        }
      },
      version: 0
    }
    
    localStorage.setItem(storageKey, JSON.stringify(themeData))
    console.log('✅ 用户主题设置已保存:', { userKey: key, mode })
  } catch (error) {
    console.error('❌ 保存用户主题设置失败:', error)
  }
}

/**
 * 迁移旧的全局主题设置到用户特定设置
 */
export function migrateGlobalThemeToUser(): void {
  if (typeof window === 'undefined') return
  
  try {
    // 检查是否有旧的全局主题设置
    const oldThemeStorage = localStorage.getItem('theme-storage')
    if (!oldThemeStorage) return
    
    const userKey = getUserThemeKey()
    if (userKey === 'default') return // 未登录用户不需要迁移
    
    // 检查用户是否已有主题设置
    const userThemeSettings = getUserThemeSettings(userKey)
    if (userThemeSettings) return // 用户已有设置，不需要迁移
    
    // 解析旧的主题设置
    const oldTheme = JSON.parse(oldThemeStorage)
    const oldMode = oldTheme.state?.mode || 'light'
    
    // 保存到用户特定的存储
    saveUserThemeSettings(oldMode, userKey)
    
    console.log('✅ 已迁移全局主题设置到用户:', { userKey, mode: oldMode })
  } catch (error) {
    console.warn('⚠️ 迁移主题设置失败:', error)
  }
}

/**
 * 清理旧的全局主题数据
 */
export function cleanupGlobalThemeData(): void {
  if (typeof window === 'undefined') return
  
  try {
    // 移除旧的全局主题存储
    localStorage.removeItem('theme-storage')
    console.log('🧹 已清理旧的全局主题数据')
  } catch (error) {
    console.warn('⚠️ 清理全局主题数据失败:', error)
  }
}

/**
 * 切换用户时重新加载主题
 */
export function reloadThemeForUser(): void {
  if (typeof window === 'undefined') return

  try {
    const userKey = getUserThemeKey()
    const userThemeSettings = getUserThemeSettings(userKey)

    if (userThemeSettings) {
      // 直接应用主题到DOM，不依赖useThemeStore
      applyThemeToDOM(userThemeSettings.mode)
      console.log('✅ 已加载用户主题设置:', { userKey, mode: userThemeSettings.mode })
    } else {
      // 用户没有主题设置，使用默认浅色主题
      applyThemeToDOM('light')
      console.log('✅ 用户无主题设置，使用默认浅色主题:', { userKey })
    }
  } catch (error) {
    console.error('❌ 重新加载用户主题失败:', error)
  }
}

/**
 * 直接应用主题到DOM
 */
function applyThemeToDOM(mode: ThemeMode): void {
  if (typeof window === 'undefined') return

  try {
    const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    const isDark = mode === 'dark' || (mode === 'auto' && isSystemDark)

    if (isDark) {
      document.documentElement.classList.add('dark')
      document.body.classList.add('admin-dark')
    } else {
      document.documentElement.classList.remove('dark')
      document.body.classList.remove('admin-dark')
    }

    console.log('🎨 主题已应用到DOM:', isDark ? '深色模式' : '浅色模式')
  } catch (error) {
    console.error('❌ 应用主题到DOM失败:', error)
  }
}

/**
 * 清理指定用户的主题数据
 */
export function clearUserThemeData(userKey?: string): void {
  if (typeof window === 'undefined') return
  
  try {
    const key = userKey || getUserThemeKey()
    const storageKey = `theme-storage-${key}`
    localStorage.removeItem(storageKey)
    console.log('🧹 已清理用户主题数据:', { userKey: key })
  } catch (error) {
    console.warn('⚠️ 清理用户主题数据失败:', error)
  }
}

/**
 * 获取所有用户的主题存储键（用于调试）
 */
export function getAllUserThemeKeys(): string[] {
  if (typeof window === 'undefined') return []
  
  try {
    const keys: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('theme-storage-')) {
        keys.push(key)
      }
    }
    return keys
  } catch (error) {
    console.warn('⚠️ 获取主题存储键失败:', error)
    return []
  }
}

/**
 * 主题管理器类
 */
export class ThemeManager {
  /**
   * 初始化用户主题
   */
  static initializeUserTheme(): void {
    // 迁移旧的全局主题设置
    migrateGlobalThemeToUser()
    
    // 重新加载当前用户的主题
    reloadThemeForUser()
    
    // 清理旧的全局主题数据
    cleanupGlobalThemeData()
  }
  
  /**
   * 用户登录时的主题处理
   */
  static onUserLogin(): void {
    // 延迟执行，确保认证状态已更新
    setTimeout(() => {
      this.initializeUserTheme()
    }, 100)
  }

  /**
   * 用户登出时的主题处理
   */
  static onUserLogout(): void {
    // 重置为默认主题
    if (typeof window !== 'undefined') {
      document.documentElement.classList.remove('dark')
      document.body.classList.remove('admin-dark')
    }
    console.log('✅ 用户登出，已重置为默认主题')
  }
  
  /**
   * 用户切换时的主题处理
   */
  static onUserSwitch(): void {
    this.initializeUserTheme()
  }
}
