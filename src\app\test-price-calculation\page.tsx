/**
 * 🇨🇳 风口云平台 - 价格计算测试页面
 */

'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

// 复制价格计算逻辑进行测试
const calculateVentPrice = (params: {
  type: string
  length: number
  width: number
  quantity: number
  unitPrice: number
}): number => {
  const { type, length, width, quantity, unitPrice } = params

  console.log(`🔧 [价格计算测试] 输入参数:`, params)

  if (unitPrice <= 0) {
    console.log(`❌ [价格计算测试] 单价无效: ${unitPrice}`)
    return 0
  }

  let calculatedPrice = 0

  // 常规风口 (元/㎡)：(长+60) × (宽+60) ÷ 1,000,000 × 单价 × 数量
  const regularVentTypes = [
    'double_white_outlet', 'double_black_outlet', 'white_return', 'black_return',
    'white_linear', 'black_linear', 'white_linear_return', 'black_linear_return', 'maintenance'
  ]

  if (regularVentTypes.includes(type)) {
    if (length > 0 && width > 0) {
      const area = (length + 60) * (width + 60) / 1000000
      calculatedPrice = area * unitPrice * quantity
      console.log(`✅ [价格计算测试] 常规风口: 面积=${area.toFixed(6)}㎡, 计算=${area} × ${unitPrice} × ${quantity} = ${calculatedPrice}`)
    } else {
      console.log(`❌ [价格计算测试] 常规风口尺寸无效: ${length}×${width}`)
    }
  } else {
    // 高端风口 (元/m)：(长+120) ÷ 1,000 × 单价 × 数量
    if (length > 0) {
      const lengthInM = (length + 120) / 1000
      calculatedPrice = lengthInM * unitPrice * quantity
      console.log(`✅ [价格计算测试] 高端风口: 长度=${lengthInM.toFixed(3)}m, 计算=${lengthInM} × ${unitPrice} × ${quantity} = ${calculatedPrice}`)
    } else {
      console.log(`❌ [价格计算测试] 高端风口长度无效: ${length}`)
    }
  }

  // 🔧 精确到一位小数，四舍五入
  const finalPrice = Math.round(calculatedPrice * 10) / 10
  console.log(`🔧 [价格计算测试] 最终价格: ${calculatedPrice} → ${finalPrice}`)
  return finalPrice
}

export default function TestPriceCalculationPage() {
  const [testParams, setTestParams] = useState({
    type: 'double_white_outlet',
    length: 1600,
    width: 200,
    quantity: 1,
    unitPrice: 130
  })
  const [result, setResult] = useState<number | null>(null)

  const runTest = () => {
    console.log('🚀 开始价格计算测试...')
    const calculatedPrice = calculateVentPrice(testParams)
    setResult(calculatedPrice)
  }

  const testCases = [
    {
      name: '出风口 1600×200',
      params: { type: 'double_white_outlet', length: 1600, width: 200, quantity: 1, unitPrice: 130 }
    },
    {
      name: '回风口 2640×300',
      params: { type: 'white_return', length: 2640, width: 300, quantity: 1, unitPrice: 130 }
    },
    {
      name: '高端风口 2000×150',
      params: { type: 'high_end_white_outlet', length: 2000, width: 150, quantity: 1, unitPrice: 80 }
    },
    {
      name: '多数量测试 1000×300×2个',
      params: { type: 'white_return', length: 1000, width: 300, quantity: 2, unitPrice: 130 }
    }
  ]

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-center">🧮 价格计算测试页面</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 手动测试 */}
          <Card>
            <CardHeader>
              <CardTitle>手动测试</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">风口类型</label>
                  <select
                    value={testParams.type}
                    onChange={(e) => setTestParams({...testParams, type: e.target.value})}
                    className="w-full p-2 border rounded"
                  >
                    <option value="double_white_outlet">双层白色出风口</option>
                    <option value="white_return">白色回风口</option>
                    <option value="high_end_white_outlet">高端白色出风口</option>
                  </select>
                </div>
                
                <div>
                  <label className="text-sm font-medium">长度 (mm)</label>
                  <Input
                    type="number"
                    value={testParams.length}
                    onChange={(e) => setTestParams({...testParams, length: Number(e.target.value)})}
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">宽度 (mm)</label>
                  <Input
                    type="number"
                    value={testParams.width}
                    onChange={(e) => setTestParams({...testParams, width: Number(e.target.value)})}
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">数量</label>
                  <Input
                    type="number"
                    value={testParams.quantity}
                    onChange={(e) => setTestParams({...testParams, quantity: Number(e.target.value)})}
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">单价 (元)</label>
                  <Input
                    type="number"
                    value={testParams.unitPrice}
                    onChange={(e) => setTestParams({...testParams, unitPrice: Number(e.target.value)})}
                  />
                </div>
              </div>
              
              <Button onClick={runTest} className="w-full">
                🧮 计算价格
              </Button>
              
              {result !== null && (
                <div className="p-4 bg-green-50 border border-green-200 rounded">
                  <p className="text-green-700 font-bold text-lg">
                    计算结果: ¥{result.toFixed(1)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 预设测试用例 */}
          <Card>
            <CardHeader>
              <CardTitle>预设测试用例</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {testCases.map((testCase, index) => (
                <div key={index} className="border rounded p-3">
                  <h4 className="font-medium">{testCase.name}</h4>
                  <p className="text-sm text-gray-600">
                    类型: {testCase.params.type}<br/>
                    尺寸: {testCase.params.length}×{testCase.params.width}mm<br/>
                    数量: {testCase.params.quantity}, 单价: ¥{testCase.params.unitPrice}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setTestParams(testCase.params)
                      const price = calculateVentPrice(testCase.params)
                      setResult(price)
                    }}
                    className="mt-2"
                  >
                    测试此用例
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* 计算公式说明 */}
        <Card>
          <CardHeader>
            <CardTitle>计算公式说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <strong>常规风口 (元/㎡):</strong><br/>
                总价 = (长度+60) × (宽度+60) ÷ 1,000,000 × 单价 × 数量
              </div>
              <div>
                <strong>高端风口 (元/m):</strong><br/>
                总价 = (长度+120) ÷ 1,000 × 单价 × 数量
              </div>
              <div className="text-gray-600">
                * 常规风口包括: double_white_outlet, white_return, black_return 等<br/>
                * 高端风口包括: high_end_white_outlet, arrow_outlet, claw_outlet 等
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
