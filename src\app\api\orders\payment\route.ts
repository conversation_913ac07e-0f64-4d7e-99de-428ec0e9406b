/**
 * 🇨🇳 风口云平台 - 订单付款API
 * 
 * 处理订单付款状态更新
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database/index'

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { orderId, paymentStatus, paidAmount, dueDate, paymentNotes } = body

    console.log('📞 收到订单付款更新请求:', {
      orderId,
      paymentStatus,
      paidAmount,
      dueDate,
      paymentNotes
    })

    // 验证必需参数
    if (!orderId) {
      return NextResponse.json({
        success: false,
        error: '订单ID不能为空'
      }, { status: 400 })
    }

    // 调用数据库服务更新订单付款信息
    const success = await db.updateOrderPayment(orderId, {
      paymentStatus,
      paidAmount,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      paymentNotes
    })

    if (success) {
      console.log('✅ 订单付款信息更新成功')
      return NextResponse.json({
        success: true,
        message: '订单付款信息更新成功'
      })
    } else {
      console.error('❌ 订单付款信息更新失败')
      return NextResponse.json({
        success: false,
        error: '更新失败，请重试'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('❌ 订单付款API错误:', error)
    return NextResponse.json({
      success: false,
      error: `服务器错误: ${error instanceof Error ? error.message : String(error)}`
    }, { status: 500 })
  }
}
