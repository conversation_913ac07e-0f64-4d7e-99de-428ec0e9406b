/**
 * 增强的OCR解析器
 * 基于真实使用反馈优化的识别算法
 */

import { DIMENSION_SEPARATORS } from './utils/dimension-utils'

// 尺寸解析结果接口
interface DimensionResult {
  length: number  // 长度(mm)
  width: number   // 宽度(mm)
  originalText: string  // 原始文本
  detectedUnit: string  // 检测到的单位
  confidence: number    // 置信度
}

// 备注清理结果接口
interface NotesResult {
  cleanedNotes: string  // 清理后的备注
  removedItems: string[]  // 被移除的内容
  confidence: number      // 清理质量评分
}

export class EnhancedOCRParser {
  
  // 智能尺寸解析
  static parseDimensionsEnhanced(text: string): DimensionResult | null {
    if (!text || typeof text !== 'string') {
      return null
    }
    
    console.log(`🔍 开始解析尺寸: "${text}"`)
    
    // 预处理：统一格式
    let cleanText = text.toLowerCase()

    // 使用统一的分隔符映射进行替换
    for (const [from, to] of Object.entries(DIMENSION_SEPARATORS.NORMALIZE_MAP)) {
      cleanText = cleanText.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to)
    }

    // 移除空格
    cleanText = cleanText.replace(/\s+/g, '')
    
    // 尺寸匹配模式（支持各种单位和分隔符）
    const dimensionPatterns = [
      // 等号格式：数字+单位x数字=xxx (如：26米x135=大厅)
      new RegExp(`(\\d+(?:\\.\\d+)?)\\s*(mm|cm|m|毫米|厘米|米)\\s*${DIMENSION_SEPARATORS.REGEX_CLASS}\\s*(\\d+(?:\\.\\d+)?)\\s*=.*?`, 'i'),
      // 标准格式：数字x数字+单位
      new RegExp(`(\\d+(?:\\.\\d+)?)\\s*${DIMENSION_SEPARATORS.REGEX_CLASS}\\s*(\\d+(?:\\.\\d+)?)\\s*(mm|cm|m|毫米|厘米|米)`, 'i'),
      // 中文格式：数字乘数字
      /(\d+(?:\.\d+)?)\s*乘\s*(\d+(?:\.\d+)?)\s*(mm|cm|m|毫米|厘米|米)?/i,
      // 简单格式：数字x数字（无单位）
      new RegExp(`(\\d+(?:\\.\\d+)?)\\s*${DIMENSION_SEPARATORS.REGEX_CLASS}\\s*(\\d+(?:\\.\\d+)?)`, 'i')
    ]
    
    for (const pattern of dimensionPatterns) {
      const match = cleanText.match(pattern)
      if (match) {
        let num1, num2, unit

        // 处理不同的匹配模式
        if (match[2] && (match[2] === 'mm' || match[2] === 'cm' || match[2] === 'm' || match[2] === '毫米' || match[2] === '厘米' || match[2] === '米')) {
          // 等号格式：26米x135=大厅
          num1 = parseFloat(match[1])
          num2 = parseFloat(match[3])
          unit = match[2]
        } else {
          // 标准格式：1920x140mm 或 1920x140
          num1 = parseFloat(match[1])
          num2 = parseFloat(match[2])
          unit = match[3] || this.inferUnit(num1, num2)
        }

        console.log(`✅ 匹配成功: ${num1} x ${num2} ${unit}`)

        // 转换为毫米
        const { length, width } = this.convertToMillimeters(num1, num2, unit)

        return {
          length,
          width,
          originalText: text,
          detectedUnit: unit,
          confidence: this.calculateDimensionConfidence(match, unit)
        }
      }
    }
    
    console.log(`❌ 尺寸解析失败: "${text}"`)
    return null
  }
  
  // 智能单位推断 - 根据用户明确的规则
  // 正确规则：
  // 1. 个位数带小数点（如1.6, 2.75, 9.5） → 米(m)
  // 2. 两位数带小数点（如12.5, 24.8, 99.9） → 厘米(cm)
  // 3. 三位数以上整数（如100, 150, 1700） → 毫米(mm)
  // 4. 小于100的整数 → 根据大小判断（≥50为mm，<50为cm）
  private static inferUnit(num1: number, num2: number): string {
    console.log(`🔍 OCR单位推断: ${num1} x ${num2}`)

    // 应用正确的规则进行单位推断
    const numbers = [num1, num2]

    for (const num of numbers) {
      const hasDecimal = num % 1 !== 0

      if (hasDecimal) {
        // 有小数点的情况 - 根据整数部分位数判断
        const integerPart = Math.floor(num)

        if (integerPart >= 10) {
          // 两位数以上带小数点 → cm
          console.log(`📏 OCR: 两位数带小数点推断为厘米: ${num} → cm`)
          return 'cm'
        } else {
          // 个位数带小数点 → 米
          console.log(`📏 OCR: 个位数带小数点推断为米: ${num} → m`)
          return 'm'
        }
      } else {
        // 整数情况
        if (num >= 100) {
          // 三位数以上 → mm
          console.log(`📏 OCR: 三位数以上推断为毫米: ${num} → mm`)
          return 'mm'
        }
      }
    }

    // 如果都是小于100的整数，根据经验判断
    const maxValue = Math.max(num1, num2)
    if (maxValue >= 50) {
      console.log(`📏 OCR: 中等整数值推断为毫米: max=${maxValue} → mm`)
      return 'mm'
    } else {
      console.log(`📏 OCR: 小整数值推断为厘米: max=${maxValue} → cm`)
      return 'cm'
    }
  }
  
  // 转换为毫米
  private static convertToMillimeters(num1: number, num2: number, unit: string): { length: number, width: number } {
    let multiplier = 1
    
    switch (unit.toLowerCase()) {
      case 'cm':
      case '厘米':
        multiplier = 10
        break
      case 'm':
      case '米':
        multiplier = 1000
        break
      case 'mm':
      case '毫米':
      default:
        multiplier = 1
        break
    }
    
    const length = Math.max(num1 * multiplier, num2 * multiplier)
    const width = Math.min(num1 * multiplier, num2 * multiplier)
    
    console.log(`🔄 单位转换: ${num1}x${num2}${unit} → ${length}x${width}mm`)
    
    return { length, width }
  }
  
  // 计算尺寸识别置信度
  private static calculateDimensionConfidence(match: RegExpMatchArray, unit: string): number {
    let confidence = 0.8 // 基础置信度
    
    // 如果有明确单位，提高置信度
    if (match[3]) {
      confidence += 0.15
    }
    
    // 如果数字合理（风口尺寸通常在50-3000mm范围内）
    const num1 = parseFloat(match[1])
    const num2 = parseFloat(match[2])
    if (num1 >= 50 && num1 <= 3000 && num2 >= 50 && num2 <= 3000) {
      confidence += 0.05
    }
    
    return Math.min(confidence, 1.0)
  }
  
  // 智能备注清理
  static cleanNotesEnhanced(rawNotes: string): NotesResult {
    if (!rawNotes || typeof rawNotes !== 'string') {
      return {
        cleanedNotes: '',
        removedItems: [],
        confidence: 1.0
      }
    }
    
    console.log(`🧹 开始清理备注: "${rawNotes}"`)
    
    const removedItems: string[] = []
    let cleanedText = rawNotes.trim()
    
    // 1. 移除尺寸信息
    const dimensionPattern = /\d+(?:\.\d+)?\s*[x×*乘]\s*\d+(?:\.\d+)?\s*(mm|cm|m|毫米|厘米|米)?/gi
    cleanedText = cleanedText.replace(dimensionPattern, (match) => {
      removedItems.push(`尺寸信息: ${match}`)
      return ''
    })
    
    // 2. 移除等号及其后的内容（如：26米x135=大厅）
    cleanedText = cleanedText.replace(/\s*=.*$/, (match) => {
      removedItems.push(`等号内容: ${match}`)
      return ''
    })
    
    // 3. 移除单位信息
    const unitPattern = /\b(mm|cm|m|毫米|厘米|米)\b/gi
    cleanedText = cleanedText.replace(unitPattern, (match) => {
      removedItems.push(`单位: ${match}`)
      return ''
    })
    
    // 4. 移除价格信息
    cleanedText = cleanedText.replace(/￥\d+(?:\.\d+)?/g, (match) => {
      removedItems.push(`价格: ${match}`)
      return ''
    })
    
    // 5. 移除数量信息（纯数字+量词）
    cleanedText = cleanedText.replace(/\b\d+\s*[个只套件张块]\b/g, (match) => {
      removedItems.push(`数量: ${match}`)
      return ''
    })
    
    // 6. 移除序号
    cleanedText = cleanedText.replace(/^\d+\s*[、.。]?\s*/, (match) => {
      removedItems.push(`序号: ${match}`)
      return ''
    })
    
    // 7. 移除无意义的符号和空白
    cleanedText = cleanedText
      .replace(/[×x*=+\-]/g, '') // 移除运算符号
      .replace(/\s+/g, ' ')      // 合并多个空格
      .trim()
    
    // 8. 保留有意义的内容
    const meaningfulContent = this.extractMeaningfulContent(cleanedText)
    
    const result = {
      cleanedNotes: meaningfulContent,
      removedItems,
      confidence: this.calculateNotesConfidence(rawNotes, meaningfulContent, removedItems)
    }
    
    console.log(`✅ 备注清理完成: "${rawNotes}" → "${meaningfulContent}"`)
    console.log(`🗑️ 移除内容: ${removedItems.join(', ')}`)
    
    return result
  }
  
  // 提取有意义的内容
  private static extractMeaningfulContent(text: string): string {
    if (!text || text.length <= 1) {
      return ''
    }
    
    // 有意义内容的特征
    const meaningfulPatterns = [
      // 房间类型
      /[客卧餐厨]厅|房间|卧室|厨房|卫生间|阳台|书房|办公室|会议室|走廊|过道/,
      // 安装要求
      /安装|吊顶|贴墙|嵌入|明装|暗装|固定/,
      // 处理要求
      /静音|降噪|防水|防潮|耐高温|阻燃/,
      // 位置描述
      /左侧|右侧|中间|角落|靠窗|靠门|顶部|底部/,
      // 特殊要求
      /定制|特殊|加急|自提|送货|包装/,
      // 楼层信息（如果不在专门的楼层列）
      /\d+楼|[一二三四五六七八九十]+楼|地下|顶楼/
    ]
    
    // 检查是否包含有意义的内容
    const hasMeaningfulContent = meaningfulPatterns.some(pattern => pattern.test(text))
    
    if (hasMeaningfulContent) {
      return text
    }
    
    // 如果没有明确的有意义内容，但长度合适，也保留
    if (text.length >= 2 && text.length <= 20 && !/^\d+$/.test(text)) {
      return text
    }
    
    return ''
  }
  
  // 计算备注清理质量
  private static calculateNotesConfidence(original: string, cleaned: string, removedItems: string[]): number {
    let confidence = 0.5 // 基础分数
    
    // 如果成功移除了无用信息，提高分数
    if (removedItems.length > 0) {
      confidence += 0.2
    }
    
    // 如果保留了有意义的内容，提高分数
    if (cleaned.length > 0 && cleaned.length < original.length) {
      confidence += 0.2
    }
    
    // 如果完全清空了（原本就是无用信息），也是好的结果
    if (cleaned.length === 0 && original.length > 0) {
      confidence += 0.1
    }
    
    return Math.min(confidence, 1.0)
  }
  
  // 综合解析单行文本
  static parseLineEnhanced(lineText: string): {
    dimensions: DimensionResult | null
    notes: NotesResult
    originalText: string
  } {
    console.log(`🔍 综合解析行: "${lineText}"`)
    
    const dimensions = this.parseDimensionsEnhanced(lineText)
    const notes = this.cleanNotesEnhanced(lineText)
    
    return {
      dimensions,
      notes,
      originalText: lineText
    }
  }
  
  // 批量测试解析效果
  static testParsingAccuracy(testCases: string[]): void {
    console.log('🧪 开始批量测试解析效果...')
    
    testCases.forEach((testCase, index) => {
      console.log(`\n📝 测试案例 ${index + 1}: "${testCase}"`)
      const result = this.parseLineEnhanced(testCase)
      
      console.log('📊 解析结果:')
      if (result.dimensions) {
        console.log(`  尺寸: ${result.dimensions.length}x${result.dimensions.width}mm (${result.dimensions.detectedUnit})`)
        console.log(`  置信度: ${(result.dimensions.confidence * 100).toFixed(1)}%`)
      } else {
        console.log('  尺寸: 未识别')
      }
      
      console.log(`  备注: "${result.notes.cleanedNotes}"`)
      console.log(`  移除: ${result.notes.removedItems.join(', ') || '无'}`)
      console.log(`  质量: ${(result.notes.confidence * 100).toFixed(1)}%`)
    })
    
    console.log('\n✅ 批量测试完成')
  }
}
