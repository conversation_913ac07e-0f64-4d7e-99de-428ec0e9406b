/**
 * 📝 文本区域分析器
 * 智能分割图片中的不同文本区域，提升备注和楼层信息识别
 */

export interface TextRegion {
  id: string
  type: 'title' | 'floor' | 'room' | 'dimension' | 'note' | 'quantity'
  content: string
  confidence: number
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  relatedRegions: string[] // 关联区域ID
}

export interface RegionAnalysisResult {
  regions: TextRegion[]
  layout: 'vertical' | 'horizontal' | 'table' | 'mixed'
  confidence: number
  suggestions: string[]
}

export class TextRegionAnalyzer {
  /**
   * 分析OCR结果中的文本区域
   */
  static analyzeRegions(ocrResult: any): RegionAnalysisResult {
    console.log('🔍 开始文本区域分析...')
    
    const words = ocrResult.words_result || []
    const regions: TextRegion[] = []
    
    // 1. 基于位置信息分组
    const positionGroups = this.groupByPosition(words)
    console.log(`📍 识别到 ${positionGroups.length} 个位置分组`)
    
    // 2. 识别各区域类型
    for (let i = 0; i < positionGroups.length; i++) {
      const group = positionGroups[i]
      const region = this.identifyRegionType(group, i)
      if (region) {
        regions.push(region)
      }
    }
    
    // 3. 建立区域关联关系
    this.establishRelations(regions)
    
    // 4. 分析整体布局
    const layout = this.analyzeLayout(regions)
    
    // 5. 计算置信度和生成建议
    const confidence = this.calculateRegionConfidence(regions)
    const suggestions = this.generateSuggestions(regions, layout)
    
    console.log(`✅ 区域分析完成: ${regions.length} 个区域, 布局: ${layout}`)
    
    return {
      regions,
      layout,
      confidence,
      suggestions
    }
  }

  /**
   * 基于位置信息对文字进行分组
   */
  private static groupByPosition(words: any[]): any[] {
    if (!words.length) return []
    
    const groups: any[] = []
    const processed = new Set<number>()
    
    for (let i = 0; i < words.length; i++) {
      if (processed.has(i)) continue
      
      const word = words[i]
      const location = word.location || { left: 0, top: 0, width: 0, height: 0 }
      
      const group = {
        words: [word],
        bounds: { ...location },
        content: word.words
      }
      
      // 查找相邻的文字
      for (let j = i + 1; j < words.length; j++) {
        if (processed.has(j)) continue
        
        const otherWord = words[j]
        const otherLocation = otherWord.location || { left: 0, top: 0, width: 0, height: 0 }
        
        // 判断是否在同一行或相邻区域
        if (this.isAdjacent(location, otherLocation)) {
          group.words.push(otherWord)
          group.content += ' ' + otherWord.words
          
          // 更新边界
          group.bounds.left = Math.min(group.bounds.left, otherLocation.left)
          group.bounds.top = Math.min(group.bounds.top, otherLocation.top)
          group.bounds.width = Math.max(
            group.bounds.left + group.bounds.width,
            otherLocation.left + otherLocation.width
          ) - group.bounds.left
          group.bounds.height = Math.max(
            group.bounds.top + group.bounds.height,
            otherLocation.top + otherLocation.height
          ) - group.bounds.top
          
          processed.add(j)
        }
      }
      
      groups.push(group)
      processed.add(i)
    }
    
    return groups
  }

  /**
   * 判断两个文字区域是否相邻
   */
  private static isAdjacent(loc1: any, loc2: any): boolean {
    const horizontalDistance = Math.abs(loc1.left - loc2.left)
    const verticalDistance = Math.abs(loc1.top - loc2.top)
    
    // 同一行：垂直距离小，水平距离适中
    const sameRow = verticalDistance < 20 && horizontalDistance < 200
    
    // 相邻行：垂直距离适中，水平位置相近
    const adjacentRow = verticalDistance < 50 && horizontalDistance < 100
    
    return sameRow || adjacentRow
  }

  /**
   * 识别区域类型
   */
  private static identifyRegionType(group: any, index: number): TextRegion | null {
    const content = group.content.trim()
    if (!content) return null
    
    let type: TextRegion['type'] = 'note'
    let confidence = 0.5
    
    // 楼层识别
    if (this.isFloorInfo(content)) {
      type = 'floor'
      confidence = 0.9
    }
    // 房间识别
    else if (this.isRoomInfo(content)) {
      type = 'room'
      confidence = 0.8
    }
    // 尺寸识别
    else if (this.isDimensionInfo(content)) {
      type = 'dimension'
      confidence = 0.95
    }
    // 数量识别
    else if (this.isQuantityInfo(content)) {
      type = 'quantity'
      confidence = 0.7
    }
    // 标题识别
    else if (this.isTitleInfo(content, index)) {
      type = 'title'
      confidence = 0.6
    }
    
    return {
      id: `region_${index}`,
      type,
      content,
      confidence,
      position: {
        x: group.bounds.left,
        y: group.bounds.top,
        width: group.bounds.width,
        height: group.bounds.height
      },
      relatedRegions: []
    }
  }

  /**
   * 楼层信息识别
   */
  private static isFloorInfo(content: string): boolean {
    const floorPatterns = [
      /^([一二三四五六七八九十]+)楼$/,
      /^(\d+)楼$/,
      /^([一二三四五六七八九十]+)层$/,
      /^(\d+)层$/,
      /^[FB](\d+)楼?$/,
      /^地下[一二三四五六七八九十\d]+[楼层]?$/,
      /^负[一二三四五六七八九十\d]+[楼层]?$/
    ]
    
    return floorPatterns.some(pattern => pattern.test(content))
  }

  /**
   * 房间信息识别
   */
  private static isRoomInfo(content: string): boolean {
    const roomKeywords = [
      '客厅', '餐厅', '厨房', '卫生间', '洗手间', '厕所', '阳台', '书房',
      '主卧', '次卧', '卧室', '儿童房', '老人房', '活动室', '茶室', '棋牌室',
      'KTV', '检修口', '维修口', '配电间', '弱电间', '强电间'
    ]
    
    return roomKeywords.some(keyword => content.includes(keyword)) ||
           /([A-Z]\d{2,4})/.test(content) || // A201, B302
           /(\d+号?房\d*)/.test(content) || // 1901号房
           /(\d+楼\d+室?)/.test(content) // 19楼01室
  }

  /**
   * 尺寸信息识别
   */
  private static isDimensionInfo(content: string): boolean {
    return /\d+\s*[x×*+\-]\s*\d+/.test(content)
  }

  /**
   * 数量信息识别
   */
  private static isQuantityInfo(content: string): boolean {
    return /\d+\s*[个只套件]/.test(content) || /数量\s*[:：]\s*\d+/.test(content)
  }

  /**
   * 标题信息识别
   */
  private static isTitleInfo(content: string, index: number): boolean {
    // 通常标题在前几行，且包含项目名称等关键词
    return index < 3 && (
      content.includes('项目') ||
      content.includes('工程') ||
      content.includes('风口') ||
      content.includes('清单') ||
      content.length > 10
    )
  }

  /**
   * 建立区域关联关系
   */
  private static establishRelations(regions: TextRegion[]): void {
    for (let i = 0; i < regions.length; i++) {
      const region = regions[i]
      
      // 查找相关区域
      for (let j = 0; j < regions.length; j++) {
        if (i === j) continue
        
        const otherRegion = regions[j]
        
        // 楼层与房间的关联
        if (region.type === 'floor' && otherRegion.type === 'room') {
          if (this.isVerticallyRelated(region, otherRegion)) {
            region.relatedRegions.push(otherRegion.id)
            otherRegion.relatedRegions.push(region.id)
          }
        }
        
        // 房间与尺寸的关联
        if (region.type === 'room' && otherRegion.type === 'dimension') {
          if (this.isHorizontallyRelated(region, otherRegion)) {
            region.relatedRegions.push(otherRegion.id)
            otherRegion.relatedRegions.push(region.id)
          }
        }
        
        // 尺寸与备注的关联
        if (region.type === 'dimension' && otherRegion.type === 'note') {
          if (this.isHorizontallyRelated(region, otherRegion)) {
            region.relatedRegions.push(otherRegion.id)
            otherRegion.relatedRegions.push(region.id)
          }
        }
      }
    }
  }

  /**
   * 判断垂直关联关系
   */
  private static isVerticallyRelated(region1: TextRegion, region2: TextRegion): boolean {
    const verticalDistance = Math.abs(region1.position.y - region2.position.y)
    const horizontalOverlap = this.calculateHorizontalOverlap(region1, region2)
    
    return verticalDistance < 100 && horizontalOverlap > 0.3
  }

  /**
   * 判断水平关联关系
   */
  private static isHorizontallyRelated(region1: TextRegion, region2: TextRegion): boolean {
    const horizontalDistance = Math.abs(region1.position.x - region2.position.x)
    const verticalOverlap = this.calculateVerticalOverlap(region1, region2)
    
    return horizontalDistance < 200 && verticalOverlap > 0.5
  }

  /**
   * 计算水平重叠度
   */
  private static calculateHorizontalOverlap(region1: TextRegion, region2: TextRegion): number {
    const left1 = region1.position.x
    const right1 = region1.position.x + region1.position.width
    const left2 = region2.position.x
    const right2 = region2.position.x + region2.position.width
    
    const overlapStart = Math.max(left1, left2)
    const overlapEnd = Math.min(right1, right2)
    const overlapWidth = Math.max(0, overlapEnd - overlapStart)
    
    const totalWidth = Math.max(right1, right2) - Math.min(left1, left2)
    
    return overlapWidth / totalWidth
  }

  /**
   * 计算垂直重叠度
   */
  private static calculateVerticalOverlap(region1: TextRegion, region2: TextRegion): number {
    const top1 = region1.position.y
    const bottom1 = region1.position.y + region1.position.height
    const top2 = region2.position.y
    const bottom2 = region2.position.y + region2.position.height
    
    const overlapStart = Math.max(top1, top2)
    const overlapEnd = Math.min(bottom1, bottom2)
    const overlapHeight = Math.max(0, overlapEnd - overlapStart)
    
    const totalHeight = Math.max(bottom1, bottom2) - Math.min(top1, top2)
    
    return overlapHeight / totalHeight
  }

  /**
   * 分析整体布局
   */
  private static analyzeLayout(regions: TextRegion[]): 'vertical' | 'horizontal' | 'table' | 'mixed' {
    if (regions.length < 2) return 'vertical'
    
    // 计算区域的分布
    const positions = regions.map(r => r.position)
    const avgY = positions.reduce((sum, p) => sum + p.y, 0) / positions.length
    const avgX = positions.reduce((sum, p) => sum + p.x, 0) / positions.length
    
    const verticalSpread = Math.max(...positions.map(p => p.y)) - Math.min(...positions.map(p => p.y))
    const horizontalSpread = Math.max(...positions.map(p => p.x)) - Math.min(...positions.map(p => p.x))
    
    if (verticalSpread > horizontalSpread * 2) {
      return 'vertical'
    } else if (horizontalSpread > verticalSpread * 2) {
      return 'horizontal'
    } else if (regions.some(r => r.type === 'dimension') && regions.length > 5) {
      return 'table'
    } else {
      return 'mixed'
    }
  }

  /**
   * 计算区域识别置信度
   */
  private static calculateRegionConfidence(regions: TextRegion[]): number {
    if (regions.length === 0) return 0
    
    const avgConfidence = regions.reduce((sum, r) => sum + r.confidence, 0) / regions.length
    const relationBonus = regions.filter(r => r.relatedRegions.length > 0).length / regions.length * 0.2
    
    return Math.min(1, avgConfidence + relationBonus)
  }

  /**
   * 生成优化建议
   */
  private static generateSuggestions(regions: TextRegion[], layout: string): string[] {
    const suggestions: string[] = []
    
    const floorRegions = regions.filter(r => r.type === 'floor')
    const roomRegions = regions.filter(r => r.type === 'room')
    const dimensionRegions = regions.filter(r => r.type === 'dimension')
    const noteRegions = regions.filter(r => r.type === 'note')
    
    if (floorRegions.length === 0 && roomRegions.length > 0) {
      suggestions.push('建议添加楼层信息以提高识别准确性')
    }
    
    if (noteRegions.length < dimensionRegions.length * 0.3) {
      suggestions.push('检测到较少备注信息，可能需要图像增强处理')
    }
    
    if (layout === 'mixed' && regions.length > 10) {
      suggestions.push('复杂布局检测，建议使用表格识别模式')
    }
    
    return suggestions
  }
}
