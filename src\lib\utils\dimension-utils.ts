/**
 * 尺寸处理工具函数
 * 统一处理所有长度和宽度的智能识别逻辑
 */

export interface DimensionResult {
  length: number
  width: number
  originalInfo?: string
  color?: string
}

/**
 * 统一的尺寸分隔符定义
 * 支持各种常见的分隔符，包括OCR识别错误的变体
 */
export const DIMENSION_SEPARATORS = {
  // 正则表达式字符类，用于匹配所有支持的分隔符（简化版本，避免复杂匹配问题）
  REGEX_CLASS: '[xX×\\*]',
  // 🔧 新增：用于分割的正则表达式（简化版本，避免分割问题）
  SPLIT_REGEX: '[xX×\\*]',
  // 字符串数组，用于替换操作
  CHARS: ['x', 'X', '×', '✖️', '✖', '✘', '✕', '⨯', '·', '*', '+', '-', '—', '–', '/', '@', '&', ':', '：', '乘', 'by'],
  // 用于预处理的替换映射
  NORMALIZE_MAP: {
    '×': 'x',      // 乘号
    '/': 'x',      // 斜杠
    '✖️': 'x',     // 粗叉号(emoji)
    '✖': 'x',      // 粗叉号
    '✘': 'x',      // 叉号
    '✕': 'x',      // 细叉号
    '⨯': 'x',      // 数学乘号
    '·': 'x',      // 中点
    '*': 'x',      // 星号
    '+': 'x',      // 加号
    '-': 'x',      // 短横线
    '—': 'x',      // 长横线
    '–': 'x',      // 中横线
    '@': 'x',      // at符号
    '&': 'x',      // and符号
    ':': 'x',      // 冒号
    '：': 'x',     // 中文冒号
    '乘': 'x',     // 中文乘
    'by': 'x',     // 英文by
    'X': 'x'       // 大写X
  }
}

/**
 * 智能尺寸识别：确保大值作为长度，小值作为宽度，并验证风口业务规则
 * @param length 长度值
 * @param width 宽度值
 * @returns 智能识别后的尺寸对象
 */
export function smartDimensionRecognition(length: number, width: number): DimensionResult {
  console.log(`🔍 智能尺寸识别开始: 输入=${length}×${width}`)

  // 🔧 检查NaN值
  if (isNaN(length) || isNaN(width)) {
    console.log(`⚠️ 跳过识别: 存在NaN值 (长度=${length}, 宽度=${width})`)
    return { length: isNaN(length) ? 0 : length, width: isNaN(width) ? 0 : width }
  }

  // 如果任一值为0或负数，直接返回原值
  if (length <= 0 || width <= 0) {
    console.log(`⚠️ 跳过识别: 存在无效值 (长度=${length}, 宽度=${width})`)
    return { length, width }
  }

  // 如果两个值相等，直接返回原值
  if (length === width) {
    console.log(`✅ 长宽相等: ${length}×${width}，无需调整`)
    return { length, width }
  }

  // 智能识别：大值作为长度，小值作为宽度
  const maxValue = Math.max(length, width)
  const minValue = Math.min(length, width)

  // 风口业务规则验证：区分正方形和长方形风口
  if (maxValue === minValue) {
    // 正方形风口/检修口：长宽相等，可以超过500mm
    console.log(`✅ 正方形风口/检修口: ${maxValue}×${minValue}mm，长宽相等属正常`)
  } else {
    // 长方形风口：宽度通常在300mm左右，不应超过500mm
    if (minValue > 500) {
      console.log(`⚠️ 长方形风口宽度异常: ${minValue}mm > 500mm，可能存在单位识别错误`)
      console.log(`🔧 建议检查原始数据的单位识别是否正确`)

      // 如果最小值都超过500mm，可能是单位识别错误，但仍按原逻辑处理
      // 实际业务中可能需要人工确认
    } else if (minValue > 400) {
      console.log(`⚠️ 长方形风口宽度偏大: ${minValue}mm，正常宽度约300mm左右`)
    } else if (minValue >= 250 && minValue <= 350) {
      console.log(`✅ 长方形风口宽度正常: ${minValue}mm (标准范围250-350mm)`)
    } else if (minValue < 250) {
      console.log(`📏 检测到特殊规格风口: 宽度${minValue}mm (小于标准250mm)`)
    }
  }

  console.log(`🔄 智能识别结果: 大值${maxValue}作为长度，小值${minValue}作为宽度`)

  return {
    length: maxValue,
    width: minValue
  }
}

/**
 * 预处理复杂组合尺寸格式
 * 处理如"1.6+1.75+1.6*125"这样的复杂组合
 * @param line 原始文本
 * @returns 预处理后的文本
 */
function preprocessComplexDimensions(line: string): string {
  console.log(`🔧 复杂尺寸预处理: "${line}"`)

  // 处理组合尺寸格式：如"1.6+1.75+1.6*125"
  // 这种格式通常表示多段组合，我们提取主要尺寸
  const complexPattern = /(\d+(?:\.\d+)?)\s*\+\s*(\d+(?:\.\d+)?)\s*\+\s*(\d+(?:\.\d+)?)\s*[*×xX]\s*(\d+(?:\.\d+)?)/
  const complexMatch = line.match(complexPattern)

  if (complexMatch) {
    // 计算总长度：前三个数字相加
    const totalLength = parseFloat(complexMatch[1]) + parseFloat(complexMatch[2]) + parseFloat(complexMatch[3])
    const width = parseFloat(complexMatch[4])
    const result = `${totalLength}×${width}`
    console.log(`🔧 复杂组合转换: "${complexMatch[0]}" → "${result}"`)
    return line.replace(complexMatch[0], result)
  }

  // 处理其他可能的组合格式
  // 如"1.2+1.8×150" → "3.0×150"
  const simplePattern = /(\d+(?:\.\d+)?)\s*\+\s*(\d+(?:\.\d+)?)\s*[*×xX]\s*(\d+(?:\.\d+)?)/
  const simpleMatch = line.match(simplePattern)

  if (simpleMatch) {
    const totalLength = parseFloat(simpleMatch[1]) + parseFloat(simpleMatch[2])
    const width = parseFloat(simpleMatch[3])
    const result = `${totalLength}×${width}`
    console.log(`🔧 简单组合转换: "${simpleMatch[0]}" → "${result}"`)
    return line.replace(simpleMatch[0], result)
  }

  // 🔧 新增：处理OCR识别的"长×宽"格式（支持OCR错误）
  // 如"回风长.34×宽0.3" → "0.34×0.3"
  // 如"出风长3.4×宽0.25" → "3.4×0.25"
  // 如"出风兴1.75×宽0.16" → "1.75×0.16"
  const lengthWidthPattern = /(?:[长兴艮良]|宽)\s*\.?(\d+(?:\.\d+)?)\s*[×xX]\s*(?:[长兴艮良]|宽)\s*\.?(\d+(?:\.\d+)?)/
  const lengthWidthMatch = line.match(lengthWidthPattern)

  if (lengthWidthMatch) {
    const num1 = lengthWidthMatch[1]
    const num2 = lengthWidthMatch[2]
    const result = `${num1}×${num2}`
    console.log(`🔧 长宽格式转换: "${lengthWidthMatch[0]}" → "${result}"`)
    return line.replace(lengthWidthMatch[0], result)
  }

  return line
}

/**
 * 预处理OCR识别的特殊格式
 * @param line 原始文本
 * @returns 预处理后的文本
 */
function preprocessOCRFormats(line: string): string {
  let processedLine = line

  // 🔧 新增：处理"尺寸✖️数量个"格式，如"1300✖️220✖️14个"
  // 将第二个✖️后的数量部分移到末尾，避免干扰尺寸解析
  const quantityWithEmojiMatch = processedLine.match(/(\d+(?:\.\d+)?✖️\d+(?:\.\d+)?)✖️(\d+(?:\s*个|\s*只|\s*套|\s*件))/)
  if (quantityWithEmojiMatch) {
    const dimensionPart = quantityWithEmojiMatch[1]
    const quantityPart = quantityWithEmojiMatch[2]
    const replacement = `${dimensionPart} ${quantityPart}`
    console.log(`🔧 ✖️数量格式转换: "${quantityWithEmojiMatch[0]}" → "${replacement}"`)
    processedLine = processedLine.replace(quantityWithEmojiMatch[0], replacement)
  }

  // 🔧 统一替换所有✖️为×，便于后续处理
  processedLine = processedLine.replace(/✖️/g, '×')

  // 处理"长.数字×宽数字"格式（OCR可能将小数点识别错误，支持OCR错误字符）
  // 如"回风长.34×宽0.3" → "回风长0.34×宽0.3"
  // 如"出风兴.75×宽0.16" → "出风兴0.75×宽0.16"
  processedLine = processedLine.replace(/[长兴艮良]\.(\d+)/g, '长0.$1')

  // 处理"长数字×宽数字"格式，提取纯数字（支持OCR错误）
  // 如"回风长0.34×宽0.3" → "0.34×0.3"
  // 如"出风兴1.75×宽0.16" → "1.75×0.16"
  const lengthWidthMatch = processedLine.match(/(?:回风|出风|进气|检修|维修)?(?:[长兴艮良]|宽)\s*(\d+(?:\.\d+)?)\s*[×xX]\s*(?:[长兴艮良]|宽)\s*(\d+(?:\.\d+)?)/)
  if (lengthWidthMatch) {
    const result = `${lengthWidthMatch[1]}×${lengthWidthMatch[2]}`
    console.log(`🔧 OCR长宽格式转换: "${lengthWidthMatch[0]}" → "${result}"`)
    processedLine = processedLine.replace(lengthWidthMatch[0], result)
  }

  return processedLine
}

/**
 * 解析一行中的多个尺寸
 * 支持格式如：15.5/266.5    25.5/152.5歺
 * @param line 包含多个尺寸的行
 * @returns 解析后的尺寸数组
 */
export function parseMultipleDimensions(line: string): DimensionResult[] {
  if (!line || typeof line !== 'string') {
    return []
  }

  console.log(`🔍 多尺寸解析: "${line}"`)

  // 🔧 预处理复杂组合尺寸格式
  let processedLine = preprocessComplexDimensions(line)

  // 🔧 新增：预处理OCR识别的特殊格式
  processedLine = preprocessOCRFormats(processedLine)

  // 🔧 新增：检测并处理连续尺寸（如：150x800243x1750）
  const continuousDimensionMatch = processedLine.match(/(\d+)\s*[xX×]\s*(\d+)(\d{3,4})\s*[xX×]\s*(\d+)/)
  if (continuousDimensionMatch) {
    const [fullMatch, dim1, dim2, dim3, dim4] = continuousDimensionMatch
    console.log(`🔧 检测到连续尺寸: "${fullMatch}"`)
    console.log(`   分割为: ${dim1}x${dim2} 和 ${dim3}x${dim4}`)

    // 分别解析两个尺寸
    const firstDimension = parseDimensionString(`${dim1}x${dim2}`)
    const secondDimension = parseDimensionString(`${dim3}x${dim4}`)

    const results: DimensionResult[] = []
    if (firstDimension) {
      results.push(firstDimension)
      console.log(`✅ 第一个尺寸: ${firstDimension.length}×${firstDimension.width}mm`)
    }
    if (secondDimension) {
      results.push(secondDimension)
      console.log(`✅ 第二个尺寸: ${secondDimension.length}×${secondDimension.width}mm`)
    }

    console.log(`🎯 连续尺寸解析完成，共解析出 ${results.length} 个尺寸`)
    return results
  }

  console.log(`🔧 预处理后: "${processedLine}"`)

  const results: DimensionResult[] = []

  // 🔧 匹配所有格式的尺寸：支持所有可能的间隔符号
  // 使用统一的分隔符定义，确保一致性
  // 🔧 修复：分别处理emoji和普通字符，支持小数点开头格式（如 250x.1020）
  const emojiPattern = `(\\d+(?:\\.\\d+)?)✖️(\\.?\\d+(?:\\.\\d+)?)`
  const normalPattern = `(\\d+(?:\\.\\d+)?)[xX×\\*+\\-—–/@&:：乘by](\\.?\\d+(?:\\.\\d+)?)`

  console.log(`🔍 使用模式匹配: emoji="${emojiPattern}", normal="${normalPattern}"`)
  console.log(`🔍 处理文本: "${processedLine}"`)

  const emojiMatches = processedLine.match(new RegExp(emojiPattern, 'g')) || []
  const normalMatches = processedLine.match(new RegExp(normalPattern, 'g')) || []

  console.log(`🔍 emoji匹配结果:`, emojiMatches)
  console.log(`🔍 normal匹配结果:`, normalMatches)

  const dimensionMatches = [...emojiMatches, ...normalMatches]

  if (dimensionMatches && dimensionMatches.length > 0) {
    console.log(`🔍 找到 ${dimensionMatches.length} 个尺寸:`, dimensionMatches)

    dimensionMatches.forEach((match, index) => {
      // 🔧 修复：先处理emoji分隔符，再使用字符类分割
      let normalizedMatch = match.replace(/✖️/g, '×')
      const parts = normalizedMatch.split(new RegExp(DIMENSION_SEPARATORS.SPLIT_REGEX))

      console.log(`  🔍 处理匹配${index + 1}: "${match}" → 分割后:`, parts)

      // 🔧 处理小数点开头的数字（如 .1020 → 1020）
      let part1 = parts[0]
      let part2 = parts[1]

      // 🔧 修复：检查 part2 是否存在，避免 undefined 错误
      if (part2 && part2.startsWith('.') && /^\.\d{4}$/.test(part2)) {
        part2 = part2.substring(1) // 移除开头的小数点
        console.log(`  🔧 修正小数点开头的4位数: "${parts[1]}" → "${part2}"`)
      }

      // 🔧 修复：检查 parts 是否有效，避免 undefined 错误
      if (!part1 || !part2) {
        console.log(`  ❌ 分割结果无效: part1="${part1}", part2="${part2}"`)
        return // 跳过这个无效的匹配
      }

      const num1 = parseFloat(part1)
      const num2 = parseFloat(part2)

      console.log(`  📊 数值解析: "${part1}" → ${num1}, "${part2}" → ${num2}`)

      // 🔧 修复：检查数值是否有效
      if (isNaN(num1) || isNaN(num2)) {
        console.log(`  ❌ 数值解析失败: num1=${num1}, num2=${num2}`)
        return // 跳过这个无效的匹配
      }

      // 🧠 智能单位识别：基于OCR识别特点和风口常见尺寸
      const smallerValue = Math.min(num1, num2)
      const largerValue = Math.max(num1, num2)
      let unit1, unit2

      // 🎯 OCR专用单位判断逻辑（基于风口实际尺寸规律）
      if (smallerValue < 1) {
        // 小数值（如0.15, 0.3, 0.25）→ 米单位
        // 风口宽度常见：150mm(0.15m), 300mm(0.3m), 250mm(0.25m)
        unit1 = unit2 = 'm'
        console.log(`  🧠 尺寸${index + 1}: ${num1}${unit1} × ${num2}${unit2} (小数值推断为米，OCR常见格式)`)
      } else if (smallerValue >= 1 && largerValue <= 10) {
        // 1-10范围（如1.2, 2.34, 3.4）→ 米单位
        // 风口长度常见：1200mm(1.2m), 2340mm(2.34m), 3400mm(3.4m)
        unit1 = unit2 = 'm'
        console.log(`  🧠 尺寸${index + 1}: ${num1}${unit1} × ${num2}${unit2} (1-10范围推断为米，OCR常见格式)`)
      } else if (smallerValue <= 50 && largerValue <= 500) {
        // 中等数值推断为cm
        unit1 = unit2 = 'cm'
        console.log(`  🧠 尺寸${index + 1}: ${num1}${unit1} × ${num2}${unit2} (中等数值推断为cm)`)
      } else {
        // 大数值推断为mm
        unit1 = unit2 = 'mm'
        console.log(`  🧠 尺寸${index + 1}: ${num1}${unit1} × ${num2}${unit2} (大数值推断为mm)`)
      }

      // 转换为毫米
      const length = convertSingleToMillimeters(num1, unit1)
      const width = convertSingleToMillimeters(num2, unit2)

      // 应用智能尺寸识别（确保大值作为长度，小值作为宽度）
      const smartResult = smartDimensionRecognition(length, width)

      const finalResult = {
        length: smartResult.length,
        width: smartResult.width,
        originalInfo: `${smartResult.length}×${smartResult.width}mm`
      }

      results.push(finalResult)

      console.log(`  ✅ 尺寸${index + 1}解析结果: ${smartResult.length}×${smartResult.width}mm`)
      console.log(`  📊 添加到结果数组: length=${finalResult.length}, width=${finalResult.width}`)
    })
  } else {
    // 如果没有斜杠格式，尝试用原有的单尺寸解析
    const singleResult = parseDimensionString(line)
    if (singleResult) {
      results.push(singleResult)
    }
  }

  console.log(`🎯 多尺寸解析完成，共解析出 ${results.length} 个尺寸`)
  return results
}

/**
 * 解析尺寸字符串（增强版本）
 * 支持各种单位和格式，包括等号格式如"26米x135=大厅"
 * @param dimensionStr 尺寸字符串
 * @returns 解析后的尺寸对象，失败返回null
 */
export function parseDimensionString(dimensionStr: string): DimensionResult | null {
  if (!dimensionStr || typeof dimensionStr !== 'string') {
    return null
  }

  console.log(`🔍 增强尺寸解析: "${dimensionStr}"`)

  // 🔧 预处理：检测圆形风口（如：圆口=直径160=2个）
  const circularVentMatch = dimensionStr.match(/(?:圆口|直径)\s*=?\s*(?:直径)?\s*(\d+)/)
  if (circularVentMatch) {
    const diameter = parseInt(circularVentMatch[1])
    console.log(`🔍 检测到圆形风口: 直径${diameter}mm → 转换为 ${diameter}x${diameter}`)
    // 圆形风口转换为正方形尺寸
    return parseDimensionString(`${diameter}x${diameter}`)
  }

  // 🔧 预处理：检测特殊尺寸格式（如：26米x135=大厅）
  const specialDimensionMatch = dimensionStr.match(/(\d+)米?\s*[×xX]\s*(\d+)\s*=\s*([\u4e00-\u9fa5]+)/)
  if (specialDimensionMatch) {
    const [, length, width, location] = specialDimensionMatch
    console.log(`🔍 检测到特殊尺寸格式: ${length}米x${width} → 位置:${location}`)
    // 将米转换为毫米，第二个数字直接作为毫米（风口宽度通常100-500mm）
    const lengthMm = parseInt(length) * 1000
    const widthMm = parseInt(width)  // 135直接作为135mm
    console.log(`🔧 单位转换: ${length}米=${lengthMm}mm, ${width}=${widthMm}mm`)
    return {
      length: lengthMm,
      width: widthMm,
      unit: 'mm',
      notes: location
    }
  }

  // 🔧 预处理：检测并分割连续尺寸（如：150x800243x1750）
  const continuousDimensionMatch = dimensionStr.match(/(\d+)\s*[xX×]\s*(\d+)(\d{3,4})\s*[xX×]\s*(\d+)/)
  if (continuousDimensionMatch) {
    const [, dim1, dim2, dim3, dim4] = continuousDimensionMatch
    console.log(`🔍 检测到连续尺寸: ${dim1}x${dim2}${dim3}x${dim4}，分割为两个尺寸`)
    console.log(`   第一个尺寸: ${dim1}x${dim2}`)
    console.log(`   第二个尺寸: ${dim3}x${dim4}`)

    // 返回第一个尺寸，第二个尺寸需要在上层处理
    // 这里我们先处理第一个，后续需要在多尺寸解析中处理完整的连续尺寸
    return parseDimensionString(`${dim1}x${dim2}`)
  }

  // 预处理：统一格式，但保留中文格式的完整性
  let cleanText = dimensionStr.toLowerCase()

  // 先尝试不预处理的匹配，如果失败再进行预处理
  console.log(`🔍 原始字符串匹配测试: "${cleanText}"`)

  // 增强的尺寸匹配模式
  const dimensionPatterns = [
    // 圆形风口：直径格式
    /直径\s*(\d+(?:\.\d+)?)\s*(mm|cm|m|毫米|厘米|米)?/i,
    // 🔧 新增：斜杠格式（智能单位识别）：15.5/266.5、25.5/152.5
    /(\d+(?:\.\d+)?)\/(\d+(?:\.\d+)?)/i,
    // 🔧 新增：风口类型+数字×数字格式：回风口300✘1500、出风口120✘2750
    new RegExp(`(?:回风口|出风口|新风口|进风口|发风口|回风|出风|新风|进风|发风)\\s*(\\d*\\.?\\d+)\\s*${DIMENSION_SEPARATORS.REGEX_CLASS}\\s*(\\d*\\.?\\d+)\\s*(mm|cm|m|毫米|厘米|米)?`, 'i'),
    // 中文长宽格式（带数量，支持OCR错误）：发风长1.2×宽0.15x3个，出风兴1.75×宽0.16
    new RegExp(`(?:回风|出风|新风|进风|发风)?[长兴艮良]\\s*(\\d*\\.?\\d+)\\s*${DIMENSION_SEPARATORS.REGEX_CLASS}\\s*宽\\s*(\\d*\\.?\\d+)(?:\\s*${DIMENSION_SEPARATORS.REGEX_CLASS}\\s*\\d+\\s*个)?`, 'i'),
    // 中文宽度格式（带数量）：出风1.2×宽0.15x3个
    new RegExp(`(?:回风|出风|新风|进风|发风)\\s*(\\d*\\.?\\d+)\\s*${DIMENSION_SEPARATORS.REGEX_CLASS}\\s*宽\\s*(\\d*\\.?\\d+)(?:\\s*${DIMENSION_SEPARATORS.REGEX_CLASS}\\s*\\d+\\s*个)?`, 'i'),
    // 中文长宽格式（带单位，支持OCR错误）：长1.2米×宽0.15米
    new RegExp(`(?:回风|出风|新风|进风|发风)?[长兴艮良]\\s*(\\d*\\.?\\d+)\\s*(mm|cm|m|毫米|厘米|米)?\\s*${DIMENSION_SEPARATORS.REGEX_CLASS}\\s*宽\\s*(\\d*\\.?\\d+)\\s*(mm|cm|m|毫米|厘米|米)?`, 'i'),
    // 等号格式：数字+单位x数字+单位=xxx (如：26米x135米=大厅)
    /(\d+(?:\.\d+)?)(mm|cm|m|毫米|厘米|米)x(\d+(?:\.\d+)?)(mm|cm|m|毫米|厘米|米)?=.*?/i,
    // 等号格式：数字+单位x数字=xxx (如：26米x135=大厅)
    /(\d+(?:\.\d+)?)(mm|cm|m|毫米|厘米|米)x(\d+(?:\.\d+)?)=.*?/i,
    // 标准格式：数字x数字+单位
    /(\d+(?:\.\d+)?)x(\d+(?:\.\d+)?)(mm|cm|m|毫米|厘米|米)/i,
    // 中文格式：数字乘数字
    /(\d+(?:\.\d+)?)乘(\d+(?:\.\d+)?)(mm|cm|m|毫米|厘米|米)?/i,
    // 简单格式：数字x数字（无单位，支持.34这样的小数，支持多种分隔符）
    new RegExp(`(\\d*\\.?\\d+)${DIMENSION_SEPARATORS.REGEX_CLASS}(\\d*\\.?\\d+)`, 'i')
  ]

  // 首先尝试原始字符串匹配（优先中文格式）
  for (const pattern of dimensionPatterns) {
    const match = cleanText.match(pattern)
    if (match) {
      let num1, num2, unit
      console.log(`✅ 原始字符串匹配成功: "${match[0]}" 使用模式: ${pattern.source.substring(0, 50)}...`)
      console.log(`🔍 匹配组详情: [0]="${match[0]}" [1]="${match[1]}" [2]="${match[2]}" [3]="${match[3] || 'undefined'}"`)

      // 🔧 增强容错性：检查匹配组的有效性
      if (!match[1] || !match[2]) {
        console.log(`⚠️ 匹配组不完整，跳过此模式`)
        continue
      }

      // 处理圆形风口：直径格式
      if (pattern.source.includes('直径')) {
        const diameter = parseFloat(match[1])
        unit = match[2] || 'mm'
        console.log(`🔵 圆形风口识别: 直径${diameter}${unit}`)

        const diameterInMm = convertSingleToMillimeters(diameter, unit)
        // 圆形风口：长度和宽度都是直径，不调换
        return {
          length: diameterInMm,
          width: diameterInMm,
          originalInfo: `直径${diameterInMm}mm`
        }
      }

      // 🔧 处理斜杠格式：15.5/266.5（智能单位识别）
      if (pattern.source.includes('\\/')) {
        num1 = parseFloat(match[1])
        num2 = parseFloat(match[2])

        // 🧠 智能单位识别：同一行的尺寸应该使用相同单位
        const smallerValue = Math.min(num1, num2)
        const largerValue = Math.max(num1, num2)

        let unit1, unit2
        if (smallerValue < 100) {
          // 小值 < 100，推断整行都是 cm
          unit1 = unit2 = 'cm'
          console.log(`🧠 智能单位识别: ${num1}${unit1} / ${num2}${unit2} (小值<100推断整行为cm)`)
        } else {
          // 都 ≥ 100，推断整行都是 mm
          unit1 = unit2 = 'mm'
          console.log(`🧠 智能单位识别: ${num1}${unit1} / ${num2}${unit2} (都≥100推断整行为mm)`)
        }

        // 转换为毫米
        const length = convertSingleToMillimeters(num1, unit1)
        const width = convertSingleToMillimeters(num2, unit2)

        // 应用智能尺寸识别（确保大值作为长度，小值作为宽度）
        const smartResult = smartDimensionRecognition(length, width)

        return {
          length: smartResult.length,
          width: smartResult.width,
          originalInfo: `${smartResult.length}×${smartResult.width}mm`
        }
      }

      // 处理中文长宽格式：长.34×宽0.3 或 出风兴1.75×宽0.16（支持OCR错误）
      if ((pattern.source.includes('[长兴艮良]') && pattern.source.includes('宽')) ||
          (pattern.source.includes('宽') && pattern.source.includes('回风|出风|新风|进风|发风'))) {
        // 检查是否是带单位的格式
        if (match[2] && (match[2] === 'mm' || match[2] === 'cm' || match[2] === 'm' || match[2] === '毫米' || match[2] === '厘米' || match[2] === '米')) {
          // 带单位格式：长1.2米×宽0.15米
          num1 = parseFloat(match[1])
          num2 = parseFloat(match[3])
          const unit1 = match[2]
          const unit2 = match[4] || unit1 // 第二个单位默认与第一个相同

          console.log(`✅ 中文长宽格式(带单位)匹配: 长${num1}${unit1} × 宽${num2}${unit2}`)

          const length = convertSingleToMillimeters(num1, unit1)
          const width = convertSingleToMillimeters(num2, unit2)

          return { length, width }
        } else {
          // 简单格式：长.34×宽0.3
          num1 = parseFloat(match[1])
          num2 = parseFloat(match[2])
          unit = inferUnit(num1, num2)

          console.log(`✅ 中文长宽格式匹配: 长${num1} × 宽${num2} (推断单位: ${unit})`)

          const length = convertSingleToMillimeters(num1, unit)
          const width = convertSingleToMillimeters(num2, unit)

          return { length, width }
        }
      }

      // 🔧 处理风口类型+数字×数字格式：回风口300✘1500、出风口120✘2750
      if (pattern.source.includes('回风口|出风口|新风口|进风口|发风口|回风|出风|新风|进风|发风')) {
        num1 = parseFloat(match[1])
        num2 = parseFloat(match[2])
        unit = match[3] || inferUnit(num1, num2)

        console.log(`✅ 风口类型格式匹配: ${num1} × ${num2} ${unit}`)

        // 转换为毫米
        const length = convertSingleToMillimeters(num1, unit)
        const width = convertSingleToMillimeters(num2, unit)

        // 应用智能尺寸识别（确保大值作为长度，小值作为宽度）
        const smartResult = smartDimensionRecognition(length, width)

        return {
          length: smartResult.length,
          width: smartResult.width,
          originalInfo: `${smartResult.length}×${smartResult.width}mm`
        }
      }

      // 处理不同的匹配模式
      if (match[2] && (match[2] === 'mm' || match[2] === 'cm' || match[2] === 'm' || match[2] === '毫米' || match[2] === '厘米' || match[2] === '米')) {
        // 等号格式：26米x135=大厅
        num1 = parseFloat(match[1])
        num2 = parseFloat(match[3])
        const unit1 = match[2]
        const unit2 = match[4] || 'mm' // 第二个数字无单位默认为mm

        console.log(`✅ 等号格式匹配: ${num1}${unit1} x ${num2}${unit2}`)

        // 分别转换两个数字
        const length = convertSingleToMillimeters(num1, unit1)
        const width = convertSingleToMillimeters(num2, unit2)

        // 应用智能尺寸识别（确保大值作为长度，小值作为宽度）
        const smartResult = smartDimensionRecognition(length, width)

        return {
          length: smartResult.length,
          width: smartResult.width,
          originalInfo: `${smartResult.length}×${smartResult.width}mm`
        }
      } else {
        // 标准格式：1920x140mm 或 1920x140
        num1 = parseFloat(match[1])
        num2 = parseFloat(match[2])
        unit = match[3] || inferUnit(num1, num2)

        console.log(`✅ 标准格式匹配: ${num1} x ${num2} ${unit}`)

        // 转换为毫米
        const length = convertSingleToMillimeters(num1, unit)
        const width = convertSingleToMillimeters(num2, unit)

        // 应用智能尺寸识别（确保大值作为长度，小值作为宽度）
        const smartResult = smartDimensionRecognition(length, width)

        return {
          length: smartResult.length,
          width: smartResult.width,
          originalInfo: `${smartResult.length}×${smartResult.width}mm`
        }
      }
    }
  }

  // 如果原始字符串匹配失败，尝试预处理后的字符串
  console.log(`🔄 原始匹配失败，尝试预处理...`)
  let preprocessedText = dimensionStr.toLowerCase()

  // 使用统一的分隔符映射进行替换
  for (const [from, to] of Object.entries(DIMENSION_SEPARATORS.NORMALIZE_MAP)) {
    preprocessedText = preprocessedText.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to)
  }

  // 统一x周围的空格
  preprocessedText = preprocessedText.replace(/\s*x\s*/g, 'x')

  console.log(`🔍 预处理后字符串: "${preprocessedText}"`)

  for (const pattern of dimensionPatterns) {
    const match = preprocessedText.match(pattern)
    if (match) {
      let num1, num2, unit
      console.log(`✅ 预处理字符串匹配成功: "${match[0]}" 使用模式: ${pattern.source.substring(0, 50)}...`)

      // 处理圆形风口：直径格式
      if (pattern.source.includes('直径')) {
        const diameter = parseFloat(match[1])
        unit = match[2] || 'mm'
        console.log(`🔵 圆形风口识别: 直径${diameter}${unit}`)

        const diameterInMm = convertSingleToMillimeters(diameter, unit)
        return {
          length: diameterInMm,
          width: diameterInMm,
          originalInfo: `直径${diameterInMm}mm`
        }
      }

      // 处理中文长宽格式：长.34×宽0.3 或 出风兴1.75×宽0.16（支持OCR错误）
      if ((pattern.source.includes('[长兴艮良]') && pattern.source.includes('宽')) ||
          (pattern.source.includes('宽') && pattern.source.includes('回风|出风|新风|进风|发风'))) {
        // 检查是否是带单位的格式
        if (match[2] && (match[2] === 'mm' || match[2] === 'cm' || match[2] === 'm' || match[2] === '毫米' || match[2] === '厘米' || match[2] === '米')) {
          // 带单位格式：长1.2米×宽0.15米
          num1 = parseFloat(match[1])
          num2 = parseFloat(match[3])
          const unit1 = match[2]
          const unit2 = match[4] || unit1 // 第二个单位默认与第一个相同

          console.log(`✅ 中文长宽格式(带单位)匹配: 长${num1}${unit1} × 宽${num2}${unit2}`)

          const length = convertSingleToMillimeters(num1, unit1)
          const width = convertSingleToMillimeters(num2, unit2)

          return { length, width }
        } else {
          // 简单格式：长.34×宽0.3
          num1 = parseFloat(match[1])
          num2 = parseFloat(match[2])
          unit = inferUnit(num1, num2)

          console.log(`✅ 中文长宽格式匹配: 长${num1} × 宽${num2} (推断单位: ${unit})`)

          const length = convertSingleToMillimeters(num1, unit)
          const width = convertSingleToMillimeters(num2, unit)

          // 应用智能尺寸识别（确保大值作为长度，小值作为宽度）
          const smartResult = smartDimensionRecognition(length, width)

          return {
            length: smartResult.length,
            width: smartResult.width,
            originalInfo: `${smartResult.length}×${smartResult.width}mm`
          }
        }
      }

      // 处理其他格式...
      if (match[2] && (match[2] === 'mm' || match[2] === 'cm' || match[2] === 'm' || match[2] === '毫米' || match[2] === '厘米' || match[2] === '米')) {
        // 等号格式：26米x135=大厅
        num1 = parseFloat(match[1])
        num2 = parseFloat(match[3])
        const unit1 = match[2]
        const unit2 = match[4] || 'mm' // 第二个数字无单位默认为mm

        console.log(`✅ 等号格式匹配: ${num1}${unit1} x ${num2}${unit2}`)

        // 分别转换两个数字
        const length = convertSingleToMillimeters(num1, unit1)
        const width = convertSingleToMillimeters(num2, unit2)

        // 应用智能尺寸识别（确保大值作为长度，小值作为宽度）
        const smartResult = smartDimensionRecognition(length, width)

        return {
          length: smartResult.length,
          width: smartResult.width,
          originalInfo: `${smartResult.length}×${smartResult.width}mm`
        }
      } else {
        // 标准格式：1920x140mm 或 1920x140
        num1 = parseFloat(match[1])
        num2 = parseFloat(match[2])

        let length: number, width: number

        if (match[3]) {
          // 有明确单位，两个数字都使用这个单位
          unit = match[3]
          console.log(`✅ 标准格式匹配: ${num1} x ${num2} ${unit}`)

          // 转换为毫米
          length = convertSingleToMillimeters(num1, unit)
          width = convertSingleToMillimeters(num2, unit)
        } else {
          // 无明确单位，分别推断每个数字的单位
          const unit1 = inferUnitForSingleNumber(num1)
          const unit2 = inferUnitForSingleNumber(num2)

          console.log(`✅ 混合单位格式: ${num1}${unit1} x ${num2}${unit2}`)

          // 分别转换为毫米
          length = convertSingleToMillimeters(num1, unit1)
          width = convertSingleToMillimeters(num2, unit2)
        }

        // 应用智能尺寸识别（包含风口业务规则验证）
        const smartResult = smartDimensionRecognition(length, width)

        return {
          length: smartResult.length,
          width: smartResult.width,
          originalInfo: `${smartResult.length}×${smartResult.width}mm`
        }
      }
    }
  }

  // 🔧 最后的备用方案：简单正则表达式解析
  console.log(`🔄 尝试简单备用解析方案...`)
  const simpleMatch = dimensionStr.match(/(\d+(?:\.\d+)?)\s*[xX×*]\s*(\d+(?:\.\d+)?)/i)
  if (simpleMatch) {
    const num1 = parseFloat(simpleMatch[1])
    const num2 = parseFloat(simpleMatch[2])

    if (!isNaN(num1) && !isNaN(num2)) {
      console.log(`✅ 简单备用解析成功: ${num1} x ${num2}`)
      const unit = inferUnit(num1, num2)
      const length = convertSingleToMillimeters(num1, unit)
      const width = convertSingleToMillimeters(num2, unit)
      const smartResult = smartDimensionRecognition(length, width)

      return {
        length: smartResult.length,
        width: smartResult.width,
        originalInfo: `${smartResult.length}×${smartResult.width}mm (简单解析)`
      }
    }
  }

  console.log(`❌ 尺寸解析失败: "${dimensionStr}"`)
  return null
}

/**
 * 为单个数字推断单位 - 根据用户明确的规则
 * 正确规则：
 * 1. 个位数带小数点（如1.6, 2.75, 9.5） → 米(m)
 * 2. 两位数带小数点（如12.5, 24.8, 99.9） → 厘米(cm)
 * 3. 三位数以上整数（如100, 150, 1700） → 毫米(mm)
 * 4. 小于100的整数 → 根据大小判断（≥50为mm，<50为cm）
 * @param num 数字
 * @returns 推断的单位
 */
function inferUnitForSingleNumber(num: number): string {
  console.log(`🔍 单个数字单位推断: ${num}`)

  const hasDecimal = num % 1 !== 0

  if (hasDecimal) {
    // 有小数点的情况 - 根据整数部分位数判断
    const integerPart = Math.floor(num)

    if (integerPart >= 10) {
      // 两位数以上带小数点 → cm
      console.log(`📏 两位数带小数点推断为厘米: ${num} → cm`)
      return 'cm'
    } else {
      // 个位数带小数点 → 米
      console.log(`📏 个位数带小数点推断为米: ${num} → m`)
      return 'm'
    }
  } else {
    // 整数情况
    if (num >= 100) {
      // 三位数以上 → mm
      console.log(`📏 三位数以上推断为毫米: ${num} → mm`)
      return 'mm'
    } else if (num >= 50) {
      console.log(`📏 中等整数值推断为毫米: ${num} → mm`)
      return 'mm'
    } else {
      console.log(`📏 小整数值推断为厘米: ${num} → cm`)
      return 'cm'
    }
  }
}

/**
 * 智能单位推断 - 根据用户明确的规则（兼容旧接口）
 * 正确规则：
 * 1. 个位数带小数点（如1.6, 2.75, 9.5） → 米(m)
 * 2. 两位数带小数点（如12.5, 24.8, 99.9） → 厘米(cm)
 * 3. 三位数以上整数（如100, 150, 1700） → 毫米(mm)
 * 4. 小于100的整数 → 根据大小判断（≥50为mm，<50为cm）
 * @param num1 第一个数字
 * @param num2 第二个数字
 * @returns 推断的单位
 */
function inferUnit(num1: number, num2: number): string {
  console.log(`🔍 单位推断: ${num1} x ${num2}`)

  // 特殊情况：如果数字看起来像是序号.尺寸格式（如1.140），优先按毫米处理
  const num1Str = num1.toString()
  const num2Str = num2.toString()

  // 检查是否为序号.尺寸格式（如1.140表示140mm）
  if (num1Str.includes('.') && num1 < 20 && num2 > 100) {
    // 第一个数字小于20且有小数点，第二个数字大于100，可能是序号.尺寸格式
    const decimalPart = num1Str.split('.')[1]
    if (decimalPart && decimalPart.length === 3) {
      console.log(`🎯 检测到序号.尺寸格式: ${num1} → ${decimalPart}mm`)
      return 'mm'
    }
  }

  // 应用正确的规则进行单位推断
  const numbers = [num1, num2]

  for (const num of numbers) {
    const hasDecimal = num % 1 !== 0

    if (hasDecimal) {
      // 有小数点的情况 - 根据整数部分位数判断
      const integerPart = Math.floor(num)

      if (integerPart >= 10) {
        // 两位数以上带小数点 → cm
        console.log(`📏 两位数带小数点推断为厘米: ${num} → cm`)
        return 'cm'
      } else {
        // 个位数带小数点 → 米
        console.log(`📏 个位数带小数点推断为米: ${num} → m`)
        return 'm'
      }
    } else {
      // 整数情况
      if (num >= 100) {
        // 三位数以上 → mm
        console.log(`📏 三位数以上推断为毫米: ${num} → mm`)
        return 'mm'
      }
    }
  }

  // 🔧 风口尺寸专用逻辑：基于宽度判断单位
  const maxValue = Math.max(num1, num2)
  const minValue = Math.min(num1, num2)

  // 🎯 关键规则：风口宽度最小应该是三位数（毫米单位）
  // 如果宽度是两位数，说明单位是厘米，需要转换为毫米
  // 风口的宽度通常是较小的那个值
  if (minValue < 100) {
    console.log(`📏 宽度<100（两位数），推断整个尺寸为厘米: 宽度=${minValue} → cm (需转换为mm)`)
    return 'cm'
  }

  // 其他情况默认毫米
  console.log(`📏 默认推断为毫米: max=${maxValue} → mm`)
  return 'mm'
}

/**
 * 转换单个数字为毫米
 * @param num 数字
 * @param unit 单位
 * @returns 转换后的毫米值
 */
function convertSingleToMillimeters(num: number, unit: string): number {
  // 🔧 检查NaN值
  if (isNaN(num)) {
    console.log(`⚠️ 单位转换失败: 输入值为NaN`)
    return 0
  }

  let multiplier = 1

  switch (unit.toLowerCase()) {
    case 'cm':
    case '厘米':
      multiplier = 10
      break
    case 'm':
    case '米':
      multiplier = 1000
      break
    case 'mm':
    case '毫米':
    default:
      multiplier = 1
      break
  }

  const result = num * multiplier
  console.log(`🔄 单位转换: ${num}${unit} → ${result}mm`)

  // 🔧 确保结果不是NaN
  return isNaN(result) ? 0 : result
}

/**
 * 转换为毫米（保持原有接口兼容性）
 * @param num1 第一个数字
 * @param num2 第二个数字
 * @param unit 单位
 * @returns 转换后的长宽（毫米）
 */
function convertToMillimeters(num1: number, num2: number, unit: string): { length: number, width: number } {
  const length = convertSingleToMillimeters(num1, unit)
  const width = convertSingleToMillimeters(num2, unit)
  return { length, width }
}

/**
 * 验证尺寸数据
 * @param length 长度
 * @param width 宽度
 * @returns 验证结果
 */
export function validateDimensions(length: number, width: number): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (length < 0) {
    errors.push('长度不能为负数')
  }
  if (width < 0) {
    errors.push('宽度不能为负数')
  }
  
  // 检查尺寸是否合理（最大30米长，10米宽）
  if (length > 30000) {
    errors.push('长度不能超过30000mm')
  }
  if (width > 10000) {
    errors.push('宽度不能超过10000mm')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 格式化尺寸显示
 * @param length 长度
 * @param width 宽度
 * @param unit 单位，默认为'mm'
 * @returns 格式化后的尺寸字符串
 */
export function formatDimensions(length: number, width: number, unit: string = 'mm'): string {
  return `${length || 0}×${width || 0}${unit}`
}

/**
 * 从订单项目中提取尺寸信息（兼容新旧数据结构）
 * @param item 订单项目
 * @returns 尺寸对象
 */
export function extractDimensionsFromItem(item: any): DimensionResult {
  let length = 0
  let width = 0
  let color = ''

  // 优先使用 dimensions 对象
  if (item.dimensions) {
    length = item.dimensions.length || 0
    width = item.dimensions.width || 0
  } else if (item.length && item.width) {
    // 兼容旧版本字段
    length = item.length
    width = item.width
  }

  // 提取颜色信息
  if (item.dimensions && item.dimensions.color) {
    color = item.dimensions.color
  } else if (item.color) {
    color = item.color
  } else {
    color = '象牙白' // 默认颜色
  }

  // 应用智能识别
  const smartDimensions = smartDimensionRecognition(length, width)

  // 生成原始信息字符串
  const originalInfo = length > 0 && width > 0
    ? `${smartDimensions.length}×${smartDimensions.width}`
    : ''

  return {
    ...smartDimensions,
    originalInfo,
    color
  }
}

/**
 * 更新对象的尺寸字段（智能识别）
 * @param updates 要更新的字段对象
 * @param currentLength 当前长度值
 * @param currentWidth 当前宽度值
 * @returns 处理后的更新对象
 */
export function updateDimensionsWithSmartRecognition(
  updates: any,
  currentLength: number = 0,
  currentWidth: number = 0
): any {
  // 如果没有更新长度或宽度，直接返回原对象
  if (updates.length === undefined && updates.width === undefined) {
    return updates
  }

  const processedUpdates = { ...updates }
  
  // 获取新的长度和宽度值
  const newLength = processedUpdates.length !== undefined ? processedUpdates.length : currentLength
  const newWidth = processedUpdates.width !== undefined ? processedUpdates.width : currentWidth
  
  // 应用智能识别
  const smartDimensions = smartDimensionRecognition(newLength, newWidth)
  
  // 如果发生了调整，记录日志
  if (newLength !== smartDimensions.length || newWidth !== smartDimensions.width) {
    console.log(`🔄 智能尺寸识别: ${newLength}×${newWidth} → ${smartDimensions.length}×${smartDimensions.width}`)
  }
  
  // 更新处理后的值
  processedUpdates.length = smartDimensions.length
  processedUpdates.width = smartDimensions.width
  
  return processedUpdates
}
