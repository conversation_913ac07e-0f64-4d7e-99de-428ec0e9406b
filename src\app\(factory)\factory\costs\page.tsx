"use client"

import { useState, use<PERSON>ffe<PERSON> } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { useAuthStore } from "@/lib/store/auth"
import { db } from "@/lib/database/client"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import * as XLSX from 'xlsx'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calculator,
  PieChart,
  Plus,
  Edit,
  Trash2,
  Download,
  AlertTriangle,
  Lock,
  Eye,
  EyeOff
} from "lucide-react"
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ian<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts'
import { AddCostDialog } from "@/components/costs/add-cost-dialog"
import { safeNumber, safePercentage, safeWanYuan, safeLocaleString, safeAmount, safeAmountSum } from '@/lib/utils/number-utils'

// 颜色配置
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

// 成本类型定义
const COST_TYPES = [
  { id: 'material', name: '原材料成本', color: '#ef4444' },
  { id: 'labor', name: '人工成本', color: '#3b82f6' },
  { id: 'equipment', name: '设备折旧', color: '#10b981' },
  { id: 'energy', name: '水电费用', color: '#f59e0b' },
  { id: 'transport', name: '运输费用', color: '#8b5cf6' },
  { id: 'overhead', name: '管理费用', color: '#06b6d4' },
  { id: 'other', name: '其他费用', color: '#84cc16' }
]

interface CostItem {
  id: string
  orderId?: string
  productName?: string
  costType: string
  costName: string
  amount: number
  unit: string
  quantity: number
  totalCost: number
  date: Date
  notes?: string
  createdBy: string
  factoryId: string
}

interface ProfitAnalysis {
  orderId: string
  orderNumber: string
  revenue: number
  totalCosts: number
  grossProfit: number
  grossMargin: number
  netProfit: number
  netMargin: number
  costBreakdown: Record<string, number>
}

export default function CostsPage() {
  const { user } = useAuthStore()
  const [loading, setLoading] = useState(true)
  const [costs, setCosts] = useState<CostItem[]>([])
  const [orders, setOrders] = useState<unknown[]>([])
  const [showAddCost, setShowAddCost] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<string>("")
  const [activeTab, setActiveTab] = useState("overview")
  const [profitAnalysis, setProfitAnalysis] = useState<ProfitAnalysis[]>([])

  // 权限检查 - 只有工厂老板可以访问
  const hasAccess = user && ('role' in user && user.role === 'owner')

  useEffect(() => {
    if (hasAccess) {
      loadData()
    }
  }, [hasAccess])

  const loadData = async () => {
    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()
      
      if (factoryId) {
        // 加载订单数据
        const ordersData = await db.getOrdersByFactoryId(factoryId)
        setOrders(ordersData)
        
        // 加载成本数据（模拟数据）
        const costsData = generateMockCosts(ordersData)
        setCosts(costsData)
        
        // 计算利润分析
        const analysis = calculateProfitAnalysis(ordersData, costsData)
        setProfitAnalysis(analysis)
      }
    } catch (error) {
      console.error('❌ 加载成本数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 生成模拟成本数据
  const generateMockCosts = (orders: unknown[]): CostItem[] => {
    const mockCosts: CostItem[] = []
    
    orders.forEach((order, orderIndex) => {
      order.items?.forEach((item: unknown, itemIndex: number) => {
        // 为每个产品生成多种成本
        const baseCost = item.totalPrice * 0.6 // 假设成本是售价的60%
        
        // 原材料成本 (40-50%的售价)
        mockCosts.push({
          id: `cost-${orderIndex}-${itemIndex}-material`,
          orderId: order.id,
          productName: item.productName,
          costType: 'material',
          costName: `${item.productName} - 原材料`,
          amount: baseCost * 0.7,
          unit: '元',
          quantity: item.quantity,
          totalCost: baseCost * 0.7,
          date: new Date(order.createdAt),
          notes: '铝合金、塑料等原材料成本',
          createdBy: user?.id || 'system',
          factoryId: getCurrentFactoryId() || ''
        })
        
        // 人工成本 (10-15%的售价)
        mockCosts.push({
          id: `cost-${orderIndex}-${itemIndex}-labor`,
          orderId: order.id,
          productName: item.productName,
          costType: 'labor',
          costName: `${item.productName} - 人工费`,
          amount: baseCost * 0.2,
          unit: '元',
          quantity: item.quantity,
          totalCost: baseCost * 0.2,
          date: new Date(order.createdAt),
          notes: '生产加工人工费用',
          createdBy: user?.id || 'system',
          factoryId: getCurrentFactoryId() || ''
        })
        
        // 设备折旧 (3-5%的售价)
        mockCosts.push({
          id: `cost-${orderIndex}-${itemIndex}-equipment`,
          orderId: order.id,
          productName: item.productName,
          costType: 'equipment',
          costName: `${item.productName} - 设备折旧`,
          amount: baseCost * 0.05,
          unit: '元',
          quantity: item.quantity,
          totalCost: baseCost * 0.05,
          date: new Date(order.createdAt),
          notes: '生产设备折旧分摊',
          createdBy: user?.id || 'system',
          factoryId: getCurrentFactoryId() || ''
        })
        
        // 其他费用 (2-3%的售价)
        mockCosts.push({
          id: `cost-${orderIndex}-${itemIndex}-other`,
          orderId: order.id,
          productName: item.productName,
          costType: 'energy',
          costName: `${item.productName} - 水电费`,
          amount: baseCost * 0.03,
          unit: '元',
          quantity: item.quantity,
          totalCost: baseCost * 0.03,
          date: new Date(order.createdAt),
          notes: '生产过程水电费用',
          createdBy: user?.id || 'system',
          factoryId: getCurrentFactoryId() || ''
        })
      })
    })
    
    return mockCosts
  }



  // 计算利润分析
  const calculateProfitAnalysis = (orders: unknown[], costs: CostItem[]): ProfitAnalysis[] => {
    return orders.map(order => {
      const orderCosts = costs.filter(cost => cost.orderId === order.id)
      const totalCosts = orderCosts.reduce((sum, cost) => sum + safeNumber(cost.totalCost), 0)
      const revenue = safeNumber(order.totalAmount)
      const grossProfit = revenue - totalCosts
      const grossMargin = revenue > 0 ? (grossProfit / revenue) * 100 : 0

      // 假设净利润扣除10%的税费和管理费用
      const netProfit = grossProfit * 0.9
      const netMargin = revenue > 0 ? (netProfit / revenue) * 100 : 0

      // 成本分解 - 🔧 修复：使用安全累加函数
      const costBreakdown: Record<string, number> = {}
      COST_TYPES.forEach(type => {
        const typeCosts = orderCosts
          .filter(cost => cost.costType === type.id)
          .map(cost => cost.totalCost)
        costBreakdown[type.id] = safeAmountSum(typeCosts)
      })

      return {
        orderId: order.id,
        orderNumber: order.orderNumber || order.id,
        revenue,
        totalCosts,
        grossProfit,
        grossMargin,
        netProfit,
        netMargin,
        costBreakdown
      }
    })
  }

  // 处理添加成本
  const handleCostAdded = (newCost: CostItem) => {
    setCosts(prev => [...prev, newCost])

    // 重新计算利润分析
    const updatedCosts = [...costs, newCost]
    const analysis = calculateProfitAnalysis(orders, updatedCosts)
    setProfitAnalysis(analysis)

    console.log('✅ 成本添加成功，数据已更新')
  }

  // 导出成本分析报告
  const handleExportCostReport = () => {
    try {
      const wb = XLSX.utils.book_new()

      // 利润分析汇总
      const summaryData = [
        ['成本与利润分析报告', ''],
        ['生成时间', new Date().toLocaleString('zh-CN')],
        ['工厂ID', getCurrentFactoryId()],
        ['', ''],
        ['订单号', '营收(元)', '总成本(元)', '毛利润(元)', '毛利率(%)', '净利润(元)', '净利率(%)'],
        ...profitAnalysis.map(analysis => [
          analysis.orderNumber,
          analysis.revenue,
          analysis.totalCosts,
          analysis.grossProfit,
          analysis.grossMargin.toFixed(2),
          analysis.netProfit,
          analysis.netMargin.toFixed(2)
        ])
      ]

      const summaryWs = XLSX.utils.aoa_to_sheet(summaryData)
      summaryWs['!cols'] = [
        { wch: 20 }, { wch: 15 }, { wch: 15 }, { wch: 15 }, 
        { wch: 12 }, { wch: 15 }, { wch: 12 }
      ]
      XLSX.utils.book_append_sheet(wb, summaryWs, '利润分析汇总')

      // 成本明细
      const costDetailData = [
        ['成本明细', ''],
        ['', ''],
        ['订单号', '产品名称', '成本类型', '成本项目', '单价(元)', '数量', '总成本(元)', '日期', '备注'],
        ...costs.map(cost => [
          cost.orderId,
          cost.productName,
          COST_TYPES.find(t => t.id === cost.costType)?.name || cost.costType,
          cost.costName,
          cost.amount,
          cost.quantity,
          cost.totalCost,
          cost.date.toLocaleDateString('zh-CN'),
          cost.notes || ''
        ])
      ]

      const costDetailWs = XLSX.utils.aoa_to_sheet(costDetailData)
      costDetailWs['!cols'] = [
        { wch: 15 }, { wch: 20 }, { wch: 12 }, { wch: 25 }, 
        { wch: 12 }, { wch: 8 }, { wch: 12 }, { wch: 12 }, { wch: 30 }
      ]
      XLSX.utils.book_append_sheet(wb, costDetailWs, '成本明细')

      const fileName = `成本利润分析报告_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(wb, fileName)

      console.log('✅ 成本分析报告导出成功')
    } catch (error) {
      console.error('❌ 导出成本分析报告失败:', error)
      alert('导出失败，请重试')
    }
  }

  // 权限检查界面
  if (!hasAccess) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="text-center py-12">
            <Lock className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">访问受限</h3>
            <p className="text-gray-600 mb-4">
              成本管理模块仅限工厂老板查看
            </p>
            <p className="text-sm text-gray-500">
              当前角色：{user && 'role' in user ? user.role : '未知'} | 需要角色：owner (工厂老板)
            </p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (loading) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">正在加载成本数据...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // 计算总体统计 - 🔧 修复：使用安全累加函数
  const totalStats = {
    totalRevenue: safeAmountSum(profitAnalysis.map(p => p.revenue)),
    totalCosts: safeAmountSum(profitAnalysis.map(p => p.totalCosts)),
    totalGrossProfit: safeAmountSum(profitAnalysis.map(p => p.grossProfit)),
    totalNetProfit: safeAmountSum(profitAnalysis.map(p => p.netProfit)),
    avgGrossMargin: profitAnalysis.length > 0
      ? safeAmountSum(profitAnalysis.map(p => p.grossMargin)) / profitAnalysis.length
      : 0,
    avgNetMargin: profitAnalysis.length > 0
      ? safeAmountSum(profitAnalysis.map(p => p.netMargin)) / profitAnalysis.length
      : 0
  }

  // 成本结构分析 - 🔧 修复：使用安全累加函数
  const costStructure = COST_TYPES.map(type => ({
    name: type.name,
    value: safeAmountSum(costs
      .filter(cost => cost.costType === type.id)
      .map(cost => cost.totalCost)),
    color: type.color
  })).filter(item => item.value > 0)

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Calculator className="h-6 w-6 mr-2 text-green-600" />
              成本管理
              <Lock className="h-4 w-4 ml-2 text-red-500" />
            </h1>
            <p className="text-gray-600">精确成本控制，科学利润分析</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button onClick={() => setShowAddCost(true)} className="bg-green-600 hover:bg-green-700">
              <Plus className="h-4 w-4 mr-2" />
              添加成本
            </Button>
            <Button onClick={handleExportCostReport} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              导出报告
            </Button>
          </div>
        </div>

        {/* 关键指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总营收</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                ¥{(safeNumber(totalStats.totalRevenue) / 10000).toFixed(1)}万
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总成本</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                ¥{(safeNumber(totalStats.totalCosts) / 10000).toFixed(1)}万
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">毛利润</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                ¥{(safeNumber(totalStats.totalGrossProfit) / 10000).toFixed(1)}万
              </div>
              <p className="text-xs text-muted-foreground">
                毛利率: {safeNumber(totalStats.avgGrossMargin).toFixed(1)}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">净利润</CardTitle>
              <Calculator className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                ¥{(safeNumber(totalStats.totalNetProfit) / 10000).toFixed(1)}万
              </div>
              <p className="text-xs text-muted-foreground">
                净利率: {safeNumber(totalStats.avgNetMargin).toFixed(1)}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">成本率</CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {safeNumber(totalStats.totalRevenue) > 0
                  ? ((safeNumber(totalStats.totalCosts) / safeNumber(totalStats.totalRevenue)) * 100).toFixed(1)
                  : 0}%
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">订单数</CardTitle>
              <Calculator className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-600">
                {profitAnalysis.length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 分析标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">利润概览</TabsTrigger>
            <TabsTrigger value="costs">成本分析</TabsTrigger>
            <TabsTrigger value="orders">订单利润</TabsTrigger>
            <TabsTrigger value="trends">趋势分析</TabsTrigger>
          </TabsList>

          {/* 利润概览 */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 成本结构饼图 */}
              <Card>
                <CardHeader>
                  <CardTitle>成本结构分析</CardTitle>
                  <CardDescription>各类成本占比分布</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={costStructure}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, value }) => `${name}: ¥${(value/10000).toFixed(1)}万`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {costStructure.map((entry, index) => (
                          <Cell key={`cost-structure-cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '成本']} />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 利润率分析 */}
              <Card>
                <CardHeader>
                  <CardTitle>利润率分析</CardTitle>
                  <CardDescription>毛利率与净利率对比</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={profitAnalysis.slice(0, 8)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="orderNumber" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${Number(value).toFixed(1)}%`, '']} />
                      <Legend />
                      <Bar dataKey="grossMargin" fill="#10b981" name="毛利率%" />
                      <Bar dataKey="netMargin" fill="#3b82f6" name="净利率%" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 利润分析提醒 */}
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="p-6">
                <div className="flex items-center space-x-2 text-yellow-600">
                  <AlertTriangle className="h-5 w-5" />
                  <span className="font-medium">利润分析提醒</span>
                </div>
                <div className="mt-2 text-sm text-yellow-600">
                  <p>• 平均毛利率: {safeNumber(totalStats.avgGrossMargin).toFixed(1)}%，建议保持在30%以上</p>
                  <p>• 平均净利率: {safeNumber(totalStats.avgNetMargin).toFixed(1)}%，建议保持在15%以上</p>
                  <p>• 成本控制建议：重点关注原材料成本和人工成本的优化</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 成本分析 */}
          <TabsContent value="costs" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 成本类型排行 */}
              <Card>
                <CardHeader>
                  <CardTitle>成本类型排行</CardTitle>
                  <CardDescription>按成本金额排序</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={costStructure} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={100} />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '成本']} />
                      <Bar dataKey="value" fill="#ef4444" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 成本占比统计 */}
              <Card>
                <CardHeader>
                  <CardTitle>成本占比统计</CardTitle>
                  <CardDescription>各类成本占总成本比例</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {costStructure.map((cost, index) => {
                      const percentage = safeNumber(totalStats.totalCosts) > 0
                        ? ((safeNumber(cost.value) / safeNumber(totalStats.totalCosts)) * 100).toFixed(1)
                        : '0.0'
                      return (
                        <div key={cost.name} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-4 h-4 rounded"
                              style={{ backgroundColor: cost.color }}
                            ></div>
                            <span className="text-sm font-medium">{cost.name}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold">¥{safeNumber(cost.value).toLocaleString()}</div>
                            <div className="text-xs text-gray-500">{percentage}%</div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 成本明细表 */}
            <Card>
              <CardHeader>
                <CardTitle>成本明细</CardTitle>
                <CardDescription>所有成本项目详细列表</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">订单号</th>
                        <th className="text-left p-2">产品名称</th>
                        <th className="text-left p-2">成本类型</th>
                        <th className="text-left p-2">成本项目</th>
                        <th className="text-right p-2">数量</th>
                        <th className="text-right p-2">单价</th>
                        <th className="text-right p-2">总成本</th>
                        <th className="text-left p-2">日期</th>
                      </tr>
                    </thead>
                    <tbody>
                      {costs.slice(0, 20).map((cost) => (
                        <tr key={cost.id} className="border-b hover:bg-gray-50">
                          <td className="p-2 text-sm">{cost.orderId}</td>
                          <td className="p-2 text-sm font-medium">{cost.productName}</td>
                          <td className="p-2">
                            <span
                              className="inline-block w-3 h-3 rounded mr-2"
                              style={{ backgroundColor: COST_TYPES.find(t => t.id === cost.costType)?.color }}
                            ></span>
                            <span className="text-sm">
                              {COST_TYPES.find(t => t.id === cost.costType)?.name}
                            </span>
                          </td>
                          <td className="p-2 text-sm">{cost.costName}</td>
                          <td className="p-2 text-right text-sm">{safeNumber(cost.quantity)}</td>
                          <td className="p-2 text-right text-sm">{safeAmount(cost.amount)}</td>
                          <td className="p-2 text-right text-sm font-bold">{safeAmount(cost.totalCost)}</td>
                          <td className="p-2 text-sm text-gray-600">
                            {cost.date.toLocaleDateString('zh-CN')}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 订单利润 */}
          <TabsContent value="orders" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 订单利润排行 */}
              <Card>
                <CardHeader>
                  <CardTitle>订单利润排行</CardTitle>
                  <CardDescription>按净利润排序</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={profitAnalysis.slice(0, 8)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="orderNumber" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '利润']} />
                      <Bar dataKey="netProfit" fill="#10b981" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 营收成本对比 */}
              <Card>
                <CardHeader>
                  <CardTitle>营收成本对比</CardTitle>
                  <CardDescription>订单营收与成本对比</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={profitAnalysis.slice(0, 8)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="orderNumber" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '']} />
                      <Legend />
                      <Bar dataKey="revenue" fill="#3b82f6" name="营收" />
                      <Bar dataKey="totalCosts" fill="#ef4444" name="成本" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 订单利润详细表 */}
            <Card>
              <CardHeader>
                <CardTitle>订单利润详细分析</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">订单号</th>
                        <th className="text-right p-2">营收(元)</th>
                        <th className="text-right p-2">总成本(元)</th>
                        <th className="text-right p-2">毛利润(元)</th>
                        <th className="text-right p-2">毛利率(%)</th>
                        <th className="text-right p-2">净利润(元)</th>
                        <th className="text-right p-2">净利率(%)</th>
                        <th className="text-center p-2">盈利状态</th>
                      </tr>
                    </thead>
                    <tbody>
                      {profitAnalysis.map((analysis) => (
                        <tr key={analysis.orderId} className="border-b hover:bg-gray-50">
                          <td className="p-2 font-medium">{analysis.orderNumber}</td>
                          <td className="p-2 text-right">¥{safeNumber(analysis.revenue).toLocaleString()}</td>
                          <td className="p-2 text-right text-red-600">¥{safeNumber(analysis.totalCosts).toLocaleString()}</td>
                          <td className="p-2 text-right font-bold">¥{safeNumber(analysis.grossProfit).toLocaleString()}</td>
                          <td className="p-2 text-right">{safeNumber(analysis.grossMargin).toFixed(1)}%</td>
                          <td className="p-2 text-right font-bold text-green-600">
                            ¥{safeNumber(analysis.netProfit).toLocaleString()}
                          </td>
                          <td className="p-2 text-right">{safeNumber(analysis.netMargin).toFixed(1)}%</td>
                          <td className="p-2 text-center">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              safeNumber(analysis.netProfit) > 0
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {safeNumber(analysis.netProfit) > 0 ? '盈利' : '亏损'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 趋势分析 */}
          <TabsContent value="trends" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 利润趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle>利润趋势分析</CardTitle>
                  <CardDescription>毛利润与净利润变化趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={profitAnalysis}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="orderNumber" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '']} />
                      <Legend />
                      <Line type="monotone" dataKey="grossProfit" stroke="#10b981" name="毛利润" strokeWidth={2} />
                      <Line type="monotone" dataKey="netProfit" stroke="#3b82f6" name="净利润" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 成本趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle>成本趋势分析</CardTitle>
                  <CardDescription>总成本变化趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={profitAnalysis}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="orderNumber" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '成本']} />
                      <Line type="monotone" dataKey="totalCosts" stroke="#ef4444" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 趋势分析提醒 */}
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-6">
                <div className="flex items-center space-x-2 text-blue-600">
                  <TrendingUp className="h-5 w-5" />
                  <span className="font-medium">趋势分析建议</span>
                </div>
                <div className="mt-2 text-sm text-blue-600">
                  <p>• 建议定期监控成本变化，及时调整定价策略</p>
                  <p>• 关注利润率波动较大的订单，分析原因并优化</p>
                  <p>• 建立成本预警机制，当成本率超过70%时及时预警</p>
                  <p>• 定期评估供应商价格，寻找成本优化机会</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 添加成本对话框 */}
        <AddCostDialog
          open={showAddCost}
          onOpenChange={setShowAddCost}
          orders={orders}
          onCostAdded={handleCostAdded}
        />
      </div>
    </DashboardLayout>
  )
}
