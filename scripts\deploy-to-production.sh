#!/bin/bash

# 🚀 风口云平台 - 生产环境部署脚本
# 
# 功能说明：
# - 自动部署到阿里云服务器
# - 更新管理员密码
# - 重启服务
# - 验证部署状态

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置信息
SERVER_HOST="**************"
SERVER_USER="root"
PROJECT_PATH="/root/factorysystem"
BACKUP_PATH="/root/backups"
SERVICE_NAME="factorysystem"

echo -e "${BLUE}🚀 风口云平台 - 生产环境部署脚本${NC}"
echo "=================================================="

# 检查本地环境
echo -e "${YELLOW}📋 检查本地环境...${NC}"
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ 错误: 请在项目根目录运行此脚本${NC}"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo -e "${RED}❌ 错误: 未找到 .env 文件${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 本地环境检查通过${NC}"

# 确认部署
echo -e "${YELLOW}⚠️  您即将部署到生产环境服务器: ${SERVER_HOST}${NC}"
echo -e "${YELLOW}📁 部署路径: ${PROJECT_PATH}${NC}"
echo -e "${YELLOW}🔐 管理员密码将更新为: admin@yhg2025${NC}"
echo ""
read -p "确认要继续部署吗？(输入 'YES' 确认): " confirm

if [ "$confirm" != "YES" ]; then
    echo -e "${RED}❌ 部署已取消${NC}"
    exit 1
fi

# 创建备份
echo -e "${BLUE}📦 创建服务器备份...${NC}"
ssh ${SERVER_USER}@${SERVER_HOST} "
    mkdir -p ${BACKUP_PATH}
    BACKUP_NAME=\"factorysystem_backup_\$(date +%Y%m%d_%H%M%S)\"
    echo \"创建备份: \$BACKUP_NAME\"
    
    if [ -d \"${PROJECT_PATH}\" ]; then
        cp -r ${PROJECT_PATH} ${BACKUP_PATH}/\$BACKUP_NAME
        echo \"✅ 备份创建成功: ${BACKUP_PATH}/\$BACKUP_NAME\"
    else
        echo \"⚠️  项目目录不存在，跳过备份\"
    fi
"

# 停止服务
echo -e "${BLUE}🛑 停止现有服务...${NC}"
ssh ${SERVER_USER}@${SERVER_HOST} "
    if systemctl is-active --quiet ${SERVICE_NAME}; then
        systemctl stop ${SERVICE_NAME}
        echo \"✅ 服务已停止\"
    else
        echo \"⚠️  服务未运行\"
    fi
"

# 上传代码
echo -e "${BLUE}📤 上传项目文件...${NC}"
rsync -avz --delete \
    --exclude 'node_modules' \
    --exclude '.next' \
    --exclude '.git' \
    --exclude 'prisma/dev.db*' \
    --exclude '.env.local' \
    --exclude 'tsconfig.tsbuildinfo' \
    ./ ${SERVER_USER}@${SERVER_HOST}:${PROJECT_PATH}/

echo -e "${GREEN}✅ 文件上传完成${NC}"

# 服务器端部署
echo -e "${BLUE}🔧 服务器端部署...${NC}"
ssh ${SERVER_USER}@${SERVER_HOST} "
    cd ${PROJECT_PATH}
    
    echo \"📦 安装依赖...\"
    npm ci --production
    
    echo \"🏗️  构建项目...\"
    npm run build
    
    echo \"🗄️  数据库迁移...\"
    npx prisma generate
    npx prisma db push
    
    echo \"🔐 更新管理员密码...\"
    node scripts/update-admin-password.js <<EOF
1
admin@yhg2025
admin@yhg2025
YES
EOF
    
    echo \"✅ 服务器端部署完成\"
"

# 启动服务
echo -e "${BLUE}🚀 启动服务...${NC}"
ssh ${SERVER_USER}@${SERVER_HOST} "
    systemctl start ${SERVICE_NAME}
    systemctl enable ${SERVICE_NAME}
    
    echo \"⏳ 等待服务启动...\"
    sleep 10
    
    if systemctl is-active --quiet ${SERVICE_NAME}; then
        echo \"✅ 服务启动成功\"
    else
        echo \"❌ 服务启动失败\"
        systemctl status ${SERVICE_NAME}
        exit 1
    fi
"

# 验证部署
echo -e "${BLUE}🔍 验证部署状态...${NC}"
ssh ${SERVER_USER}@${SERVER_HOST} "
    echo \"📊 服务状态:\"
    systemctl status ${SERVICE_NAME} --no-pager -l
    
    echo \"\"
    echo \"🌐 网络检查:\"
    if curl -f -s http://localhost:3000 > /dev/null; then
        echo \"✅ 应用响应正常\"
    else
        echo \"❌ 应用无响应\"
    fi
    
    echo \"\"
    echo \"💾 数据库检查:\"
    cd ${PROJECT_PATH}
    if npx prisma db pull > /dev/null 2>&1; then
        echo \"✅ 数据库连接正常\"
    else
        echo \"❌ 数据库连接失败\"
    fi
"

# 部署完成
echo ""
echo -e "${GREEN}🎉 部署完成！${NC}"
echo "=================================================="
echo -e "${BLUE}📋 部署信息:${NC}"
echo -e "🌐 服务器地址: ${SERVER_HOST}"
echo -e "📁 项目路径: ${PROJECT_PATH}"
echo -e "🔐 管理员账户: admin / admin@yhg2025"
echo -e "🕒 部署时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo -e "${YELLOW}🔒 安全提醒:${NC}"
echo "1. 管理员密码已更新，请妥善保管"
echo "2. 建议定期更换密码"
echo "3. 监控服务器运行状态"
echo "4. 定期备份数据库"
echo ""
echo -e "${GREEN}✅ 部署脚本执行完成${NC}"
