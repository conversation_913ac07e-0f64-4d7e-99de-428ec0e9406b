/**
 * 🇨🇳 风口云平台 - 工厂用户密码验证API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json()

    if (!username || !password) {
      return NextResponse.json(
        { error: '用户名和密码不能为空' },
        { status: 400 }
      )
    }

    // 验证工厂用户密码
    const isValid = await db.verifyFactoryUserPassword(username, password)

    return NextResponse.json({
      success: true,
      data: {
        isValid
      }
    })

  } catch (error) {
    console.error('❌ 验证工厂用户密码失败:', error)
    return NextResponse.json(
      { error: '验证服务异常' },
      { status: 500 }
    )
  }
}
