/**
 * 🇨🇳 风口云平台 - 本地数据库存储模块
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 提供本地存储的数据访问接口
 * - 兼容旧版本的LocalDB调用方式
 * - 实际数据从MySQL数据库获取
 */

import { db } from '@/lib/database'

// 管理员数据接口
interface AdminData {
  id: string
  username: string
  password: string
  name: string
  email: string
  role: string
  isActive: boolean
}

// 工厂用户数据接口
interface FactoryUserData {
  id: string
  factoryId: string
  username: string
  password: string
  name: string
  email?: string
  phone?: string
  role: string
  isActive: boolean
}

// 工厂数据接口
interface FactoryData {
  id: string
  name: string
  code: string
  address?: string
  phone?: string
  email?: string
  status: string
}

/**
 * 本地数据库存储类
 * 提供与旧版本兼容的API接口
 */
export class LocalDB {
  // 管理员数据访问
  static admins = {
    async getAll(): Promise<AdminData[]> {
      try {
        const admins = await db.getAllAdmins()
        return admins.map((admin: unknown) => ({
          id: admin.id,
          username: admin.username,
          password: '••••••••', // 不暴露真实密码
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isActive: admin.isActive
        }))
      } catch (error) {
        console.error('❌ 获取管理员数据失败:', error)
        return []
      }
    },

    async getById(id: string): Promise<AdminData | null> {
      try {
        const admin = await db.getAdminById(id)
        if (!admin) return null
        
        return {
          id: admin.id,
          username: admin.username,
          password: '••••••••',
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isActive: admin.isActive
        }
      } catch (error) {
        console.error('❌ 获取管理员数据失败:', error)
        return null
      }
    }
  }

  // 工厂用户数据访问
  static factoryUsers = {
    async getAll(): Promise<FactoryUserData[]> {
      try {
        const users = await db.getAllFactoryUsers()
        return users.map((user: unknown) => ({
          id: user.id,
          factoryId: user.factoryId,
          username: user.username,
          password: '••••••••', // 不暴露真实密码
          name: user.name,
          email: user.email,
          phone: user.phone,
          role: user.role,
          isActive: user.isActive
        }))
      } catch (error) {
        console.error('❌ 获取工厂用户数据失败:', error)
        return []
      }
    },

    async getByFactoryId(factoryId: string): Promise<FactoryUserData[]> {
      try {
        const users = await db.getEmployeesByFactoryId(factoryId)
        return users.map((user: unknown) => ({
          id: user.id,
          factoryId: user.factoryId,
          username: user.username,
          password: '••••••••',
          name: user.name,
          email: user.email,
          phone: user.phone,
          role: user.role,
          isActive: user.isActive
        }))
      } catch (error) {
        console.error('❌ 获取工厂用户数据失败:', error)
        return []
      }
    }
  }

  // 工厂数据访问
  static factories = {
    async getAll(): Promise<FactoryData[]> {
      try {
        const factories = await db.getFactories()
        return factories.map((factory: unknown) => ({
          id: factory.id,
          name: factory.name,
          code: factory.code,
          address: factory.address,
          phone: factory.phone,
          email: factory.email,
          status: factory.status
        }))
      } catch (error) {
        console.error('❌ 获取工厂数据失败:', error)
        return []
      }
    },

    async getById(id: string): Promise<FactoryData | null> {
      try {
        const factory = await db.getFactoryById(id)
        if (!factory) return null
        
        return {
          id: factory.id,
          name: factory.name,
          code: factory.code,
          address: factory.address,
          phone: factory.phone,
          email: factory.email,
          status: factory.status
        }
      } catch (error) {
        console.error('❌ 获取工厂数据失败:', error)
        return null
      }
    }
  }
}

export default LocalDB
