"use client"

import { useAuthStore } from "@/lib/store/auth"
import { checkDataIsolationStatus } from "@/lib/middleware/data-isolation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export default function DebugAuthPage() {
  const router = useRouter()
  const authState = useAuthStore()
  const isolationStatus = checkDataIsolationStatus()

  const handleGoToDashboard = () => {
    router.push("/factory/dashboard")
  }

  const handleGoToLogin = () => {
    router.push("/factory/login")
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">认证状态调试</h1>
        
        <Card>
          <CardHeader>
            <CardTitle>认证状态 (useAuthStore)</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify({
                isAuthenticated: authState.isAuthenticated,
                role: authState.role,
                factoryId: authState.factoryId,
                user: authState.user ? {
                  id: authState.user.id,
                  name: authState.user.name,
                  username: 'username' in authState.user ? authState.user.username : 'N/A',
                  userType: 'userType' in authState.user ? authState.user.userType : 'N/A',
                  factoryId: 'factoryId' in authState.user ? authState.user.factoryId : 'N/A'
                } : null,
                hasAccessToken: !!authState.accessToken,
                tokenType: authState.tokenType,
                expiresIn: authState.expiresIn
              }, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>数据隔离状态</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(isolationStatus, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>localStorage 状态</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify({
                accessToken: typeof window !== 'undefined' ? localStorage.getItem('accessToken')?.substring(0, 50) + '...' : 'N/A',
                refreshToken: typeof window !== 'undefined' ? localStorage.getItem('refreshToken')?.substring(0, 50) + '...' : 'N/A',
                user: typeof window !== 'undefined' ? localStorage.getItem('user') : 'N/A',
                role: typeof window !== 'undefined' ? localStorage.getItem('role') : 'N/A',
                factoryId: typeof window !== 'undefined' ? localStorage.getItem('factoryId') : 'N/A'
              }, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <div className="flex space-x-4">
          <Button onClick={handleGoToDashboard}>
            前往仪表板
          </Button>
          <Button variant="outline" onClick={handleGoToLogin}>
            前往登录页
          </Button>
        </div>
      </div>
    </div>
  )
}
