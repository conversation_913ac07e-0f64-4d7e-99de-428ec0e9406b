/**
 * 🇨🇳 风口云平台 - 单个股东操作API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 获取单个股东信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: shareholderId } = await params

    if (!shareholderId) {
      return NextResponse.json(
        { error: '股东ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📞 获取股东信息请求:', shareholderId)

    // 获取股东信息
    const shareholder = await db.getShareholderById(shareholderId)

    if (!shareholder) {
      return NextResponse.json(
        { error: '股东不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      shareholder
    })

  } catch (error) {
    console.error('❌ 获取股东信息失败:', error)
    return NextResponse.json(
      { error: '获取股东信息失败' },
      { status: 500 }
    )
  }
}

// 更新单个股东信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: shareholderId } = await params
    const updates = await request.json()

    if (!shareholderId) {
      return NextResponse.json(
        { error: '股东ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📞 更新股东信息请求:', shareholderId)

    // 更新股东信息
    const shareholder = await db.updateShareholder(shareholderId, updates)

    if (!shareholder) {
      return NextResponse.json(
        { error: '更新股东信息失败' },
        { status: 500 }
      )
    }

    console.log('✅ 股东信息更新成功:', shareholder.name)

    return NextResponse.json({
      success: true,
      shareholder
    })

  } catch (error) {
    console.error('❌ 更新股东信息失败:', error)
    
    // 处理特定错误
    if ((error as Error).message && (error as Error).message.includes('股权比例')) {
      return NextResponse.json(
        { error: (error as Error).message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: '更新股东信息失败' },
      { status: 500 }
    )
  }
}

// 删除单个股东
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: shareholderId } = await params

    if (!shareholderId) {
      return NextResponse.json(
        { error: '股东ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📞 删除股东请求:', shareholderId)

    // 删除股东
    const success = await db.deleteShareholder(shareholderId)

    if (!success) {
      return NextResponse.json(
        { error: '删除股东失败' },
        { status: 500 }
      )
    }

    console.log('✅ 股东删除成功')

    return NextResponse.json({
      success: true
    })

  } catch (error) {
    console.error('❌ 删除股东失败:', error)
    return NextResponse.json(
      { error: '删除股东失败' },
      { status: 500 }
    )
  }
}
