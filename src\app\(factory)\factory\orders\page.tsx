"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button  } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from "@/lib/database/client"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import { safeAmount, safeWanYuan } from "@/lib/utils/number-utils"
import type { Order } from "@/types"
import * as XLSX from 'xlsx'
import {
  ShoppingCart,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  MoreHorizontal,
  User,
  Calendar,
  Package,
  DollarSign,
  Trash2
} from "lucide-react"
import { PaymentStatusDialog } from "@/components/orders/payment-status-dialog"
import OrderStatusDialog from "@/components/orders/order-status-dialog"
import { DeleteOrderDialog } from "@/components/orders/delete-order-dialog"
import { FactoryAnnouncements } from "@/components/announcements/factory-announcements"

// 模拟订单数据
const mockOrders = [
  {
    id: "ORD-2024-001",
    clientName: "上海建筑公司",
    clientPhone: "13800138000",
    itemCount: 3,
    totalQuantity: 25,
    totalAmount: 15800,
    status: "pending" as const,
    createdBy: "张小明",
    createdByRole: "operator",
    createdAt: new Date("2024-01-15T09:30:00"),
    updatedAt: new Date("2024-01-15T09:30:00"),
    items: [
      { productName: "方形风口", specifications: "600×400mm", quantity: 10, unitPrice: 580, totalPrice: 5800 },
      { productName: "圆形风口", specifications: "Φ300mm", quantity: 8, unitPrice: 650, totalPrice: 5200 },
      { productName: "矩形风口", specifications: "800×200mm", quantity: 7, unitPrice: 680, totalPrice: 4760 }
    ]
  },
  {
    id: "ORD-2024-002",
    clientName: "北京装饰工程",
    clientPhone: "13800138001",
    itemCount: 2,
    totalQuantity: 18,
    totalAmount: 23400,
    status: "production" as const,
    createdBy: "李经理",
    createdByRole: "manager",
    createdAt: new Date("2024-01-14T14:20:00"),
    updatedAt: new Date("2024-01-14T16:45:00"),
    items: [
      { productName: "定制风口", specifications: "1200×600mm", quantity: 12, unitPrice: 1200, totalPrice: 14400 },
      { productName: "方形风口", specifications: "500×500mm", quantity: 6, unitPrice: 1500, totalPrice: 9000 }
    ]
  },
  {
    id: "ORD-2024-003",
    clientName: "广州空调安装",
    clientPhone: "13800138002",
    itemCount: 1,
    totalQuantity: 15,
    totalAmount: 8900,
    status: "completed" as const,
    createdBy: "王小红",
    createdByRole: "operator",
    createdAt: new Date("2024-01-13T11:15:00"),
    updatedAt: new Date("2024-01-13T17:30:00"),
    items: [
      { productName: "圆形风口", specifications: "Φ250mm", quantity: 15, unitPrice: 593, totalPrice: 8900 }
    ]
  },
  {
    id: "ORD-2024-004",
    clientName: "深圳建设集团",
    clientPhone: "13800138003",
    itemCount: 4,
    totalQuantity: 32,
    totalAmount: 45600,
    status: "completed" as const,
    createdBy: "张小明",
    createdByRole: "operator",
    createdAt: new Date("2024-01-12T08:45:00"),
    updatedAt: new Date("2024-01-12T18:20:00"),
    items: [
      { productName: "方形风口", specifications: "700×500mm", quantity: 10, unitPrice: 1200, totalPrice: 12000 },
      { productName: "圆形风口", specifications: "Φ400mm", quantity: 8, unitPrice: 980, totalPrice: 7840 },
      { productName: "矩形风口", specifications: "1000×300mm", quantity: 6, unitPrice: 1560, totalPrice: 9360 },
      { productName: "定制风口", specifications: "特殊规格", quantity: 8, unitPrice: 2050, totalPrice: 16400 }
    ]
  }
]

// 根据付款状态获取有效的订单状态
const getEffectiveStatus = (order: unknown) => {
  // 如果付款状态为部分付款或已付清，则视为已完成
  if (order.paymentStatus === 'partial' || order.paymentStatus === 'paid') {
    return 'completed'
  }
  // 否则使用原始状态
  return order.status
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'text-yellow-600 bg-yellow-100'
    case 'production': return 'text-blue-600 bg-blue-100'
    case 'completed': return 'text-green-600 bg-green-100'
    case 'cancelled': return 'text-red-600 bg-red-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待确认'
    case 'production': return '生产中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

const getRoleColor = (role: string) => {
  switch (role) {
    case 'manager': return 'text-blue-600'
    case 'operator': return 'text-green-600'
    default: return 'text-gray-600'
  }
}

// 获取付款状态颜色
const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case 'paid': return 'bg-green-100 text-green-800'
    case 'partial': return 'bg-yellow-100 text-yellow-800'
    case 'unpaid': return 'bg-red-100 text-red-800'
    case 'overdue': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

// 获取付款状态文本
const getPaymentStatusText = (status: string) => {
  switch (status) {
    case 'paid': return '已付清'
    case 'partial': return '部分付款'
    case 'unpaid': return '未付款'
    case 'overdue': return '逾期'
    default: return '未知'
  }
}

// 格式化录单员显示
const formatCreatedBy = (order: unknown): string => {
  const name = order.createdByName
  const account = order.createdBy

  // 清理账号ID，提取简短的用户名
  const cleanAccount = (rawAccount: string): string => {
    if (!rawAccount) return ''

    // 处理括号格式：lz001(cmbw1e306001sunaccm8wln16) -> lz001
    if (rawAccount.includes('(')) {
      return rawAccount.split('(')[0]
    }

    // 处理长ID，只保留前面的简短部分
    if (rawAccount.length > 10) {
      // 尝试按分隔符分割
      const parts = rawAccount.split(/[_\-\.]/);
      if (parts.length > 1 && parts[0].length <= 8) {
        return parts[0]
      }
      // 如果没有分隔符，截取前8个字符
      return rawAccount.substring(0, 8)
    }

    return rawAccount
  }

  // 如果有姓名，优先显示姓名
  if (name && name.trim()) {
    // 如果有账号且与姓名不同，显示格式：姓名(账号)
    if (account && account.trim() && account !== name) {
      const shortAccount = cleanAccount(account)
      return shortAccount ? `${name}(${shortAccount})` : name
    }
    return name
  }

  // 如果没有姓名，只显示清理后的账号
  if (account && account.trim()) {
    return cleanAccount(account) || '未知录单员'
  }

  return '未知录单员'
}

export default function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [clientsMap, setClientsMap] = useState<Record<string, any>>({})
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)

  // 加载订单数据
  useEffect(() => {
    // 延迟加载，确保数据库服务初始化完成
    const timer = setTimeout(() => {
      loadOrders()
    }, 100)

    // 设置自动刷新机制，每30秒检查一次数据变化
    const interval = setInterval(() => {
      checkForDataUpdates()
    }, 30000)

    return () => {
      clearTimeout(timer)
      clearInterval(interval)
    }
  }, [])

  // 检查数据更新（已移除localStorage依赖）
  const checkForDataUpdates = async () => {
    try {
      // 简单的定时刷新，不依赖localStorage
      console.log('🔄 定时刷新订单数据...')
      await loadOrders()
      setLastSyncTime(new Date())
    } catch (error) {
      console.error('❌ 检查数据更新失败:', error)
    }
  }

  const loadOrders = async () => {
    try {
      setLoading(true)
      console.log('🔄 开始加载订单...')

      const factoryId = getCurrentFactoryId()
      console.log('📋 工厂ID:', factoryId)

      if (factoryId) {
        // 检查数据库服务是否可用
        if (!db || typeof db.getOrdersByFactoryId !== 'function') {
          console.warn('⚠️ 数据库服务不可用，使用模拟数据')
          setOrders(mockOrders)
          return
        }

        console.log('🗄️ 调用数据库服务获取订单...')
        const factoryOrders = await db.getOrdersByFactoryId(factoryId)
        console.log('✅ 订单加载成功:', factoryOrders)

        // 🔍 调试：检查每个订单的数量统计
        factoryOrders.forEach((order, index) => {
          const itemsArray = Array.isArray(order.items) ? order.items : []
          const totalQuantity = itemsArray.reduce((sum, item) => sum + (item.quantity || 0), 0)
          const itemCount = itemsArray.length

          console.log(`📦 订单 ${index + 1} (${order.orderNumber || order.id}):`)
          console.log(`   - 项目种类: ${itemCount}`)
          console.log(`   - 总数量: ${totalQuantity}`)
          console.log(`   - 项目详情:`, itemsArray.map(item => ({
            name: item.productName || item.type,
            quantity: item.quantity || 0,
            specs: item.specifications
          })))

          if (totalQuantity !== itemCount) {
            console.log(`   ⚠️ 注意：此订单的总数量(${totalQuantity})与项目种类数(${itemCount})不同`)
          }
        })

        setOrders(factoryOrders)
      } else {
        console.warn('⚠️ 无法获取工厂ID，使用模拟数据')
        setOrders(mockOrders)
      }
    } catch (error) {
      console.error('❌ 加载订单失败:', error)
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      // 回退到模拟数据
      console.log('🔄 回退到模拟数据')
      setOrders(mockOrders)
    } finally {
      setLoading(false)
    }
  }

  // 处理付款状态更新
  const handlePaymentUpdated = (updatedOrder: Order) => {
    setOrders(prev => prev.map(order =>
      order.id === updatedOrder.id ? updatedOrder : order
    ))
  }

  // 处理付款管理
  const handleManagePayment = (order: Order) => {
    setSelectedOrder(order)
    setShowPaymentDialog(true)
  }

  // 处理订单状态编辑
  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order)
    setShowEditDialog(true)
  }

  // 处理订单状态更新回调
  const handleOrderUpdated = (updatedOrder: Order) => {
    setOrders(prev => prev.map(order =>
      order.id === updatedOrder.id ? updatedOrder : order
    ))
    console.log('✅ 订单状态已更新:', updatedOrder.id)
  }

  // 处理删除订单
  const handleDeleteOrder = (order: Order) => {
    setSelectedOrder(order)
    setShowDeleteDialog(true)
  }

  // 处理订单删除成功回调
  const handleOrderDeleted = () => {
    // 重新加载订单列表
    loadOrders()
    console.log('✅ 订单已删除，重新加载列表')
  }

  // 导出订单数据为Excel（美化版本）
  const handleExportOrders = async () => {
    try {
      // 动态导入ExcelJS
      const ExcelJS = await import('exceljs')

      // 创建工作簿
      const workbook = new ExcelJS.Workbook()
      workbook.creator = '风口云平台'
      workbook.created = new Date()

      // 订单汇总工作表
      const summarySheet = workbook.addWorksheet('订单汇总', {
        pageSetup: { paperSize: 9, orientation: 'landscape' }
      })

      // 设置标题
      summarySheet.mergeCells('A1:J1')
      const titleCell = summarySheet.getCell('A1')
      titleCell.value = '订单汇总报表'
      titleCell.font = { name: '微软雅黑', size: 18, bold: true, color: { argb: 'FF1F4E79' } }
      titleCell.alignment = { horizontal: 'center', vertical: 'middle' }
      titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF2F2F2' } }
      summarySheet.getRow(1).height = 30

      // 统计信息
      const statsData = [
        ['总订单数量', stats.total],
        ['待确认订单', stats.pending],
        ['生产中订单', stats.production],
        ['已完成订单', stats.completed],
        ['总订单金额', `¥${safeNumber(stats.totalAmount).toLocaleString()}`],
        ['导出时间', new Date().toLocaleString('zh-CN')]
      ]

      statsData.forEach((stat, index) => {
        const row = summarySheet.getRow(index + 3)
        row.getCell(1).value = stat[0]
        row.getCell(2).value = stat[1]
        row.getCell(1).font = { bold: true, color: { argb: 'FF1F4E79' } }
        row.getCell(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } }
      })

      // 表头
      const headerRow = summarySheet.getRow(10)
      const headers = ['订单号', '客户名称', '客户电话', '项目地址', '产品种类', '总数量', '订单金额(元)', '付款状态', '订单状态', '录单员', '创建时间']
      headers.forEach((header, index) => {
        const cell = headerRow.getCell(index + 1)
        cell.value = header
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } }
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
      headerRow.height = 25

      // 数据行
      filteredOrders.forEach((order, index) => {
        const row = summarySheet.getRow(index + 11)
        const rowData = [
          order.orderNumber || order.id,
          order.clientName || `客户ID: ${order.clientId}`,
          order.clientPhone || '未提供',
          order.projectAddress || '未指定项目地址',
          order.items.length,
          order.items.reduce((sum, item) => sum + item.quantity, 0),
          safeNumber(order.totalAmount),
          getPaymentStatusText(order.paymentStatus || 'unpaid'),
          getStatusText(getEffectiveStatus(order)),
          formatCreatedBy(order),
          (() => {
            try {
              const date = order.createdAt instanceof Date ? order.createdAt : new Date(order.createdAt)
              return date.toLocaleString('zh-CN')
            } catch (error) {
              return '日期格式错误'
            }
          })()
        ]

        rowData.forEach((data, cellIndex) => {
          const cell = row.getCell(cellIndex + 1)
          cell.value = data
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }

          // 金额列格式化
          if (cellIndex === 5) {
            cell.numFmt = '#,##0.00'
          }

          // 状态列颜色
          if (cellIndex === 7) {
            const status = getEffectiveStatus(order)
            switch (status) {
              case 'completed':
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFD4EDDA' } }
                break
              case 'production':
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFEAA7' } }
                break
              case 'pending':
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8D7DA' } }
                break
            }
          }
        })

        // 交替行颜色
        if (index % 2 === 1) {
          row.eachCell((cell) => {
            if (!cell.fill || !cell.fill.fgColor) {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
            }
          })
        }
      })

      // 设置列宽
      summarySheet.columns = [
        { width: 20 }, // 订单号
        { width: 15 }, // 客户名称
        { width: 15 }, // 客户电话
        { width: 12 }, // 产品种类
        { width: 10 }, // 总数量
        { width: 15 }, // 订单金额
        { width: 12 }, // 付款状态
        { width: 12 }, // 订单状态
        { width: 15 }, // 录单员
        { width: 20 }  // 创建时间
      ]

      // 导出Excel文件
      const fileName = `订单数据_${new Date().toISOString().split('T')[0]}.xlsx`
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.click()
      window.URL.revokeObjectURL(url)

      console.log('✅ 订单数据导出成功 (美化Excel格式)')
    } catch (error) {
      console.error('❌ 导出订单数据失败:', error)
      alert('导出失败，请重试')
    }
  }

  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      (order.orderNumber || order.id).toLowerCase().includes(searchTerm.toLowerCase()) ||
      formatCreatedBy(order).toLowerCase().includes(searchTerm.toLowerCase()) ||
      (order.clientName || order.clientId || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (order.clientPhone || '').includes(searchTerm)

    // 使用有效状态进行筛选
    const effectiveStatus = getEffectiveStatus(order)
    const matchesStatus = statusFilter === "all" || effectiveStatus === statusFilter

    return matchesSearch && matchesStatus
  })

  // 安全数值转换函数
  const safeNumber = (value: unknown): number => {
    if (value === null || value === undefined) return 0
    if (typeof value === 'number') return isNaN(value) ? 0 : value
    if (typeof value === 'string') {
      const parsed = parseFloat(value)
      return isNaN(parsed) ? 0 : parsed
    }
    // 处理Decimal类型
    if (value && typeof value.toNumber === 'function') {
      const num = value.toNumber()
      return isNaN(num) ? 0 : num
    }
    return 0
  }

  const stats = {
    total: orders.length,
    pending: orders.filter(o => getEffectiveStatus(o) === 'pending').length,
    production: orders.filter(o => getEffectiveStatus(o) === 'production').length,
    completed: orders.filter(o => getEffectiveStatus(o) === 'completed').length,
    totalAmount: orders.reduce((sum, o) => sum + safeNumber(o.totalAmount), 0)
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* 总部公告推送 */}
        <FactoryAnnouncements />

        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">订单管理</h1>
            <p className="text-gray-600">管理工厂的所有订单信息</p>
          </div>
          <Link href="/factory/orders/create-table">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 shadow-lg">
              <Plus className="h-5 w-5 mr-2" />
              智能录单系统
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总订单</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <ShoppingCart className="h-8 w-8 text-gray-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">待确认</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
                </div>
                <Package className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">生产中</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.production}</p>
                </div>
                <Package className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">已完成</p>
                  <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
                </div>
                <Package className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总金额</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {safeWanYuan(stats.totalAmount)}
                  </p>
                </div>
                <Package className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索订单号、客户名称、电话或录单员..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">全部状态</option>
                <option value="pending">待确认</option>
                <option value="production">生产中</option>
                <option value="completed">已完成</option>
                <option value="cancelled">已取消</option>
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                筛选
              </Button>
              <Button variant="outline" onClick={handleExportOrders}>
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.map((order) => (
            <Card key={order.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-3">
                      <h3 className="text-lg font-semibold text-gray-900">{order.orderNumber || order.id}</h3>
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(getEffectiveStatus(order))}`}>
                        {getStatusText(getEffectiveStatus(order))}
                      </span>
                      {/* 如果有效状态与原始状态不同，显示原始状态 */}
                      {getEffectiveStatus(order) !== order.status && (
                        <span className="text-xs text-gray-500">
                          (原状态: {getStatusText(order.status)})
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">客户信息</p>
                        <p className="font-medium text-blue-600">
                          {order.clientName || `客户ID: ${order.clientId}`}
                        </p>
                        <p className="text-gray-500">
                          {order.clientPhone || '电话未提供'}
                        </p>
                      </div>

                      <div>
                        <p className="text-gray-600">项目地址</p>
                        <p className="font-medium text-green-600">
                          {order.projectAddress || '未指定项目地址'}
                        </p>
                        {order.notes && (
                          <p className="text-gray-500 text-xs mt-1">
                            备注: {order.notes}
                          </p>
                        )}
                      </div>

                      <div>
                        <p className="text-gray-600">订单详情</p>
                        <p className="font-medium">{order.items.length} 种产品</p>
                        <p className="text-gray-500">
                          共 {order.items.reduce((sum, item) => sum + (item.quantity || 0), 0)} 件
                          {/* 调试信息 */}
                          {process.env.NODE_ENV === 'development' && (
                            <span className="text-xs text-blue-600 ml-2">
                              [调试: {order.items.map(item => `${item.quantity || 0}`).join('+')}]
                            </span>
                          )}
                        </p>
                      </div>

                      <div>
                        <p className="text-gray-600">录单信息</p>
                        <p className="font-medium text-blue-600">
                          {formatCreatedBy(order)}
                        </p>
                        <p className="text-gray-500">
                          {(() => {
                            try {
                              const date = order.createdAt instanceof Date ? order.createdAt : new Date(order.createdAt)
                              return `${date.toLocaleDateString()} ${date.toLocaleTimeString().slice(0, 5)}`
                            } catch (error) {
                              return '日期格式错误'
                            }
                          })()}
                        </p>
                      </div>

                      <div>
                        <p className="text-gray-600">订单金额</p>
                        <p className="font-bold text-lg text-green-600">
                          {safeAmount(order.totalAmount)}
                        </p>
                        {/* 付款状态 */}
                        <div className="mt-1">
                          <span className={`text-xs px-2 py-1 rounded-full ${getPaymentStatusColor(order.paymentStatus || 'unpaid')}`}>
                            {getPaymentStatusText(order.paymentStatus || 'unpaid')}
                          </span>
                        </div>
                        {/* 付款信息 */}
                        {safeNumber(order.paidAmount) > 0 && (
                          <div className="text-xs text-gray-600 mt-1">
                            已付：{safeAmount(order.paidAmount)}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 订单项目预览 */}
                    <div className="mt-4 pt-4 border-t">
                      <p className="text-sm text-gray-600 mb-2">订单项目:</p>
                      <div className="flex flex-wrap gap-2">
                        {order.items.map((item, index) => (
                          <span
                            key={index}
                            className="text-xs bg-gray-100 px-2 py-1 rounded"
                          >
                            {item.productName} {item.specifications} ×{item.quantity}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center space-x-2 ml-6">
                    <Link href={`/factory/orders/${order.id}`}>
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        查看
                      </Button>
                    </Link>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleManagePayment(order)}
                    >
                      <DollarSign className="h-4 w-4 mr-1" />
                      付款
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditOrder(order)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      编辑
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteOrder(order)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      删除
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredOrders.length === 0 && (
          <div className="text-center py-12">
            <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到订单</h3>
            <p className="text-gray-600">尝试调整搜索条件或创建新的订单</p>
          </div>
        )}

        {/* 付款状态管理对话框 */}
        <PaymentStatusDialog
          order={selectedOrder}
          open={showPaymentDialog}
          onOpenChange={setShowPaymentDialog}
          onPaymentUpdated={handlePaymentUpdated}
        />

        {/* 订单状态编辑对话框 */}
        <OrderStatusDialog
          order={selectedOrder}
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          onOrderUpdated={handleOrderUpdated}
        />

        {/* 删除订单对话框 */}
        <DeleteOrderDialog
          order={selectedOrder}
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          onOrderDeleted={handleOrderDeleted}
        />
      </div>
    </DashboardLayout>
  )
}
