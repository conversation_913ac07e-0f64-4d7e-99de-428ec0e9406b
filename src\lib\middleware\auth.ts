/**
 * 🇨🇳 风口云平台 - 认证中间件
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - API路由认证保护
 * - 权限验证
 * - 数据隔离检查
 */

import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest, hasPermission, canAccessFactory, type JWTPayload } from '@/lib/auth/jwt'

// 认证中间件选项
export interface AuthMiddlewareOptions {
  requiredRole?: string
  requireFactoryAccess?: boolean
  allowAdmin?: boolean
}

// 认证中间件结果
export interface AuthMiddlewareResult {
  success: boolean
  user?: JWTPayload
  response?: NextResponse
}

/**
 * 认证中间件
 * 验证用户身份和权限
 */
export function withAuth(
  handler: (request: NextRequest, user: JWTPayload) => Promise<NextResponse>,
  options: AuthMiddlewareOptions = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      console.log('🔐 认证中间件开始验证:', request.url)

      // 验证用户身份
      const authResult = getUserFromRequest(request)

      console.log('🔍 认证结果:', authResult.success ? '成功' : '失败', authResult.error || '')

      if (!authResult.success || !authResult.user) {
        console.log('❌ 认证失败:', authResult.error)
        return NextResponse.json(
          {
            error: '认证失败',
            message: authResult.error || '无效的认证令牌'
          },
          { status: 401 }
        )
      }

      const user = authResult.user
      console.log('👤 认证用户:', user.username, '类型:', user.userType, '角色:', user.role)

      // 检查角色权限
      if (options.requiredRole && !hasPermission(user, options.requiredRole)) {
        console.log('🔍 权限检查:', '需要角色:', options.requiredRole, '用户角色:', user.role, '用户类型:', user.userType)
        // 如果允许管理员访问且用户是管理员，则通过
        if (!(options.allowAdmin && user.userType === 'admin')) {
          console.log('❌ 权限不足:', `需要 ${options.requiredRole} 权限`)
          return NextResponse.json(
            {
              error: '权限不足',
              message: `需要 ${options.requiredRole} 权限`
            },
            { status: 403 }
          )
        }
        console.log('✅ 管理员权限通过')
      }

      // 检查工厂访问权限
      if (options.requireFactoryAccess) {
        const url = new URL(request.url)
        const factoryId = url.searchParams.get('factoryId') || 
                         request.headers.get('x-factory-id')

        if (factoryId && !canAccessFactory(user, factoryId)) {
          return NextResponse.json(
            { 
              error: '访问被拒绝',
              message: '无权访问该工厂数据'
            },
            { status: 403 }
          )
        }
      }

      // 调用实际的处理函数
      return await handler(request, user)

    } catch (error) {
      console.error('❌ 认证中间件错误:', error)
      return NextResponse.json(
        { 
          error: '服务器错误',
          message: '认证服务异常'
        },
        { status: 500 }
      )
    }
  }
}

/**
 * 管理员权限中间件
 */
export function withAdminAuth(
  handler: (request: NextRequest, user: JWTPayload) => Promise<NextResponse>
) {
  return async (request: NextRequest) => {
    console.log('🔐 管理员认证中间件开始验证:', request.url)

    try {
      // 直接验证JWT token，不依赖会话验证
      const authResult = getUserFromRequest(request)

      if (!authResult.success || !authResult.user) {
        console.log('❌ JWT认证失败:', authResult.error)
        return NextResponse.json(
          {
            error: '认证失败',
            message: authResult.error || '无效的认证令牌'
          },
          { status: 401 }
        )
      }

      const user = authResult.user

      // 确保用户是管理员类型
      if (user.userType !== 'admin') {
        console.log('❌ 非管理员用户尝试访问管理员API:', user.userType, user.username)
        return NextResponse.json(
          {
            error: '权限不足',
            message: '需要管理员权限'
          },
          { status: 403 }
        )
      }

      console.log('✅ 管理员权限验证通过:', user.username, '角色:', user.role)
      const result = await handler(request, user)
      console.log('🔐 管理员认证中间件完成，状态:', result.status)
      return result

    } catch (error) {
      console.error('❌ 管理员认证中间件错误:', error)
      return NextResponse.json(
        {
          error: '服务器错误',
          message: '认证服务异常'
        },
        { status: 500 }
      )
    }
  }
}

/**
 * 工厂用户权限中间件
 */
export function withFactoryAuth(
  handler: (request: NextRequest, user: JWTPayload) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireFactoryAccess: true,
    allowAdmin: true
  })
}

/**
 * 工厂管理员权限中间件
 */
export function withFactoryManagerAuth(
  handler: (request: NextRequest, user: JWTPayload) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requiredRole: 'manager',
    requireFactoryAccess: true,
    allowAdmin: true
  })
}

/**
 * 管理员或工厂用户权限中间件（用于数据访问）
 */
export function withAdminOrFactoryAuth(
  handler: (request: NextRequest, user: JWTPayload) => Promise<NextResponse>
) {
  return withAuth(handler, {
    allowAdmin: true,
    requireFactoryAccess: false // 管理员不需要工厂访问检查
  })
}

/**
 * 验证请求中的工厂ID
 */
export function validateFactoryAccess(request: NextRequest, user: JWTPayload): string | null {
  // 从URL参数或请求头中获取工厂ID
  const url = new URL(request.url)
  const factoryId = url.searchParams.get('factoryId') ||
                   request.headers.get('x-factory-id') ||
                   url.pathname.match(/\/factories\/([^\/]+)/)?.[1]

  if (!factoryId) {
    return null
  }

  // 管理员可以访问所有工厂数据，直接返回工厂ID
  if (user.userType === 'admin') {
    return factoryId
  }

  // 检查访问权限
  if (!canAccessFactory(user, factoryId)) {
    return null
  }

  return factoryId
}

/**
 * 创建认证错误响应
 */
export function createAuthErrorResponse(message: string, status: number = 401): NextResponse {
  return NextResponse.json(
    {
      error: '认证失败',
      message,
      timestamp: new Date().toISOString()
    },
    { status }
  )
}

/**
 * 创建权限错误响应
 */
export function createPermissionErrorResponse(message: string): NextResponse {
  return NextResponse.json(
    {
      error: '权限不足',
      message,
      timestamp: new Date().toISOString()
    },
    { status: 403 }
  )
}

/**
 * 检查API密钥（用于内部服务调用）
 */
export function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('x-api-key')
  const validApiKey = process.env.INTERNAL_API_KEY
  
  return Boolean(apiKey && validApiKey && apiKey === validApiKey)
}

/**
 * 内部API中间件（用于服务间调用）
 */
export function withInternalAuth(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    if (!validateApiKey(request)) {
      return NextResponse.json(
        { error: '无效的API密钥' },
        { status: 401 }
      )
    }

    return await handler(request)
  }
}
