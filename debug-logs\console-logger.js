/**
 * 控制台日志收集器
 * 用于收集和分析前端错误日志
 */

(function() {
  'use strict';
  
  // 防止重复初始化
  if (window.consoleLogger) {
    return;
  }
  
  console.log('🔧 控制台日志收集器启动中...');
  
  // 存储原始的控制台方法
  const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info
  };
  
  // 日志存储
  const logs = [];
  const maxLogs = 1000; // 最大存储日志数量
  
  // 创建日志条目
  function createLogEntry(level, args) {
    return {
      timestamp: new Date().toISOString(),
      level: level,
      message: args.map(arg => {
        if (typeof arg === 'object') {
          try {
            return JSON.stringify(arg, null, 2);
          } catch (e) {
            return String(arg);
          }
        }
        return String(arg);
      }).join(' '),
      url: window.location.href,
      userAgent: navigator.userAgent
    };
  }
  
  // 添加日志到存储
  function addLog(level, args) {
    const logEntry = createLogEntry(level, args);
    logs.push(logEntry);
    
    // 保持日志数量在限制内
    if (logs.length > maxLogs) {
      logs.shift();
    }
    
    // 如果是错误，立即发送到服务器（可选）
    if (level === 'error') {
      // 这里可以添加发送错误到服务器的逻辑
      console.warn('🚨 检测到错误:', logEntry.message);
    }
  }
  
  // 重写控制台方法
  console.log = function(...args) {
    addLog('log', args);
    originalConsole.log.apply(console, args);
  };
  
  console.error = function(...args) {
    addLog('error', args);
    originalConsole.error.apply(console, args);
  };
  
  console.warn = function(...args) {
    addLog('warn', args);
    originalConsole.warn.apply(console, args);
  };
  
  console.info = function(...args) {
    addLog('info', args);
    originalConsole.info.apply(console, args);
  };
  
  // 捕获未处理的错误
  window.addEventListener('error', function(event) {
    addLog('error', [`未捕获的错误: ${event.message}`, `文件: ${event.filename}:${event.lineno}:${event.colno}`, event.error]);
  });
  
  // 捕获未处理的 Promise 拒绝
  window.addEventListener('unhandledrejection', function(event) {
    addLog('error', [`未处理的 Promise 拒绝:`, event.reason]);
  });
  
  // 暴露日志收集器 API
  window.consoleLogger = {
    getLogs: function(level) {
      if (level) {
        return logs.filter(log => log.level === level);
      }
      return [...logs];
    },
    
    clearLogs: function() {
      logs.length = 0;
      console.log('🧹 日志已清空');
    },
    
    exportLogs: function() {
      const logData = {
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        logs: logs
      };
      
      const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `console-logs-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      console.log('📥 日志已导出');
    },
    
    getErrorCount: function() {
      return logs.filter(log => log.level === 'error').length;
    }
  };
  
  console.log('✅ 控制台日志收集器已启动');
  
})();
