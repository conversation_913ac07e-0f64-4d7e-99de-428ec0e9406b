/**
 * 🇨🇳 风口云平台 - 数据同步服务
 */

import { db } from '@/lib/database'
import { safeNumber } from '@/lib/utils/number-utils'
import { api } from '@/lib/api/client'
import type { Client, Order } from '@/types'

export interface SyncStatus {
  isOnline: boolean
  lastSyncTime: Date | null
  syncInProgress: boolean
  errorCount: number
  lastError: string | null
}

export type SyncEventType = 'client_created' | 'client_updated' | 'order_created' | 'order_updated' | 'factory_updated'

export interface SyncEvent {
  id: string
  type: SyncEventType
  factoryId: string
  data: any // 使用any而不是unknown，因为我们需要访问属性
  timestamp: Date
  synced: boolean
}

export interface FactoryStatistics {
  totalClients: number
  totalOrders: number
  monthlyRevenue: number
  activeClients: number
  pendingOrders: number
}

export interface AllFactoriesStatistics {
  totalFactories: number
  totalClients: number
  totalOrders: number
  totalRevenue: number
  activeFactories: number
  factoryStats: { [factoryId: string]: { factory: any } & FactoryStatistics }
}

class DataSyncService {
  private syncStatus: SyncStatus = {
    isOnline: true,
    lastSyncTime: null,
    syncInProgress: false,
    errorCount: 0,
    lastError: null
  }

  private syncEvents: SyncEvent[] = []
  private syncListeners: ((status: SyncStatus) => void)[] = []

  getSyncStatus(): SyncStatus {
    return { ...this.syncStatus }
  }

  addSyncListener(listener: (status: SyncStatus) => void) {
    this.syncListeners.push(listener)
  }

  removeSyncListener(listener: (status: SyncStatus) => void) {
    const index = this.syncListeners.indexOf(listener)
    if (index > -1) {
      this.syncListeners.splice(index, 1)
    }
  }

  private notifySyncListeners() {
    // 使用 setTimeout 确保状态更新是异步的，避免在渲染期间同步更新状态
    setTimeout(() => {
      this.syncListeners.forEach(listener => {
        try {
          listener(this.syncStatus)
        } catch (error) {
          console.error('同步状态监听器错误:', error)
        }
      })
    }, 0)
  }

  private updateSyncStatus(updates: Partial<SyncStatus>) {
    this.syncStatus = { ...this.syncStatus, ...updates }
    this.notifySyncListeners()
  }

  async getAllFactoriesClients(): Promise<{ [factoryId: string]: Client[] }> {
    try {
      const factories = await db.getFactories()
      const allClientsData: { [factoryId: string]: Client[] } = {}

      for (const factory of factories) {
        try {
          const clients = await db.getClientsByFactoryId(factory.id)
          allClientsData[factory.id] = clients
        } catch (error) {
          allClientsData[factory.id] = []
        }
      }

      this.updateSyncStatus({
        lastSyncTime: new Date(),
        errorCount: 0,
        lastError: null
      })

      return allClientsData
    } catch (error) {
      this.updateSyncStatus({
        errorCount: this.syncStatus.errorCount + 1,
        lastError: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  async getAllFactoriesOrders(): Promise<{ [factoryId: string]: Order[] }> {
    try {
      const factories = await db.getFactories()
      const allOrdersData: { [factoryId: string]: Order[] } = {}

      for (const factory of factories) {
        try {
          const orders = await db.getOrdersByFactoryId(factory.id)
          allOrdersData[factory.id] = orders
        } catch (error) {
          allOrdersData[factory.id] = []
        }
      }

      this.updateSyncStatus({
        lastSyncTime: new Date(),
        errorCount: 0,
        lastError: null
      })

      return allOrdersData
    } catch (error) {
      this.updateSyncStatus({
        errorCount: this.syncStatus.errorCount + 1,
        lastError: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  async getFactoryStatistics(factoryId: string): Promise<FactoryStatistics> {
    try {
      // 使用API获取客户数据
      const clientsResponse = await api.get(`/api/clients?factoryId=${factoryId}`)
      const clients = clientsResponse.success ? (clientsResponse.data?.clients || []) : []

      // 使用API获取订单数据
      const ordersResponse = await api.get(`/api/orders?factoryId=${factoryId}`)
      const orders = ordersResponse.success ? (ordersResponse.data?.orders || []) : []

      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()
      const monthlyOrders = orders.filter((order: Order) => {
        const orderDate = new Date(order.createdAt)
        return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
      })
      const monthlyRevenue = monthlyOrders.reduce((sum: number, order: Order) => sum + safeNumber(order.totalAmount), 0)

      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      const recentOrders = orders.filter((order: Order) => new Date(order.createdAt) >= thirtyDaysAgo)
      const activeClientIds = new Set(recentOrders.map((order: Order) => order.clientId))

      const pendingOrders = orders.filter((order: Order) => order.status === 'pending').length

      return {
        totalClients: clients.length,
        totalOrders: orders.length,
        monthlyRevenue,
        activeClients: activeClientIds.size,
        pendingOrders
      }
    } catch (error) {
      throw error
    }
  }

  async getAllFactoriesStatistics(): Promise<AllFactoriesStatistics> {
    try {
      const factories = await db.getFactories()
      const factoryStats: { [factoryId: string]: any } = {}

      let totalClients = 0
      let totalOrders = 0
      let totalRevenue = 0
      let activeFactories = 0

      for (const factory of factories) {
        try {
          const stats = await this.getFactoryStatistics(factory.id)
          factoryStats[factory.id] = { factory, ...stats }

          totalClients += safeNumber(stats.totalClients)
          totalOrders += safeNumber(stats.totalOrders)
          totalRevenue += safeNumber(stats.monthlyRevenue)

          if (stats.activeClients > 0) {
            activeFactories++
          }
        } catch (error) {
          factoryStats[factory.id] = {
            factory,
            totalClients: 0,
            totalOrders: 0,
            monthlyRevenue: 0,
            activeClients: 0,
            pendingOrders: 0
          }
        }
      }

      return {
        totalFactories: factories.length,
        totalClients,
        totalOrders,
        totalRevenue,
        activeFactories,
        factoryStats
      }
    } catch (error) {
      throw error
    }
  }

  async getHeadquartersStatistics(): Promise<{
    totalFactories: number
    activeFactories: number
    totalClients: number
    totalOrders: number
    totalRevenue: number
  }> {
    try {
      console.log('📊 获取总部统计数据...')

      const factories = await db.getFactories()
      let totalClients = 0
      let totalOrders = 0
      let totalRevenue = 0
      let activeFactories = 0

      for (const factory of factories) {
        try {
          const stats = await this.getFactoryStatistics(factory.id)
          totalClients += safeNumber(stats.totalClients)
          totalOrders += safeNumber(stats.totalOrders)
          totalRevenue += safeNumber(stats.monthlyRevenue)

          if (stats.totalClients > 0 || stats.totalOrders > 0) {
            activeFactories++
          }
        } catch (error) {
          console.warn(`获取工厂 ${factory.name} 统计失败:`, error)
        }
      }

      const result = {
        totalFactories: factories.length,
        activeFactories,
        totalClients,
        totalOrders,
        totalRevenue
      }

      console.log('✅ 总部统计数据获取完成:', result)
      return result
    } catch (error) {
      console.error('❌ 获取总部统计数据失败:', error)
      throw error
    }
  }

  async syncFactoryDataToHeadquarters(factoryId: string, eventType: SyncEventType, data: any): Promise<void> {
    try {
      this.updateSyncStatus({
        syncInProgress: true
      })

      // 创建同步事件
      const syncEvent: SyncEvent = {
        id: `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        type: eventType,
        factoryId,
        data,
        timestamp: new Date(),
        synced: false
      }

      // 添加到事件列表
      this.syncEvents.push(syncEvent)

      // 模拟同步延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 标记为已同步
      syncEvent.synced = true

      this.updateSyncStatus({
        syncInProgress: false,
        lastSyncTime: new Date(),
        errorCount: 0,
        lastError: null
      })

      console.log(`✅ 数据同步完成: ${eventType} for factory ${factoryId}`)
    } catch (error) {
      this.updateSyncStatus({
        syncInProgress: false,
        errorCount: this.syncStatus.errorCount + 1,
        lastError: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  getSyncEvents(): SyncEvent[] {
    return [...this.syncEvents].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }
}

export const dataSyncService = new DataSyncService()
