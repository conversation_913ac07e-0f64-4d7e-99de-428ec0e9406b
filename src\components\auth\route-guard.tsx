"use client"

import { useEffect, useState } from "react"
import { useRouter  } from "next/navigation"
import { useAuthStore } from "@/lib/store/auth"
import { checkDataIsolationStatus } from "@/lib/middleware/data-isolation"
import type { UserRole } from "@/types"
import type { AuthRole } from "@/lib/store/auth"

interface RouteGuardProps {
  children: React.ReactNode
  requiredRole?: AuthRole | AuthRole[]
  requireAuth?: boolean
  requireFactoryId?: boolean
  fallbackPath?: string
}

export function RouteGuard({
  children,
  requiredRole,
  requireAuth = true,
  requireFactoryId = false,
  fallbackPath = "/"
}: RouteGuardProps) {
  const router = useRouter()
  const { isAuthenticated, role, factoryId } = useAuthStore()
  const [isChecking, setIsChecking] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)

  useEffect(() => {
    // 添加延迟确保 Zustand store 状态完全恢复
    const checkAccess = () => {
      console.log('🔍 RouteGuard 权限检查开始:', {
        isAuthenticated,
        role,
        factoryId,
        requiredRole,
        requireAuth,
        requireFactoryId
      })

      // 检查认证状态
      if (requireAuth && !isAuthenticated) {
        console.log('🔒 认证检查失败，跳转到首页')
        router.push("/")
        return
      }

      // 检查角色权限
      if (requiredRole) {
        const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
        if (!role || !allowedRoles.includes(role as AuthRole)) {
          console.log('🚫 角色权限检查失败，当前角色:', role, '需要角色:', allowedRoles)
          router.push(fallbackPath)
          return
        }
      }

      // 检查工厂ID要求
      if (requireFactoryId && !factoryId) {
        console.log('🏭 工厂ID检查失败，当前工厂ID:', factoryId)
        router.push(fallbackPath)
        return
      }

      // 检查数据隔离状态（管理员跳过此检查）
      if (role !== 'admin') {
        const isolationStatus = checkDataIsolationStatus()
        if (!isolationStatus.isAuthenticated) {
          console.log('🔐 数据隔离状态检查失败:', isolationStatus)
          router.push("/")
          return
        }
      } else {
        console.log('✅ 管理员用户，跳过数据隔离检查')
      }

      console.log('✅ 权限检查通过')
      setHasAccess(true)
      setIsChecking(false)
    }

    // 添加短暂延迟确保状态完全恢复
    const timer = setTimeout(checkAccess, 200)
    return () => clearTimeout(timer)
  }, [isAuthenticated, role, factoryId, requiredRole, requireAuth, requireFactoryId, router, fallbackPath])

  if (isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">验证权限中...</p>
        </div>
      </div>
    )
  }

  if (!hasAccess) {
    return null
  }

  return <>{children}</>
}

// 工厂路由保护组件
export function FactoryRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard
      requiredRole="factory"
      requireAuth={true}
      requireFactoryId={true}
      fallbackPath="/factory/login"
    >
      {children}
    </RouteGuard>
  )
}

// 总部路由保护组件
export function AdminRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard
      requiredRole="admin"
      requireAuth={true}
      requireFactoryId={false}
      fallbackPath="/admin/login"
    >
      {children}
    </RouteGuard>
  )
}

// 客户路由保护组件
export function ClientRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard
      requiredRole="client"
      requireAuth={true}
      requireFactoryId={false}
      fallbackPath="/client"
    >
      {children}
    </RouteGuard>
  )
}

// 权限检查Hook
export function usePermissionCheck() {
  const { isAuthenticated, role, factoryId, user } = useAuthStore()

  const checkPermission = (permission: string): boolean => {
    if (!isAuthenticated || !user) return false

    // 总部管理员拥有所有权限
    if (role === 'admin') return true

    // 工厂用户权限检查
    if (role === 'factory' && 'permissions' in user) {
      const permissions = user.permissions as string[]
      return permissions.includes('all') || permissions.includes(permission)
    }

    return false
  }

  const hasFactoryAccess = (targetFactoryId?: string): boolean => {
    if (!isAuthenticated) return false

    // 总部管理员可以访问所有工厂
    if (role === 'admin') return true

    // 工厂用户只能访问自己的工厂
    if (role === 'factory') {
      return !targetFactoryId || factoryId === targetFactoryId
    }

    return false
  }

  const canCreateOrder = (): boolean => {
    return checkPermission('orders:create') || checkPermission('all')
  }

  const canViewOrders = (): boolean => {
    return checkPermission('orders:read') || checkPermission('all')
  }

  const canEditOrder = (): boolean => {
    return checkPermission('orders:update') || checkPermission('all')
  }

  const canDeleteOrder = (): boolean => {
    return checkPermission('orders:delete') || checkPermission('all')
  }

  const canManageClients = (): boolean => {
    return checkPermission('clients:create') || 
           checkPermission('clients:update') || 
           checkPermission('all')
  }

  const canViewReports = (): boolean => {
    return checkPermission('reports:read') || checkPermission('all')
  }

  const canManageEmployees = (): boolean => {
    if (!isAuthenticated || !user) return false
    
    // 只有工厂管理员(owner)和经理(manager)可以管理员工
    if (role === 'factory' && 'role' in user) {
      return user.role === 'owner' || user.role === 'manager'
    }

    return role === 'admin'
  }

  return {
    checkPermission,
    hasFactoryAccess,
    canCreateOrder,
    canViewOrders,
    canEditOrder,
    canDeleteOrder,
    canManageClients,
    canViewReports,
    canManageEmployees,
    isAdmin: role === 'admin',
    isFactory: role === 'factory',
    isClient: role === 'client'
  }
}

// 权限组件 - 根据权限显示/隐藏内容
interface PermissionWrapperProps {
  children: React.ReactNode
  permission?: string
  role?: AuthRole | AuthRole[]
  factoryId?: string
  fallback?: React.ReactNode
}

export function PermissionWrapper({ 
  children, 
  permission, 
  role, 
  factoryId,
  fallback = null 
}: PermissionWrapperProps) {
  const { checkPermission, hasFactoryAccess } = usePermissionCheck()
  const { role: currentRole } = useAuthStore()

  // 检查角色权限
  if (role) {
    const allowedRoles = Array.isArray(role) ? role : [role]
    if (!currentRole || !allowedRoles.includes(currentRole as AuthRole)) {
      return <>{fallback}</>
    }
  }

  // 检查具体权限
  if (permission && !checkPermission(permission)) {
    return <>{fallback}</>
  }

  // 检查工厂访问权限
  if (factoryId && !hasFactoryAccess(factoryId)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
