/**
 * 🇨🇳 风口云平台 - 工厂用户认证API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { generateTokenPair } from '@/lib/auth/jwt'
import { recordLoginWithRequestInfo } from '@/lib/middleware/session'
import { initializeServer } from '@/lib/server/startup'
import jwt from 'jsonwebtoken'

export async function POST(request: NextRequest) {
  console.log('🔍 工厂登录API被调用 - DEBUG VERSION 2025-07-06')
  console.log('🚨 如果看到这条消息说明代码已更新')

  // 确保服务器已初始化
  initializeServer()

  try {
    console.log('📝 开始解析请求体...')
    const { username, password } = await request.json()
    console.log('✅ 请求体解析成功:', { username, password: '***' })

    if (!username || !password) {
      console.log('❌ 用户名或密码为空')
      return NextResponse.json(
        { error: '用户名和密码不能为空' },
        { status: 400 }
      )
    }

    // 🔧 修复：获取客户端信息用于认证
    const ip = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || 'unknown'

    console.log('🔐 开始数据库认证...')
    console.log('📍 认证参数:', { username, ip, userAgent })

    // 认证用户，传递客户端信息
    const user = await db.authenticateFactoryUser(username, password, {
      ipAddress: ip,
      userAgent: userAgent,
      deviceInfo: parseDeviceInfo(userAgent)
    })

    console.log('✅ 数据库认证完成，结果:', user ? '成功' : '失败')

    if (!user) {
      console.log('❌ 认证失败')
      return NextResponse.json(
        { error: '用户名或密码错误' },
        { status: 401 }
      )
    }

    console.log('✅ 认证成功:', user.name)

    // 🔧 新增：智能会话管理
    console.log('🔍 检查并踢出活跃会话...')
    const { SingleSessionService } = await import('@/lib/services/single-session.service')

    // 生成设备指纹
    const deviceFingerprint = SingleSessionService.generateDeviceFingerprint(
      ip,
      userAgent
    )

    console.log('🔍 工厂用户设备指纹:', deviceFingerprint)

    // 智能检查活跃会话 - 修正：允许同设备多标签页，禁止多设备
    const activeSessionCheck = await SingleSessionService.checkActiveSession(
      user.id,
      'factory_user',
      deviceFingerprint,
      {
        allowMultipleTabsOnSameDevice: true,  // 🔧 修正：允许同设备多标签页
        allowMultipleDevices: false,          // 工厂用户不允许多设备
        maxConcurrentSessions: 5,             // 🔧 修正：允许同设备最多5个标签页
        sessionTimeoutMinutes: 480            // 8小时超时
      }
    )

    // 🔧 修正：智能处理会话冲突
    let sessionConflictInfo = null

    if (activeSessionCheck.hasActiveSession) {
      const isSameDevice = activeSessionCheck.isSameDevice
      const hasMultipleDevices = activeSessionCheck.hasMultipleDevices

      if (isSameDevice && !hasMultipleDevices) {
        // 同设备登录，允许多标签页共存
        console.log('✅ 同设备登录，允许多标签页共存')
      } else if (hasMultipleDevices) {
        // 检测到多设备登录，踢出其他设备
        console.log('⚠️ 检测到多设备登录，踢出其他设备的会话')

        sessionConflictInfo = {
          hasConflict: true,
          conflictType: 'multi_device',
          lastLoginTime: activeSessionCheck.activeSessionInfo?.loginTime,
          lastLoginIP: activeSessionCheck.activeSessionInfo?.ipAddress,
          lastLoginDevice: activeSessionCheck.activeSessionInfo?.deviceInfo
        }

        // 生成临时会话ID用于踢出其他设备会话
        const tempSessionId = SingleSessionService.generateSessionId()

        // 只踢出其他设备的会话，保留同设备会话
        await SingleSessionService.invalidateOtherDeviceSessions(
          user.id,
          'factory_user',
          deviceFingerprint,
          tempSessionId,
          '新设备登录，踢出其他设备'
        )
        console.log('✅ 已踢出其他设备的会话')
      }
    } else {
      console.log('✅ 首次登录，没有发现活跃会话')
    }

    // 生成新的会话ID
    const sessionId = SingleSessionService.generateSessionId()
    console.log('🆔 生成新会话ID:', sessionId)

    // 🔧 修复：生成JWT令牌，与管理员登录保持一致
    console.log('📝 生成JWT令牌...')

    const { generateAccessToken, generateRefreshToken } = await import('@/lib/auth/jwt')

    // 生成访问令牌
    const accessToken = generateAccessToken({
      userId: user.id,
      username: user.username,
      userType: 'factory_user',
      factoryId: user.factoryId,
      name: user.name,
      role: user.role || 'factory_user',
      sessionId // 🔧 新增：包含会话ID
    })

    // 生成刷新令牌
    const refreshToken = generateRefreshToken({
      userId: user.id,
      username: user.username,
      userType: 'factory_user',
      factoryId: user.factoryId,
      name: user.name,
      role: user.role || 'factory_user',
      sessionId // 🔧 新增：包含会话ID
    })

    // 🔧 新增：创建会话记录
    console.log('📝 创建会话记录...')
    try {
      await SingleSessionService.createSessionRecord({
        sessionId,
        userId: user.id,
        userType: 'factory_user',
        username: user.username,
        ipAddress: ip,
        userAgent: userAgent,
        deviceInfo: deviceFingerprint,  // 使用设备指纹
        deviceFingerprint: deviceFingerprint
      })
      console.log('✅ 会话记录创建成功')
    } catch (error) {
      console.error('❌ 创建会话记录失败:', error)
      // 不影响登录流程，继续执行
    }

    // 返回用户信息和令牌（不包含密码）
    const { passwordHash, ...userInfo } = user

    // 处理工厂信息中的BigInt字段
    const factoryInfo = user.factory ? {
      ...user.factory,
      totalSuspendedMs: user.factory.totalSuspendedMs ? Number(user.factory.totalSuspendedMs) : 0
    } : null

    console.log('✅ 准备返回响应（包含令牌）')

    // 🔧 新增：构建响应数据，包含会话冲突信息
    const responseData: any = {
      success: true,
      message: '登录成功',
      user: {
        ...userInfo,
        factory: factoryInfo,
        factoryName: user.factory?.name
      },
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: 28800 // 8小时
    }

    // 🔧 修正：只在真正有设备冲突时才显示提示信息
    if (sessionConflictInfo && sessionConflictInfo.hasConflict) {
      responseData.sessionConflict = {
        message: sessionConflictInfo.conflictType === 'multi_device'
          ? '检测到您的账号在其他设备登录，已自动踢出其他设备的会话'
          : '检测到会话冲突，已处理',
        conflictType: sessionConflictInfo.conflictType,
        previousSession: {
          loginTime: sessionConflictInfo.lastLoginTime,
          ipAddress: sessionConflictInfo.lastLoginIP,
          deviceInfo: sessionConflictInfo.lastLoginDevice
        }
      }
    }

    return NextResponse.json(responseData)

  } catch (error) {
    console.error('❌ 工厂用户认证失败:', error)

    // 检查是否是工厂状态相关的错误
    const errorMessage = error instanceof Error ? error.message : '认证服务异常'
    const errorCode = (error as any)?.code
    const statusCode = (error as any)?.statusCode || 500

    console.error('❌ 详细错误信息:', errorMessage)
    console.error('❌ 错误代码:', errorCode)
    console.error('❌ 状态码:', statusCode)

    // 根据错误代码返回特定的错误响应
    switch (errorCode) {
      case 'FACTORY_SUSPENDED':
        return NextResponse.json(
          {
            error: errorMessage,
            code: 'FACTORY_SUSPENDED',
            title: '服务已暂停',
            suggestion: '请联系管理员了解详情并申请恢复服务'
          },
          { status: 403 }
        )

      case 'FACTORY_EXPIRED':
        return NextResponse.json(
          {
            error: errorMessage,
            code: 'FACTORY_EXPIRED',
            title: '服务已到期',
            suggestion: '请联系管理员续费以继续使用服务'
          },
          { status: 403 }
        )

      case 'FACTORY_INACTIVE':
        return NextResponse.json(
          {
            error: errorMessage,
            code: 'FACTORY_INACTIVE',
            title: '账号未激活',
            suggestion: '请联系管理员激活您的工厂账号'
          },
          { status: 403 }
        )

      default:
        // 对于其他错误，检查错误消息内容（向后兼容）
        if (errorMessage.includes('工厂服务已暂停')) {
          return NextResponse.json(
            {
              error: errorMessage,
              code: 'FACTORY_SUSPENDED',
              title: '服务已暂停',
              suggestion: '请联系管理员了解详情并申请恢复服务'
            },
            { status: 403 }
          )
        }

        if (errorMessage.includes('使用期限已到期') || errorMessage.includes('试用期已结束')) {
          return NextResponse.json(
            {
              error: errorMessage,
              code: 'FACTORY_EXPIRED',
              title: '服务已到期',
              suggestion: '请联系管理员续费以继续使用服务'
            },
            { status: 403 }
          )
        }

        if (errorMessage.includes('工厂账号未激活')) {
          return NextResponse.json(
            {
              error: errorMessage,
              code: 'FACTORY_INACTIVE',
              title: '账号未激活',
              suggestion: '请联系管理员激活您的工厂账号'
            },
            { status: 403 }
          )
        }

        if (errorMessage.includes('工厂信息不存在')) {
          return NextResponse.json(
            {
              error: errorMessage,
              code: 'FACTORY_NOT_FOUND',
              title: '工厂信息异常',
              suggestion: '请联系管理员检查工厂配置'
            },
            { status: 404 }
          )
        }

        // 默认返回500错误
        return NextResponse.json(
          {
            error: errorMessage,
            code: 'INTERNAL_ERROR',
            debug: process.env.NODE_ENV === 'development'
          },
          { status: statusCode }
        )
    }
  }
}



// 记录登录尝试的辅助函数
async function recordLoginAttempt(request: NextRequest, loginData: {
  username: string
  userType: 'admin' | 'factory_user'
  loginStatus: 'success' | 'failed' | 'blocked'
  failReason?: string
  userId?: string
  userName?: string
  factoryId?: string
  factoryName?: string
}) {
  try {
    // 获取客户端信息
    const ip = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || 'unknown'

    await db.recordLogin({
      userId: loginData.userId || '',
      userType: loginData.userType,
      username: loginData.username,
      userName: loginData.userName || '',
      factoryId: loginData.factoryId,
      factoryName: loginData.factoryName,
      loginStatus: loginData.loginStatus,
      failReason: loginData.failReason,
      ipAddress: ip,
      userAgent: userAgent,
      deviceInfo: parseDeviceInfo(userAgent)
    })
  } catch (error) {
    console.error('❌ 记录登录尝试失败:', error)
  }
}

// 获取客户端真实IP地址
function getClientIP(request: NextRequest): string {
  // 尝试从各种头部获取真实IP
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip') // Cloudflare
  const xClientIP = request.headers.get('x-client-ip')

  // 优先级顺序处理
  if (cfConnectingIP) return normalizeIP(cfConnectingIP.trim())
  if (realIP) return normalizeIP(realIP.trim())
  if (xClientIP) return normalizeIP(xClientIP.trim())

  if (forwarded) {
    // x-forwarded-for 可能包含多个IP，取第一个
    const ips = forwarded.split(',').map(ip => ip.trim())
    for (const ip of ips) {
      const normalizedIP = normalizeIP(ip)
      // 过滤掉私有IP和本地IP
      if (isPublicIP(normalizedIP)) {
        return normalizedIP
      }
    }
    // 如果没有公网IP，返回第一个（标准化后）
    return normalizeIP(ips[0])
  }

  // 从连接信息获取（开发环境fallback）
  const url = new URL(request.url)
  if (url.hostname !== 'localhost' && url.hostname !== '127.0.0.1') {
    return url.hostname
  }

  // 开发环境默认值
  return '127.0.0.1'
}

// 标准化IP地址格式
function normalizeIP(ip: string): string {
  if (!ip) return '127.0.0.1'

  // 处理IPv6映射的IPv4地址 (::ffff:*********** -> ***********)
  if (ip.startsWith('::ffff:')) {
    return ip.substring(7)
  }

  // 处理IPv6本地地址
  if (ip === '::1') {
    return '127.0.0.1'
  }

  return ip
}

// 判断是否为公网IP
function isPublicIP(ip: string): boolean {
  if (!ip) return false

  // IPv6本地地址
  if (ip === '::1' || ip.startsWith('::ffff:127.') || ip.startsWith('fe80:')) {
    return false
  }

  // IPv4私有地址和本地地址
  if (ip === '127.0.0.1' || ip === 'localhost') return false
  if (ip.startsWith('192.168.')) return false
  if (ip.startsWith('10.')) return false
  if (ip.startsWith('172.')) {
    const parts = ip.split('.')
    if (parts.length >= 2) {
      const second = parseInt(parts[1])
      if (second >= 16 && second <= 31) return false
    }
  }

  return true
}

// 解析设备信息
function parseDeviceInfo(userAgent: string): string {
  if (!userAgent) return 'Unknown Device'

  if (userAgent.includes('Mobile')) return 'Mobile Device'
  if (userAgent.includes('Tablet')) return 'Tablet'
  if (userAgent.includes('Windows')) return 'Windows PC'
  if (userAgent.includes('Mac')) return 'Mac'
  if (userAgent.includes('Linux')) return 'Linux'

  return 'Desktop'
}
