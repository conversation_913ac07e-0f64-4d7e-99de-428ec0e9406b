/**
 * 🇨🇳 风口云平台 - 公告标记已读API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 标记公告为已读
export async function POST(request: NextRequest) {
  try {
    console.log('📢 接收到标记公告已读请求')
    const { factoryId, announcementId } = await request.json()

    // 验证必需参数
    if (!factoryId || !announcementId) {
      console.log('❌ 缺少必要参数')
      return NextResponse.json(
        { error: '缺少工厂ID或公告ID' },
        { status: 400 }
      )
    }

    console.log('📝 标记公告为已读:', { factoryId, announcementId })

    // 调用数据库服务标记公告为已读
    await db.markAnnouncementAsRead(factoryId, announcementId)

    console.log('✅ 公告标记已读成功')

    return NextResponse.json({
      success: true,
      message: '公告已标记为已读'
    })

  } catch (error) {
    console.error('❌ 标记公告已读失败:', error)
    return NextResponse.json(
      { error: `标记公告已读失败: ${error.message || '未知错误'}` },
      { status: 500 }
    )
  }
}
