<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 API性能深度诊断</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover { background: #1976D2; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .button.urgent { background: #f44336; }
        .button.urgent:hover { background: #d32f2f; }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #4CAF50; background: #e8f5e8; }
        .error { border-color: #f44336; background: #ffe8e8; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        .critical { border-color: #9c27b0; background: #f3e5f5; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API性能深度诊断工具</h1>
        <p><strong>目标</strong>：找出为什么DeepSeek和硅基流动API都这么慢的真正原因</p>
        
        <div style="background: #ffebee; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #f44336;">
            <strong>🚨 当前问题：</strong><br>
            • DeepSeek原生API: 60秒+ 响应时间<br>
            • 硅基流动API: 30秒+ 超时<br>
            • 预期性能: 5-15秒<br>
            • <strong>问题严重程度: 🔴 极高</strong>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-value" id="totalTests">0</div>
                <div class="stat-label">总测试次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgLatency">-</div>
                <div class="stat-label">平均延迟</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="successRate">-</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="networkType">-</div>
                <div class="stat-label">网络类型</div>
            </div>
        </div>
        
        <button class="button urgent" onclick="runCriticalDiagnosis()">🚨 紧急诊断 (找出根本原因)</button>
        <button class="button" onclick="testNetworkLatency()">🌐 网络延迟测试</button>
        <button class="button" onclick="testDNSResolution()">🔍 DNS解析测试</button>
        <button class="button" onclick="testOptimizedAPI()">⚡ 优化后API测试</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        const DEEPSEEK_KEY = '***********************************';
        const SILICONFLOW_KEY = 'sk-szczomdkrprhzlzuzwlblenwfvvuuuyxbxnjgmrcetorftth';
        
        let testStats = {
            total: 0,
            successful: 0,
            totalLatency: 0,
            results: []
        };
        
        function addResult(title, content, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}] ${title}</strong>\n${content}`;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
            
            updateStats();
        }
        
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('avgLatency').textContent = 
                testStats.total > 0 ? `${(testStats.totalLatency / testStats.total / 1000).toFixed(2)}s` : '-';
            document.getElementById('successRate').textContent = 
                testStats.total > 0 ? `${(testStats.successful / testStats.total * 100).toFixed(1)}%` : '-';
            
            // 检测网络类型
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            document.getElementById('networkType').textContent = 
                connection ? connection.effectiveType || connection.type || '未知' : '未知';
        }
        
        async function runCriticalDiagnosis() {
            addResult('🚨 紧急诊断开始', '正在进行全面的性能问题诊断...', 'critical');
            
            // 1. 网络基础测试
            await testBasicConnectivity();
            
            // 2. DNS解析测试
            await testDNSResolution();
            
            // 3. API端点测试
            await testAPIEndpoints();
            
            // 4. 优化参数测试
            await testOptimizedParameters();
            
            // 5. 生成诊断报告
            generateDiagnosisReport();
        }
        
        async function testBasicConnectivity() {
            addResult('🌐 网络连通性测试', '测试基础网络连接...', 'info');
            
            const endpoints = [
                { name: 'DeepSeek API', url: 'https://api.deepseek.com' },
                { name: '硅基流动 API', url: 'https://api.siliconflow.cn' },
                { name: 'Google DNS', url: 'https://8.8.8.8' },
                { name: 'Cloudflare', url: 'https://1.1.1.1' }
            ];
            
            for (const endpoint of endpoints) {
                const startTime = performance.now();
                try {
                    const response = await fetch(endpoint.url + '/favicon.ico', { 
                        method: 'HEAD',
                        mode: 'no-cors',
                        signal: AbortSignal.timeout(5000)
                    });
                    const duration = performance.now() - startTime;
                    
                    addResult(
                        `✅ ${endpoint.name} 连通性`,
                        `延迟: ${duration.toFixed(2)}ms
状态: ${duration < 1000 ? '🟢 优秀' : duration < 3000 ? '🟡 一般' : '🔴 较差'}`,
                        duration < 3000 ? 'success' : 'warning'
                    );
                    
                    testStats.total++;
                    testStats.successful++;
                    testStats.totalLatency += duration;
                    
                } catch (error) {
                    const duration = performance.now() - startTime;
                    addResult(
                        `❌ ${endpoint.name} 连通性`,
                        `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}
状态: 🔴 连接失败`,
                        'error'
                    );
                    
                    testStats.total++;
                    testStats.totalLatency += duration;
                }
            }
        }
        
        async function testDNSResolution() {
            addResult('🔍 DNS解析测试', '测试域名解析速度...', 'info');
            
            const domains = [
                'api.deepseek.com',
                'api.siliconflow.cn',
                'google.com',
                'baidu.com'
            ];
            
            for (const domain of domains) {
                const startTime = performance.now();
                try {
                    // 使用图片请求测试DNS解析
                    const img = new Image();
                    const promise = new Promise((resolve, reject) => {
                        img.onload = resolve;
                        img.onerror = resolve; // DNS解析成功但资源不存在也算成功
                        setTimeout(() => reject(new Error('DNS解析超时')), 5000);
                    });
                    
                    img.src = `https://${domain}/favicon.ico?t=${Date.now()}`;
                    await promise;
                    
                    const duration = performance.now() - startTime;
                    addResult(
                        `✅ ${domain} DNS解析`,
                        `解析时间: ${duration.toFixed(2)}ms
状态: ${duration < 500 ? '🟢 快速' : duration < 2000 ? '🟡 正常' : '🔴 缓慢'}`,
                        duration < 2000 ? 'success' : 'warning'
                    );
                    
                } catch (error) {
                    const duration = performance.now() - startTime;
                    addResult(
                        `❌ ${domain} DNS解析`,
                        `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}
状态: 🔴 解析失败`,
                        'error'
                    );
                }
            }
        }
        
        async function testAPIEndpoints() {
            addResult('🔌 API端点测试', '测试API服务器响应...', 'info');
            
            // 测试DeepSeek
            await testSingleAPI('DeepSeek', 'https://api.deepseek.com/v1/models', DEEPSEEK_KEY);
            
            // 测试硅基流动
            await testSingleAPI('硅基流动', 'https://api.siliconflow.cn/v1/models', SILICONFLOW_KEY);
        }
        
        async function testSingleAPI(name, url, apiKey) {
            const startTime = performance.now();
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`
                    },
                    signal: AbortSignal.timeout(10000)
                });
                
                const duration = performance.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(
                        `✅ ${name} API端点`,
                        `响应时间: ${duration.toFixed(2)}ms
状态码: ${response.status}
可用模型: ${data.data?.length || 'N/A'}
状态: ${duration < 2000 ? '🟢 快速' : duration < 5000 ? '🟡 正常' : '🔴 缓慢'}`,
                        duration < 5000 ? 'success' : 'warning'
                    );
                } else {
                    addResult(
                        `❌ ${name} API端点`,
                        `响应时间: ${duration.toFixed(2)}ms
状态码: ${response.status}
状态: 🔴 API错误`,
                        'error'
                    );
                }
                
            } catch (error) {
                const duration = performance.now() - startTime;
                addResult(
                    `❌ ${name} API端点`,
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}
状态: 🔴 连接失败`,
                    'error'
                );
            }
        }
        
        async function testOptimizedParameters() {
            addResult('⚡ 优化参数测试', '测试优化后的API参数...', 'info');
            
            const optimizedRequest = {
                model: 'deepseek-chat',
                messages: [{ role: 'user', content: '测试' }],
                max_tokens: 50,  // 极小的token数量
                temperature: 0.3,
                stream: false
            };
            
            const startTime = performance.now();
            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${DEEPSEEK_KEY}`
                    },
                    body: JSON.stringify(optimizedRequest),
                    signal: AbortSignal.timeout(15000)
                });
                
                const duration = performance.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(
                        '✅ 优化参数测试成功',
                        `响应时间: ${duration.toFixed(2)}ms (${(duration/1000).toFixed(2)}秒)
Token使用: ${data.usage?.total_tokens || 'N/A'}
性能提升: ${duration < 10000 ? '🟢 显著' : duration < 20000 ? '🟡 一般' : '🔴 无效'}
建议: ${duration < 10000 ? '参数优化有效，建议采用' : '参数优化效果有限，问题在网络层'}`,
                        duration < 20000 ? 'success' : 'warning'
                    );
                } else {
                    const errorText = await response.text();
                    addResult(
                        '❌ 优化参数测试失败',
                        `状态码: ${response.status}
错误: ${errorText}`,
                        'error'
                    );
                }
                
            } catch (error) {
                const duration = performance.now() - startTime;
                addResult(
                    '❌ 优化参数测试异常',
                    `耗时: ${duration.toFixed(2)}ms
错误: ${error.message}`,
                    'error'
                );
            }
        }
        
        function generateDiagnosisReport() {
            const avgLatency = testStats.total > 0 ? testStats.totalLatency / testStats.total : 0;
            const successRate = testStats.total > 0 ? testStats.successful / testStats.total * 100 : 0;
            
            let diagnosis = '';
            let recommendations = '';
            
            if (avgLatency > 5000) {
                diagnosis = '🔴 网络延迟过高，这是主要问题';
                recommendations = `1. 🌐 更换网络环境（尝试手机热点）
2. 🔧 使用VPN或代理服务
3. 📞 联系ISP检查网络质量
4. ⏰ 避开网络高峰期使用`;
            } else if (successRate < 80) {
                diagnosis = '🟡 网络连接不稳定';
                recommendations = `1. 🔄 增加重试机制
2. 🛡️ 检查防火墙设置
3. 📡 优化DNS设置`;
            } else {
                diagnosis = '🟢 网络状况良好，问题可能在API服务端';
                recommendations = `1. ⚡ 继续使用优化后的参数
2. 🔄 实施多API提供商策略
3. 📊 监控API服务商状态`;
            }
            
            addResult(
                '📊 诊断报告',
                `平均延迟: ${(avgLatency/1000).toFixed(2)}秒
成功率: ${successRate.toFixed(1)}%
诊断结果: ${diagnosis}

💡 解决建议:
${recommendations}

🎯 立即行动:
${avgLatency > 5000 ? '网络问题严重，建议立即更换网络环境测试' : '网络基本正常，专注于API参数优化'}`,
                avgLatency > 5000 ? 'critical' : successRate > 80 ? 'success' : 'warning'
            );
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testStats = { total: 0, successful: 0, totalLatency: 0, results: [] };
            updateStats();
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult(
                '📋 深度诊断说明',
                `🎯 目标: 找出API慢的根本原因
🔍 方法: 网络层、DNS、API端点全面测试
⏱️ 预计时间: 2-3分钟
🚨 重要: 请确保网络环境稳定

点击"🚨 紧急诊断"开始全面检查！`,
                'info'
            );
            
            updateStats();
        };
    </script>
</body>
</html>
