-- AlterTable
ALTER TABLE "factory_users" ADD COLUMN "first_login_at" DATETIME;

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_factories" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "address" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "settings" TEXT,
    "subscription_type" TEXT NOT NULL DEFAULT 'trial',
    "subscription_start" DATETIME,
    "subscription_end" DATETIME,
    "first_login_at" DATETIME,
    "is_permanent" BOOLEAN NOT NULL DEFAULT false,
    "suspended_at" DATETIME,
    "suspended_reason" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);
INSERT INTO "new_factories" ("address", "code", "created_at", "email", "id", "name", "phone", "settings", "status", "updated_at") SELECT "address", "code", "created_at", "email", "id", "name", "phone", "settings", "status", "updated_at" FROM "factories";
DROP TABLE "factories";
ALTER TABLE "new_factories" RENAME TO "factories";
CREATE UNIQUE INDEX "factories_code_key" ON "factories"("code");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
