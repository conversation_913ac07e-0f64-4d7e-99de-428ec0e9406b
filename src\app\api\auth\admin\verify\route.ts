/**
 * 🇨🇳 风口云平台 - 管理员密码验证API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const { adminId, password } = await request.json()

    if (!adminId || !password) {
      return NextResponse.json(
        { error: '管理员ID和密码不能为空' },
        { status: 400 }
      )
    }

    // 验证管理员密码
    const isValid = await db.verifyAdminPassword(adminId, password)

    return NextResponse.json({
      success: true,
      isValid
    })

  } catch (error) {
    console.error('❌ 验证管理员密码失败:', error)
    return NextResponse.json(
      { error: '验证服务异常' },
      { status: 500 }
    )
  }
}
