#!/bin/bash

# 阿里云部署脚本
# 使用方法: ./deploy.sh

set -e

echo "🚀 开始部署风口云平台到阿里云..."

# 检查必要的文件
if [ ! -f ".env.production" ]; then
    echo "❌ 错误: .env.production 文件不存在"
    echo "请先创建 .env.production 文件并配置环境变量"
    exit 1
fi

if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 错误: docker-compose.yml 文件不存在"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down || true

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker system prune -f

# 构建新镜像
echo "🔨 构建应用镜像..."
docker-compose build --no-cache

# 启动数据库服务
echo "🗄️ 启动数据库服务..."
docker-compose up -d postgres redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 30

# 运行数据库迁移
echo "📊 运行数据库迁移..."
docker-compose run --rm app npx prisma migrate deploy

# 生成Prisma客户端
echo "🔧 生成Prisma客户端..."
docker-compose run --rm app npx prisma generate

# 启动所有服务
echo "🚀 启动所有服务..."
docker-compose up -d

# 检查服务状态
echo "🔍 检查服务状态..."
sleep 10
docker-compose ps

# 显示日志
echo "📋 显示应用日志..."
docker-compose logs app --tail=50

echo "✅ 部署完成!"
echo "🌐 应用访问地址: https://fengkouyun.cn"
echo "📊 数据库端口: 5432"
echo "🔧 Redis端口: 6379"
echo ""
echo "📝 常用命令:"
echo "  查看日志: docker-compose logs -f"
echo "  重启应用: docker-compose restart app"
echo "  停止服务: docker-compose down"
echo "  更新应用: ./deploy.sh"
