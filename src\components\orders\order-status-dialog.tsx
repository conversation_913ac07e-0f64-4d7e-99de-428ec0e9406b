/**
 * 🇨🇳 风口云平台 - 订单状态编辑对话框
 * 
 * 允许手动编辑订单状态，同时保持付款状态自动完成的业务逻辑
 */

'use client'

import { useState, useEffect } from 'react'
import { Order, OrderStatus } from '@/types'
import { db } from '@/lib/database'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button  } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertCircle, CheckCircle } from 'lucide-react'

interface OrderStatusDialogProps {
  order: Order | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onOrderUpdated?: (order: Order) => void
}

export default function OrderStatusDialog({
  order,
  open,
  onOpenChange,
  onOrderUpdated
}: OrderStatusDialogProps) {
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [successMessage, setSuccessMessage] = useState("")
  const [formData, setFormData] = useState({
    status: 'pending' as OrderStatus,
    notes: ''
  })

  // 当订单变化时重置表单
  useEffect(() => {
    if (order) {
      setFormData({
        status: order.status,
        notes: order.notes || ''
      })
      setErrors({})
      setSuccessMessage("")
    }
  }, [order])

  // 获取有效状态（考虑付款状态的自动完成逻辑）
  const getEffectiveStatus = (order: Order) => {
    if (order.paymentStatus === 'partial' || order.paymentStatus === 'paid') {
      return 'completed'
    }
    return order.status
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '待确认'
      case 'production': return '生产中'
      case 'completed': return '已完成'
      case 'cancelled': return '已取消'
      default: return '未知'
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'production': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!order) return

    try {
      setLoading(true)
      setErrors({})

      // 构建更新的订单数据
      const updatedOrder: Order = {
        ...order,
        status: formData.status,
        notes: formData.notes,
        updatedAt: new Date()
      }

      // 更新订单状态
      const success = await db.updateOrderStatus(order.id, {
        status: formData.status,
        notes: formData.notes
      })

      if (success) {
        console.log('✅ 订单状态更新成功')
        setSuccessMessage("订单状态更新成功！")

        // 通知父组件
        if (onOrderUpdated) {
          onOrderUpdated(updatedOrder)
        }

        // 延迟关闭对话框，让用户看到成功消息
        setTimeout(() => {
          onOpenChange(false)
          setSuccessMessage("")
        }, 1500)
      } else {
        setErrors({ general: "更新失败，请重试" })
      }
    } catch (error) {
      console.error('更新订单状态失败:', error)
      setErrors({ general: `更新失败: ${(error as Error).message}` })
    } finally {
      setLoading(false)
    }
  }

  if (!order) return null

  const effectiveStatus = getEffectiveStatus(order)
  const isAutoCompleted = effectiveStatus === 'completed' && order.status !== 'completed'

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>编辑订单状态</DialogTitle>
          <DialogDescription>
            修改订单 {order.orderNumber || order.id} 的状态信息
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 当前状态显示 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">当前状态</Label>
            <div className="flex items-center space-x-3">
              <span className={`px-3 py-1 rounded-full text-sm border ${getStatusColor(order.status)}`}>
                原始状态: {getStatusText(order.status)}
              </span>
              {isAutoCompleted && (
                <span className={`px-3 py-1 rounded-full text-sm border ${getStatusColor('completed')}`}>
                  有效状态: {getStatusText('completed')} (自动)
                </span>
              )}
            </div>
            {isAutoCompleted && (
              <div className="flex items-start space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium">自动完成提醒</p>
                  <p>由于订单已有付款记录，系统自动将其视为"已完成"状态。您仍可手动修改原始状态。</p>
                </div>
              </div>
            )}
          </div>

          {/* 状态选择 */}
          <div className="space-y-2">
            <Label htmlFor="status" className="text-sm font-medium">
              订单状态 <span className="text-red-500">*</span>
            </Label>
            <select
              id="status"
              value={formData.status}
              onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as OrderStatus }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="pending">待确认</option>
              <option value="production">生产中</option>
              <option value="completed">已完成</option>
              <option value="cancelled">已取消</option>
            </select>
            {errors.status && (
              <p className="text-sm text-red-600">{errors.status}</p>
            )}
          </div>

          {/* 备注 */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="text-sm font-medium">备注</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="添加订单备注信息..."
              rows={3}
              className="resize-none"
            />
          </div>

          {/* 错误信息 */}
          {errors.general && (
            <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <p className="text-sm text-red-800">{errors.general}</p>
            </div>
          )}

          {/* 成功信息 */}
          {successMessage && (
            <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-md">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <p className="text-sm text-green-800">{successMessage}</p>
            </div>
          )}

          {/* 按钮 */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? '更新中...' : '确认更新'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
