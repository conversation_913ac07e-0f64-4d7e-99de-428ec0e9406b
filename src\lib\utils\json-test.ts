/**
 * JSON解析测试工具
 */

export function testJsonParsing() {
  // 模拟可能出现的问题JSON
  const problematicJsons = [
    // 1. 尾随逗号
    `{
      "projects": [{
        "projectName": "测试项目",
        "floors": [{
          "rooms": [{
            "vents": [{
              "systemType": "double_white_outlet",
              "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            }]
          }]
        }]
      }],
      "confidence": 0.95,
    }`,
    
    // 2. 缺失引号
    `{
      projects: [{
        projectName: "测试项目",
        floors: [{
          rooms: [{
            vents: [{
              systemType: "double_white_outlet",
              dimensions: {length: 2665, width: 155, unit: "mm"}
            }]
          }]
        }]
      }],
      confidence: 0.95
    }`,
    
    // 3. 包含代码块
    `这是一个JSON响应：
    \`\`\`json
    {
      "projects": [{
        "projectName": "测试项目",
        "floors": [{
          "rooms": [{
            "vents": [{
              "systemType": "double_white_outlet",
              "dimensions": {"length": 2665, "width": 155, "unit": "mm"}
            }]
          }]
        }]
      }],
      "confidence": 0.95
    }
    \`\`\`
    解析完成。`
  ];

  console.log('🧪 开始JSON解析测试...');
  
  problematicJsons.forEach((json, index) => {
    console.log(`\n📝 测试案例 ${index + 1}:`);
    console.log('原始JSON:', json.substring(0, 100) + '...');
    
    try {
      // 直接解析
      JSON.parse(json);
      console.log('✅ 直接解析成功');
    } catch (error) {
      console.log('❌ 直接解析失败:', error.message);
      
      // 尝试修复
      try {
        const cleaned = cleanAndFixJson(json);
        JSON.parse(cleaned);
        console.log('✅ 修复后解析成功');
      } catch (fixError) {
        console.log('❌ 修复后仍失败:', fixError.message);
      }
    }
  });
}

function cleanAndFixJson(jsonStr: string): string {
  let cleaned = jsonStr;
  
  // 1. 提取JSON部分
  const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
  if (jsonMatch) {
    cleaned = jsonMatch[0];
  } else {
    const codeBlockMatch = cleaned.match(/```json\s*([\s\S]*?)\s*```/);
    if (codeBlockMatch) {
      cleaned = codeBlockMatch[1];
    }
  }
  
  // 2. 清理
  cleaned = cleaned
    .trim()
    .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
    .replace(/\/\/.*$/gm, '')
    .replace(/'/g, '"') // 标准化引号
    .replace(/,(\s*[}\]])/g, '$1') // 移除尾随逗号
    .replace(/(\w+):/g, '"$1":'); // 添加缺失引号
  
  return cleaned;
}

// 导出测试函数
export { cleanAndFixJson };
