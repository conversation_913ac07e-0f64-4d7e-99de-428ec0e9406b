// 测试楼层数字修复效果
console.log('🧪 测试楼层数字修复效果');

console.log('\n🔍 问题分析:');
console.log('❌ 原问题: AI识别后分成两个楼层，但楼层数字都显示为"1"');
console.log('🔧 根本原因: 对已经正确的楼层数字再次调用normalizeFloorName函数');

console.log('\n📊 修复逻辑:');
console.log('1. 楼层提取阶段: extractFloorFromLine("一楼厨房...") → "1"');
console.log('2. 楼层上下文: currentFloor = "1" 或 "2"');
console.log('3. 楼层创建: floor.name = "1" 或 "2" (正确)');
console.log('4. 转换为表单: floorName = floor.name (修复后，不再调用normalizeFloorName)');

console.log('\n🎯 修复前后对比:');
console.log('修复前:');
console.log('  楼层提取: "一楼" → "1"');
console.log('  楼层创建: floor.name = "1"');
console.log('  表单转换: normalizeFloorName("1") → "1" (可能有问题)');
console.log('  最终显示: "1" (错误，所有楼层都是1)');

console.log('\n修复后:');
console.log('  楼层提取: "一楼" → "1", "二楼" → "2"');
console.log('  楼层创建: floor.name = "1", floor.name = "2"');
console.log('  表单转换: floorName = floor.name (直接使用)');
console.log('  最终显示: "1", "2" (正确)');

console.log('\n🔧 修复的代码位置:');
console.log('1. 第3648行: floorName: normalizeFloorName(floor.name) → floorName: floor.name');
console.log('2. 第7856行: floorName: normalizeFloorName(floor.name) → floorName: floor.name');
console.log('3. 第7893行: floorName: normalizeFloorName(tempFloor.name) → floorName: tempFloor.name');

console.log('\n📋 normalizeFloorName函数的正确用途:');
console.log('✅ 应该用于: 处理原始项目名称 (如"书香华府1201" → "12")');
console.log('✅ 应该用于: 处理原始楼层文本 (如"一楼" → "1")');
console.log('❌ 不应用于: 已经是数字格式的楼层名称 (如"1" → "1")');

console.log('\n🎯 预期修复效果:');
console.log('✅ 第一个楼层显示: "1"');
console.log('✅ 第二个楼层显示: "2"');
console.log('✅ 风口正确分组到对应楼层');
console.log('✅ 楼层数字与实际内容匹配');

console.log('\n📈 测试场景:');
console.log('输入文本包含:');
console.log('- "一楼厨房出风口..." → 应归属楼层"1"');
console.log('- "二楼客厅出风口..." → 应归属楼层"2"');
console.log('- 每个楼层的风口数量和尺寸正确');

console.log('\n🔧 新发现的问题和修复:');
console.log('❌ 问题: 预览编辑器中楼层名称显示为"undefined"');
console.log('🔍 根本原因: convertToEditableFloor函数中使用了错误的字段名');
console.log('   - 数据结构使用: floor.name');
console.log('   - 代码尝试获取: floor.floorName');
console.log('   - 结果: floor.floorName为undefined，导致楼层名称为空');

console.log('\n🔧 最新修复:');
console.log('文件: src/hooks/use-import-preview.ts');
console.log('第127行: floorName: floor.floorName || floor.name || ""');
console.log('第123行: 日志中也修复了楼层名称显示');

console.log('\n📊 完整的数据流修复:');
console.log('1. 楼层识别: "一楼" → "1", "二楼" → "2" ✅');
console.log('2. 楼层创建: floor.name = "1", floor.name = "2" ✅');
console.log('3. 数据转换: floorName = floor.name (不再错误调用normalizeFloorName) ✅');
console.log('4. 预览编辑器: floorName = floor.floorName || floor.name ✅');
console.log('5. 最终显示: "1", "2" (应该正确) ✅');

console.log('\n✅ 修复完成，等待实际测试验证');
