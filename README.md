# 🌪️ 风口云平台 (DuctCloud Platform)

一个专为中央空调风口加工厂设计的现代化SaaS管理平台，提供多工厂管理、订单处理、客户关系管理、股东利润分析等完整解决方案。

## ✨ 核心功能

### 🏭 多工厂管理
- **工厂独立运营**: 每个工厂拥有独立的数据空间和管理权限
- **总部统一监控**: 总部可以查看所有工厂的运营数据和统计信息
- **灵活权限控制**: 支持工厂管理员、员工、总部管理员等多种角色

### 📋 订单管理系统
- **智能订单编号**: 基于工厂代码的自动编号系统，避免重复
- **完整订单流程**: 从下单到交付的全流程跟踪
- **多样化产品支持**: 支持各种风口类型和规格
- **实时状态更新**: 订单状态实时同步，便于跟踪

### 👥 客户关系管理
- **客户档案管理**: 完整的客户信息记录和历史订单查询
- **推荐奖励系统**: 客户推荐新客户可获得奖励，支持多种使用方式
- **智能搜索**: 快速查找客户信息和订单记录
- **客户分析**: 客户价值分析和行为统计

### 💰 财务管理
- **收款管理**: 支持部分付款、分期付款等多种收款方式
- **成本控制**: 材料成本、人工成本的精确计算
- **利润分析**: 实时利润统计和趋势分析
- **财务报表**: 自动生成各类财务报表

### 🏢 股东管理与利润分析
- **股权结构管理**: 完整的股东信息和股权比例管理
- **投资记录追踪**: 股东投资历史和股权变更记录
- **智能利润分配**: 基于股权比例的自动利润分配计算
- **多维度利润分析**: 月度、季度、年度利润分析
- **专业报表导出**: Excel格式的股东利润分配报表
- **投资回报率计算**: 实时ROI计算和趋势分析

### 📊 数据分析
- **实时仪表板**: 关键业务指标的实时展示
- **趋势分析**: 销售趋势、客户增长等数据分析
- **报表导出**: 支持Excel等格式的报表导出
- **数据可视化**: 图表化展示业务数据

## 🛠️ 技术架构

### 前端技术栈
- **Next.js 15**: 现代化React全栈框架
- **React 19**: 最新的React版本，提供更好的性能
- **TypeScript**: 类型安全的JavaScript开发
- **Tailwind CSS**: 原子化CSS框架，快速构建美观界面
- **Radix UI**: 无障碍的UI组件库
- **Lucide React**: 现代化图标库

### 后端技术栈
- **Next.js API Routes**: 服务端API开发
- **Prisma**: 现代化数据库ORM
- **PostgreSQL**: 可靠的关系型数据库
- **Redis**: 高性能缓存和会话存储

### 部署技术
- **Docker**: 容器化部署，确保环境一致性
- **Docker Compose**: 多服务编排管理
- **Nginx**: 高性能Web服务器和反向代理

## 🚀 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- Docker & Docker Compose (推荐)

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/your-username/factorysystem.git
cd factorysystem
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env.local
# 编辑 .env.local 文件，配置数据库连接等信息
```

4. **初始化数据库**
```bash
npx prisma migrate dev
npx prisma generate
```

5. **启动开发服务器**
```bash
npm run dev
```

访问 `http://localhost:3000` 开始使用。

### 生产部署

详细的部署指南请参考 [🚀 部署指南](DEPLOYMENT_GUIDE.md)

**快速部署命令：**
```bash
# 配置环境变量
cp .env.production .env

# 执行部署
chmod +x deploy.sh
./deploy.sh
```

## 📖 文档

- [📋 项目结构说明](PROJECT_STRUCTURE.md) - 详细的项目架构和代码组织
- [🚀 部署指南](DEPLOYMENT_GUIDE.md) - 完整的阿里云部署教程
- [🗄️ 数据库设置](docs/database-setup.md) - 数据库配置和迁移

## 🎯 主要特性

### 💼 业务特色
- **行业专精**: 专为风口加工行业设计，贴合实际业务需求
- **多工厂支持**: 支持集团化管理，每个工厂独立运营
- **智能推荐**: 客户推荐奖励系统，促进业务增长
- **股东透明**: 完整的股东利润分析和分配系统

### 💡 技术特色
- **现代化架构**: 基于最新的Web技术栈
- **响应式设计**: 完美适配桌面和移动设备
- **高性能**: 优化的数据库查询和缓存策略
- **安全可靠**: 完善的权限控制和数据安全保护

### 🔄 系统集成
- **API优先**: RESTful API设计，易于集成
- **数据导出**: 支持Excel等格式的数据导出
- **扩展性**: 模块化设计，易于功能扩展
- **监控告警**: 完善的系统监控和告警机制

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看项目文档
2. 检查部署指南中的故障排除章节
3. 创建Issue描述问题

---

**风口云平台** - 让风口加工厂管理更简单、更高效！
