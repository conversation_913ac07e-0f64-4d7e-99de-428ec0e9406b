/**
 * 🇨🇳 风口云平台 - 主题切换按钮
 * 
 * 功能说明：
 * - 快速切换浅色/深色主题
 * - 显示当前主题状态
 * - 可放置在导航栏或工具栏中
 */

'use client'

import { Button } from '@/components/ui/button'
import { useUserTheme } from './user-theme-provider'
import { useMounted } from '@/hooks/use-mounted'
import { Sun, Moon, Monitor } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface ThemeToggleProps {
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'default' | 'sm' | 'lg'
  showLabel?: boolean
}

export function ThemeToggle({
  variant = 'ghost',
  size = 'default',
  showLabel = false
}: ThemeToggleProps) {
  const { mode, setTheme } = useUserTheme()
  const mounted = useMounted()

  // 防止水合错误
  if (!mounted) {
    return (
      <Button variant={variant} size={size} disabled>
        <Sun className="h-4 w-4" />
        {showLabel && <span className="hidden sm:inline ml-2">主题</span>}
      </Button>
    )
  }

  const themeOptions = [
    {
      value: 'light' as const,
      label: '浅色主题',
      icon: Sun,
      description: '适合白天使用'
    },
    {
      value: 'dark' as const,
      label: '深色主题',
      icon: Moon,
      description: '适合夜间使用'
    },
    {
      value: 'auto' as const,
      label: '跟随系统',
      icon: Monitor,
      description: '自动切换'
    }
  ]

  const currentTheme = themeOptions.find(option => option.value === mode)
  const CurrentIcon = currentTheme?.icon || Sun

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant={variant} 
          size={size}
          className="flex items-center space-x-2"
        >
          <CurrentIcon className="h-4 w-4" />
          {showLabel && (
            <span className="hidden sm:inline">
              {currentTheme?.label || '主题'}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {themeOptions.map((option) => {
          const Icon = option.icon
          const isSelected = mode === option.value

          return (
            <DropdownMenuItem
              key={option.value}
              onClick={() => setTheme(option.value)}
              className={`flex items-center space-x-3 ${
                isSelected ? 'bg-accent' : ''
              }`}
            >
              <Icon className="h-4 w-4" />
              <div className="flex-1">
                <div className="font-medium">{option.label}</div>
                <div className="text-xs text-muted-foreground">
                  {option.description}
                </div>
              </div>
              {isSelected && (
                <div className="w-2 h-2 bg-primary rounded-full"></div>
              )}
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// 简单的主题切换按钮（只在浅色和深色之间切换）
export function SimpleThemeToggle({
  variant = 'ghost',
  size = 'default'
}: Omit<ThemeToggleProps, 'showLabel'>) {
  const { mode, toggleTheme } = useTheme()
  const mounted = useMounted()

  // 防止水合错误
  if (!mounted) {
    return (
      <Button variant={variant} size={size} disabled>
        <Sun className="h-4 w-4" />
      </Button>
    )
  }

  const Icon = mode === 'dark' ? Sun : Moon
  const label = mode === 'dark' ? '切换到浅色主题' : '切换到深色主题'

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleTheme}
      title={label}
      className="flex items-center justify-center"
    >
      <Icon className="h-4 w-4" />
    </Button>
  )
}
