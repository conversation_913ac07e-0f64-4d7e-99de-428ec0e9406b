"use client"

import { useState, useEffect } from "react"
import { Card, CardContent  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { AddEmployeeDialog } from "@/components/factory/add-employee-dialog"
import { EditEmployeeDialog } from "@/components/factory/edit-employee-dialog"
import { EmployeeActions, EmployeeDetailsDialog } from "@/components/factory/employee-actions"
import { useAuthStore } from "@/lib/store/auth"
import { db } from "@/lib/database"
import { FactoryUser } from "@/types"
import {
  Users,
  Plus,
  Search,
  UserCheck,
  UserX,
  Shield,
  Clock
} from "lucide-react"

// 数据库服务已通过导入获得

const getRoleColor = (role: string) => {
  switch (role) {
    case 'owner': return 'text-purple-600 bg-purple-100'
    case 'manager': return 'text-blue-600 bg-blue-100'
    case 'employee': return 'text-green-600 bg-green-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getRoleName = (role: string) => {
  switch (role) {
    case 'owner': return '工厂主'
    case 'manager': return '管理员'
    case 'employee': return '员工'
    default: return '未知'
  }
}

const getStatusColor = (isActive: boolean) => {
  return isActive ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
}

const getStatusText = (isActive: boolean) => {
  return isActive ? '正常' : '停用'
}

export default function EmployeesPage() {
  const { user, factoryId } = useAuthStore()
  const [searchTerm, setSearchTerm] = useState("")
  const [employees, setEmployees] = useState<FactoryUser[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<FactoryUser | null>(null)

  // 加载员工数据
  useEffect(() => {
    loadEmployees()
  }, [factoryId])

  const loadEmployees = async () => {
    if (!factoryId) return

    setLoading(true)
    try {
      // 获取员工列表（包含动态计算的统计信息）
      console.log('🔄 加载员工列表...')
      const employeeList = await db.getEmployeesByFactoryId(factoryId)
      setEmployees(employeeList)

      console.log('✅ 员工列表加载完成，包含统计信息')
    } catch (error) {
      console.error('加载员工列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 添加员工
  const handleAddEmployee = async (employeeData: unknown) => {
    if (!factoryId || !user) return

    try {
      const newEmployee = await db.createEmployee({
        ...employeeData,
        factoryId,
        createdBy: user.id,
        isActive: true
      })

      if (newEmployee) {
        await loadEmployees() // 重新加载员工列表
        console.log('✅ 员工添加成功:', newEmployee.name)
      }
    } catch (error) {
      console.error('❌ 添加员工失败:', error)
    }
  }

  // 编辑员工
  const handleEditEmployee = async (updatedEmployee: FactoryUser) => {
    try {
      const result = await db.updateEmployee(updatedEmployee.id, updatedEmployee)

      if (result) {
        await loadEmployees() // 重新加载员工列表
        console.log('✅ 员工信息更新成功:', result.name)
      } else {
        console.error('❌ 更新员工信息失败: 服务器返回空结果')
        throw new Error('更新员工信息失败')
      }
    } catch (error) {
      console.error('❌ 更新员工信息失败:', error)
      // 可以在这里添加用户友好的错误提示
      alert('更新员工信息失败，请重试')
    }
  }

  // 删除员工
  const handleDeleteEmployee = async (employee: FactoryUser) => {
    if (!confirm(`确定要删除员工 "${employee.name}" 吗？此操作不可恢复。`)) {
      return
    }

    try {
      const success = await db.deleteEmployee(employee.id)

      if (success) {
        await loadEmployees() // 重新加载员工列表
        console.log('✅ 员工删除成功:', employee.name)
      }
    } catch (error) {
      console.error('❌ 删除员工失败:', error)
    }
  }

  // 切换员工状态
  const handleToggleStatus = async (employee: FactoryUser) => {
    try {
      const result = await db.toggleEmployeeStatus(employee.id)

      if (result) {
        await loadEmployees() // 重新加载员工列表
        console.log(`✅ 员工状态已${result.isActive ? '激活' : '停用'}:`, result.name)
      }
    } catch (error) {
      console.error('❌ 切换员工状态失败:', error)
    }
  }

  // 查看员工详情
  const handleViewDetails = (employee: FactoryUser) => {
    setSelectedEmployee(employee)
    setShowDetailsDialog(true)
  }

  // 编辑员工
  const handleEditClick = (employee: FactoryUser) => {
    setSelectedEmployee(employee)
    setShowEditDialog(true)
  }

  const filteredEmployees = employees.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.username.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载员工数据中...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">员工管理</h1>
            <p className="text-gray-600">管理工厂的所有员工账号和权限</p>
          </div>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            添加员工
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总员工数</p>
                  <p className="text-2xl font-bold text-gray-900">{employees.length}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">活跃员工</p>
                  <p className="text-2xl font-bold text-green-600">
                    {employees.filter(emp => emp.isActive).length}
                  </p>
                </div>
                <UserCheck className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">本月录单</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {employees.reduce((sum, emp) => sum + (emp.thisMonthOrders || 0), 0)}
                  </p>
                </div>
                <Shield className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">停用员工</p>
                  <p className="text-2xl font-bold text-red-600">
                    {employees.filter(emp => !emp.isActive).length}
                  </p>
                </div>
                <UserX className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索员工姓名或用户名..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline">筛选</Button>
              <Button variant="outline">导出</Button>
            </div>
          </CardContent>
        </Card>

        {/* Employees List */}
        <div className="space-y-4">
          {filteredEmployees.map((employee) => (
            <Card key={employee.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-100 p-3 rounded-full">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{employee.name}</h3>
                        <span className={`text-xs px-2 py-1 rounded-full ${getRoleColor(employee.role)}`}>
                          {getRoleName(employee.role)}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(employee.isActive)}`}>
                          {getStatusText(employee.isActive)}
                        </span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">用户名：</span>
                          {employee.username}
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>最后登录：{employee.lastLoginAt ? new Date(employee.lastLoginAt).toLocaleDateString() : '从未登录'}</span>
                        </div>
                        <div>
                          <span className="font-medium">权限数：</span>
                          {employee.permissions.length} 项
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-6">
                    {/* Stats */}
                    <div className="text-center">
                      <p className="text-lg font-bold text-blue-600">{employee.totalOrders || 0}</p>
                      <p className="text-xs text-gray-600">总订单</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold text-green-600">{employee.thisMonthOrders || 0}</p>
                      <p className="text-xs text-gray-600">本月订单</p>
                    </div>
                    
                    {/* Actions */}
                    <EmployeeActions
                      employee={employee}
                      onEdit={handleEditClick}
                      onDelete={handleDeleteEmployee}
                      onToggleStatus={handleToggleStatus}
                      onViewDetails={handleViewDetails}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredEmployees.length === 0 && !loading && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? '没有找到匹配的员工' : '还没有员工'}
            </h3>
            <p className="text-gray-600">
              {searchTerm ? '尝试调整搜索条件' : '点击上方"添加员工"按钮开始添加员工'}
            </p>
          </div>
        )}

        {/* 对话框组件 */}
        <AddEmployeeDialog
          isOpen={showAddDialog}
          onClose={() => setShowAddDialog(false)}
          onSave={handleAddEmployee}
        />

        <EditEmployeeDialog
          isOpen={showEditDialog}
          onClose={() => setShowEditDialog(false)}
          onSave={handleEditEmployee}
          employee={selectedEmployee}
        />

        <EmployeeDetailsDialog
          isOpen={showDetailsDialog}
          onClose={() => setShowDetailsDialog(false)}
          employee={selectedEmployee}
        />
      </div>
    </DashboardLayout>
  )
}
