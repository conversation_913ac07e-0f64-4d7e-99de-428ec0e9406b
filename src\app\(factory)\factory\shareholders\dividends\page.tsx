"use client"

import { useState, useEffect } from "react"
import { Card, CardContent  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { AddDividendDialog } from "@/components/dividends/add-dividend-dialog"
import { Loader2, AlertCircle, TrendingUp, ArrowLeft } from "lucide-react"
import { Dividend } from "@/types"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import { safeAmount } from "@/lib/utils/number-utils"
import Link from "next/link"

export default function DividendsPage() {
  const [dividends, setDividends] = useState<Dividend[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 加载分红数据
  const loadDividends = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        setError("无法获取工厂信息")
        return
      }

      const response = await fetch(`/api/dividends?factoryId=${factoryId}`)
      const result = await response.json()

      if (result.success) {
        setDividends(result.dividends)
      } else {
        setError(result.error || "获取分红数据失败")
      }
    } catch (error) {
      console.error('加载分红数据失败:', error)
      setError("加载分红数据失败")
    } finally {
      setLoading(false)
    }
  }

  // 处理分红添加成功
  const handleDividendAdded = (newDividend: Dividend) => {
    setDividends(prev => [newDividend, ...prev])
  }

  // 页面加载时获取数据
  useEffect(() => {
    loadDividends()
  }, [])

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/factory/shareholders">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回股东管理
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">分红管理</h1>
              <p className="text-gray-600">管理股东分红计划和分配记录</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <AddDividendDialog onDividendAdded={handleDividendAdded} />
          </div>
        </div>

        {/* Stats Card */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">分红次数</p>
                  <p className="text-2xl font-bold text-gray-900">{dividends.length}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">加载分红数据中...</span>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={loadDividends} variant="outline">
                重试
              </Button>
            </div>
          </div>
        )}

        {/* Dividends List */}
        {!loading && !error && (
          <div className="space-y-4">
            {dividends.map((dividend) => (
              <Card key={dividend.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900">{dividend.title}</h3>
                      <p className="text-gray-600">{dividend.description || '无描述'}</p>
                      <p className="text-gray-600">分红金额: {safeAmount(dividend.totalAmount)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {dividends.length === 0 && (
              <div className="text-center py-12">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">还没有分红记录</h3>
                <p className="text-gray-600">点击上方"创建分红"按钮开始创建分红计划</p>
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
