import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { calculateProportionalReward } from '@/lib/utils/reward-calculator'
import { getUserFromRequest } from '@/lib/auth/jwt'

// 获取认证用户信息
async function getAuthenticatedUser(request: NextRequest) {
  try {
    const authResult = getUserFromRequest(request)
    if (authResult.success && authResult.user) {
      const user = authResult.user
      return {
        success: true,
        user: {
          id: user.userId,
          userType: user.userType,
          factoryId: user.factoryId,
          username: user.username,
          name: user.name
        }
      }
    }
  } catch (error) {
    console.log('JWT认证失败:', error)
  }

  return {
    success: false,
    error: '认证失败'
  }
}

/**
 * 同步特定客户的奖励数据
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { clientId: string } }
) {
  try {
    const { clientId } = params
    const { searchParams } = new URL(request.url)
    const isPublic = searchParams.get('public') === 'true'

    console.log('🔄 同步客户奖励数据:', { clientId, isPublic })

    if (!clientId) {
      return NextResponse.json({
        success: false,
        error: '客户ID不能为空'
      }, { status: 400 })
    }

    let factoryId: string | null = null
    let user: any = null

    if (isPublic) {
      // 🆕 公开访问模式：从查询参数获取工厂ID
      factoryId = searchParams.get('factoryId')
      console.log('🌐 公开访问模式，工厂ID:', factoryId)
    } else {
      // 🔒 认证访问模式
      const authResult = await getAuthenticatedUser(request)
      if (!authResult.success) {
        return NextResponse.json({
          success: false,
          error: '认证失败，请重新登录'
        }, { status: 401 })
      }

      user = authResult.user
      factoryId = searchParams.get('factoryId') || user?.factoryId
      console.log('🔒 认证访问模式，用户类型:', user?.userType, '工厂ID:', factoryId)
    }

    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '无法获取工厂信息'
      }, { status: 400 })
    }

    // 获取客户信息
    const client = await db.getClientById(clientId)
    if (!client) {
      return NextResponse.json({
        success: false,
        error: '客户不存在'
      }, { status: 404 })
    }

    if (client.factoryId !== factoryId) {
      return NextResponse.json({
        success: false,
        error: '客户不属于当前工厂'
      }, { status: 403 })
    }

    // 计算实时奖励状态
    const rewardStatus = await calculateProportionalReward(clientId, factoryId)

    console.log('💰 实时奖励状态计算完成:', {
      clientId,
      clientName: client.name,
      totalReward: rewardStatus.totalReward,
      availableReward: rewardStatus.availableReward,
      usedReward: rewardStatus.usedReward,
      pendingReward: rewardStatus.pendingReward
    })

    // 更新数据库中的奖励数据
    await db.updateClient(clientId, {
      referralReward: rewardStatus.totalReward,
      availableReward: rewardStatus.availableReward,
      pendingReward: rewardStatus.pendingReward,
      usedReward: rewardStatus.usedReward
    })

    console.log('✅ 客户奖励数据同步完成:', client.name)

    return NextResponse.json({
      success: true,
      message: '奖励数据同步成功',
      data: {
        clientId,
        clientName: client.name,
        oldData: {
          referralReward: client.referralReward || 0,
          availableReward: client.availableReward || 0,
          usedReward: client.usedReward || 0
        },
        newData: {
          referralReward: rewardStatus.totalReward,
          availableReward: rewardStatus.availableReward,
          usedReward: rewardStatus.usedReward,
          pendingReward: rewardStatus.pendingReward
        }
      }
    })

  } catch (error) {
    console.error('❌ 同步客户奖励数据失败:', error)
    return NextResponse.json({
      success: false,
      error: '同步奖励数据失败'
    }, { status: 500 })
  }
}
