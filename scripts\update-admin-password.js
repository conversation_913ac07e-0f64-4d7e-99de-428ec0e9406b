/**
 * 🔐 风口云平台 - 管理员密码更新脚本
 * 
 * 功能说明：
 * - 更新管理员账户的默认密码
 * - 支持本地开发环境和生产环境
 * - 提供安全的密码哈希处理
 */

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')
const readline = require('readline')

// 创建readline接口用于用户输入
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// 提示用户输入的函数
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

// 隐藏密码输入的函数
function askPassword(question) {
  return new Promise((resolve) => {
    process.stdout.write(question)
    process.stdin.setRawMode(true)
    process.stdin.resume()
    process.stdin.setEncoding('utf8')
    
    let password = ''
    
    process.stdin.on('data', function(char) {
      char = char + ''
      
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false)
          process.stdin.pause()
          process.stdout.write('\n')
          resolve(password)
          break
        case '\u0003':
          process.exit()
          break
        case '\u007f': // backspace
          if (password.length > 0) {
            password = password.slice(0, -1)
            process.stdout.write('\b \b')
          }
          break
        default:
          password += char
          process.stdout.write('*')
          break
      }
    })
  })
}

async function updateAdminPassword() {
  const prisma = new PrismaClient()
  
  try {
    console.log('🔐 风口云平台 - 管理员密码更新工具')
    console.log('=' .repeat(50))
    
    // 检查当前环境
    const isProduction = process.env.NODE_ENV === 'production'
    const dbUrl = process.env.DATABASE_URL
    
    console.log(`📍 当前环境: ${isProduction ? '生产环境' : '开发环境'}`)
    console.log(`📊 数据库: ${dbUrl ? dbUrl.split('@')[1] || '本地SQLite' : '本地SQLite'}`)
    console.log('')
    
    // 查找现有管理员账户
    const existingAdmins = await prisma.admin.findMany({
      select: {
        id: true,
        username: true,
        name: true,
        email: true,
        isActive: true,
        lastLoginAt: true
      }
    })
    
    if (existingAdmins.length === 0) {
      console.log('❌ 未找到管理员账户')
      console.log('请先运行初始化脚本创建管理员账户')
      return
    }
    
    console.log('📋 现有管理员账户:')
    existingAdmins.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.username} (${admin.name}) - ${admin.isActive ? '激活' : '禁用'}`)
      if (admin.lastLoginAt) {
        console.log(`   最后登录: ${admin.lastLoginAt.toLocaleString('zh-CN')}`)
      }
    })
    console.log('')
    
    // 选择要更新的管理员
    let selectedAdmin
    if (existingAdmins.length === 1) {
      selectedAdmin = existingAdmins[0]
      console.log(`✅ 自动选择管理员: ${selectedAdmin.username}`)
    } else {
      const choice = await askQuestion('请选择要更新密码的管理员 (输入序号): ')
      const index = parseInt(choice) - 1
      
      if (index < 0 || index >= existingAdmins.length) {
        console.log('❌ 无效的选择')
        return
      }
      
      selectedAdmin = existingAdmins[index]
    }
    
    console.log('')
    console.log(`🎯 将为管理员 "${selectedAdmin.username}" 更新密码`)
    console.log('')
    
    // 输入新密码
    const newPassword = await askPassword('请输入新密码 (至少6位): ')
    
    if (newPassword.length < 6) {
      console.log('❌ 密码长度至少需要6位')
      return
    }
    
    const confirmPassword = await askPassword('请确认新密码: ')
    
    if (newPassword !== confirmPassword) {
      console.log('❌ 两次输入的密码不一致')
      return
    }
    
    console.log('')
    
    // 生产环境额外确认
    if (isProduction) {
      console.log('⚠️  您正在生产环境中更新管理员密码！')
      const confirm = await askQuestion('确认要继续吗？(输入 "YES" 确认): ')
      
      if (confirm !== 'YES') {
        console.log('❌ 操作已取消')
        return
      }
    }
    
    // 生成密码哈希
    console.log('🔄 正在生成安全密码哈希...')
    const saltRounds = isProduction ? 12 : 10
    const passwordHash = await bcrypt.hash(newPassword, saltRounds)
    
    // 更新密码
    console.log('🔄 正在更新密码...')
    const updatedAdmin = await prisma.admin.update({
      where: { id: selectedAdmin.id },
      data: { 
        passwordHash: passwordHash,
        updatedAt: new Date()
      },
      select: {
        id: true,
        username: true,
        name: true,
        updatedAt: true
      }
    })
    
    console.log('')
    console.log('✅ 密码更新成功！')
    console.log(`👤 管理员: ${updatedAdmin.username} (${updatedAdmin.name})`)
    console.log(`🕒 更新时间: ${updatedAdmin.updatedAt.toLocaleString('zh-CN')}`)
    console.log('')
    
    // 安全提醒
    console.log('🔒 安全提醒:')
    console.log('1. 请妥善保管新密码')
    console.log('2. 建议定期更换密码')
    console.log('3. 不要在不安全的环境中输入密码')
    if (isProduction) {
      console.log('4. 生产环境密码已更新，请通知相关人员')
    }
    
  } catch (error) {
    console.error('❌ 更新密码失败:', error)
    
    if (error.code === 'P2025') {
      console.log('💡 提示: 管理员账户不存在，请先运行初始化脚本')
    } else if (error.code === 'P1001') {
      console.log('💡 提示: 数据库连接失败，请检查数据库配置')
    }
    
    throw error
  } finally {
    await prisma.$disconnect()
    rl.close()
  }
}

// 运行脚本
if (require.main === module) {
  updateAdminPassword()
    .then(() => {
      console.log('\n🎉 脚本执行完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 脚本执行失败:', error.message)
      process.exit(1)
    })
}

module.exports = { updateAdminPassword }
