"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  FileSpreadsheet,
  ClipboardPaste,
  DollarSign,
  Settings,
  FileText,
  Grid3X3,
  ChevronDown,
  Trash2
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function TestToolbarLayout() {
  const [selectedVentIds, setSelectedVentIds] = useState(new Set())
  const [floors] = useState([
    { id: '1', floorName: '1楼', vents: [1, 2, 3] },
    { id: '2', floorName: '2楼', vents: [1, 2] },
    { id: '3', floorName: '3楼', vents: [1, 2, 3, 4] },
    { id: '4', floorName: '4楼', vents: [1] },
    { id: '5', floorName: '5楼', vents: [1, 2] },
  ])

  const totalVents = floors.reduce((total, floor) => total + floor.vents.length, 0)

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">工具栏布局测试</h1>
        
        <Card>
          <CardHeader>
            <div className="flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>楼层风口录单</CardTitle>
                  <CardDescription>表格式批量录入，高效便捷，支持Excel导入</CardDescription>
                </div>
              </div>
              
              {/* 工具栏区域 - 响应式布局 */}
              <div className="border-t border-gray-100 pt-4">
                <div className="flex flex-col xl:flex-row gap-4 xl:items-center xl:justify-between">
                  {/* 左侧：主要功能按钮 */}
                  <div className="flex flex-wrap gap-2 items-center">
                    {/* 核心功能按钮组 */}
                    <div className="flex gap-2">
                      {/* Excel导入下拉菜单 */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="outline"
                            className="hover:bg-blue-50 hover:border-blue-300"
                          >
                            <FileSpreadsheet className="h-4 w-4 mr-2" />
                            Excel导入
                            <ChevronDown className="h-3 w-3 ml-1" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start">
                          <DropdownMenuItem>
                            <FileSpreadsheet className="h-4 w-4 mr-2" />
                            导入单个项目
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <FileSpreadsheet className="h-4 w-4 mr-2" />
                            导入多个项目
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    {/* 智能粘贴识别按钮 */}
                    <Button
                      variant="outline"
                      className="hover:bg-blue-50 hover:border-blue-300"
                    >
                      <ClipboardPaste className="h-4 w-4 mr-2" />
                      智能粘贴识别
                    </Button>

                    {/* 智能OCR识别按钮 */}
                    <Button
                      variant="outline"
                      className="hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 hover:border-purple-300 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200"
                    >
                      <Grid3X3 className="h-4 w-4 mr-2 text-purple-600" />
                      🤖 智能图片识别
                    </Button>

                    {/* 批量操作下拉菜单 */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          className="hover:bg-green-50 hover:border-green-300"
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          批量操作
                          <ChevronDown className="h-3 w-3 ml-1" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start">
                        <DropdownMenuItem>
                          <DollarSign className="h-4 w-4 mr-2" />
                          全局批量改价
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="h-4 w-4 mr-2" />
                          全局批量改出风口
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="h-4 w-4 mr-2" />
                          全局批量改回风口
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  {/* 右侧：批量备注管理按钮 */}
                  <div className="batch-notes-container flex flex-wrap items-center gap-2 xl:border-l xl:border-gray-300 xl:pl-4">
                    {/* 批量备注操作组 */}
                    <div className="flex items-center gap-1">
                      <span className="text-sm text-gray-600 font-medium whitespace-nowrap">批量备注:</span>
                      <Button
                        onClick={() => setSelectedVentIds(selectedVentIds.size === totalVents ? new Set() : new Set(Array.from({length: totalVents}, (_, i) => i)))}
                        variant="outline"
                        size="sm"
                        className="hover:bg-purple-50 hover:border-purple-300 px-2"
                      >
                        <input
                          type="checkbox"
                          checked={selectedVentIds.size > 0 && selectedVentIds.size === totalVents}
                          onChange={() => {}}
                          className="h-3 w-3 mr-1"
                          readOnly
                        />
                        {selectedVentIds.size === totalVents ? '取消全选' : '全选'}
                        {selectedVentIds.size > 0 && ` (${selectedVentIds.size})`}
                      </Button>
                    </div>
                    
                    {/* 备注操作按钮 */}
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={selectedVentIds.size === 0}
                        className="hover:bg-green-50 hover:border-green-300 disabled:opacity-50 px-2"
                        title="批量添加备注 (Ctrl+Shift+N)"
                      >
                        <FileText className="h-3 w-3 mr-1" />
                        添加
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={selectedVentIds.size === 0}
                        className="hover:bg-red-50 hover:border-red-300 disabled:opacity-50 px-2"
                        title="批量清除备注 (Ctrl+Shift+D)"
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        清除
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center text-gray-600">
                <p>这是工具栏布局测试页面</p>
                <p>请调整浏览器窗口大小来测试响应式布局</p>
                <p>当前选中风口数量: {selectedVentIds.size}</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                {floors.map((floor) => (
                  <div key={floor.id} className="border border-gray-200 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">{floor.floorName}</h3>
                    <p className="text-sm text-gray-600">风口数量: {floor.vents.length}</p>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">测试说明</h2>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• 在大屏幕上，工具栏按钮应该水平排列，批量备注按钮在右侧</li>
            <li>• 在中等屏幕上，工具栏应该换行显示</li>
            <li>• 在小屏幕上，工具栏应该垂直堆叠</li>
            <li>• 下拉菜单应该正常工作，减少工具栏的宽度占用</li>
            <li>• 批量备注按钮应该始终可见，不会被挤出视野</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
