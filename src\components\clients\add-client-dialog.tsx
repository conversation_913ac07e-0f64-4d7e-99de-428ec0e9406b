"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger  } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Loader2 } from "lucide-react"
import { db } from "@/lib/database"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import type { Client } from "@/types"

interface AddClientDialogProps {
  onClientAdded?: (client: Client) => void
  existingClients?: Client[]
}

export function AddClientDialog({ onClientAdded, existingClients = [] }: AddClientDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    email: "",
    company: "",
    address: "",
    referrerId: "",
    referrerName: ""
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 验证表单数据
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "客户名称不能为空"
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "联系电话不能为空"
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = "请输入正确的11位手机号码（以1开头）"
    }

    if (!formData.address.trim()) {
      newErrors.address = "客户地址不能为空"
    }

    // 检查电话号码是否重复
    if (formData.phone && existingClients.some(client => client.phone === formData.phone)) {
      newErrors.phone = "该电话号码已存在"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理推荐人选择
  const handleReferrerChange = (referrerId: string) => {
    if (referrerId === "none") {
      setFormData(prev => ({
        ...prev,
        referrerId: "",
        referrerName: ""
      }))
    } else {
      const selectedReferrer = existingClients.find(c => c.id === referrerId)
      setFormData(prev => ({
        ...prev,
        referrerId,
        referrerName: selectedReferrer?.name || ""
      }))
    }
  }

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()

      if (!factoryId) {
        setErrors({ general: "无法获取工厂信息" })
        return
      }

      if (!db || typeof db.createClient !== 'function') {
        setErrors({ general: "数据库服务不可用" })
        return
      }

      const clientData = {
        factoryId,
        name: formData.name.trim(),
        phone: formData.phone.trim(),
        email: formData.email.trim() || undefined,
        company: formData.company.trim() || undefined,
        address: formData.address.trim(),
        referrerId: formData.referrerId && formData.referrerId !== "none" ? formData.referrerId : undefined,
        referrerName: formData.referrerName || undefined
        // 其他统计字段会使用数据库默认值
      }

      const createdClient = await db.createClient(clientData)

      if (createdClient) {
        console.log('✅ 客户创建成功:', createdClient)
        
        // 重置表单
        setFormData({
          name: "",
          phone: "",
          email: "",
          company: "",
          address: "",
          referrerId: "",
          referrerName: ""
        })
        setErrors({})
        setOpen(false)

        // 通知父组件
        if (onClientAdded) {
          onClientAdded(createdClient)
        }
      } else {
        setErrors({ general: "客户创建失败，请重试" })
      }
    } catch (error) {
      console.error('创建客户失败:', error)
      setErrors({ general: `创建失败: ${error.message}` })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          添加客户
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>添加新客户</DialogTitle>
          <DialogDescription>
            填写客户的基本信息，带 * 的字段为必填项
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 错误提示 */}
          {errors.general && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {errors.general}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 客户名称 */}
            <div className="space-y-2">
              <Label htmlFor="name">客户名称 *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="请输入客户名称或公司名称"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
            </div>

            {/* 联系电话 */}
            <div className="space-y-2">
              <Label htmlFor="phone">联系电话 *</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '') // 只允许数字
                  setFormData(prev => ({ ...prev, phone: value }))
                }}
                placeholder="请输入11位电话号码"
                maxLength={11}
                pattern="[0-9]*"
                className={errors.phone ? "border-red-500" : ""}
              />
              {errors.phone && <p className="text-sm text-red-600">{errors.phone}</p>}
            </div>

            {/* 邮箱地址 */}
            <div className="space-y-2">
              <Label htmlFor="email">邮箱地址</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="请输入邮箱地址（可选）"
              />
            </div>

            {/* 公司名称 */}
            <div className="space-y-2">
              <Label htmlFor="company">公司名称</Label>
              <Input
                id="company"
                value={formData.company}
                onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                placeholder="请输入公司名称（可选）"
              />
            </div>
          </div>

          {/* 客户地址 */}
          <div className="space-y-2">
            <Label htmlFor="address">客户地址 *</Label>
            <Textarea
              id="address"
              value={formData.address}
              onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
              placeholder="请输入详细地址"
              rows={3}
              className={errors.address ? "border-red-500" : ""}
            />
            {errors.address && <p className="text-sm text-red-600">{errors.address}</p>}
          </div>

          {/* 推荐人 */}
          {existingClients.length > 0 && (
            <div className="space-y-2">
              <Label htmlFor="referrer">推荐人</Label>
              <Select value={formData.referrerId} onValueChange={handleReferrerChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择推荐人（可选）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">无推荐人</SelectItem>
                  {existingClients.map(client => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.name} - {client.phone}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* 按钮 */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  创建中...
                </>) : (
                "创建客户"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
