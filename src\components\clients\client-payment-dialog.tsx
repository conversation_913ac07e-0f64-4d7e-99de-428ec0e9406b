"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, DialogHeader, DialogTitle  } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DollarSign, Loader2, Plus, Minus, CheckCircle } from "lucide-react"
import { db } from "@/lib/database/client"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import { safeAmount } from "@/lib/utils/number-utils"
import type { Client, Order } from "@/types"

interface ClientPaymentDialogProps {
  client: Client | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onPaymentUpdated?: () => void
}

export function ClientPaymentDialog({ client, open, onOpenChange, onPaymentUpdated }: ClientPaymentDialogProps) {
  const [loading, setLoading] = useState(false)
  const [clientOrders, setClientOrders] = useState<Order[]>([])
  const [paymentAmount, setPaymentAmount] = useState(0)
  const [paymentNotes, setPaymentNotes] = useState("")
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [successMessage, setSuccessMessage] = useState("")
  const [statistics, setStatistics] = useState({
    totalAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0
  })

  // 加载客户订单数据
  const loadClientOrders = async () => {
    if (!client) return

    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()

      if (!factoryId || !db) {
        console.warn('无法获取工厂ID或数据库服务不可用')
        return
      }

      // 使用新的根据客户ID获取订单的方法
      let orders: Order[] = []
      if (typeof db.getOrdersByClientId === 'function') {
        console.log('🔄 使用客户ID获取订单:', client.id)
        orders = await db.getOrdersByClientId(client.id)
      } else {
        // 回退到原来的方法
        console.log('🔄 回退到工厂订单筛选方法')
        const allOrders = await db.getOrdersByFactoryId(factoryId)
        orders = allOrders.filter(order => order.clientId === client.id)
      }

      setClientOrders(orders)
      console.log('✅ 客户订单加载成功:', orders.length, '条')

      // 计算统计数据
      const totalAmount = orders.reduce((sum, order) => sum + Number(order.totalAmount || 0), 0)
      const paidAmount = orders.reduce((sum, order) => sum + Number(order.paidAmount || 0), 0)
      const unpaidAmount = totalAmount - paidAmount

      setStatistics({
        totalAmount,
        paidAmount,
        unpaidAmount
      })

    } catch (error) {
      console.error('加载客户订单失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 当对话框打开时加载数据
  useEffect(() => {
    if (open && client) {
      loadClientOrders()
    }
  }, [open, client])

  // 验证付款金额
  const validatePayment = () => {
    const newErrors: Record<string, string> = {}

    if (paymentAmount <= 0) {
      newErrors.paymentAmount = "付款金额必须大于0"
    }

    if (paymentAmount > statistics.unpaidAmount) {
      newErrors.paymentAmount = "付款金额不能超过未付金额"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 智能分配付款到订单
  const allocatePaymentToOrders = (amount: number, orders: Order[]) => {
    const unpaidOrders = orders
      .filter(order => (Number(order.totalAmount || 0) - Number(order.paidAmount || 0)) > 0)
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()) // 按创建时间排序，优先付早期订单

    const allocations: { orderId: string, amount: number }[] = []
    let remainingAmount = amount

    for (const order of unpaidOrders) {
      if (remainingAmount <= 0) break

      const unpaidAmount = Number(order.totalAmount || 0) - Number(order.paidAmount || 0)
      const allocationAmount = Math.min(remainingAmount, unpaidAmount)
      
      allocations.push({
        orderId: order.id,
        amount: allocationAmount
      })

      remainingAmount -= allocationAmount
    }

    return allocations
  }

  // 处理付款
  const handlePayment = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!client || !validatePayment()) {
      return
    }

    try {
      setLoading(true)

      // 智能分配付款到各个订单
      const allocations = allocatePaymentToOrders(paymentAmount, clientOrders)

      // 更新每个订单的付款状态
      for (const allocation of allocations) {
        const order = clientOrders.find(o => o.id === allocation.orderId)
        if (order) {
          const newPaidAmount = Number(order.paidAmount || 0) + allocation.amount
          // 🔧 修复：使用精度处理避免浮点数比较问题
          const paymentStatus = (Math.abs(newPaidAmount - Number(order.totalAmount)) < 0.01 || newPaidAmount >= Number(order.totalAmount)) ? 'paid' : 'partial'

          // 确保 paidAmount 是正确的数字格式，避免浮点数精度问题
          const formattedPaidAmount = Math.round(newPaidAmount * 100) / 100

          const updateSuccess = await db.updateOrderPayment(order.id, {
            paymentStatus,
            paidAmount: formattedPaidAmount,
            paymentNotes: paymentNotes || `客户付款 ${safeAmount(allocation.amount)}`
          })

          if (!updateSuccess) {
            throw new Error(`更新订单 ${order.orderNumber} 付款信息失败`)
          }
        }
      }

      // 更新客户统计信息
      await db.updateClientStatistics(client.id)

      console.log('✅ 客户付款处理成功')

      // 显示成功消息
      setSuccessMessage(`付款 ${safeAmount(paymentAmount)} 记录成功！`)

      // 重置表单
      setPaymentAmount(0)
      setPaymentNotes("")
      setErrors({})

      // 重新加载数据
      await loadClientOrders()

      // 通知父组件
      if (onPaymentUpdated) {
        onPaymentUpdated()
      }

      // 3秒后清除成功消息
      setTimeout(() => {
        setSuccessMessage("")
      }, 3000)

    } catch (error) {
      console.error('处理客户付款失败:', error)
      setErrors({ general: `付款处理失败: ${error.message}` })
    } finally {
      setLoading(false)
    }
  }

  if (!client) return null

  // 获取付款状态颜色
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'partial': return 'bg-yellow-100 text-yellow-800'
      case 'unpaid': return 'bg-red-100 text-red-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // 获取付款状态文本
  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'paid': return '已付清'
      case 'partial': return '部分付款'
      case 'unpaid': return '未付款'
      case 'overdue': return '逾期'
      default: return '未知'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>客户付款管理 - {client.name}</span>
          </DialogTitle>
          <DialogDescription>
            管理客户的付款记录，系统会智能分配到相应订单
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">加载付款信息中...</span>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 财务概览 */}
            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-lg font-bold text-blue-600">{safeAmount(statistics.totalAmount)}</div>
                  <p className="text-sm text-gray-600">总金额</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-lg font-bold text-green-600">{safeAmount(statistics.paidAmount)}</div>
                  <p className="text-sm text-gray-600">已付金额</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-lg font-bold text-red-600">{safeAmount(statistics.unpaidAmount)}</div>
                  <p className="text-sm text-gray-600">未付金额</p>
                </CardContent>
              </Card>
            </div>

            {/* 付款表单 */}
            {statistics.unpaidAmount > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Plus className="h-4 w-4" />
                    <span>记录新付款</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handlePayment} className="space-y-4">
                    {/* 成功提示 */}
                    {successMessage && (
                      <div className="p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4" />
                        <span>{successMessage}</span>
                      </div>
                    )}

                    {/* 错误提示 */}
                    {errors.general && (
                      <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                        {errors.general}
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* 付款金额 */}
                      <div className="space-y-2">
                        <Label htmlFor="paymentAmount">付款金额 *</Label>
                        <Input
                          id="paymentAmount"
                          type="number"
                          step="0.01"
                          min="0"
                          max={statistics.unpaidAmount}
                          value={paymentAmount}
                          onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                          placeholder="请输入付款金额"
                          className={errors.paymentAmount ? "border-red-500" : ""}
                        />
                        {errors.paymentAmount && <p className="text-sm text-red-600">{errors.paymentAmount}</p>}
                      </div>

                      {/* 快捷金额按钮 */}
                      <div className="space-y-2">
                        <Label>快捷选择</Label>
                        <div className="flex flex-wrap gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setPaymentAmount(statistics.unpaidAmount)}
                          >
                            全部付清
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setPaymentAmount(statistics.unpaidAmount / 2)}
                          >
                            付一半
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* 付款备注 */}
                    <div className="space-y-2">
                      <Label htmlFor="paymentNotes">付款备注</Label>
                      <Textarea
                        id="paymentNotes"
                        value={paymentNotes}
                        onChange={(e) => setPaymentNotes(e.target.value)}
                        placeholder="请输入付款相关备注信息"
                        rows={2}
                      />
                    </div>

                    {/* 提交按钮 */}
                    <Button
                      type="submit"
                      disabled={loading || paymentAmount <= 0}
                      className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-2 border-green-400"
                      size="lg"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          <span className="text-base">处理中...</span>
                        </>
                      ) : (
                        <>
                          <DollarSign className="h-5 w-5 mr-2" />
                          <span className="text-base">记录付款 {safeAmount(paymentAmount)}</span>
                        </>
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            )}

            {/* 订单列表 */}
            <Card>
              <CardHeader>
                <CardTitle>订单付款状态</CardTitle>
              </CardHeader>
              <CardContent>
                {clientOrders.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    暂无订单记录
                  </div>
                ) : (
                  <div className="space-y-3">
                    {clientOrders.map((order) => {
                      const unpaidAmount = Number(order.totalAmount || 0) - Number(order.paidAmount || 0)
                      return (
                        <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div>
                            <div className="font-medium">{order.orderNumber || order.id}</div>
                            <div className="text-sm text-gray-600">
                              {new Date(order.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm">
                              <span className="text-gray-600">总额：</span>
                              <span className="font-medium">{safeAmount(order.totalAmount)}</span>
                            </div>
                            <div className="text-sm">
                              <span className="text-gray-600">已付：</span>
                              <span className="font-medium text-green-600">{safeAmount(order.paidAmount || 0)}</span>
                            </div>
                            <div className="text-sm">
                              <span className="text-gray-600">未付：</span>
                              <span className={`font-medium ${unpaidAmount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                                {safeAmount(unpaidAmount)}
                              </span>
                            </div>
                            <Badge className={getPaymentStatusColor(order.paymentStatus || 'unpaid')}>
                              {getPaymentStatusText(order.paymentStatus || 'unpaid')}
                            </Badge>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
