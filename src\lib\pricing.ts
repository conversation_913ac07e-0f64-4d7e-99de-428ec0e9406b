import type { OrderItem } from '@/types'
import { smartDimensionRecognition } from '@/lib/utils/dimension-utils'

// 获取产品类型的中文名称
export function getProductTypeName(productType: OrderItem['productType'] | string): string {
  const names = {
    // 常规风口类型 (元/㎡)
    double_white_outlet: '双层白色出风口',
    double_black_outlet: '双层黑色出风口',
    white_black_bottom_outlet: '白色黑底出风口',
    white_black_bottom_return: '白色黑底回风口',
    white_return: '白色回风口',
    black_return: '黑色回风口',
    white_linear: '白色线型风口',
    black_linear: '黑色线型风口',
    white_linear_return: '白色线型回风口',
    black_linear_return: '黑色线型回风口',
    maintenance: '检修口',

    // 高端风口系列 (元/m)
    high_end_white_outlet: '高端白色出风口',
    high_end_black_outlet: '高端黑色出风口',
    high_end_white_return: '高端白色回风口',
    high_end_black_return: '高端黑色回风口',
    arrow_outlet: '箭型出风口',
    arrow_return: '箭型回风口',
    claw_outlet: '爪型出风口',
    claw_return: '爪型回风口',
    black_white_outlet: '黑白双色出风口',
    black_white_return: '黑白双色回风口',
    black_white_dual_outlet: '黑白双色出风口',
    black_white_dual_return: '黑白双色回风口',
    wood_outlet: '木纹出风口',
    wood_return: '木纹回风口',
    wood_grain_outlet: '木纹出风口',
    wood_grain_return: '木纹回风口',
    white_putty_outlet: '白色腻子粉出风口',
    white_putty_return: '白色腻子粉回风口',
    black_putty_outlet: '黑色腻子粉出风口',
    black_putty_return: '黑色腻子粉回风口',

    // 石膏板风口 (元/m)
    white_gypsum_outlet: '白色石膏板出风口',
    white_gypsum_return: '白色石膏板回风口',
    black_gypsum_outlet: '黑色石膏板出风口',
    black_gypsum_return: '黑色石膏板回风口',

    // 兼容旧版本数据
    square: '双层白色出风口',
    round: '白色回风口',
    rectangular: '黑白双色出风口',
    custom: '高端白色出风口'
  }
  // 多层级回退确保总是返回有意义的名称
  const result = names[productType as keyof typeof names] ||
                productType ||
                '双层白色出风口' // 默认类型，而不是"未知类型"

  return result
}

// 获取产品类型的详细说明
export function getProductTypeDescription(productType: OrderItem['productType'] | string): string {
  const descriptions = {
    // 常规风口类型 (元/㎡)
    double_white_outlet: '双层白色出风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    double_black_outlet: '双层黑色出风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    white_black_bottom_outlet: '白色黑底出风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    white_black_bottom_return: '白色黑底回风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    white_return: '白色回风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    black_return: '黑色回风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    white_linear: '白色线型风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    black_linear: '黑色线型风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    white_linear_return: '白色线型回风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    black_linear_return: '黑色线型回风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    maintenance: '检修口：(长+60mm)×(宽+60mm)×单价(元/㎡)',

    // 高端风口系列 (元/m)
    high_end_white_outlet: '高端白色出风口：(长+120mm)×单价(元/m)',
    high_end_black_outlet: '高端黑色出风口：(长+120mm)×单价(元/m)',
    high_end_white_return: '高端白色回风口：(长+120mm)×单价(元/m)',
    high_end_black_return: '高端黑色回风口：(长+120mm)×单价(元/m)',
    arrow_outlet: '箭型出风口：(长+120mm)×单价(元/m)',
    arrow_return: '箭型回风口：(长+120mm)×单价(元/m)',
    black_white_dual_outlet: '黑白双色出风口：(长+120mm)×单价(元/m)',
    black_white_dual_return: '黑白双色回风口：(长+120mm)×单价(元/m)',
    wood_grain_outlet: '木纹出风口：(长+120mm)×单价(元/m)',
    wood_grain_return: '木纹回风口：(长+120mm)×单价(元/m)',
    white_putty_outlet: '白色腻子粉出风口：(长+120mm)×单价(元/m)',
    white_putty_return: '白色腻子粉回风口：(长+120mm)×单价(元/m)',
    black_putty_outlet: '黑色腻子粉出风口：(长+120mm)×单价(元/m)',
    black_putty_return: '黑色腻子粉回风口：(长+120mm)×单价(元/m)',

    // 石膏板风口 (元/m)
    white_gypsum_outlet: '白色石膏板出风口：(长+120mm)×单价(元/m)',
    white_gypsum_return: '白色石膏板回风口：(长+120mm)×单价(元/m)',
    black_gypsum_outlet: '黑色石膏板出风口：(长+120mm)×单价(元/m)',
    black_gypsum_return: '黑色石膏板回风口：(长+120mm)×单价(元/m)',

    // 兼容旧版本数据
    square: '双层白色出风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    round: '白色回风口：(长+60mm)×(宽+60mm)×单价(元/㎡)',
    rectangular: '高端白色出风口：(长+120mm)×单价(元/m)',
    custom: '高端白色出风口：(长+120mm)×单价(元/m)'
  }
  return descriptions[productType as keyof typeof descriptions] || `${productType}：详细说明待补充`
}

// 验证尺寸数据
export function validateDimensions(
  productType: OrderItem['productType'],
  dimensions: OrderItem['dimensions']
): { isValid: boolean; errors: string[]; correctedDimensions?: { length: number; width: number } } {
  const errors: string[] = []

  // 所有风口类型都需要长度和宽度
  if (dimensions.length === undefined || dimensions.length < 0) {
    errors.push('长度不能为负数')
  }
  if (dimensions.width === undefined || dimensions.width < 0) {
    errors.push('宽度不能为负数')
  }
  // 注意：允许为0，因为0值有特殊含义（结果为0）

  // 如果尺寸有效，应用智能识别
  let correctedDimensions
  if (errors.length === 0 && dimensions.length && dimensions.width) {
    correctedDimensions = smartDimensionRecognition(dimensions.length, dimensions.width)
  }

  return {
    isValid: errors.length === 0,
    errors,
    correctedDimensions
  }
}

// 格式化尺寸显示
export function formatDimensions(
  productType: OrderItem['productType'],
  dimensions: OrderItem['dimensions']
): string {
  // 应用智能尺寸识别后显示
  const smartDimensions = smartDimensionRecognition(dimensions.length || 0, dimensions.width || 0)
  return `${smartDimensions.length}×${smartDimensions.width}mm`
}

// 获取产品类型需要的输入字段
export function getRequiredFields(productType: OrderItem['productType']): string[] {
  // 所有风口类型都支持长度和宽度输入
  // 但高端风口的宽度不参与价格计算，只用于记录
  return ['length', 'width']
}
