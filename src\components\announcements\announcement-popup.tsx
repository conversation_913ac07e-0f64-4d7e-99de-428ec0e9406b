"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle  } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Bell, 
  X, 
  AlertTriangle, 
  Info, 
  Clock,
  Building,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import { db } from "@/lib/database"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import type { Announcement } from "@/types"

interface AnnouncementPopupProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAnnouncementRead?: (announcementId: string) => void
}

export function AnnouncementPopup({ open, onOpenChange, onAnnouncementRead }: AnnouncementPopupProps) {
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [loading, setLoading] = useState(false)

  // 加载未读公告
  const loadUnreadAnnouncements = async () => {
    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()

      if (!factoryId) {
        console.warn('⚠️ 无法获取工厂ID')
        return
      }

      console.log('🔄 公告弹窗开始加载未读公告...', { factoryId, timestamp: new Date().toISOString() })

      // 直接使用API获取未读公告，确保状态过滤正确
      const response = await fetch(`/api/announcements/active?factoryId=${factoryId}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || '获取未读公告失败')
      }

      const unreadAnnouncements = result.data?.announcements || []

      console.log('📢 公告弹窗加载未读公告完成:', {
        factoryId,
        count: unreadAnnouncements.length,
        announcements: unreadAnnouncements.map(a => ({
          id: a.id,
          title: a.title,
          type: a.type
        })),
        timestamp: new Date().toISOString()
      })

      setAnnouncements(unreadAnnouncements)
      setCurrentIndex(0)
    } catch (error) {
      console.error('❌ 加载未读公告失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 标记当前公告为已读
  const markCurrentAsRead = async () => {
    const factoryId = getCurrentFactoryId()
    const currentAnnouncement = announcements[currentIndex]

    if (!factoryId || !currentAnnouncement) {
      console.warn('⚠️ 无法标记公告为已读：缺少工厂ID或公告信息')
      return
    }

    try {
      console.log('📝 开始标记公告为已读:', {
        factoryId,
        announcementId: currentAnnouncement.id,
        title: currentAnnouncement.title
      })

      // 使用API调用而不是直接调用数据库方法
      const response = await fetch('/api/announcements/mark-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factoryId: factoryId,
          announcementId: currentAnnouncement.id
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || '标记已读失败')
      }

      onAnnouncementRead?.(currentAnnouncement.id)
      console.log('✅ 公告已标记为已读:', currentAnnouncement.title)
    } catch (error) {
      console.error('❌ 标记已读失败:', error)
      // 即使API调用失败，也要通知父组件，避免界面卡住
      onAnnouncementRead?.(currentAnnouncement.id)
    }
  }

  // 关闭当前公告
  const dismissCurrentAnnouncement = async () => {
    const factoryId = getCurrentFactoryId()
    const currentAnnouncement = announcements[currentIndex]

    if (!factoryId || !currentAnnouncement) return

    try {
      // 使用API调用关闭公告（会自动标记为已读）
      const response = await fetch('/api/announcements/dismiss', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factoryId: factoryId,
          announcementId: currentAnnouncement.id
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || '关闭公告失败')
      }

      onAnnouncementRead?.(currentAnnouncement.id)

      // 从列表中移除当前公告
      const newAnnouncements = announcements.filter((_, index) => index !== currentIndex)
      setAnnouncements(newAnnouncements)

      // 调整当前索引
      if (newAnnouncements.length === 0) {
        // 没有更多公告，关闭弹窗
        onOpenChange(false)
      } else if (currentIndex >= newAnnouncements.length) {
        // 当前索引超出范围，调整到最后一个
        setCurrentIndex(newAnnouncements.length - 1)
      }

      console.log('✅ 公告已关闭:', currentAnnouncement.title)
    } catch (error) {
      console.error('❌ 关闭公告失败:', error)
    }
  }

  // 下一个公告
  const nextAnnouncement = () => {
    if (currentIndex < announcements.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  // 上一个公告
  const prevAnnouncement = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  // 关闭所有公告
  const dismissAllAnnouncements = async () => {
    const factoryId = getCurrentFactoryId()

    if (!factoryId) return

    try {
      // 使用API调用关闭所有公告
      for (const announcement of announcements) {
        const response = await fetch('/api/announcements/dismiss', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            factoryId: factoryId,
            announcementId: announcement.id
          })
        })

        if (!response.ok) {
          console.error(`关闭公告失败: ${announcement.title}`)
          continue
        }

        const result = await response.json()
        if (result.success) {
          onAnnouncementRead?.(announcement.id)
        }
      }

      setAnnouncements([])
      onOpenChange(false)

      console.log('✅ 所有公告已关闭')
    } catch (error) {
      console.error('❌ 关闭所有公告失败:', error)
    }
  }

  // 获取公告类型图标
  const getAnnouncementIcon = (type: string) => {
    switch (type) {
      case 'urgent':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  // 获取公告类型文本
  const getAnnouncementTypeText = (type: string) => {
    switch (type) {
      case 'urgent': return '紧急公告'
      case 'warning': return '重要通知'
      case 'info': return '信息公告'
      default: return '公告'
    }
  }

  // 获取公告类型样式
  const getAnnouncementStyle = (type: string) => {
    switch (type) {
      case 'urgent':
        return {
          cardClass: 'border-red-200 bg-red-50',
          badgeClass: 'bg-red-100 text-red-800',
          titleClass: 'text-red-900'
        }
      case 'warning':
        return {
          cardClass: 'border-yellow-200 bg-yellow-50',
          badgeClass: 'bg-yellow-100 text-yellow-800',
          titleClass: 'text-yellow-900'
        }
      case 'info':
      default:
        return {
          cardClass: 'border-blue-200 bg-blue-50',
          badgeClass: 'bg-blue-100 text-blue-800',
          titleClass: 'text-blue-900'
        }
    }
  }

  // 格式化时间
  const formatTime = (date: Date | string) => {
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date

      // 检查日期是否有效
      if (isNaN(dateObj.getTime())) {
        return '时间未知'
      }

      const now = new Date()
      const diff = now.getTime() - dateObj.getTime()
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days > 0) return `${days}天前`
      if (hours > 0) return `${hours}小时前`
      if (minutes > 0) return `${minutes}分钟前`
      return '刚刚'
    } catch (error) {
      console.error('格式化时间失败:', error)
      return '时间未知'
    }
  }

  // 当弹窗打开时加载公告
  useEffect(() => {
    if (open) {
      loadUnreadAnnouncements()
    }
  }, [open])

  // 如果没有公告，不显示弹窗
  if (!open || announcements.length === 0) {
    return null
  }

  const currentAnnouncement = announcements[currentIndex]
  const style = getAnnouncementStyle(currentAnnouncement.type)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Bell className="h-5 w-5 text-blue-500" />
            <span>总部新公告</span>
            <Badge variant="secondary" className="text-xs">
              {currentIndex + 1} / {announcements.length}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            您有 {announcements.length} 条新公告需要查看
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">加载中...</span>
          </div>
        ) : (
          <div className="space-y-4">
            {/* 公告内容 */}
            <Card className={style.cardClass}>
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  {getAnnouncementIcon(currentAnnouncement.type)}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-3">
                      <h3 className={`font-semibold text-lg ${style.titleClass}`}>
                        {currentAnnouncement.title}
                      </h3>
                      <Badge className={style.badgeClass}>
                        {getAnnouncementTypeText(currentAnnouncement.type)}
                      </Badge>
                    </div>
                    
                    <div className="prose prose-sm max-w-none mb-4">
                      <p className="text-gray-700 whitespace-pre-wrap">
                        {currentAnnouncement.content}
                      </p>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <Building className="h-4 w-4" />
                          <span>总部发布</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{formatTime(currentAnnouncement.publishedAt)}</span>
                        </div>
                      </div>
                      {currentAnnouncement.expiresAt && (
                        <div className="text-orange-600">
                          有效期至: {(() => {
                            try {
                              const expiresDate = typeof currentAnnouncement.expiresAt === 'string'
                                ? new Date(currentAnnouncement.expiresAt)
                                : currentAnnouncement.expiresAt
                              return expiresDate.toLocaleDateString('zh-CN')
                            } catch (error) {
                              return '日期格式错误'
                            }
                          })()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 操作按钮 */}
            <div className="flex items-center justify-between">
              {/* 导航按钮 */}
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={prevAnnouncement}
                  disabled={currentIndex === 0}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  上一个
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={nextAnnouncement}
                  disabled={currentIndex === announcements.length - 1}
                >
                  下一个
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>

              {/* 主要操作按钮 */}
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={dismissAllAnnouncements}
                >
                  全部已读
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={dismissCurrentAnnouncement}
                >
                  <X className="h-4 w-4 mr-1" />
                  关闭此条
                </Button>
                <Button
                  onClick={async () => {
                    await markCurrentAsRead()

                    // 从列表中移除当前已读的公告
                    const newAnnouncements = announcements.filter((_, index) => index !== currentIndex)
                    setAnnouncements(newAnnouncements)

                    if (newAnnouncements.length === 0) {
                      // 没有更多公告，关闭弹窗
                      onOpenChange(false)
                    } else if (currentIndex >= newAnnouncements.length) {
                      // 当前索引超出范围，调整到最后一个
                      setCurrentIndex(newAnnouncements.length - 1)
                    }
                    // 如果还有公告且索引有效，继续显示下一个公告
                  }}
                >
                  我知道了
                </Button>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
