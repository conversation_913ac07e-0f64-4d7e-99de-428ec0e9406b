/**
 * 🇨🇳 风口云平台 - 股东管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 获取股东列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')

    if (!factoryId) {
      return NextResponse.json(
        { error: '工厂ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📞 获取股东列表请求:', factoryId)

    // 获取工厂股东列表
    const shareholders = await db.getShareholdersByFactoryId(factoryId)

    return NextResponse.json({
      success: true,
      shareholders
    })

  } catch (error) {
    console.error('❌ 获取股东列表失败:', error)
    return NextResponse.json(
      { error: '获取股东列表失败' },
      { status: 500 }
    )
  }
}

// 创建股东
export async function POST(request: NextRequest) {
  try {
    const shareholderData = await request.json()

    console.log('📞 创建股东请求:', shareholderData.name)

    // 验证必需字段
    if (!shareholderData.factoryId || !shareholderData.name || !shareholderData.shareholderType) {
      return NextResponse.json(
        { error: '工厂ID、股东姓名和股东类型不能为空' },
        { status: 400 }
      )
    }

    // 🔧 修改：持股数量改为可选项，持股比例和投资金额仍为必填
    if (!shareholderData.sharePercentage || !shareholderData.investmentAmount) {
      return NextResponse.json(
        { error: '持股比例和投资金额不能为空' },
        { status: 400 }
      )
    }

    if (!shareholderData.joinDate) {
      return NextResponse.json(
        { error: '入股日期不能为空' },
        { status: 400 }
      )
    }

    // 创建股东
    const shareholder = await db.createShareholder(shareholderData)

    if (!shareholder) {
      return NextResponse.json(
        { error: '创建股东失败' },
        { status: 500 }
      )
    }

    console.log('✅ 股东创建成功:', shareholder.id)

    return NextResponse.json({
      success: true,
      shareholder
    })

  } catch (error) {
    console.error('❌ 创建股东失败:', error)
    
    // 处理特定错误
    if ((error as Error).message && (error as Error).message.includes('股权比例')) {
      return NextResponse.json(
        { error: (error as Error).message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: '创建股东失败' },
      { status: 500 }
    )
  }
}

// 更新股东信息
export async function PUT(request: NextRequest) {
  try {
    const { id, ...updates } = await request.json()

    console.log('📞 更新股东信息请求:', id)

    if (!id) {
      return NextResponse.json(
        { error: '股东ID不能为空' },
        { status: 400 }
      )
    }

    // 更新股东信息
    const shareholder = await db.updateShareholder(id, updates)

    if (!shareholder) {
      return NextResponse.json(
        { error: '更新股东信息失败' },
        { status: 500 }
      )
    }

    console.log('✅ 股东信息更新成功:', shareholder.name)

    return NextResponse.json({
      success: true,
      shareholder
    })

  } catch (error) {
    console.error('❌ 更新股东信息失败:', error)
    
    // 处理特定错误
    if ((error as Error).message && (error as Error).message.includes('股权比例')) {
      return NextResponse.json(
        { error: (error as Error).message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: '更新股东信息失败' },
      { status: 500 }
    )
  }
}

// 删除股东
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const shareholderId = searchParams.get('id')

    console.log('📞 删除股东请求:', shareholderId)

    if (!shareholderId) {
      return NextResponse.json(
        { error: '股东ID不能为空' },
        { status: 400 }
      )
    }

    // 删除股东
    const success = await db.deleteShareholder(shareholderId)

    if (!success) {
      return NextResponse.json(
        { error: '删除股东失败' },
        { status: 500 }
      )
    }

    console.log('✅ 股东删除成功')

    return NextResponse.json({
      success: true
    })

  } catch (error) {
    console.error('❌ 删除股东失败:', error)
    return NextResponse.json(
      { error: '删除股东失败' },
      { status: 500 }
    )
  }
}
