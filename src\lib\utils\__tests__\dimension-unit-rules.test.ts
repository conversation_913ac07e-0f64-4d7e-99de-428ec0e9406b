/**
 * 测试用户定义的尺寸单位识别规则
 * 规则：
 * 1. 三位数以上（≥100） → 默认单位是 mm
 * 2. 两位数带小数点 → 单位是 cm  
 * 3. 个位数带小数点 → 单位是 米(m)
 */

import { parseDimensions } from '../dimension-utils'

describe('尺寸单位识别规则测试', () => {
  
  describe('规则1: 三位数以上（≥100） → mm', () => {
    test('500×500 应该识别为 mm', () => {
      const result = parseDimensions('500x500')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(500)
      expect(result?.width).toBe(500)
      // 应该被识别为mm，所以不需要转换
    })

    test('460×460 应该识别为 mm', () => {
      const result = parseDimensions('460*460')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(460)
      expect(result?.width).toBe(460)
    })

    test('750×300 应该识别为 mm', () => {
      const result = parseDimensions('750*300')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(750)
      expect(result?.width).toBe(300)
    })

    test('105×245 应该识别为 mm', () => {
      const result = parseDimensions('105x245')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(245)
      expect(result?.width).toBe(105)
    })
  })

  describe('规则2: 两位数带小数点 → cm', () => {
    test('20.5×30.2 应该识别为 cm 并转换为 mm', () => {
      const result = parseDimensions('20.5x30.2')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(302) // 30.2cm = 302mm
      expect(result?.width).toBe(205) // 20.5cm = 205mm
    })

    test('30.5×10.5 应该识别为 cm 并转换为 mm', () => {
      const result = parseDimensions('30.5x10.5')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(305) // 30.5cm = 305mm
      expect(result?.width).toBe(105) // 10.5cm = 105mm
    })

    test('15.8×25.3 应该识别为 cm 并转换为 mm', () => {
      const result = parseDimensions('15.8x25.3')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(253) // 25.3cm = 253mm
      expect(result?.width).toBe(158) // 15.8cm = 158mm
    })
  })

  describe('规则3: 个位数带小数点 → 米(m)', () => {
    test('1.25×0.25 应该识别为 m 并转换为 mm', () => {
      const result = parseDimensions('1.25x0.25')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(1250) // 1.25m = 1250mm
      expect(result?.width).toBe(250)   // 0.25m = 250mm
    })

    test('1.2×250 应该识别为混合单位：1.2m × 250mm', () => {
      const result = parseDimensions('1.2x250')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(1200) // 1.2m = 1200mm
      expect(result?.width).toBe(250)   // 250mm = 250mm
    })

    test('2.45×250 应该识别为混合单位：2.45m × 250mm', () => {
      const result = parseDimensions('2.45x250')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(2450) // 2.45m = 2450mm
      expect(result?.width).toBe(250)   // 250mm = 250mm
    })

    test('6.63×245 应该识别为混合单位：6.63m × 245mm', () => {
      const result = parseDimensions('6.63x245')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(6630) // 6.63m = 6630mm
      expect(result?.width).toBe(245)   // 245mm = 245mm
    })

    test('1.73×250 应该识别为混合单位：1.73m × 250mm', () => {
      const result = parseDimensions('1.73x250')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(1730) // 1.73m = 1730mm
      expect(result?.width).toBe(250)   // 250mm = 250mm
    })
  })

  describe('边界情况测试', () => {
    test('99×99 应该识别为 cm（小于100的整数）', () => {
      const result = parseDimensions('99x99')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(990) // 99cm = 990mm
      expect(result?.width).toBe(990)  // 99cm = 990mm
    })

    test('100×100 应该识别为 mm（等于100）', () => {
      const result = parseDimensions('100x100')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(100) // 100mm = 100mm
      expect(result?.width).toBe(100)  // 100mm = 100mm
    })

    test('9.9×9.9 应该识别为 m（个位数带小数）', () => {
      const result = parseDimensions('9.9x9.9')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(9900) // 9.9m = 9900mm
      expect(result?.width).toBe(9900)  // 9.9m = 9900mm
    })

    test('10.1×10.1 应该识别为 cm（两位数带小数）', () => {
      const result = parseDimensions('10.1x10.1')
      expect(result).toBeTruthy()
      expect(result?.length).toBe(101) // 10.1cm = 101mm
      expect(result?.width).toBe(101)  // 10.1cm = 101mm
    })
  })

  describe('实际OCR数据测试', () => {
    test('从OCR日志中的实际数据', () => {
      // 500X500=2个
      const result1 = parseDimensions('500X500')
      expect(result1?.length).toBe(500)
      expect(result1?.width).toBe(500)

      // 1.2x250=1个
      const result2 = parseDimensions('1.2x250')
      expect(result2?.length).toBe(1200) // 1.2m = 1200mm
      expect(result2?.width).toBe(250)   // 250mm

      // 460*460=1个
      const result3 = parseDimensions('460*460')
      expect(result3?.length).toBe(460)
      expect(result3?.width).toBe(460)

      // 长750*300=1个
      const result4 = parseDimensions('750*300')
      expect(result4?.length).toBe(750)
      expect(result4?.width).toBe(300)

      // 6.63×245=1个
      const result5 = parseDimensions('6.63×245')
      expect(result5?.length).toBe(6630) // 6.63m = 6630mm
      expect(result5?.width).toBe(245)   // 245mm
    })
  })
})
