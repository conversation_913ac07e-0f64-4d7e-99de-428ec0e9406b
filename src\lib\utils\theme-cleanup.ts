/**
 * 🇨🇳 风口云平台 - 主题数据清理工具
 * 
 * 功能说明：
 * - 清理损坏的主题数据
 * - 修复localStorage中的无效JSON
 * - 重置主题设置到默认状态
 */

/**
 * 清理所有损坏的主题数据
 */
export function cleanupCorruptedThemeData(): void {
  if (typeof window === 'undefined') return
  
  try {
    console.log('🧹 开始清理损坏的主题数据...')
    
    const keysToRemove: string[] = []
    
    // 遍历所有localStorage键
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('theme-storage')) {
        try {
          const value = localStorage.getItem(key)
          if (value) {
            // 尝试解析JSON
            if (value.startsWith('{') || value.startsWith('[')) {
              JSON.parse(value)
              console.log('✅ 主题数据正常:', key)
            } else {
              // 不是有效的JSON格式
              console.warn('⚠️ 发现无效的主题数据:', key, value)
              keysToRemove.push(key)
            }
          }
        } catch (error) {
          console.warn('⚠️ 发现损坏的主题数据:', key, error)
          keysToRemove.push(key)
        }
      }
    }
    
    // 删除损坏的数据
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      console.log('🗑️ 已删除损坏的主题数据:', key)
    })
    
    console.log('✅ 主题数据清理完成，删除了', keysToRemove.length, '个损坏的条目')
    
  } catch (error) {
    console.error('❌ 清理主题数据失败:', error)
  }
}

/**
 * 重置所有主题设置到默认状态
 */
export function resetAllThemeSettings(): void {
  if (typeof window === 'undefined') return
  
  try {
    console.log('🔄 开始重置所有主题设置...')
    
    const themeKeys: string[] = []
    
    // 收集所有主题相关的键
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('theme-storage')) {
        themeKeys.push(key)
      }
    }
    
    // 删除所有主题设置
    themeKeys.forEach(key => {
      localStorage.removeItem(key)
      console.log('🗑️ 已删除主题设置:', key)
    })
    
    // 重置DOM主题
    document.documentElement.classList.remove('dark')
    document.body.classList.remove('admin-dark')
    
    console.log('✅ 所有主题设置已重置，删除了', themeKeys.length, '个条目')
    
  } catch (error) {
    console.error('❌ 重置主题设置失败:', error)
  }
}

/**
 * 验证主题数据完整性
 */
export function validateThemeData(): { valid: number; invalid: number; details: any[] } {
  if (typeof window === 'undefined') return { valid: 0, invalid: 0, details: [] }
  
  const result = {
    valid: 0,
    invalid: 0,
    details: [] as any[]
  }
  
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('theme-storage')) {
        try {
          const value = localStorage.getItem(key)
          if (value) {
            if (value.startsWith('{') || value.startsWith('[')) {
              const parsed = JSON.parse(value)
              result.valid++
              result.details.push({
                key,
                status: 'valid',
                data: parsed
              })
            } else {
              result.invalid++
              result.details.push({
                key,
                status: 'invalid_format',
                data: value
              })
            }
          }
        } catch (error) {
          result.invalid++
          result.details.push({
            key,
            status: 'parse_error',
            error: error.message
          })
        }
      }
    }
  } catch (error) {
    console.error('❌ 验证主题数据失败:', error)
  }
  
  return result
}

/**
 * 自动修复主题数据
 */
export function autoFixThemeData(): void {
  if (typeof window === 'undefined') return
  
  try {
    console.log('🔧 开始自动修复主题数据...')
    
    // 先验证数据
    const validation = validateThemeData()
    console.log('📊 主题数据验证结果:', validation)
    
    if (validation.invalid > 0) {
      // 清理损坏的数据
      cleanupCorruptedThemeData()
      
      // 重新验证
      const newValidation = validateThemeData()
      console.log('📊 修复后验证结果:', newValidation)
    }
    
    console.log('✅ 主题数据自动修复完成')
    
  } catch (error) {
    console.error('❌ 自动修复主题数据失败:', error)
  }
}

/**
 * 在应用启动时自动运行清理
 */
export function initThemeCleanup(): void {
  if (typeof window === 'undefined') return
  
  try {
    // 延迟执行，避免影响应用启动
    setTimeout(() => {
      autoFixThemeData()
    }, 1000)
  } catch (error) {
    console.error('❌ 初始化主题清理失败:', error)
  }
}
