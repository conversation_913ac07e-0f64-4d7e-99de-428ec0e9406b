"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ProductCarousel } from "@/components/ui/carousel"
import { Building2, Users, Search, Phone, Download, Gift, Calendar, MapPin, Package } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { db } from "@/lib/database/client"
import { safeAmount, safeNumber, safeDecimal, safeGreaterThan, safeSubtract } from "@/lib/utils/number-utils"
import { getProductTypeName } from "@/lib/pricing"
import type { Client, Order } from "@/types"

// 订单状态映射
const getPaymentStatusText = (status: string) => {
  switch (status) {
    case 'paid': return '已付款'
    case 'partial': return '部分付款'
    case 'unpaid': return '未付款'
    case 'overdue': return '逾期'
    default: return '未知'
  }
}

const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case 'paid': return 'text-green-600 bg-green-100'
    case 'partial': return 'text-blue-600 bg-blue-100'
    case 'unpaid': return 'text-red-600 bg-red-100'
    case 'overdue': return 'text-red-600 bg-red-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'text-yellow-600 bg-yellow-100'
    case 'processing': return 'text-blue-600 bg-blue-100'
    case 'completed': return 'text-green-600 bg-green-100'
    case 'cancelled': return 'text-red-600 bg-red-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待处理'
    case 'processing': return '处理中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

// 客户数据接口
interface ClientData {
  client: Client
  orders: Order[]
  referredClients: Client[]
  factoryName: string
  statistics: {
    totalAmount: number
    paidAmount: number
    unpaidAmount: number
  }
}

export default function ClientPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [searchType, setSearchType] = useState<'phone' | 'name' | 'company' | 'all'>('all')
  const [searchResults, setSearchResults] = useState<Client[]>([])
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [clientData, setClientData] = useState<ClientData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [showOrderDetail, setShowOrderDetail] = useState(false)
  const [refreshingReward, setRefreshingReward] = useState(false)

  // 搜索客户
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")
    setSearchResults([])
    setSelectedClient(null)
    setClientData(null)

    try {
      console.log('🔍 开始搜索客户:', searchTerm, '类型:', searchType)

      if (!searchTerm.trim()) {
        setError("请输入搜索内容")
        return
      }

      // 公开搜索只支持手机号，强制设置为手机号搜索
      const actualSearchType = 'phone'

      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(searchTerm)) {
        setError("请输入正确的手机号码格式（11位数字，以1开头）")
        return
      }

      // 搜索客户
      const clients = await db.searchClients(searchTerm, actualSearchType)
      console.log('🔍 搜索结果:', clients)

      if (clients.length === 0) {
        setError("未找到匹配的客户信息")
        return
      }

      setSearchResults(clients)

      // 如果只有一个结果，直接选中
      if (clients.length === 1) {
        await selectClient(clients[0])
      }

    } catch (err) {
      console.error('❌ 搜索失败:', err)
      setError(`搜索失败：${err instanceof Error ? err.message : '未知错误'}`)
    } finally {
      setIsLoading(false)
    }
  }

  // 选择客户并获取详细信息
  const selectClient = async (client: Client) => {
    setIsLoading(true)
    setError("")
    setSelectedClient(client)

    try {
      console.log('✅ 选择客户:', client.name)

      // 获取客户的订单记录
      const orders = await db.getOrdersByClientId(client.id)
      console.log('📋 客户订单数量:', orders.length)

      // 获取客户推荐的其他客户
      const referredClients = await db.getReferredClients(client.id)
      console.log('👥 推荐客户数量:', referredClients.length)

      // 获取工厂信息（如果失败不影响其他功能）
      let factoryName = '未知工厂'
      try {
        const factories = await db.getFactories()
        const factory = factories.find(f => f.id === client.factoryId)
        factoryName = factory?.name || '未知工厂'
        console.log('🏭 工厂信息获取成功:', factoryName)
      } catch (factoryError) {
        console.warn('⚠️ 获取工厂信息失败，使用默认值:', factoryError)
        factoryName = '未知工厂'
      }

      // 计算实时统计数据
      const totalAmount = orders.reduce((sum, order) => sum + safeNumber(order.totalAmount), 0)
      const paidAmount = orders.reduce((sum, order) => sum + safeNumber(order.paidAmount || 0), 0)
      const unpaidAmount = totalAmount - paidAmount

      // 🆕 实时计算推荐奖励数据
      let rewardData = {
        availableReward: 0,
        referralReward: 0,
        pendingReward: 0,
        usedReward: safeNumber(client.usedReward || 0)
      }

      // 🔧 修复：无论是否有推荐客户，都尝试获取实时奖励状态
      try {
        console.log('💰 获取客户实时奖励状态:', client.name)

        // 🔧 修复：添加认证头信息
        const headers: HeadersInit = {
          'Content-Type': 'application/json'
        }

        // 尝试从localStorage获取token（如果有的话）
        if (typeof window !== 'undefined') {
          const token = localStorage.getItem('accessToken')
          if (token) {
            headers['Authorization'] = `Bearer ${token}`
          }
        }

        const response = await fetch(`/api/clients/${client.id}/reward-status?factoryId=${client.factoryId}&public=true`, {
          method: 'GET',
          headers
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            const rewardResult = result.data
            rewardData = {
              availableReward: rewardResult.availableReward,
              referralReward: rewardResult.totalReward,
              pendingReward: rewardResult.pendingReward,
              usedReward: rewardResult.usedReward || 0 // 🔧 使用实时计算的已使用奖励
            }
            console.log('💰 实时奖励状态获取成功:', rewardData)
          } else {
            console.warn('⚠️ 获取奖励状态失败:', result.error)
          }
        } else {
          console.warn('⚠️ API请求失败，状态码:', response.status, '响应:', await response.text())
        }
      } catch (rewardError) {
        console.warn('⚠️ 获取实时奖励状态失败，使用默认值:', rewardError)
      }

      // 创建更新后的客户对象，包含实时奖励数据
      const updatedClient = {
        ...client,
        availableReward: rewardData.availableReward,
        referralReward: rewardData.referralReward,
        pendingReward: rewardData.pendingReward,
        usedReward: rewardData.usedReward
      }

      setClientData({
        client: updatedClient, // 使用包含实时奖励数据的客户对象
        orders,
        referredClients,
        factoryName,
        statistics: {
          totalAmount,
          paidAmount,
          unpaidAmount
        }
      })

      console.log('✅ 客户详情获取成功')

    } catch (err) {
      console.error('❌ 获取客户详情失败:', err)
      setError(`获取详情失败：${err instanceof Error ? err.message : '未知错误'}`)
    } finally {
      setIsLoading(false)
    }
  }

  // 查看订单详情
  const handleViewOrderDetail = (order: Order) => {
    console.log('📋 查看订单详情:', order.id)
    setSelectedOrder(order)
    setShowOrderDetail(true)
  }

  // 🆕 刷新奖励数据
  const refreshRewardData = async () => {
    if (!clientData?.client) return

    setRefreshingReward(true)
    try {
      console.log('🔄 刷新客户奖励数据:', clientData.client.name)

      // 🔧 修复：添加认证头信息
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      }

      // 尝试从localStorage获取token（如果有的话）
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('accessToken')
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }
      }

      // 调用同步API
      const response = await fetch(`/api/clients/${clientData.client.id}/sync-reward?factoryId=${clientData.client.factoryId}&public=true`, {
        method: 'POST',
        headers
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          // 更新客户数据
          const updatedClient = {
            ...clientData.client,
            referralReward: result.data.newData.referralReward,
            availableReward: result.data.newData.availableReward,
            usedReward: result.data.newData.usedReward,
            pendingReward: result.data.newData.pendingReward
          }

          setClientData({
            ...clientData,
            client: updatedClient
          })

          console.log('✅ 奖励数据刷新成功')
        } else {
          throw new Error(result.error || '刷新失败')
        }
      } else {
        throw new Error('API请求失败')
      }
    } catch (error) {
      console.error('❌ 刷新奖励数据失败:', error)
      setError('刷新奖励数据失败：' + (error instanceof Error ? error.message : '未知错误'))
    } finally {
      setRefreshingReward(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100">
      {/* Header */}
      <header className="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <div className="bg-green-600 p-2 rounded-lg">
                <Users className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">风口云平台</h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">客户查询系统</p>
              </div>
            </div>
            <Link
              href="/"
              className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 flex items-center space-x-1"
            >
              <Building2 className="h-4 w-4" />
              <span>返回首页</span>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-12 gap-8">
          {/* 左侧产品轮播 - 专业尺寸 */}
          <div className="lg:col-span-3 hidden lg:block">
            <ProductCarousel
              title="产品展示"
              className="sticky top-8"
            />
          </div>

          {/* 中间主要内容 - 扩大到6列 */}
          <div className="lg:col-span-6 space-y-6">
            {/* Search Form - 专业尺寸 */}
            <Card className="mb-8 shadow-lg">
              <CardHeader className="text-center pb-6 pt-8">
                <CardTitle className="text-2xl font-bold text-gray-900">客户信息查询</CardTitle>
                <CardDescription className="text-base text-gray-600 mt-2">
                  请输入您的手机号码查询客户信息
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0 px-8 pb-8">
                <form onSubmit={handleSearch} className="space-y-6">
                  {/* 提示信息 */}
                  <div className="text-center">
                    <div className="inline-flex items-center px-4 py-2 bg-blue-50 text-blue-700 rounded-lg text-sm font-medium">
                      <Phone className="h-4 w-4 mr-2" />
                      <span>仅支持手机号查询</span>
                    </div>
                  </div>

                  {/* 搜索输入框 - 扩大尺寸 */}
                  <div className="flex gap-4 max-w-lg mx-auto">
                    <div className="flex-1">
                      <Input
                        type="tel"
                        placeholder="请输入11位手机号码"
                        value={searchTerm}
                        onChange={(e) => {
                          const value = e.target.value.replace(/\D/g, '') // 只允许数字
                          setSearchTerm(value)
                        }}
                        maxLength={11}
                        pattern="[0-9]*"
                        required
                        className="h-12 text-base px-4 border-2 border-gray-200 focus:border-green-500 focus:ring-green-500 rounded-lg transition-colors"
                      />
                    </div>
                    <Button 
                      type="submit" 
                      disabled={isLoading} 
                      className="h-12 px-6 text-base font-semibold bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-200 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      <Search className="h-4 w-4 mr-2" />
                      {isLoading ? "搜索中..." : "搜索"}
                    </Button>
                  </div>
                </form>

                {error && (
                  <div className="mt-4 text-sm text-red-600 bg-red-50 p-4 rounded-lg border border-red-200 text-center">
                    {error}
                  </div>
                )}


              </CardContent>
            </Card>

            {/* Search Results - 专业展示 */}
            {searchResults.length > 1 && (
              <Card className="mb-8 shadow-lg">
                <CardHeader className="pb-6 pt-6">
                  <CardTitle className="text-xl font-bold text-gray-900">搜索结果</CardTitle>
                  <CardDescription className="text-base text-gray-600">找到 {searchResults.length} 个匹配的客户，请选择一个查看详情</CardDescription>
                </CardHeader>
                <CardContent className="pt-0 px-6 pb-6">
                  <div className="space-y-4">
                    {searchResults.map((client) => (
                      <div
                        key={client.id}
                        className={`p-5 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                          selectedClient?.id === client.id
                            ? 'border-green-500 bg-green-50 dark:bg-green-900/20 shadow-md'
                            : 'border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                        }`}
                        onClick={() => selectClient(client)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <p className="font-semibold text-base text-gray-900 mb-2">{client.name}</p>
                            <p className="text-sm text-gray-600 flex items-center mb-1">
                              <Phone className="h-4 w-4 mr-2" />
                              {client.phone}
                            </p>
                            {client.company && (
                              <p className="text-sm text-gray-600">{client.company}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-600 mb-1">{client.totalOrders || 0} 个订单</p>
                            <p className="text-base font-semibold text-green-600">
                              {safeAmount(client.totalAmount || 0)}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Client Data - 专业展示 */}
            {clientData && (
              <div className="space-y-6">
                {/* Client Info */}
                <Card className="shadow-lg">
                  <CardHeader className="pb-6 pt-6">
                    <CardTitle className="flex items-center space-x-3 text-xl font-bold text-gray-900">
                      <Users className="h-6 w-6 text-green-600" />
                      <span>客户信息</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0 px-6 pb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-600">客户名称</p>
                        <p className="font-semibold text-lg text-gray-900">{clientData.client.name}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-600">联系电话</p>
                        <p className="font-semibold text-lg text-gray-900 flex items-center">
                          <Phone className="h-5 w-5 mr-2 text-green-600" />
                          {clientData.client.phone}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-600">所属工厂</p>
                        <p className="font-semibold text-lg text-gray-900">{clientData.factoryName}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-600">累计消费</p>
                        <p className="font-bold text-xl text-green-600">{safeAmount(clientData.client.totalAmount || 0)}</p>
                      </div>
                      {clientData.client.company && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">公司名称</p>
                          <p className="font-semibold text-lg text-gray-900">{clientData.client.company}</p>
                        </div>
                      )}
                      {clientData.client.address && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">地址</p>
                          <p className="font-semibold text-lg text-gray-900">{clientData.client.address}</p>
                        </div>
                      )}
                    </div>

                    {/* 统计卡片 */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-lg font-bold text-blue-600">{clientData.orders.length}</div>
                          <p className="text-xs text-gray-600">总订单数</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-lg font-bold text-green-600">{clientData.referredClients.length}</div>
                          <p className="text-xs text-gray-600">推荐客户数</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-lg font-bold text-purple-600">{safeAmount(clientData.client.availableReward || 0)}</div>
                          <p className="text-xs text-gray-600">可用奖励</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-lg font-bold text-orange-600">{safeAmount(clientData.statistics.unpaidAmount)}</div>
                          <p className="text-xs text-gray-600">未付金额</p>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>

                {/* Orders - 专业展示 */}
                <Card className="shadow-lg">
                  <CardHeader className="pb-6 pt-6">
                    <CardTitle className="flex items-center space-x-3 text-xl font-bold text-gray-900">
                      <Package className="h-6 w-6 text-blue-600" />
                      <span>订单记录</span>
                    </CardTitle>
                    <CardDescription className="text-base text-gray-600 mt-2">您的历史订单信息</CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0 px-6 pb-6">
                    <div className="space-y-4">
                      {clientData.orders.length === 0 ? (
                        <div className="text-center py-12 text-gray-500">
                          <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p className="text-base">暂无订单记录</p>
                        </div>
                      ) : (
                        clientData.orders.map((order) => (
                          <div key={order.id} className="p-6 border-2 border-gray-200 rounded-lg hover:shadow-md hover:border-blue-300 transition-all duration-200">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                              <div className="space-y-2">
                                <p className="text-sm font-medium text-gray-600">订单号</p>
                                <p className="font-semibold text-base text-gray-900">{order.orderNumber || order.id}</p>
                              </div>
                              <div className="space-y-2">
                                <p className="text-sm font-medium text-gray-600">创建时间</p>
                                <p className="text-base text-gray-900">
                                  {order.createdAt instanceof Date
                                    ? order.createdAt.toLocaleDateString('zh-CN')
                                    : new Date(order.createdAt).toLocaleDateString('zh-CN')
                                  }
                                </p>
                              </div>
                              <div className="space-y-2">
                                <p className="text-sm font-medium text-gray-600">项目地址</p>
                                <p className="text-base text-gray-900">{order.projectAddress || '项目地址未填写'}</p>
                              </div>
                              <div className="space-y-2">
                                <p className="text-sm font-medium text-gray-600">订单状态</p>
                                <div className="flex flex-wrap gap-2">
                                  <span className={`text-sm px-3 py-1 rounded-full font-medium ${getStatusColor(order.status)}`}>
                                    {getStatusText(order.status)}
                                  </span>
                                  {order.paymentStatus && (
                                    <span className={`text-sm px-3 py-1 rounded-full font-medium ${getPaymentStatusColor(order.paymentStatus)}`}>
                                      {getPaymentStatusText(order.paymentStatus)}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center justify-between pt-6 border-t-2 border-gray-100">
                              <div className="grid grid-cols-2 md:grid-cols-3 gap-6 flex-1">
                                <div className="space-y-2">
                                  <p className="text-sm font-medium text-gray-600">订单总额</p>
                                  <p className="font-bold text-xl text-green-600">{safeAmount(order.totalAmount)}</p>
                                </div>
                                {order.paidAmount && safeGreaterThan(order.paidAmount, 0) && (
                                  <div>
                                    <p className="text-sm text-gray-600 mb-1">已付金额</p>
                                    <p className="text-green-600 font-medium">{safeAmount(order.paidAmount)}</p>
                                  </div>
                                )}
                                {order.paidAmount && safeGreaterThan(order.totalAmount - order.paidAmount, 0) && (
                                  <div>
                                    <p className="text-sm text-gray-600 mb-1">待付金额</p>
                                    <p className="text-red-600 font-medium">{safeAmount(order.totalAmount - order.paidAmount)}</p>
                                  </div>
                                )}
                              </div>
                              <Button
                                variant="outline"
                                onClick={() => handleViewOrderDetail(order)}
                                className="ml-6 h-10 px-4 text-sm font-medium border-2 border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
                              >
                                <Download className="h-4 w-4 mr-2" />
                                查看详情
                              </Button>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Referrals - 专业展示 */}
                <Card className="shadow-lg">
                  <CardHeader className="pb-6 pt-6">
                    <CardTitle className="flex items-center space-x-3 text-xl font-bold text-gray-900">
                      <Gift className="h-6 w-6 text-purple-600" />
                      <span>推荐奖励</span>
                    </CardTitle>
                    <CardDescription className="text-base text-gray-600 mt-2">您推荐的客户及获得的奖励</CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0 px-6 pb-6">
                    <div className="space-y-4">
                      {clientData.referredClients.length === 0 ? (
                        <div className="text-center py-12 text-gray-500">
                          <Gift className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p className="text-base">暂无推荐记录</p>
                        </div>
                      ) : (
                        clientData.referredClients.map((referredClient) => (
                          <div key={referredClient.id} className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                              <div>
                                <p className="text-sm text-gray-600 mb-1">客户姓名</p>
                                <p className="font-medium">{referredClient.name}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-600 mb-1">联系电话</p>
                                <p className="text-sm">{referredClient.phone}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-600 mb-1">注册时间</p>
                                <p className="text-sm">
                                  {referredClient.createdAt instanceof Date
                                    ? referredClient.createdAt.toLocaleDateString('zh-CN')
                                    : new Date(referredClient.createdAt).toLocaleDateString('zh-CN')
                                  }
                                </p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-600 mb-1">订单数量</p>
                                <p className="font-medium">{referredClient.totalOrders || 0} 个</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-600 mb-1">消费金额</p>
                                <p className="text-blue-600 font-medium">{safeAmount(referredClient.totalAmount || 0)}</p>
                              </div>
                            </div>
                            <div className="mt-4 pt-4 border-t border-gray-100">
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">推荐奖励</span>
                                <span className="text-green-600 font-medium text-lg">{safeAmount(referredClient.referralReward || 0)}</span>
                              </div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>

                    {/* 奖励统计 - 🔧 修复：显示所有奖励信息，不仅仅是可用奖励 */}
                    {(safeGreaterThan(clientData.client.availableReward || 0, 0) ||
                      safeGreaterThan(clientData.client.referralReward || 0, 0) ||
                      safeGreaterThan(clientData.client.usedReward || 0, 0)) && (
                      <div className="mt-6 p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                        <div className="flex justify-between items-center mb-4">
                          <h4 className="text-lg font-semibold text-gray-800">奖励统计</h4>
                          <Button
                            onClick={refreshRewardData}
                            disabled={refreshingReward}
                            variant="outline"
                            size="sm"
                            className="text-xs"
                          >
                            {refreshingReward ? '刷新中...' : '刷新数据'}
                          </Button>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                          <div>
                            <p className="text-sm text-gray-600 mb-1">可用奖励</p>
                            <p className="text-2xl font-bold text-green-600">
                              {safeAmount(clientData.client.availableReward || 0)}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600 mb-1">总奖励</p>
                            <p className="text-2xl font-bold text-blue-600">
                              {safeAmount(clientData.client.referralReward || 0)}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600 mb-1">待结算</p>
                            <p className="text-2xl font-bold text-yellow-600">
                              {safeAmount((clientData.client.referralReward || 0) - (clientData.client.availableReward || 0) - (clientData.client.usedReward || 0))}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600 mb-1">已使用</p>
                            <p className="text-2xl font-bold text-gray-600">
                              {safeAmount(clientData.client.usedReward || 0)}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
          </div>

          {/* 右侧工艺展示 - 专业尺寸 */}
          <div className="lg:col-span-3 hidden lg:block">
            <ProductCarousel
              title="工艺展示"
              className="sticky top-8"
            />
          </div>
        </div>

        {/* 移动端产品轮播 - 增强展示 */}
        <div className="lg:hidden mt-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ProductCarousel title="产品展示" />
            <ProductCarousel title="工艺展示" />
          </div>
        </div>
      </div>

      {/* 订单详情对话框 */}
      <Dialog open={showOrderDetail} onOpenChange={setShowOrderDetail}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <span>订单详情</span>
            </DialogTitle>
            <DialogDescription>
              查看订单的详细信息和产品明细
            </DialogDescription>
          </DialogHeader>

          {selectedOrder && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">订单号</p>
                  <p className="font-medium">{selectedOrder.orderNumber || selectedOrder.id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">创建时间</p>
                  <p className="text-sm">
                    {selectedOrder.createdAt instanceof Date
                      ? selectedOrder.createdAt.toLocaleDateString('zh-CN')
                      : new Date(selectedOrder.createdAt).toLocaleDateString('zh-CN')
                    }
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">项目地址</p>
                  <p className="text-sm">{selectedOrder.projectAddress || '未填写'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">订单状态</p>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getStatusColor(selectedOrder.status)}`}>
                    {getStatusText(selectedOrder.status)}
                  </span>
                </div>
              </div>

              {/* 金额信息 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-3">金额信息</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">订单总额</p>
                    <p className="text-lg font-bold text-green-600">{safeAmount(selectedOrder.totalAmount)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">已付金额</p>
                    <p className="text-lg font-bold text-blue-600">{safeAmount(selectedOrder.paidAmount || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">待付金额</p>
                    <p className="text-lg font-bold text-red-600">
                      {safeAmount(selectedOrder.totalAmount - (selectedOrder.paidAmount || 0))}
                    </p>
                  </div>
                </div>
                {selectedOrder.paymentStatus && (
                  <div className="mt-3">
                    <p className="text-sm text-gray-600">付款状态</p>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getPaymentStatusColor(selectedOrder.paymentStatus)}`}>
                      {getPaymentStatusText(selectedOrder.paymentStatus)}
                    </span>
                  </div>
                )}
              </div>

              {/* 产品明细 */}
              {selectedOrder.items && selectedOrder.items.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">产品明细</h4>
                  <div className="space-y-2">
                    {selectedOrder.items.map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <div>
                          <p className="font-medium">{getProductTypeName(item.productType) || item.productName || item.productType}</p>
                          <p className="text-sm text-gray-600">
                            尺寸: {item.length || item.dimensions?.length || 0}mm × {item.width || item.dimensions?.width || 0}mm
                            {item.color && ` | 颜色: ${item.color}`}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{safeAmount(item.price || item.totalPrice)}</p>
                          <p className="text-sm text-gray-600">数量: {item.quantity || 1}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 备注信息 */}
              {selectedOrder.notes && (
                <div>
                  <h4 className="font-medium mb-2">备注信息</h4>
                  <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">{selectedOrder.notes}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
