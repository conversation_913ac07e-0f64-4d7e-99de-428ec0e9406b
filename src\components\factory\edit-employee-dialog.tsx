"use client"

import { useState, useEffect } from "react"
import { Button  } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { FactoryUser, UserRole } from "@/types"

interface EditEmployeeDialogProps {
  isOpen: boolean
  onClose: () => void
  onSave: (employee: FactoryUser) => void
  employee: FactoryUser | null
}

export function EditEmployeeDialog({ isOpen, onClose, onSave, employee }: EditEmployeeDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    username: "",
    email: "",
    phone: "",
    role: "employee",
    permissions: [] as string[]
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const roleOptions = [
    { value: "manager", label: "管理员", description: "可以管理所有功能" },
    { value: "employee", label: "员工", description: "可以录入和查看订单" }
  ]

  const permissionOptions = [
    // 订单管理
    { id: "orders:create", label: "创建订单", category: "订单管理" },
    { id: "orders:read", label: "查看订单", category: "订单管理" },
    { id: "orders:update", label: "修改订单", category: "订单管理" },
    { id: "orders:delete", label: "删除订单", category: "订单管理" },
    
    // 客户管理
    { id: "clients:create", label: "添加客户", category: "客户管理" },
    { id: "clients:read", label: "查看客户", category: "客户管理" },
    { id: "clients:update", label: "修改客户", category: "客户管理" },
    { id: "clients:delete", label: "删除客户", category: "客户管理" },
    
    // 数据分析
    { id: "reports:read", label: "查看报表", category: "数据分析" },
    { id: "analytics:read", label: "数据分析", category: "数据分析" },
    
    // 系统设置
    { id: "employees:manage", label: "员工管理", category: "系统设置" },
    { id: "settings:manage", label: "系统设置", category: "系统设置" }
  ]

  // 当员工数据变化时更新表单
  useEffect(() => {
    if (employee) {
      setFormData({
        name: employee.name || "",
        username: employee.username || "",
        email: employee.email || "",
        phone: employee.phone || "",
        role: employee.role || "employee",
        permissions: employee.permissions || []
      })
    }
  }, [employee])

  const handleRoleChange = (role: string) => {
    setFormData(prev => ({ ...prev, role }))
    
    // 根据角色自动设置权限
    let defaultPermissions: string[] = []
    if (role === "manager") {
      defaultPermissions = permissionOptions.map(p => p.id)
    } else if (role === "employee") {
      defaultPermissions = ["orders:create", "orders:read", "clients:read"]
    }
    
    setFormData(prev => ({ ...prev, permissions: defaultPermissions }))
  }

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked 
        ? [...prev.permissions, permissionId]
        : prev.permissions.filter(p => p !== permissionId)
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "请输入员工姓名"
    }

    if (!formData.username.trim()) {
      newErrors.username = "请输入用户名"
    } else if (formData.username.length < 3) {
      newErrors.username = "用户名至少3个字符"
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "请输入有效的邮箱地址"
    }

    if (formData.phone && !/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = "请输入有效的手机号码"
    }

    if (formData.permissions.length === 0) {
      newErrors.permissions = "请至少选择一个权限"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (!validateForm() || !employee) return

    const updatedEmployee = {
      ...employee,
      ...formData,
      updatedAt: new Date()
    }

    onSave({
      ...updatedEmployee,
      role: updatedEmployee.role as UserRole
    })
    onClose()
    setErrors({})
  }

  if (!isOpen || !employee) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">编辑员工</h2>
          <Button variant="ghost" onClick={onClose}>✕</Button>
        </div>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">员工姓名 *</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="请输入员工姓名"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">用户名 *</label>
              <Input
                value={formData.username}
                onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                placeholder="请输入用户名"
                className={errors.username ? "border-red-500" : ""}
              />
              {errors.username && <p className="text-red-500 text-xs mt-1">{errors.username}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">邮箱</label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="请输入邮箱地址"
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">手机号</label>
              <Input
                type="tel"
                value={formData.phone}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '') // 只允许数字
                  setFormData(prev => ({ ...prev, phone: value }))
                }}
                placeholder="请输入11位手机号码"
                maxLength={11}
                pattern="[0-9]*"
                className={errors.phone ? "border-red-500" : ""}
              />
              {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
            </div>
          </div>

          {/* 角色选择 */}
          <div>
            <label className="block text-sm font-medium mb-2">员工角色 *</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {roleOptions.map(option => (
                <div
                  key={option.value}
                  className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                    formData.role === option.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleRoleChange(option.value)}
                >
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      checked={formData.role === option.value}
                      onChange={() => handleRoleChange(option.value)}
                      className="text-blue-600"
                    />
                    <div>
                      <p className="font-medium">{option.label}</p>
                      <p className="text-sm text-gray-600">{option.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 权限详细设置 */}
          <div>
            <label className="block text-sm font-medium mb-2">具体权限 *</label>
            <div className="border rounded-lg p-4 max-h-48 overflow-y-auto">
              {["订单管理", "客户管理", "数据分析", "系统设置"].map(category => (
                <div key={category} className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">{category}</h4>
                  <div className="space-y-2">
                    {permissionOptions
                      .filter(p => p.category === category)
                      .map(permission => (
                        <label key={permission.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.permissions.includes(permission.id)}
                            onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}
                            className="text-blue-600"
                          />
                          <span className="text-sm">{permission.label}</span>
                        </label>
                      ))}
                  </div>
                </div>
              ))}
            </div>
            {errors.permissions && <p className="text-red-500 text-xs mt-1">{errors.permissions}</p>}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={handleSave}>
            保存修改
          </Button>
        </div>
      </div>
    </div>
  )
}
