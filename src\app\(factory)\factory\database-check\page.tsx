'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Database, Search, AlertCircle } from 'lucide-react'
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { safeAmountFormat } from '@/lib/utils/number-utils'

interface DatabaseData {
  clients: Array<{
    id: string
    name: string
    phone: string
    referrerId?: string
    referrerName?: string
    referralReward: number
    availableReward: number
    pendingReward: number
    totalOrders: number
    totalAmount: number
  }>
  orders: Array<{
    id: string
    orderNumber?: string
    clientId: string
    clientName?: string
    totalAmount: number
    items: unknown[]
    createdAt: Date
  }>
  referralRelationships: Array<{
    referrerId: string
    referrerName: string
    referredClients: Array<{
      id: string
      name: string
      phone: string
      totalOrders: number
      totalAmount: number
    }>
  }>
}

export default function DatabaseCheckPage() {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<DatabaseData | null>(null)
  const [error, setError] = useState<string | null>(null)

  const checkDatabase = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        setError('无法获取工厂ID')
        return
      }

      console.log('🔍 开始检查数据库数据...')

      // 获取所有客户
      const clients = await db.getClientsByFactoryId(factoryId)
      console.log(`👥 客户数量: ${clients.length}`)

      // 获取所有订单
      const orders = await db.getOrdersByFactoryId(factoryId)
      console.log(`📦 订单数量: ${orders.length}`)

      // 处理客户数据
      const processedClients = clients.map((client: unknown) => ({
        id: client.id,
        name: client.name,
        phone: client.phone,
        referrerId: client.referrerId || undefined,
        referrerName: client.referrerName || undefined,
        referralReward: typeof client.referralReward === 'number' ? client.referralReward : 
                       typeof client.referralReward === 'object' && client.referralReward ? 
                       parseFloat(client.referralReward.toString()) : 0,
        availableReward: typeof client.availableReward === 'number' ? client.availableReward : 
                        typeof client.availableReward === 'object' && client.availableReward ? 
                        parseFloat(client.availableReward.toString()) : 0,
        pendingReward: typeof client.pendingReward === 'number' ? client.pendingReward : 
                      typeof client.pendingReward === 'object' && client.pendingReward ? 
                      parseFloat(client.pendingReward.toString()) : 0,
        totalOrders: client.totalOrders || 0,
        totalAmount: typeof client.totalAmount === 'number' ? client.totalAmount : 
                    typeof client.totalAmount === 'object' && client.totalAmount ? 
                    parseFloat(client.totalAmount.toString()) : 0
      }))

      // 处理订单数据
      const processedOrders = orders.map((order: unknown) => {
        const client = clients.find((c: unknown) => c.id === order.clientId)
        return {
          id: order.id,
          orderNumber: order.orderNumber,
          clientId: order.clientId,
          clientName: client?.name || '未知客户',
          totalAmount: typeof order.totalAmount === 'number' ? order.totalAmount : 
                      typeof order.totalAmount === 'object' && order.totalAmount ? 
                      parseFloat(order.totalAmount.toString()) : 0,
          items: order.items || [],
          createdAt: order.createdAt
        }
      })

      // 分析推荐关系
      const referralRelationships: unknown[] = []
      const referrers = processedClients.filter((client: unknown) =>
        processedClients.some((c: unknown) => c.referrerId === client.id)
      )

      for (const referrer of referrers) {
        const referredClients = processedClients.filter((c: unknown) => c.referrerId === referrer.id)
        const referredClientsWithOrders = referredClients.map((client: unknown) => {
          const clientOrders = processedOrders.filter((order: unknown) => order.clientId === client.id)
          const totalAmount = clientOrders.reduce((sum: number, order: unknown) => sum + order.totalAmount, 0)
          
          return {
            id: client.id,
            name: client.name,
            phone: client.phone,
            totalOrders: clientOrders.length,
            totalAmount
          }
        })

        referralRelationships.push({
          referrerId: referrer.id,
          referrerName: referrer.name,
          referredClients: referredClientsWithOrders
        })
      }

      setData({
        clients: processedClients,
        orders: processedOrders,
        referralRelationships
      })

      console.log('✅ 数据库检查完成')
      console.log('📊 数据统计:', {
        客户总数: processedClients.length,
        订单总数: processedOrders.length,
        推荐人数量: referralRelationships.length,
        有推荐人的客户: processedClients.filter((c: unknown) => c.referrerId).length,
        有奖励的客户: processedClients.filter((c: unknown) => c.referralReward > 0).length
      })

    } catch (error) {
      console.error('❌ 数据库检查失败:', error)
      setError(error instanceof Error ? error.message : '检查失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">数据库数据检查</h1>
            <p className="text-gray-600">检查客户、订单和推荐关系的原始数据</p>
          </div>
          <Button onClick={checkDatabase} disabled={loading}>
            {loading ? (
              <>
                <Search className="h-4 w-4 mr-2 animate-spin" />
                检查中...
              </>
            ) : (
              <>
                <Database className="h-4 w-4 mr-2" />
                开始检查
              </>
            )}
          </Button>
        </div>

        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2 text-red-600">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">检查失败</span>
              </div>
              <p className="text-red-600 mt-2">{error}</p>
            </CardContent>
          </Card>
        )}

        {data && (
          <div className="space-y-6">
            {/* 数据统计 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">客户总数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data.clients.length}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">订单总数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data.orders.length}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">推荐人数量</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data.referralRelationships.length}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">有奖励客户</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {data.clients.filter((c: unknown) => c.referralReward > 0).length}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 推荐关系详情 */}
            <Card>
              <CardHeader>
                <CardTitle>推荐关系详情</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.referralRelationships.map((relationship) => {
                    const referrer = data.clients.find((c: unknown) => c.id === relationship.referrerId)
                    return (
                      <div key={relationship.referrerId} className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-3">
                          <h3 className="font-semibold text-lg">{relationship.referrerName}</h3>
                          <div className="text-right">
                            <div className="text-sm text-gray-600">数据库中的奖励数据:</div>
                            <div className="text-green-600">总奖励: ¥{safeAmountFormat(referrer?.referralReward || 0)}</div>
                            <div className="text-blue-600">可用奖励: ¥{safeAmountFormat(referrer?.availableReward || 0)}</div>
                            <div className="text-orange-600">待结算: ¥{safeAmountFormat(referrer?.pendingReward || 0)}</div>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <h4 className="font-medium">推荐的客户:</h4>
                          {relationship.referredClients.map((client: unknown) => (
                            <div key={client.id} className="bg-gray-50 p-3 rounded flex justify-between">
                              <div>
                                <div className="font-medium">{client.name}</div>
                                <div className="text-sm text-gray-600">{client.phone}</div>
                              </div>
                              <div className="text-right">
                                <div className="text-sm">订单: {client.totalOrders}个</div>
                                <div className="text-sm">金额: ¥{safeAmountFormat(client.totalAmount)}</div>
                                <div className="text-xs text-gray-500">
                                  按2%计算应得: ¥{safeAmountFormat(client.totalAmount * 0.02)}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* 所有客户的奖励状态 */}
            <Card>
              <CardHeader>
                <CardTitle>所有客户奖励状态</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {data.clients.map((client) => (
                    <div key={client.id} className="flex justify-between items-center p-2 border-b">
                      <div>
                        <span className="font-medium">{client.name}</span>
                        {client.referrerId && (
                          <span className="text-sm text-gray-600 ml-2">
                            (推荐人: {client.referrerName || '未知'})
                          </span>
                        )}
                      </div>
                      <div className="text-right text-sm">
                        <div>订单: {client.totalOrders}个</div>
                        <div>消费: ¥{safeAmountFormat(client.totalAmount)}</div>
                        <div className="text-green-600">奖励: ¥{safeAmountFormat(client.referralReward)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {!data && !loading && (
          <Card>
            <CardContent className="p-8 text-center">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">开始检查</h3>
              <p className="text-gray-600">点击"开始检查"按钮来查看数据库中的原始数据
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
