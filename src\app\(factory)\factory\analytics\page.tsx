"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from "@/lib/database"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import { safeNumber, safeWanYuan, safeLocaleString, safeDivide, safeAverage, safeSum } from "@/lib/utils/number-utils"
import { getProductTypeName } from "@/lib/pricing"
import * as XLSX from 'xlsx'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Package,
  Download,
  Calendar,
  Target,
  Award,
  AlertCircle
} from "lucide-react"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts'

// 颜色配置
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

interface AnalyticsData {
  // 基础统计
  totalOrders: number
  totalRevenue: number
  totalClients: number
  averageOrderValue: number

  // 趋势数据
  monthlyTrends: Array<{
    month: string
    orders: number
    revenue: number
    clients: number
  }>

  // 季度报表数据
  quarterlyReports: Array<{
    quarter: string
    period: string
    orders: number
    revenue: number
    clients: number
    avgOrderValue: number
    growth: number
  }>

  // 年度报表数据
  yearlyReports: Array<{
    year: string
    orders: number
    revenue: number
    clients: number
    avgOrderValue: number
    growth: number
    quarters: Array<{
      quarter: string
      orders: number
      revenue: number
    }>
  }>

  // 月度详细报表
  monthlyReports: Array<{
    month: string
    period: string
    orders: number
    revenue: number
    clients: number
    avgOrderValue: number
    growth: number
    topProducts: Array<{
      name: string
      quantity: number
      revenue: number
    }>
    topClients: Array<{
      name: string
      orders: number
      revenue: number
    }>
  }>

  // 产品分析
  productStats: Array<{
    name: string
    quantity: number
    revenue: number
    orders: number
  }>

  // 客户分析
  clientStats: Array<{
    name: string
    orders: number
    revenue: number
    lastOrder: string
  }>

  // 付款状态分析
  paymentStats: Array<{
    status: string
    count: number
    amount: number
  }>

  // 员工业绩
  employeeStats: Array<{
    name: string
    orders: number
    revenue: number
  }>
}

export default function AnalyticsPage() {
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [timeRange, setTimeRange] = useState("6months")
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    loadAnalyticsData()
  }, [timeRange])

  const loadAnalyticsData = async () => {
    try {
      setLoading(true)
      console.log('🔄 开始加载数据分析...')

      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        console.warn('⚠️ 无法获取工厂ID')
        return
      }

      // 获取订单数据
      const orders = await db.getOrdersByFactoryId(factoryId)
      const clients = await db.getClientsByFactoryId(factoryId)
      
      console.log('📊 数据加载完成:', { orders: orders.length, clients: clients.length })

      // 处理数据
      const analyticsData = processAnalyticsData(orders, clients)
      setData(analyticsData)

    } catch (error) {
      console.error('❌ 加载数据分析失败:', error)
      // 使用模拟数据
      setData(generateMockData())
    } finally {
      setLoading(false)
    }
  }

  const processAnalyticsData = (orders: any[], clients: any[]): AnalyticsData => {
    // 基础统计
    const totalOrders = orders.length
    const totalRevenue = safeSum(orders.map((order: any) => order.totalAmount))
    const totalClients = clients.length
    const averageOrderValue = safeDivide(totalRevenue, totalOrders)

    // 月度趋势（最近6个月）
    const monthlyTrends = generateMonthlyTrends(orders)

    // 月度详细报表（最近12个月）
    const monthlyReports = generateMonthlyReports(orders, clients)

    // 季度报表（最近8个季度）
    const quarterlyReports = generateQuarterlyReports(orders, clients)

    // 年度报表（最近3年）
    const yearlyReports = generateYearlyReports(orders, clients)

    // 产品统计
    const productStats = generateProductStats(orders)

    // 客户统计（前10名）
    const clientStats = generateClientStats(orders, clients).slice(0, 10)

    // 付款状态统计
    const paymentStats = generatePaymentStats(orders)

    // 员工业绩统计
    const employeeStats = generateEmployeeStats(orders)

    return {
      totalOrders,
      totalRevenue,
      totalClients,
      averageOrderValue,
      monthlyTrends,
      monthlyReports,
      quarterlyReports,
      yearlyReports,
      productStats,
      clientStats,
      paymentStats,
      employeeStats
    }
  }

  // 生成月度趋势数据
  const generateMonthlyTrends = (orders: any[]) => {
    const months: Array<{
      month: string;
      orders: number;
      revenue: number;
      clients: number;
    }> = []
    const now = new Date()

    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`

      const monthOrders = orders.filter((order: any) => {
        const orderDate = new Date(order.createdAt)
        return orderDate.getFullYear() === date.getFullYear() &&
               orderDate.getMonth() === date.getMonth()
      })

      months.push({
        month: `${date.getMonth() + 1}月`,
        orders: monthOrders.length,
        revenue: safeSum(monthOrders.map((order: any) => order.totalAmount)),
        clients: new Set(monthOrders.map((order: any) => order.clientId)).size
      })
    }

    return months
  }

  // 生成月度详细报表
  const generateMonthlyReports = (orders: any[], clients: any[]) => {
    const reports: any[] = []
    const now = new Date()

    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`

      const monthOrders = orders.filter((order: any) => {
        const orderDate = new Date(order.createdAt)
        return orderDate.getFullYear() === date.getFullYear() &&
               orderDate.getMonth() === date.getMonth()
      })

      const monthRevenue = safeSum(monthOrders.map((order: any) => order.totalAmount))
      const monthClients = new Set(monthOrders.map((order: any) => order.clientId)).size
      const avgOrderValue = safeDivide(monthRevenue, monthOrders.length)

      // 计算环比增长
      const prevDate = new Date(date.getFullYear(), date.getMonth() - 1, 1)
      const prevMonthOrders = orders.filter((order: any) => {
        const orderDate = new Date(order.createdAt)
        return orderDate.getFullYear() === prevDate.getFullYear() &&
               orderDate.getMonth() === prevDate.getMonth()
      })
      const prevRevenue = safeSum(prevMonthOrders.map((order: any) => order.totalAmount))
      const growth = safeDivide((monthRevenue - prevRevenue), prevRevenue) * 100

      // 月度产品排行
      const productStats = generateProductStats(monthOrders)
      const topProducts = productStats.slice(0, 5)

      // 月度客户排行
      const clientStats = generateClientStats(monthOrders, clients)
      const topClients = clientStats.slice(0, 5)

      reports.push({
        month: `${date.getMonth() + 1}月`,
        period: monthStr,
        orders: monthOrders.length,
        revenue: monthRevenue,
        clients: monthClients,
        avgOrderValue,
        growth,
        topProducts,
        topClients
      })
    }

    return reports
  }

  // 生成季度报表
  const generateQuarterlyReports = (orders: any[], clients: any[]) => {
    const reports: any[] = []
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentQuarter = Math.floor(now.getMonth() / 3) + 1

    for (let i = 7; i >= 0; i--) {
      // 计算季度
      const quarterOffset = currentQuarter - 1 - i
      const year = currentYear + Math.floor(quarterOffset / 4)
      const quarter = ((quarterOffset % 4) + 4) % 4 + 1

      // 季度开始和结束月份
      const startMonth = (quarter - 1) * 3
      const endMonth = startMonth + 2

      const quarterOrders = orders.filter((order: any) => {
        const orderDate = new Date(order.createdAt)
        const orderYear = orderDate.getFullYear()
        const orderMonth = orderDate.getMonth()
        return orderYear === year && orderMonth >= startMonth && orderMonth <= endMonth
      })

      const quarterRevenue = safeSum(quarterOrders.map((order: any) => order.totalAmount))
      const quarterClients = new Set(quarterOrders.map((order: any) => order.clientId)).size
      const avgOrderValue = safeDivide(quarterRevenue, quarterOrders.length)

      // 计算同比增长（与去年同季度比较）
      const prevYearOrders = orders.filter((order: any) => {
        const orderDate = new Date(order.createdAt)
        const orderYear = orderDate.getFullYear()
        const orderMonth = orderDate.getMonth()
        return orderYear === year - 1 && orderMonth >= startMonth && orderMonth <= endMonth
      })
      const prevYearRevenue = safeSum(prevYearOrders.map((order: any) => order.totalAmount))
      const growth = safeDivide((quarterRevenue - prevYearRevenue), prevYearRevenue) * 100

      reports.push({
        quarter: `Q${quarter}`,
        period: `${year}年Q${quarter}`,
        orders: quarterOrders.length,
        revenue: quarterRevenue,
        clients: quarterClients,
        avgOrderValue,
        growth
      })
    }

    return reports.reverse()
  }

  // 生成年度报表
  const generateYearlyReports = (orders: any[], clients: any[]) => {
    const reports: any[] = []
    const now = new Date()
    const currentYear = now.getFullYear()

    for (let i = 2; i >= 0; i--) {
      const year = currentYear - i

      const yearOrders = orders.filter((order: any) => {
        const orderDate = new Date(order.createdAt)
        return orderDate.getFullYear() === year
      })

      const yearRevenue = safeSum(yearOrders.map((order: any) => order.totalAmount))
      const yearClients = new Set(yearOrders.map((order: any) => order.clientId)).size
      const avgOrderValue = safeDivide(yearRevenue, yearOrders.length)

      // 计算同比增长
      const prevYearOrders = orders.filter((order: any) => {
        const orderDate = new Date(order.createdAt)
        return orderDate.getFullYear() === year - 1
      })
      const prevYearRevenue = safeSum(prevYearOrders.map((order: any) => order.totalAmount))
      const growth = safeDivide((yearRevenue - prevYearRevenue), prevYearRevenue) * 100

      // 生成季度数据
      const quarters: Array<{
        quarter: string;
        orders: number;
        revenue: number;
      }> = []
      for (let q = 1; q <= 4; q++) {
        const startMonth = (q - 1) * 3
        const endMonth = startMonth + 2

        const quarterOrders = yearOrders.filter((order: any) => {
          const orderDate = new Date(order.createdAt)
          const orderMonth = orderDate.getMonth()
          return orderMonth >= startMonth && orderMonth <= endMonth
        })

        quarters.push({
          quarter: `Q${q}`,
          orders: quarterOrders.length,
          revenue: safeSum(quarterOrders.map((order: any) => order.totalAmount))
        })
      }

      reports.push({
        year: `${year}年`,
        orders: yearOrders.length,
        revenue: yearRevenue,
        clients: yearClients,
        avgOrderValue,
        growth,
        quarters
      })
    }

    return reports
  }

  // 生成产品统计数据
  const generateProductStats = (orders: any[]) => {
    const productMap = new Map()

    orders.forEach((order: any) => {
      order.items?.forEach((item: any) => {
        // 优先使用统一的产品类型名称转换函数，确保显示中文名称
        let productName = '未知产品'

        if (item.productType) {
          // 使用统一的产品类型名称转换函数
          productName = getProductTypeName(item.productType)
        } else if (item.productName) {
          // 如果没有productType，检查productName是否为英文代码
          const chineseName = getProductTypeName(item.productName)
          productName = chineseName !== item.productName ? chineseName : item.productName
        }

        if (!productMap.has(productName)) {
          productMap.set(productName, { name: productName, quantity: 0, revenue: 0, orders: 0 })
        }
        const product = productMap.get(productName)
        product.quantity += safeNumber(item.quantity)
        product.revenue += safeNumber(item.totalPrice)
        product.orders += 1
      })
    })

    return Array.from(productMap.values()).sort((a, b) => b.revenue - a.revenue).slice(0, 8)
  }

  // 生成客户统计数据
  const generateClientStats = (orders: any[], clients: any[]) => {
    const clientMap = new Map()

    orders.forEach((order: any) => {
      const clientId = order.clientId
      const clientName = order.clientName || clients.find((c: any) => c.id === clientId)?.name || '未知客户'

      if (!clientMap.has(clientId)) {
        clientMap.set(clientId, {
          name: clientName,
          orders: 0,
          revenue: 0,
          lastOrder: order.createdAt
        })
      }

      const client = clientMap.get(clientId)
      client.orders += 1
      client.revenue += safeNumber(order.totalAmount)
      if (new Date(order.createdAt) > new Date(client.lastOrder)) {
        client.lastOrder = order.createdAt
      }
    })

    return Array.from(clientMap.values())
      .sort((a, b) => b.revenue - a.revenue)
      .map(client => ({
        ...client,
        lastOrder: new Date(client.lastOrder).toLocaleDateString('zh-CN')
      }))
  }

  // 生成付款状态统计
  const generatePaymentStats = (orders: any[]) => {
    const statusMap = {
      'paid': { status: '已付清', count: 0, amount: 0 },
      'partial': { status: '部分付款', count: 0, amount: 0 },
      'unpaid': { status: '未付款', count: 0, amount: 0 },
      'overdue': { status: '逾期', count: 0, amount: 0 }
    }

    type PaymentStatusKey = keyof typeof statusMap

    orders.forEach((order: any) => {
      const rawStatus = order.paymentStatus || 'unpaid'
      // 类型安全的状态检查和转换
      const status: PaymentStatusKey = ['paid', 'partial', 'unpaid', 'overdue'].includes(rawStatus)
        ? rawStatus as PaymentStatusKey
        : 'unpaid'

      statusMap[status].count += 1
      statusMap[status].amount += safeNumber(order.totalAmount)
    })

    return Object.values(statusMap)
  }

  // 生成员工业绩统计
  const generateEmployeeStats = (orders: any[]) => {
    const employeeMap = new Map()

    orders.forEach((order: any) => {
      const employeeName = order.createdByName || order.createdBy || '未知员工'

      if (!employeeMap.has(employeeName)) {
        employeeMap.set(employeeName, { name: employeeName, orders: 0, revenue: 0 })
      }

      const employee = employeeMap.get(employeeName)
      employee.orders += 1
      employee.revenue += safeNumber(order.totalAmount)
    })

    return Array.from(employeeMap.values()).sort((a, b) => b.revenue - a.revenue)
  }

  // 生成模拟数据
  const generateMockData = (): AnalyticsData => {
    return {
      totalOrders: 156,
      totalRevenue: 2850000,
      totalClients: 89,
      averageOrderValue: 18269,
      monthlyTrends: [
        { month: '8月', orders: 18, revenue: 285000, clients: 12 },
        { month: '9月', orders: 22, revenue: 356000, clients: 15 },
        { month: '10月', orders: 28, revenue: 445000, clients: 18 },
        { month: '11月', orders: 35, revenue: 578000, clients: 22 },
        { month: '12月', orders: 31, revenue: 498000, clients: 19 },
        { month: '1月', orders: 22, revenue: 388000, clients: 16 }
      ],
      monthlyReports: [
        { month: '1月', period: '2024-01', orders: 22, revenue: 388000, clients: 16, avgOrderValue: 17636, growth: -22.1, topProducts: [], topClients: [] },
        { month: '2月', period: '2024-02', orders: 18, revenue: 295000, clients: 14, avgOrderValue: 16389, growth: -24.0, topProducts: [], topClients: [] },
        { month: '3月', period: '2024-03', orders: 25, revenue: 445000, clients: 19, avgOrderValue: 17800, growth: 50.8, topProducts: [], topClients: [] },
        { month: '4月', period: '2024-04', orders: 30, revenue: 520000, clients: 22, avgOrderValue: 17333, growth: 16.9, topProducts: [], topClients: [] },
        { month: '5月', period: '2024-05', orders: 28, revenue: 485000, clients: 20, avgOrderValue: 17321, growth: -6.7, topProducts: [], topClients: [] },
        { month: '6月', period: '2024-06', orders: 32, revenue: 578000, clients: 24, avgOrderValue: 18063, growth: 19.2, topProducts: [], topClients: [] }
      ],
      quarterlyReports: [
        { quarter: 'Q1', period: '2024年Q1', orders: 65, revenue: 1128000, clients: 35, avgOrderValue: 17354, growth: 12.5 },
        { quarter: 'Q2', period: '2024年Q2', orders: 90, revenue: 1583000, clients: 48, avgOrderValue: 17589, growth: 15.8 },
        { quarter: 'Q3', period: '2024年Q3', orders: 78, revenue: 1389000, clients: 42, avgOrderValue: 17808, growth: 8.3 },
        { quarter: 'Q4', period: '2024年Q4', orders: 85, revenue: 1520000, clients: 45, avgOrderValue: 17882, growth: 18.7 }
      ],
      yearlyReports: [
        {
          year: '2022年',
          orders: 245,
          revenue: 4250000,
          clients: 125,
          avgOrderValue: 17347,
          growth: 0,
          quarters: [
            { quarter: 'Q1', orders: 58, revenue: 985000 },
            { quarter: 'Q2', orders: 65, revenue: 1125000 },
            { quarter: 'Q3', orders: 62, revenue: 1080000 },
            { quarter: 'Q4', orders: 60, revenue: 1060000 }
          ]
        },
        {
          year: '2023年',
          orders: 298,
          revenue: 5180000,
          clients: 156,
          avgOrderValue: 17383,
          growth: 21.9,
          quarters: [
            { quarter: 'Q1', orders: 70, revenue: 1220000 },
            { quarter: 'Q2', orders: 78, revenue: 1365000 },
            { quarter: 'Q3', orders: 75, revenue: 1295000 },
            { quarter: 'Q4', orders: 75, revenue: 1300000 }
          ]
        },
        {
          year: '2024年',
          orders: 318,
          revenue: 5620000,
          clients: 178,
          avgOrderValue: 17673,
          growth: 8.5,
          quarters: [
            { quarter: 'Q1', orders: 65, revenue: 1128000 },
            { quarter: 'Q2', orders: 90, revenue: 1583000 },
            { quarter: 'Q3', orders: 78, revenue: 1389000 },
            { quarter: 'Q4', orders: 85, revenue: 1520000 }
          ]
        }
      ],
      productStats: [
        { name: '方形风口', quantity: 245, revenue: 856000, orders: 45 },
        { name: '圆形风口', quantity: 189, revenue: 678000, orders: 38 },
        { name: '矩形风口', quantity: 156, revenue: 545000, orders: 32 },
        { name: '定制风口', quantity: 89, revenue: 445000, orders: 28 },
        { name: '百叶风口', quantity: 67, revenue: 234000, orders: 18 },
        { name: '散流器', quantity: 45, revenue: 156000, orders: 12 }
      ],
      clientStats: [
        { name: '上海建筑公司', orders: 12, revenue: 285000, lastOrder: '2024-01-15' },
        { name: '北京装饰工程', orders: 8, revenue: 198000, lastOrder: '2024-01-12' },
        { name: '广州空调安装', orders: 6, revenue: 145000, lastOrder: '2024-01-10' },
        { name: '深圳建设集团', orders: 9, revenue: 234000, lastOrder: '2024-01-08' },
        { name: '成都暖通公司', orders: 5, revenue: 98000, lastOrder: '2024-01-05' }
      ],
      paymentStats: [
        { status: '已付清', count: 89, amount: 1850000 },
        { status: '部分付款', count: 34, amount: 680000 },
        { status: '未付款', count: 28, amount: 285000 },
        { status: '逾期', count: 5, amount: 35000 }
      ],
      employeeStats: [
        { name: '张小明', orders: 45, revenue: 856000 },
        { name: '李经理', orders: 38, revenue: 678000 },
        { name: '王小红', orders: 32, revenue: 545000 },
        { name: '刘师傅', orders: 28, revenue: 445000 },
        { name: '陈主管', orders: 13, revenue: 325000 }
      ]
    }
  }

  // 导出数据分析报告
  const handleExportReport = () => {
    if (!data) return

    try {
      const wb = XLSX.utils.book_new()

      // 概览数据工作表
      const overviewData = [
        ['数据分析报告', ''],
        ['生成时间', new Date().toLocaleString('zh-CN')],
        ['时间范围', timeRange === '6months' ? '最近6个月' : timeRange === '1year' ? '最近1年' : '全部时间'],
        ['', ''],
        ['关键指标', '数值'],
        ['总订单数', data.totalOrders],
        ['总营收(元)', data.totalRevenue],
        ['客户总数', data.totalClients],
        ['平均订单价值(元)', Math.round(data.averageOrderValue)],
        ['', ''],
        ['月度趋势', ''],
        ['月份', '订单数', '营收(元)', '新客户数'],
        ...data.monthlyTrends.map(trend => [trend.month, trend.orders, trend.revenue, trend.clients])
      ]

      // 月度报表工作表
      const monthlyData = [
        ['月度详细报表', ''],
        ['', ''],
        ['月份', '订单数', '营收(元)', '客户数', '平均订单价值(元)', '环比增长(%)'],
        ...data.monthlyReports.map(report => [
          report.period,
          report.orders,
          report.revenue,
          report.clients,
          Math.round(report.avgOrderValue),
          report.growth.toFixed(1)
        ])
      ]

      // 季度报表工作表
      const quarterlyData = [
        ['季度报表', ''],
        ['', ''],
        ['季度', '订单数', '营收(元)', '客户数', '平均订单价值(元)', '同比增长(%)'],
        ...data.quarterlyReports.map(report => [
          report.period,
          report.orders,
          report.revenue,
          report.clients,
          Math.round(report.avgOrderValue),
          report.growth.toFixed(1)
        ])
      ]

      // 年度报表工作表
      const yearlyData = [
        ['年度报表', ''],
        ['', ''],
        ['年份', '订单数', '营收(元)', '客户数', '平均订单价值(元)', '同比增长(%)'],
        ...data.yearlyReports.map((report, index) => [
          report.year,
          report.orders,
          report.revenue,
          report.clients,
          Math.round(report.avgOrderValue),
          index === 0 ? '-' : report.growth.toFixed(1)
        ])
      ]

      const overviewWs = XLSX.utils.aoa_to_sheet(overviewData)
      overviewWs['!cols'] = [{ wch: 20 }, { wch: 20 }, { wch: 15 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(wb, overviewWs, '数据概览')

      // 添加月度报表工作表
      const monthlyWs = XLSX.utils.aoa_to_sheet(monthlyData)
      monthlyWs['!cols'] = [{ wch: 15 }, { wch: 12 }, { wch: 15 }, { wch: 12 }, { wch: 18 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(wb, monthlyWs, '月度报表')

      // 添加季度报表工作表
      const quarterlyWs = XLSX.utils.aoa_to_sheet(quarterlyData)
      quarterlyWs['!cols'] = [{ wch: 15 }, { wch: 12 }, { wch: 15 }, { wch: 12 }, { wch: 18 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(wb, quarterlyWs, '季度报表')

      // 添加年度报表工作表
      const yearlyWs = XLSX.utils.aoa_to_sheet(yearlyData)
      yearlyWs['!cols'] = [{ wch: 15 }, { wch: 12 }, { wch: 15 }, { wch: 12 }, { wch: 18 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(wb, yearlyWs, '年度报表')

      // 产品分析工作表
      const productData = [
        ['产品销售分析', ''],
        ['', ''],
        ['产品名称', '销售数量', '销售金额(元)', '订单数量'],
        ...data.productStats.map(product => [
          product.name,
          product.quantity,
          product.revenue,
          product.orders
        ])
      ]

      const productWs = XLSX.utils.aoa_to_sheet(productData)
      productWs['!cols'] = [{ wch: 20 }, { wch: 15 }, { wch: 20 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(wb, productWs, '产品分析')

      // 客户分析工作表
      const clientData = [
        ['客户价值分析', ''],
        ['', ''],
        ['客户名称', '订单数量', '消费金额(元)', '最后订单日期'],
        ...data.clientStats.map(client => [
          client.name,
          client.orders,
          client.revenue,
          client.lastOrder
        ])
      ]

      const clientWs = XLSX.utils.aoa_to_sheet(clientData)
      clientWs['!cols'] = [{ wch: 25 }, { wch: 15 }, { wch: 20 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(wb, clientWs, '客户分析')

      // 员工业绩工作表
      const employeeData = [
        ['员工业绩分析', ''],
        ['', ''],
        ['员工姓名', '订单数量', '业绩金额(元)'],
        ...data.employeeStats.map(employee => [
          employee.name,
          employee.orders,
          employee.revenue
        ])
      ]

      const employeeWs = XLSX.utils.aoa_to_sheet(employeeData)
      employeeWs['!cols'] = [{ wch: 20 }, { wch: 15 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(wb, employeeWs, '员工业绩')

      // 导出Excel文件
      const fileName = `数据分析报告_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(wb, fileName)

      console.log('✅ 数据分析报告导出成功')
    } catch (error) {
      console.error('❌ 导出数据分析报告失败:', error)
      alert('导出失败，请重试')
    }
  }

  if (loading) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">正在加载数据分析...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!data) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">数据加载失败</h3>
            <p className="text-gray-600">无法加载数据分析，请稍后重试</p>
            <Button onClick={loadAnalyticsData} className="mt-4">
              重新加载
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <BarChart3 className="h-6 w-6 mr-2 text-blue-600" />
              数据分析
            </h1>
            <p className="text-gray-600">深度洞察业务数据，助力科学决策</p>
          </div>
          <div className="flex items-center space-x-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3months">最近3个月</SelectItem>
                <SelectItem value="6months">最近6个月</SelectItem>
                <SelectItem value="1year">最近1年</SelectItem>
                <SelectItem value="all">全部时间</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleExportReport} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              导出报告
            </Button>
          </div>
        </div>

        {/* 关键指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总订单数</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.totalOrders}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                较上期增长 12.5%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总营收</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥{(data.totalRevenue / 10000).toFixed(1)}万</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                较上期增长 8.3%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">客户总数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.totalClients}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                较上期增长 15.2%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均订单价值</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥{safeLocaleString(Math.round(safeNumber(data.averageOrderValue)))}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingDown className="h-3 w-3 inline mr-1" />
                较上期下降 3.1%
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 分析标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="overview">业务概览</TabsTrigger>
            <TabsTrigger value="monthly">月度报表</TabsTrigger>
            <TabsTrigger value="quarterly">季度报表</TabsTrigger>
            <TabsTrigger value="yearly">年度报表</TabsTrigger>
            <TabsTrigger value="sales">销售分析</TabsTrigger>
            <TabsTrigger value="products">产品分析</TabsTrigger>
            <TabsTrigger value="clients">客户分析</TabsTrigger>
            <TabsTrigger value="employees">员工业绩</TabsTrigger>
          </TabsList>

          {/* 业务概览 */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 月度趋势图 */}
              <Card>
                <CardHeader>
                  <CardTitle>月度业务趋势</CardTitle>
                  <CardDescription>订单数量和营收变化趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={data.monthlyTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip
                        formatter={(value, name) => [
                          name === 'orders' ? `${value} 个` : `¥${Number(value).toLocaleString()}`,
                          name === 'orders' ? '订单数' : '营收'
                        ]}
                      />
                      <Legend />
                      <Bar yAxisId="left" dataKey="orders" fill="#8884d8" name="订单数" />
                      <Line yAxisId="right" type="monotone" dataKey="revenue" stroke="#82ca9d" name="营收" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 付款状态分布 */}
              <Card>
                <CardHeader>
                  <CardTitle>付款状态分布</CardTitle>
                  <CardDescription>订单付款状态统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={data.paymentStats}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ status, count }) => `${status}: ${count}`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {data.paymentStats.map((entry, index) => (
                          <Cell key={`payment-stats-cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value} 个`, '订单数']} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 付款状态详细统计 */}
            <Card>
              <CardHeader>
                <CardTitle>付款状态详细统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {data.paymentStats.map((stat, index) => (
                    <div key={stat.status} className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold" style={{ color: COLORS[index % COLORS.length] }}>
                        {stat.count}
                      </div>
                      <div className="text-sm text-gray-600">{stat.status}</div>
                      <div className="text-xs text-gray-500">
                        ¥{safeLocaleString(stat.amount)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 月度报表 */}
          <TabsContent value="monthly" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 月度营收趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle>月度营收趋势</CardTitle>
                  <CardDescription>最近12个月营收变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={data.monthlyReports}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']} />
                      <Line type="monotone" dataKey="revenue" stroke="#8884d8" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 月度增长率 */}
              <Card>
                <CardHeader>
                  <CardTitle>月度增长率</CardTitle>
                  <CardDescription>环比增长率变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.monthlyReports}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${Number(value).toFixed(1)}%`, '增长率']} />
                      <Bar dataKey="growth" fill="#10b981" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 月度详细数据表 */}
            <Card>
              <CardHeader>
                <CardTitle>月度详细数据</CardTitle>
                <CardDescription>各月份业务指标详情</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3">月份</th>
                        <th className="text-right p-3">订单数</th>
                        <th className="text-right p-3">营收(元)</th>
                        <th className="text-right p-3">客户数</th>
                        <th className="text-right p-3">平均订单价值</th>
                        <th className="text-right p-3">环比增长</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.monthlyReports.map((report, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="p-3 font-medium">{report.period}</td>
                          <td className="p-3 text-right">{report.orders}</td>
                          <td className="p-3 text-right">¥{safeLocaleString(report.revenue)}</td>
                          <td className="p-3 text-right">{report.clients}</td>
                          <td className="p-3 text-right">¥{safeLocaleString(Math.round(safeNumber(report.avgOrderValue)))}</td>
                          <td className={`p-3 text-right font-medium ${
                            safeNumber(report.growth) >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {safeNumber(report.growth) >= 0 ? '+' : ''}{safeNumber(report.growth).toFixed(1)}%
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 季度报表 */}
          <TabsContent value="quarterly" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 季度营收对比 */}
              <Card>
                <CardHeader>
                  <CardTitle>季度营收对比</CardTitle>
                  <CardDescription>各季度营收表现</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.quarterlyReports}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="period" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']} />
                      <Bar dataKey="revenue" fill="#3b82f6" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 季度增长趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle>季度增长趋势</CardTitle>
                  <CardDescription>同比增长率变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={data.quarterlyReports}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="quarter" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${Number(value).toFixed(1)}%`, '增长率']} />
                      <Line type="monotone" dataKey="growth" stroke="#10b981" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 季度数据表 */}
            <Card>
              <CardHeader>
                <CardTitle>季度业绩汇总</CardTitle>
                <CardDescription>各季度关键指标对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3">季度</th>
                        <th className="text-right p-3">订单数</th>
                        <th className="text-right p-3">营收(元)</th>
                        <th className="text-right p-3">客户数</th>
                        <th className="text-right p-3">平均订单价值</th>
                        <th className="text-right p-3">同比增长</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.quarterlyReports.map((report, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="p-3 font-medium">{report.period}</td>
                          <td className="p-3 text-right">{report.orders}</td>
                          <td className="p-3 text-right">¥{safeLocaleString(report.revenue)}</td>
                          <td className="p-3 text-right">{report.clients}</td>
                          <td className="p-3 text-right">¥{safeLocaleString(Math.round(safeNumber(report.avgOrderValue)))}</td>
                          <td className={`p-3 text-right font-medium ${
                            safeNumber(report.growth) >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {safeNumber(report.growth) >= 0 ? '+' : ''}{safeNumber(report.growth).toFixed(1)}%
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 年度报表 */}
          <TabsContent value="yearly" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 年度营收趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle>年度营收趋势</CardTitle>
                  <CardDescription>历年营收增长情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.yearlyReports}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="year" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']} />
                      <Bar dataKey="revenue" fill="#8b5cf6" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 年度季度分解 */}
              <Card>
                <CardHeader>
                  <CardTitle>年度季度分解</CardTitle>
                  <CardDescription>当年各季度营收分布</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.yearlyReports[data.yearlyReports.length - 1]?.quarters || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="quarter" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']} />
                      <Bar dataKey="revenue" fill="#f59e0b" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 年度数据表 */}
            <Card>
              <CardHeader>
                <CardTitle>年度业绩总览</CardTitle>
                <CardDescription>历年关键指标对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3">年份</th>
                        <th className="text-right p-3">订单数</th>
                        <th className="text-right p-3">营收(元)</th>
                        <th className="text-right p-3">客户数</th>
                        <th className="text-right p-3">平均订单价值</th>
                        <th className="text-right p-3">同比增长</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.yearlyReports.map((report, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="p-3 font-medium">{report.year}</td>
                          <td className="p-3 text-right">{report.orders}</td>
                          <td className="p-3 text-right">¥{safeLocaleString(report.revenue)}</td>
                          <td className="p-3 text-right">{report.clients}</td>
                          <td className="p-3 text-right">¥{safeLocaleString(Math.round(safeNumber(report.avgOrderValue)))}</td>
                          <td className={`p-3 text-right font-medium ${
                            safeNumber(report.growth) >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {index === 0 ? '-' : `${safeNumber(report.growth) >= 0 ? '+' : ''}${safeNumber(report.growth).toFixed(1)}%`}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* 年度季度详细分解 */}
            <Card>
              <CardHeader>
                <CardTitle>年度季度详细分解</CardTitle>
                <CardDescription>各年度季度表现详情</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {data.yearlyReports.map((yearReport, yearIndex) => (
                    <div key={yearIndex} className="border rounded-lg p-4">
                      <h4 className="font-medium text-lg mb-3">{yearReport.year}</h4>
                      <div className="grid grid-cols-4 gap-4">
                        {yearReport.quarters.map((quarter, quarterIndex) => (
                          <div key={quarterIndex} className="text-center p-3 bg-gray-50 rounded">
                            <div className="font-medium text-gray-900">{quarter.quarter}</div>
                            <div className="text-sm text-gray-600">{quarter.orders}个订单</div>
                            <div className="text-sm font-medium text-blue-600">
                              ¥{safeWanYuan(quarter.revenue, 1)}万
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 销售分析 */}
          <TabsContent value="sales" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 月度销售趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle>月度销售趋势</CardTitle>
                  <CardDescription>营收和订单数量变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={data.monthlyTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']} />
                      <Area type="monotone" dataKey="revenue" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 客户增长趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle>客户增长趋势</CardTitle>
                  <CardDescription>每月新增客户数量</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.monthlyTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value} 个`, '新客户']} />
                      <Bar dataKey="clients" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 产品分析 */}
          <TabsContent value="products" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 产品销量排行 */}
              <Card>
                <CardHeader>
                  <CardTitle>产品销量排行</CardTitle>
                  <CardDescription>按销售数量排序</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.productStats} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={80} />
                      <Tooltip formatter={(value) => [`${value} 个`, '销量']} />
                      <Bar dataKey="quantity" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 产品营收排行 */}
              <Card>
                <CardHeader>
                  <CardTitle>产品营收排行</CardTitle>
                  <CardDescription>按销售金额排序</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.productStats} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={80} />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '营收']} />
                      <Bar dataKey="revenue" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 产品详细统计表 */}
            <Card>
              <CardHeader>
                <CardTitle>产品详细统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">产品名称</th>
                        <th className="text-right p-2">销售数量</th>
                        <th className="text-right p-2">销售金额</th>
                        <th className="text-right p-2">订单数量</th>
                        <th className="text-right p-2">平均单价</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.productStats.map((product, index) => (
                        <tr key={product.name} className="border-b hover:bg-gray-50">
                          <td className="p-2 font-medium">{product.name}</td>
                          <td className="p-2 text-right">{product.quantity}</td>
                          <td className="p-2 text-right">¥{safeLocaleString(product.revenue)}</td>
                          <td className="p-2 text-right">{product.orders}</td>
                          <td className="p-2 text-right">
                            ¥{safeLocaleString(Math.round(safeDivide(product.revenue, product.quantity)))}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 客户分析 */}
          <TabsContent value="clients" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 客户价值分布 */}
              <Card>
                <CardHeader>
                  <CardTitle>客户价值分布</CardTitle>
                  <CardDescription>按消费金额排序</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.clientStats.slice(0, 8)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '消费金额']} />
                      <Bar dataKey="revenue" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 客户订单频次 */}
              <Card>
                <CardHeader>
                  <CardTitle>客户订单频次</CardTitle>
                  <CardDescription>按订单数量排序</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.clientStats.slice(0, 8)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value} 个`, '订单数']} />
                      <Bar dataKey="orders" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 客户详细统计表 */}
            <Card>
              <CardHeader>
                <CardTitle>重要客户统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">客户名称</th>
                        <th className="text-right p-2">订单数量</th>
                        <th className="text-right p-2">消费金额</th>
                        <th className="text-right p-2">平均订单价值</th>
                        <th className="text-right p-2">最后订单</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.clientStats.map((client, index) => (
                        <tr key={client.name} className="border-b hover:bg-gray-50">
                          <td className="p-2 font-medium">{client.name}</td>
                          <td className="p-2 text-right">{client.orders}</td>
                          <td className="p-2 text-right">¥{safeLocaleString(client.revenue)}</td>
                          <td className="p-2 text-right">
                            ¥{safeLocaleString(Math.round(safeDivide(client.revenue, client.orders)))}
                          </td>
                          <td className="p-2 text-right text-sm text-gray-600">{client.lastOrder}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 员工业绩 */}
          <TabsContent value="employees" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 员工业绩排行 */}
              <Card>
                <CardHeader>
                  <CardTitle>员工业绩排行</CardTitle>
                  <CardDescription>按销售金额排序</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.employeeStats}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '业绩']} />
                      <Bar dataKey="revenue" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 员工订单数量 */}
              <Card>
                <CardHeader>
                  <CardTitle>员工订单数量</CardTitle>
                  <CardDescription>按订单数量排序</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.employeeStats}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value} 个`, '订单数']} />
                      <Bar dataKey="orders" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* 员工业绩统计表 */}
            <Card>
              <CardHeader>
                <CardTitle>员工业绩详细统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">员工姓名</th>
                        <th className="text-right p-2">订单数量</th>
                        <th className="text-right p-2">业绩金额</th>
                        <th className="text-right p-2">平均订单价值</th>
                        <th className="text-right p-2">业绩占比</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.employeeStats.map((employee, index) => {
                        const percentage = safeDivide(employee.revenue, data.totalRevenue) * 100
                        return (
                          <tr key={employee.name} className="border-b hover:bg-gray-50">
                            <td className="p-2 font-medium flex items-center">
                              {index < 3 && (
                                <Award className={`h-4 w-4 mr-2 ${
                                  index === 0 ? 'text-yellow-500' :
                                  index === 1 ? 'text-gray-400' : 'text-orange-500'
                                }`} />
                              )}
                              {employee.name}
                            </td>
                            <td className="p-2 text-right">{employee.orders}</td>
                            <td className="p-2 text-right">¥{safeLocaleString(employee.revenue)}</td>
                            <td className="p-2 text-right">
                              ¥{safeLocaleString(Math.round(safeDivide(employee.revenue, employee.orders)))}
                            </td>
                            <td className="p-2 text-right">{safeNumber(percentage).toFixed(1)}%</td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* 员工业绩提醒 */}
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-6">
                <div className="flex items-center space-x-2 text-blue-600">
                  <Target className="h-5 w-5" />
                  <span className="font-medium">业绩分析提醒</span>
                </div>
                <div className="mt-2 text-sm text-blue-600">
                  <p>• 前三名员工贡献了 {
                    (safeDivide(safeSum(data.employeeStats.slice(0, 3).map(emp => emp.revenue)), data.totalRevenue) * 100).toFixed(1)
                  }% 的总业绩</p>
                  <p>• 建议对优秀员工进行表彰和激励</p>
                  <p>• 关注业绩较低员工的培训和支持需求</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
