"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger  } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Loader2, Users } from "lucide-react"
import { getCurrentFactoryId, getCurrentUser } from "@/lib/utils/factory"
import type { Shareholder, ShareholderType, ShareholderStatus } from "@/types"

interface AddShareholderDialogProps {
  onShareholderAdded?: (shareholder: Shareholder) => void
}

export function AddShareholderDialog({ onShareholderAdded }: AddShareholderDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    idCard: "",
    phone: "",
    email: "",
    address: "",
    shareholderType: "INVESTOR" as ShareholderType,
    shareCount: "",
    sharePercentage: "",
    investmentAmount: "",
    joinDate: "",
    status: "active" as ShareholderStatus,
    notes: ""
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // 股东类型选项
  const shareholderTypeOptions = [
    { value: "FOUNDER", label: "创始股东" },
    { value: "INVESTOR", label: "投资股东" },
    { value: "EMPLOYEE", label: "员工股东" },
    { value: "PARTNER", label: "合伙人股东" }
  ]

  // 股东状态选项
  const shareholderStatusOptions = [
    { value: "active", label: "在职股东" },
    { value: "inactive", label: "非活跃股东" }
  ]

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "股东姓名不能为空"
    }

    if (!formData.shareholderType) {
      newErrors.shareholderType = "请选择股东类型"
    }

    // 🔧 修改：持股数量改为非必填项
    if (formData.shareCount.trim() && (isNaN(Number(formData.shareCount)) || Number(formData.shareCount) <= 0)) {
      newErrors.shareCount = "持股数量必须是正数"
    }

    if (!formData.sharePercentage.trim()) {
      newErrors.sharePercentage = "持股比例不能为空"
    } else if (isNaN(Number(formData.sharePercentage)) || Number(formData.sharePercentage) <= 0 || Number(formData.sharePercentage) > 100) {
      newErrors.sharePercentage = "持股比例必须在0-100之间"
    }

    if (!formData.investmentAmount.trim()) {
      newErrors.investmentAmount = "投资金额不能为空"
    } else if (isNaN(Number(formData.investmentAmount)) || Number(formData.investmentAmount) < 0) {
      newErrors.investmentAmount = "投资金额必须是非负数"
    }

    if (!formData.joinDate) {
      newErrors.joinDate = "入股日期不能为空"
    }

    // 验证身份证号格式（如果填写）
    if (formData.idCard && !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(formData.idCard)) {
      newErrors.idCard = "身份证号格式不正确"
    }

    // 验证手机号格式（如果填写）
    if (formData.phone && !/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = "手机号格式不正确"
    }

    // 验证邮箱格式（如果填写）
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "邮箱格式不正确"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()
      const currentUser = getCurrentUser()

      if (!factoryId) {
        setErrors({ general: "无法获取工厂信息" })
        return
      }

      if (!currentUser) {
        setErrors({ general: "无法获取用户信息" })
        return
      }

      const shareholderData = {
        factoryId,
        name: formData.name.trim(),
        idCard: formData.idCard.trim() || undefined,
        phone: formData.phone.trim() || undefined,
        email: formData.email.trim() || undefined,
        address: formData.address.trim() || undefined,
        shareholderType: formData.shareholderType,
        shareCount: formData.shareCount.trim() ? Number(formData.shareCount) : 0, // 🔧 如果未填写则默认为0
        sharePercentage: Number(formData.sharePercentage),
        investmentAmount: Number(formData.investmentAmount),
        joinDate: formData.joinDate,
        status: formData.status,
        notes: formData.notes.trim() || undefined,
        createdBy: currentUser.id
      }

      // 调用API创建股东
      const response = await fetch('/api/shareholders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(shareholderData),
      })

      const result = await response.json()

      if (result.success) {
        console.log('✅ 股东创建成功:', result.shareholder)
        
        // 重置表单
        setFormData({
          name: "",
          idCard: "",
          phone: "",
          email: "",
          address: "",
          shareholderType: "INVESTOR",
          shareCount: "",
          sharePercentage: "",
          investmentAmount: "",
          joinDate: "",
          status: "active",
          notes: ""
        })
        setErrors({})
        setOpen(false)

        // 通知父组件
        if (onShareholderAdded) {
          onShareholderAdded(result.shareholder)
        }
      } else {
        setErrors({ general: result.error || "创建股东失败，请重试" })
      }
    } catch (error) {
      console.error('创建股东失败:', error)
      setErrors({ general: `创建失败: ${error.message}` })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          添加股东
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>添加新股东</span>
          </DialogTitle>
          <DialogDescription>
            填写股东的基本信息和股权信息，带 * 的字段为必填项
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 错误提示 */}
          {errors.general && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {errors.general}
            </div>
          )}

          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">基本信息</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 股东姓名 */}
              <div className="space-y-2">
                <Label htmlFor="name">股东姓名 *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="请输入股东姓名"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
              </div>

              {/* 股东类型 */}
              <div className="space-y-2">
                <Label htmlFor="shareholderType">股东类型 *</Label>
                <Select
                  value={formData.shareholderType}
                  onValueChange={(value: ShareholderType) => 
                    setFormData(prev => ({ ...prev, shareholderType: value }))
                  }
                >
                  <SelectTrigger className={errors.shareholderType ? "border-red-500" : ""}>
                    <SelectValue placeholder="请选择股东类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {shareholderTypeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.shareholderType && <p className="text-sm text-red-600">{errors.shareholderType}</p>}
              </div>

              {/* 身份证号 */}
              <div className="space-y-2">
                <Label htmlFor="idCard">身份证号</Label>
                <Input
                  id="idCard"
                  value={formData.idCard}
                  onChange={(e) => setFormData(prev => ({ ...prev, idCard: e.target.value }))}
                  placeholder="请输入身份证号"
                  className={errors.idCard ? "border-red-500" : ""}
                />
                {errors.idCard && <p className="text-sm text-red-600">{errors.idCard}</p>}
              </div>

              {/* 手机号 */}
              <div className="space-y-2">
                <Label htmlFor="phone">手机号</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '') // 只允许数字
                    setFormData(prev => ({ ...prev, phone: value }))
                  }}
                  maxLength={11}
                  pattern="[0-9]*"
                  placeholder="请输入11位手机号码"
                  className={errors.phone ? "border-red-500" : ""}
                />
                {errors.phone && <p className="text-sm text-red-600">{errors.phone}</p>}
              </div>

              {/* 邮箱 */}
              <div className="space-y-2">
                <Label htmlFor="email">邮箱</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="请输入邮箱地址"
                  className={errors.email ? "border-red-500" : ""}
                />
                {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
              </div>

              {/* 股东状态 */}
              <div className="space-y-2">
                <Label htmlFor="status">股东状态</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: ShareholderStatus) => 
                    setFormData(prev => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {shareholderStatusOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 地址 */}
            <div className="space-y-2">
              <Label htmlFor="address">联系地址</Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                placeholder="请输入联系地址"
              />
            </div>
          </div>

          {/* 股权信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">股权信息</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 持股数量 */}
              <div className="space-y-2">
                <Label htmlFor="shareCount">持股数量</Label>
                <Input
                  id="shareCount"
                  type="number"
                  value={formData.shareCount}
                  onChange={(e) => setFormData(prev => ({ ...prev, shareCount: e.target.value }))}
                  placeholder="可选，如不填写将基于投资金额计算"
                  className={errors.shareCount ? "border-red-500" : ""}
                />
                {errors.shareCount && <p className="text-sm text-red-600">{errors.shareCount}</p>}
                <p className="text-xs text-gray-500">💡 提示：如果不填写，系统将根据投资金额自动计算股份数</p>
              </div>

              {/* 持股比例 */}
              <div className="space-y-2">
                <Label htmlFor="sharePercentage">持股比例 (%) *</Label>
                <Input
                  id="sharePercentage"
                  type="number"
                  step="0.01"
                  max="100"
                  value={formData.sharePercentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, sharePercentage: e.target.value }))}
                  placeholder="请输入持股比例"
                  className={errors.sharePercentage ? "border-red-500" : ""}
                />
                {errors.sharePercentage && <p className="text-sm text-red-600">{errors.sharePercentage}</p>}
              </div>

              {/* 投资金额 */}
              <div className="space-y-2">
                <Label htmlFor="investmentAmount">投资金额 (元) *</Label>
                <Input
                  id="investmentAmount"
                  type="number"
                  step="0.01"
                  value={formData.investmentAmount}
                  onChange={(e) => setFormData(prev => ({ ...prev, investmentAmount: e.target.value }))}
                  placeholder="请输入投资金额"
                  className={errors.investmentAmount ? "border-red-500" : ""}
                />
                {errors.investmentAmount && <p className="text-sm text-red-600">{errors.investmentAmount}</p>}
              </div>
            </div>

            {/* 入股日期 */}
            <div className="space-y-2">
              <Label htmlFor="joinDate">入股日期 *</Label>
              <Input
                id="joinDate"
                type="date"
                value={formData.joinDate}
                onChange={(e) => setFormData(prev => ({ ...prev, joinDate: e.target.value }))}
                className={errors.joinDate ? "border-red-500" : ""}
              />
              {errors.joinDate && <p className="text-sm text-red-600">{errors.joinDate}</p>}
            </div>
          </div>

          {/* 备注 */}
          <div className="space-y-2">
            <Label htmlFor="notes">备注</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="请输入备注信息（可选）"
              rows={3}
            />
          </div>

          {/* 按钮 */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  创建中...
                </>) : (
                "创建股东"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
