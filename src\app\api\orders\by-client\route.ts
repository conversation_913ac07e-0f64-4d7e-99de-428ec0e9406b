/**
 * 🇨🇳 风口云平台 - 根据客户ID获取订单API
 *
 * 获取指定客户的所有订单
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminOrFactoryAuth } from '@/lib/middleware/auth'

// 公开获取客户订单处理函数
async function handlePublicClientOrders(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const clientId = searchParams.get('clientId')
    const isPublic = searchParams.get('public') === 'true'

    console.log('📋 公开获取客户订单API请求:', { clientId, isPublic })

    if (!clientId) {
      return NextResponse.json({
        success: false,
        error: '客户ID不能为空'
      }, { status: 400 })
    }

    // 验证客户是否存在
    const client = await db.getClientById(clientId)
    if (!client) {
      return NextResponse.json({
        success: false,
        error: '客户不存在'
      }, { status: 404 })
    }

    // 获取客户的订单
    const orders = await db.getOrdersByClientId(clientId)
    console.log('✅ 客户订单获取完成，找到', orders.length, '条订单')

    // 按创建时间倒序排列
    orders.sort((a, b) => {
      const aTime = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt)
      const bTime = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt)
      return bTime.getTime() - aTime.getTime()
    })

    return NextResponse.json({
      success: true,
      orders,
      total: orders.length,
      clientId,
      isPublic
    })

  } catch (error) {
    console.error('❌ 公开获取客户订单API错误:', error)
    return NextResponse.json({
      success: false,
      error: '获取客户订单失败，请重试'
    }, { status: 500 })
  }
}

// 认证获取客户订单处理函数
const handleAuthenticatedClientOrders = withAdminOrFactoryAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const clientId = searchParams.get('clientId')

    console.log('📋 认证获取客户订单API请求:', { clientId, userType: user.userType })

    if (!clientId) {
      return NextResponse.json({
        success: false,
        error: '客户ID不能为空'
      }, { status: 400 })
    }

    // 验证客户是否存在
    const client = await db.getClientById(clientId)
    if (!client) {
      return NextResponse.json({
        success: false,
        error: '客户不存在'
      }, { status: 404 })
    }

    // 检查权限：工厂用户只能查看自己工厂的客户订单
    if (user.userType !== 'admin' && user.factoryId !== client.factoryId) {
      return NextResponse.json({
        success: false,
        error: '无权限查看该客户的订单'
      }, { status: 403 })
    }

    // 获取客户的订单
    const orders = await db.getOrdersByClientId(clientId)
    console.log('✅ 认证客户订单获取完成，找到', orders.length, '条订单')

    // 按创建时间倒序排列
    orders.sort((a: unknown, b: unknown) => {
      const aTime = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt)
      const bTime = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt)
      return bTime.getTime() - aTime.getTime()
    })

    return NextResponse.json({
      success: true,
      orders,
      total: orders.length,
      clientId
    })

  } catch (error) {
    console.error('❌ 认证获取客户订单API错误:', error)
    return NextResponse.json({
      success: false,
      error: '获取客户订单失败，请重试'
    }, { status: 500 })
  }
})

// 主要的GET处理函数
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const isPublic = searchParams.get('public') === 'true'
  
  if (isPublic) {
    // 公开访问，不需要认证
    return handlePublicClientOrders(request)
  } else {
    // 认证访问，需要登录
    return handleAuthenticatedClientOrders(request)
  }
}
