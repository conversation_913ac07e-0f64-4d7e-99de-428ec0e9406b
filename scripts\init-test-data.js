#!/usr/bin/env node

/**
 * 🇨🇳 风口云平台 - 初始化测试数据脚本
 * 为本地开发环境创建默认的管理员账户和测试数据
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function initTestData() {
  console.log('🌱 开始初始化测试数据...\n');

  try {
    // 1. 创建默认管理员
    console.log('👤 创建默认管理员账户...');
    const adminPassword = await bcrypt.hash('admin@yhg2025', 12);
    
    const admin = await prisma.admin.upsert({
      where: { username: 'admin' },
      update: {},
      create: {
        username: 'admin',
        passwordHash: adminPassword,
        name: '系统管理员',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true
      }
    });
    console.log('✅ 管理员账户创建成功: admin / admin@yhg2025');

    // 2. 创建默认工厂
    console.log('🏭 创建默认工厂...');
    const factory = await prisma.factory.upsert({
      where: { code: 'lin001' },
      update: {},
      create: {
        id: 'lin001',
        name: '南宁加工厂',
        code: 'lin001',
        address: '广西南宁市',
        phone: '0771-1234567',
        status: 'active'
      }
    });
    console.log('✅ 默认工厂创建成功: 南宁加工厂 (lin001)');

    // 3. 创建默认工厂用户
    console.log('👥 创建默认工厂用户...');
    const factoryUserPassword = await bcrypt.hash('lin001', 10);
    
    const factoryUser = await prisma.factoryUser.upsert({
      where: { username: 'lin001' },
      update: {},
      create: {
        factoryId: factory.id,
        username: 'lin001',
        passwordHash: factoryUserPassword,
        name: '林经理',
        role: 'owner',
        permissions: JSON.stringify(['all']),
        isActive: true
      }
    });
    console.log('✅ 工厂用户创建成功: lin001 / lin001');

    // 4. 创建测试客户
    console.log('👤 创建测试客户...');
    // 先检查客户是否存在
    let client1 = await prisma.client.findFirst({
      where: {
        factoryId: factory.id,
        phone: '***********'
      }
    });

    if (!client1) {
      client1 = await prisma.client.create({
        data: {
          factoryId: factory.id,
          name: '张三',
          phone: '***********',
          email: '<EMAIL>',
          company: '测试公司A',
          address: '广西南宁市青秀区',
          status: 'active'
        }
      });
    }

    let client2 = await prisma.client.findFirst({
      where: {
        factoryId: factory.id,
        phone: '13800138002'
      }
    });

    if (!client2) {
      client2 = await prisma.client.create({
        data: {
          factoryId: factory.id,
          name: '李四',
          phone: '13800138002',
          email: '<EMAIL>',
          company: '测试公司B',
          address: '广西南宁市西乡塘区',
          referrerId: client1.id,
          referrerName: '张三',
          status: 'active'
        }
      });
    }
    console.log('✅ 测试客户创建成功: 张三、李四');

    // 5. 创建测试订单
    console.log('📋 创建测试订单...');
    const orderItems = JSON.stringify([
      {
        type: 'wind_outlet',
        specification: '600x600',
        quantity: 10,
        unitPrice: 120,
        totalPrice: 1200
      },
      {
        type: 'maintenance_port',
        specification: '400x400',
        quantity: 5,
        unitPrice: 100,
        totalPrice: 500
      }
    ]);

    const order = await prisma.order.upsert({
      where: { orderNumber: 'LIN001-********-001' },
      update: {},
      create: {
        factoryId: factory.id,
        clientId: client1.id,
        orderNumber: 'LIN001-********-001',
        items: orderItems,
        totalAmount: 1700,
        status: 'confirmed',
        projectAddress: '南宁市某商业大厦',
        clientName: '张三',
        clientPhone: '***********',
        notes: '测试订单，请按时交货',
        createdBy: factoryUser.id
      }
    });
    console.log('✅ 测试订单创建成功: LIN001-********-001');

    // 6. 创建付款记录
    console.log('💰 创建付款记录...');
    await prisma.payment.create({
      data: {
        orderId: order.id,
        clientId: client1.id,
        amount: 850, // 50%预付款
        paymentMethod: 'bank_transfer',
        status: 'completed',
        paidAt: new Date(),
        notes: '预付款50%'
      }
    });
    console.log('✅ 付款记录创建成功');

    // 7. 创建系统公告
    console.log('📢 创建系统公告...');
    const announcement = await prisma.announcement.create({
      data: {
        title: '欢迎使用风口云平台开发版',
        content: '这是一个测试公告。本地开发环境已经成功配置，您可以开始测试各项功能。',
        type: 'system',
        publishedAt: new Date(),
        createdBy: admin.id
      }
    });

    await prisma.announcementTarget.create({
      data: {
        announcementId: announcement.id,
        factoryId: factory.id
      }
    });
    console.log('✅ 系统公告创建成功');

    console.log('\n🎉 测试数据初始化完成！');
    console.log('\n📝 登录信息：');
    console.log('管理员登录: admin / admin@yhg2025');
    console.log('工厂用户登录: lin001 / lin001');
    console.log('\n💡 提示：');
    console.log('- 数据库文件位置: ./dev.db');
    console.log('- 可以使用 npm run db:studio 查看数据库');
    console.log('- 运行 npm run dev 启动开发服务器');

  } catch (error) {
    console.error('❌ 初始化测试数据失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行初始化
if (require.main === module) {
  initTestData()
    .then(() => {
      console.log('\n✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { initTestData };
