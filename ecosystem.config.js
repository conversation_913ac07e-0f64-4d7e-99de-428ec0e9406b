module.exports = {
  apps: [{
    name: 'factorysystem',
    script: './node_modules/.bin/next',
    args: 'start',
    cwd: '/opt/factorysystem',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s',
    env: {
      NODE_ENV: 'production',
      PORT: '3000',
      HOSTNAME: '0.0.0.0'
    },
    error_file: '/var/log/pm2/factorysystem-error.log',
    out_file: '/var/log/pm2/factorysystem-out.log',
    log_file: '/var/log/pm2/factorysystem.log'
  }]
}
