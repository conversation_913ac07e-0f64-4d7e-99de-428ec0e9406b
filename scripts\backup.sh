#!/bin/bash
# backup.sh - 风口云平台备份脚本
# 使用方法: ./backup.sh [--full] [--compress]

FULL_BACKUP=false
COMPRESS_BACKUP=true
BACKUP_RETENTION_DAYS=30

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --full)
      FULL_BACKUP=true
      shift
      ;;
    --no-compress)
      COMPRESS_BACKUP=false
      shift
      ;;
    --retention)
      BACKUP_RETENTION_DAYS="$2"
      shift 2
      ;;
    -h|--help)
      echo "使用方法: $0 [选项]"
      echo "选项:"
      echo "  --full         完整备份（包含所有文件）"
      echo "  --no-compress  不压缩备份文件"
      echo "  --retention N  备份保留天数（默认30天）"
      echo "  -h, --help     显示帮助信息"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      exit 1
      ;;
  esac
done

echo "💾 风口云平台备份工具"
echo "时间: $(date)"
echo "备份类型: $([ "$FULL_BACKUP" = true ] && echo "完整备份" || echo "标准备份")"
echo "================================"

# 创建备份目录
BACKUP_DIR="/backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📁 备份目录: $BACKUP_DIR"

# 函数：记录备份信息
log_backup_info() {
  cat > "$BACKUP_DIR/backup_info.txt" << EOF
备份信息
========
备份时间: $(date)
备份类型: $([ "$FULL_BACKUP" = true ] && echo "完整备份" || echo "标准备份")
Git提交: $(git rev-parse HEAD 2>/dev/null || echo "未知")
Git分支: $(git branch --show-current 2>/dev/null || echo "未知")
系统信息: $(uname -a)
Docker版本: $(docker --version 2>/dev/null || echo "未安装")
EOF
}

# 函数：备份数据库
backup_database() {
  echo "🗄️ 备份数据库..."
  
  # 检查数据库服务状态
  if ! docker-compose ps postgres | grep -q "Up"; then
    echo "❌ 数据库服务未运行"
    return 1
  fi
  
  # 创建数据库备份
  local db_backup_file="$BACKUP_DIR/database.sql"
  
  if docker-compose exec -T postgres pg_dump -U factorysystem factorysystem > "$db_backup_file"; then
    local db_size=$(du -h "$db_backup_file" | cut -f1)
    echo "✅ 数据库备份完成 ($db_size)"
    
    # 压缩数据库备份
    if [ "$COMPRESS_BACKUP" = true ]; then
      echo "  🗜️ 压缩数据库备份..."
      gzip "$db_backup_file"
      echo "  ✅ 压缩完成"
    fi
  else
    echo "❌ 数据库备份失败"
    return 1
  fi
}

# 函数：备份应用文件
backup_app_files() {
  echo "📁 备份应用文件..."
  
  local files_to_backup=(
    "./uploads"
    "./logs"
    "./.env.production"
    "./.env.local"
    "./docker-compose.yml"
    "./nginx.conf"
    "./package.json"
    "./package-lock.json"
  )
  
  if [ "$FULL_BACKUP" = true ]; then
    files_to_backup+=(
      "./src"
      "./prisma"
      "./public"
      "./next.config.ts"
      "./tsconfig.json"
      "./tailwind.config.ts"
    )
  fi
  
  local app_backup_file="$BACKUP_DIR/app_files.tar"
  
  # 创建文件列表（只包含存在的文件）
  local existing_files=()
  for file in "${files_to_backup[@]}"; do
    if [ -e "$file" ]; then
      existing_files+=("$file")
    fi
  done
  
  if [ ${#existing_files[@]} -gt 0 ]; then
    if tar -cf "$app_backup_file" "${existing_files[@]}" 2>/dev/null; then
      local app_size=$(du -h "$app_backup_file" | cut -f1)
      echo "✅ 应用文件备份完成 ($app_size)"
      
      # 压缩应用文件备份
      if [ "$COMPRESS_BACKUP" = true ]; then
        echo "  🗜️ 压缩应用文件备份..."
        gzip "$app_backup_file"
        echo "  ✅ 压缩完成"
      fi
    else
      echo "❌ 应用文件备份失败"
      return 1
    fi
  else
    echo "⚠️ 没有找到要备份的应用文件"
  fi
}

# 函数：备份Docker镜像（可选）
backup_docker_images() {
  if [ "$FULL_BACKUP" = false ]; then
    return 0
  fi
  
  echo "🐳 备份Docker镜像..."
  
  local images=$(docker-compose config --services)
  local image_backup_dir="$BACKUP_DIR/docker_images"
  mkdir -p "$image_backup_dir"
  
  for service in $images; do
    local image_name=$(docker-compose config | grep -A 5 "^  $service:" | grep "image:" | awk '{print $2}')
    if [ -n "$image_name" ]; then
      echo "  📦 备份镜像: $image_name"
      local image_file="$image_backup_dir/${service}_image.tar"
      
      if docker save "$image_name" > "$image_file" 2>/dev/null; then
        local image_size=$(du -h "$image_file" | cut -f1)
        echo "  ✅ 镜像备份完成 ($image_size)"
        
        if [ "$COMPRESS_BACKUP" = true ]; then
          gzip "$image_file"
        fi
      else
        echo "  ⚠️ 镜像备份失败: $image_name"
        rm -f "$image_file"
      fi
    fi
  done
}

# 函数：创建备份校验和
create_checksums() {
  echo "🔐 创建备份校验和..."
  
  cd "$BACKUP_DIR"
  find . -type f -name "*.sql*" -o -name "*.tar*" | while read file; do
    sha256sum "$file" >> checksums.sha256
  done
  
  if [ -f "checksums.sha256" ]; then
    echo "✅ 校验和文件创建完成"
  fi
}

# 函数：验证备份完整性
verify_backup() {
  echo "✅ 验证备份完整性..."
  
  local errors=0
  
  # 检查数据库备份
  local db_file="$BACKUP_DIR/database.sql"
  if [ "$COMPRESS_BACKUP" = true ]; then
    db_file="$BACKUP_DIR/database.sql.gz"
  fi
  
  if [ -f "$db_file" ] && [ -s "$db_file" ]; then
    echo "  ✅ 数据库备份文件存在且非空"
  else
    echo "  ❌ 数据库备份文件异常"
    errors=$((errors + 1))
  fi
  
  # 检查应用文件备份
  local app_file="$BACKUP_DIR/app_files.tar"
  if [ "$COMPRESS_BACKUP" = true ]; then
    app_file="$BACKUP_DIR/app_files.tar.gz"
  fi
  
  if [ -f "$app_file" ]; then
    echo "  ✅ 应用文件备份存在"
  else
    echo "  ⚠️ 应用文件备份不存在"
  fi
  
  # 验证校验和
  if [ -f "$BACKUP_DIR/checksums.sha256" ]; then
    cd "$BACKUP_DIR"
    if sha256sum -c checksums.sha256 >/dev/null 2>&1; then
      echo "  ✅ 校验和验证通过"
    else
      echo "  ❌ 校验和验证失败"
      errors=$((errors + 1))
    fi
  fi
  
  return $errors
}

# 函数：清理旧备份
cleanup_old_backups() {
  echo "🧹 清理旧备份..."
  
  if [ -d "/backups" ]; then
    local deleted_count=0
    
    # 查找并删除旧备份
    while IFS= read -r -d '' backup_dir; do
      rm -rf "$backup_dir"
      deleted_count=$((deleted_count + 1))
    done < <(find /backups -maxdepth 1 -type d -mtime +$BACKUP_RETENTION_DAYS -print0 2>/dev/null)
    
    if [ $deleted_count -gt 0 ]; then
      echo "✅ 清理了 $deleted_count 个旧备份"
    else
      echo "📋 没有需要清理的旧备份"
    fi
    
    # 显示当前备份统计
    local total_backups=$(ls -1 /backups | wc -l)
    local total_size=$(du -sh /backups 2>/dev/null | cut -f1)
    echo "📊 当前备份统计: $total_backups 个备份，总大小 $total_size"
  fi
}

# 函数：发送备份通知
send_notification() {
  local status="$1"
  local message="$2"
  
  # 这里可以添加邮件、Slack、微信等通知逻辑
  echo "📢 通知: [$status] $message"
  
  # 示例：写入系统日志
  logger "风口云平台备份: [$status] $message"
}

# 函数：显示备份摘要
show_backup_summary() {
  echo ""
  echo "📊 备份摘要"
  echo "================================"
  echo "备份时间: $(date)"
  echo "备份目录: $BACKUP_DIR"
  echo "备份类型: $([ "$FULL_BACKUP" = true ] && echo "完整备份" || echo "标准备份")"
  
  if [ -d "$BACKUP_DIR" ]; then
    local backup_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    local file_count=$(find "$BACKUP_DIR" -type f | wc -l)
    echo "备份大小: $backup_size"
    echo "文件数量: $file_count"
    
    echo ""
    echo "📁 备份内容:"
    ls -la "$BACKUP_DIR"
  fi
  
  echo ""
  echo "🎉 备份完成！"
}

# 主函数
main() {
  local start_time=$(date)
  
  # 记录备份信息
  log_backup_info
  
  # 执行备份
  if backup_database && backup_app_files; then
    if [ "$FULL_BACKUP" = true ]; then
      backup_docker_images
    fi
    
    create_checksums
    
    if verify_backup; then
      cleanup_old_backups
      show_backup_summary
      send_notification "SUCCESS" "备份成功完成 (开始时间: $start_time)"
      echo "✅ 备份成功完成！"
    else
      send_notification "WARNING" "备份完成但验证发现问题"
      echo "⚠️ 备份完成但验证发现问题"
      exit 1
    fi
  else
    send_notification "ERROR" "备份过程中发生错误"
    echo "❌ 备份失败"
    exit 1
  fi
}

# 执行主函数
main "$@"
