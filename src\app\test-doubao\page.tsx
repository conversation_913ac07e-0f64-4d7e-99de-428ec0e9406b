/**
 * 🇨🇳 风口云平台 - 豆包AI测试页面
 */

'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { getDoubaoAPI } from '@/lib/services/doubao-api'

const API_KEYS = {
  DOUBAO: process.env.NEXT_PUBLIC_DOUBAO_API_KEY || '20dc1a0c-8219-4280-9441-901efe0d7637'
}

export default function TestDoubaoPage() {
  const [inputText, setInputText] = useState(`客厅回风141*30
客出风568.2*14.5
房间出风205.2*14.7
房间回风93.7*30（单边）
4455*145(一分二)
5580*150(一分二)
出风271.8*15.1
141*30`)

  const [result, setResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 检查配置状态
  const isApiKeyConfigured = API_KEYS.DOUBAO && API_KEYS.DOUBAO !== 'YOUR_DOUBAO_API_KEY_HERE'
  const configStatus = isApiKeyConfigured ? '✅ 已配置' : '⚠️ 需要配置'

  const handleTest = async () => {
    if (!inputText.trim()) {
      setError('请输入测试文本')
      return
    }

    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('🚀 开始测试豆包AI...')
      const doubao = getDoubaoAPI(API_KEYS.DOUBAO)
      
      // 先测试连接
      console.log('🔧 测试连接...')
      const connectionTest = await doubao.testConnection()
      console.log('🔧 连接测试结果:', connectionTest)

      if (!connectionTest.success) {
        throw new Error(`连接测试失败: ${connectionTest.error}`)
      }

      // 测试风口分析
      console.log('🔧 测试风口分析...')
      const analysisResult = await doubao.analyzeVentOrder(inputText)
      console.log('✅ 分析结果:', analysisResult)

      setResult(analysisResult)
    } catch (err) {
      console.error('❌ 测试失败:', err)
      setError(err instanceof Error ? err.message : '测试失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            🤖 豆包AI测试页面
          </CardTitle>
          <p className="text-center text-gray-600">
            测试豆包AI（火山引擎）的风口识别功能
          </p>

          {/* 配置状态提示 */}
          <div className={`p-3 rounded-lg text-center ${isApiKeyConfigured ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
            <p className={`font-medium ${isApiKeyConfigured ? 'text-green-600' : 'text-yellow-600'}`}>
              配置状态: {configStatus}
            </p>
            <p className={`text-sm ${isApiKeyConfigured ? 'text-green-700' : 'text-yellow-700'}`}>
              当前API Key: <code className="bg-gray-100 px-1 rounded">{API_KEYS.DOUBAO.substring(0, 20)}...</code>
            </p>
            <p className={`text-sm ${isApiKeyConfigured ? 'text-green-700' : 'text-yellow-700'}`}>
              使用模型: <code className="bg-gray-100 px-1 rounded">doubao-seed-1-6-250615</code>
            </p>
            {!isApiKeyConfigured && (
              <p className="text-yellow-700 text-sm mt-1">
                请先配置正确的豆包AI API Key，访问火山引擎控制台获取
              </p>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              测试文本：
            </label>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="输入要测试的风口文本..."
              className="min-h-[200px] font-mono text-sm"
            />
          </div>

          <Button
            onClick={handleTest}
            disabled={isLoading || !inputText.trim()}
            className="w-full"
          >
            {isLoading ? '🔄 测试中...' : '🚀 开始测试豆包AI'}
          </Button>

          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg space-y-3">
              <p className="text-red-600 font-medium">❌ 错误：</p>
              <p className="text-red-700 whitespace-pre-line">{error}</p>

              {(error.includes('InvalidEndpointOrModel.NotFound') || error.includes('认证失败')) && (
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                  <p className="text-blue-600 font-medium">💡 配置指南：</p>
                  <ol className="text-blue-700 text-sm mt-2 space-y-1 list-decimal list-inside">
                    <li>访问 <a href="https://console.volcengine.com/ark" target="_blank" className="underline">火山引擎控制台</a></li>
                    <li>在"API管理"页面创建API Key</li>
                    <li>确保API Key以"sk-"开头</li>
                    <li>确保已开通豆包大模型服务</li>
                    <li>将API Key配置到项目中</li>
                  </ol>
                  <p className="text-blue-600 text-sm mt-2">
                    参考文档：<a href="https://www.volcengine.com/docs/82379/1541594" target="_blank" className="underline">火山引擎API配置指南</a>
                  </p>
                </div>
              )}
            </div>
          )}

          {result && (
            <div className="space-y-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-600 font-medium">✅ 测试成功！</p>
                <p className="text-green-700">
                  提供商: {result.provider} | 
                  模型: {result.modelUsed} | 
                  置信度: {result.confidence}
                </p>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>📊 识别结果</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </CardContent>
              </Card>

              {result.projects && result.projects.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>🏗️ 项目详情</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {result.projects.map((project: any, projectIndex: number) => (
                      <div key={projectIndex} className="space-y-3">
                        <div className="border-b pb-2">
                          <h3 className="font-semibold">项目: {project.projectName}</h3>
                          <p className="text-gray-600">客户: {project.clientInfo}</p>
                        </div>
                        
                        {project.floors?.map((floor: any, floorIndex: number) => (
                          <div key={floorIndex} className="ml-4 space-y-2">
                            <h4 className="font-medium text-blue-600">🏢 {floor.floorName}</h4>
                            
                            {floor.rooms?.map((room: any, roomIndex: number) => (
                              <div key={roomIndex} className="ml-4 space-y-1">
                                <h5 className="font-medium text-purple-600">🏠 {room.roomName}</h5>
                                
                                {room.vents?.map((vent: any, ventIndex: number) => (
                                  <div key={ventIndex} className="ml-4 p-2 bg-gray-50 rounded text-sm">
                                    <div className="flex items-center gap-2">
                                      <span className="font-medium">
                                        {vent.systemType === 'double_white_outlet' ? '📤' : '📥'}
                                      </span>
                                      <span>{vent.originalType}</span>
                                    </div>
                                    <div className="text-gray-600">
                                      尺寸: {vent.dimensions?.length}×{vent.dimensions?.width}{vent.dimensions?.unit} | 
                                      数量: {vent.quantity} | 
                                      备注: {vent.notes || '无'}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
