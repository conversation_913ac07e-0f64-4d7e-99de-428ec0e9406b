'use client'

import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button  } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Download, FileSpreadsheet, Sparkles } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"

export default function ExportDemoPage() {
  const [isExporting, setIsExporting] = useState(false)

  // 模拟订单数据
  const mockOrder = {
    id: 'demo-order-001',
    orderNumber: 'FKY-2024-001',
    clientName: '张三建筑公司',
    clientPhone: '13800138000',
    projectAddress: '北京市朝阳区某某大厦A座',
    totalAmount: 15680.50,
    status: 'production',
    createdAt: new Date(),
    createdBy: 'admin',
    createdByName: '管理员',
    notes: '客户要求加急处理，质量要求较高',
    items: [
      {
        productName: '普通出风口',
        specifications: '2000×300mm',
        floor: '1楼',
        quantity: 1,
        unitPrice: 150,
        totalPrice: 150,
        notes: '无'
      },
      {
        productName: '普通出风口',
        specifications: '2000×150mm',
        floor: '1楼',
        quantity: 1,
        unitPrice: 150,
        totalPrice: 150,
        notes: '无'
      },
      {
        productName: '普通出风口',
        specifications: '3600×300mm',
        floor: '1楼',
        quantity: 10,
        unitPrice: 150,
        totalPrice: 1500,
        notes: '无'
      },
      {
        productName: '普通出风口',
        specifications: '3600×200mm',
        floor: '1楼',
        quantity: 20,
        unitPrice: 150,
        totalPrice: 3000,
        notes: '无'
      },
      {
        productName: '普通出风口',
        specifications: '1800×200mm',
        floor: '1楼',
        quantity: 1,
        unitPrice: 150,
        totalPrice: 150,
        notes: '无'
      }
    ]
  }

  const mockClient = {
    name: '张三建筑公司',
    phone: '13800138000',
    email: '<EMAIL>',
    company: '张三建筑有限公司',
    address: '北京市朝阳区建国路88号'
  }

  // 美化版Excel导出
  const handleBeautifulExport = async () => {
    setIsExporting(true)
    try {
      // 动态导入ExcelJS
      const ExcelJS = await import('exceljs')
      
      // 创建工作簿
      const workbook = new ExcelJS.Workbook()
      workbook.creator = '风口云平台'
      workbook.created = new Date()

      // 订单详情工作表
      const worksheet = workbook.addWorksheet('订单详情', {
        pageSetup: { paperSize: 9, orientation: 'portrait' }
      })

      // 设置主标题
      worksheet.mergeCells('A1:H1')
      const titleCell = worksheet.getCell('A1')
      titleCell.value = '订单详情报表'
      titleCell.font = { name: '微软雅黑', size: 20, bold: true, color: { argb: 'FF1F4E79' } }
      titleCell.alignment = { horizontal: 'center', vertical: 'middle' }
      titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF2F2F2' } }
      worksheet.getRow(1).height = 35

      // 订单基本信息
      worksheet.mergeCells('A3:H3')
      const infoTitleCell = worksheet.getCell('A3')
      infoTitleCell.value = '订单基本信息'
      infoTitleCell.font = { name: '微软雅黑', size: 14, bold: true, color: { argb: 'FF1F4E79' } }
      infoTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } }
      worksheet.getRow(3).height = 25

      const orderInfo = [
        ['订单号', mockOrder.orderNumber, '项目地址', mockOrder.projectAddress],
        ['订单金额', `¥${mockOrder.totalAmount.toLocaleString()}`, '订单状态', '生产中'],
        ['录单员', mockOrder.createdByName, '创建时间', mockOrder.createdAt.toLocaleString('zh-CN')]
      ]

      orderInfo.forEach((info, index) => {
        const row = worksheet.getRow(index + 4)
        info.forEach((value, cellIndex) => {
          const cell = row.getCell(cellIndex * 2 + 1)
          if (cellIndex % 2 === 0) {
            cell.value = value
            cell.font = { bold: true, color: { argb: 'FF1F4E79' } }
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
          } else {
            cell.value = value
            cell.font = { color: { argb: 'FF333333' } }
          }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
        })
      })

      // 客户信息
      worksheet.mergeCells('A8:H8')
      const clientTitleCell = worksheet.getCell('A8')
      clientTitleCell.value = '客户信息'
      clientTitleCell.font = { name: '微软雅黑', size: 14, bold: true, color: { argb: 'FF1F4E79' } }
      clientTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } }
      worksheet.getRow(8).height = 25

      const clientInfo = [
        ['客户名称', mockClient.name, '联系电话', mockClient.phone],
        ['邮箱', mockClient.email, '公司名称', mockClient.company],
        ['地址', mockClient.address, '', '']
      ]

      clientInfo.forEach((info, index) => {
        const row = worksheet.getRow(index + 9)
        info.forEach((value, cellIndex) => {
          const cell = row.getCell(cellIndex * 2 + 1)
          if (cellIndex % 2 === 0) {
            cell.value = value
            cell.font = { bold: true, color: { argb: 'FF1F4E79' } }
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
          } else {
            cell.value = value
            cell.font = { color: { argb: 'FF333333' } }
          }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
        })
      })

      // 订单明细
      worksheet.mergeCells('A13:H13')
      const detailTitleCell = worksheet.getCell('A13')
      detailTitleCell.value = '订单明细'
      detailTitleCell.font = { name: '微软雅黑', size: 14, bold: true, color: { argb: 'FF1F4E79' } }
      detailTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } }
      worksheet.getRow(13).height = 25

      // 明细表头
      const headerRow = worksheet.getRow(14)
      const headers = ['序号', '产品名称', '规格', '楼层', '数量', '单价(元)', '小计(元)', '备注']
      headers.forEach((header, index) => {
        const cell = headerRow.getCell(index + 1)
        cell.value = header
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } }
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
      headerRow.height = 25

      // 明细数据
      mockOrder.items.forEach((item, index) => {
        const row = worksheet.getRow(15 + index)
        const rowData = [
          index + 1,
          item.productName,
          item.specifications,
          item.floor,
          item.quantity,
          item.unitPrice,
          item.totalPrice,
          item.notes
        ]

        rowData.forEach((data, cellIndex) => {
          const cell = row.getCell(cellIndex + 1)
          cell.value = data
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
          
          if (cellIndex === 5 || cellIndex === 6) {
            cell.numFmt = '#,##0.00'
          }
          
          if (cellIndex === 0 || cellIndex === 4) {
            cell.alignment = { horizontal: 'center' }
          }
        })

        if (index % 2 === 1) {
          row.eachCell((cell) => {
            if (!cell.fill || !cell.fill.fgColor) {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } } as unknown
            }
          })
        }
      })

      // 总计行
      const totalRow = worksheet.getRow(15 + mockOrder.items.length)
      totalRow.getCell(6).value = '总计:'
      totalRow.getCell(6).font = { bold: true, color: { argb: 'FF1F4E79' } }
      totalRow.getCell(6).alignment = { horizontal: 'right' }
      totalRow.getCell(7).value = mockOrder.totalAmount
      totalRow.getCell(7).font = { bold: true, color: { argb: 'FF1F4E79' } }
      totalRow.getCell(7).numFmt = '#,##0.00'
      totalRow.getCell(7).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFEAA7' } }

      // 设置列宽
      worksheet.columns = [
        { width: 8 },  // 序号
        { width: 20 }, // 产品名称
        { width: 25 }, // 规格
        { width: 12 }, // 楼层
        { width: 10 }, // 数量
        { width: 12 }, // 单价
        { width: 12 }, // 小计
        { width: 20 }  // 备注
      ]

      // 导出Excel文件
      const fileName = `美化订单详情_${mockOrder.orderNumber}_${new Date().toISOString().split('T')[0]}.xlsx`
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.click()
      window.URL.revokeObjectURL(url)

      console.log('✅ 美化版订单导出成功')
    } catch (error) {
      console.error('❌ 导出失败:', error)
      alert('导出失败，请重试')
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Excel导出美化演示</h1>
            <p className="text-muted-foreground">对比美化前后的Excel导出效果</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 美化前 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileSpreadsheet className="h-5 w-5" />
                美化前的导出效果
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">特点：</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 纯文本格式，无样式</li>
                    <li>• 黑白表格，视觉单调</li>
                    <li>• 无颜色区分，难以阅读</li>
                    <li>• 列宽固定，内容可能被截断</li>
                    <li>• 无格式化，数字显示不美观</li>
                  </ul>
                </div>
                <div className="text-center">
                  <Badge variant="secondary">传统导出方式</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 美化后 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-yellow-500" />
                美化后的导出效果
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">特点：</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 🎨 专业配色方案，视觉美观</li>
                    <li>• 📊 表头突出显示，层次分明</li>
                    <li>• 🌈 状态颜色区分，一目了然</li>
                    <li>• 📏 自适应列宽，内容完整显示</li>
                    <li>• 💰 金额格式化，千分位分隔</li>
                    <li>• 🔄 交替行颜色，提升可读性</li>
                    <li>• 🏢 企业级样式，专业印象</li>
                  </ul>
                </div>
                <div className="text-center">
                  <Button 
                    onClick={handleBeautifulExport}
                    disabled={isExporting}
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    {isExporting ? '导出中...' : '体验美化导出'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 功能对比 */}
        <Card>
          <CardHeader>
            <CardTitle>功能对比详情</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-2 text-left">功能特性</th>
                    <th className="border border-gray-300 px-4 py-2 text-center">美化前</th>
                    <th className="border border-gray-300 px-4 py-2 text-center">美化后</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2 font-medium">标题样式</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">❌ 无样式</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">✅ 大标题+配色</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="border border-gray-300 px-4 py-2 font-medium">表头设计</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">❌ 普通文本</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">✅ 蓝色背景+白字</td>
                  </tr>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2 font-medium">数据格式化</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">❌ 原始数字</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">✅ 千分位+货币符号</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="border border-gray-300 px-4 py-2 font-medium">行颜色</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">❌ 单一白色</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">✅ 交替灰白色</td>
                  </tr>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2 font-medium">状态显示</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">❌ 纯文本</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">✅ 颜色区分状态</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="border border-gray-300 px-4 py-2 font-medium">列宽调整</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">❌ 固定宽度</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">✅ 自适应内容</td>
                  </tr>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2 font-medium">总计突出</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">❌ 普通显示</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">✅ 黄色背景突出</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>使用说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">如何使用美化导出：</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
                  <li>进入订单管理页面</li>
                  <li>点击"导出订单"按钮</li>
                  <li>系统自动生成美化的Excel文件</li>
                  <li>文件包含完整的样式和格式</li>
                </ol>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">适用场景：</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                  <li>向客户提供正式报表</li>
                  <li>内部会议展示数据</li>
                  <li>财务对账和审核</li>
                  <li>存档和打印需求</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
