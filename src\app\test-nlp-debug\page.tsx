'use client'

import { useState } from 'react'

export default function TestNLPDebugPage() {
  const [inputText, setInputText] = useState('')
  const [debugResult, setDebugResult] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  // 示例文本
  const exampleTexts = [
    {
      name: '您的手写清单',
      text: `1. 140x240 300x1600
2. 140x3480 300x1600
3. 140x3510 300x1600
4. 140x3460 300x1600
5. 150x2460 300x1600`
    },
    {
      name: '复杂项目清单',
      text: `粤桂苑A栋 3楼
出风口 1300x300 5个
回风口 1000x300 3个
线型风口 1920x140 2个`
    },
    {
      name: '简单描述',
      text: `书香华府1201
出风口1920X140，数量2
回风口1000X300，数量1`
    }
  ]

  const processSteps = [
    { name: '文本预处理', icon: '🔧', description: '清理和标准化输入文本' },
    { name: '排版识别', icon: '📐', description: '识别师傅手写习惯和排版' },
    { name: '意图分类', icon: '🧠', description: '识别用户意图类型' },
    { name: '智能实体提取', icon: '📊', description: '基于排版提取关键信息' },
    { name: '风口识别', icon: '🔍', description: '识别风口项目详情' },
    { name: '置信度计算', icon: '📈', description: '评估识别准确性' },
    { name: '订单生成', icon: '📋', description: '生成最终订单数据' }
  ]

  const handleDebugProcess = async () => {
    if (!inputText.trim()) {
      alert('请输入文本内容')
      return
    }

    setIsProcessing(true)
    setDebugResult(null)
    setCurrentStep(0)

    try {
      // 模拟步骤处理过程
      for (let i = 0; i < processSteps.length; i++) {
        setCurrentStep(i)
        await new Promise(resolve => setTimeout(resolve, 800)) // 模拟处理时间
      }

      const response = await fetch('/api/nlp/debug', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: inputText,
          enableDebug: true
        })
      })

      const data = await response.json()
      setDebugResult(data)
    } catch (error) {
      setDebugResult({
        success: false,
        error: '处理失败: ' + (error instanceof Error ? error.message : '未知错误')
      })
    } finally {
      setIsProcessing(false)
      setCurrentStep(0)
    }
  }

  const loadExample = (text: string) => {
    setInputText(text)
    setDebugResult(null)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🧠 NLP意图识别调试界面
          </h1>

          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* 输入区域 */}
            <div className="xl:col-span-1">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                📝 输入文本
              </h2>
              
              {/* 示例按钮 */}
              <div className="mb-4">
                <div className="text-sm text-gray-600 mb-2">快速加载示例:</div>
                <div className="space-y-2">
                  {exampleTexts.map((example, index) => (
                    <button
                      key={index}
                      onClick={() => loadExample(example.text)}
                      className="w-full px-3 py-2 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200 transition-colors text-left"
                    >
                      {example.name}
                    </button>
                  ))}
                </div>
              </div>

              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="请输入包含风口信息的文本..."
                className="w-full h-48 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />

              <button
                onClick={handleDebugProcess}
                disabled={isProcessing || !inputText.trim()}
                className="w-full mt-4 bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 px-6 rounded-lg hover:from-purple-600 hover:to-pink-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? '🔄 正在分析处理...' : '🧠 开始NLP调试分析'}
              </button>
            </div>

            {/* 处理步骤可视化 */}
            <div className="xl:col-span-1">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                ⚡ 处理步骤
              </h2>
              
              <div className="space-y-3">
                {processSteps.map((step, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                      isProcessing && currentStep === index
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : isProcessing && currentStep > index
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center mb-2">
                      <span className="text-2xl mr-3">{step.icon}</span>
                      <div>
                        <div className="font-medium text-gray-800">{step.name}</div>
                        <div className="text-sm text-gray-600">{step.description}</div>
                      </div>
                      {isProcessing && currentStep === index && (
                        <div className="ml-auto">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        </div>
                      )}
                      {isProcessing && currentStep > index && (
                        <div className="ml-auto text-green-500">
                          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 调试结果 */}
            <div className="xl:col-span-1">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                🔍 调试结果
              </h2>

              {!debugResult ? (
                <div className="bg-gray-100 rounded-lg p-8 text-center text-gray-500">
                  {isProcessing ? '正在处理中...' : '请输入文本并开始分析'}
                </div>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {debugResult.success ? (
                    <>
                      {/* 文本预处理结果 */}
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h4 className="font-medium text-blue-800 mb-2">🔧 文本预处理</h4>
                        <div className="text-sm space-y-2">
                          <div>
                            <span className="text-gray-600">原始文本长度:</span>
                            <span className="ml-2 font-mono">{debugResult.debug?.originalLength || 0}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">处理后长度:</span>
                            <span className="ml-2 font-mono">{debugResult.debug?.processedLength || 0}</span>
                          </div>
                          <div className="bg-white rounded p-2 text-xs font-mono">
                            {debugResult.debug?.processedText?.substring(0, 100)}...
                          </div>
                        </div>
                      </div>

                      {/* 意图分类结果 */}
                      <div className="bg-green-50 rounded-lg p-4">
                        <h4 className="font-medium text-green-800 mb-2">🧠 意图分类</h4>
                        <div className="text-sm space-y-2">
                          <div>
                            <span className="text-gray-600">识别意图:</span>
                            <span className="ml-2 font-semibold text-green-700">
                              {debugResult.intentResult?.intent || 'unknown'}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">置信度:</span>
                            <span className="ml-2 font-semibold text-green-700">
                              {((debugResult.intentResult?.confidence || 0) * 100).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* 排版识别结果 */}
                      <div className="bg-indigo-50 rounded-lg p-4">
                        <h4 className="font-medium text-indigo-800 mb-2">📐 排版识别</h4>
                        <div className="text-sm space-y-2">
                          <div>
                            <span className="text-gray-600">排版类型:</span>
                            <span className="ml-2 font-semibold text-indigo-700">
                              {debugResult.intentResult?.layout?.layoutType === 'column_based' ? '分列排版(师傅分两列)' :
                               debugResult.intentResult?.layout?.layoutType === 'sequence_based' ? '序号排版(师傅按顺序)' :
                               debugResult.intentResult?.layout?.layoutType === 'table_based' ? '表格排版' :
                               debugResult.intentResult?.layout?.layoutType === 'mixed' ? '混合排版' : '未知排版'}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">排版置信度:</span>
                            <span className="ml-2 font-semibold text-indigo-700">
                              {((debugResult.intentResult?.layout?.confidence || 0) * 100).toFixed(1)}%
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">风口分组:</span>
                            <span className="ml-2">{debugResult.intentResult?.layout?.ventGroups?.length || 0}组</span>
                          </div>
                        </div>
                      </div>

                      {/* 实体提取结果 */}
                      <div className="bg-purple-50 rounded-lg p-4">
                        <h4 className="font-medium text-purple-800 mb-2">📊 智能实体提取</h4>
                        <div className="text-sm space-y-2">
                          <div>
                            <span className="text-gray-600">项目名称:</span>
                            <span className="ml-2">{debugResult.intentResult?.entities?.projectName || '未识别'}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">楼层信息:</span>
                            <span className="ml-2">{debugResult.intentResult?.entities?.floorInfo || '未识别'}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">风口项目数:</span>
                            <span className="ml-2 font-semibold text-purple-700">
                              {debugResult.intentResult?.entities?.ventItems?.length || 0}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">识别方式:</span>
                            <span className="ml-2 text-purple-600">基于排版智能提取</span>
                          </div>
                        </div>
                      </div>

                      {/* 排版分组详情 */}
                      {debugResult.intentResult?.layout?.ventGroups && (
                        <div className="bg-teal-50 rounded-lg p-4">
                          <h4 className="font-medium text-teal-800 mb-2">📦 排版分组详情</h4>
                          <div className="space-y-3 max-h-48 overflow-y-auto">
                            {debugResult.intentResult.layout.ventGroups.map((group: any, groupIndex: number) => (
                              <div key={groupIndex} className="bg-white rounded p-3">
                                <div className="flex justify-between items-center mb-2">
                                  <span className="font-medium text-teal-700">{group.title}</span>
                                  <span className="text-xs bg-teal-100 text-teal-600 px-2 py-1 rounded">
                                    {group.position} | {group.items.length}项
                                  </span>
                                </div>
                                <div className="space-y-1">
                                  {group.items.slice(0, 3).map((item: any, itemIndex: number) => (
                                    <div key={itemIndex} className="text-xs text-gray-600">
                                      {item.sequence && `${item.sequence}. `}
                                      {item.floor && `${item.floor} `}
                                      {item.dimensions}
                                      {item.quantity && ` ×${item.quantity}`}
                                    </div>
                                  ))}
                                  {group.items.length > 3 && (
                                    <div className="text-xs text-gray-500">
                                      ...还有{group.items.length - 3}个项目
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 风口识别详情 */}
                      <div className="bg-orange-50 rounded-lg p-4">
                        <h4 className="font-medium text-orange-800 mb-2">🔍 最终风口识别</h4>
                        <div className="space-y-2 max-h-48 overflow-y-auto">
                          {debugResult.intentResult?.entities?.ventItems?.map((item: any, index: number) => (
                            <div key={index} className="bg-white rounded p-2 text-xs">
                              <div className="flex justify-between items-center">
                                <span className="font-medium">{item.type}</span>
                                <span className="text-orange-600">{(item.confidence * 100).toFixed(1)}%</span>
                              </div>
                              <div className="text-gray-600">
                                {item.length}×{item.width}mm × {item.quantity}个
                              </div>
                              {item.notes && (
                                <div className="text-blue-600 text-xs">备注: {item.notes}</div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 最终订单结果 */}
                      {debugResult.orderResult && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-800 mb-2">📋 订单生成结果</h4>
                          <div className="text-sm space-y-2">
                            <div>
                              <span className="text-gray-600">总金额:</span>
                              <span className="ml-2 font-semibold text-green-600">
                                ¥{debugResult.orderResult.orderData?.totalAmount?.toFixed(1) || 0}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-600">项目数:</span>
                              <span className="ml-2">{debugResult.orderResult.orderData?.items?.length || 0}</span>
                            </div>
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="bg-red-50 rounded-lg p-4">
                      <h4 className="font-medium text-red-800 mb-2">❌ 处理失败</h4>
                      <div className="text-red-700 text-sm">{debugResult.error}</div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 学习进度和统计 */}
            {debugResult && debugResult.success && debugResult.learning && (
              <div className="mt-6 bg-white rounded-lg p-4">
                <h4 className="font-medium text-indigo-800 mb-3">📊 学习进度统计</h4>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="bg-green-50 rounded p-3 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {(debugResult.learning.progress.currentAccuracy * 100).toFixed(1)}%
                    </div>
                    <div className="text-sm text-green-700">当前准确率</div>
                  </div>
                  <div className="bg-blue-50 rounded p-3 text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {debugResult.learning.stats.totalProcessed}
                    </div>
                    <div className="text-sm text-blue-700">总处理次数</div>
                  </div>
                  <div className="bg-purple-50 rounded p-3 text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {debugResult.learning.suggestions.length}
                    </div>
                    <div className="text-sm text-purple-700">优化建议</div>
                  </div>
                  <div className="bg-orange-50 rounded p-3 text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {(debugResult.learning.progress.learningRate * 1000).toFixed(1)}
                    </div>
                    <div className="text-sm text-orange-700">学习速率‰</div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 rounded p-3">
                    <div className="text-sm font-medium text-gray-700 mb-2">🎯 下一个里程碑</div>
                    <div className="text-sm text-gray-600">{debugResult.learning.progress.nextMilestone}</div>
                  </div>
                  <div className="bg-gray-50 rounded p-3">
                    <div className="text-sm font-medium text-gray-700 mb-2">📈 预期改进</div>
                    <div className="text-sm text-gray-600">
                      准确率可提升至 {(debugResult.learning.progress.projectedImprovement * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 学习历史和优化建议 */}
          {debugResult && debugResult.success && debugResult.learning && (
            <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 最近学习历史 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  📚 最近学习历史
                </h3>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {debugResult.learning.recentHistory.map((record: any, index: number) => (
                    <div key={index} className="bg-gray-50 rounded p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div className="text-sm font-medium text-gray-800">
                          {record.processingResult.intent} - {(record.processingResult.confidence * 100).toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(record.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                      <div className="text-xs text-gray-600 mb-2">
                        识别项目: {record.processingResult.itemsFound}个 |
                        处理时间: {record.processingTime}ms
                      </div>
                      <div className="text-xs text-blue-600">
                        学习点: {record.learningPoints.length}个 |
                        优化建议: {record.optimizations.length}个
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 优化建议 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  💡 智能优化建议
                </h3>
                <div className="space-y-3">
                  {debugResult.learning.suggestions.map((suggestion: any, index: number) => (
                    <div key={index} className={`rounded p-3 ${
                      suggestion.priority === 'high' ? 'bg-red-50 border border-red-200' :
                      suggestion.priority === 'medium' ? 'bg-yellow-50 border border-yellow-200' :
                      'bg-green-50 border border-green-200'
                    }`}>
                      <div className="flex justify-between items-start mb-2">
                        <div className={`text-sm font-medium ${
                          suggestion.priority === 'high' ? 'text-red-800' :
                          suggestion.priority === 'medium' ? 'text-yellow-800' :
                          'text-green-800'
                        }`}>
                          {suggestion.description}
                        </div>
                        <span className={`px-2 py-1 rounded text-xs ${
                          suggestion.priority === 'high' ? 'bg-red-100 text-red-700' :
                          suggestion.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-green-100 text-green-700'
                        }`}>
                          {suggestion.priority}
                        </span>
                      </div>
                      <div className="text-xs text-gray-600 mb-1">
                        预期改进: {suggestion.expectedImprovement}
                      </div>
                      <div className="text-xs text-gray-500">
                        实现方式: {suggestion.implementation}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 学习和优化建议 */}
          <div className="mt-8 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-indigo-800 mb-4">
              🎓 机器学习和优化过程
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-indigo-700 mb-2">📈 学习机制</h4>
                <ul className="text-sm text-indigo-600 space-y-1">
                  <li>• 基于置信度评估识别质量</li>
                  <li>• 收集用户反馈优化算法</li>
                  <li>• 动态调整关键词权重</li>
                  <li>• 持续改进实体提取规则</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-indigo-700 mb-2">🔧 优化策略</h4>
                <ul className="text-sm text-indigo-600 space-y-1">
                  <li>• 根据错误模式调整预处理</li>
                  <li>• 优化风口类型判断逻辑</li>
                  <li>• 改进数量识别准确性</li>
                  <li>• 增强上下文理解能力</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-indigo-700 mb-2">🧠 智能特性</h4>
                <ul className="text-sm text-indigo-600 space-y-1">
                  <li>• 自适应阈值调整</li>
                  <li>• 上下文语义理解</li>
                  <li>• 多模态信息融合</li>
                  <li>• 实时性能监控</li>
                </ul>
              </div>
            </div>

            {/* 实时学习演示 */}
            {debugResult && debugResult.success && (
              <div className="mt-6 bg-white rounded-lg p-4">
                <h4 className="font-medium text-indigo-800 mb-3">🔄 本次处理的学习点</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-indigo-50 rounded p-3">
                    <div className="text-sm font-medium text-indigo-700 mb-1">识别模式学习</div>
                    <div className="text-xs text-indigo-600">
                      • 文本长度: {debugResult.debug?.preprocessing?.originalLength}字符<br/>
                      • 尺寸模式: {debugResult.debug?.entityExtraction?.ventItems?.length}个<br/>
                      • 平均置信度: {(debugResult.debug?.performance?.averageConfidence * 100 || 0).toFixed(1)}%
                    </div>
                  </div>
                  <div className="bg-purple-50 rounded p-3">
                    <div className="text-sm font-medium text-purple-700 mb-1">优化建议生成</div>
                    <div className="text-xs text-purple-600">
                      • 处理时间: {debugResult.debug?.performance?.processingTime}ms<br/>
                      • 关键词匹配: {Object.keys(debugResult.debug?.intentAnalysis?.keywordMatches || {}).length}类<br/>
                      • 类型推断: 基于宽度+关键词
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
