/**
 * 快速R1模型测试
 */

export async function quickR1Test() {
  console.log('🚀 开始快速R1模型测试...')
  
  const apiKey = 'sk-c9047196d51d4f6c99dc94f9fbef602c'
  const baseURL = 'https://api.deepseek.com/v1/chat/completions'
  
  // 非常简单的测试prompt
  const simplePrompt = '1+1等于几？'
  
  const startTime = Date.now()
  
  try {
    console.log('📡 发送简单请求到R1模型...')
    
    const response = await fetch(baseURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'deepseek-reasoner',
        messages: [
          {
            role: "user",
            content: simplePrompt
          }
        ],
        max_tokens: 100,
        stream: false
      })
    })
    
    const responseTime = Date.now() - startTime
    console.log(`⏱️ R1模型响应时间: ${responseTime}ms (${(responseTime/1000).toFixed(1)}秒)`)
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ R1模型请求失败:', response.status, errorText)
      return {
        success: false,
        responseTime,
        error: `HTTP ${response.status}: ${errorText}`
      }
    }
    
    const data = await response.json()
    console.log('✅ R1模型响应成功!')
    console.log('📦 响应数据:', {
      content: data.choices?.[0]?.message?.content,
      reasoning_content_length: data.choices?.[0]?.message?.reasoning_content?.length,
      has_reasoning: !!data.choices?.[0]?.message?.reasoning_content
    })
    
    if (data.choices?.[0]?.message?.reasoning_content) {
      console.log('🧠 推理过程 (前200字符):', 
        data.choices[0].message.reasoning_content.substring(0, 200) + '...')
    }
    
    return {
      success: true,
      responseTime,
      content: data.choices?.[0]?.message?.content,
      hasReasoning: !!data.choices?.[0]?.message?.reasoning_content
    }
    
  } catch (error: any) {
    const responseTime = Date.now() - startTime
    console.error('❌ R1模型测试失败:', error.message)
    
    return {
      success: false,
      responseTime,
      error: error.message
    }
  }
}

// 测试网络延迟
export async function testNetworkLatency() {
  console.log('🌐 测试网络延迟...')
  
  const startTime = Date.now()
  
  try {
    // 简单的HEAD请求测试延迟
    const response = await fetch('https://api.deepseek.com', {
      method: 'HEAD'
    })
    
    const latency = Date.now() - startTime
    console.log(`📡 网络延迟: ${latency}ms`)
    
    return {
      success: response.ok,
      latency
    }
  } catch (error) {
    const latency = Date.now() - startTime
    console.error('❌ 网络测试失败:', error)
    
    return {
      success: false,
      latency,
      error: error.message
    }
  }
}

// 在浏览器控制台中可以调用
if (typeof window !== 'undefined') {
  (window as any).quickR1Test = quickR1Test
  (window as any).testNetworkLatency = testNetworkLatency
}
