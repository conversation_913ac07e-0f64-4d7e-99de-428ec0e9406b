/**
 * 🇨🇳 风口云平台 - 员工管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 获取员工列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')

    if (!factoryId) {
      return NextResponse.json(
        { error: '工厂ID不能为空' },
        { status: 400 }
      )
    }

    // 获取工厂员工列表
    const employees = await db.getEmployeesByFactoryId(factoryId)

    return NextResponse.json({
      success: true,
      employees
    })

  } catch (error) {
    console.error('❌ 获取员工列表失败:', error)
    return NextResponse.json(
      { error: '获取员工列表失败' },
      { status: 500 }
    )
  }
}

// 创建员工
export async function POST(request: NextRequest) {
  try {
    const employeeData = await request.json()

    if (!employeeData.factoryId || !employeeData.username || !employeeData.name) {
      return NextResponse.json(
        { error: '工厂ID、用户名和姓名不能为空' },
        { status: 400 }
      )
    }

    // 创建员工
    const employee = await db.createEmployee(employeeData)

    if (!employee) {
      return NextResponse.json(
        { error: '创建员工失败' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      employee
    })

  } catch (error) {
    console.error('❌ 创建员工失败:', error)
    return NextResponse.json(
      { error: '创建员工失败' },
      { status: 500 }
    )
  }
}

// 更新员工信息
export async function PUT(request: NextRequest) {
  try {
    const { id, ...updates } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: '员工ID不能为空' },
        { status: 400 }
      )
    }

    // 更新员工信息
    const employee = await db.updateEmployee(id, updates)

    if (!employee) {
      return NextResponse.json(
        { error: '更新员工信息失败' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      employee
    })

  } catch (error) {
    console.error('❌ 更新员工信息失败:', error)
    return NextResponse.json(
      { error: `更新员工信息失败: ${error.message || error}` },
      { status: 500 }
    )
  }
}

// 删除员工
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const employeeId = searchParams.get('id')

    if (!employeeId) {
      return NextResponse.json(
        { error: '员工ID不能为空' },
        { status: 400 }
      )
    }

    // 删除员工
    const success = await db.deleteEmployee(employeeId)

    if (!success) {
      return NextResponse.json(
        { error: '删除员工失败' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true
    })

  } catch (error) {
    console.error('❌ 删除员工失败:', error)
    return NextResponse.json(
      { error: '删除员工失败' },
      { status: 500 }
    )
  }
}
