'use client'

import React, { useRef, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Upload, File, X, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface UploadButtonProps {
  onFileSelect?: (files: File[]) => void
  onUploadComplete?: (results: any[]) => void
  onUploadError?: (error: string) => void
  accept?: string
  multiple?: boolean
  maxSize?: number // MB
  maxFiles?: number
  disabled?: boolean
  className?: string
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  showProgress?: boolean
  uploadUrl?: string
  children?: React.ReactNode
}

interface UploadedFile {
  file: File
  id: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
  result?: any
}

export function UploadButton({
  onFileSelect,
  onUploadComplete,
  onUploadError,
  accept = '*/*',
  multiple = false,
  maxSize = 10, // 10MB default
  maxFiles = 5,
  disabled = false,
  className,
  variant = 'default',
  size = 'default',
  showProgress = true,
  uploadUrl,
  children
}: UploadButtonProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [isUploading, setIsUploading] = useState(false)

  // 生成唯一ID
  const generateId = () => Math.random().toString(36).substr(2, 9)

  // 验证文件
  const validateFile = (file: File): string | null => {
    // 检查文件大小
    if (file.size > maxSize * 1024 * 1024) {
      return `文件大小不能超过 ${maxSize}MB`
    }

    // 检查文件类型（如果指定了accept）
    if (accept !== '*/*') {
      const acceptedTypes = accept.split(',').map(type => type.trim())
      const fileType = file.type
      const fileName = file.name.toLowerCase()
      
      const isAccepted = acceptedTypes.some(acceptType => {
        if (acceptType.startsWith('.')) {
          return fileName.endsWith(acceptType.toLowerCase())
        }
        if (acceptType.includes('*')) {
          const baseType = acceptType.split('/')[0]
          return fileType.startsWith(baseType)
        }
        return fileType === acceptType
      })

      if (!isAccepted) {
        return `不支持的文件类型，仅支持: ${accept}`
      }
    }

    return null
  }

  // 处理文件选择
  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return

    const fileArray = Array.from(files)
    const newFiles: UploadedFile[] = []

    // 检查文件数量限制
    if (uploadedFiles.length + fileArray.length > maxFiles) {
      onUploadError?.(`最多只能上传 ${maxFiles} 个文件`)
      return
    }

    for (const file of fileArray) {
      // 验证文件
      const error = validateFile(file)
      if (error) {
        onUploadError?.(error)
        continue
      }

      // 检查是否已存在相同文件
      const isDuplicate = uploadedFiles.some(uf => 
        uf.file.name === file.name && uf.file.size === file.size
      )
      if (isDuplicate) {
        onUploadError?.(`文件 ${file.name} 已存在`)
        continue
      }

      newFiles.push({
        file,
        id: generateId(),
        status: 'pending',
        progress: 0
      })
    }

    if (newFiles.length > 0) {
      setUploadedFiles(prev => [...prev, ...newFiles])
      onFileSelect?.(newFiles.map(f => f.file))

      // 如果提供了上传URL，自动开始上传
      if (uploadUrl) {
        uploadFiles(newFiles)
      }
    }
  }

  // 上传文件
  const uploadFiles = async (filesToUpload: UploadedFile[]) => {
    if (!uploadUrl) return

    setIsUploading(true)
    const results: any[] = []

    try {
      for (const fileItem of filesToUpload) {
        // 更新状态为上传中
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { ...f, status: 'uploading', progress: 0 } : f
        ))

        try {
          const formData = new FormData()
          formData.append('file', fileItem.file)

          const response = await fetch(uploadUrl, {
            method: 'POST',
            body: formData,
          })

          if (!response.ok) {
            throw new Error(`上传失败: ${response.status}`)
          }

          const result = await response.json()
          results.push(result)

          // 更新状态为成功
          setUploadedFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { 
              ...f, 
              status: 'success', 
              progress: 100,
              result 
            } : f
          ))

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '上传失败'
          
          // 更新状态为失败
          setUploadedFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { 
              ...f, 
              status: 'error', 
              error: errorMessage 
            } : f
          ))
        }
      }

      onUploadComplete?.(results)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败'
      onUploadError?.(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }

  // 移除文件
  const removeFile = (id: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== id))
  }

  // 清空所有文件
  const clearFiles = () => {
    setUploadedFiles([])
  }

  // 点击上传按钮
  const handleClick = () => {
    fileInputRef.current?.click()
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <File className="h-4 w-4 text-gray-400" />
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-4">
      {/* 上传按钮 */}
      <Button
        onClick={handleClick}
        disabled={disabled || isUploading}
        variant={variant}
        size={size}
        className={cn(className)}
      >
        {isUploading ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            上传中...
          </>
        ) : (
          <>
            <Upload className="h-4 w-4 mr-2" />
            {children || '选择文件'}
          </>
        )}
      </Button>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
      />

      {/* 文件列表 */}
      {showProgress && uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">
              已选择文件 ({uploadedFiles.length}/{maxFiles})
            </h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFiles}
              className="h-6 text-xs"
            >
              清空
            </Button>
          </div>
          
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {uploadedFiles.map((fileItem) => (
              <div
                key={fileItem.id}
                className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg"
              >
                {getStatusIcon(fileItem.status)}
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {fileItem.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(fileItem.file.size)}
                  </p>
                  {fileItem.error && (
                    <p className="text-xs text-red-500 mt-1">{fileItem.error}</p>
                  )}
                </div>

                {/* 进度条 */}
                {fileItem.status === 'uploading' && (
                  <div className="w-20">
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${fileItem.progress}%` }}
                      />
                    </div>
                  </div>
                )}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(fileItem.id)}
                  className="h-6 w-6 p-0 text-gray-400 hover:text-red-500"
                  disabled={fileItem.status === 'uploading'}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
