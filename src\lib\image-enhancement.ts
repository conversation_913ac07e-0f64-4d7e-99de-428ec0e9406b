/**
 * 🖼️ 图像增强处理库
 * 专门针对备注和楼层信息识别的图像预处理
 */

import sharp from 'sharp'

export interface ImageEnhancementOptions {
  enhanceText: boolean
  enhanceLayout: boolean
  denoiseLevel: 'low' | 'medium' | 'high'
  contrastBoost: number
  deskew: boolean
}

export class ImageEnhancer {
  /**
   * 智能图像增强 - 针对备注和楼层信息优化
   */
  static async enhanceForTextRecognition(
    file: File, 
    options: Partial<ImageEnhancementOptions> = {}
  ): Promise<string> {
    const defaultOptions: ImageEnhancementOptions = {
      enhanceText: true,
      enhanceLayout: true,
      denoiseLevel: 'medium',
      contrastBoost: 1.2,
      deskew: true
    }

    const config = { ...defaultOptions, ...options }
    
    try {
      console.log('🔧 开始智能图像增强...')
      const buffer = await file.arrayBuffer()
      let processor = sharp(Buffer.from(buffer))

      // 1. 基础预处理
      processor = processor
        .resize({ width: 2000, height: 2000, fit: 'inside', withoutEnlargement: true })
        .grayscale()

      // 2. 去噪处理
      if (config.denoiseLevel !== 'low') {
        const blurAmount = config.denoiseLevel === 'high' ? 0.8 : 0.5
        processor = processor.blur(blurAmount)
      }

      // 3. 对比度增强 - 关键优化点
      processor = processor
        .normalize()
        .linear(config.contrastBoost, -(128 * config.contrastBoost) + 128)

      // 4. 文本增强处理
      if (config.enhanceText) {
        processor = processor
          .sharpen({ sigma: 1, m1: 0.5, m2: 2 })
          .threshold(120) // 二值化阈值优化
      }

      // 5. 形态学处理 - 连接断开的文字
      processor = processor
        .convolve({
          width: 3,
          height: 3,
          kernel: [-1, -1, -1, -1, 8, -1, -1, -1, -1]
        })

      const enhanced = await processor.toBuffer()
      const base64 = enhanced.toString('base64')
      
      console.log('✅ 图像增强完成')
      return base64

    } catch (error) {
      console.warn('⚠️ 图像增强失败，使用原图:', error)
      return await this.fileToBase64(file)
    }
  }

  /**
   * 专门针对表格和排版的增强
   */
  static async enhanceForLayoutRecognition(file: File): Promise<string> {
    try {
      console.log('📐 开始排版识别增强...')
      const buffer = await file.arrayBuffer()
      
      const enhanced = await sharp(Buffer.from(buffer))
        .resize({ width: 1800, fit: 'inside' })
        .grayscale()
        .normalize()
        // 增强线条和边框
        .convolve({
          width: 3,
          height: 3,
          kernel: [0, -1, 0, -1, 5, -1, 0, -1, 0]
        })
        .threshold(130)
        .toBuffer()

      return enhanced.toString('base64')
    } catch (error) {
      console.warn('⚠️ 排版增强失败:', error)
      return await this.fileToBase64(file)
    }
  }

  /**
   * 多尺度增强 - 同时处理大文字和小备注
   */
  static async multiScaleEnhancement(file: File): Promise<{
    original: string
    enhanced: string
    layout: string
  }> {
    const [original, enhanced, layout] = await Promise.all([
      this.fileToBase64(file),
      this.enhanceForTextRecognition(file, { contrastBoost: 1.3 }),
      this.enhanceForLayoutRecognition(file)
    ])

    return { original, enhanced, layout }
  }

  private static async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const base64 = (reader.result as string).split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }
}
