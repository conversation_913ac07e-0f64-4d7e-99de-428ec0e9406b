/**
 * 调试数据库连接
 */

import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  console.log('🔍 调试数据库连接API被调用')
  
  try {
    // 检查环境
    const isServer = typeof window === 'undefined'
    console.log('📍 环境检查:', { isServer, nodeEnv: process.env.NODE_ENV })
    
    // 尝试导入数据库服务
    console.log('📦 尝试导入数据库服务...')
    const { db } = await import('@/lib/database')
    console.log('✅ 数据库服务导入成功')
    
    // 检查数据库服务的类型
    console.log('🔍 数据库服务类型:', typeof db)
    console.log('🔍 数据库服务方法:', Object.keys(db))
    
    // 尝试调用一个简单的方法
    if (typeof db.authenticateFactoryUser === 'function') {
      console.log('✅ authenticateFactoryUser 方法存在')

      // 尝试直接导入Prisma客户端
      console.log('📦 尝试导入Prisma客户端...')
      const { PrismaClient } = await import('@prisma/client')
      const prisma = new PrismaClient()

      console.log('🔍 测试数据库连接...')
      await prisma.$connect()
      console.log('✅ Prisma数据库连接成功')

      // 测试查询
      const factoryCount = await prisma.factory.count()
      console.log('📊 工厂数量:', factoryCount)

      await prisma.$disconnect()

    } else {
      console.log('❌ authenticateFactoryUser 方法不存在')
    }
    
    return NextResponse.json({
      success: true,
      environment: {
        isServer,
        nodeEnv: process.env.NODE_ENV
      },
      dbService: {
        type: typeof db,
        hasAuthMethod: typeof db.authenticateFactoryUser === 'function',
        methods: Object.keys(db)
      }
    })
    
  } catch (error) {
    console.error('❌ 调试数据库连接失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      stack: error instanceof Error ? error.stack : '无堆栈信息'
    }, { status: 500 })
  }
}
