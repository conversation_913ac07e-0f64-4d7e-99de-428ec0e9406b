"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle  } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import {
  <PERSON><PERSON><PERSON>,
  Bar<PERSON>hart3,
  Users,
  DollarSign,
  ArrowLeft,
  Download
} from "lucide-react"
import Link from "next/link"
import { Shareholder, ShareholderStats, ShareholderType } from "@/types"

// 模拟数据
const mockShareholders: Shareholder[] = [
  {
    id: "sh1",
    factoryId: "factory1",
    name: "张三",
    shareholderType: "FOUNDER",
    shareCount: 5000,
    sharePercentage: 50.0,
    investmentAmount: 500000,
    joinDate: new Date("2020-01-01"),
    status: "active",
    createdBy: "admin",
    createdAt: new Date("2020-01-01"),
    updatedAt: new Date()
  },
  {
    id: "sh2",
    factoryId: "factory1",
    name: "李四",
    shareholderType: "INVESTOR",
    shareCount: 3000,
    sharePercentage: 30.0,
    investmentAmount: 300000,
    joinDate: new Date("2020-06-01"),
    status: "active",
    createdBy: "admin",
    createdAt: new Date("2020-06-01"),
    updatedAt: new Date()
  },
  {
    id: "sh3",
    factoryId: "factory1",
    name: "王五",
    shareholderType: "EMPLOYEE",
    shareCount: 2000,
    sharePercentage: 20.0,
    investmentAmount: 200000,
    joinDate: new Date("2021-01-01"),
    status: "active",
    createdBy: "admin",
    createdAt: new Date("2021-01-01"),
    updatedAt: new Date()
  }
]

export default function ShareholderStructurePage() {
  const [shareholders, setShareholders] = useState<Shareholder[]>(mockShareholders)

  // 计算统计数据
  const totalShares = shareholders.reduce((sum, s) => sum + s.shareCount, 0)
  const totalInvestment = shareholders.reduce((sum, s) => sum + s.investmentAmount, 0)

  // 按类型分组
  const shareholdersByType = shareholders.reduce((acc, shareholder) => {
    const type = shareholder.shareholderType
    if (!acc[type]) {
      acc[type] = []
    }
    acc[type].push(shareholder)
    return acc
  }, {} as Record<ShareholderType, Shareholder[]>)

  // 获取股东类型显示文本
  const getShareholderTypeText = (type: ShareholderType): string => {
    const typeMap = {
      FOUNDER: "创始股东",
      INVESTOR: "投资股东", 
      EMPLOYEE: "员工股东",
      PARTNER: "合伙人股东"
    }
    return typeMap[type]
  }

  // 获取股东类型颜色
  const getShareholderTypeColor = (type: ShareholderType): string => {
    const colorMap = {
      FOUNDER: "bg-purple-500",
      INVESTOR: "bg-blue-500",
      EMPLOYEE: "bg-green-500",
      PARTNER: "bg-orange-500"
    }
    return colorMap[type]
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/factory/shareholders">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回股东管理
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">股权架构</h1>
              <p className="text-gray-600">查看工厂股权分布和股东架构</p>
            </div>
          </div>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            导出架构图
          </Button>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">股东总数</p>
                  <p className="text-2xl font-bold text-gray-900">{shareholders.length}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总股本</p>
                  <p className="text-2xl font-bold text-green-600">{totalShares.toLocaleString()}</p>
                </div>
                <PieChart className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总投资额</p>
                  <p className="text-2xl font-bold text-purple-600">
                    ¥{(totalInvestment / 10000).toFixed(1)}万
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Shareholding Structure */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Pie Chart Visualization */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="h-5 w-5 mr-2" />
                股权分布图
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {shareholders.map((shareholder) => (
                  <div key={shareholder.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div 
                        className={`w-4 h-4 rounded ${getShareholderTypeColor(shareholder.shareholderType)}`}
                      />
                      <span className="font-medium">{shareholder.name}</span>
                      <span className="text-sm text-gray-500">
                        ({getShareholderTypeText(shareholder.shareholderType)})
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{shareholder.sharePercentage}%</div>
                      <div className="text-sm text-gray-500">
                        {shareholder.shareCount.toLocaleString()} 股
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Simple Progress Bars */}
              <div className="mt-6 space-y-3">
                {shareholders.map((shareholder) => (
                  <div key={shareholder.id}>
                    <div className="flex justify-between text-sm mb-1">
                      <span>{shareholder.name}</span>
                      <span>{shareholder.sharePercentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getShareholderTypeColor(shareholder.shareholderType)}`}
                        style={{ width: `${shareholder.sharePercentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Investment Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                投资分布
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {shareholders.map((shareholder) => (
                  <div key={shareholder.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div 
                        className={`w-4 h-4 rounded ${getShareholderTypeColor(shareholder.shareholderType)}`}
                      />
                      <span className="font-medium">{shareholder.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        ¥{shareholder.investmentAmount.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {((shareholder.investmentAmount / totalInvestment) * 100).toFixed(1)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Investment Progress Bars */}
              <div className="mt-6 space-y-3">
                {shareholders.map((shareholder) => {
                  const percentage = (shareholder.investmentAmount / totalInvestment) * 100
                  return (
                    <div key={shareholder.id}>
                      <div className="flex justify-between text-sm mb-1">
                        <span>{shareholder.name}</span>
                        <span>¥{shareholder.investmentAmount.toLocaleString()}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${getShareholderTypeColor(shareholder.shareholderType)}`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Shareholder Type Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              股东类型汇总
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {Object.entries(shareholdersByType).map(([type, typeHolders]) => {
                const totalTypeShares = typeHolders.reduce((sum, s) => sum + s.shareCount, 0)
                const totalTypeInvestment = typeHolders.reduce((sum, s) => sum + s.investmentAmount, 0)
                const typePercentage = (totalTypeShares / totalShares) * 100
                
                return (
                  <div key={type} className="text-center">
                    <div className={`w-16 h-16 rounded-full ${getShareholderTypeColor(type as ShareholderType)} mx-auto mb-3 flex items-center justify-center`}>
                      <Users className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {getShareholderTypeText(type as ShareholderType)}
                    </h3>
                    <div className="space-y-1 text-sm">
                      <p className="text-gray-600">{typeHolders.length} 人</p>
                      <p className="font-medium">{typePercentage.toFixed(1)}% 股权</p>
                      <p className="text-gray-600">
                        ¥{(totalTypeInvestment / 10000).toFixed(1)}万投资
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
