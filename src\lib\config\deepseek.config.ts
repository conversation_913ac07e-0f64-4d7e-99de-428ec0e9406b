/**
 * DeepSeek API 配置管理
 */

export interface DeepSeekConfig {
  apiKey: string
  baseURL: string
  model: string
  maxTokens: number
  temperature: number
  timeout: number
}

/**
 * 获取DeepSeek配置
 */
export function getDeepSeekConfig(): DeepSeekConfig {
  // 优先使用客户端环境变量，然后是服务端环境变量
  const apiKey = process.env.NEXT_PUBLIC_DEEPSEEK_API_KEY ||
                 process.env.DEEPSEEK_API_KEY ||
                 (typeof window !== 'undefined' ? localStorage.getItem('deepseek_api_key') : null)

  if (!apiKey) {
    // 如果在客户端且没有配置，提供更友好的错误信息
    if (typeof window !== 'undefined') {
      throw new Error('DeepSeek API Key未配置，请在环境变量中设置 NEXT_PUBLIC_DEEPSEEK_API_KEY 或在设置页面配置')
    } else {
      throw new Error('DeepSeek API Key未配置，请在环境变量中设置 DEEPSEEK_API_KEY')
    }
  }

  return {
    apiKey,
    baseURL: process.env.NEXT_PUBLIC_DEEPSEEK_BASE_URL ||
             process.env.DEEPSEEK_BASE_URL ||
             'https://api.deepseek.com/v1/chat/completions',
    model: process.env.NEXT_PUBLIC_DEEPSEEK_MODEL ||
           process.env.DEEPSEEK_MODEL ||
           'deepseek-chat',
    maxTokens: parseInt(process.env.DEEPSEEK_MAX_TOKENS || '2000'),
    temperature: parseFloat(process.env.DEEPSEEK_TEMPERATURE || '0.1'),
    timeout: parseInt(process.env.DEEPSEEK_TIMEOUT || '30000')
  }
}

/**
 * 验证DeepSeek配置
 */
export function validateDeepSeekConfig(): boolean {
  try {
    const config = getDeepSeekConfig()
    return !!(config.apiKey && config.baseURL && config.model)
  } catch (error) {
    console.error('DeepSeek配置验证失败:', error)
    return false
  }
}

/**
 * DeepSeek模型选项
 */
export const DEEPSEEK_MODELS = {
  CHAT: 'deepseek-chat',           // V3通用对话模型 - 推荐
  CODER: 'deepseek-coder',         // V3代码专用模型
  REASONER: 'deepseek-reasoner'    // R1推理模型（慢但准确）
} as const

/**
 * 根据任务类型推荐模型
 */
export function getRecommendedModel(taskType: 'general' | 'code' | 'reasoning' = 'general'): string {
  switch (taskType) {
    case 'code':
      return DEEPSEEK_MODELS.CODER
    case 'reasoning':
      return DEEPSEEK_MODELS.REASONER
    case 'general':
    default:
      return DEEPSEEK_MODELS.CHAT
  }
}
