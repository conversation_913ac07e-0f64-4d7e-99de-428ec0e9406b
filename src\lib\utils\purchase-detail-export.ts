/**
 * 🇨🇳 风口云平台 - 风口采购明细导出工具
 * 
 * 功能说明：
 * - 导出风口采购明细Excel表格
 * - 支持单个订单和多订单汇总导出
 * - 格式与用户提供的模板一致
 */

import ExcelJS from 'exceljs'
import type { Order, OrderItem } from '@/types'

// 采购明细项目接口
export interface PurchaseDetailItem {
  序号: number
  日期: string
  项目名称: string
  风口类型: string
  风口名称: string
  长度: number | string  // mm
  宽度: number | string   // mm
  数量: number           // 平方
  单价: number           // 元
  金额: number           // 元
  总金额: number
  备注: string
}

// 导出参数接口
export interface PurchaseExportOptions {
  title: string           // 表格标题，如 "2025年2月份风口采购明细"
  orders: Order[]         // 订单列表
  clientName?: string     // 客户名称（用于客户汇总）
  startDate?: Date        // 开始日期
  endDate?: Date          // 结束日期
}

/**
 * 将订单项目转换为采购明细项目
 */
function convertOrderItemsToPurchaseDetails(orders: Order[]): PurchaseDetailItem[] {
  const details: PurchaseDetailItem[] = []
  let sequenceNumber = 1

  orders.forEach(order => {
    const orderDate = new Date(order.createdAt).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '.')

    // 解析订单项目
    let items: OrderItem[] = []
    try {
      if (typeof order.items === 'string') {
        items = JSON.parse(order.items)
      } else {
        items = order.items || []
      }
    } catch (error) {
      console.error('解析订单项目失败:', error)
      items = []
    }

    items.forEach(item => {
      // 提取尺寸信息
      let 长度 = 0
      let 宽度 = 0
      
      if (item.dimensions) {
        长度 = item.dimensions.length || 0
        宽度 = item.dimensions.width || 0
      } else if (item.length && item.width) {
        // 兼容旧版本字段
        长度 = item.length
        宽度 = item.width
      }

      // 提取风口类型和名称
      let 风口类型 = '普通风口'
      let 风口名称 = '出风口'

      // 根据productType判断风口类型 - 使用统一的分类器
      if (item.productType) {
        // 使用统一的风口分类器判断
        const { isPremiumVent } = require('@/lib/utils/vent-type-classifier')

        if (item.productType === 'maintenance') {
          风口类型 = '检修口'
        } else if (isPremiumVent(item.productType, item.productName)) {
          风口类型 = '高端风口'
        } else {
          风口类型 = '常规风口'
        }

        // 根据productType判断风口名称
        if (item.productType.includes('return') || item.productType.includes('回风')) {
          风口名称 = '回风口'
        } else if (item.productType === 'maintenance' || item.productType.includes('检修')) {
          风口名称 = '检修口'
        } else {
          风口名称 = '出风口'
        }
      }

      // 如果有productName，也进行补充判断
      if (item.productName) {
        if (item.productName.includes('出风口')) {
          风口名称 = '出风口'
        } else if (item.productName.includes('回风口')) {
          风口名称 = '回风口'
        } else if (item.productName.includes('检修口')) {
          风口名称 = '检修口'
        }

        // 从产品名称中识别高端风口关键词
        const highEndKeywords = ['箭型', '爪型', '木纹', '黑白', '腻子粉', '石膏板', '高端']
        if (highEndKeywords.some(keyword => item.productName.includes(keyword))) {
          风口类型 = '高端风口'
        }
      }

      // 根据风口类型计算数量（按系统计价方式）
      const 长度数值 = parseFloat(长度?.toString() || '0')
      const 宽度数值 = parseFloat(宽度?.toString() || '0')
      const 风口数量 = item.quantity || 0

      let 计算数量 = 0

      // 判断是否为高端风口
      const highEndTypes = [
        'black_white_outlet', 'black_white_return', 'claw_outlet', 'claw_return',
        'wood_outlet', 'wood_return', 'arrow_outlet', 'arrow_return',
        'white_putty_outlet', 'white_putty_return', 'black_putty_outlet', 'black_putty_return',
        'white_gypsum_outlet', 'white_gypsum_return', 'black_gypsum_outlet', 'black_gypsum_return',
        'high_end_white_outlet', 'high_end_black_outlet', 'high_end_white_return', 'high_end_black_return',
        'wood_grain_outlet', 'wood_grain_return'
      ]

      if (highEndTypes.includes(item.productType || '')) {
        // 高端风口：按长度计价 (长+120) ÷ 1,000 × 数量
        if (长度数值 > 0) {
          const 单个长度 = (长度数值 + 120) / 1000 // 转换为米
          计算数量 = parseFloat((风口数量 * 单个长度).toFixed(3)) // 保留3位小数
        }
      } else {
        // 普通风口：按面积计价 (长+60) × (宽+60) ÷ 1,000,000 × 数量
        if (长度数值 > 0 && 宽度数值 > 0) {
          const 单个面积 = (长度数值 + 60) * (宽度数值 + 60) / 1000000 // 转换为平方米
          计算数量 = parseFloat((风口数量 * 单个面积).toFixed(3)) // 保留3位小数
        }
      }

      const detail: PurchaseDetailItem = {
        序号: sequenceNumber++,
        日期: orderDate,
        项目名称: order.projectAddress || '未指定项目',
        风口类型,
        风口名称,
        长度: 长度 || '',
        宽度: 宽度 || '',
        数量: 计算数量, // 按系统计价方式计算的数量
        单价: item.unitPrice || 0,
        金额: item.totalPrice || (item.quantity * item.unitPrice) || 0,
        总金额: item.totalPrice || 0,
        备注: item.notes || ''
      }

      details.push(detail)
    })
  })

  return details
}

/**
 * 导出风口采购明细Excel
 */
export async function exportPurchaseDetailExcel(options: PurchaseExportOptions): Promise<void> {
  try {
    console.log('🔄 开始导出风口采购明细Excel...')
    
    // 转换数据
    const details = convertOrderItemsToPurchaseDetails(options.orders)
    
    if (details.length === 0) {
      throw new Error('没有可导出的采购明细数据')
    }

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = '风口云平台'
    workbook.created = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('风口采购明细', {
      pageSetup: { 
        paperSize: 9, 
        orientation: 'landscape',
        margins: {
          left: 0.7, right: 0.7, top: 0.75, bottom: 0.75,
          header: 0.3, footer: 0.3
        }
      }
    })

    // 设置列宽
    worksheet.columns = [
      { width: 6 },   // A: 序号
      { width: 12 },  // B: 日期
      { width: 25 },  // C: 项目名称
      { width: 12 },  // D: 风口类型
      { width: 12 },  // E: 风口名称
      { width: 10 },  // F: 长度/mm
      { width: 10 },  // G: 宽度/mm
      { width: 12 },  // H: 数量(平方)
      { width: 10 },  // I: 单价/元
      { width: 12 },  // J: 金额/元
      { width: 12 },  // K: 总金额
      { width: 20 }   // L: 备注
    ]

    // 1. 添加标题
    const titleRow = worksheet.getRow(1)
    titleRow.getCell(1).value = options.title
    titleRow.getCell(1).font = { 
      bold: true, 
      size: 16, 
      color: { argb: 'FF000000' } 
    }
    titleRow.getCell(1).alignment = { 
      horizontal: 'center', 
      vertical: 'middle' 
    }
    titleRow.height = 30
    
    // 合并标题行
    worksheet.mergeCells('A1:L1')

    // 2. 添加表头
    const headerRow = worksheet.getRow(2)
    const headers = [
      '序号', '日期', '项目名称', '风口类型', '风口名称',
      '长度/mm', '宽度/mm', '数量(㎡/m)', '单价/元', '金额/元', '总金额', '备注'
    ]
    
    headers.forEach((header, index) => {
      const cell = headerRow.getCell(index + 1)
      cell.value = header
      cell.font = { 
        bold: true, 
        size: 11, 
        color: { argb: 'FF000000' } 
      }
      cell.fill = { 
        type: 'pattern', 
        pattern: 'solid', 
        fgColor: { argb: 'FFE6E6E6' } 
      }
      cell.alignment = { 
        horizontal: 'center', 
        vertical: 'middle' 
      }
      cell.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
      }
    })
    headerRow.height = 25

    // 3. 添加数据行
    details.forEach((detail, index) => {
      const row = worksheet.getRow(index + 3)
      const rowData = [
        detail.序号,
        detail.日期,
        detail.项目名称,
        detail.风口类型,
        detail.风口名称,
        detail.长度,
        detail.宽度,
        detail.数量,
        detail.单价,
        detail.金额,
        detail.总金额,
        detail.备注
      ]

      rowData.forEach((data, cellIndex) => {
        const cell = row.getCell(cellIndex + 1)
        cell.value = data
        cell.border = {
          top: { style: 'thin', color: { argb: 'FF000000' } },
          left: { style: 'thin', color: { argb: 'FF000000' } },
          bottom: { style: 'thin', color: { argb: 'FF000000' } },
          right: { style: 'thin', color: { argb: 'FF000000' } }
        }

        // 数字格式化
        if (cellIndex === 7) { // 数量
          cell.numFmt = '0.00'
          cell.alignment = { horizontal: 'center' }
        } else if (cellIndex === 8 || cellIndex === 9 || cellIndex === 10) { // 单价、金额、总金额
          cell.numFmt = '#,##0.00'
          cell.alignment = { horizontal: 'right' }
        } else if (cellIndex === 0) { // 序号
          cell.alignment = { horizontal: 'center' }
        } else if (cellIndex === 1) { // 日期
          cell.alignment = { horizontal: 'center' }
        }
      })
      row.height = 20
    })

    // 4. 计算实际需要的行数（根据数据动态调整）
    const dataRows = details.length + 2 // 标题行 + 表头行 + 数据行
    const minDisplayRows = 22 // 最少显示行数（用于美观）
    const actualRows = Math.max(dataRows, minDisplayRows)

    // 如果数据行数少于最小显示行数，添加空行填充
    if (dataRows < minDisplayRows) {
      for (let i = dataRows + 1; i <= minDisplayRows; i++) {
        const row = worksheet.getRow(i)
        for (let j = 1; j <= 12; j++) {
          const cell = row.getCell(j)
          cell.border = {
            top: { style: 'thin', color: { argb: 'FF000000' } },
            left: { style: 'thin', color: { argb: 'FF000000' } },
            bottom: { style: 'thin', color: { argb: 'FF000000' } },
            right: { style: 'thin', color: { argb: 'FF000000' } }
          }
        }
        row.height = 20
      }
    }

    // 5. 添加总计行（紧跟在实际数据后面）
    const totalRowIndex = actualRows + 1
    const totalRow = worksheet.getRow(totalRowIndex)
    totalRow.getCell(1).value = '合计'
    totalRow.getCell(1).font = { bold: true }
    totalRow.getCell(1).alignment = { horizontal: 'center' }

    // 计算总计
    const totalQuantity = details.reduce((sum, item) => sum + (parseFloat(item.数量?.toString() || '0') || 0), 0)
    const totalAmount = details.reduce((sum, item) => sum + (parseFloat(item.金额?.toString() || '0') || 0), 0)

    totalRow.getCell(8).value = totalQuantity.toFixed(2) // 数量总计
    totalRow.getCell(8).numFmt = '0.00'
    totalRow.getCell(8).alignment = { horizontal: 'center' }
    totalRow.getCell(8).font = { bold: true }

    totalRow.getCell(10).value = totalAmount.toFixed(2) // 金额总计
    totalRow.getCell(10).numFmt = '#,##0.00'
    totalRow.getCell(10).alignment = { horizontal: 'right' }
    totalRow.getCell(10).font = { bold: true }

    totalRow.getCell(11).value = totalAmount.toFixed(2) // 总金额
    totalRow.getCell(11).numFmt = '#,##0.00'
    totalRow.getCell(11).alignment = { horizontal: 'right' }
    totalRow.getCell(11).font = { bold: true }

    // 为总计行添加边框
    for (let j = 1; j <= 12; j++) {
      const cell = totalRow.getCell(j)
      cell.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
      }
    }
    totalRow.height = 22

    // 6. 添加付款信息（动态位置）
    const paymentInfoRowIndex = totalRowIndex + 2 // 总计行后空一行
    const paymentInfoRow = worksheet.getRow(paymentInfoRowIndex)
    paymentInfoRow.getCell(1).value = '付款信息:'
    paymentInfoRow.getCell(1).font = { bold: true, size: 12 }
    paymentInfoRow.height = 25

    // 如果有客户信息，显示付款统计
    if (options.clientName && options.orders.length > 0) {
      // 计算付款统计
      const totalOrderAmount = options.orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0)
      const totalPaidAmount = options.orders.reduce((sum, order) => sum + (order.paidAmount || 0), 0)
      const totalUnpaidAmount = totalOrderAmount - totalPaidAmount

      const paymentStatsRowIndex = paymentInfoRowIndex + 1
      const paymentStatsRow = worksheet.getRow(paymentStatsRowIndex)
      paymentStatsRow.getCell(1).value = `订单总额: ¥${totalOrderAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
      paymentStatsRow.getCell(4).value = `已付款: ¥${totalPaidAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
      paymentStatsRow.getCell(7).value = `待付款: ¥${totalUnpaidAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`

      paymentStatsRow.getCell(1).font = { size: 11 }
      paymentStatsRow.getCell(4).font = { size: 11, color: { argb: 'FF008000' } } // 绿色
      paymentStatsRow.getCell(7).font = { size: 11, color: { argb: totalUnpaidAmount > 0 ? 'FFFF0000' : 'FF008000' } } // 红色或绿色
      paymentStatsRow.height = 22
    }

    // 7. 导出文件
    const fileName = `${options.title.replace(/[\/\\:*?"<>|]/g, '_')}.xlsx`
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    link.click()
    window.URL.revokeObjectURL(url)

    console.log('✅ 风口采购明细Excel导出成功:', fileName)
  } catch (error) {
    console.error('❌ 导出风口采购明细Excel失败:', error)
    throw error
  }
}
