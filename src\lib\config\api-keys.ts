/**
 * API密钥配置
 * 注意：在生产环境中，这些密钥应该通过环境变量管理
 */

export const API_KEYS = {
  // DeepSeek 官方API密钥
  DEEPSEEK: process.env.NEXT_PUBLIC_DEEPSEEK_API_KEY || '***********************************',

  // 硅基流动API密钥
  SILICONFLOW: process.env.NEXT_PUBLIC_SILICONFLOW_API_KEY || 'sk-szczomdkrprhzlzuzwlblenwfvvuuuyxbxnjgmrcetorftth',

  // 通义千问API密钥
  QWEN: process.env.NEXT_PUBLIC_QWEN_API_KEY || 'sk-d4efb618e0d54ef282d5f1d8d5d71009',

  // 豆包AI（火山引擎）API密钥
  DOUBAO: process.env.NEXT_PUBLIC_DOUBAO_API_KEY || '20dc1a0c-8219-4280-9441-901efe0d7637'
}

// API提供商配置
export const API_PROVIDERS = {
  deepseek: {
    name: 'DeepSeek 官方',
    baseURL: 'https://api.deepseek.com/v1',
    models: ['deepseek-chat', 'deepseek-reasoner'],
    description: '使用DeepSeek官方API，稳定可靠',
    pricing: {
      input: '$0.27/M tokens',
      output: '$1.10/M tokens'
    }
  },
  siliconflow: {
    name: '硅基流动',
    baseURL: 'https://api.siliconflow.cn/v1',
    models: ['deepseek-ai/DeepSeek-V3'],
    description: '使用硅基流动加速服务，速度提升10倍以上，国内访问更稳定',
    pricing: {
      input: '¥2.00/M tokens',
      output: '¥8.00/M tokens'
    },
    advantages: [
      '10x+ 速度提升',
      '国内访问稳定',
      '有免费额度',
      '兼容OpenAI API格式'
    ]
  },
  qwen: {
    name: '通义千问',
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    models: ['qwen-turbo', 'qwen-plus', 'qwen-max'],
    description: '阿里云通义千问大模型，中文理解能力强',
    pricing: {
      input: '¥2.00/M tokens',
      output: '¥6.00/M tokens'
    },
    advantages: [
      '中文理解优秀',
      '阿里云稳定服务',
      '兼容OpenAI API格式',
      '响应速度快'
    ]
  },
  doubao: {
    name: '豆包AI',
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    models: ['doubao-seed-1-6-250615'],
    description: '字节跳动豆包大模型，兼容OpenAI API格式',
    pricing: {
      input: '¥0.80/M tokens',
      output: '¥2.00/M tokens'
    },
    advantages: [
      '字节跳动技术',
      '兼容OpenAI API格式',
      '支持多模态',
      '性价比高'
    ]
  }
}

// 检查API密钥是否配置
export function checkApiKeyConfiguration(): {
  deepseek: boolean
  siliconflow: boolean
  qwen: boolean
  doubao: boolean
  warnings: string[]
} {
  const warnings: string[] = []

  const deepseekConfigured = API_KEYS.DEEPSEEK !== '***********************************'
  const siliconflowConfigured = API_KEYS.SILICONFLOW !== 'YOUR_SILICONFLOW_API_KEY_HERE' && API_KEYS.SILICONFLOW.startsWith('sk-')
  const qwenConfigured = API_KEYS.QWEN !== 'YOUR_QWEN_API_KEY_HERE' && API_KEYS.QWEN.startsWith('sk-')
  const doubaoConfigured = API_KEYS.DOUBAO !== 'YOUR_DOUBAO_API_KEY_HERE' && API_KEYS.DOUBAO.length > 10

  if (!deepseekConfigured) {
    warnings.push('DeepSeek API密钥使用默认值，建议配置自己的密钥')
  }

  if (!siliconflowConfigured) {
    warnings.push('硅基流动API密钥未配置，请申请并配置API密钥')
  }

  if (!qwenConfigured) {
    warnings.push('通义千问API密钥未配置，请申请并配置API密钥')
  }

  if (!doubaoConfigured) {
    warnings.push('豆包AI API密钥未配置，请申请并配置API密钥')
  }

  return {
    deepseek: deepseekConfigured,
    siliconflow: siliconflowConfigured,
    qwen: qwenConfigured,
    doubao: doubaoConfigured,
    warnings
  }
}
