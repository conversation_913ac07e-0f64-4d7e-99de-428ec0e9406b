/**
 * 🇨🇳 风口云平台 - 工厂管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 获取工厂列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const isPublic = searchParams.get('public') === 'true'

    console.log('🏭 获取工厂列表API请求:', { isPublic })

    const factories = await db.getFactories()

    // 处理数据序列化问题 - 清理所有不可序列化的数据
    const cleanFactories = factories.map(factory => ({
      id: factory.id,
      name: factory.name,
      code: factory.code,
      address: factory.address,
      phone: factory.phone,
      email: factory.email,
      status: factory.status,
      createdAt: factory.createdAt ? new Date(factory.createdAt).toISOString() : null,
      updatedAt: factory.updatedAt ? new Date(factory.updatedAt).toISOString() : null,
      ownerName: factory.ownerName || null,
      // 处理可能的BigInt字段
      totalSuspendedMs: factory.totalSuspendedMs ? Number(factory.totalSuspendedMs) : 0,
      // 移除users关联数据，避免循环引用
      // users字段已经在数据库层处理，这里不需要包含
    }))

    // 如果是公开访问，只返回基本信息
    const responseData = isPublic ? {
      success: true,
      factories: cleanFactories.map(factory => ({
        id: factory.id,
        name: factory.name,
        code: factory.code,
        address: factory.address,
        phone: factory.phone,
        email: factory.email,
        status: factory.status,
        createdAt: factory.createdAt,
        ownerName: factory.ownerName
      })),
      isPublic
    } : {
      success: true,
      factories: cleanFactories
    }

    return NextResponse.json(responseData)

  } catch (error) {
    console.error('❌ 获取工厂失败:', error)
    return NextResponse.json(
      { error: '获取工厂失败' },
      { status: 500 }
    )
  }
}

// 更新工厂信息
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { factoryId, factoryData, adminData } = body

    console.log('🔧 更新工厂信息:', { factoryId, factoryData, adminData })

    if (!factoryId) {
      return NextResponse.json(
        { error: '工厂ID不能为空' },
        { status: 400 }
      )
    }

    // 更新工厂基本信息
    const updatedFactory = await db.updateFactory(factoryId, factoryData)

    // 如果提供了管理员信息，同时更新管理员信息
    if (adminData) {
      await db.updateFactoryAdmin(factoryId, adminData)
    }

    console.log('✅ 工厂信息更新成功:', updatedFactory.name)

    return NextResponse.json({
      success: true,
      factory: updatedFactory,
      message: '工厂信息更新成功'
    })

  } catch (error) {
    console.error('❌ 更新工厂信息失败:', error)
    return NextResponse.json(
      { error: '更新工厂信息失败' },
      { status: 500 }
    )
  }
}

// 创建工厂
export async function POST(request: NextRequest) {
  try {
    console.log('📝 接收到创建工厂请求')
    const { factoryData, adminData, password } = await request.json()

    if (!factoryData.name || !factoryData.code || !adminData.username || !password) {
      return NextResponse.json(
        { error: '缺少必要的工厂信息' },
        { status: 400 }
      )
    }

    console.log('🔍 检查工厂编码和用户名是否已存在...')

    // 检查工厂编码是否已存在
    const codeExists = await db.checkFactoryCodeExists(factoryData.code)
    if (codeExists) {
      console.log('❌ 工厂编码已存在:', factoryData.code)
      return NextResponse.json(
        { error: '工厂编码已存在' },
        { status: 400 }
      )
    }

    // 检查用户名是否已存在
    const usernameExists = await db.checkUsernameExists(adminData.username)
    if (usernameExists) {
      console.log('❌ 用户名已存在:', adminData.username)
      return NextResponse.json(
        { error: '用户名已存在' },
        { status: 400 }
      )
    }

    console.log('🏭 开始创建工厂和管理员...')
    const result = await db.createFactoryWithAdmin(factoryData, adminData, password)

    if (!result || !result.factory || !result.admin) {
      console.log('❌ 创建工厂失败：返回结果为空')
      return NextResponse.json(
        { error: '创建工厂失败：数据保存异常' },
        { status: 500 }
      )
    }

    console.log('✅ 工厂创建成功:', result.factory.name)
    console.log('✅ 管理员创建成功:', result.admin.username)

    // 处理BigInt序列化问题
    const factoryForResponse = {
      ...result.factory,
      totalSuspendedMs: result.factory.totalSuspendedMs ? Number(result.factory.totalSuspendedMs) : 0
    }

    return NextResponse.json({
      success: true,
      data: {
        factory: factoryForResponse,
        admin: result.admin
      }
    })

  } catch (error) {
    console.error('❌ 创建工厂失败:', error)
    return NextResponse.json(
      { error: `创建工厂失败: ${error.message || '未知错误'}` },
      { status: 500 }
    )
  }
}

// 删除工厂
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('id')

    if (!factoryId) {
      return NextResponse.json(
        { error: '工厂ID不能为空' },
        { status: 400 }
      )
    }

    // 删除工厂
    const success = await db.deleteFactory(factoryId)

    if (!success) {
      return NextResponse.json(
        { error: '删除工厂失败' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: '工厂删除成功'
    })

  } catch (error) {
    console.error('❌ 删除工厂失败:', error)
    return NextResponse.json(
      { error: '删除工厂失败' },
      { status: 500 }
    )
  }
}
