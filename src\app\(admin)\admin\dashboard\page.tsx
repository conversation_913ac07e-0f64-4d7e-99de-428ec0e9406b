"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { db } from "@/lib/database"
import { dataSyncService } from "@/lib/services/data-sync"
// import { DataSyncStatus } from "@/components/data-sync/data-sync-status" // 已删除
import { useRouter } from "next/navigation"
import { AdminRouteGuard } from "@/components/auth/route-guard"
import {
  Factory,
  Users,
  ShoppingCart,
  TrendingUp,
  AlertCircle,
  Plus,
  RefreshCw,
  Calendar,
  Clock
} from "lucide-react"

export default function AdminDashboard() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [dashboardStats, setDashboardStats] = useState({
    totalFactories: 0,
    activeFactories: 0,
    totalClients: 0,
    totalOrders: 0,
    totalRevenue: 0,
    monthlyGrowth: 0
  })
  const [recentFactories, setRecentFactories] = useState<any[]>([])
  const [recentAlerts, setRecentAlerts] = useState<any[]>([])
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null)

  // 加载仪表板数据
  const loadDashboardData = async () => {
    try {
      setLoading(true)
      console.log('🔄 开始加载仪表板数据...')

      // 诊断步骤1: 检查数据库连接
      console.log('🔍 步骤1: 检查数据库连接...')
      try {
        const factories = await db.getFactories()
        console.log('✅ 数据库连接正常，获取到工厂列表:', factories.length, '个工厂')

        if (factories.length === 0) {
          console.warn('⚠️ 警告: 数据库中没有工厂数据')
          setRecentAlerts([{
            id: 'no-factories',
            type: 'warning',
            message: '数据库中没有工厂数据，请先添加工厂',
            time: '刚刚'
          }])
          return
        }

        // 诊断步骤2: 逐个检查工厂数据
        console.log('🔍 步骤2: 检查各工厂数据连接...')
        const factoryDiagnostics: unknown[] = []

        for (const factory of factories.slice(0, 4)) {
          console.log(`🏭 检查工厂: ${factory.name} (ID: ${factory.id})`)

          try {
            // 检查客户数据
            const clients = await db.getClientsByFactoryId(factory.id)
            console.log(`  📋 客户数据: ${clients.length} 个客户`)

            // 检查订单数据
            const orders = await db.getOrdersByFactoryId(factory.id)
            console.log(`  📦 订单数据: ${orders.length} 个订单`)

            // 检查统计数据
            const factoryStats = await dataSyncService.getFactoryStatistics(factory.id)
            console.log(`  📊 统计数据:`, factoryStats)

            factoryDiagnostics.push({
              factory: factory.name,
              status: 'success',
              clients: clients.length,
              orders: orders.length,
              stats: factoryStats
            })

          } catch (error) {
            console.error(`❌ 工厂 ${factory.name} 数据读取失败:`, error)
            factoryDiagnostics.push({
              factory: factory.name,
              status: 'error',
              error: error instanceof Error ? error.message : String(error),
              clients: 0,
              orders: 0
            })
          }
        }

        console.log('🔍 工厂数据诊断结果:', factoryDiagnostics)

        // 诊断步骤3: 获取总部统计数据
        console.log('🔍 步骤3: 获取总部统计数据...')
        const stats = await dataSyncService.getHeadquartersStatistics()
        console.log('📊 总部统计数据:', stats)

        setDashboardStats({
          totalFactories: stats.totalFactories,
          activeFactories: stats.activeFactories,
          totalClients: stats.totalClients,
          totalOrders: stats.totalOrders,
          totalRevenue: stats.totalRevenue,
          monthlyGrowth: 15.8 // 暂时使用固定值，后续可以计算实际增长率
        })

        // 格式化工厂数据
        const factoriesWithStats = factoryDiagnostics.map((diag: any) => {
          const factory = factories.find((f: any) => f.name === diag.factory)
          return {
            id: factory?.id || '',
            name: diag.factory,
            location: factory?.address || '未知',
            status: diag.status === 'success' ? 'active' : 'inactive',
            clients: diag.clients,
            orders: diag.orders
          }
        })

        setRecentFactories(factoriesWithStats)

        // 生成系统提醒（包含诊断信息）
        const alerts = generateSystemAlerts(factoriesWithStats, stats)

        // 添加诊断相关的提醒
        const errorFactories = factoryDiagnostics.filter((d: any) => d.status === 'error')
        if (errorFactories.length > 0) {
          alerts.unshift({
            id: 'data-sync-error',
            type: 'urgent',
            message: `${errorFactories.length} 个工厂数据读取失败，请检查连接`,
            time: '刚刚'
          })
        }

        setRecentAlerts(alerts)
        setLastUpdateTime(new Date())
        console.log('✅ 仪表板数据加载完成')

      } catch (dbError) {
        console.error('❌ 数据库连接失败:', dbError)
        setRecentAlerts([{
          id: 'db-connection-error',
          type: 'urgent',
          message: `数据库连接失败: ${dbError instanceof Error ? dbError.message : String(dbError)}`,
          time: '刚刚'
        }])
        throw dbError
      }

    } catch (error) {
      console.error('❌ 加载仪表板数据失败:', error)
      setRecentAlerts([{
        id: 'dashboard-load-error',
        type: 'urgent',
        message: `仪表板加载失败: ${error instanceof Error ? error.message : String(error)}`,
        time: '刚刚'
      }])
    } finally {
      setLoading(false)
    }
  }

  // 生成系统提醒
  const generateSystemAlerts = (factories: unknown[], stats: unknown) => {
    const alerts: unknown[] = []
    const now = new Date()

    // 检查工厂状态
    const inactiveFactories = factories.filter((f: any) => f.status !== 'active')
    if (inactiveFactories.length > 0) {
      alerts.push({
        id: `inactive-${Date.now()}`,
        type: 'warning',
        message: `有 ${inactiveFactories.length} 个工厂处于非活跃状态`,
        time: '刚刚'
      })
    }

    // 检查订单量
    const lowOrderFactories = factories.filter((f: any) => f.orders < 10)
    if (lowOrderFactories.length > 0) {
      alerts.push({
        id: `low-orders-${Date.now()}`,
        type: 'info',
        message: `${(lowOrderFactories[0] as any).name} 等工厂订单量较少，需要关注`,
        time: '5分钟前'
      })
    }

    // 检查客户数量
    const lowClientFactories = factories.filter((f: any) => f.clients < 5)
    if (lowClientFactories.length > 0) {
      alerts.push({
        id: `low-clients-${Date.now()}`,
        type: 'urgent',
        message: `${(lowClientFactories[0] as any).name} 客户数量不足，建议加强推广`,
        time: '10分钟前'
      })
    }

    // 添加一些通用提醒
    if ((stats as any).totalFactories > 0) {
      alerts.push({
        id: `sync-${Date.now()}`,
        type: 'info',
        message: `数据同步正常，已连接 ${(stats as any).totalFactories} 个工厂`,
        time: '15分钟前'
      })
    }

    return alerts.slice(0, 3) // 只显示前3个提醒
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadDashboardData()
  }, [])

  // 跳转到添加工厂页面
  const handleAddFactory = () => {
    router.push('/admin/factories')
  }
  return (
    <AdminRouteGuard>
      <DashboardLayout role="admin">
      <div className="p-8 bg-background min-h-full">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-foreground">仪表板</h1>
            <p className="text-muted-foreground flex items-center space-x-2">
              <span>平台总部数据概览</span>
              {lastUpdateTime && (
                <>
                  <span>•</span>
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">
                    最后更新: {lastUpdateTime.toLocaleTimeString()}
                  </span>
                </>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              size="sm"
              variant="outline"
              onClick={loadDashboardData}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新数据
            </Button>
            <Button size="sm" onClick={handleAddFactory}>
              <Plus className="h-4 w-4 mr-2" />
              添加工厂
            </Button>
          </div>
        </div>
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总工厂数</CardTitle>
              <Factory className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? (
                  <RefreshCw className="h-6 w-6 animate-spin" />
                ) : (
                  dashboardStats.totalFactories
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                活跃工厂: {dashboardStats.activeFactories}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总客户数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? (
                  <RefreshCw className="h-6 w-6 animate-spin" />
                ) : (
                  dashboardStats.totalClients.toLocaleString()
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                跨所有工厂
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总订单数</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? (
                  <RefreshCw className="h-6 w-6 animate-spin" />
                ) : (
                  dashboardStats.totalOrders.toLocaleString()
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                全平台订单
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总营收</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? (
                  <RefreshCw className="h-6 w-6 animate-spin" />
                ) : (
                  `¥${(dashboardStats.totalRevenue / 10000).toFixed(1)}万`
                )}
              </div>
              <p className="text-xs text-green-600">
                {dashboardStats.monthlyGrowth > 0 && `+${dashboardStats.monthlyGrowth}% 环比上月`}
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Factories */}
          <Card>
            <CardHeader>
              <CardTitle>工厂概览</CardTitle>
              <CardDescription>最近活跃的工厂列表</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentFactories.map((factory: any) => (
                  <div key={factory.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        factory.status === 'active' ? 'bg-green-500' : 'bg-gray-400'
                      }`} />
                      <div>
                        <p className="font-medium">{factory.name}</p>
                        <p className="text-sm text-gray-600">{factory.location}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{factory.clients} 客户</p>
                      <p className="text-sm text-gray-600">{factory.orders} 订单</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Alerts */}
          <Card>
            <CardHeader>
              <CardTitle>系统提醒</CardTitle>
              <CardDescription>需要关注的重要信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentAlerts.map((alert: any) => (
                  <div key={alert.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <AlertCircle className={`h-5 w-5 mt-0.5 ${
                      alert.type === 'urgent' ? 'text-red-500' :
                      alert.type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }`} />
                    <div className="flex-1">
                      <p className="text-sm">{alert.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{alert.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Data Sync Status - 已移除 */}
          {/* <DataSyncStatus /> */}
        </div>
      </div>
    </DashboardLayout>
    </AdminRouteGuard>
  )
}
