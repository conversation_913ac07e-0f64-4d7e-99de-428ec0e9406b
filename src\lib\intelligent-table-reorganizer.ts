/**
 * 智能表格数据重组器
 * 用于分析OCR识别的混乱文字内容，识别表格结构规律并重新组织数据
 */

export interface TableItem {
  sequence: number
  dimensions: string
  notes?: string
  originalText: string
  confidence: number
}

export interface TableColumn {
  items: TableItem[]
  startSequence: number
  endSequence: number
}

export interface ReorganizedTable {
  columns: TableColumn[]
  totalItems: number
  confidence: number
  detectedPattern: string
}

export class IntelligentTableReorganizer {
  
  /**
   * 主要入口函数：分析并重组表格数据
   */
  static analyzeAndReorganize(text: string): ReorganizedTable {
    console.log('🧠 开始智能表格数据重组分析...')
    console.log('📝 原始文字:', text)
    
    // 步骤1: 预处理文本
    const cleanedLines = this.preprocessText(text)
    console.log('🧹 预处理后的行数:', cleanedLines.length)
    
    // 步骤2: 识别序号模式
    const sequencePattern = this.detectSequencePattern(cleanedLines)
    console.log('🔢 检测到序号模式:', sequencePattern)
    
    // 步骤3: 提取表格项目
    const tableItems = this.extractTableItems(cleanedLines)
    console.log('📋 提取到表格项目数:', tableItems.length)
    
    // 步骤4: 分析表格结构
    const tableStructure = this.analyzeTableStructure(tableItems, sequencePattern)
    console.log('📊 分析表格结构:', tableStructure)
    
    // 步骤5: 重组数据
    const reorganizedTable = this.reorganizeData(tableItems, tableStructure)
    console.log('✅ 重组完成')
    
    return reorganizedTable
  }
  
  /**
   * 预处理文本：清理和标准化
   */
  private static preprocessText(text: string): string[] {
    const lines = text.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
    
    const cleanedLines: string[] = []
    
    for (const line of lines) {
      // 统一乘号格式
      let cleaned = line
        .replace(/[×*]/g, 'x')
        .replace(/\s+/g, '')
        .trim()
      
      // 修复常见OCR错误
      cleaned = this.fixCommonOCRErrors(cleaned)
      
      if (cleaned.length > 0) {
        cleanedLines.push(cleaned)
      }
    }
    
    return cleanedLines
  }
  
  /**
   * 修复常见的OCR识别错误
   */
  private static fixCommonOCRErrors(text: string): string {
    let fixed = text

    console.log('🔧 修复OCR错误，原文:', text)

    // 修复序号中的错误
    fixed = fixed.replace(/^(\d+)\.(\d+)\.0x(\d+)/, '$1.$2x$3') // 3.14.0x3510 -> 3.140x3510
    fixed = fixed.replace(/^(\d+)\.(\d+)\.(\d+)x/, '$1.$2$3x') // 3.14.0x -> 3.140x

    // 修复尺寸中的错误
    fixed = fixed.replace(/(\d+)29B00\.?/, '$1x2900') // 15029B00 -> 150x2900
    fixed = fixed.replace(/(\d+)-(\d+)x/, '$1.$2x') // 11-150x -> 11.150x

    // 修复序号格式
    fixed = fixed.replace(/^(\d+)-(\d+)x/, '$1.$2x') // 11-150x3540 -> 11.150x3540

    // 特殊修复：处理序号.数字.数字x格式
    fixed = fixed.replace(/^(\d+)\.(\d)\.(\d+)x(\d+)/, (match, seq, d1, d2, d3) => {
      // 3.1.40x3510 -> 3.140x3510
      return `${seq}.${d1}${d2}x${d3}`
    })

    // 特殊修复：处理3.14.0x3510这种特殊情况
    fixed = fixed.replace(/^3\.14\.0x(\d+)/, '3.140x$1') // 3.14.0x3510 -> 3.140x3510
    fixed = fixed.replace(/^(\d+)\.(\d+)\.0x(\d+)/, '$1.$2x$3') // 通用：序号.数字.0x -> 序号.数字x

    // 移除末尾的句号和逗号
    fixed = fixed.replace(/[.,，]$/, '')

    // 统一乘号
    fixed = fixed.replace(/[×*]/g, 'x')

    if (fixed !== text) {
      console.log('✅ OCR错误修复:', text, '->', fixed)
    }

    return fixed
  }
  
  /**
   * 检测序号模式
   */
  private static detectSequencePattern(lines: string[]): {
    type: 'sequential' | 'dual_column' | 'unknown'
    sequences: number[]
    hasJumps: boolean
    jumpPattern?: { from: number, to: number }[]
  } {
    const sequences: number[] = []
    const jumpPattern: { from: number, to: number }[] = []
    
    for (const line of lines) {
      const sequenceMatch = line.match(/^(\d+)\./)
      if (sequenceMatch) {
        const seq = parseInt(sequenceMatch[1])
        sequences.push(seq)
      }
    }
    
    if (sequences.length === 0) {
      return { type: 'unknown', sequences: [], hasJumps: false }
    }
    
    // 分析序号跳跃模式
    let hasJumps = false
    for (let i = 1; i < sequences.length; i++) {
      const diff = sequences[i] - sequences[i-1]
      if (diff > 1) {
        hasJumps = true
        jumpPattern.push({ from: sequences[i-1], to: sequences[i] })
      }
    }
    
    // 判断是否为双列模式
    const isDualColumn = this.isDualColumnPattern(sequences)
    
    return {
      type: isDualColumn ? 'dual_column' : (hasJumps ? 'sequential' : 'sequential'),
      sequences,
      hasJumps,
      jumpPattern: jumpPattern.length > 0 ? jumpPattern : undefined
    }
  }
  
  /**
   * 判断是否为双列模式（如：1,2,3...8,9,10...）
   */
  private static isDualColumnPattern(sequences: number[]): boolean {
    if (sequences.length < 6) return false

    console.log('🔍 检查双列模式，序号:', sequences)

    // 查找最大的跳跃点
    let maxJump = 0
    let jumpIndex = -1

    for (let i = 1; i < sequences.length; i++) {
      const diff = sequences[i] - sequences[i-1]
      if (diff > maxJump && diff > 1) {
        maxJump = diff
        jumpIndex = i
      }
    }

    console.log('🔍 最大跳跃:', { maxJump, jumpIndex })

    // 双列模式应该有一个显著的跳跃点
    if (jumpIndex > 0 && maxJump >= 3) {
      const firstPart = sequences.slice(0, jumpIndex)
      const secondPart = sequences.slice(jumpIndex)

      // 检查两部分是否都是连续的
      const firstContinuous = this.isSequenceContinuous(firstPart)
      const secondContinuous = this.isSequenceContinuous(secondPart)

      console.log('🔍 双列检查结果:', {
        firstPart,
        secondPart,
        firstContinuous,
        secondContinuous
      })

      return firstContinuous && secondContinuous
    }

    return false
  }

  /**
   * 检查序号是否连续
   */
  private static isSequenceContinuous(sequences: number[]): boolean {
    if (sequences.length <= 1) return true

    const sorted = [...sequences].sort((a, b) => a - b)
    for (let i = 1; i < sorted.length; i++) {
      if (sorted[i] !== sorted[i-1] + 1) {
        return false
      }
    }
    return true
  }

  /**
   * 提取表格项目
   */
  private static extractTableItems(lines: string[]): TableItem[] {
    const items: TableItem[] = []
    let currentSequence: number | null = null
    let pendingDimensions: string[] = []

    console.log('📋 开始提取表格项目...')

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      console.log(`🔍 处理第${i+1}行: "${line}"`)

      // 检查是否为序号行 - 改进正则表达式处理序号.尺寸格式
      const sequenceMatch = line.match(/^(\d+)\.(.*)/)
      if (sequenceMatch) {
        // 处理之前积累的尺寸数据
        if (currentSequence !== null && pendingDimensions.length > 0) {
          for (const dim of pendingDimensions) {
            items.push({
              sequence: currentSequence,
              dimensions: dim,
              originalText: dim,
              confidence: 0.8
            })
          }
        }

        // 开始新的序号
        currentSequence = parseInt(sequenceMatch[1])
        pendingDimensions = []
        console.log(`🔢 发现序号: ${currentSequence}`)

        // 检查序号行是否包含尺寸 - 处理1.140x3420格式
        const remainingText = sequenceMatch[2]
        console.log(`📝 序号后内容: "${remainingText}"`)

        // 特殊处理：如果是序号.数字x数字格式，需要正确解析
        if (remainingText.match(/^\d+x\d+/)) {
          // 直接是尺寸：1.140x3420 -> 140x3420
          const dimensionMatch = remainingText.match(/^(\d+x\d+)/)
          if (dimensionMatch) {
            pendingDimensions.push(dimensionMatch[1])
            console.log(`📐 序号行包含尺寸: ${dimensionMatch[1]}`)
          }
        } else {
          // 其他格式的尺寸匹配，包括修复后的格式
          const dimensionMatch = remainingText.match(/(\d+x\d+)/)
          if (dimensionMatch) {
            pendingDimensions.push(dimensionMatch[1])
            console.log(`📐 序号行包含尺寸: ${dimensionMatch[1]}`)
          }
        }
      } else {
        // 非序号行，检查是否为尺寸
        const dimensionMatch = line.match(/(\d+x\d+)/)
        if (dimensionMatch && currentSequence !== null) {
          pendingDimensions.push(dimensionMatch[1])
          console.log(`📐 独立尺寸行: ${dimensionMatch[1]} (属于序号${currentSequence})`)
        }
      }
    }

    // 处理最后的数据
    if (currentSequence !== null && pendingDimensions.length > 0) {
      for (const dim of pendingDimensions) {
        items.push({
          sequence: currentSequence,
          dimensions: dim,
          originalText: dim,
          confidence: 0.8
        })
      }
    }

    console.log(`✅ 表格项目提取完成，共${items.length}项`)
    items.forEach(item => {
      console.log(`  序号${item.sequence}: ${item.dimensions}`)
    })

    return items
  }

  /**
   * 分析表格结构
   */
  private static analyzeTableStructure(items: TableItem[], sequencePattern: any): {
    columnCount: number
    rowsPerItem: number
    isDoubleColumn: boolean
  } {
    if (sequencePattern.type === 'dual_column') {
      return {
        columnCount: 2,
        rowsPerItem: this.calculateRowsPerItem(items),
        isDoubleColumn: true
      }
    }

    return {
      columnCount: 1,
      rowsPerItem: this.calculateRowsPerItem(items),
      isDoubleColumn: false
    }
  }

  /**
   * 计算每个项目的行数
   */
  private static calculateRowsPerItem(items: TableItem[]): number {
    const sequenceGroups = new Map<number, number>()

    for (const item of items) {
      const count = sequenceGroups.get(item.sequence) || 0
      sequenceGroups.set(item.sequence, count + 1)
    }

    const counts = Array.from(sequenceGroups.values())
    // 返回最常见的行数
    const mode = counts.sort((a, b) =>
      counts.filter(v => v === a).length - counts.filter(v => v === b).length
    ).pop() || 1

    return mode
  }

  /**
   * 重组数据为表格格式
   */
  private static reorganizeData(items: TableItem[], structure: any): ReorganizedTable {
    const columns: TableColumn[] = []

    if (structure.isDoubleColumn) {
      // 双列模式重组
      const sequences = [...new Set(items.map(item => item.sequence))].sort((a, b) => a - b)
      const midPoint = Math.ceil(sequences.length / 2)

      // 第一列
      const firstColumnSequences = sequences.slice(0, midPoint)
      const firstColumnItems = items.filter(item => firstColumnSequences.includes(item.sequence))
      columns.push({
        items: firstColumnItems,
        startSequence: Math.min(...firstColumnSequences),
        endSequence: Math.max(...firstColumnSequences)
      })

      // 第二列
      const secondColumnSequences = sequences.slice(midPoint)
      const secondColumnItems = items.filter(item => secondColumnSequences.includes(item.sequence))
      columns.push({
        items: secondColumnItems,
        startSequence: Math.min(...secondColumnSequences),
        endSequence: Math.max(...secondColumnSequences)
      })
    } else {
      // 单列模式
      columns.push({
        items: items,
        startSequence: Math.min(...items.map(item => item.sequence)),
        endSequence: Math.max(...items.map(item => item.sequence))
      })
    }

    return {
      columns,
      totalItems: items.length,
      confidence: this.calculateOverallConfidence(items, structure),
      detectedPattern: structure.isDoubleColumn ? 'dual_column' : 'single_column'
    }
  }

  /**
   * 计算整体置信度
   */
  private static calculateOverallConfidence(items: TableItem[], structure: any): number {
    let confidence = 0.3

    console.log('🔍 计算置信度，项目数:', items.length, '结构:', structure)

    // 基于项目数量
    if (items.length >= 10) confidence += 0.3
    if (items.length >= 20) confidence += 0.2

    // 基于结构规律性
    if (structure.isDoubleColumn) {
      confidence += 0.4
      console.log('✅ 双列结构加分: +0.4')
    }

    // 基于序号连续性和规律性
    const sequences = [...new Set(items.map(item => item.sequence))].sort((a, b) => a - b)
    console.log('🔢 唯一序号:', sequences)

    // 检查序号是否有明显的双列模式
    if (this.isDualColumnPattern(sequences)) {
      confidence += 0.3
      console.log('✅ 双列序号模式加分: +0.3')
    }

    // 检查每个序号是否都有对应的数据
    const sequenceGroups = new Map<number, number>()
    items.forEach(item => {
      const count = sequenceGroups.get(item.sequence) || 0
      sequenceGroups.set(item.sequence, count + 1)
    })

    // 如果大部分序号都有2个项目（主尺寸+子尺寸），增加置信度
    const twoItemSequences = Array.from(sequenceGroups.values()).filter(count => count === 2).length
    const totalSequences = sequenceGroups.size
    if (twoItemSequences / totalSequences > 0.7) {
      confidence += 0.2
      console.log('✅ 序号数据完整性加分: +0.2')
    }

    const finalConfidence = Math.min(confidence, 1.0)
    console.log('📊 最终置信度:', finalConfidence)

    return finalConfidence
  }
}
