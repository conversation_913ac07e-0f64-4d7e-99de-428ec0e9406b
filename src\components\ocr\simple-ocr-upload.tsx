"use client"

import { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Upload,
  X,
  FileText,
  Image as ImageIcon,
  Loader2,
  CheckCircle,
  AlertCircle,
  Trash2,
  Eye,
  RotateCcw,
  Edit3,
  Save,
  RotateCw,
  Brain
} from "lucide-react"

interface SimpleOCRUploadProps {
  onTextRecognized: (text: string) => void
  onClose: () => void
}

interface RecognitionResult {
  text: string
  statistics: {
    total: number
    success: number
    failed: number
    totalWords: number
    totalChars: number
  }
}

interface UploadedFile {
  id: string
  file: File
  preview: string
  status: 'pending' | 'processing' | 'success' | 'error'
  error?: string
  result?: string
}

export function SimpleOCRUpload({ onTextRecognized, onClose }: SimpleOCRUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [useAccurate, setUseAccurate] = useState(false)
  const [recognitionResult, setRecognitionResult] = useState<RecognitionResult | null>(null)
  const [isEditingText, setIsEditingText] = useState(false)
  const [editedText, setEditedText] = useState('')
  const [isAIAnalyzing, setIsAIAnalyzing] = useState(false)
  const [aiResult, setAiResult] = useState<any>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  console.log('🎯 OCR组件已渲染，当前状态:', {
    uploadedFiles: uploadedFiles.length,
    isProcessing,
    dragActive,
    useAccurate
  })

  // 支持的文件类型
  const acceptedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/bmp',
    'image/gif',
    'image/tiff',
    'image/tif',
    'image/webp',
    'image/svg+xml',
    'image/x-icon',
    'image/vnd.microsoft.icon'
  ]

  const maxFileSize = 4 * 1024 * 1024 // 4MB
  const maxFiles = 10

  // 生成唯一ID
  const generateId = () => Math.random().toString(36).substr(2, 9)

  // 验证文件
  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type.toLowerCase())) {
      return `不支持的文件类型: ${file.type}。支持的格式: JPG, PNG, BMP, GIF, TIFF, WebP, SVG, ICO`
    }
    if (file.size > maxFileSize) {
      return `文件大小超过限制: ${(file.size / 1024 / 1024).toFixed(2)}MB > 4MB`
    }
    if (file.size === 0) {
      return '文件为空'
    }
    return null
  }

  // 创建预览URL
  const createPreview = (file: File): string => {
    try {
      return URL.createObjectURL(file)
    } catch (error) {
      console.error('创建预览失败:', error)
      return ''
    }
  }

  // 处理文件选择
  const handleFiles = useCallback((files: FileList | File[]) => {
    console.log(`📁 开始处理 ${files.length} 个文件`)
    const fileArray = Array.from(files)
    const newFiles: UploadedFile[] = []

    for (const file of fileArray) {
      console.log(`📄 处理文件: ${file.name}, 大小: ${file.size}, 类型: ${file.type}`)

      // 检查文件数量限制
      if (uploadedFiles.length + newFiles.length >= maxFiles) {
        alert(`最多只能上传${maxFiles}张图片`)
        break
      }

      // 验证文件
      const error = validateFile(file)
      if (error) {
        console.error(`❌ 文件验证失败: ${file.name} - ${error}`)
        alert(`文件 ${file.name}: ${error}`)
        continue
      }

      // 检查是否已存在相同文件
      const isDuplicate = uploadedFiles.some(uf =>
        uf.file.name === file.name &&
        uf.file.size === file.size &&
        uf.file.lastModified === file.lastModified
      )
      if (isDuplicate) {
        console.warn(`⚠️ 文件重复: ${file.name}`)
        alert(`文件 ${file.name} 已存在`)
        continue
      }

      const preview = createPreview(file)
      if (preview) {
        const newFile = {
          id: generateId(),
          file,
          preview,
          status: 'pending' as const
        }
        newFiles.push(newFile)
        console.log(`✅ 文件添加成功: ${file.name}, ID: ${newFile.id}`)
      }
    }

    if (newFiles.length > 0) {
      setUploadedFiles(prev => {
        const updated = [...prev, ...newFiles]
        console.log(`📋 文件列表更新: 总计 ${updated.length} 个文件`)
        return updated
      })
      console.log(`✅ 成功添加 ${newFiles.length} 个文件`)
    } else {
      console.log(`❌ 没有文件被添加`)
    }
  }, [uploadedFiles])

  // 拖拽处理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files)
    }
  }, [handleFiles])

  // 文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      handleFiles(event.target.files)
    }
    // 清空input值，允许重复选择相同文件
    event.target.value = ''
  }

  // 删除文件
  const removeFile = (id: string) => {
    setUploadedFiles(prev => {
      const updated = prev.filter(file => file.id !== id)
      // 释放预览URL
      const fileToRemove = prev.find(file => file.id === id)
      if (fileToRemove && fileToRemove.preview) {
        URL.revokeObjectURL(fileToRemove.preview)
      }
      return updated
    })
  }

  // 重试单个文件
  const retryFile = (id: string) => {
    setUploadedFiles(prev =>
      prev.map(file =>
        file.id === id
          ? { ...file, status: 'pending', error: undefined, result: undefined }
          : file
      )
    )
  }

  // 开始OCR识别
  const startOCR = async () => {
    if (uploadedFiles.length === 0) {
      alert('请先上传图片')
      return
    }

    const pendingFiles = uploadedFiles.filter(f => f.status === 'pending' || f.status === 'error')
    if (pendingFiles.length === 0) {
      // 如果所有文件都已处理成功，直接合并结果
      const successFiles = uploadedFiles.filter(f => f.status === 'success' && f.result)
      if (successFiles.length > 0) {
        const mergedText = successFiles.map(f => f.result).join('\n\n')
        onTextRecognized(mergedText)
        return
      }
      alert('没有需要处理的文件')
      return
    }

    setIsProcessing(true)

    try {
      // 更新待处理文件状态为处理中
      setUploadedFiles(prev =>
        prev.map(file =>
          pendingFiles.some(pf => pf.id === file.id)
            ? { ...file, status: 'processing' as const, error: undefined }
            : file
        )
      )

      // 准备FormData
      const formData = new FormData()
      pendingFiles.forEach(uf => {
        formData.append('files', uf.file)
      })
      formData.append('useAccurate', useAccurate.toString())

      console.log(`🔍 发送OCR请求: ${pendingFiles.length}个文件，${useAccurate ? '高精度' : '通用'}模式`)

      const response = await fetch('/api/ocr', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP错误 ${response.status}: ${errorText}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'OCR识别失败')
      }

      const { results, mergedText, statistics } = result.data

      // 更新文件状态和结果
      setUploadedFiles(prev =>
        prev.map((file, index) => {
          const pendingIndex = pendingFiles.findIndex(pf => pf.id === file.id)
          if (pendingIndex !== -1 && results[pendingIndex]) {
            const ocrResult = results[pendingIndex]
            return {
              ...file,
              status: ocrResult.success ? 'success' : 'error',
              result: ocrResult.success ? ocrResult.text : undefined,
              error: ocrResult.success ? undefined : ocrResult.error
            }
          }
          return file
        })
      )

      console.log('📊 OCR识别统计:', statistics)

      // 合并所有成功识别的文本（包括之前成功的）
      const allSuccessFiles = uploadedFiles
        .map((file, index) => {
          const pendingIndex = pendingFiles.findIndex(pf => pf.id === file.id)
          if (pendingIndex !== -1 && results[pendingIndex]?.success) {
            return results[pendingIndex].text
          }
          return file.status === 'success' ? file.result : null
        })
        .filter(text => text && text.trim())

      const finalMergedText = allSuccessFiles.join('\n\n')

      if (finalMergedText.trim()) {
        console.log('🎯 OCR识别完成，识别到的文本:')
        console.log(finalMergedText)

        // 保存识别结果用于显示
        setRecognitionResult({
          text: finalMergedText,
          statistics
        })

        // 初始化编辑文本
        setEditedText(finalMergedText)

        console.log('✅ OCR识别完成，用户可以查看和编辑识别结果')

      } else {
        alert('未能识别到有效文字，请检查图片质量或重新上传')
      }

    } catch (error) {
      console.error('❌ OCR处理失败:', error)

      let errorMessage = 'OCR识别失败'
      if (error instanceof Error) {
        errorMessage += `: ${error.message}`
      }

      alert(errorMessage)

      // 更新处理中的文件状态为错误
      setUploadedFiles(prev =>
        prev.map(file =>
          file.status === 'processing'
            ? { ...file, status: 'error', error: errorMessage }
            : file
        )
      )
    } finally {
      setIsProcessing(false)
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: UploadedFile['status']) => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <ImageIcon className="h-4 w-4 text-gray-400" />
    }
  }

  // AI识别并创建订单
  const handleAIAnalyze = async () => {
    if (!editedText.trim()) {
      alert('请先进行OCR识别或输入文本内容')
      return
    }

    setIsAIAnalyzing(true)
    try {
      console.log('🤖 开始AI智能识别OCR文本...')

      const response = await fetch('/api/ai/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: editedText,
          provider: 'qwen', // 使用系统默认的通义千问
          model: 'qwen-turbo' // 使用通义千问的快速模型
        })
      })

      if (!response.ok) {
        throw new Error(`AI识别请求失败: ${response.status}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'AI识别失败')
      }

      setAiResult(result.data)
      console.log('✅ AI识别完成:', result.data)

      // AI识别成功后，直接创建订单
      console.log('🚀 使用AI识别结果创建订单...')
      const formattedText = formatAIResultForOrder(result.data)
      onTextRecognized(formattedText)
      cleanup()
      onClose()

    } catch (error) {
      console.error('❌ AI识别失败:', error)
      alert(`AI识别失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsAIAnalyzing(false)
    }
  }

  // 将AI结果格式化为适合订单创建的文本格式
  const formatAIResultForOrder = (aiResult: any): string => {
    let formattedText = ''

    // 处理通义千问的projects结构
    if (aiResult.projects && aiResult.projects.length > 0) {
      aiResult.projects.forEach((project: any) => {
        // 添加项目信息
        if (project.projectName) {
          formattedText += `${project.projectName}\n`
        }

        // 处理楼层和风口信息
        if (project.floors && project.floors.length > 0) {
          project.floors.forEach((floor: any) => {
            if (floor.floorName) {
              formattedText += `${floor.floorName}\n`
            }

            // 处理房间和风口
            if (floor.rooms && floor.rooms.length > 0) {
              floor.rooms.forEach((room: any) => {
                if (room.roomName && room.roomName !== '默认房间') {
                  formattedText += `${room.roomName}\n`
                }

                // 处理风口信息
                if (room.vents && room.vents.length > 0) {
                  room.vents.forEach((vent: any) => {
                    if (vent.length && vent.width) {
                      formattedText += `${vent.length}×${vent.width}`
                      if (vent.quantity && vent.quantity > 1) {
                        formattedText += `×${vent.quantity}个`
                      }
                      if (vent.notes) {
                        formattedText += ` ${vent.notes}`
                      }
                      formattedText += '\n'
                    }
                  })
                }
              })
            }
          })
        }
      })
    }

    return formattedText.trim()
  }

  // 使用AI分析结果
  const handleUseAIResult = () => {
    if (aiResult && aiResult.entities && aiResult.entities.ventItems) {
      // 将AI分析的结构化数据转换为文本格式
      const aiText = formatAIResultToText(aiResult)
      setEditedText(aiText)
    }
  }

  // 将AI结果格式化为文本（适配通义千问格式）
  const formatAIResultToText = (result: any): string => {
    let formattedText = ''

    // 处理通义千问的projects结构
    if (result.projects && result.projects.length > 0) {
      result.projects.forEach((project: any, projectIndex: number) => {
        // 添加项目信息
        if (project.projectName) {
          formattedText += `项目：${project.projectName}\n`
        }
        if (project.clientInfo) {
          formattedText += `客户：${project.clientInfo}\n`
        }

        formattedText += '\n'

        // 处理楼层信息
        if (project.floors && project.floors.length > 0) {
          project.floors.forEach((floor: any) => {
            if (floor.floorName) {
              formattedText += `${floor.floorName}:\n`
            }

            // 处理房间信息
            if (floor.rooms && floor.rooms.length > 0) {
              floor.rooms.forEach((room: any) => {
                if (room.roomName && room.roomName !== '默认房间') {
                  formattedText += `  ${room.roomName}:\n`
                }

                // 处理风口信息
                if (room.vents && room.vents.length > 0) {
                  room.vents.forEach((vent: any, ventIndex: number) => {
                    const prefix = room.roomName && room.roomName !== '默认房间' ? '    ' : '  '
                    formattedText += `${prefix}${ventIndex + 1}. `

                    // 尺寸信息
                    if (vent.length && vent.width) {
                      formattedText += `${vent.length}×${vent.width}`
                    }

                    // 数量信息
                    if (vent.quantity && vent.quantity > 1) {
                      formattedText += ` ${vent.quantity}个`
                    }

                    // 备注信息
                    if (vent.notes) {
                      formattedText += ` (${vent.notes})`
                    }

                    formattedText += '\n'
                  })
                }
              })
            }
            formattedText += '\n'
          })
        }
      })
    } else if (result.entities) {
      // 兼容其他格式（如DeepSeek格式）
      const { entities } = result

      // 添加项目信息
      if (entities.projectName) {
        formattedText += `项目：${entities.projectName}\n`
      }
      if (entities.floorInfo) {
        formattedText += `楼层：${entities.floorInfo}\n`
      }

      formattedText += '\n'

      // 添加风口信息
      if (entities.ventItems && entities.ventItems.length > 0) {
        entities.ventItems.forEach((item: any, index: number) => {
          formattedText += `${index + 1}. ${item.type || '风口'}`
          if (item.dimensions) {
            formattedText += ` ${item.dimensions}`
          }
          if (item.quantity && item.quantity > 1) {
            formattedText += ` ${item.quantity}个`
          }
          if (item.notes) {
            formattedText += ` (${item.notes})`
          }
          formattedText += '\n'
        })
      }
    }

    return formattedText.trim()
  }

  // 清理预览URL
  const cleanup = () => {
    uploadedFiles.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview)
      }
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-6xl mx-4 max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                专业OCR文字识别
              </CardTitle>
              <CardDescription>
                支持多种图片格式的高精度文字识别，批量处理，智能解析
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                cleanup()
                onClose()
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 上传区域 - 整个区域都可以点击 */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
              dragActive
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={() => {
              console.log('🖱️ 点击上传区域')
              fileInputRef.current?.click()
            }}
          >
            <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              拖拽图片到此处或点击此区域上传
            </p>
            <p className="text-sm text-gray-500 mb-4">
              支持 JPG, PNG, BMP, GIF, TIFF, WebP, SVG, ICO 格式，单个文件不超过4MB
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-lg">
              <Upload className="h-4 w-4 mr-2" />
              点击此处选择图片
            </div>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept={acceptedTypes.join(',')}
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>

          {/* 识别选项和状态 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="accurate"
                    checked={useAccurate}
                    onChange={(e) => setUseAccurate(e.target.checked)}
                    disabled={isProcessing}
                    className="rounded"
                  />
                  <label htmlFor="accurate" className="text-sm text-gray-700">
                    高精度识别（更准确但速度较慢）
                  </label>
                </div>
              </div>
              <div className="text-sm text-gray-500">
                已上传: {uploadedFiles.length}/{maxFiles}
              </div>
            </div>

            {/* 状态提示 */}
            {uploadedFiles.length === 0 ? (
              <div className="text-center py-4 bg-gray-50 rounded-lg">
                <p className="text-gray-600">请先上传图片文件</p>
                <p className="text-sm text-gray-500 mt-1">支持拖拽或点击上传区域选择文件</p>
              </div>
            ) : (
              <div className="text-center py-4 bg-blue-50 rounded-lg">
                <p className="text-blue-800 font-medium">
                  ✅ 已准备好 {uploadedFiles.length} 个文件
                </p>
                <p className="text-sm text-blue-600 mt-1">
                  点击下方"开始识别"按钮进行OCR文字识别
                </p>
              </div>
            )}
          </div>

          {/* 文件列表 */}
          {uploadedFiles.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">上传的图片</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {uploadedFiles.map((uploadedFile) => (
                  <div key={uploadedFile.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2 flex-1 min-w-0">
                        {getStatusIcon(uploadedFile.status)}
                        <span className="text-sm font-medium truncate">
                          {uploadedFile.file.name}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        {uploadedFile.status === 'error' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => retryFile(uploadedFile.id)}
                            disabled={isProcessing}
                            title="重试"
                          >
                            <RotateCcw className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(uploadedFile.id)}
                          disabled={isProcessing}
                          title="删除"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      {uploadedFile.preview && (
                        <img
                          src={uploadedFile.preview}
                          alt="预览"
                          className="w-16 h-16 object-cover rounded border"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.style.display = 'none'
                          }}
                        />
                      )}
                      <div className="flex-1 text-xs text-gray-500 space-y-1">
                        <div>大小: {(uploadedFile.file.size / 1024).toFixed(1)}KB</div>
                        <div>类型: {uploadedFile.file.type}</div>
                        {uploadedFile.status === 'success' && uploadedFile.result && (
                          <div className="text-green-600">
                            识别成功: {uploadedFile.result.split('\n').length}行文字
                          </div>
                        )}
                        {uploadedFile.status === 'error' && uploadedFile.error && (
                          <div className="text-red-600 break-words">
                            错误: {uploadedFile.error}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 识别结果显示和编辑 */}
          {recognitionResult && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900">识别结果</h3>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsEditingText(!isEditingText)
                      if (!isEditingText) {
                        setEditedText(recognitionResult.text)
                      }
                    }}
                  >
                    {isEditingText ? (
                      <>
                        <Eye className="h-4 w-4 mr-1" />
                        预览
                      </>
                    ) : (
                      <>
                        <Edit3 className="h-4 w-4 mr-1" />
                        编辑
                      </>
                    )}
                  </Button>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="font-medium text-green-800">
                      {isEditingText ? '编辑识别文字' : '识别成功'}
                    </span>
                  </div>
                  <div className="text-sm text-green-600">
                    {isEditingText
                      ? `${editedText.split('\n').length}行 · ${editedText.length}字符`
                      : `${recognitionResult.statistics.totalWords}行 · ${recognitionResult.statistics.totalChars}字符`
                    }
                  </div>
                </div>

                {isEditingText ? (
                  <div className="space-y-3">
                    <textarea
                      value={editedText}
                      onChange={(e) => setEditedText(e.target.value)}
                      className="w-full h-60 p-3 border rounded-md text-sm font-mono resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="在此编辑识别的文字内容..."
                    />
                    <div className="text-xs text-gray-600 bg-blue-50 p-2 rounded">
                      💡 提示：您可以调整文字排版、修正识别错误、添加缺失信息等，以确保订单生成的准确性
                    </div>
                  </div>
                ) : (
                  <div className="bg-white border rounded p-3 max-h-60 overflow-y-auto">
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
                      {editedText || recognitionResult.text}
                    </pre>
                  </div>
                )}



                <div className="mt-4 flex justify-between items-center">
                  {/* 左侧：重置按钮 */}
                  <div>
                    {isEditingText && (
                      <Button
                        variant="outline"
                        onClick={() => {
                          setEditedText(recognitionResult.text)
                          setAiResult(null)
                        }}
                      >
                        <RotateCw className="h-4 w-4 mr-2" />
                        重置为原始文字
                      </Button>
                    )}
                  </div>

                  {/* 右侧：识别选择按钮 */}
                  <div className="flex space-x-3">
                    {/* AI智能识别按钮 */}
                    <Button
                      variant="outline"
                      onClick={handleAIAnalyze}
                      disabled={isAIAnalyzing || !editedText.trim()}
                      className="border-purple-300 text-purple-700 hover:bg-purple-50"
                    >
                      {isAIAnalyzing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          AI识别中...
                        </>
                      ) : (
                        <>
                          <Brain className="h-4 w-4 mr-2" />
                          AI识别并创建订单
                        </>
                      )}
                    </Button>

                    {/* 默认识别按钮 */}
                    <Button
                      onClick={() => {
                        const finalText = editedText || recognitionResult.text
                        console.log('🔄 用户选择默认识别，使用文字:')
                        console.log(finalText)
                        onTextRecognized(finalText)
                        cleanup()
                        onClose()
                      }}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      默认识别并创建订单
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {uploadedFiles.length > 0 && (
                <span>
                  已上传 {uploadedFiles.length} 个文件，
                  待识别 {uploadedFiles.filter(f => f.status === 'pending' || f.status === 'error').length} 个
                </span>
              )}
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  cleanup()
                  onClose()
                }}
                disabled={isProcessing}
              >
                取消
              </Button>
              {uploadedFiles.length > 0 && (
                <Button
                  onClick={startOCR}
                  disabled={isProcessing || uploadedFiles.filter(f => f.status === 'pending' || f.status === 'error').length === 0}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      识别中...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      开始识别 ({uploadedFiles.filter(f => f.status === 'pending' || f.status === 'error').length})
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
