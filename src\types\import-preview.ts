/**
 * 导入预览编辑相关的数据结构定义
 * 用于Excel导入后的预览编辑阶段
 */

// 导入类型枚举
export type ImportType = 'single_project' | 'multi_project'

// 编辑状态枚举
export type EditStatus = 'preview' | 'editing' | 'validating' | 'ready'

// 验证错误类型
export interface ValidationError {
  field: string
  message: string
  severity: 'error' | 'warning'
}

// 可编辑的风口项目数据结构（扩展自VentItem）
export interface EditableVentItem {
  id: string
  type: 'double_white_outlet' | 'double_black_outlet' | 'white_black_bottom_outlet' | 'white_black_bottom_return' |
        'white_return' | 'black_return' | 'white_linear' | 'black_linear' |
        'white_linear_return' | 'black_linear_return' | 'maintenance' |
        'high_end_white_outlet' | 'high_end_black_outlet' | 'high_end_white_return' | 'high_end_black_return' |
        'arrow_outlet' | 'arrow_return' | 'black_white_dual_outlet' | 'black_white_dual_return' |
        'wood_grain_outlet' | 'wood_grain_return' |
        'white_putty_outlet' | 'white_putty_return' | 'black_putty_outlet' | 'black_putty_return' |
        'white_gypsum_outlet' | 'white_gypsum_return' | 'black_gypsum_outlet' | 'black_gypsum_return'
  length: number
  width: number
  quantity: number
  unitPrice: number
  totalPrice: number
  notes?: string
  
  // 编辑状态相关字段
  isEditing?: boolean
  hasChanges?: boolean
  validationErrors?: ValidationError[]
  originalData?: Partial<EditableVentItem> // 保存原始导入数据，用于重置
}

// 可编辑的楼层数据结构（扩展自FloorData）
export interface EditableFloorData {
  id: string
  floorName: string
  vents: EditableVentItem[]
  totalPrice: number
  
  // 编辑状态相关字段
  isEditing?: boolean
  hasChanges?: boolean
  validationErrors?: ValidationError[]
  originalData?: Partial<EditableFloorData>
}

// 可编辑的项目数据结构（扩展自ProjectData）
export interface EditableProjectData {
  id: string
  orderDate: string
  projectAddress: string
  floors: EditableFloorData[]
  totalAmount: number
  
  // 编辑状态相关字段
  isEditing?: boolean
  hasChanges?: boolean
  isSelected?: boolean // 是否选中要创建订单
  validationErrors?: ValidationError[]
  originalData?: Partial<EditableProjectData>
}

// 导入预览状态管理
export interface ImportPreviewState {
  // 基本状态
  importType: ImportType
  editStatus: EditStatus
  isLoading: boolean
  
  // 数据状态
  projects: EditableProjectData[]
  selectedProjectIds: string[]
  
  // 单项目模式的楼层数据
  floors: EditableFloorData[]

  // 单项目模式的项目信息更新
  singleProjectUpdates?: Partial<EditableProjectData>

  // 编辑状态
  editingItemId?: string // 当前正在编辑的项目ID
  hasUnsavedChanges: boolean
  
  // 验证状态
  globalValidationErrors: ValidationError[]
  isValid: boolean
  
  // UI状态
  showPreview: boolean
  showValidationSummary: boolean
}

// 导入预览操作接口
export interface ImportPreviewActions {
  // 数据操作
  updateVentItem: (projectId: string, floorId: string, ventId: string, updates: Partial<EditableVentItem>) => void
  updateFloor: (projectId: string, floorId: string, updates: Partial<EditableFloorData>) => void
  updateProject: (projectId: string, updates: Partial<EditableProjectData>) => void
  
  // 项目选择
  toggleProjectSelection: (projectId: string) => void
  selectAllProjects: () => void
  deselectAllProjects: () => void
  
  // 编辑操作
  startEditing: (itemId: string) => void
  stopEditing: () => void
  resetItem: (projectId: string, floorId?: string, ventId?: string) => void
  resetAll: () => void
  
  // 行操作
  addVentItem: (projectId: string, floorId: string) => void
  removeVentItem: (projectId: string, floorId: string, ventId: string) => void
  duplicateVentItem: (projectId: string, floorId: string, ventId: string) => void
  
  // 楼层操作
  addFloor: (projectId: string) => void
  removeFloor: (projectId: string, floorId: string) => void
  
  // 批量操作
  batchUpdatePrice: (projectId: string, floorId: string, unitPrice: number) => void
  batchUpdateType: (projectId: string, floorId: string, type: string) => void
  
  // 验证操作
  validateItem: (projectId: string, floorId?: string, ventId?: string) => ValidationError[]
  validateAll: () => boolean
  
  // 导入确认
  confirmImport: () => Promise<boolean>
  cancelImport: () => void
}

// 导入预览配置
export interface ImportPreviewConfig {
  // 验证规则
  validation: {
    minLength: number
    maxLength: number
    minWidth: number
    maxWidth: number
    minQuantity: number
    maxQuantity: number
    minPrice: number
    maxPrice: number
  }
  
  // 默认值
  defaults: {
    floorName: string
    ventType: string
    unitPrice: number
    quantity: number
  }
  
  // UI配置
  ui: {
    showOriginalData: boolean
    enableBatchEdit: boolean
    autoSave: boolean
    autoSaveInterval: number
  }
}

// 导入预览上下文类型
export interface ImportPreviewContextType {
  state: ImportPreviewState
  actions: ImportPreviewActions
  config: ImportPreviewConfig
}

// 导入结果类型
export interface ImportResult {
  success: boolean
  createdOrderIds: string[]
  errors: ValidationError[]
  summary: {
    totalProjects: number
    totalOrders: number
    totalAmount: number
  }
}

// 导出默认配置
export const DEFAULT_IMPORT_PREVIEW_CONFIG: ImportPreviewConfig = {
  validation: {
    minLength: 10,
    maxLength: 9999,
    minWidth: 10,
    maxWidth: 9999,
    minQuantity: 1,
    maxQuantity: 9999,
    minPrice: 0,
    maxPrice: 99999
  },
  defaults: {
    floorName: '1楼',
    ventType: 'double_white_outlet',
    unitPrice: 0,
    quantity: 1
  },
  ui: {
    showOriginalData: false,
    enableBatchEdit: true,
    autoSave: false,
    autoSaveInterval: 30000
  }
}
