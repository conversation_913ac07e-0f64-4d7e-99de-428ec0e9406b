import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { getCurrentFactoryId } from '@/lib/utils/factory'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    // 尝试从URL参数获取工厂ID，如果没有则从session获取
    const { searchParams } = new URL(request.url)
    let factoryId = searchParams.get('factoryId') || getCurrentFactoryId()

    if (!factoryId) {
      // 如果还是没有，使用默认的工厂ID
      factoryId = 'cmbx0onx0000gun48lje6r02s'
    }

    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '无法获取工厂ID'
      }, { status: 400 })
    }

    console.log('📊 直接读取数据库原始数据:', factoryId)

    // 直接从数据库读取客户数据
    const clients = await prisma.client.findMany({
      where: { factoryId },
      select: {
        id: true,
        name: true,
        phone: true,
        referrerId: true,
        referrerName: true,
        referralReward: true,
        availableReward: true,
        pendingReward: true,
        referralCount: true,
        totalOrders: true,
        totalAmount: true,
        createdAt: true
      }
    })

    // 直接从数据库读取订单数据
    const orders = await prisma.order.findMany({
      where: { factoryId },
      select: {
        id: true,
        orderNumber: true,
        clientId: true,
        totalAmount: true,
        paidAmount: true,
        paymentStatus: true,
        createdAt: true,
        items: true
      }
    })

    console.log(`📋 读取到 ${clients.length} 个客户, ${orders.length} 个订单`)

    // 分析推荐关系
    const referralAnalysis: unknown[] = []
    
    for (const client of clients) {
      // 找出这个客户推荐的其他客户
      const referredClients = clients.filter(c => c.referrerId === client.id)
      
      if (referredClients.length > 0) {
        // 计算推荐奖励
        let calculatedReward = 0
        const referredDetails: unknown[] = []
        
        for (const referredClient of referredClients) {
          // 获取被推荐客户的订单
          const clientOrders = orders.filter(order => order.clientId === referredClient.id)
          const totalAmount = clientOrders.reduce((sum, order) => {
            const amount = typeof order.totalAmount === 'number' ? order.totalAmount : 
                          typeof order.totalAmount === 'object' && order.totalAmount ? 
                          parseFloat(order.totalAmount.toString()) : 0
            return sum + amount
          }, 0)
          
          const reward = totalAmount * 0.02 // 2%
          calculatedReward += reward
          
          referredDetails.push({
            clientId: referredClient.id,
            clientName: referredClient.name,
            clientPhone: referredClient.phone,
            orderCount: clientOrders.length,
            totalAmount: Math.round(totalAmount * 100) / 100,
            rewardGenerated: Math.round(reward * 100) / 100,
            orders: clientOrders.map(order => ({
              id: order.id,
              orderNumber: order.orderNumber,
              totalAmount: typeof order.totalAmount === 'number' ? order.totalAmount : 
                          typeof order.totalAmount === 'object' && order.totalAmount ? 
                          parseFloat(order.totalAmount.toString()) : 0,
              createdAt: order.createdAt
            }))
          })
        }
        
        // 获取数据库中的奖励数据
        const dbReward = typeof client.referralReward === 'number' ? client.referralReward : 
                         typeof client.referralReward === 'object' && client.referralReward ? 
                         parseFloat(client.referralReward.toString()) : 0
        
        const dbAvailable = typeof client.availableReward === 'number' ? client.availableReward : 
                           typeof client.availableReward === 'object' && client.availableReward ? 
                           parseFloat(client.availableReward.toString()) : 0
        
        const dbPending = typeof client.pendingReward === 'number' ? client.pendingReward : 
                         typeof client.pendingReward === 'object' && client.pendingReward ? 
                         parseFloat(client.pendingReward.toString()) : 0

        referralAnalysis.push({
          referrerId: client.id,
          referrerName: client.name,
          referrerPhone: client.phone,
          referredCount: referredClients.length,
          calculatedReward: Math.round(calculatedReward * 100) / 100,
          databaseReward: Math.round(dbReward * 100) / 100,
          availableReward: Math.round(dbAvailable * 100) / 100,
          pendingReward: Math.round(dbPending * 100) / 100,
          referralCount: client.referralCount || 0,
          hasDiscrepancy: Math.abs(calculatedReward - dbReward) > 0.01,
          discrepancyAmount: Math.round((calculatedReward - dbReward) * 100) / 100,
          referredDetails
        })
      }
    }

    // 找出被推荐的客户
    const referredClients = clients.filter(client => client.referrerId)
    const referredAnalysis = referredClients.map(client => {
      const clientOrders = orders.filter(order => order.clientId === client.id)
      const totalAmount = clientOrders.reduce((sum, order) => {
        const amount = typeof order.totalAmount === 'number' ? order.totalAmount : 
                      typeof order.totalAmount === 'object' && order.totalAmount ? 
                      parseFloat(order.totalAmount.toString()) : 0
        return sum + amount
      }, 0)
      
      return {
        clientId: client.id,
        clientName: client.name,
        clientPhone: client.phone,
        referrerId: client.referrerId,
        referrerName: client.referrerName,
        orderCount: clientOrders.length,
        totalAmount: Math.round(totalAmount * 100) / 100,
        shouldGenerateReward: Math.round(totalAmount * 0.02 * 100) / 100,
        orders: clientOrders.map(order => ({
          id: order.id,
          orderNumber: order.orderNumber,
          totalAmount: typeof order.totalAmount === 'number' ? order.totalAmount : 
                      typeof order.totalAmount === 'object' && order.totalAmount ? 
                      parseFloat(order.totalAmount.toString()) : 0,
          createdAt: order.createdAt
        }))
      }
    })

    const result = {
      factoryId,
      timestamp: new Date().toISOString(),
      summary: {
        totalClients: clients.length,
        totalOrders: orders.length,
        referrersCount: referralAnalysis.length,
        referredClientsCount: referredClients.length,
        clientsWithDiscrepancy: referralAnalysis.filter(r => r.hasDiscrepancy).length
      },
      referrers: referralAnalysis,
      referredClients: referredAnalysis,
      rawClients: clients.map(client => ({
        id: client.id,
        name: client.name,
        phone: client.phone,
        referrerId: client.referrerId,
        referrerName: client.referrerName,
        referralReward: typeof client.referralReward === 'number' ? client.referralReward : 
                       typeof client.referralReward === 'object' && client.referralReward ? 
                       parseFloat(client.referralReward.toString()) : 0,
        availableReward: typeof client.availableReward === 'number' ? client.availableReward : 
                        typeof client.availableReward === 'object' && client.availableReward ? 
                        parseFloat(client.availableReward.toString()) : 0,
        pendingReward: typeof client.pendingReward === 'number' ? client.pendingReward : 
                      typeof client.pendingReward === 'object' && client.pendingReward ? 
                      parseFloat(client.pendingReward.toString()) : 0,
        referralCount: client.referralCount || 0,
        totalOrders: client.totalOrders || 0,
        totalAmount: typeof client.totalAmount === 'number' ? client.totalAmount : 
                    typeof client.totalAmount === 'object' && client.totalAmount ? 
                    parseFloat(client.totalAmount.toString()) : 0
      })),
      rawOrders: orders.map(order => ({
        id: order.id,
        orderNumber: order.orderNumber,
        clientId: order.clientId,
        totalAmount: typeof order.totalAmount === 'number' ? order.totalAmount : 
                    typeof order.totalAmount === 'object' && order.totalAmount ? 
                    parseFloat(order.totalAmount.toString()) : 0,
        paidAmount: typeof order.paidAmount === 'number' ? order.paidAmount : 
                   typeof order.paidAmount === 'object' && order.paidAmount ? 
                   parseFloat(order.paidAmount.toString()) : 0,
        paymentStatus: order.paymentStatus,
        createdAt: order.createdAt,
        itemsCount: Array.isArray(order.items) ? order.items.length : 0
      }))
    }

    console.log('✅ 数据读取完成:', {
      客户数: result.summary.totalClients,
      订单数: result.summary.totalOrders,
      推荐人数: result.summary.referrersCount,
      被推荐客户数: result.summary.referredClientsCount,
      有差异的客户: result.summary.clientsWithDiscrepancy
    })

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('❌ 读取数据失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '读取数据失败'
    }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}
