"use client"

import { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Upload,
  Image as ImageIcon,
  X,
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle,
  Eye,
  Trash2
} from "lucide-react"

// OCR识别结果接口
interface OCRResult {
  success: boolean
  text: string
  words: Array<{
    words: string
    location: {
      left: number
      top: number
      width: number
      height: number
    }
  }>
  error?: string
}

interface OCRUploadProps {
  onTextRecognized: (text: string) => void
  onClose: () => void
  maxFiles?: number
  acceptedTypes?: string[]
}

interface UploadedFile {
  file: File
  preview: string
  status: 'pending' | 'processing' | 'success' | 'error'
  result?: OCRResult
  id: string
}

interface ProcessingProgress {
  current: number
  total: number
  currentFileName: string
}

export function OCRUpload({ 
  onTextRecognized, 
  onClose, 
  maxFiles = 5,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/bmp', 'image/gif']
}: OCRUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [useAccurate, setUseAccurate] = useState(false)
  const [processingProgress, setProcessingProgress] = useState<ProcessingProgress | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 生成唯一ID
  const generateId = () => Math.random().toString(36).substr(2, 9)

  // 创建文件预览URL
  const createPreview = (file: File): string => {
    return URL.createObjectURL(file)
  }

  // 验证文件
  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return '不支持的文件类型，请上传图片文件'
    }
    if (file.size > 4 * 1024 * 1024) {
      return '文件大小不能超过4MB'
    }
    return null
  }

  // 处理文件选择
  const handleFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files)
    const newFiles: UploadedFile[] = []

    for (const file of fileArray) {
      // 检查文件数量限制
      if (uploadedFiles.length + newFiles.length >= maxFiles) {
        alert(`最多只能上传${maxFiles}张图片`)
        break
      }

      // 验证文件
      const error = validateFile(file)
      if (error) {
        alert(`文件 ${file.name}: ${error}`)
        continue
      }

      // 检查是否已存在相同文件
      const isDuplicate = uploadedFiles.some(uf => 
        uf.file.name === file.name && uf.file.size === file.size
      )
      if (isDuplicate) {
        alert(`文件 ${file.name} 已存在`)
        continue
      }

      newFiles.push({
        file,
        preview: createPreview(file),
        status: 'pending',
        id: generateId()
      })
    }

    if (newFiles.length > 0) {
      setUploadedFiles(prev => [...prev, ...newFiles])
    }
  }, [uploadedFiles, maxFiles])

  // 拖拽处理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files)
    }
  }, [handleFiles])

  // 文件选择
  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files)
    }
    // 清空input值，允许重复选择相同文件
    e.target.value = ''
  }

  // 删除文件
  const removeFile = (id: string) => {
    setUploadedFiles(prev => {
      const updated = prev.filter(file => file.id !== id)
      // 释放预览URL
      const fileToRemove = prev.find(file => file.id === id)
      if (fileToRemove) {
        URL.revokeObjectURL(fileToRemove.preview)
      }
      return updated
    })
  }

  // 开始OCR识别
  const startOCR = async () => {
    if (uploadedFiles.length === 0) {
      alert('请先上传图片')
      return
    }

    setIsProcessing(true)
    setProcessingProgress({
      current: 0,
      total: uploadedFiles.length,
      currentFileName: uploadedFiles[0]?.file.name || ''
    })

    try {
      // 更新所有文件状态为处理中
      setUploadedFiles(prev =>
        prev.map(file => ({ ...file, status: 'processing' as const }))
      )

      // 使用API路由进行OCR识别
      const formData = new FormData()
      uploadedFiles.forEach(uf => {
        formData.append('files', uf.file)
      })
      formData.append('useAccurate', useAccurate.toString())

      console.log(`🔍 发送OCR请求: ${uploadedFiles.length}个文件，${useAccurate ? '高精度' : '通用'}模式`)

      const response = await fetch('/api/ocr', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'OCR识别失败')
      }

      const { results, mergedText, statistics } = result.data

      // 更新文件状态和结果
      setUploadedFiles(prev =>
        prev.map((file, index) => ({
          ...file,
          status: results[index]?.success ? 'success' : 'error',
          result: results[index]
        }))
      )

      console.log('📊 OCR识别统计:', statistics)

      if (mergedText && mergedText.trim()) {
        console.log('🎯 OCR识别完成，识别到的文本:')
        console.log(mergedText)

        // 显示识别统计信息
        const message = `✅ 识别完成！\n` +
          `📸 处理图片: ${statistics.total}张\n` +
          `✅ 成功: ${statistics.success}张\n` +
          `❌ 失败: ${statistics.failed}张\n` +
          `📝 识别文字: ${statistics.totalWords}行，共${statistics.totalChars}个字符`

        alert(message)
        onTextRecognized(mergedText)
      } else {
        alert('未能识别到有效文字，请检查图片质量或重新上传')
      }

    } catch (error) {
      console.error('❌ OCR处理失败:', error)

      let errorMessage = 'OCR识别失败'
      if (error instanceof Error) {
        errorMessage += `: ${error.message}`
      }

      alert(errorMessage)

      // 更新所有文件状态为错误
      setUploadedFiles(prev =>
        prev.map(file => ({ ...file, status: 'error' as const }))
      )
    } finally {
      setIsProcessing(false)
      setProcessingProgress(null)
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: UploadedFile['status']) => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <ImageIcon className="h-4 w-4 text-gray-400" />
    }
  }

  // 清理预览URL
  const cleanup = () => {
    uploadedFiles.forEach(file => {
      URL.revokeObjectURL(file.preview)
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                百度OCR文字识别
              </CardTitle>
              <CardDescription>
                上传图片进行文字识别，支持中英文混合识别
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                cleanup()
                onClose()
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 上传区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              拖拽图片到此处或点击上传
            </p>
            <p className="text-sm text-gray-500 mb-4">
              支持 JPG、PNG、BMP、GIF 格式，单个文件不超过4MB
            </p>
            <Button onClick={handleFileSelect} disabled={isProcessing}>
              <Upload className="h-4 w-4 mr-2" />
              选择图片
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept={acceptedTypes.join(',')}
              onChange={handleFileInputChange}
              className="hidden"
            />
          </div>

          {/* 识别选项 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="accurate"
                checked={useAccurate}
                onChange={(e) => setUseAccurate(e.target.checked)}
                disabled={isProcessing}
                className="rounded"
              />
              <label htmlFor="accurate" className="text-sm text-gray-700">
                使用高精度识别（更准确但速度较慢）
              </label>
            </div>
            <div className="text-sm text-gray-500">
              已上传: {uploadedFiles.length}/{maxFiles}
            </div>
          </div>

          {/* 处理进度显示 */}
          {processingProgress && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-800">
                  正在识别图片...
                </span>
                <span className="text-sm text-blue-600">
                  {processingProgress.current}/{processingProgress.total}
                </span>
              </div>
              <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${(processingProgress.current / processingProgress.total) * 100}%`
                  }}
                />
              </div>
              <div className="text-xs text-blue-600 truncate">
                当前: {processingProgress.currentFileName}
              </div>
            </div>
          )}

          {/* 文件列表 */}
          {uploadedFiles.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">上传的图片</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {uploadedFiles.map((uploadedFile) => (
                  <div key={uploadedFile.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(uploadedFile.status)}
                        <span className="text-sm font-medium truncate">
                          {uploadedFile.file.name}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(uploadedFile.id)}
                        disabled={isProcessing}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <img
                        src={uploadedFile.preview}
                        alt="预览"
                        className="w-16 h-16 object-cover rounded border"
                      />
                      <div className="flex-1 text-xs text-gray-500">
                        <div>大小: {(uploadedFile.file.size / 1024).toFixed(1)}KB</div>
                        {uploadedFile.result && (
                          <div className="mt-1">
                            {uploadedFile.result.success 
                              ? `识别到 ${uploadedFile.result.words.length} 行文字`
                              : `错误: ${uploadedFile.result.error}`
                            }
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => {
                cleanup()
                onClose()
              }}
              disabled={isProcessing}
            >
              取消
            </Button>
            <Button
              onClick={startOCR}
              disabled={uploadedFiles.length === 0 || isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  识别中...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  开始识别
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
