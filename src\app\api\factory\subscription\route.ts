/**
 * 🇨🇳 风口云平台 - 工厂订阅信息 API
 * 工厂用户获取自己的订阅信息
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withFactoryAuth } from '@/lib/middleware/auth'
import { isFactoryExpired, isFactoryExpiringSoon, calculateTotalSuspendedTime } from '@/lib/utils/factory-subscription'

export const GET = withFactoryAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId') || user.factoryId

    if (!factoryId) {
      return NextResponse.json(
        { error: '缺少工厂ID参数' },
        { status: 400 }
      )
    }

    // 验证用户是否有权限访问该工厂信息
    if (user.factoryId !== factoryId) {
      return NextResponse.json(
        { error: '无权限访问该工厂信息' },
        { status: 403 }
      )
    }

    // 获取工厂信息
    const factory = await db.getFactoryById(factoryId)
    if (!factory) {
      return NextResponse.json(
        { error: '工厂不存在' },
        { status: 404 }
      )
    }

    // 检查工厂状态
    const isExpired = isFactoryExpired(factory)
    const isExpiringSoon = isFactoryExpiringSoon(factory)
    
    // 计算剩余天数（考虑暂停时间）
    let remainingDays = null
    if (factory.subscriptionEnd && !factory.isPermanent && factory.subscriptionType !== 'permanent') {
      const now = new Date()
      const endDate = new Date(factory.subscriptionEnd)

      // 计算暂停时间累计，调整实际结束时间
      const totalSuspendedMs = calculateTotalSuspendedTime(factory)

      // 实际结束时间 = 原结束时间 + 暂停时间累计
      const adjustedEndDate = new Date(endDate.getTime() + totalSuspendedMs)
      const remainingMs = adjustedEndDate.getTime() - now.getTime()
      remainingDays = Math.ceil(remainingMs / (1000 * 60 * 60 * 24))
    }

    // 确定警告级别
    let warningLevel: 'none' | 'warning' | 'critical' | 'expired' = 'none'
    if (isExpired) {
      warningLevel = 'expired'
    } else if (remainingDays !== null) {
      if (remainingDays <= 3) {
        warningLevel = 'critical'
      } else if (remainingDays <= 7) {
        warningLevel = 'warning'
      }
    }

    // 构建响应数据
    const responseData = {
      factory: {
        id: factory.id,
        name: factory.name,
        code: factory.code,
        status: factory.status,
        subscriptionType: factory.subscriptionType,
        subscriptionStart: factory.subscriptionStart,
        subscriptionEnd: factory.subscriptionEnd,
        firstLoginAt: factory.firstLoginAt,
        isPermanent: factory.isPermanent,
        suspendedAt: factory.suspendedAt,
        suspendedReason: factory.suspendedReason
      },
      statusCheck: {
        canLogin: !isExpired && factory.status !== 'suspended',
        reason: isExpired ? '订阅已过期' : factory.status === 'suspended' ? '工厂已暂停' : undefined,
        status: factory.status,
        isExpired,
        remainingDays,
        warningLevel
      },
      remainingDays
    }

    console.log('📊 订阅信息API返回数据:', JSON.stringify(responseData, null, 2))

    return NextResponse.json({
      success: true,
      data: responseData
    })

  } catch (error) {
    console.error('❌ 获取工厂订阅信息失败:', error)
    return NextResponse.json(
      { error: '获取工厂订阅信息失败' },
      { status: 500 }
    )
  }
})
