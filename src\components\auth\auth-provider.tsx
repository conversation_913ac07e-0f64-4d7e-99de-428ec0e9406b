'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/store/auth'
import { tokenMonitorService } from '@/lib/services/token-monitor.service'

interface AuthProviderProps {
  children: React.ReactNode
}

/**
 * 认证状态提供者组件
 * 负责在应用启动时恢复认证状态
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const [isHydrated, setIsHydrated] = useState(false)
  const { hydrate, isAuthenticated, accessToken, login } = useAuthStore()

  useEffect(() => {
    const initializeAuth = async () => {
      console.log('🚀 AuthProvider: 开始初始化认证状态...')

      try {
        // 检查localStorage中的数据
        const storedToken = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null
        const storedUser = typeof window !== 'undefined' ? localStorage.getItem('user') : null
        const storedRole = typeof window !== 'undefined' ? localStorage.getItem('role') : null
        const storedRefreshToken = typeof window !== 'undefined' ? localStorage.getItem('refreshToken') : null
        const storedFactoryId = typeof window !== 'undefined' ? localStorage.getItem('factoryId') : null

        console.log('📋 AuthProvider: 本地存储检查:', {
          hasStoredToken: !!storedToken,
          hasStoredUser: !!storedUser,
          storedRole,
          storedFactoryId: storedFactoryId || 'null'
        })

        // 手动调用hydrate来恢复状态
        hydrate()

        // 等待状态恢复 - 增加等待时间
        await new Promise(resolve => setTimeout(resolve, 1000))

        console.log('📋 AuthProvider: 认证状态检查:', {
          isAuthenticated,
          hasAccessToken: !!accessToken,
          hasStoredToken: !!storedToken,
          hasStoredUser: !!storedUser,
          storedRole
        })

        // 如果localStorage有数据但Zustand状态没有恢复，强制恢复
        if (storedToken && storedUser && storedRole && !isAuthenticated) {
          console.log('🔄 AuthProvider: 检测到状态不一致，强制恢复认证状态...')

          try {
            const user = JSON.parse(storedUser)

            // 直接调用login方法强制恢复状态
            login(user, storedRole as any, {
              accessToken: storedToken,
              refreshToken: storedRefreshToken || '',
              tokenType: 'Bearer',
              expiresIn: '7d'
            }, storedFactoryId || undefined)

            console.log('✅ AuthProvider: 认证状态强制恢复成功:', user.name)
          } catch (parseError) {
            console.error('❌ AuthProvider: 解析存储的用户信息失败:', parseError)
            // 清除损坏的数据
            localStorage.removeItem('accessToken')
            localStorage.removeItem('refreshToken')
            localStorage.removeItem('user')
            localStorage.removeItem('role')
            localStorage.removeItem('factoryId')
          }
        }

        console.log('✅ AuthProvider: 认证状态初始化完成')

        // 最终检查：如果用户已登录，启动 Token 监控
        const finalAuthState = useAuthStore.getState()
        if (finalAuthState.isAuthenticated) {
          console.log('🔄 AuthProvider: 启动 Token 监控服务')
          tokenMonitorService.onUserLogin()
        }
      } catch (error) {
        console.error('❌ AuthProvider: 认证状态初始化失败:', error)
      } finally {
        setIsHydrated(true)
      }
    }

    initializeAuth()
  }, [])

  // 在认证状态初始化完成前显示加载状态
  if (!isHydrated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
