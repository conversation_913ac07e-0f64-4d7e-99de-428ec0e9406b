/**
 * 调试API - 检查管理员数据和认证
 */

import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { db } from '@/lib/database'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 检查管理员数据和数据库服务...')

    // 测试数据库服务
    console.log('🔧 测试数据库服务类型:', typeof db)
    console.log('🔧 数据库服务方法:', Object.keys(db))

    // 测试数据库服务的authenticateAdmin方法
    try {
      const dbAuthResult = await db.authenticateAdmin('admin', 'admin@yhg2025')
      console.log('🔧 数据库服务认证结果:', dbAuthResult)
    } catch (dbError) {
      console.error('❌ 数据库服务认证失败:', dbError)
    }

    // 查询所有管理员
    const admins = await prisma.admin.findMany({
      select: {
        id: true,
        username: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        passwordHash: true,
        createdAt: true
      }
    })

    console.log('📊 管理员数据:', admins)

    // 测试密码验证
    const testResults = []
    for (const admin of admins) {
      const testPassword = 'admin@yhg2025'
      const isValid = await bcrypt.compare(testPassword, admin.passwordHash)
      testResults.push({
        username: admin.username,
        testPassword,
        isValid,
        passwordHashLength: admin.passwordHash.length
      })
    }

    return NextResponse.json({
      success: true,
      adminCount: admins.length,
      dbServiceType: typeof db,
      dbServiceMethods: Object.keys(db).slice(0, 10), // 只显示前10个方法
      admins: admins.map(admin => ({
        ...admin,
        passwordHash: admin.passwordHash.substring(0, 20) + '...' // 只显示前20个字符
      })),
      passwordTests: testResults
    })

  } catch (error) {
    console.error('❌ 调试失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json()
    console.log('🔐 测试管理员认证:', username)

    // 直接使用Prisma查询管理员
    const admin = await prisma.admin.findFirst({
      where: {
        username,
        isActive: true
      }
    })

    if (!admin) {
      return NextResponse.json({
        success: false,
        error: '管理员不存在或已禁用'
      }, { status: 401 })
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, admin.passwordHash)
    if (!isValidPassword) {
      return NextResponse.json({
        success: false,
        error: '密码错误'
      }, { status: 401 })
    }

    // 返回成功结果（不包含密码）
    const { passwordHash, ...adminInfo } = admin

    return NextResponse.json({
      success: true,
      message: '认证成功',
      admin: adminInfo
    })

  } catch (error) {
    console.error('❌ 认证测试失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
