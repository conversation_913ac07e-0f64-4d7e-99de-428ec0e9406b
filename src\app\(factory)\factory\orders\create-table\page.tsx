"use client"

import { useState, useEffect, useRef, use<PERSON><PERSON>back } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import {
  Plus,
  Trash2,
  Save,
  ArrowLeft,
  Copy,
  Upload,
  FileSpreadsheet,
  ClipboardPaste,
  DollarSign,
  Settings,
  FileText,
  Grid3X3,
  ChevronDown
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { getProductTypeName } from "@/lib/pricing"
import { FactoryRouteGuard } from "@/components/auth/route-guard"
import { db } from "@/lib/database"
import { getCurrentFactoryId, getCurrentUser, generateOrderNumber } from "@/lib/utils/factory"
import { IntelligentOCRUpload } from "@/components/ocr/intelligent-ocr-upload"
import { OCRFeedbackPanel } from "@/components/ocr/ocr-feedback-panel"
import AIPasteRecognition from "@/components/ai/ai-paste-recognition"
import PerformanceMonitor from "@/components/debug/performance-monitor"
import { getDefaultUnitPricesAsync } from "@/lib/utils/factory-settings"

import { isRegularVent, calculateVentPrice } from '@/lib/utils/vent-type-classifier'
import {
  smartDimensionRecognition,
  parseDimensionString,
  parseMultipleDimensions,
  updateDimensionsWithSmartRecognition,
  extractDimensionsFromItem
} from '@/lib/utils/dimension-utils'
import ImportPreviewEditor from '@/components/import/import-preview-editor'
import { useImportPreview } from '@/hooks/use-import-preview'
import type { OrderItem, Order } from "@/types"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { recognizeIntent, recognizeIntentWithTable } from '@/lib/nlp/intent-recognition'

// 楼层数据结构
interface FloorData {
  id: string
  floorName: string
  vents: VentItem[]
  totalPrice: number
}

// 客户数据结构
interface ClientData {
  id: string
  name: string
  phone: string
  address?: string
  factoryId?: string
}

// 新客户表单数据结构
interface NewClientFormData {
  name: string
  phone: string
  email: string
  company: string
  address?: string
  referrerId: string
  referrerName: string
}

// 临时楼层数据结构（用于解析过程）
interface TempFloorData {
  name: string
  vents: Omit<VentItem, 'id' | 'unitPrice' | 'totalPrice'>[]
}

// 临时项目数据结构（用于解析过程）
interface TempProjectData {
  name: string
  floors: TempFloorData[]
}

// 项目数据结构（用于多项目导入）
interface ProjectData {
  id: string
  orderDate: string
  projectAddress: string
  floors: FloorData[]
  totalAmount: number
}

// 风口项目数据结构
interface VentItem {
  id: string
  type: 'double_white_outlet' | 'double_black_outlet' | 'white_black_bottom_outlet' | 'white_black_bottom_return' |
        'white_return' | 'black_return' | 'white_linear' | 'black_linear' |
        'white_linear_return' | 'black_linear_return' | 'maintenance' |
        // 高端风口类型
        'high_end_white_outlet' | 'high_end_black_outlet' | 'high_end_white_return' | 'high_end_black_return' |
        'arrow_outlet' | 'arrow_return' | 'claw_outlet' | 'claw_return' |
        'black_white_dual_outlet' | 'black_white_dual_return' |
        'wood_grain_outlet' | 'wood_grain_return' |
        'white_putty_outlet' | 'white_putty_return' | 'black_putty_outlet' | 'black_putty_return' |
        'white_gypsum_outlet' | 'white_gypsum_return' | 'black_gypsum_outlet' | 'black_gypsum_return'
  length: number
  width: number
  quantity: number
  unitPrice: number
  totalPrice: number
  notes?: string // 备注信息
}

// 风口数据结构（用于导入解析）
interface VentData {
  id: string
  originalInfo: string
  length: number
  width: number
  quantity: number
  color: string
  type: VentItem['type']
  unitPrice: number
  totalPrice: number
  notes?: string
}



function CreateTableOrderPage() {
  const router = useRouter()

  // 防重复保存状态 - 必须在所有使用它的地方之前定义
  const [isSaving, setIsSaving] = useState(false)

  const [selectedClient, setSelectedClient] = useState<string | ClientData | null>("")
  const [floors, setFloors] = useState<FloorData[]>([])
  const [notes, setNotes] = useState("")
  const [projectAddress, setProjectAddress] = useState("") // 项目地址单独管理
  const [showClientForm, setShowClientForm] = useState(false)

  // ID生成器 - 确保唯一性
  const idCounterRef = useRef(0)
  const generateUniqueId = (prefix: string) => {
    const newId = `${prefix}-${Date.now()}-${idCounterRef.current}`
    idCounterRef.current += 1
    return newId
  }
  const [clientSearchTerm, setClientSearchTerm] = useState("") // 客户搜索关键字
  const [showClientDropdown, setShowClientDropdown] = useState(false) // 控制下拉框显示
  const [newClientData, setNewClientData] = useState<NewClientFormData>({
    name: "",
    phone: "",
    email: "",
    company: "",
    address: "",
    referrerId: "",
    referrerName: ""
  })
  const [phoneError, setPhoneError] = useState("")
  const [batchPriceInput, setBatchPriceInput] = useState("") // 批量设置单价的输入值
  const [showBatchPriceModal, setShowBatchPriceModal] = useState<string | null>(null) // 显示批量设置单价模态框的楼层ID

  // 全局批量设置单价相关状态
  const [showGlobalBatchPriceModal, setShowGlobalBatchPriceModal] = useState(false)
  const [globalBatchPriceInput, setGlobalBatchPriceInput] = useState("")

  // 批量更改风口类型相关状态
  const [showBatchOutletTypeModal, setShowBatchOutletTypeModal] = useState<string | null>(null) // 显示批量更改出风口类型模态框的楼层ID
  const [batchOutletType, setBatchOutletType] = useState<VentItem['type']>('double_white_outlet') // 批量更改的出风口类型
  const [showBatchReturnTypeModal, setShowBatchReturnTypeModal] = useState<string | null>(null) // 显示批量更改回风口类型模态框的楼层ID
  const [batchReturnType, setBatchReturnType] = useState<VentItem['type']>('white_return') // 批量更改的回风口类型

  // 全局批量更改风口类型相关状态
  const [showGlobalBatchOutletTypeModal, setShowGlobalBatchOutletTypeModal] = useState(false)
  const [globalBatchOutletType, setGlobalBatchOutletType] = useState<VentItem['type']>('double_white_outlet')
  const [showGlobalBatchReturnTypeModal, setShowGlobalBatchReturnTypeModal] = useState(false)
  const [globalBatchReturnType, setGlobalBatchReturnType] = useState<VentItem['type']>('white_return')

  const [existingClients, setExistingClients] = useState<ClientData[]>([])
  const [importing, setImporting] = useState(false)

  // 智能粘贴识别相关状态
  const [showMultiPasteDialog, setShowMultiPasteDialog] = useState(false)
  const [multiPasteContent, setMultiPasteContent] = useState("")
  const [debugMode, setDebugMode] = useState(false)

  // 智能OCR相关状态
  const [showIntelligentOCRDialog, setShowIntelligentOCRDialog] = useState(false)
  const [currentOCRSessionId, setCurrentOCRSessionId] = useState<string | null>(null)
  const [showOCRFeedback, setShowOCRFeedback] = useState(false)

  // AI知识库相关功能已移除

  // AI智能识别相关状态
  const [showAIRecognitionDialog, setShowAIRecognitionDialog] = useState(false)

  // 🔧 新增：反馈面板显示控制（已禁用）
  const checkShouldShowFeedback = (): boolean => {
    // 直接返回 false，禁用反馈面板
    console.log('🔕 反馈面板已禁用，不再显示')
    return false

    // 以下代码已注释，如需重新启用反馈面板可取消注释
    /*
    // 检查本地存储中的反馈频率设置
    const feedbackSettings = localStorage.getItem('ocr_feedback_settings')
    if (feedbackSettings) {
      const settings = JSON.parse(feedbackSettings)

      // 如果用户选择了"不再显示"，则不显示
      if (settings.neverShow) {
        return false
      }

      // 检查上次反馈时间，避免频繁显示
      const lastFeedbackTime = settings.lastFeedbackTime || 0
      const now = Date.now()
      const oneHour = 60 * 60 * 1000 // 1小时

      // 如果距离上次反馈不到1小时，不显示
      if (now - lastFeedbackTime < oneHour) {
        console.log('🔕 反馈面板：距离上次反馈不到1小时，跳过显示')
        return false
      }
    }

    // 随机显示：只有20%的概率显示反馈面板
    const shouldShow = Math.random() < 0.2
    console.log(`🎲 反馈面板随机显示判断: ${shouldShow ? '显示' : '跳过'}`)

    return shouldShow
    */
  }

  // 导入类型状态
  const [importType, setImportType] = useState<'single' | 'multi' | null>(null)

  // 预览编辑状态管理
  const { state: previewState, actions: previewActions, statistics } = useImportPreview()

  // 批量备注管理状态
  const [selectedVentIds, setSelectedVentIds] = useState<Set<string>>(new Set())
  const [showBatchNotesModal, setShowBatchNotesModal] = useState(false)
  const [batchNotesText, setBatchNotesText] = useState('')
  const [batchNotesMode, setBatchNotesMode] = useState<'overwrite' | 'append'>('overwrite')

  // 默认单价设置状态
  const [defaultUnitPrices, setDefaultUnitPrices] = useState({
    // 常规风口 (元/㎡)
    double_white_outlet: 150,
    double_black_outlet: 150,
    white_black_bottom_outlet: 150,
    white_black_bottom_return: 150,
    white_return: 150,
    black_return: 150,
    white_linear: 150,
    black_linear: 150,
    white_linear_return: 150,
    black_linear_return: 150,
    maintenance: 150,
    // 高端风口 (元/m) - 统一命名
    high_end_white_outlet: 80,
    high_end_black_outlet: 80,
    high_end_white_return: 80,
    high_end_black_return: 80,
    arrow_outlet: 90,                     // 箭型出风口
    arrow_return: 90,                     // 箭型回风口
    claw_outlet: 100,                     // 爪型出风口
    claw_return: 100,                     // 爪型回风口
    black_white_dual_outlet: 85,          // 黑白双色出风口
    black_white_dual_return: 85,          // 🔧 黑白双色回风口
    wood_grain_outlet: 120,
    wood_grain_return: 120,
    white_putty_outlet: 110,              // 🔧 白色腻子粉出风口
    white_putty_return: 110,              // 🔧 白色腻子粉回风口
    black_putty_outlet: 110,              // 🔧 黑色腻子粉出风口
    black_putty_return: 110,              // 🔧 黑色腻子粉回风口
    white_gypsum_outlet: 130,
    white_gypsum_return: 130,
    black_gypsum_outlet: 130,
    black_gypsum_return: 130
  })

  // 加载现有客户数据
  const loadExistingClients = async () => {
    try {
      const factoryId = getCurrentFactoryId()
      if (factoryId && db && typeof db.getClientsByFactoryId === 'function') {
        const clients = await db.getClientsByFactoryId(factoryId)
        setExistingClients(clients)
      }
    } catch (error) {
      console.error('❌ 加载客户数据失败:', error)
    }
  }

  // AI知识库相关功能已移除

  // 组件挂载时加载客户数据和默认单价
  useEffect(() => {
    loadExistingClients()

    // 加载工厂默认单价设置（异步版本，从数据库读取）
    const loadDefaultUnitPrices = async () => {
      try {
        const factoryId = getCurrentFactoryId()
        if (factoryId) {
          const prices = await getDefaultUnitPricesAsync(factoryId)
          setDefaultUnitPrices(prices)
          console.log('✅ 录单页面加载默认单价设置（从数据库）:', prices)
        }
      } catch (error) {
        console.error('❌ 加载默认单价设置失败:', error)
      }
    }
    loadDefaultUnitPrices()
  }, [])

  // 保存订单函数 - 必须在 useEffect 之前定义
  const saveOrder = useCallback(async () => {
    // 防止重复保存
    if (isSaving) {
      console.log('⚠️ 订单正在保存中，忽略重复请求')
      return
    }

    if (!selectedClient) {
      alert('请选择客户')
      return
    }

    setIsSaving(true)
    console.log('🔒 开始保存订单，设置保存状态为true')

    // 如果是新客户，验证客户信息
    if (selectedClient === 'new') {
      if (!newClientData.name || !newClientData.name.trim()) {
        alert('请填写客户名称或公司名称')
        setIsSaving(false)
        return
      }
      if (!newClientData.phone || !newClientData.phone.trim()) {
        alert('请填写联系电话')
        setIsSaving(false)
        return
      }
      if (!validatePhone(newClientData.phone)) {
        alert('电话号码格式不正确，请输入正确的11位手机号码（以1开头）')
        setIsSaving(false)
        return
      }
    }

    if (!projectAddress || !projectAddress.trim()) {
      alert('请填写项目地址')
      setIsSaving(false)
      return
    }

    if (floors.length === 0) {
      alert('请添加至少一个楼层')
      setIsSaving(false)
      return
    }

    // 验证数据并自动修复空楼层名称
    let hasErrors = false
    setFloors(prevFloors => prevFloors.map(floor => {
      // 如果楼层名称为空，自动设置为"1楼"
      if (!floor.floorName || !floor.floorName.trim()) {
        return { ...floor, floorName: '1楼' }
      }
      return floor
    }))

    floors.forEach(floor => {

      floor.vents.forEach(vent => {
        if (vent.length <= 0 || vent.width <= 0) {
          alert(`${floor.floorName} 存在尺寸为0的风口，请检查`)
          hasErrors = true
          return
        }
        if (vent.unitPrice <= 0) {
          alert(`${floor.floorName} 存在单价为0的风口，请检查`)
          hasErrors = true
          return
        }
      })
    })

    if (hasErrors) {
      setIsSaving(false)
      return
    }

    try {
      console.log('🚀 开始保存订单...')

      // 保存前进行智能尺寸转换
      console.log('🔄 保存前进行智能尺寸转换...')
      floors.forEach(floor => {
        floor.vents.forEach(vent => {
          if (vent.length > 0 && vent.width > 0) {
            const smartDimensions = smartDimensionRecognition(vent.length, vent.width)
            if (smartDimensions.length !== vent.length || smartDimensions.width !== vent.width) {
              console.log(`🔄 保存前智能转换: ${vent.length}×${vent.width} → ${smartDimensions.length}×${smartDimensions.width}`)
              updateVentSync(floor.id, vent.id, {
                length: smartDimensions.length,
                width: smartDimensions.width
              })
            }
          }
        })
      })
      console.log('✅ 智能尺寸转换完成')

      // 获取当前工厂ID和用户信息
      const currentFactoryId = getCurrentFactoryId()
      const currentUser = getCurrentUser()

      console.log('📋 工厂信息:', { currentFactoryId, currentUser })

      if (!currentFactoryId) {
        alert('无法获取工厂信息，请重新登录')
        return
      }

      if (!currentUser) {
        alert('无法获取用户信息，请重新登录')
        return
      }

      // 生成订单号
      const orderNumber = await generateOrderNumber()

      let finalClientId = typeof selectedClient === 'string' ? selectedClient :
                          (selectedClient && typeof selectedClient === 'object' ? selectedClient.id : '')
      let clientName = ''
      let clientPhone = ''

      // 如果是新客户，先创建客户记录
      if (selectedClient === 'new') {
        console.log('🆕 创建新客户记录...')

        // 验证新客户数据
        if (!newClientData.name || !newClientData.phone) {
          alert('请填写完整的客户信息')
          return
        }

        // 创建客户数据
        const clientData = {
          factoryId: currentFactoryId,
          name: newClientData.name,
          phone: newClientData.phone,
          email: newClientData.email || undefined,
          company: newClientData.company || undefined,
          address: projectAddress || undefined, // 使用项目地址作为客户地址
          referrerId: newClientData.referrerId || undefined
          // 其他统计字段会使用数据库默认值
        }

        // 检查数据库服务是否支持客户创建
        if (!db || typeof db.createClient !== 'function') {
          console.error('❌ 数据库服务不支持客户创建:', db)
          alert('数据库服务不完整，请联系管理员')
          return
        }

        // 创建客户记录
        const createdClient = await db.createClient(clientData)

        if (!createdClient) {
          console.error('❌ 客户创建失败')
          alert('客户创建失败，请重试')
          return
        }

        console.log('✅ 客户创建成功:', createdClient)
        finalClientId = createdClient.id
        clientName = newClientData.name
        clientPhone = newClientData.phone
      } else {
        // 如果是现有客户，获取客户信息
        console.log('📋 获取现有客户信息...')
        try {
          const allClients = await db.getClientsByFactoryId(currentFactoryId)
          const selectedClientData = allClients.find(c => c.id === selectedClient)

          if (selectedClientData) {
            clientName = selectedClientData.name
            clientPhone = selectedClientData.phone
            console.log('✅ 现有客户信息获取成功:', { name: clientName, phone: clientPhone })
          } else {
            console.warn('⚠️ 未找到选中的客户信息，将使用客户ID')
            clientName = `客户ID: ${selectedClient}`
            clientPhone = ''
          }
        } catch (error) {
          console.error('❌ 获取客户信息失败:', error)
          clientName = `客户ID: ${selectedClient}`
          clientPhone = ''
        }
      }

      // 转换为订单格式
      const orderItems: OrderItem[] = []
      floors.forEach(floor => {
        floor.vents.forEach(vent => {
          // 确保价格计算正确，如果为null则重新计算
          let finalTotalPrice = vent.totalPrice
          if (finalTotalPrice === null || finalTotalPrice === undefined || isNaN(finalTotalPrice)) {
            finalTotalPrice = calculateVentPriceLocal(vent)
          }

          orderItems.push({
            id: vent.id,
            productName: getProductTypeName(vent.type),
            productType: vent.type,
            specifications: `${vent.length}×${vent.width}mm`,
            dimensions: { length: vent.length, width: vent.width },
            floor: floor.floorName,
            quantity: vent.quantity,
            unitPrice: vent.unitPrice,
            totalPrice: finalTotalPrice,
            notes: vent.notes || ""  // 保存风口的备注信息
          })
        })
      })

      // 计算订单总金额（确保不为null）- 精确到一位小数
      const calculatedTotalAmount = orderItems.reduce((sum, item) => {
        const itemTotal = item.totalPrice || 0
        return sum + itemTotal
      }, 0)
      const finalTotalAmount = Math.round(calculatedTotalAmount * 10) / 10

      // 构建订单数据
      const orderData: Omit<Order, 'id'> = {
        factoryId: currentFactoryId,
        clientId: finalClientId,
        orderNumber,
        items: orderItems,
        totalAmount: finalTotalAmount,
        status: 'pending',
        projectAddress: projectAddress || '',
        notes: notes || '',
        // 保存客户信息到订单中（用于显示）
        clientName: clientName,
        clientPhone: clientPhone,
        createdBy: '', // 会在API路由中自动设置为正确值
        createdAt: new Date(),
        updatedAt: new Date()
      }

      console.log('📦 订单数据构建完成:', orderData)
      console.log('🔧 准备调用数据库服务...')

      // 检查数据库服务是否可用
      if (!db || typeof db.createOrder !== 'function') {
        console.error('❌ 数据库服务不可用:', db)
        alert('数据库服务初始化失败，请刷新页面重试')
        return
      }

      console.log('✅ 数据库服务可用，开始保存订单...')

      // 使用数据库服务保存订单
      const savedOrder = await db.createOrder(orderData)

      console.log('💾 订单保存结果:', savedOrder)

      if (savedOrder) {
        console.log('✅ 订单保存成功!')

        // 改进用户体验：先清空表单，再显示成功消息和跳转
        // 1. 立即清空表单数据，让用户知道保存成功了
        try {
          setFloors([])
          setProjectAddress('')
          setSelectedClient('')
          setClientSearchTerm('')
          setShowClientForm(false)
          setNewClientData({ name: '', phone: '', email: '', company: '', address: '', referrerId: '', referrerName: '' })
          setNotes('')
          console.log('✅ 表单状态重置完成')
        } catch (resetError) {
          console.warn('⚠️ 表单状态重置时出现警告:', resetError)
        }

        // 2. 显示成功消息并自动跳转
        alert('✅ 订单保存成功！正在跳转到订单列表...')

        // 3. 短暂延迟后跳转，让用户看到表单已清空
        setTimeout(() => {
          router.push('/factory/orders')
        }, 1000)
      } else {
        console.log('❌ 订单保存失败')
        alert('订单保存失败，请重试')
      }
    } catch (error) {
      console.error('❌ 保存订单过程中发生错误:', error)
      console.error('错误详情:', {
        message: error?.message || '未知错误',
        stack: error?.stack || '无堆栈信息',
        name: error?.name || '未知错误类型',
        error: error
      })

      // 提供更详细的错误信息
      const errorMessage = error?.message || '未知错误'
      let userMessage = `保存失败: ${errorMessage}`

      if (errorMessage.includes('not a function')) {
        userMessage = '数据库服务初始化失败，请刷新页面重试'
      } else if (errorMessage.includes('network')) {
        userMessage = '网络连接失败，请检查网络后重试'
      } else if (errorMessage.includes('HTTP')) {
        userMessage = `服务器错误: ${errorMessage}`
      } else if (errorMessage.includes('validation')) {
        userMessage = `数据验证失败: ${errorMessage}`
      }

      alert(userMessage)
    } finally {
      // 无论成功还是失败，都要重置保存状态
      setIsSaving(false)
      console.log('🔓 保存完成，重置保存状态为false')
    }
  }, [isSaving, selectedClient, newClientData, projectAddress, floors, notes, router])

  // 全局键盘快捷键监听
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Ctrl+S: 快速保存
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault()
        console.log('🎹 检测到全局 Ctrl+S 快捷键，开始保存订单...')
        // 直接调用保存函数，避免闭包问题
        document.dispatchEvent(new CustomEvent('save-order'))
        return
      }

      // Ctrl+A: 全选/取消全选风口
      if (e.ctrlKey && e.key === 'a' && floors.length > 0) {
        e.preventDefault()
        const totalVents = floors.reduce((total, floor) => total + floor.vents.length, 0)
        if (selectedVentIds.size === totalVents) {
          // 取消全选
          setSelectedVentIds(new Set())
          console.log('🎹 快捷键取消全选风口')
        } else {
          // 全选
          const allVentIds = floors.flatMap(floor =>
            floor.vents.map(vent => `${floor.id}-${vent.id}`)
          )
          setSelectedVentIds(new Set(allVentIds))
          console.log('🎹 快捷键全选风口')
        }
        return
      }

      // Ctrl+Shift+N: 批量添加备注
      if (e.ctrlKey && e.shiftKey && e.key === 'N' && selectedVentIds.size > 0) {
        e.preventDefault()
        console.log('🎹 快捷键批量添加备注')
        setShowBatchNotesModal(true)
        return
      }

      // Ctrl+Shift+D: 批量清除备注
      if (e.ctrlKey && e.shiftKey && e.key === 'D' && selectedVentIds.size > 0) {
        e.preventDefault()
        console.log('🎹 快捷键批量清除备注')

        const confirmed = window.confirm(`确定要清除 ${selectedVentIds.size} 个风口的备注吗？此操作不可撤销。`)
        if (!confirmed) return

        let processedCount = 0

        selectedVentIds.forEach(uniqueId => {
          const [floorId, ventId] = uniqueId.split('-')
          const floor = floors.find(f => f.id === floorId)
          const vent = floor?.vents.find(v => v.id === ventId)

          if (vent && vent.notes && vent.notes.trim() !== '') {
            // 直接更新状态
            setFloors(prevFloors => prevFloors.map(f => {
              if (f.id === floorId) {
                return {
                  ...f,
                  vents: f.vents.map(v => {
                    if (v.id === ventId) {
                      return { ...v, notes: '' }
                    }
                    return v
                  })
                }
              }
              return f
            }))
            processedCount++
          }
        })

        const totalSelected = selectedVentIds.size // 保存选中数量
        setSelectedVentIds(new Set())

        if (processedCount > 0) {
          const successMessage = `✅ 批量清除备注完成！\n\n` +
            `📊 操作统计:\n` +
            `• 清除备注数量: ${processedCount} 个\n` +
            `• 选中风口总数: ${totalSelected} 个\n` +
            `• 跳过空备注: ${totalSelected - processedCount} 个\n\n` +
            `💡 提示: 可使用 Ctrl+Z 撤销操作`

          alert(successMessage)
        } else {
          alert('ℹ️ 没有找到需要清除的备注\n\n所选风口的备注都是空的')
        }
        return
      }

      // Ctrl+Shift+R: 强制重置保存状态（调试用）
      if (e.ctrlKey && e.shiftKey && e.key === 'R') {
        e.preventDefault()
        console.log('🔧 强制重置保存状态')
        setIsSaving(false)
        return
      }
    }

    // 处理自定义保存事件
    const handleSaveEvent = () => {
      // 检查是否正在保存中
      if (isSaving) {
        console.log('⚠️ 订单正在保存中，忽略快捷键请求')
        // 🔧 添加强制重置机制：如果保存状态持续超过10秒，强制重置
        setTimeout(() => {
          if (isSaving) {
            console.log('🔧 检测到保存状态异常，强制重置')
            setIsSaving(false)
          }
        }, 10000)
        return
      }
      saveOrder()
    }

    // 添加全局键盘事件监听
    document.addEventListener('keydown', handleGlobalKeyDown)
    document.addEventListener('save-order', handleSaveEvent)
    console.log('✅ 全局键盘快捷键监听器已添加')

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown)
      document.removeEventListener('save-order', handleSaveEvent)
      console.log('🧹 全局键盘快捷键监听器已移除')
    }
  }, [isSaving, saveOrder, selectedVentIds.size, floors.length]) // 添加依赖数组，确保在保存状态变化时更新监听器

  // 计算风口价格
  const calculateVentPriceLocal = (vent: VentItem): number => {
    const { type, length, width, quantity, unitPrice } = vent

    // 🔧 确保所有参数都是有效数值
    const safeLength = isNaN(length) ? 0 : length
    const safeWidth = isNaN(width) ? 0 : width
    const safeQuantity = isNaN(quantity) ? 1 : quantity
    const safeUnitPrice = isNaN(unitPrice) ? 0 : unitPrice

    // 使用统一的价格计算器
    const result = calculateVentPrice({
      productType: type,
      length: safeLength,
      width: safeWidth,
      quantity: safeQuantity,
      unitPrice: safeUnitPrice
    })

    // 🔧 确保返回值不是NaN
    return isNaN(result) ? 0 : result
  }

  // 根据产品类型获取默认单价
  const getDefaultPriceByType = (type: string): number => {
    const price = defaultUnitPrices[type as keyof typeof defaultUnitPrices] || defaultUnitPrices.double_white_outlet

    console.log(`🔍 获取默认单价: ${type} -> ${price}`, { defaultUnitPrices })
    return price
  }

  // 获取风口类型的中文名称 - 使用统一的命名函数
  const getVentTypeName = (type: VentItem['type']): string => {
    return getProductTypeName(type)
  }

  // 获取风口类型的计价单位
  const getVentTypeUnit = (type: VentItem['type']): string => {
    const regularVentTypes = [
      'double_white_outlet', 'double_black_outlet', 'white_return', 'black_return',
      'white_linear', 'black_linear', 'white_linear_return', 'black_linear_return', 'maintenance'
    ]
    return regularVentTypes.includes(type) ? '元/㎡' : '元/m'
  }

  // 过滤客户列表
  const filteredClients = existingClients.filter(client =>
    client.name.toLowerCase().includes(clientSearchTerm.toLowerCase()) ||
    client.phone.includes(clientSearchTerm)
  )

  // 处理客户选择
  const handleClientSelect = (client: ClientData) => {
    setSelectedClient(client.id)
    setClientSearchTerm(client.name)
    setShowClientDropdown(false)
    setShowClientForm(false)
  }

  // 处理新建客户
  const handleNewClient = () => {
    setSelectedClient("new")
    setClientSearchTerm("新客户")
    setShowClientDropdown(false)
    setShowClientForm(true)
  }

  // 批量设置单价（单个楼层）
  const handleBatchSetPrice = (floorId: string) => {
    const price = Number(batchPriceInput)
    if (price <= 0) {
      alert('请输入有效的单价')
      return
    }

    // 使用函数式更新确保状态一致性
    setFloors(prevFloors => {
      return prevFloors.map(floor => {
        if (floor.id === floorId) {
          const updatedVents = floor.vents.map(vent => {
            const updatedVent = { ...vent, unitPrice: price }
            // 重新计算价格
            updatedVent.totalPrice = calculateVentPriceLocal(updatedVent)
            return updatedVent
          })

          // 重新计算楼层总价
          const floorTotal = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)

          return {
            ...floor,
            vents: updatedVents,
            totalPrice: floorTotal
          }}
        return floor
      })
    })

    setShowBatchPriceModal(null)
    setBatchPriceInput("")
    alert(`已将该楼层所有风口单价设置为 ${price} 元`)
  }

  // 全局批量设置单价（所有楼层）
  const handleGlobalBatchSetPrice = () => {
    const price = Number(globalBatchPriceInput)
    if (price <= 0) {
      alert('请输入有效的单价')
      return
    }

    let totalVentsUpdated = 0

    // 使用函数式更新确保状态一致性
    setFloors(prevFloors => {
      return prevFloors.map(floor => {
        const updatedVents = floor.vents.map(vent => {
          const updatedVent = { ...vent, unitPrice: price }
          // 重新计算价格
          updatedVent.totalPrice = calculateVentPriceLocal(updatedVent)
          totalVentsUpdated++
          return updatedVent
        })

        // 重新计算楼层总价
        const floorTotal = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)

        return {
          ...floor,
          vents: updatedVents,
          totalPrice: floorTotal
        }
      })
    })

    setShowGlobalBatchPriceModal(false)
    setGlobalBatchPriceInput("")
    alert(`已将所有楼层共 ${totalVentsUpdated} 个风口的单价设置为 ${price} 元`)
  }

  // 判断是否为出风口
  const isOutletVent = (type: VentItem['type']): boolean => {
    const outletTypes = [
      'double_white_outlet', 'double_black_outlet', 'white_black_bottom_outlet',
      'white_linear', 'black_linear', 'high_end_white_outlet', 'high_end_black_outlet',
      'arrow_outlet', 'claw_outlet', 'black_white_dual_outlet', 'wood_grain_outlet',
      'white_putty_outlet', 'black_putty_outlet', 'white_gypsum_outlet', 'black_gypsum_outlet'
    ]
    return outletTypes.includes(type)
  }

  // 判断是否为回风口
  const isReturnVent = (type: VentItem['type']): boolean => {
    const returnTypes = [
      'white_black_bottom_return', 'white_return', 'black_return',
      'white_linear_return', 'black_linear_return', 'high_end_white_return', 'high_end_black_return',
      'arrow_return', 'claw_return', 'black_white_dual_return', 'wood_grain_return',
      'white_putty_return', 'black_putty_return', 'white_gypsum_return', 'black_gypsum_return'
    ]
    return returnTypes.includes(type)
  }

  // 批量更改出风口类型（单个楼层）
  const handleBatchChangeOutletType = (floorId: string) => {
    let updatedCount = 0

    setFloors(prevFloors => {
      return prevFloors.map(floor => {
        if (floor.id === floorId) {
          const updatedVents = floor.vents.map(vent => {
            // 只更改出风口类型
            if (isOutletVent(vent.type)) {
              updatedCount++
              const updatedVent = {
                ...vent,
                type: batchOutletType,
                unitPrice: getDefaultPriceByType(batchOutletType)
              }
              updatedVent.totalPrice = calculateVentPriceLocal(updatedVent)
              return updatedVent
            }
            return vent
          })

          const floorTotal = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)

          return {
            ...floor,
            vents: updatedVents,
            totalPrice: floorTotal
          }
        }
        return floor
      })
    })

    setShowBatchOutletTypeModal(null)
    const typeName = getVentTypeDisplayName(batchOutletType)
    alert(`已将该楼层 ${updatedCount} 个出风口类型更改为 ${typeName}`)
  }

  // 批量更改回风口类型（单个楼层）
  const handleBatchChangeReturnType = (floorId: string) => {
    let updatedCount = 0

    setFloors(prevFloors => {
      return prevFloors.map(floor => {
        if (floor.id === floorId) {
          const updatedVents = floor.vents.map(vent => {
            // 只更改回风口类型
            if (isReturnVent(vent.type)) {
              updatedCount++
              const updatedVent = {
                ...vent,
                type: batchReturnType,
                unitPrice: getDefaultPriceByType(batchReturnType)
              }
              updatedVent.totalPrice = calculateVentPriceLocal(updatedVent)
              return updatedVent
            }
            return vent
          })

          const floorTotal = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)

          return {
            ...floor,
            vents: updatedVents,
            totalPrice: floorTotal
          }
        }
        return floor
      })
    })

    setShowBatchReturnTypeModal(null)
    const typeName = getVentTypeDisplayName(batchReturnType)
    alert(`已将该楼层 ${updatedCount} 个回风口类型更改为 ${typeName}`)
  }

  // 全局批量更改出风口类型（所有楼层）
  const handleGlobalBatchChangeOutletType = () => {
    let totalVentsUpdated = 0

    setFloors(prevFloors => {
      return prevFloors.map(floor => {
        const updatedVents = floor.vents.map(vent => {
          // 只更改出风口类型
          if (isOutletVent(vent.type)) {
            totalVentsUpdated++
            const updatedVent = {
              ...vent,
              type: globalBatchOutletType,
              unitPrice: getDefaultPriceByType(globalBatchOutletType)
            }
            updatedVent.totalPrice = calculateVentPriceLocal(updatedVent)
            return updatedVent
          }
          return vent
        })

        const floorTotal = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)

        return {
          ...floor,
          vents: updatedVents,
          totalPrice: floorTotal
        }
      })
    })

    setShowGlobalBatchOutletTypeModal(false)
    const typeName = getVentTypeDisplayName(globalBatchOutletType)
    alert(`已将所有楼层共 ${totalVentsUpdated} 个出风口的类型更改为 ${typeName}`)
  }

  // 全局批量更改回风口类型（所有楼层）
  const handleGlobalBatchChangeReturnType = () => {
    let totalVentsUpdated = 0

    setFloors(prevFloors => {
      return prevFloors.map(floor => {
        const updatedVents = floor.vents.map(vent => {
          // 只更改回风口类型
          if (isReturnVent(vent.type)) {
            totalVentsUpdated++
            const updatedVent = {
              ...vent,
              type: globalBatchReturnType,
              unitPrice: getDefaultPriceByType(globalBatchReturnType)
            }
            updatedVent.totalPrice = calculateVentPriceLocal(updatedVent)
            return updatedVent
          }
          return vent
        })

        const floorTotal = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)

        return {
          ...floor,
          vents: updatedVents,
          totalPrice: floorTotal
        }
      })
    })

    setShowGlobalBatchReturnTypeModal(false)
    const typeName = getVentTypeDisplayName(globalBatchReturnType)
    alert(`已将所有楼层共 ${totalVentsUpdated} 个回风口的类型更改为 ${typeName}`)
  }

  // 获取风口类型显示名称
  const getVentTypeDisplayName = (type: VentItem['type']): string => {
    const displayNames: Record<VentItem['type'], string> = {
      // 常规风口
      double_white_outlet: '双层白色出风口',
      double_black_outlet: '双层黑色出风口',
      white_black_bottom_outlet: '白色黑底出风口',
      white_black_bottom_return: '白色黑底回风口',
      white_return: '白色回风口',
      black_return: '黑色回风口',
      white_linear: '白色线型风口',
      black_linear: '黑色线型风口',
      white_linear_return: '白色线型回风口',
      black_linear_return: '黑色线型回风口',
      maintenance: '检修口',
      // 高端风口
      high_end_white_outlet: '高端白色出风口',
      high_end_black_outlet: '高端黑色出风口',
      high_end_white_return: '高端白色回风口',
      high_end_black_return: '高端黑色回风口',
      arrow_outlet: '箭型出风口',
      arrow_return: '箭型回风口',
      claw_outlet: '爪型出风口',
      claw_return: '爪型回风口',
      black_white_dual_outlet: '黑白双色出风口',
      black_white_dual_return: '黑白双色回风口',
      black_white_outlet: '黑白双色出风口',  // 批量修改功能使用的简化命名
      black_white_return: '黑白双色回风口',  // 批量修改功能使用的简化命名
      wood_grain_outlet: '木纹出风口',
      wood_grain_return: '木纹回风口',
      wood_outlet: '木纹出风口',  // 批量修改功能使用的简化命名
      wood_return: '木纹回风口',  // 批量修改功能使用的简化命名
      white_putty_outlet: '白色腻子粉出风口',
      white_putty_return: '白色腻子粉回风口',
      black_putty_outlet: '黑色腻子粉出风口',
      black_putty_return: '黑色腻子粉回风口',
      white_gypsum_outlet: '白色石膏板出风口',
      white_gypsum_return: '白色石膏板回风口',
      black_gypsum_outlet: '黑色石膏板出风口',
      black_gypsum_return: '黑色石膏板回风口'
    }
    if (!displayNames[type]) {
      console.error(`🚨 未知风口类型: "${type}" (类型: ${typeof type})`)
      console.error('📋 可用的风口类型:', Object.keys(displayNames))
    }
    return displayNames[type] || `未知类型(${type})`
  }

  // 🔧 安全的数值显示函数，确保React input不接收NaN
  const safeValueForInput = (value: any, defaultValue: string | number = ''): string | number => {
    if (value === null || value === undefined) return defaultValue
    if (typeof value === 'string' && value.trim() === '') return defaultValue
    const num = Number(value)
    return isNaN(num) ? defaultValue : value
  }

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.client-search-container')) {
        setShowClientDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // 验证电话号码
  const validatePhone = (phone: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  // 处理电话号码输入
  const handlePhoneChange = (phone: string) => {
    // 只允许数字输入
    const numericPhone = phone.replace(/\D/g, '')
    setNewClientData(prev => ({ ...prev, phone: numericPhone }))
    if (numericPhone && !validatePhone(numericPhone)) {
      setPhoneError("请输入正确的11位手机号码（以1开头）")
    } else {
      setPhoneError("")
    }
  }

  // 添加新楼层
  const addFloor = () => {
    const newFloor: FloorData = {
      id: generateUniqueId('floor'),
      floorName: `${floors.length + 1}楼`,
      vents: [
        {
          id: generateUniqueId('vent'),
          type: 'double_white_outlet',
          length: 0,
          width: 0,
          quantity: 1,
          unitPrice: getDefaultPriceByType('double_white_outlet'),
          totalPrice: 0
        },
        {
          id: generateUniqueId('vent'),
          type: 'white_return',
          length: 0,
          width: 0,
          quantity: 1,
          unitPrice: getDefaultPriceByType('white_return'),
          totalPrice: 0
        }
      ],
      totalPrice: 0
    }
    setFloors([...floors, newFloor])
  }



  // 添加风口到指定楼层
  const addVentToFloor = (floorId: string) => {
    setFloors(floors.map(floor => {
      if (floor.id === floorId) {
        const newVent: VentItem = {
          id: generateUniqueId('vent'),
          type: 'double_white_outlet',
          length: 0,
          width: 0,
          quantity: 1,
          unitPrice: getDefaultPriceByType('double_white_outlet'),
          totalPrice: 0
        }
        newVent.totalPrice = calculateVentPriceLocal(newVent)
        return {
          ...floor,
          vents: [...floor.vents, newVent]
        }
      }
      return floor
    }))
  }

  // 删除楼层
  const removeFloor = (floorId: string) => {
    setFloors(floors.filter(floor => floor.id !== floorId))
  }

  // 删除风口
  const removeVent = (floorId: string, ventId: string) => {
    setFloors(floors.map(floor => {
      if (floor.id === floorId) {
        return {
          ...floor,
          vents: floor.vents.filter(vent => vent.id !== ventId)
        }
      }
      return floor
    }))
  }

  // 复制风口（完整复制）
  const duplicateVent = (floorId: string, ventId: string) => {
    setFloors(floors.map(floor => {
      if (floor.id === floorId) {
        const originalVent = floor.vents.find(vent => vent.id === ventId)
        if (originalVent) {
          const newVent: VentItem = {
            ...originalVent,
            id: generateUniqueId('vent'),
          }
          return {
            ...floor,
            vents: [...floor.vents, newVent]
          }
        }
      }
      return floor
    }))
  }

  // 复制风口（排除尺寸信息）
  const duplicateVentWithoutDimensions = (floorId: string, ventId: string) => {
    const newVentId = generateUniqueId('vent')

    setFloors(floors.map(floor => {
      if (floor.id === floorId) {
        const originalVent = floor.vents.find(vent => vent.id === ventId)
        if (originalVent) {
          const newVent: VentItem = {
            ...originalVent,
            id: newVentId,
            length: 0,  // 重置长度
            width: 0,   // 重置宽度
            totalPrice: 0  // 重置总价
          }
          return {
            ...floor,
            vents: [...floor.vents, newVent]
          }
        }
      }
      return floor
    }))

    // 聚焦到新行的长度输入框
    setTimeout(() => {
      const newRowInput = document.querySelector(`[data-vent-id="${newVentId}"] input[data-field="length"]`) as HTMLInputElement
      if (newRowInput) {
        newRowInput.focus()
        newRowInput.select()
      }
    }, 100)
  }

  // 处理键盘快捷键
  const handleVentKeyDown = (e: React.KeyboardEvent, floorId: string, ventId: string, fieldType: string) => {
    // Ctrl+D: 完整复制当前行
    if (e.ctrlKey && e.key === 'd') {
      e.preventDefault()
      duplicateVent(floorId, ventId)
      return
    }

    // Ctrl+R: 复制当前行（排除尺寸信息）
    if (e.ctrlKey && e.key === 'r') {
      e.preventDefault()
      duplicateVentWithoutDimensions(floorId, ventId)
      return
    }

    // 注意：Ctrl+S 已由全局监听器处理，这里不再重复处理以避免双重保存

    // Enter: 智能跳转
    if (e.key === 'Enter') {
      e.preventDefault()

      // 如果是尺寸输入框，先进行智能转换
      if (fieldType === 'length' || fieldType === 'width') {
        handleDimensionInputComplete(floorId, ventId)
      }

      // 找到当前楼层
      const currentFloor = floors.find(floor => floor.id === floorId)
      if (!currentFloor) return

      // 找到当前风口的索引
      const currentVentIndex = currentFloor.vents.findIndex(vent => vent.id === ventId)

      // 如果是尺寸输入框（length/width），智能跳转逻辑
      if (fieldType === 'length' || fieldType === 'width') {
        // 如果是最后一行，添加新风口
        if (currentVentIndex === currentFloor.vents.length - 1) {
          // 先添加新风口
          const newVent: VentItem = {
            id: generateUniqueId('vent'),
            type: 'double_white_outlet',  // 使用常用的默认类型
            length: 0,
            width: 0,
            quantity: 1,
            unitPrice: getDefaultPriceByType('double_white_outlet'),
            totalPrice: 0
          }

          setFloors(floors.map(floor => {
            if (floor.id === floorId) {
              return {
                ...floor,
                vents: [...floor.vents, newVent]
              }
            }
            return floor
          }))

          // 延迟聚焦到新行的第一个输入框（长度）
          setTimeout(() => {
            const newRowInput = document.querySelector(`[data-vent-id="${newVent.id}"] input[data-field="length"]`) as HTMLInputElement
            if (newRowInput) {
              newRowInput.focus()
              newRowInput.select()
            }
          }, 100)
        } else {
          // 如果不是最后一行，跳转到下一行的长度输入框
          const nextVentId = currentFloor.vents[currentVentIndex + 1].id
          setTimeout(() => {
            const nextInput = document.querySelector(`[data-vent-id="${nextVentId}"] input[data-field="length"]`) as HTMLInputElement
            if (nextInput) {
              nextInput.focus()
              nextInput.select()
            }
          }, 50)
        }
      } else {
        // 对于其他字段，保持原有的跳转逻辑（跳转到下一行的相同字段）
        if (currentVentIndex === currentFloor.vents.length - 1) {
          // 最后一行，添加新风口并跳转到对应字段
          const newVent: VentItem = {
            id: generateUniqueId('vent'),
            type: 'double_white_outlet',
            length: 0,
            width: 0,
            quantity: 1,
            unitPrice: getDefaultPriceByType('double_white_outlet'),
            totalPrice: 0
          }

          setFloors(floors.map(floor => {
            if (floor.id === floorId) {
              return {
                ...floor,
                vents: [...floor.vents, newVent]
              }
            }
            return floor
          }))

          // 延迟聚焦到新行的对应字段
          setTimeout(() => {
            const newRowInput = document.querySelector(`[data-vent-id="${newVent.id}"] input[data-field="${fieldType}"]`) as HTMLInputElement
            if (newRowInput) {
              newRowInput.focus()
              newRowInput.select()
            }
          }, 100)
        } else {
          // 跳转到下一行的相同字段
          const nextVentId = currentFloor.vents[currentVentIndex + 1].id
          setTimeout(() => {
            const nextInput = document.querySelector(`[data-vent-id="${nextVentId}"] input[data-field="${fieldType}"]`) as HTMLInputElement
            if (nextInput) {
              nextInput.focus()
              nextInput.select()
            }
          }, 50)
        }
      }
    }
  }

  // 更新楼层名称
  const updateFloorName = (floorId: string, floorName: string) => {
    setFloors(floors.map(floor => {
      if (floor.id === floorId) {
        return { ...floor, floorName }
      }
      return floor
    }))
  }

  // 立即更新风口数据（不进行智能尺寸转换，用于实时输入）
  const updateVentImmediate = (floorId: string, ventId: string, updates: Partial<VentItem>) => {
    setFloors(prevFloors => prevFloors.map(floor => {
      if (floor.id === floorId) {
        const updatedVents = floor.vents.map(vent => {
          if (vent.id === ventId) {
            // 直接更新，不进行智能尺寸识别
            const updatedVent = { ...vent, ...updates }

            // 重新计算价格
            updatedVent.totalPrice = calculateVentPriceLocal(updatedVent)

            return updatedVent
          }
          return vent
        })

        // 重新计算楼层总价
        const floorTotal = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)

        return {
          ...floor,
          vents: updatedVents,
          totalPrice: floorTotal
        }
      }
      return floor
    }))
  }

  // 智能更新风口数据（进行智能尺寸转换，用于完成输入后）
  const updateVentSync = (floorId: string, ventId: string, updates: Partial<VentItem>) => {
    setFloors(prevFloors => prevFloors.map(floor => {
      if (floor.id === floorId) {
        const updatedVents = floor.vents.map(vent => {
          if (vent.id === ventId) {
            // 使用统一的智能尺寸识别工具函数
            const processedUpdates = updateDimensionsWithSmartRecognition(
              updates,
              vent.length || 0,
              vent.width || 0
            )

            const updatedVent = { ...vent, ...processedUpdates }

            // 重新计算价格
            updatedVent.totalPrice = calculateVentPriceLocal(updatedVent)

            return updatedVent
          }
          return vent
        })

        // 重新计算楼层总价
        const floorTotal = updatedVents.reduce((sum, vent) => sum + vent.totalPrice, 0)

        return {
          ...floor,
          vents: updatedVents,
          totalPrice: floorTotal
        }
      }
      return floor
    }))
  }

  // 处理尺寸输入完成（失去焦点时进行智能转换）
  const handleDimensionInputComplete = (floorId: string, ventId: string) => {
    const floor = floors.find(f => f.id === floorId)
    const vent = floor?.vents.find(v => v.id === ventId)

    if (vent && vent.length > 0 && vent.width > 0) {
      // 检查是否需要智能转换
      const smartDimensions = smartDimensionRecognition(vent.length, vent.width)

      if (smartDimensions.length !== vent.length || smartDimensions.width !== vent.width) {
        console.log(`🔄 智能尺寸转换: ${vent.length}×${vent.width} → ${smartDimensions.length}×${smartDimensions.width}`)

        // 进行智能转换
        updateVentSync(floorId, ventId, {
          length: smartDimensions.length,
          width: smartDimensions.width
        })
      }
    }
  }

  // 异步更新风口数据（用于类型变更）
  const updateVent = async (floorId: string, ventId: string, updates: Partial<VentItem>) => {
    // 如果更新了产品类型，异步获取最新的默认单价
    if (updates.type) {
      try {
        const factoryId = getCurrentFactoryId()
        if (factoryId) {
          const latestPrices = await getDefaultUnitPricesAsync(factoryId)
          const newUnitPrice = latestPrices[updates.type] || latestPrices.double_white_outlet
          console.log(`🔄 产品类型变更为 ${updates.type}，从数据库获取最新单价: ${newUnitPrice}`, latestPrices)
          updates.unitPrice = newUnitPrice
        }
      } catch (error) {
        console.error('❌ 获取最新默认单价失败，使用本地缓存:', error)
        updates.unitPrice = getDefaultPriceByType(updates.type)
      }
    }

    // 使用同步函数进行实际更新
    updateVentSync(floorId, ventId, updates)
  }



  // 计算总金额 - 精确到一位小数
  const totalAmount = floors.reduce((sum, floor) => {
    const floorTotal = floor.totalPrice || 0
    return sum + floorTotal
  }, 0)
  const finalDisplayTotal = Math.round(totalAmount * 10) / 10

  // 获取产品类型的颜色
  const getVentTypeColor = (type: VentItem['type']) => {
    if (isRegularVent(type)) {
      return 'bg-blue-500 text-white'
    }

    // 高端风口 - 绿色系
    return 'bg-green-500 text-white'
  }

  // 批量备注管理函数
  // 生成风口的唯一ID（楼层ID + 风口ID）
  const getVentUniqueId = (floorId: string, ventId: string) => `${floorId}-${ventId}`

  // 解析风口的唯一ID
  const parseVentUniqueId = (uniqueId: string) => {
    // 处理复杂的ID格式：floor-1753185605524-0-vent-1753185605524-1
    // 需要找到 '-vent-' 分隔符来正确分割楼层ID和风口ID
    const ventIndex = uniqueId.indexOf('-vent-')
    if (ventIndex === -1) {
      // 如果没有找到 '-vent-'，使用简单的分割方式（向后兼容）
      const [floorId, ventId] = uniqueId.split('-')
      return { floorId, ventId }
    }

    const floorId = uniqueId.substring(0, ventIndex)
    const ventId = uniqueId.substring(ventIndex + 1) // +1 是为了去掉开头的 '-'

    console.log(`🔍 复杂ID解析: "${uniqueId}" → floorId="${floorId}", ventId="${ventId}"`)
    return { floorId, ventId }
  }

  // 处理单个风口选择
  const handleVentSelection = (floorId: string, ventId: string) => {
    const uniqueId = getVentUniqueId(floorId, ventId)
    console.log(`🔍 风口选择: floorId=${floorId}, ventId=${ventId}, uniqueId=${uniqueId}`)

    // 调试：检查当前楼层和风口数据
    console.log(`🔍 当前所有楼层:`, floors.map(f => ({ id: f.id, name: f.floorName, ventCount: f.vents.length })))
    const targetFloor = floors.find(f => f.id === floorId)
    if (targetFloor) {
      console.log(`🔍 目标楼层 ${floorId} 的所有风口:`, targetFloor.vents.map(v => ({ id: v.id, type: v.type, notes: v.notes })))
    } else {
      console.log(`❌ 未找到楼层: ${floorId}`)
    }

    setSelectedVentIds(prev => {
      const newSet = new Set(prev)
      if (newSet.has(uniqueId)) {
        newSet.delete(uniqueId)
        console.log(`✅ 取消选择风口: ${uniqueId}`)
      } else {
        newSet.add(uniqueId)
        console.log(`✅ 选择风口: ${uniqueId}`)
      }
      console.log(`🔍 当前选中风口数量: ${newSet.size}`)
      console.log(`🔍 当前选中风口列表:`, Array.from(newSet))
      return newSet
    })
  }

  // 全选所有风口
  const handleSelectAllVents = () => {
    const allVentIds = floors.flatMap(floor =>
      floor.vents.map(vent => getVentUniqueId(floor.id, vent.id))
    )
    console.log('🔍 全选风口:', allVentIds)
    console.log('🔍 楼层信息:', floors.map(f => ({ id: f.id, name: f.floorName, ventCount: f.vents.length })))
    setSelectedVentIds(new Set(allVentIds))
  }

  // 取消全选
  const handleDeselectAllVents = () => {
    setSelectedVentIds(new Set())
  }

  // 批量添加备注
  const handleBatchAddNotes = () => {
    if (selectedVentIds.size === 0) {
      alert('请先选择要添加备注的风口')
      return
    }
    setShowBatchNotesModal(true)
  }

  // 确认批量添加备注
  const confirmBatchAddNotes = () => {
    if (!batchNotesText.trim()) {
      alert('请输入备注内容')
      return
    }

    console.log('🔍 批量添加备注调试信息:')
    console.log('选中的风口IDs:', Array.from(selectedVentIds))
    console.log('当前楼层数据:', floors.map(f => ({ id: f.id, name: f.floorName, ventCount: f.vents.length })))

    let processedCount = 0

    selectedVentIds.forEach(uniqueId => {
      console.log(`🔍 处理风口ID: ${uniqueId}`)
      const { floorId, ventId } = parseVentUniqueId(uniqueId)
      console.log(`🔍 解析得到 floorId: ${floorId}, ventId: ${ventId}`)

      const floor = floors.find(f => f.id === floorId)
      console.log(`🔍 找到楼层:`, floor ? `${floor.floorName} (${floor.vents.length}个风口)` : '未找到')

      const vent = floor?.vents.find(v => v.id === ventId)
      console.log(`🔍 找到风口:`, vent ? `${vent.type} (当前备注: "${vent.notes || '无'}")` : '未找到')

      if (vent) {
        const newNotes = batchNotesMode === 'overwrite'
          ? batchNotesText.trim()
          : vent.notes
            ? `${vent.notes} ${batchNotesText.trim()}`
            : batchNotesText.trim()

        console.log(`🔍 新备注内容: "${newNotes}"`)
        updateVentSync(floorId, ventId, { notes: newNotes })
        processedCount++
      } else {
        console.log(`❌ 未找到风口: floorId=${floorId}, ventId=${ventId}`)
      }
    })

    console.log(`🔍 处理完成，成功处理 ${processedCount} 个风口`)

    const modeText = batchNotesMode === 'overwrite' ? '设置' : '追加'
    const notesContent = batchNotesText.trim() // 保存备注内容

    setShowBatchNotesModal(false)
    setBatchNotesText('')
    setSelectedVentIds(new Set())

    // 显示详细的操作结果
    const successMessage = `✅ 批量备注操作完成！\n\n` +
      `📊 操作统计:\n` +
      `• 处理风口数量: ${processedCount} 个\n` +
      `• 操作模式: ${modeText}备注\n` +
      `• 备注内容: "${notesContent}"\n\n` +
      `💡 提示: 可使用 Ctrl+Z 撤销操作`

    alert(successMessage)
  }

  // 批量清除备注
  const handleBatchClearNotes = () => {
    if (selectedVentIds.size === 0) {
      alert('请先选择要清除备注的风口')
      return
    }

    const confirmed = window.confirm(`确定要清除 ${selectedVentIds.size} 个风口的备注吗？此操作不可撤销。`)
    if (!confirmed) return

    console.log('🔍 批量清除备注调试信息:')
    console.log('选中的风口IDs:', Array.from(selectedVentIds))
    console.log('当前楼层数据:', floors.map(f => ({ id: f.id, name: f.floorName, ventCount: f.vents.length })))

    let processedCount = 0

    selectedVentIds.forEach(uniqueId => {
      console.log(`🔍 处理风口ID: ${uniqueId}`)
      const { floorId, ventId } = parseVentUniqueId(uniqueId)
      console.log(`🔍 解析得到 floorId: ${floorId}, ventId: ${ventId}`)

      const floor = floors.find(f => f.id === floorId)
      console.log(`🔍 找到楼层:`, floor ? `${floor.floorName} (${floor.vents.length}个风口)` : '未找到')

      const vent = floor?.vents.find(v => v.id === ventId)
      console.log(`🔍 找到风口:`, vent ? `${vent.type} (当前备注: "${vent.notes || '无'}")` : '未找到')

      if (vent && vent.notes && vent.notes.trim() !== '') {
        console.log(`🔍 清除风口备注: "${vent.notes}" -> ""`)
        updateVentSync(floorId, ventId, { notes: '' })
        processedCount++
      } else if (vent) {
        console.log(`⚠️ 风口备注为空，跳过: "${vent.notes || '无'}"`)
      } else {
        console.log(`❌ 未找到风口: floorId=${floorId}, ventId=${ventId}`)
      }
    })

    console.log(`🔍 处理完成，成功处理 ${processedCount} 个风口`)

    const totalSelected = selectedVentIds.size // 保存选中数量
    setSelectedVentIds(new Set())

    if (processedCount > 0) {
      const successMessage = `✅ 批量清除备注完成！\n\n` +
        `📊 操作统计:\n` +
        `• 清除备注数量: ${processedCount} 个\n` +
        `• 选中风口总数: ${totalSelected} 个\n` +
        `• 跳过空备注: ${totalSelected - processedCount} 个\n\n` +
        `💡 提示: 可使用 Ctrl+Z 撤销操作`

      alert(successMessage)
    } else {
      alert('ℹ️ 没有找到需要清除的备注\n\n所选风口的备注都是空的')
    }
  }

  // 获取风口类型简短名称
  const getVentTypeShort = (type: VentItem['type']) => {
    const shortNames: Record<VentItem['type'], string> = {
      // 常规风口
      double_white_outlet: '双白出',
      double_black_outlet: '双黑出',
      white_black_bottom_outlet: '白黑底出',
      white_black_bottom_return: '白黑底回',
      white_return: '白回',
      black_return: '黑回',
      white_linear: '白线出',
      black_linear: '黑线出',
      white_linear_return: '白线回',
      black_linear_return: '黑线回',
      maintenance: '检修',
      // 高端风口
      high_end_white_outlet: '高端白出',
      high_end_black_outlet: '高端黑出',
      high_end_white_return: '高端白回',
      high_end_black_return: '高端黑回',
      arrow_outlet: '箭型出',
      arrow_return: '箭型回',
      claw_outlet: '爪型出',
      claw_return: '爪型回',
      black_white_dual_outlet: '黑白双出',
      black_white_dual_return: '黑白双回',
      wood_grain_outlet: '木纹出',
      wood_grain_return: '木纹回',
      white_putty_outlet: '白腻出',
      white_putty_return: '白腻回',
      black_putty_outlet: '黑腻出',
      black_putty_return: '黑腻回',
      white_gypsum_outlet: '白石出',
      white_gypsum_return: '白石回',
      black_gypsum_outlet: '黑石出',
      black_gypsum_return: '黑石回'
    }
    return shortNames[type] || '未知'
  }






  // 处理单个项目文件导入
  const handleSingleProjectImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setImportType('single')
    setImporting(true)
    try {
      const XLSX = await import('xlsx')
      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

      // 解析数据并转换为订单格式
      const importResult = parseImportedData(jsonData)

      if (importResult.floors.length > 0) {
        // 单项目导入：启动预览编辑模式
        previewActions.initializePreview('single_project', { floors: importResult.floors })
      } else if (importResult.projects.length > 0) {
        // 如果检测到多项目，但用户选择了单项目导入，取第一个项目
        const firstProject = importResult.projects[0]
        previewActions.initializePreview('single_project', { floors: firstProject.floors })
      } else {
        alert('未能识别到有效的订单数据，请检查文件格式')
      }
    } catch (error) {
      console.error('导入文件失败:', error)
      alert('导入文件失败，请检查文件格式是否正确')
    } finally {
      setImporting(false)
      setImportType(null)
      // 清空文件输入
      event.target.value = ''
    }
  }

  // 处理预览编辑确认导入
  const handlePreviewConfirm = async () => {
    try {
      if (previewState.importType === 'single_project') {
        // 单项目模式：将预览的楼层数据设置到主表单
        if (previewState.floors.length > 0) {
          // 🔧 新增：客户信息验证和保持逻辑
          console.log('🔍 单项目模式客户信息检查:', {
            selectedClient,
            newClientData,
            existingClientsCount: existingClients.length
          })

          // 如果在预览中选择了客户，保持客户选择状态
          if (selectedClient && selectedClient !== '') {
            console.log('✅ 客户信息已选择，将保持在主表单中:', selectedClient)

            // 如果选择的是新客户，验证新客户信息是否完整
            if (selectedClient === 'new') {
              if (!newClientData.name || !newClientData.phone) {
                alert('请填写完整的新客户信息（姓名和电话）')
                return
              }
              console.log('✅ 新客户信息验证通过')
            } else {
              // 验证现有客户是否存在
              const client = existingClients.find((c: any) => c.id === selectedClient)
              if (!client) {
                alert('所选客户不存在，请重新选择客户')
                return
              }
              console.log('✅ 现有客户验证通过:', client.name)
            }
          } else {
            console.log('ℹ️ 未选择客户，导入后需要在主表单中选择客户')
          }

          // 转换预览数据为主表单格式
          const convertedFloors = previewState.floors.map(floor => ({
            ...floor,
            vents: floor.vents.map(vent => ({
              ...vent,
              isEditing: false,
              hasChanges: false,
              validationErrors: undefined,
              originalData: undefined
            }))
          }))
          setFloors(convertedFloors)

          // 🔧 设置项目地址（如果有的话）
          console.log('🔍 检查预览状态中的项目数据:', {
            hasProjects: !!previewState.projects,
            projectsLength: previewState.projects?.length || 0,
            firstProject: previewState.projects?.[0],
            floors: previewState.floors?.length || 0
          })

          if (previewState.projects && previewState.projects.length > 0) {
            const project = previewState.projects[0]
            const projectName = project.projectAddress || project.name
            if (projectName && projectName !== '未命名项目' && projectName.trim().length > 0) {
              setProjectAddress(projectName)
              console.log(`🏗️ 已设置项目地址: "${projectName}"`)
            } else {
              console.log(`⚠️ 项目名称为空或无效，跳过设置项目地址:`, {
                projectAddress: project.projectAddress,
                name: project.name
              })
            }
          } else if (previewState.floors && previewState.floors.length > 0) {
            // 如果没有项目数据，尝试从楼层名称中获取项目信息
            const firstFloorName = previewState.floors[0].name
            if (firstFloorName && firstFloorName !== '1楼' && firstFloorName !== '默认楼层' && firstFloorName !== '未命名楼层') {
              setProjectAddress(firstFloorName)
              console.log(`🏗️ 从楼层名称设置项目地址: "${firstFloorName}"`)
            }
          }

          previewActions.closePreview()

          // 🔧 改进成功消息，提示客户信息状态
          const clientMessage = selectedClient && selectedClient !== ''
            ? (selectedClient === 'new' ? '新客户信息已保留' : '客户选择已保留')
            : '请记得选择客户'

          alert(`✅ 成功导入 ${convertedFloors.length} 个楼层的数据！\n${clientMessage}`)
        }
      } else if (previewState.importType === 'multi_project') {
        // 多项目模式：创建多个订单

        // 🔧 特殊处理：如果只有一个项目，转换为单项目模式
        if (previewState.projects.length === 1 && previewState.selectedProjectIds.length === 1) {
          console.log('🔄 检测到单项目，转换为单项目模式')
          const singleProject = previewState.projects[0]

          // 🔧 新增：客户信息验证和保持逻辑
          console.log('🔍 单项目转换模式客户信息检查:', {
            selectedClient,
            newClientData,
            existingClientsCount: existingClients.length
          })

          // 如果在预览中选择了客户，验证客户信息
          if (selectedClient && selectedClient !== '') {
            console.log('✅ 客户信息已选择，将保持在主表单中:', selectedClient)

            // 如果选择的是新客户，验证新客户信息是否完整
            if (selectedClient === 'new') {
              if (!newClientData.name || !newClientData.phone) {
                alert('请填写完整的新客户信息（姓名和电话）')
                return
              }
              console.log('✅ 新客户信息验证通过')
            } else {
              // 验证现有客户是否存在
              const client = existingClients.find((c: any) => c.id === selectedClient)
              if (!client) {
                alert('所选客户不存在，请重新选择客户')
                return
              }
              console.log('✅ 现有客户验证通过:', client.name)
            }
          } else {
            console.log('ℹ️ 未选择客户，导入后需要在主表单中选择客户')
          }

          // 转换为楼层数据并设置到主表单
          const convertedFloors = singleProject.floors.map(floor => ({
            ...floor,
            vents: floor.vents.map(vent => ({
              ...vent,
              isEditing: false,
              hasChanges: false,
              validationErrors: undefined,
              originalData: undefined
            }))
          }))

          setFloors(convertedFloors)

          // 先关闭预览
          previewActions.closePreview()

          // 🔧 在关闭预览后设置项目地址
          const projectName = singleProject.projectAddress
          if (projectName && projectName !== '未命名项目' && projectName.trim().length > 0) {
            // 延迟设置，确保预览关闭完成
            setTimeout(() => {
              setProjectAddress(projectName)
              console.log(`🏗️ 预览关闭后设置项目地址: "${projectName}"`)
            }, 50)
          } else {
            console.log(`⚠️ 项目名称为空或无效，跳过设置项目地址:`, {
              projectAddress: singleProject.projectAddress,
              projectName: projectName
            })
          }

          // 🔧 改进成功消息，提示客户信息状态
          const clientMessage = selectedClient && selectedClient !== ''
            ? (selectedClient === 'new' ? '新客户信息已保留' : '客户选择已保留')
            : '请记得选择客户'

          alert(`✅ 成功导入 ${convertedFloors.length} 个楼层的数据！\n${clientMessage}`)
          return
        }

        console.log('🔍 预览确认客户信息验证:', {
          selectedClient,
          newClientData,
          existingClientsCount: existingClients.length
        })

        // 首先验证是否选择了客户
        if (!selectedClient || selectedClient === '') {
          alert('请先选择客户再确认导入')
          return
        }

        // 如果选择的是新客户，验证新客户信息是否完整
        if (selectedClient === 'new') {
          if (!newClientData.name || !newClientData.phone) {
            alert('请填写完整的新客户信息（姓名和电话）')
            return
          }
        } else {
          // 验证现有客户是否存在
          const client = existingClients.find((c: any) => c.id === selectedClient)
          if (!client) {
            alert('所选客户不存在，请重新选择客户')
            return
          }
        }

        const selectedProjects = previewState.projects.filter(p =>
          previewState.selectedProjectIds.includes(p.id)
        )

        if (selectedProjects.length === 0) {
          alert('请至少选择一个项目进行导入')
          return
        }

        // 调用批量创建订单逻辑
        await handleCreateMultipleOrdersFromPreview(selectedProjects)
      }
    } catch (error) {
      console.error('确认导入失败:', error)
      alert('确认导入失败，请重试')
    }
  }

  // 处理预览编辑取消
  const handlePreviewCancel = () => {
    previewActions.closePreview()
  }

  // 获取当前订单信息
  const getCurrentOrderInfo = () => {
    // 获取客户信息
    let clientId = selectedClient
    let clientName = ''
    let clientPhone = ''
    let clientEmail = ''
    let referrerId = ''
    let referrerName = ''

    if (selectedClient === 'new') {
      clientId = 'new'
      clientName = newClientData.name
      clientPhone = newClientData.phone
      clientEmail = newClientData.email
      referrerId = newClientData.referrerId
      referrerName = newClientData.referrerName
    } else {
      const client = existingClients.find((c: any) => c.id === selectedClient)
      if (client) {
        clientName = (client as any).name || ''
        clientPhone = (client as any).phone || ''
        clientEmail = (client as any).email || ''
      }
    }

    return {
      clientId,
      clientName,
      clientPhone,
      clientEmail,
      projectAddress,
      orderDate: new Date().toISOString().split('T')[0], // 当前日期
      referrerId,
      referrerName
    }
  }

  // 从预览数据创建多个订单
  const handleCreateMultipleOrdersFromPreview = async (projects: any[]) => {
    try {
      const factoryId = getCurrentFactoryId()
      const currentUser = getCurrentUser()

      if (!factoryId || !currentUser) {
        alert('请先登录')
        return
      }

      // 验证客户信息（这里的验证是双重保险，主要验证已在 handlePreviewConfirm 中完成）
      if (!selectedClient || selectedClient === '') {
        alert('请选择客户')
        return
      }

      const orderInfo = getCurrentOrderInfo()

      console.log('🔍 多项目导入客户信息验证:', {
        selectedClient,
        orderInfo,
        existingClientsCount: existingClients.length,
        newClientData
      })

      // 验证客户信息是否完整
      if (!orderInfo.clientId || orderInfo.clientId === '') {
        alert('请选择客户')
        return
      }

      // 对于现有客户，验证是否能找到客户信息
      if (orderInfo.clientId !== 'new') {
        const client = existingClients.find((c: any) => c.id === orderInfo.clientId)
        if (!client) {
          alert('所选客户不存在，请重新选择客户')
          return
        }
        if (!client.name) {
          alert('客户信息不完整，请重新选择客户')
          return
        }
      }

      // 如果是新客户，验证新客户信息
      if (orderInfo.clientId === 'new') {
        if (!orderInfo.clientName || !orderInfo.clientPhone) {
          alert('请填写完整的新客户信息（姓名和电话）')
          return
        }
      }

      let successCount = 0
      const createdOrderIds: string[] = []
      let finalClientId = typeof orderInfo.clientId === 'string' ? orderInfo.clientId :
                          (orderInfo.clientId && typeof orderInfo.clientId === 'object' ? orderInfo.clientId.id : '')
      let finalClientName = orderInfo.clientName
      let finalClientPhone = orderInfo.clientPhone

      // 如果是新客户，先创建客户记录
      if (orderInfo.clientId === 'new') {
        console.log('🆕 创建新客户记录...')

        const clientData = {
          factoryId,
          name: orderInfo.clientName,
          phone: orderInfo.clientPhone,
          email: orderInfo.clientEmail || undefined,
          company: newClientData.company || undefined,
          address: orderInfo.projectAddress || undefined,
          referrerId: orderInfo.referrerId || undefined,
          referrerName: orderInfo.referrerName || undefined
        }

        try {
          const createdClient = await db.createClient(clientData)
          if (createdClient) {
            finalClientId = createdClient.id
            finalClientName = createdClient.name
            finalClientPhone = createdClient.phone
            console.log('✅ 客户创建成功:', createdClient)
          } else {
            alert('客户创建失败，请重试')
            return
          }
        } catch (error) {
          console.error('❌ 客户创建失败:', error)
          alert('客户创建失败，请重试')
          return
        }
      }

      for (const project of projects) {
        try {
          // 🔍 调试：检查项目数据结构
          console.log('🔍 项目数据结构检查:', {
            projectAddress: project.projectAddress,
            floorsCount: project.floors?.length || 0,
            floors: project.floors?.map((f: any) => ({
              floorName: f.floorName,
              ventsCount: f.vents?.length || 0,
              vents: f.vents?.map((v: any) => `${v.type} ${v.length}×${v.width}`) || []
            })) || []
          })

          // 转换预览项目数据为订单格式
          const orderItems: OrderItem[] = []

          if (!project.floors || project.floors.length === 0) {
            console.error('❌ 项目没有楼层数据:', project.projectAddress)
            continue
          }

          project.floors.forEach((floor: any) => {
            if (!floor.vents || floor.vents.length === 0) {
              console.warn('⚠️ 楼层没有风口数据:', floor.floorName)
              return
            }

            floor.vents.forEach((vent: any) => {
              orderItems.push({
                id: vent.id,
                productName: getProductTypeName(vent.type) || '风口',
                productType: vent.type,
                specifications: `${vent.length}×${vent.width}mm`,
                dimensions: {
                  length: vent.length,
                  width: vent.width
                },
                // 兼容字段
                type: vent.type,
                length: vent.length,
                width: vent.width,
                quantity: vent.quantity,
                unitPrice: vent.unitPrice,
                totalPrice: vent.totalPrice,
                notes: vent.notes || '',
                floor: floor.floorName
              })
            })
          })

          // 🔍 检查是否有有效的订单项目
          if (orderItems.length === 0) {
            console.error('❌ 项目没有有效的订单项目:', project.projectAddress)
            continue
          }

          console.log('✅ 订单项目数量:', orderItems.length)

          // 生成订单号
          const orderNumber = await generateOrderNumber()

          // 创建订单对象（不包含id，由数据库自动生成）
          const orderData: Omit<Order, 'id'> = {
            orderNumber,
            factoryId,
            clientId: finalClientId,
            clientName: finalClientName,
            clientPhone: finalClientPhone,
            projectAddress: project.projectAddress || orderInfo.projectAddress,
            items: orderItems,
            totalAmount: Math.round(project.floors.reduce((sum: number, floor: any) =>
              sum + floor.vents.reduce((ventSum: number, vent: any) => ventSum + vent.totalPrice, 0), 0
            ) * 10) / 10,
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: currentUser.id,
            notes: `项目：${project.projectAddress || '未指定项目'}`
          }

          // 保存订单到数据库
          const savedOrder = await db.createOrder(orderData)

          if (savedOrder) {
            createdOrderIds.push(savedOrder.id)
            successCount++
            console.log(`✅ 订单创建成功 (项目: ${project.projectAddress}):`, savedOrder.id)
          } else {
            console.error(`❌ 订单创建失败 (项目: ${project.projectAddress}): 数据库返回null`)
          }

        } catch (error) {
          console.error(`创建订单失败 (项目: ${project.projectAddress}):`, error)
        }
      }

      if (successCount > 0) {
        // 改进用户体验：先关闭预览，显示成功消息，然后跳转
        previewActions.closePreview()
        alert(`✅ 成功创建 ${successCount} 个订单！正在跳转到订单列表...`)

        // 短暂延迟后跳转，让用户看到预览已关闭
        setTimeout(() => {
          router.push('/factory/orders')
        }, 1000)
      } else {
        alert('订单创建失败，请重试')
      }
    } catch (error) {
      console.error('批量创建订单失败:', error)
      alert('批量创建订单失败，请重试')
    }
  }

  // 处理多个项目文件导入
  const handleMultiProjectImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setImportType('multi')
    setImporting(true)
    try {
      const XLSX = await import('xlsx')
      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

      // 解析数据并转换为订单格式
      const importResult = parseImportedData(jsonData)

      if (importResult.projects.length > 0) {
        // 多项目导入：启动预览编辑模式
        console.log('🏗️ 检测到多项目数据:', importResult.projects)
        previewActions.initializePreview('multi_project', { projects: importResult.projects })
      } else if (importResult.floors.length > 0) {
        // 如果只有单项目数据，转换为项目格式显示预览
        const singleProject = {
          id: generateUniqueId('project'),
          orderDate: '',
          projectAddress: '导入的项目',
          floors: importResult.floors,
          totalAmount: Math.round(importResult.floors.reduce((sum, floor) => sum + floor.totalPrice, 0) * 10) / 10
        }
        previewActions.initializePreview('multi_project', { projects: [singleProject] })
      } else {
        alert('未能识别到有效的订单数据，请检查文件格式')
      }
    } catch (error) {
      console.error('导入文件失败:', error)
      alert('导入文件失败，请检查文件格式是否正确')
    } finally {
      setImporting(false)
      setImportType(null)
      // 清空文件输入
      event.target.value = ''
    }
  }





  // 获取客户信息的辅助函数
  const getClientName = (clientId: string): string => {
    // 如果是新客户（在批量创建过程中，selectedClient可能仍然是对象）
    if (clientId === 'new' && typeof selectedClient === 'object' && selectedClient?.name) {
      return selectedClient.name
    }
    // 如果是现有客户或已创建的新客户
    const client = existingClients.find((c: any) => c.id === clientId)
    if (client) {
      return (client as any).name
    }
    // 如果在现有客户列表中找不到，但selectedClient是对象且有name，使用它
    if (typeof selectedClient === 'object' && selectedClient?.name) {
      return selectedClient.name
    }
    return ''
  }

  const getClientPhone = (clientId: string): string => {
    // 如果是新客户（在批量创建过程中，selectedClient可能仍然是对象）
    if (clientId === 'new' && typeof selectedClient === 'object' && selectedClient?.phone) {
      return selectedClient.phone
    }
    // 如果是现有客户或已创建的新客户
    const client = existingClients.find((c: any) => c.id === clientId)
    if (client) {
      return (client as any).phone
    }
    // 如果在现有客户列表中找不到，但selectedClient是对象且有phone，使用它
    if (typeof selectedClient === 'object' && selectedClient?.phone) {
      return selectedClient.phone
    }
    return ''
  }

  // 多项目智能识别解析导入数据
  // 支持一个Excel文件包含多个项目的订单数据
  // 识别内容：订货日期、项目地址、尺寸、风口类型、颜色、数量
  const parseImportedData = (data: any[][]): { projects: ProjectData[], floors: FloorData[] } => {
    const projects: ProjectData[] = []
    const floors: FloorData[] = []
    let currentProject: ProjectData | null = null
    let currentFloor: FloorData | null = null

    console.log('🔍 开始多项目智能解析Excel数据，共', data.length, '行')

    for (let i = 0; i < data.length; i++) {
      const row = data[i]
      if (!row || row.length === 0) continue

      console.log(`📋 解析第${i+1}行:`, row)

      // 逐行从左到右识别数据
      const rowData = parseRowData(row)

      // 检查是否为新项目开始
      const isNewProject = detectNewProject(row, i, data)
      if (isNewProject.isNew) {
        console.log('🏗️ 识别到新项目:', isNewProject.projectInfo)

        // 保存当前楼层到当前项目
        if (currentFloor && currentProject) {
          currentProject.floors.push(currentFloor)
          currentFloor = null
        }

        // 保存当前项目（只有包含风口数据的项目才保存）
        if (currentProject && currentProject.floors.length > 0) {
          projects.push(currentProject)
        } else if (currentProject) {
          console.log('🗑️ 跳过空项目:', currentProject.projectAddress)
        }

        // 创建新项目
        currentProject = {
          id: generateUniqueId('project'),
          orderDate: isNewProject.projectInfo.orderDate || '',
          projectAddress: isNewProject.projectInfo.projectAddress || '',
          floors: [],
          totalAmount: 0
        }
      }

      // 如果识别到楼层信息，创建新楼层
      if (rowData.floorInfo) {
        console.log('🏢 识别到楼层:', rowData.floorInfo)

        // 保存当前楼层到当前项目
        if (currentFloor && currentProject) {
          currentProject.floors.push(currentFloor)
        }

        // 创建新楼层
        currentFloor = {
          id: generateUniqueId('floor'),
          floorName: rowData.floorInfo,
          vents: [],
          totalPrice: 0
        }
      }

      // 如果识别到风口数据，添加到当前楼层
      if (rowData.ventData) {
        console.log('🌪️ 识别到风口数据:', rowData.ventData)
        // 如果没有当前楼层，创建默认楼层
        if (!currentFloor) {
          currentFloor = {
            id: generateUniqueId('floor'),
            floorName: '1楼',
            vents: [],
            totalPrice: 0
          }
        }

        // 验证并修正数量
        const validatedQuantity = Math.max(1, rowData.ventData.quantity || 1)

        const vent: VentItem = {
          id: generateUniqueId('vent'),
          type: rowData.ventData.type,
          length: rowData.ventData.length,
          width: rowData.ventData.width,
          quantity: validatedQuantity,
          unitPrice: rowData.ventData.unitPrice || getDefaultPriceByType(rowData.ventData.type),
          totalPrice: 0,
          notes: rowData.ventData.notes
        }

        console.log(`🔢 最终数量确认: ${validatedQuantity}`)

        // 计算价格
        vent.totalPrice = calculateVentPriceLocal(vent)
        currentFloor.vents.push(vent)
      }

      // 如果识别到项目地址，更新当前项目信息（但不创建新项目）
      if (rowData.projectAddress && currentProject && !currentProject.projectAddress) {
        console.log('🏠 更新当前项目地址:', rowData.projectAddress)
        currentProject.projectAddress = rowData.projectAddress
      }
    }

    // 添加最后一个楼层到当前项目
    if (currentFloor && currentProject) {
      currentProject.floors.push(currentFloor)
    }

    // 添加最后一个项目（只有包含风口数据的项目才保存）
    if (currentProject && currentProject.floors.length > 0) {
      projects.push(currentProject)
    } else if (currentProject) {
      console.log('🗑️ 跳过最后的空项目:', currentProject.projectAddress)
    }

    // 如果没有识别到项目，将所有楼层作为单个项目处理
    if (projects.length === 0 && currentFloor) {
      floors.push(currentFloor)

      // 计算楼层总价 - 精确到一位小数
      floors.forEach(floor => {
        const floorTotal = floor.vents.reduce((sum, vent) => sum + vent.totalPrice, 0)
        floor.totalPrice = Math.round(floorTotal * 10) / 10
      })

      console.log('✅ 单项目解析完成，共识别到', floors.length, '个楼层')
      return { projects: [], floors }
    }

    // 计算每个项目的总金额 - 精确到一位小数
    projects.forEach(project => {
      project.floors.forEach(floor => {
        const floorTotal = floor.vents.reduce((sum, vent) => sum + vent.totalPrice, 0)
        floor.totalPrice = Math.round(floorTotal * 10) / 10
      })
      const projectTotal = project.floors.reduce((sum, floor) => sum + floor.totalPrice, 0)
      project.totalAmount = Math.round(projectTotal * 10) / 10
    })

    console.log('✅ 多项目解析完成，共识别到', projects.length, '个项目')
    return { projects, floors: [] }
  }

  // 按列位置识别数据 (基于Excel表格结构优化)
  const parseRowData = (row: any[]) => {
    const result = {
      floorInfo: null as string | null,
      ventData: null as any,
      projectAddress: null as string | null
    }

    console.log(`  📝 解析行数据 (${row.length}列):`, row)

    // 优先检查地址列（第2列，索引1）的项目信息
    const addressColumnIndex = 1
    const addressCell = String(row[addressColumnIndex] || '').trim()

    if (addressCell && addressCell.length > 2) {
      // 排除表头
      const isTableHeader = addressCell.includes('地址') || addressCell.includes('原始信息') ||
                           addressCell.includes('序号') || addressCell.includes('数量')

      if (!isTableHeader) {
        // 检查是否为项目地址（更宽松的识别规则）
        const isProjectAddress =
          // 包含项目关键词
          addressCell.includes('栋') || addressCell.includes('单元') || addressCell.includes('号') ||
          addressCell.includes('公馆') || addressCell.includes('豪门') || addressCell.includes('小区') ||
          addressCell.includes('广场') || addressCell.includes('大厦') || addressCell.includes('中心') ||
          addressCell.includes('花园') || addressCell.includes('苑') || addressCell.includes('城') ||
          addressCell.includes('府') || addressCell.includes('庭') || addressCell.includes('居') ||
          addressCell.includes('村') || addressCell.includes('社区') ||
          // 行政区划关键词
          addressCell.includes('县') || addressCell.includes('市') || addressCell.includes('区') ||
          addressCell.includes('镇') || addressCell.includes('乡') ||
          // 房间号格式：2-1401（2单元1401房间）
          /^\d+-\d+$/.test(addressCell) ||
          // 楼栋格式：33栋、19栋1单元
          /^\d+栋/.test(addressCell) ||
          // 复合地址：高迪公馆2-1401
          /^.+\d+-\d+$/.test(addressCell) ||
          // 包含中文地址信息且长度合适（降低长度要求支持短地址）
          (addressCell.length >= 3 && /[\u4e00-\u9fa5]/.test(addressCell) &&
           !addressCell.includes('出风') && !addressCell.includes('回风') &&
           !addressCell.includes('检修') && !addressCell.includes('白色') &&
           !addressCell.includes('黑色'))

        if (isProjectAddress) {
          result.projectAddress = addressCell
          console.log(`    🏠 识别为项目地址(地址列): ${addressCell}`)
          // 不要直接返回，继续解析可能的风口数据
        }
      }
    }

    // 检查是否为楼层信息行（通常在序号列或地址列）
    if (row[0]) {
      const floorInfo = identifyFloorInfo(String(row[0]).trim())
      if (floorInfo) {
        result.floorInfo = floorInfo
        console.log(`    🏢 识别为楼层: ${floorInfo}`)
        // 不要直接返回，继续检查是否还有风口数据
      }
    }

    // 检查地址列是否包含楼层信息
    if (addressCell && !result.floorInfo) {
      const floorInfo = identifyFloorInfo(addressCell)
      if (floorInfo) {
        result.floorInfo = floorInfo
        console.log(`    🏢 识别为楼层(地址列): ${floorInfo}`)
        // 不要直接返回，继续检查是否还有风口数据
      }
    }

    // 按列位置识别风口数据
    const ventData = parseVentByColumns(row)
    if (ventData) {
      result.ventData = ventData
      console.log(`    🌪️ 识别为风口数据:`, ventData)
    }

    return result
  }

  // 识别房间号格式（如 B101, A201, C301 等）
  const identifyRoomNumber = (cellStr: string): string | null => {
    if (!cellStr) return null

    // 匹配房间号格式：B101, A201, C301 等
    const roomPatterns = [
      /^[A-Z]\d{3}$/i,           // B101, A201, C301
      /^[A-Z]\d{2}$/i,           // B01, A02 (较短的房间号)
      /^[A-Z]\d{4}$/i,           // B1001 (较长的房间号)
    ]

    for (const pattern of roomPatterns) {
      if (pattern.test(cellStr)) {
        return cellStr
      }
    }

    return null
  }

  // 识别楼层信息（简化版：只识别明确的楼层标题）
  const identifyFloorInfo = (cellStr: string, hasFloorSequence: boolean = false): string | null => {
    if (!cellStr) return null

    // 🚫 简化规则：字母开头的房间号不再识别为楼层，直接作为备注
    const roomNumber = identifyRoomNumber(cellStr)
    if (roomNumber) {
      // 所有房间号都不作为楼层，统一作为备注处理
      return null
    }

    // 只匹配明确的楼层格式：1楼、2F、B1楼、地下一层、一楼、二楼等
    const floorPatterns = [
      /^(\d+)[楼层]$/i,           // 严格匹配：1楼、2层（必须以楼层结尾）
      /^(\d+)F$/i,                // 严格匹配：1F、2F
      /^[B地下]+(\d+)[楼层]$/i,   // 地下楼层：B1楼、地下1层
      /^第(\d+)层$/,              // 第1层、第2层
      // 中文数字楼层（必须以楼层结尾）
      /^(一|二|三|四|五|六|七|八|九|十)[楼层]$/i,
      /^(地下|负)(一|二|三|四|五)[楼层]$/i,
      // 特殊格式（但要避免误识别）
      /^(\d+)楼$/,                // 1楼（但不匹配包含其他内容的）
    ]

    for (const pattern of floorPatterns) {
      const match = cellStr.match(pattern)
      if (match) {
        return cellStr
      }
    }

    return null
  }

  // 🔧 改进的房间信息识别（更精确的匹配）
  const identifyRoomInfo = (line: string): string | null => {
    if (!line) return null

    const trimmed = line.trim()

    // 🔧 房间格式匹配（按优先级排序）
    const roomPatterns = [
      // 1. 功能性房间名称（优先级最高）
      /^(活动室|客厅|茶室|棋牌室|餐厅|厨房|KTV|会议室|办公室|卧室|书房|阳台|卫生间|洗手间|储物间|走廊|过道|大厅|接待室|休息室|娱乐室|健身房|音响室|设备间|机房|库房|仓库|主卧|次卧|儿童房|老人房|保姆房|衣帽间|玄关|餐厅|客餐厅|起居室|影音室|琴房|画室|工作室)$/,

      // 2. 房间号格式
      /^[A-Z]\d{3}$/i,           // A301, B202 等（3位数字）
      /^[A-Z]\d{2}$/i,           // A01, B02 等（2位数字）
      /^[A-Z]\d{4}$/i,           // A1001 等（4位数字）

      // 3. 其他房间格式
      /^房间\d+$/,               // 房间1, 房间2 等
      /^\d+号房$/,               // 1号房, 2号房 等
      /^[东南西北][A-Z]?\d*$/,    // 东A1, 南1, 西B2 等

      // 4. 区域标识
      /^[A-Z]区$/i,              // A区, B区 等
      /^第[一二三四五六七八九十\d]+房间$/,  // 第一房间, 第1房间 等
    ]

    for (let i = 0; i < roomPatterns.length; i++) {
      const pattern = roomPatterns[i]
      if (pattern.test(trimmed)) {
        console.log(`🚪 识别为房间信息 (模式${i+1}): ${trimmed}`)
        return trimmed
      }
    }

    console.log(`❌ 不是房间格式: ${trimmed}`)
    return null
  }

  // 识别项目地址
  const identifyProjectAddress = (cellStr: string): string | null => {
    if (!cellStr || cellStr.length < 5) return null

    // 项目地址关键词
    const addressKeywords = ['市', '区', '县', '路', '街', '道', '号', '项目', '工程', '大厦', '广场', '中心', '小区']
    const hasAddressKeyword = addressKeywords.some(keyword => cellStr.includes(keyword))

    if (hasAddressKeyword && cellStr.length > 5) {
      return cellStr
    }

    return null
  }

  // 检测新项目开始（基于Excel表格结构优化）
  const detectNewProject = (row: any[], rowIndex: number, allData: any[][]): {
    isNew: boolean,
    projectInfo: {
      orderDate?: string
      clientName?: string
      projectAddress?: string
    }
  } => {
    const result = {
      isNew: false,
      projectInfo: {} as {
        orderDate?: string
        clientName?: string
        projectAddress?: string
      }
    }

    // 检查是否包含订货日期（支持多种格式）
    const datePatterns = [
      /\d{4}\/\d{1,2}\/\d{1,2}/,     // 2023/8/23
      /\d{4}-\d{1,2}-\d{1,2}/,       // 2023-8-23
      /\d{4}\.\d{1,2}\.\d{1,2}/,     // 2023.8.23
      /\d{4}年\d{1,2}月\d{1,2}日/    // 2023年8月23日
    ]
    const rowText = row.join(' ')

    // 规则1：检测到订货日期行（强制新项目标识）
    if (rowText.includes('订货日期') || rowText.includes('日期')) {
      for (const pattern of datePatterns) {
        const dateMatch = rowText.match(pattern)
        if (dateMatch) {
          result.isNew = true
          result.projectInfo.orderDate = dateMatch[0]
          console.log('📅 检测到订货日期，强制新项目:', dateMatch[0])
          return result // 立即返回，这是最强的新项目标识
        }
      }
    }

    // 规则1.5：单独的日期行（没有"订货"字样但符合日期格式）
    for (const pattern of datePatterns) {
      if (pattern.test(rowText) && rowText.length < 20) { // 短行且包含日期
        const dateMatch = rowText.match(pattern)
        if (dateMatch) {
          result.isNew = true
          result.projectInfo.orderDate = dateMatch[0]
          console.log('📅 检测到独立日期行，强制新项目:', dateMatch[0])
          return result
        }
      }
    }

    // 规则2：检测到订货单位行（客户信息）
    if (rowText.includes('订货单位') && rowText.length > 10) {
      const clientName = rowText.replace('订货单位:', '').replace('订货单位：', '').trim()
      if (clientName) {
        result.projectInfo.clientName = clientName
        console.log('🏢 检测到客户:', clientName)
      }
    }

    // 规则3：检测地址列的项目信息（最重要的识别规则）
    // 根据您的表格结构，地址列在第2列（索引1）
    const addressColumnIndex = 1 // "地址"列
    const addressCell = String(row[addressColumnIndex] || '').trim()

    if (addressCell && addressCell.length > 2) {
      // 排除表头和无效内容
      const isTableHeader = addressCell.includes('地址') || addressCell.includes('原始信息') ||
                           addressCell.includes('序号') || addressCell.includes('数量') ||
                           addressCell.includes('长度') || addressCell.includes('宽度') ||
                           addressCell.includes('颜色') || addressCell.includes('单价') ||
                           addressCell.includes('金额') || addressCell.includes('备注')

      if (!isTableHeader) {
        // 更全面的项目地址识别规则
        const isProjectAddress =
          // 明确的项目地址格式
          (addressCell.includes('栋') && addressCell.length >= 4) ||  // 江山豪门33栋
          (addressCell.includes('公馆') && addressCell.includes('-')) || // 高迪公馆2-1401
          (addressCell.includes('豪门') && addressCell.includes('栋')) || // 江山豪门33栋
          // 其他明确的项目标识
          (addressCell.includes('小区') && addressCell.length >= 5) ||
          (addressCell.includes('广场') && addressCell.length >= 5) ||
          (addressCell.includes('大厦') && addressCell.length >= 5) ||
          (addressCell.includes('花园') && addressCell.length >= 4) ||
          (addressCell.includes('苑') && addressCell.length >= 3) ||    // 保利苑
          (addressCell.includes('城') && addressCell.length >= 4) ||
          (addressCell.includes('府') && addressCell.length >= 4) ||
          (addressCell.includes('庭') && addressCell.length >= 4) ||
          (addressCell.includes('居') && addressCell.length >= 4) ||
          (addressCell.includes('村') && addressCell.length >= 4) ||    // 民族新村
          (addressCell.includes('新村') && addressCell.length >= 5) ||
          (addressCell.includes('社区') && addressCell.length >= 5) ||
          // 行政区划：县、市、区等（支持短地址）
          (addressCell.includes('县') && addressCell.length >= 3) ||    // 上林县
          (addressCell.includes('市') && addressCell.length >= 3) ||    // 南宁市
          (addressCell.includes('区') && addressCell.length >= 3) ||    // 青秀区
          (addressCell.includes('镇') && addressCell.length >= 3) ||    // 大塘镇
          (addressCell.includes('乡') && addressCell.length >= 3) ||    // 三里乡
          // 地址格式：包含"路"、"街"、"巷"等
          (addressCell.includes('路') && addressCell.length >= 5) ||
          (addressCell.includes('街') && addressCell.length >= 5) ||
          (addressCell.includes('巷') && addressCell.length >= 5)

        if (isProjectAddress) {
          result.projectInfo.projectAddress = addressCell
          console.log('🏠 检测到项目地址:', addressCell)

          // 检查是否为新项目
          // 查找前面最近的有效项目地址
          let previousProjectAddress = ''
          let foundPreviousProject = false

          for (let i = rowIndex - 1; i >= 0; i--) {
            const prevRow = allData[i]
            const prevAddressCell = String(prevRow[addressColumnIndex] || '').trim()

            // 检查前面的地址是否也是有效的项目地址
            const isPrevProjectAddress =
              (prevAddressCell.includes('栋') && prevAddressCell.length >= 4) ||
              (prevAddressCell.includes('公馆') && prevAddressCell.includes('-')) ||
              (prevAddressCell.includes('豪门') && prevAddressCell.includes('栋')) ||
              (prevAddressCell.includes('小区') && prevAddressCell.length >= 5) ||
              (prevAddressCell.includes('广场') && prevAddressCell.length >= 5) ||
              (prevAddressCell.includes('大厦') && prevAddressCell.length >= 5) ||
              (prevAddressCell.includes('苑') && prevAddressCell.length >= 3) ||
              (prevAddressCell.includes('园') && prevAddressCell.length >= 4) ||
              (prevAddressCell.includes('府') && prevAddressCell.length >= 4) ||
              (prevAddressCell.includes('县') && prevAddressCell.length >= 3) ||
              (prevAddressCell.includes('市') && prevAddressCell.length >= 3) ||
              (prevAddressCell.includes('区') && prevAddressCell.length >= 3) ||
              (prevAddressCell.includes('镇') && prevAddressCell.length >= 3) ||
              (prevAddressCell.includes('乡') && prevAddressCell.length >= 3)

            if (isPrevProjectAddress) {
              previousProjectAddress = prevAddressCell
              foundPreviousProject = true
              break
            }
          }

          // 如果找到了不同的项目地址，或者这是第一个项目地址，都标记为新项目
          if (foundPreviousProject && previousProjectAddress !== addressCell) {
            result.isNew = true
            console.log('🆕 检测到新项目，从', previousProjectAddress, '切换到', addressCell)
          } else if (!foundPreviousProject && rowIndex >= 0) {
            // 如果这是第一个项目地址，标记为新项目（移除行数限制）
            result.isNew = true
            console.log('🆕 检测到第一个项目:', addressCell)
          }
        }
      }
    }

    return result
  }

  // 按列位置解析风口数据 (基于您的Excel表格结构优化)
  const parseVentByColumns = (row: any[]): any => {
    if (!row || row.length < 6) return null

    console.log(`    🔍 按列解析风口数据 (${row.length}列):`, row)

    // 根据您的表格结构调整列位置：
    // 列0: 序号
    // 列1: 地址 (项目信息，已在上层处理)
    // 列2: 原始信息/mm (尺寸信息)
    // 列3: 数量
    // 列4: 长度/mm
    // 列5: 宽度/mm
    // 列6: 颜色
    // 列7: 单价
    // 列8: 金额/元
    // 列9: 备注

    // 检查是否为有效的数据行（有序号且有尺寸信息）
    const serialNumber = String(row[0] || '').trim()
    // 更宽松的序号验证：包含数字即可（支持 "1"、"1."、"序号1" 等格式）
    const hasNumber = /\d+/.test(serialNumber)
    const isNotEmpty = serialNumber.length > 0

    console.log(`      🔍 检查序号: "${serialNumber}", 包含数字: ${hasNumber}, 非空: ${isNotEmpty}`)

    if (!hasNumber || !isNotEmpty) {
      console.log(`      ❌ 不是有效数据行，序号: ${serialNumber}`)
      return null
    }

    let dimensions = ''
    let quantity = 1
    let notes = ''
    let ventType = 'double_white_outlet' // 默认类型

    // 列2: 原始信息/mm (尺寸)
    if (row[2]) {
      const sizeStr = String(row[2]).trim()
      if (sizeStr.includes('*') || sizeStr.includes('×') || sizeStr.includes('x')) {
        dimensions = sizeStr.replace(/\*/g, '×').replace(/x/g, '×') // 统一使用×符号
        console.log(`      尺寸(列2): ${dimensions}`)
      }
    }

    // 列3: 数量
    if (row[3]) {
      const qtyStr = String(row[3]).trim()
      const parsedQty = parseQuantity(qtyStr)
      if (parsedQty > 0) {
        quantity = parsedQty
        console.log(`      数量(列3): ${quantity}`)
      }
    }

    // 列6: 颜色信息
    let colorInfo = ''
    if (row[6]) {
      colorInfo = String(row[6]).trim()
      console.log(`      颜色(列6): ${colorInfo}`)
    }

    // 列9: 备注信息
    if (row[9]) {
      const remarkStr = String(row[9]).trim()
      if (remarkStr) {
        const rawNotes = remarkStr
        notes = cleanNotes(rawNotes) // 使用清理函数
        console.log(`      备注(列9): "${rawNotes}" → "${notes}"`)
      }
    }

    // 根据备注信息推断风口类型
    if (notes) {
      if (notes.includes('出风')) {
        ventType = colorInfo.includes('黑') ? 'double_black_outlet' : 'double_white_outlet'
      } else if (notes.includes('回风')) {
        ventType = colorInfo.includes('黑') ? 'black_return' : 'white_return'
      }
      console.log(`      推断类型: ${ventType} (基于备注: ${notes}, 颜色: ${colorInfo})`)
    }

    // 解析尺寸信息
    if (!dimensions) {
      console.log(`      ❌ 没有找到尺寸信息`)
      return null
    }

    const sizeResult = parseDimensions(dimensions)
    if (sizeResult.length <= 0 || sizeResult.width <= 0) {
      console.log(`      ❌ 尺寸解析失败: ${dimensions}`)
      return null
    }

    // 最终确定风口类型
    const finalType = determineVentType(ventType, notes, sizeResult.width, notes)

    console.log(`    ✅ 按列解析完成:`)
    console.log(`       序号: ${serialNumber}`)
    console.log(`       类型: ${finalType}`)
    console.log(`       尺寸: ${sizeResult.length}×${sizeResult.width}mm`)
    console.log(`       数量: ${quantity}`)
    console.log(`       颜色: ${colorInfo || '无'}`)
    console.log(`       备注: ${notes || '无'}`)

    return {
      type: finalType,
      length: sizeResult.length,
      width: sizeResult.width,
      quantity,
      unitPrice: undefined,
      notes: cleanNotes(notes || colorInfo) // 清理备注，备注优先，没有备注时使用颜色信息
    }
  }

  // 从行数据中解析风口信息 (简化版：只识别尺寸、类型、颜色、数量、楼层)
  const parseVentFromRow = (row: any[]): {
    type: VentItem['type']
    length: number
    width: number
    quantity: number
    unitPrice?: number
    notes?: string
  } | null => {
    let length = 0, width = 0
    let ventTypeStr = ''
    let quantity = 1
    let notes = ''
    let colorStr = ''

    console.log('    🔍 逐个检查单元格寻找风口数据 (尺寸+类型+颜色+数量)...')

    // 从左到右检查每个单元格
    for (let i = 0; i < row.length; i++) {
      const cell = row[i]
      if (!cell) continue

      const cellStr = String(cell).trim()
      if (!cellStr) continue

      console.log(`      📝 单元格[${i}]: "${cellStr}"`)

      // 1. 尺寸识别 (优先级最高)
      if (length === 0 || width === 0) {
        const dimensions = parseDimensions(cellStr)
        if (dimensions) {
          length = dimensions.length
          width = dimensions.width
          console.log(`        📏 识别尺寸: ${length}x${width}`)
          continue
        }
      }

      // 2. 数量识别 (优先级高，因为数量对计算很重要)
      const qty = parseQuantity(cellStr)
      if (qty > 0 && qty !== quantity) {
        quantity = qty
        console.log(`        🔢 更新数量: ${quantity}`)
        continue
      }

      // 3. 风口类型识别
      if (!ventTypeStr) {
        const typeInfo = parseVentType(cellStr)
        if (typeInfo) {
          ventTypeStr = typeInfo.type
          if (typeInfo.color) colorStr = typeInfo.color
          if (typeInfo.specialType) {
            // 记录特殊类型信息到备注中，供后续类型判断使用
            notes = notes ? `${notes} ${typeInfo.specialType}` : typeInfo.specialType
          }
          console.log(`        🌪️ 识别类型: ${ventTypeStr}, 颜色: ${colorStr}, 特殊类型: ${typeInfo.specialType || '无'}`)
          continue
        }
      }

      // 4. 颜色识别
      if (!colorStr) {
        const color = parseColor(cellStr)
        if (color) {
          colorStr = color
          console.log(`        🎨 识别颜色: ${colorStr}`)
          continue
        }
      }

      // 5. 单价不从Excel识别，使用系统设置
      // 单价将从系统设置中自动获取

      // 6. 备注信息 (排除已识别的类型、颜色等)
      if (!notes && cellStr.length > 1 && !isNumeric(cellStr) &&
          !parseVentType(cellStr) && !parseColor(cellStr)) {
        const rawNotes = cellStr
        notes = cleanNotes(rawNotes) // 使用清理函数
        if (notes) { // 只有清理后还有内容才记录
          console.log(`        📝 识别备注: "${rawNotes}" → "${notes}"`)
        }
      }
    }

    // 验证是否有足够的数据构成风口
    if (length > 0 && width > 0) {
      // 确定最终的风口类型
      const finalType = determineVentType(ventTypeStr, colorStr, width, notes)

      console.log(`    ✅ 风口数据解析完成:`)
      console.log(`       类型: ${finalType}`)
      console.log(`       尺寸: ${length}x${width}mm`)
      console.log(`       数量: ${quantity}`)
      console.log(`       备注: ${notes || '无'}`)
      console.log(`    💰 单价将从系统设置自动获取`)

      // 验证数量的合理性
      if (quantity <= 0) {
        console.warn(`    ⚠️ 数量异常: ${quantity}，设置为默认值1`)
        quantity = 1
      }

      return {
        type: finalType,
        length,
        width,
        quantity,
        unitPrice: undefined, // 不从Excel获取，使用系统设置
        notes
      }
    }

    console.log(`    ❌ 未能识别到完整的风口数据`)
    return null
  }

  // 解析尺寸信息 - 使用统一的工具函数
  const parseDimensions = (cellStr: string): { length: number, width: number } | null => {
    return parseDimensionString(cellStr)
  }

  // 解析数量 (严格版 - 避免误识别尺寸)
  const parseQuantity = (cellStr: string): number => {
    if (!cellStr) return 0

    const cleanStr = String(cellStr).trim()
    if (!cleanStr) return 0

    // 🚫 排除明显的尺寸格式
    if (/\d+\s*[×xX*+\-]\s*\d+/.test(cleanStr)) {
      return 0 // 包含尺寸格式的不是数量
    }

    // 🚫 排除过大的数字（可能是尺寸）
    if (/^\d{3,}$/.test(cleanStr)) {
      const num = parseInt(cleanStr)
      if (num > 100) {
        return 0 // 大于100的纯数字很可能是尺寸
      }
    }

    // ✅ 支持的严格数量格式
    const quantityPatterns = [
      /^([1-9]|[1-4][0-9]|50)$/,                    // 纯数字: 1-50
      /^([1-9]|[1-4][0-9]|50)[个只件块片张台套]$/,  // 带单位: 1-50个
      /^([1-9]|[1-4][0-9]|50)\s*[个只件块片张台套]$/,   // 带空格: 1-50 个
      /^数量[：:]\s*([1-9]|[1-4][0-9]|50)/,        // 数量: 1-50
      /^qty[：:]\s*([1-9]|[1-4][0-9]|50)/i,        // qty: 1-50
      /^count[：:]\s*([1-9]|[1-4][0-9]|50)/i,      // count: 1-50
    ]

    for (const pattern of quantityPatterns) {
      const match = cleanStr.match(pattern)
      if (match) {
        const qty = parseInt(match[1])
        if (qty >= 1 && qty <= 50) { // 严格限制在1-50范围
          console.log(`        🔢 识别数量: ${qty} (原文: "${cleanStr}")`)
          return qty
        }
      }
    }

    // 支持中文数字
    const chineseNumbers = {
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
      '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5, '陆': 6, '柒': 7, '捌': 8, '玖': 9, '拾': 10,
      '两': 2, '俩': 2
    }

    // 检查是否包含中文数字
    for (const [chinese, number] of Object.entries(chineseNumbers)) {
      if (cleanStr.includes(chinese)) {
        const chinesePattern = new RegExp(`(${chinese})[个只件块片张台套]?`)
        const chineseMatch = cleanStr.match(chinesePattern)
        if (chineseMatch) {
          console.log(`        🔢 识别中文数量: ${number} (原文: "${cleanStr}")`)
          return number
        }
      }
    }

    // 特殊处理：如果单元格只包含数字字符
    const numericOnly = cleanStr.replace(/[^\d]/g, '')
    if (numericOnly && numericOnly.length <= 5) {
      const qty = parseInt(numericOnly)
      if (qty > 0 && qty <= 99999) {
        console.log(`        🔢 提取数字识别数量: ${qty} (原文: "${cleanStr}")`)
        return qty
      }
    }

    return 0
  }

  // 解析风口类型
  const parseVentType = (cellStr: string): { type: string, color?: string, specialType?: string } | null => {
    const lowerStr = cellStr.toLowerCase()

    // 高端风口特殊类型识别
    if (lowerStr.includes('箭型') || lowerStr.includes('箭形')) {
      const isReturn = lowerStr.includes('回风')
      return { type: isReturn ? 'return' : 'outlet', specialType: 'arrow' }
    }

    if (lowerStr.includes('爪型') || lowerStr.includes('爪形') || lowerStr.includes('爪式')) {
      const isReturn = lowerStr.includes('回风')
      return { type: isReturn ? 'return' : 'outlet', specialType: 'claw' }
    }

    if (lowerStr.includes('腻子粉') || lowerStr.includes('腻子') || lowerStr.includes('预埋')) {
      const isReturn = lowerStr.includes('回风')
      const color = lowerStr.includes('黑') ? 'black' : 'white'
      return { type: isReturn ? 'return' : 'outlet', color, specialType: 'putty' }
    }

    if (lowerStr.includes('木纹') || lowerStr.includes('木质') || lowerStr.includes('木色')) {
      const isReturn = lowerStr.includes('回风')
      return { type: isReturn ? 'return' : 'outlet', specialType: 'wood' }
    }

    if (lowerStr.includes('石膏板') || lowerStr.includes('石膏')) {
      const isReturn = lowerStr.includes('回风')
      const color = lowerStr.includes('黑') ? 'black' : 'white'
      return { type: isReturn ? 'return' : 'outlet', color, specialType: 'gypsum' }
    }

    if (lowerStr.includes('黑白') || (lowerStr.includes('双色') && lowerStr.includes('黑') && lowerStr.includes('白'))) {
      const isReturn = lowerStr.includes('回风')
      return { type: isReturn ? 'return' : 'outlet', specialType: 'black_white' }
    }

    if (lowerStr.includes('线型') || lowerStr.includes('线形') || lowerStr.includes('线条')) {
      const isReturn = lowerStr.includes('回风')
      const color = lowerStr.includes('黑') ? 'black' : 'white'
      return { type: isReturn ? 'return' : 'outlet', color, specialType: 'linear' }
    }

    if (lowerStr.includes('黑底') || (lowerStr.includes('白色') && lowerStr.includes('黑'))) {
      const isReturn = lowerStr.includes('回风')
      return { type: isReturn ? 'return' : 'outlet', specialType: 'white_black_bottom' }
    }

    // 出风口关键词
    if (lowerStr.includes('出风口') || lowerStr.includes('出风') || lowerStr.includes('送风')) {
      let color = ''
      if (lowerStr.includes('白') || lowerStr.includes('白色')) color = 'white'
      if (lowerStr.includes('黑') || lowerStr.includes('黑色')) color = 'black'
      return { type: 'outlet', color }
    }

    // 回风口关键词
    if (lowerStr.includes('回风口') || lowerStr.includes('回风') || lowerStr.includes('回气')) {
      let color = ''
      if (lowerStr.includes('白') || lowerStr.includes('白色')) color = 'white'
      if (lowerStr.includes('黑') || lowerStr.includes('黑色')) color = 'black'
      return { type: 'return', color }
    }

    // 检修口
    if (lowerStr.includes('检修口') || lowerStr.includes('检修')) {
      return { type: 'maintenance' }
    }

    return null
  }

  // 智能检测项目名称的函数
  const detectProjectName = (text: string, currentRowIndex: number, allLines: any[]): { isProject: boolean, rowCount: number } => {
    if (!text || typeof text !== 'string') return { isProject: false, rowCount: 0 }

    const cleanText = text.trim()

    // 🔧 强化排除：明显不是项目名称的内容
    if (cleanText.length < 2 ||
        cleanText.includes('出风') || cleanText.includes('回风') ||
        cleanText.includes('检修') || cleanText.includes('白色') ||
        cleanText.includes('黑色') || cleanText.includes('mm') ||
        cleanText.includes('数量') || cleanText.includes('单价') ||
        cleanText.includes('金额') || cleanText.includes('备注') ||
        /^\d+$/.test(cleanText) || // 纯数字
        // 🔧 关键修复：排除所有尺寸格式（包括/、—、:等分隔符）
        /\d+(?:\.\d+)?[*×xX✘✖✕⨯·+\-—–/@&:：乘by]\d+(?:\.\d+)?/.test(cleanText)) {
      console.log(`🚫 排除非项目名称内容: "${cleanText}"`)
      return { isProject: false, rowCount: 0 }
    }

    // 计算这个可能的项目名称对应多少行数据
    let rowCount = 0
    let hasValidData = false

    // 向下查找，直到遇到下一个项目名称或表格结束
    for (let i = currentRowIndex + 1; i < allLines.length; i++) {
      const nextRow = allLines[i]
      if (!nextRow || nextRow.length === 0) continue

      const nextFirstCol = String(nextRow[0] || '').trim()

      // 如果遇到新的项目名称候选，停止计数
      if (nextFirstCol &&
          nextFirstCol.length >= 2 &&
          !nextFirstCol.includes('出风') &&
          !nextFirstCol.includes('回风') &&
          !nextFirstCol.includes('mm') &&
          !/^\d+$/.test(nextFirstCol) &&
          // 🔧 修复：排除所有尺寸格式
          !/\d+(?:\.\d+)?[*×xX✘✖✕⨯·+\-—–/@&:：乘by]\d+(?:\.\d+)?/.test(nextFirstCol) &&
          !identifyFloorInfo(nextFirstCol)) {

        // 检查是否为另一个项目名称
        const hasProjectIndicators =
          nextFirstCol.includes('栋') || nextFirstCol.includes('公馆') ||
          nextFirstCol.includes('小区') || nextFirstCol.includes('花园') ||
          nextFirstCol.includes('广场') || nextFirstCol.includes('大厦') ||
          nextFirstCol.includes('中心') || nextFirstCol.includes('城') ||
          nextFirstCol.includes('苑') || nextFirstCol.includes('路') ||
          nextFirstCol.includes('街') || nextFirstCol.includes('项目') ||
          nextFirstCol.includes('工程')

        // 如果是明确的项目地址，或者是简短的可能项目名称，停止计数
        if (hasProjectIndicators || (nextFirstCol.length <= 6 && /[\u4e00-\u9fa5]/.test(nextFirstCol))) {
          break
        }
      }

      // 检查这一行是否包含有效的风口数据
      const hasValidWindOutletData = nextRow.some((cell: any, index: number) => {
        if (index === 0) return false // 跳过第一列
        const cellStr = String(cell || '').trim()
        return cellStr && (
          /^\d+$/.test(cellStr) || // 数字
          /^\d+\*\d+$/.test(cellStr) || // 尺寸
          cellStr.includes('出风') || cellStr.includes('回风') ||
          cellStr.includes('检修') || cellStr.includes('白色') ||
          cellStr.includes('黑色')
        )
      })

      if (hasValidWindOutletData) {
        rowCount++
        hasValidData = true
      }
    }

    // 判断是否为项目名称
    const isProject =
      // 有明确的项目关键词
      (cleanText.includes('栋') || cleanText.includes('公馆') ||
       cleanText.includes('小区') || cleanText.includes('花园') ||
       cleanText.includes('广场') || cleanText.includes('大厦') ||
       cleanText.includes('中心') || cleanText.includes('城') ||
       cleanText.includes('苑') || cleanText.includes('路') ||
       cleanText.includes('街') || cleanText.includes('项目') ||
       cleanText.includes('工程')) ||
      // 或者是简短的中文名称且对应多行数据
      (cleanText.length >= 2 && cleanText.length <= 6 &&
       /[\u4e00-\u9fa5]/.test(cleanText) &&
       rowCount >= 2 && hasValidData)

    return { isProject, rowCount }
  }

  // 解析颜色
  const parseColor = (cellStr: string): string | null => {
    const lowerStr = cellStr.toLowerCase()
    if (lowerStr.includes('白') || lowerStr.includes('白色')) return 'white'
    if (lowerStr.includes('黑') || lowerStr.includes('黑色')) return 'black'
    return null
  }



  // 判断是否为数字
  const isNumeric = (str: string): boolean => {
    return !isNaN(Number(str)) && !isNaN(parseFloat(str))
  }

  // 测试数量识别功能
  const testQuantityParsing = () => {
    const testCases = ['10', '5个', '8只', '15件', '数量:20', '25 个', 'qty:30']
    console.log('🧪 测试数量识别:')
    testCases.forEach(test => {
      const result = parseQuantity(test)
      console.log(`  "${test}" -> ${result}`)
    })
  }

  // 检测是否为表格格式（Tab分隔的多列数据）
  const isTableFormat = (content: string): boolean => {
    const lines = content.split('\n').filter(line => line.trim())
    if (lines.length < 2) return false

    // 检查是否有多行包含Tab分隔符
    const tabLines = lines.filter(line => line.includes('\t'))
    const hasMultipleColumns = tabLines.some(line => line.split('\t').length >= 5)

    console.log(`🔍 表格格式检测: ${tabLines.length}行包含Tab, 多列数据: ${hasMultipleColumns}`)
    return tabLines.length >= 2 && hasMultipleColumns
  }

  // 解析表格格式的粘贴内容
  const parseTableContent = (content: string): FloorData[] => {
    console.log('📊 开始解析表格格式内容')

    const lines = content.split('\n').map(line => line.trim()).filter(line => line)
    const floors: FloorData[] = []
    let currentFloor: FloorData | null = null
    let currentProject = ''

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      const columns = line.split('\t').map(col => col.trim())

      console.log(`📝 处理表格行 ${i + 1}: ${columns.length}列`)
      console.log(`    列数据:`, columns.map((col, idx) => `[${idx}]="${col}"`).join(', '))

      // 跳过表头行
      if (i === 0 && (line.includes('地址') || line.includes('原始信息') || line.includes('数量'))) {
        console.log('📋 跳过表头行')
        continue
      }

      // 检查第一列是否为项目地址或楼层信息
      const firstCol = columns[0] ? columns[0].trim() : ''
      console.log(`    第一列内容: "${firstCol}" (长度: ${firstCol.length})`)

      if (firstCol.length > 0) {
        // 首先检查是否为楼层信息（优先级最高）
        const floorInfo = identifyFloorInfo(firstCol)
        if (floorInfo) {
          console.log(`🏢 识别到楼层信息: ${floorInfo}`)

          // 保存当前楼层（如果有风口数据）
          if (currentFloor && currentFloor.vents.length > 0) {
            floors.push(currentFloor)
          }

          // 创建新楼层
          currentFloor = {
            id: generateUniqueId('floor'),
            floorName: floorInfo,
            vents: [],
            totalPrice: 0
          }
          console.log(`🏢 创建新楼层: ${floorInfo}`)
          continue // 跳过后续处理，进入下一行
        }

        // 如果不是楼层信息，检查是否为项目名称（使用更智能的识别逻辑）
        const isProjectName = detectProjectName(firstCol, i, lines)

        if (isProjectName.isProject) {
          currentProject = firstCol
          console.log(`🏠 识别项目名称: "${currentProject}" (跨越${isProjectName.rowCount}行)`)
          console.log(`📍 更新项目信息，不创建楼层`)
        }
      }

      // 如果还没有楼层，创建默认楼层（不使用项目地址作为楼层名）
      if (!currentFloor) {
        currentFloor = {
          id: generateUniqueId('floor'),
          floorName: '1楼',  // 默认使用"1楼"
          vents: [],
          totalPrice: 0
        }
        console.log(`🏢 创建默认楼层: ${currentFloor.floorName}`)
      }

      // 解析风口数据（智能列位置判断）
      if (columns.length >= 5 && currentFloor) {
        let originalSize, quantity, length, width, color, remark

        // 根据列数和第一列内容判断数据结构
        const hasProjectInFirstCol = firstCol.length > 0 && (
          // 基础地址关键词
          firstCol.includes('栋') || firstCol.includes('号') || firstCol.includes('室') ||
          firstCol.includes('房') || firstCol.includes('单元') ||
          // 小区/楼盘关键词
          firstCol.includes('公馆') || firstCol.includes('豪门') || firstCol.includes('小区') ||
          firstCol.includes('花园') || firstCol.includes('广场') || firstCol.includes('大厦') ||
          firstCol.includes('中心') || firstCol.includes('城') || firstCol.includes('苑') ||
          // 地理位置关键词
          firstCol.includes('市') || firstCol.includes('区') || firstCol.includes('县') ||
          firstCol.includes('路') || firstCol.includes('街') || firstCol.includes('道') ||
          // 商业/工程项目关键词
          firstCol.includes('项目') || firstCol.includes('工程') || firstCol.includes('店') ||
          firstCol.includes('厂') || firstCol.includes('公司') || firstCol.includes('大楼') ||
          // 特殊项目名称（华润、万科等）
          (firstCol.includes('华润') && firstCol.length >= 4) ||
          (firstCol.includes('万科') && firstCol.length >= 4) ||
          (firstCol.includes('恒大') && firstCol.length >= 4) ||
          (firstCol.includes('碧桂园') && firstCol.length >= 4)
        )

        console.log(`    🔍 判断条件: 列数=${columns.length}, 第一列="${firstCol}", 包含项目=${hasProjectInFirstCol}`)

        if (columns.length >= 9 && hasProjectInFirstCol) {
          // 9列格式：项目地址	原始尺寸	数量	长度	宽度	颜色	单价	金额	备注
          originalSize = columns[1] || ''
          quantity = columns[2] || '1'
          length = columns[3] || ''
          width = columns[4] || ''
          color = columns[5] || ''
          remark = columns[8] || ''
          console.log(`    📊 解析风口数据(9列-有项目): 尺寸=${originalSize}, 数量=${quantity}, 长宽=${length}×${width}, 颜色=${color}, 备注=${remark}`)
        } else {
          // 8列格式：原始尺寸	数量	长度	宽度	颜色	单价	金额	备注
          originalSize = columns[0] || ''
          quantity = columns[1] || '1'
          length = columns[2] || ''
          width = columns[3] || ''
          color = columns[4] || ''
          remark = columns[7] || ''
          console.log(`    📊 解析风口数据(8列-无项目): 尺寸=${originalSize}, 数量=${quantity}, 长宽=${length}×${width}, 颜色=${color}, 备注=${remark}`)
        }

        // 验证是否有有效的尺寸数据
        const lengthNum = parseInt(length)
        const widthNum = parseInt(width)
        const quantityNum = parseInt(quantity) || 1

        if (lengthNum > 0 && widthNum > 0) {
          // 应用智能尺寸识别
          const smartDimensions = smartDimensionRecognition(lengthNum, widthNum)

          // 根据备注和尺寸判断风口类型
          let ventType: VentItem['type'] = 'double_white_outlet'

          // 使用增强的类型判断函数
          ventType = determineVentType('', color || '', smartDimensions.width, remark)

          // 检查颜色
          if (color.includes('黑') || color.includes('黑色')) {
            if (ventType === 'white_return') ventType = 'black_return'
            else if (ventType === 'double_white_outlet') ventType = 'double_black_outlet'
          }

          // 组合备注信息
          let notes = ''
          if (color) notes += color
          if (remark) notes += (notes ? ' ' : '') + remark
          notes = cleanNotes(notes) // 清理备注内容

          const vent: VentItem = {
            id: generateUniqueId('vent'),
            type: ventType,
            length: smartDimensions.length,
            width: smartDimensions.width,
            quantity: quantityNum,
            unitPrice: getDefaultPriceByType(ventType),
            totalPrice: 0,
            notes: notes
          }

          // 计算总价
          vent.totalPrice = calculateVentPrice({
            productType: vent.type,
            length: vent.length,
            width: vent.width,
            quantity: vent.quantity,
            unitPrice: vent.unitPrice
          })

          currentFloor.vents.push(vent)
          console.log(`✅ 添加风口: ${ventType} ${smartDimensions.length}×${smartDimensions.width} ×${quantityNum} (${notes})`)
        }
      }
    }

    // 添加最后一个楼层
    if (currentFloor && currentFloor.vents.length > 0) {
      floors.push(currentFloor)
    }

    // 自动填充项目地址
    if (currentProject && floors.length > 0) {
      setProjectAddress(currentProject)
      console.log(`📍 自动填充项目地址: ${currentProject}`)
    }

    console.log(`📊 表格解析完成，共识别 ${floors.length} 个项目`)
    return floors
  }

  // 解析粘贴的文本内容（使用统一解析引擎）
  const parsePastedContent = (content: string): FloorData[] => {
    console.log('📋 开始智能解析粘贴内容:')
    console.log(content)

    // 检测是否为表格格式
    if (isTableFormat(content)) {
      return parseTableContent(content)
    }

    // 🎯 使用多项目解析引擎来处理粘贴内容
    console.log('🔄 使用多项目解析引擎处理粘贴内容')
    const multiProjectResult = parseMultiProjectContent(content)

    if (multiProjectResult && multiProjectResult.length > 0) {
      console.log(`✅ 多项目解析成功，识别到 ${multiProjectResult.length} 个项目`)

      // 🔧 设置项目地址（如果只有一个项目）
      if (multiProjectResult.length === 1 && multiProjectResult[0].name) {
        const projectName = multiProjectResult[0].name
        if (projectName && projectName !== '未命名项目' && projectName.trim().length > 0) {
          setProjectAddress(projectName)
          console.log(`🏗️ 设置项目地址: "${projectName}"`)
        }
      }

      // 转换 TempProjectData[] 为 FloorData[] 格式
      const floors: FloorData[] = []
      multiProjectResult.forEach(project => {
        project.floors.forEach(floor => {
          // 正确转换楼层格式，确保楼层名称正确传递
          const floorData: FloorData = {
            id: generateUniqueId('floor'),
            floorName: floor.name, // 直接使用楼层名称，已经是正确格式
            vents: floor.vents.map(vent => ({
              ...vent,
              id: generateUniqueId('vent'),
              unitPrice: getDefaultPriceByType(vent.type),
              totalPrice: calculateVentPrice(vent.type, vent.length, vent.width, vent.quantity, getDefaultPriceByType(vent.type))
            })),
            totalPrice: 0 // 会在后续计算中更新
          }
          // 计算楼层总价
          floorData.totalPrice = floorData.vents.reduce((sum, vent) => sum + vent.totalPrice, 0)
          floors.push(floorData)
          console.log(`✅ 转换楼层: "${floor.name}" → "${floorData.floorName}" (${floorData.vents.length}个风口)`)
        })
      })
      return floors
    }

    // 如果多项目解析失败，使用原有逻辑作为备用
    console.log('⚠️ 多项目解析失败，使用原有逻辑')
    const lines = content.split('\n').map(line => line.trim()).filter(line => line)
    const floors: FloorData[] = []
    let currentFloor: FloorData | null = null
    let currentProject = ''
    let currentRoom = '' // 当前房间信息
    let hasFloorSequence = false // 是否检测到楼层序列（1楼、2楼等）

    // 预扫描：检测是否存在真正的楼层序列（如"二楼"、"三楼"，不包括房间号）
    for (const line of lines) {
      // 只检测真正的楼层标题，不包括房间号
      const isRealFloor = /^(一|二|三|四|五|六|七|八|九|十)[楼层]$/i.test(line.trim()) ||
                         /^(\d+)[楼层]$/i.test(line.trim()) ||
                         /^(\d+)F$/i.test(line.trim()) ||
                         /^第(\d+)层$/i.test(line.trim())

      if (isRealFloor) {
        hasFloorSequence = true
        console.log(`🔍 预扫描发现真正的楼层序列: ${line}`)
        break
      }
    }
    console.log(`🔍 预扫描结果: ${hasFloorSequence ? '检测到楼层序列' : '未检测到楼层序列'}`)

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      console.log(`📝 处理行 ${i + 1}: "${line}"`)

      // 1. 识别项目信息 (通常在开头，包含公司名、项目名等)
      if (line.includes('（') || line.includes('(') || line.includes('项目') || line.includes('工程') ||
          line.includes('#') || line.includes('汽车') || line.includes('公司') || line.includes('店') ||
          line.includes('栋') || line.includes('苑') || line.includes('园') || line.includes('府') ||
          line.includes('城') || line.includes('庭') || line.includes('居') || line.includes('墅') ||
          line.includes('广场') || line.includes('中心') || line.includes('大厦') || line.includes('公馆') ||
          line.includes('花园') || line.includes('小区') || line.includes('社区') || line.includes('新村') ||
          line.includes('别墅') || line.includes('豪庭') || line.includes('雅苑') || line.includes('华庭') ||
          line.includes('名邸') || line.includes('御景') || line.includes('尊邸') || line.includes('悦府')) {
        currentProject = line
        console.log(`🏠 识别项目信息: ${currentProject}`)
        continue
      }

      // 2. 智能识别楼层信息（考虑是否有楼层序列）
      const floorInfo = identifyFloorInfo(line, hasFloorSequence)
      if (floorInfo) {
        // ⚠️ 重要：保存当前楼层（如果有风口数据）
        if (currentFloor && currentFloor.vents.length > 0) {
          floors.push(currentFloor)
          console.log(`💾 保存楼层: ${currentFloor.floorName} (${currentFloor.vents.length}个风口)`)
        }
        // 创建新楼层
        currentFloor = {
          id: generateUniqueId('floor'),
          floorName: normalizeFloorName(floorInfo), // 🏢 标准化楼层名称
          vents: [],
          totalPrice: 0
        }
        currentRoom = '' // 重置房间信息
        console.log(`🏢 创建新楼层: ${floorInfo}`)
        continue
      }

      // 3. 简化逻辑：所有房间号都作为备注，不再创建楼层
      const roomNumber = identifyRoomNumber(line)
      if (roomNumber) {
        currentRoom = roomNumber
        console.log(`🚪 识别房间号 (作为备注): ${roomNumber}`)
        continue
      }

      // 4. 识别其他房间信息（也作为备注）
      const roomInfo = identifyRoomInfo(line)
      if (roomInfo) {
        currentRoom = roomInfo
        console.log(`🚪 识别房间信息 (作为备注): ${roomInfo}`)
        continue
      }

      // 3. 识别风口信息（支持连续多个风口）
      const ventDataList = parseContinuousVents(line, '')
      if (ventDataList.length > 0) {
        console.log(`💨 识别到 ${ventDataList.length} 个风口`)

        // 逐个处理每个风口
        for (const ventData of ventDataList) {
        // ⚠️ 重要：如果没有当前楼层，提醒用户先添加楼层
        if (!currentFloor) {
          console.warn(`⚠️ 警告: 发现风口数据但没有楼层信息，创建默认楼层`)
          currentFloor = {
            id: generateUniqueId('floor'),
            floorName: '1楼',
            vents: [],
            totalPrice: 0
          }
        }

        // 合并房间信息和原有备注
        let combinedNotes = ''
        if (currentRoom) {
          combinedNotes = currentRoom
        }
        if (ventData.notes) {
          combinedNotes = combinedNotes ? `${combinedNotes} ${ventData.notes}` : ventData.notes
        }
        combinedNotes = cleanNotes(combinedNotes) // 清理合并后的备注

        const vent: VentItem = {
          id: generateUniqueId('vent'),
          type: ventData.type,
          length: ventData.length,
          width: ventData.width,
          quantity: ventData.quantity,
          unitPrice: getDefaultPriceByType(ventData.type),
          totalPrice: 0,
          notes: combinedNotes
        }

          vent.totalPrice = calculateVentPriceLocal(vent)
          currentFloor.vents.push(vent)
          console.log(`✅ 添加风口到 ${currentFloor.floorName}: ${ventData.type} ${ventData.length}x${ventData.width} x${ventData.quantity}个 备注:"${combinedNotes}"`)
        }
      } else {
        console.log(`❓ 无法识别行内容: "${line}"`)
      }
    }

    // 添加最后一个楼层
    if (currentFloor && currentFloor.vents.length > 0) {
      floors.push(currentFloor)
      console.log(`💾 保存最后楼层: ${currentFloor.floorName} (${currentFloor.vents.length}个风口)`)
    }

    // 更新项目地址
    if (currentProject && floors.length > 0) {
      setProjectAddress(currentProject)
    }

    console.log(`📋 粘贴解析完成，共识别 ${floors.length} 个楼层`)
    return floors
  }

  // 智能清理和预处理文本行
  const preprocessLine = (line: string): string => {
    // 1. 替换制表符为空格
    let cleaned = line.replace(/\t/g, ' ')

    // 2. 移除多余空格，但保持单个空格分隔
    cleaned = cleaned.replace(/\s+/g, ' ').trim()

    // 3. 处理特殊情况：如果包含价格信息，尝试提取核心数据
    // 格式: 1710*300	12	1710	300	象牙白	82.8 	994.0 	回风
    const tableMatch = cleaned.match(/^(\d+\*\d+)\s+(\d+)\s+\d+\s+\d+\s+([^\s]+)(?:\s+[\d.]+\s+[\d.]+)?\s*(.*)$/)
    if (tableMatch) {
      const [, dimensions, quantity, color, rest] = tableMatch
      // 重新组织为标准格式
      cleaned = `${dimensions} ${quantity} ${color} ${rest}`.trim()
      console.log(`    🔧 表格格式重组: "${cleaned}"`)
    }

    return cleaned
  }

  // 中文数字转换函数
  const chineseToNumber = (chineseNum: string): number => {
    console.log(`      🔤 中文数字转换输入: "${chineseNum}"`)

    const chineseMap: { [key: string]: number } = {
      // 简体中文数字
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
      // 繁体/大写中文数字
      '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5, '陆': 6, '柒': 7, '捌': 8, '玖': 9, '拾': 10,
      // 其他
      '零': 0, '〇': 0
    }

    // 如果是纯数字，直接返回
    if (/^\d+$/.test(chineseNum)) {
      const result = parseInt(chineseNum)
      console.log(`      🔤 阿拉伯数字: "${chineseNum}" → ${result}`)
      return result
    }

    // 处理简单的中文数字
    if (chineseMap[chineseNum]) {
      const result = chineseMap[chineseNum]
      console.log(`      🔤 简单中文数字: "${chineseNum}" → ${result}`)
      return result
    }

    // 处理十几的情况
    if (chineseNum.startsWith('十') || chineseNum.startsWith('拾')) {
      const rest = chineseNum.substring(1)
      const result = 10 + (chineseMap[rest] || 0)
      console.log(`      🔤 十几数字: "${chineseNum}" → ${result}`)
      return result
    }

    // 处理二十、三十等
    if (chineseNum.length === 2 && (chineseNum.endsWith('十') || chineseNum.endsWith('拾'))) {
      const first = chineseNum.substring(0, 1)
      const result = (chineseMap[first] || 1) * 10
      console.log(`      🔤 整十数字: "${chineseNum}" → ${result}`)
      return result
    }

    // 处理二十一、三十二等
    if (chineseNum.length === 3 && (chineseNum.includes('十') || chineseNum.includes('拾'))) {
      const parts = chineseNum.split(/[十拾]/)
      const tens = chineseMap[parts[0]] || 1
      const ones = chineseMap[parts[1]] || 0
      const result = tens * 10 + ones
      console.log(`      🔤 复合数字: "${chineseNum}" → ${result}`)
      return result
    }

    // 默认返回1
    console.log(`      🔤 无法识别，返回默认值: "${chineseNum}" → 1`)
    return 1
  }

  // 🎯 统一核心解析引擎 - 结合所有识别方式的优势
  const unifiedVentParser = {
    // 统一的备注清理函数（智能提取有意义的备注）
    cleanNotes: (rawNotes: string): string => {
      if (!rawNotes) return ''

      let cleaned = rawNotes
        // 首先移除尺寸信息（包括各种分隔符）
        .replace(/\d+(?:\.\d+)?\s*[×✖️✖✘✕xX*+\-]\s*\d+(?:\.\d+)?/g, '')
        // 移除风口类型关键词
        .replace(/(出风口?|回风口?|进风口?|进气口?|检修口?|维修口?|新风口?|线型风口?|条型风口?|格栅风口?|散流器?)/g, '')
        // 移除单位
        .replace(/mm|㎜|毫米/g, '')
        .replace(/cm|厘米/g, '')
        // 移除数量词
        .replace(/\s*个\s*$/g, '') // 移除结尾的"个"
        .replace(/\s*只\s*$/g, '') // 移除结尾的"只"
        .replace(/\s*套\s*$/g, '') // 移除结尾的"套"
        .replace(/\s*件\s*$/g, '') // 移除结尾的"件"
        // 移除序号信息
        .replace(/序号.*次/g, '') // 移除"序号二次"等
        // 移除冒号和无意义符号
        .replace(/[：:]/g, '')
        .replace(/[×✖️✖✘✕x*+\-]+/g, ' ') // 移除无意义符号
        .replace(/\s+/g, ' ') // 合并多个空格
        .trim()

      // 移除开头的纯数字（但保留房间号格式如"2103房号"）
      cleaned = cleaned.replace(/^(\d+)\s+(?!房|号|室|厅|层|楼)/, '')

      // 🔧 移除所有独立的数字（但保留有意义的文字）
      cleaned = cleaned.replace(/\b\d+\b/g, '').replace(/\s+/g, ' ').trim()

      // 如果清理后为空、只剩下无意义的内容或纯数字，返回空字符串
      if (!cleaned ||
          /^[×✖️✖✘✕x*+\-\s]*$/.test(cleaned) ||
          /^\d+$/.test(cleaned) || // 🔧 纯数字
          /^\d+\s*[×xX*+\-]\s*\d+$/.test(cleaned) || // 🔧 纯尺寸格式
          cleaned.length <= 1) { // 🔧 太短的内容
        return ''
      }

      return cleaned
    },

    // 🔧 智能提取房间/地点信息（增强版 - 支持字母数字房间编号）
    extractLocationInfo: (text: string): string => {
      console.log(`      🏠 房间信息提取: "${text}"`)

      const locationPatterns = [
        // 🔧 字母+数字房间编号（如A101, B102, C201）
        /([A-Z]\d{2,4})\b/g,
        // 🔧 房间号格式（101-199, 201-299, 1001-1999等，扩大识别范围）
        /\b(\d{1,2}0[1-9])\b(?!\s*[×✖️xX*+\-]\s*\d)/g,
        // 🔧 更宽泛的房间号格式（101, 102, 103, 104等）
        /\b(10[1-9]|20[1-9]|30[1-9]|[1-9]0[1-9])\b(?!\s*[×✖️xX*+\-]\s*\d)/g,
        // 房间号格式
        /(\d+房号?|房号?\d+)/g,
        // 房间类型
        /(餐厅|歺厅|客厅|大厅|主厅|卧室|主卧|次卧|书房|厨房|卫生间|洗手间|阳台|走廊|过道|茶室|棋牌室|KTV|活动室)/g,
        // 房间编号（包含数字后缀）
        /(小房间\d*|小房\d*|房间\d*|卧室\d*|办公室\d*)/g,
        // 楼层信息
        /(\d+楼|\d+层|[一二三四五六七八九十]+楼|[一二三四五六七八九十]+层)/g,
        // 区域信息
        /(南区|北区|东区|西区|A区|B区|C区|D区)/g
      ]

      const locations: string[] = []

      for (const pattern of locationPatterns) {
        const matches = text.match(pattern)
        if (matches) {
          console.log(`      ✅ 匹配到位置信息: ${matches.join(', ')}`)
          locations.push(...matches)
        }
      }

      const result = locations.join(' ').trim()
      console.log(`      🎯 最终房间信息: "${result}"`)
      return result
    },

    // 统一的风口类型判断函数（使用最新修正的逻辑）
    determineVentType: (text: string, length: number, width: number): string => {
      const lowerText = text.toLowerCase()
      console.log(`🔍 风口类型判断开始: 文本="${text}", 长度=${length}, 宽度=${width}`)

      // 1. 优先基于文本内容判断
      if (lowerText.includes('检修') || lowerText.includes('维修')) {
        console.log(`✅ 关键词识别: 检修口`)
        return '检修口'
      }
      if (lowerText.includes('回风') || lowerText.includes('进气') ||
          lowerText.includes('进风') || lowerText.includes('吸风') ||
          lowerText.includes('排风') || lowerText.includes('抽风')) {
        console.log(`✅ 关键词识别: 回风口`)
        return '回风口'
      }
      if (lowerText.includes('出风') || lowerText.includes('送风')) {
        console.log(`✅ 关键词识别: 出风口`)
        return '出风口'
      }
      if (lowerText.includes('线型') || lowerText.includes('线形') || lowerText.includes('线性') || lowerText.includes('条形') || lowerText.includes('条型')) {
        console.log(`✅ 关键词识别: 线型风口`)
        return '线型风口'
      }
      if (lowerText.includes('圆口') || lowerText.includes('直径')) {
        console.log(`✅ 关键词识别: 圆形风口`)
        return '出风口'  // 圆形风口通常是出风口
      }

      // 2. 基于修正后的宽度判断（确保大的是长度，小的是宽度）
      const finalLength = Math.max(length, width)
      const finalWidth = Math.min(length, width)
      console.log(`🔍 基于尺寸判断: 最终长度=${finalLength}, 最终宽度=${finalWidth}`)

      if (finalWidth >= 255) {
        console.log(`✅ 宽度判断: 回风口 (宽度${finalWidth}mm ≥ 255mm)`)
        return '回风口'
      }
      console.log(`✅ 宽度判断: 出风口 (宽度${finalWidth}mm < 255mm)`)
      return '出风口'
    },

    // 统一的尺寸解析函数 - 使用修复后的单位推断逻辑
    parseDimensions: (text: string): { length: number; width: number } | null => {
      // 使用修复后的 parseDimensionString 函数，它包含正确的单位推断逻辑
      const result = parseDimensionString(text)
      if (!result) return null

      return {
        length: result.length,
        width: result.width
      }
    },

    // 🔧 智能数量解析函数（避免误识别尺寸）
    parseQuantity: (text: string): number => {
      console.log(`      🔢 智能数量解析: "${text}"`)

      // 🔧 优先查找明确的数量表达式（避免误识别工艺要求）
      const explicitQuantityPatterns = [
        // 🔧 修复：支持✖️分隔的数量格式，如"1300✖️220✖️14个"
        { pattern: /✖️(\d+)\s*个/, name: '✖️个数格式', priority: 1 },
        { pattern: /×(\d+)\s*个/, name: '×个数格式', priority: 1 },
        { pattern: /[xX*](\d+)\s*个/, name: 'x个数格式', priority: 1 },
        { pattern: /(\d+)\s*个/, name: '个数格式', priority: 1 },
        { pattern: /(\d+)\s*只/, name: '只数格式', priority: 1 },
        { pattern: /(\d+)\s*套/, name: '套数格式', priority: 1 },
        { pattern: /(\d+)\s*件/, name: '件数格式', priority: 1 },
        // 注意：移除了"段"的数量识别，因为"两段"是工艺要求而不是数量
        { pattern: /数量\s*[:：]\s*(\d+)/, name: '数量标记', priority: 2 },
        { pattern: /共\s*(\d+)/, name: '共计格式', priority: 2 }
      ]

      // 查找明确的数量表达式
      for (const {pattern, name} of explicitQuantityPatterns) {
        const match = text.match(pattern)
        if (match) {
          const quantity = parseInt(match[1])
          console.log(`      ✅ 找到${name}: ${quantity}`)
          return quantity
        }
      }

      // 🔧 查找中文数字
      const chineseNumbers = {
        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
        '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5, '陆': 6, '柒': 7, '捌': 8, '玖': 9, '拾': 10,
        '两': 2, '俩': 2
      }

      for (const [chinese, number] of Object.entries(chineseNumbers)) {
        if (text.includes(chinese + '个') || text.includes(chinese + '只') || text.includes(chinese + '套')) {
          console.log(`      ✅ 找到中文数字: ${chinese} = ${number}`)
          return number
        }
      }

      console.log(`      ⚠️ 未找到明确数量信息，使用默认值1`)
      return 1 // 默认数量
    }
  }

  // 🔄 智能预处理：将多尺寸文本拆分为一行一个尺寸
  const smartPreprocessText = (text: string): string => {
    console.log(`🔄 智能预处理多尺寸文本: "${text}"`)

    // 1. 检测是否为多尺寸连续文本
    const dimensionCount = (text.match(/\d+(?:\.\d+)?[*×xX✘✖✕⨯·+\-—–/@&:：乘by]\d+(?:\.\d+)?/g) || []).length
    if (dimensionCount <= 1) {
      console.log(`📝 单尺寸文本，无需预处理`)
      return text
    }

    console.log(`📝 检测到 ${dimensionCount} 个尺寸，开始智能拆分`)

    // 2. 智能拆分规则
    let processedText = text

    // 规则1: 在风口关键词前插入换行符
    const ventKeywords = [
      '客厅回风', '客厅出风', '客出风', '客进风',
      '房间回风', '房间出风', '房间进风',
      '餐厅回风', '餐厅出风', '餐厅进风', '歺厅回风', '歺厅出风',
      '卧室回风', '卧室出风', '主卧回风', '主卧出风', '次卧回风', '次卧出风',
      '厨房回风', '厨房出风', '书房回风', '书房出风',
      '大厅回风', '大厅出风', '客厅风口', '房间风口',
      '回风口', '出风口', '进风口', '新风口'
    ]

    // 按长度排序，优先匹配长关键词
    const sortedKeywords = ventKeywords.sort((a, b) => b.length - a.length)

    for (const keyword of sortedKeywords) {
      // 在关键词前插入换行符（但不在文本开头）
      const regex = new RegExp(`(?<!^)(?=\\s*${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'g')
      processedText = processedText.replace(regex, '\n')
    }

    // 规则2: 处理独立的"出风"、"回风"关键词
    processedText = processedText.replace(/(?<!^)(?=\s*(出风|回风)(?!\w))/g, '\n')

    // 规则3: 🔧 新增：处理特殊分隔符（如"1400/295—1600/300"）
    processedText = processedText.replace(
      /(\d+(?:\.\d+)?[*×xX✘✖✕⨯·+\-—–/@&:：乘by]\d+(?:\.\d+)?)(\s*[（(][^）)]*[）)])?([—–]+)/g,
      '$1$2\n'
    )

    // 规则4: 在完整尺寸后插入换行符（如果后面还有内容且不是括号）
    processedText = processedText.replace(
      /(\d+(?:\.\d+)?[*×xX✘✖✕⨯·+\-—–/@&:：乘by]\d+(?:\.\d+)?)(\s*[（(][^）)]*[）)])?(\s+(?![（(])(?=\S))/g,
      '$1$2\n'
    )

    // 规则4: 清理多余的空行和空格
    processedText = processedText
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')

    console.log(`✅ 智能拆分完成:`)
    processedText.split('\n').forEach((line, index) => {
      console.log(`  ${index + 1}. "${line}"`)
    })

    return processedText
  }

  // 🔄 解析连续风口信息（支持一行多个风口）
  const parseContinuousVents = (line: string, currentRoom: string = ''): {
    type: VentItem['type']
    length: number
    width: number
    quantity: number
    notes: string
  }[] => {
    console.log(`🔄 解析连续风口信息: "${line}"`)

    // 🔧 关键改进：先进行智能预处理
    const preprocessedText = smartPreprocessText(line)

    // 如果预处理后有多行，递归处理每一行
    const lines = preprocessedText.split('\n').filter(l => l.trim().length > 0)
    if (lines.length > 1) {
      console.log(`📝 预处理后分为 ${lines.length} 行，逐行解析`)
      const allResults: any[] = []
      lines.forEach((singleLine, index) => {
        console.log(`  处理第 ${index + 1} 行: "${singleLine}"`)
        // 🔧 智能粘贴不需要OCR修正
        const correctedLine = singleLine // 直接使用原始输入
        const lineResults = parseContinuousVents(correctedLine, '')
        allResults.push(...lineResults)
      })
      return allResults
    }

    // 🔄 分割连续风口信息（现在应该是单行了）
    const segments = splitContinuousVentInfo(preprocessedText)
    const results: {
      type: VentItem['type']
      length: number
      width: number
      quantity: number
      notes: string
    }[] = []

    // 🔧 检查是否为连续风口信息（多个风口且无明确备注）
    const isContinuousVents = segments.length > 1
    console.log(`    🔍 连续风口检测: ${isContinuousVents ? '是' : '否'} (${segments.length}个段落)`)

    // 🔧 智能提取项目房间号（避免误识别风口尺寸中的数字）
    let defaultNotes = ''
    if (!isContinuousVents) {
      const projectRoomMatch = line.match(/^([^.。，,]+?)(?:[.。，,]|$)/)
      if (projectRoomMatch) {
        const projectInfo = projectRoomMatch[1].trim()

        // 🔍 检查是否包含风口信息，如果包含则不提取房间号
        // 🔧 增强OCR错误识别：支持"出风兴"、"回风艮"等OCR常见错误
        const hasVentKeywords = /(出风[口兴艮良]?|回风[口兴艮良]?|进气[口兴艮良]?|检修[口兴艮良]?|维修[口兴艮良]?|风[口长兴艮良]|[长兴艮良].*宽|宽.*[长兴艮良])/.test(projectInfo)
        const hasDimensions = new RegExp(`\\d+\\s*[xX×✖✘✕⨯·*+\\-—–/@&:：乘by]\\s*\\d+`).test(projectInfo) || /\d+\s*✖️\s*\d+/.test(projectInfo)
        const hasVentInfo = hasVentKeywords || hasDimensions

        console.log(`    🔍 风口信息检测: 关键词=${hasVentKeywords}, 尺寸=${hasDimensions}, 总体=${hasVentInfo}, 文本="${projectInfo}"`)

        if (!hasVentInfo) {
          // 只有在不包含风口信息的情况下才提取房间号
          const roomMatch = projectInfo.match(/(\d{3,4})$/)
          if (roomMatch) {
            defaultNotes = roomMatch[1]
            console.log(`    🏠 提取默认房间号: "${defaultNotes}"`)
          }
        } else {
          console.log(`    🚫 包含风口信息，跳过房间号提取: "${projectInfo}"`)
        }
      }
    } else {
      console.log(`    🚫 连续风口信息，不使用默认房间号备注`)
    }

    // 逐个解析每个风口段
    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i]
      console.log(`    🔍 解析第${i+1}个风口段: "${segment}"`)

      const ventResults = parseVentFromText(segment, currentRoom)
      if (ventResults && ventResults.length > 0) {
        // 处理每个解析出的风口
        ventResults.forEach((ventResult, ventIndex) => {
          // 🔧 智能备注处理：连续风口只有明确特殊说明才添加备注
          let finalNotes = ''

          if (isContinuousVents) {
            // 连续风口：只保留明确的特殊说明（如"普通下出"），不使用房间号
            if (ventResult.notes && ventResult.notes.trim()) {
              // 检查是否为有意义的特殊说明（不是纯数字房间号）
              const isSpecialNote = !/^\d{3,4}$/.test(ventResult.notes.trim()) &&
                                   ventResult.notes.length > 2 &&
                                   /(普通|下出|上出|侧出|特殊|定制|加强|静音|高效)/.test(ventResult.notes)
              if (isSpecialNote) {
                finalNotes = ventResult.notes
                console.log(`    📝 连续风口保留特殊说明: "${finalNotes}"`)
              } else {
                console.log(`    🚫 连续风口跳过房间号备注: "${ventResult.notes}"`)
              }
            }
          } else {
            // 单个风口：使用原有逻辑
            finalNotes = ventResult.notes || defaultNotes
          }

          results.push({
            ...ventResult,
            notes: finalNotes
          })
          console.log(`    ✅ 第${i+1}个风口段第${ventIndex+1}个风口解析成功: ${ventResult.type} ${ventResult.length}×${ventResult.width} 备注:"${finalNotes}"`)
        })
      } else {
        console.log(`    ❌ 第${i+1}个风口段解析失败`)
      }
    }

    console.log(`    🎯 总共解析出 ${results.length} 个风口`)
    return results
  }

  // 从文本行中解析风口信息（使用统一核心解析引擎）
  const parseVentFromText = (line: string, currentRoom: string = ''): {
    type: VentItem['type']
    length: number
    width: number
    quantity: number
    notes: string
  }[] => {
    console.log(`    🔍 解析风口文本: "${line}"`)

    // 智能预处理
    const cleanLine = preprocessLine(line)
    console.log(`    🧹 预处理后: "${cleanLine}"`)

    // 🎯 使用多尺寸解析引擎
    // 1. 解析多个尺寸
    const multipleDimensions = parseMultipleDimensions(cleanLine)
    if (!multipleDimensions || multipleDimensions.length === 0) {
      console.log(`    ❌ 无法解析尺寸`)
      return []
    }

    console.log(`    🔍 解析到 ${multipleDimensions.length} 个尺寸`)

    const results: {
      type: VentType
      length: number
      width: number
      quantity: number
      notes: string
    }[] = []

    // 为每个尺寸创建一个风口
    multipleDimensions.forEach((dimensions, index) => {
      console.log(`      🔍 处理第${index + 1}个尺寸: ${dimensions.length}×${dimensions.width}`)

      // 2. 解析数量（所有尺寸共享同一个数量）
      const quantity = unifiedVentParser.parseQuantity(cleanLine)

      // 3. 🔧 智能备注提取策略：为每个风口单独提取相关备注
      let notes = ''

      // 🔧 简化备注提取：由于智能预处理，每行现在只有一个风口
      const originalLine = line // 保存原始完整文本

      // 🎯 关键改进：直接使用整行文本作为相关文本（因为现在一行一个风口）
      const relevantText = originalLine.trim()
      console.log(`    🎯 当前风口完整文本: "${relevantText}"`)

      const bracketMatch = relevantText.match(/[（(]([^）)]+)[）)]/g)
      if (bracketMatch) {
        notes = bracketMatch.map(match => match.replace(/[（()）]/g, '')).join(' ')
        console.log(`    📝 提取括号备注: "${notes}"`)
      } else {
        // 3.2 🔧 新策略：从相关文本组合提取所有有意义的备注信息
        console.log(`    🔍 分析相关文本提取备注: "${relevantText}"`)

        // 🔧 关键检查：如果文本只是纯尺寸格式，直接跳过备注提取
        // 🔧 修复：更严格的纯尺寸检查，确保不会误判包含其他信息的文本
        const isPureDimension = /^\s*\d+\s*[×xX✖️*+\-]\s*\d+\s*$/.test(relevantText.trim())
        if (isPureDimension) {
          console.log(`    🚫 检测到纯尺寸格式，跳过备注提取: "${relevantText}"`)
          // 直接跳到风口类型判断
        } else {
          const notesParts: string[] = []

        // 提取房间信息（字母+数字格式，如A101、B102）
        // 🔧 修复：排除尺寸信息中的X300、X150等误识别
        const roomMatch = relevantText.match(/\b([A-Z]\d{2,4})\b/g)
        const filteredRoomMatch = roomMatch?.filter(match => {
          // 排除尺寸信息中的X+数字格式
          return !relevantText.includes(`${match.charAt(0).toLowerCase()}${match.slice(1)}`) &&
                 !relevantText.includes(`${match}㎜`) &&
                 !relevantText.includes(`${match}mm`)
        })

      if (filteredRoomMatch && filteredRoomMatch.length > 0) {
        notesParts.push(filteredRoomMatch[0])
        console.log(`    🏠 提取房间编号: "${filteredRoomMatch[0]}"`)
      }

        // 🔧 改进：提取房间描述（包含楼层信息）
        const roomDescWithFloorMatch = relevantText.match(/(一楼|二楼|三楼|四楼|五楼|六楼|七楼|八楼|九楼|十楼|楼上|楼下|地下|顶楼|底楼|[1-9]楼|[1-9]F|B[1-9])(餐厅|歺厅|客厅|大厅|主厅|卧室|主卧|次卧|书房|厨房|卫生间|洗手间|阳台|走廊|过道|茶室|棋牌室|KTV|活动室|办公室|会议室|储物间|衣帽间|楼梯间|地下室|车库|花园|露台|天台|门厅|起居室|会客厅|影音室|健身房|工作室|接待室|休息室|洗衣房|杂物间|设备间)/)
        if (roomDescWithFloorMatch) {
          const fullMatch = roomDescWithFloorMatch[0]
          const floorPart = roomDescWithFloorMatch[1]
          const roomPart = roomDescWithFloorMatch[2]

          // 只添加房间部分到备注，避免重复
          notesParts.push(roomPart)
          console.log(`提取带楼层房间描述: "${fullMatch}" → 楼层:"${floorPart}" 房间:"${roomPart}"`)
        }

        // 尝试匹配简单房间类型（包含数字后缀的房间）
        const simpleRoomMatch = relevantText.match(/(小房间\d*|小房\d*|房间\d*|餐厅|歺厅|客厅|大厅|主厅|卧室|主卧|次卧|书房|厨房|卫生间|洗手间|阳台|走廊|过道|茶室|棋牌室|KTV|活动室|办公室|会议室|储物间|衣帽间|楼梯间|地下室|车库|花园|露台|天台|门厅|起居室|会客厅|影音室|健身房|工作室|接待室|休息室|洗衣房|杂物间|设备间)/)
        if (simpleRoomMatch && !notesParts.includes(simpleRoomMatch[0])) {
          notesParts.push(simpleRoomMatch[0])
          console.log(`    🏠 提取简单房间类型: "${simpleRoomMatch[0]}"`)
        }

        // 🔧 新增：提取单字符房间信息（如"歺"、"柜"等）
        // 但要避免重复提取已经识别的完整房间名称中的字符
        const singleCharMatch = relevantText.match(/[歺柜厅卧厨卫阳台书房客餐]/g)
      if (singleCharMatch) {
        // 转换单字符为完整房间名称
        const charMap: { [key: string]: string } = {
          '歺': '餐厅',
          '柜': '柜子',
          '厅': '客厅',
          '卧': '卧室',
          '厨': '厨房',
          '卫': '卫生间',
          '阳': '阳台',
          '台': '阳台',
          '书': '书房',
          '房': '房间',
          '客': '客厅',
          '餐': '餐厅'
        }

        singleCharMatch.forEach(char => {
          const fullName = charMap[char] || char

          // 🔧 避免重复：如果已经有完整的房间名称包含此字符，则跳过
          const hasCompleteRoomName = notesParts.some(part =>
            part.includes(char) && part.length > 1 && part !== fullName
          )

          if (!hasCompleteRoomName && !notesParts.includes(fullName)) {
            notesParts.push(fullName)
            console.log(`    🏠 提取单字符房间: "${char}" → "${fullName}"`)
          } else if (hasCompleteRoomName) {
            console.log(`    🚫 跳过单字符房间: "${char}" (已有完整房间名称)`)
          }
        })
      }

        // 提取技术规格信息（移除包边相关，避免与后面的详细匹配重复）
        const techMatch = relevantText.match(/(分段|普通下出|侧出|上出|下出|四面出风|双向出风|留圆接\d*软管|留圆接\d*|软管连接|硬管连接|法兰连接|铝合金|不锈钢|塑料|ABS|防火|防水|静音|可调|固定|线形|线型|条形|条型|格栅|百叶|散流器|旋流|方形|圆形|倒角|圆角|直角|斜角|加厚|薄型|标准|定制|特殊|加工|焊接|冲压|拉伸)/g)
        if (techMatch) {
          notesParts.push(...techMatch)
          console.log(`    🔧 提取技术规格: "${techMatch.join(' ')}"`)
        }

        // 提取位置信息
        const locationMatch = relevantText.match(/(东侧|西侧|南侧|北侧|左侧|右侧|中间|角落|吊顶|墙面|地面|顶部|底部|靠窗|靠门|靠墙|中央)/g)
        if (locationMatch) {
          notesParts.push(...locationMatch)
          console.log(`    📍 提取位置信息: "${locationMatch.join(' ')}"`)
        }

        // 提取颜色信息
        const colorMatch = relevantText.match(/(白色|黑色|红色|蓝色|绿色|黄色|灰色|银色|金色)/g)
        if (colorMatch) {
          // 🔧 确保颜色信息不是从尺寸中误提取的
          const validColors = colorMatch.filter(color => {
            // 检查颜色词前后是否有数字，如果有则可能是误提取
            const colorIndex = relevantText.indexOf(color)
            const beforeChar = colorIndex > 0 ? relevantText[colorIndex - 1] : ''
            const afterChar = colorIndex + color.length < relevantText.length ? relevantText[colorIndex + color.length] : ''
            return !/\d/.test(beforeChar) && !/\d/.test(afterChar)
          })

          if (validColors.length > 0) {
            notesParts.push(...validColors)
            console.log(`    🎨 提取颜色信息: "${validColors.join(' ')}"`)
          }
        }

        // 提取工艺要求和特殊描述信息
        const craftRequirements = relevantText.match(/(外白内黑|内黑|黑底|白底|全黑|全白|双色|共挤|水墨|两段|三段|四段|五段|分段|帮减|木纹|腻子粉|石膏板|预埋|箭型|爪型|T型|线型|线性|条形|条型|门铰型)/g)
        if (craftRequirements) {
          notesParts.push(...craftRequirements)
          console.log(`    🔧 提取工艺要求: "${craftRequirements.join(' ')}"`)
        }

        // 提取款式描述（颜色+类型组合）
        const styleDescMatch = relevantText.match(/(白色线型|黑色线型|白色条形|黑色条形|白色线性|黑色线性|白色箭型|黑色箭型|白色爪型|黑色爪型|条型风口)/g)
        if (styleDescMatch) {
          notesParts.push(...styleDescMatch)
          console.log(`    🎭 提取款式描述: "${styleDescMatch.join(' ')}"`)
        }

        // 提取技术规格和特殊要求
        const techSpecMatch = relevantText.match(/(小弯叶片|大弯叶片|直叶片|开口尺寸|实际尺寸|深度.*?公分|包边.*?公分|包边.*?厘米|包边.*?毫米|包边.*?cm|包边.*?mm|加急|做线型|做条形)/g)
        if (techSpecMatch) {
          notesParts.push(...techSpecMatch)
          console.log(`    ⚙️ 提取技术规格: "${techSpecMatch.join(' ')}"`)
        }

        // 🔧 提取"数量+个+描述"格式的备注（如"4个普通下出"）
        const quantityDescMatch = relevantText.match(/\d+\s*个\s*([^\d,，、]+)/g)
        if (quantityDescMatch) {
          // 提取描述部分，去掉数量和"个"
          const descriptions = quantityDescMatch.map(match => {
            const desc = match.replace(/\d+\s*个\s*/, '').trim()
            return desc
          }).filter(desc => desc.length > 0)

          if (descriptions.length > 0) {
            notesParts.push(...descriptions)
            console.log(`    🔢 提取数量描述: "${descriptions.join(' ')}"`)
          }
        }

      // 🔧 组合所有备注信息
      if (notesParts.length > 0) {
        // 去重并组合
        const uniqueNotes = [...new Set(notesParts)]
        // 🔧 过滤掉纯数字的备注部分
        const filteredNotes = uniqueNotes.filter(note => {
          const trimmedNote = note.trim()
          // 排除纯数字、纯尺寸信息
          return !/^\d+$/.test(trimmedNote) &&
                 !/^\d+\s*[×xX*+\-]\s*\d+$/.test(trimmedNote) &&
                 trimmedNote.length > 0
        })

        if (filteredNotes.length > 0) {
          notes = filteredNotes.join(' ')
          console.log(`    ✨ 组合备注信息: "${notes}"`)
        } else {
          console.log(`    ❌ 过滤后无有效备注信息`)
        }
      } else {
        console.log(`    ❌ 未找到有意义的备注信息`)
      }
        } // 结束 else 分支（非纯尺寸格式的备注提取）
    }

    // 3.3 添加房间信息到备注
    const roomNoteParts: string[] = []

    // 🏠 添加当前房间信息（如果存在且有意义）
    if (currentRoom && currentRoom.trim()) {
      const roomName = currentRoom.trim()
      // 🔧 确保房间名不是纯尺寸信息
      if (!/^\d+\s*[×xX✘✖*/]\s*\d+/.test(roomName)) {
        roomNoteParts.push(roomName)
        console.log(`添加房间信息到备注: "${roomName}"`)
      }
    }

    // 🔧 添加原有备注信息
    if (notes && notes.trim()) {
      roomNoteParts.push(notes.trim())
    }

    // 🔧 组合房间信息和备注
    if (roomNoteParts.length > 0) {
      notes = roomNoteParts.join(' ')
      console.log(`组合房间+备注信息: "${notes}"`)
    }

    // 3.4 最终清理（轻量级）
    if (notes) {
      notes = cleanNotes(notes)
      console.log(`    ✨ 最终备注: "${notes}"`)
    }

      // 4. 判断风口类型 - 使用相关文本片段而不是整个长文本
      const ventType = unifiedVentParser.determineVentType(relevantText, dimensions.length, dimensions.width)

      // 5. 映射到系统类型
      const systemType = mapToSystemVentType(ventType, dimensions.length, dimensions.width)

      console.log(`      ✅ 第${index + 1}个风口解析结果: ${systemType} ${dimensions.length}×${dimensions.width} ×${quantity} 备注:"${notes}"`)

      results.push({
        type: systemType as VentItem['type'],
        length: dimensions.length,
        width: dimensions.width,
        quantity,
        notes
      })
    })

    console.log(`    🎯 总共解析出 ${results.length} 个风口`)
    return results
  }

  // 🔧 改进的备注清理函数 - 更智能地保留原始文本信息
  const cleanNotes = (notes: string): string => {
    if (!notes || typeof notes !== 'string') return ''

    let cleaned = notes.trim()
    console.log(`🧹 开始清理备注: "${notes}"`)

    // 🚫 如果备注过短，但要保留有意义的房间信息
    if (cleaned.length <= 2) {
      // ✅ 保留有意义的房间类型（即使只有2个字符）
      const meaningfulRooms = ['主卧', '次卧', '客厅', '厨房', '餐厅', '歺厅', '大厅', '书房', '阳台', '卫生间', '洗手间', '走廊', '过道', '玄关', '门厅', '储物间', '衣帽间', '楼梯间', '地下室', '车库', '花园', '露台', '天台', '起居室', '会客厅', '影音室', '健身房', '茶室', '棋牌室', '工作室', '办公室', '会议室', '接待室', '休息室', '洗衣房', '杂物间', '设备间']
      // ✅ 保留有意义的技术规格（即使只有2个字符）
      const meaningfulTech = ['分段', '侧出', '上出', '下出', '静音', '可调', '固定', '防火', '防水', '白色', '黑色', '红色', '蓝色', '绿色', '黄色', '灰色', '银色', '金色', '单边', '双边', '左边', '右边', '线形', '线型', '条形', '条型', '格栅', '百叶']
      // 🔧 检查是否包含数字后缀的房间类型
      const roomWithNumberPattern = /^(小房间|小房|房间|卧室|办公室)\d*$/
      if (meaningfulRooms.includes(cleaned) || meaningfulTech.includes(cleaned) || roomWithNumberPattern.test(cleaned)) {
        console.log(`✅ 保留有意义的房间信息: "${cleaned}"`)
        return cleaned
      }
      console.log(`🚫 备注过短且无意义，跳过: "${cleaned}"`)
      return ''
    }

    // 🚫 过滤明显的无意义内容
    const meaninglessPatterns = [
      /^[×*+\-，,、。！!？?；;：:（）()【】\[\]{}「」『』〈〉《》""''‚„…·•‧∙▪▫■□▲△▼▽◆◇○●◎◉⊙⊚⊛⊜⊝⊞⊟⊠⊡⊢⊣⊤⊥⊦⊧⊨⊩⊪⊫⊬⊭⊮⊯⊰⊱⊲⊳⊴⊵⊶⊷⊸⊹⊺⊻⊼⊽⊾⊿⋀⋁⋂⋃⋄⋅⋆⋇⋈⋉⋊⋋⋌⋍⋎⋏⋐⋑⋒⋓⋔⋕⋖⋗⋘⋙⋚⋛⋜⋝⋞⋟⋠⋡⋢⋣⋤⋥⋦⋧⋨⋩⋪⋫⋬⋭⋮⋯⋰⋱⋲⋳⋴⋵⋶⋷⋸⋹⋺⋻⋼⋽⋾⋿\s]+$/, // 只有符号
      /^\d+$/, // 纯数字
      /^[长宽高深厚]\s*[.。]?\s*$/, // 只有"长."、"宽."等残余
      /^[.。]+$/, // 只有点号
      /^[×✖️x*+\-\s]+$/, // 只有运算符号
      /^序号\d*$/, // 序号
      /^[一二三四五六七八九十]\d*$/, // 中文数字序号
    ]

    for (const pattern of meaninglessPatterns) {
      if (pattern.test(cleaned)) {
        console.log(`🚫 匹配无意义模式，跳过: "${cleaned}"`)
        return ''
      }
    }

    // 🔧 轻量级清理：只移除明显的干扰内容，保留原始描述
    cleaned = cleaned
      // 移除说明性文字（如 "→ 识别为xxx"）
      .replace(/[→←↑↓]\s*识别为.*/gi, '')
      .replace(/识别为.*/gi, '')
      // 移除开头和结尾的特殊符号（但保留中间的）
      .replace(/^[×*+\-，,\s→←↑↓.。]+/, '')
      .replace(/[×*+\-，,\s→←↑↓.。]+$/, '')
      // 清理多余空格
      .replace(/\s+/g, ' ')
      .trim()

    // 🔧 新增：去除重复词汇（如"一楼厨房 厨房" → "一楼厨房"）
    const words = cleaned.split(/\s+/)
    const uniqueWords: string[] = []
    const seenWords = new Set<string>()

    for (const word of words) {
      if (!seenWords.has(word)) {
        uniqueWords.push(word)
        seenWords.add(word)
      }
    }

    cleaned = uniqueWords.join(' ')
    console.log(`🔧 去重后: "${cleaned}"`)

    // 🔧 最终检查：确保清理后还有有意义的内容
    if (cleaned.length === 0) {
      console.log(`🚫 清理后为空，跳过`)
      return ''
    }

    // ✅ 保留有意义的单字（颜色、材质等）
    if (cleaned.length === 1) {
      const meaningfulSingleChars = ['白', '黑', '红', '蓝', '绿', '黄', '灰', '银', '金', '铜', '铁', '钢', '木', '竹', '石', '玻', '塑', '铝']
      if (!meaningfulSingleChars.includes(cleaned)) {
        console.log(`🚫 单字符无意义，跳过: "${cleaned}"`)
        return ''
      }
    }

    console.log(`✅ 备注清理完成: "${notes}" → "${cleaned}"`)
    return cleaned
  }



  // 严密的项目地址检测逻辑
  const detectProjectAddress = (line: string): { isProject: boolean; address: string } => {
    const trimmedLine = line.trim()

    // 排除明显不是项目地址的内容
    if (trimmedLine.length < 3 ||
        trimmedLine.includes('出风') || trimmedLine.includes('回风') ||
        trimmedLine.includes('检修') || trimmedLine.includes('白色') ||
        trimmedLine.includes('黑色') || trimmedLine.includes('序号') ||
        trimmedLine.includes('数量') || trimmedLine.includes('长度') ||
        trimmedLine.includes('宽度') || trimmedLine.includes('单价') ||
        /^\d+$/.test(trimmedLine) || // 纯数字
        /^\d+\*\d+$/.test(trimmedLine) || // 尺寸格式
        /^\d+\.?\d*$/.test(trimmedLine)) { // 价格格式
      return { isProject: false, address: '' }
    }

    // 强项目地址标识（高置信度）
    const strongIndicators = [
      '栋', '单元', '公馆', '豪门', '小区', '广场', '大厦', '中心',
      '花园', '苑', '城', '府', '庭', '居', '村', '社区',
      '县', '市', '区', '镇', '乡', '路', '街', '巷'
    ]

    // 检查是否包含强标识符
    const hasStrongIndicator = strongIndicators.some(indicator => trimmedLine.includes(indicator))

    if (hasStrongIndicator) {
      return { isProject: true, address: trimmedLine }
    }

    // 项目名称+区域标识（如：丹桂苑A、丹桂苑B）
    const projectWithAreaPattern = /^.+[苑园庭府城][A-Z]$/
    if (projectWithAreaPattern.test(trimmedLine)) {
      return { isProject: true, address: trimmedLine }
    }

    // 弱项目地址标识（需要额外验证）
    const weakPatterns = [
      /^.+\d+-\d+$/, // 复合地址：高迪公馆2-1401
      /^\d+栋/, // 楼栋格式：33栋
    ]

    const hasWeakPattern = weakPatterns.some(pattern => pattern.test(trimmedLine))

    if (hasWeakPattern && trimmedLine.length >= 4) {
      return { isProject: true, address: trimmedLine }
    }

    // 排除房间号格式（避免误识别）
    const roomNumberPatterns = [
      /^[A-Z]\d{3}$/, // A101, B102 等房间号
      /^[A-Z]\d{2}$/, // A01, B02 等房间号
      /^\d{3,4}$/, // 101, 1001 等房间号
    ]

    const isRoomNumber = roomNumberPatterns.some(pattern => pattern.test(trimmedLine))
    if (isRoomNumber) {
      return { isProject: false, address: '' }
    }

    return { isProject: false, address: '' }
  }

  // 🔧 改进的楼层信息检测（避免误识别房间号）
  const detectFloorInfo = (line: string): { isFloor: boolean; floorName: string } => {
    const trimmedLine = line.trim()

    // 🚫 首先排除房间号格式（避免误识别）
    const roomNumberPatterns = [
      /^[A-Z]\d{3}$/i,  // A201, B202, C301 等房间号
      /^[A-Z]\d{2}$/i,  // A01, B02 等房间号
      /^[A-Z]\d{4}$/i,  // A1001 等房间号
    ]

    const isRoomNumber = roomNumberPatterns.some(pattern => pattern.test(trimmedLine))
    if (isRoomNumber) {
      console.log(`🚫 排除房间号格式: ${trimmedLine}`)
      return { isFloor: false, floorName: '' }
    }

    // ✅ 更宽松的楼层格式匹配（包含楼、层关键词即可）
    const floorPatterns = [
      /^([一二三四五六七八九十]+)楼$/,        // 一楼、二楼（中文数字）
      /^([一二三四五六七八九十]+)层$/,        // 一层、二层（中文数字）
      /^(\d+)楼$/,                           // 1楼、2楼、3楼（阿拉伯数字）
      /^(\d+)层$/,                           // 1层、2层、3层（阿拉伯数字）
      /^[FB](\d+)楼?$/,                      // F1楼、B1楼
      /^地下[一二三四五六七八九十\d]+[楼层]?$/, // 地下一楼、地下1层
      /^负[一二三四五六七八九十\d]+[楼层]?$/,   // 负一楼、负1层
      /^第(\d+)层$/,                         // 第1层、第2层
    ]

    // 先尝试精确匹配
    for (const pattern of floorPatterns) {
      if (pattern.test(trimmedLine)) {
        console.log(`✅ 识别为楼层: ${trimmedLine}`)
        return { isFloor: true, floorName: trimmedLine }
      }
    }

    // 🔧 新增：宽松匹配（包含楼、层关键词）
    if ((trimmedLine.includes('楼') || trimmedLine.includes('层')) &&
        trimmedLine.length <= 10 && // 长度限制，避免误识别
        !/\d+[x×*+\-]\d+/.test(trimmedLine) && // 不是尺寸格式
        !/￥/.test(trimmedLine)) { // 不包含价格

      // 提取楼层数字
      const floorMatch = trimmedLine.match(/(\d+)/)
      if (floorMatch) {
        const floorNumber = parseInt(floorMatch[1])
        if (floorNumber >= 1 && floorNumber <= 50) { // 合理的楼层范围
          console.log(`✅ 宽松匹配识别为楼层: ${trimmedLine}`)
          return { isFloor: true, floorName: trimmedLine }
        }
      }

      // 中文数字楼层
      if (/[一二三四五六七八九十]/.test(trimmedLine)) {
        console.log(`✅ 中文楼层识别: ${trimmedLine}`)
        return { isFloor: true, floorName: trimmedLine }
      }
    }

    console.log(`❌ 不是楼层格式: ${trimmedLine}`)
    return { isFloor: false, floorName: '' }
  }

  // 从行中提取日期
  const extractDateFromLine = (line: string): string | null => {
    const datePatterns = [
      /\d{4}\/\d{1,2}\/\d{1,2}/,
      /\d{4}-\d{1,2}-\d{1,2}/,
      /\d{1,2}\/\d{1,2}\/\d{4}/,
      /\d{1,2}-\d{1,2}-\d{4}/,
    ]

    for (const pattern of datePatterns) {
      const match = line.match(pattern)
      if (match) {
        return match[0]
      }
    }

    return null
  }

  // 从行中解析风口数据（用于多项目粘贴）- 增强版
  const parseVentDataFromLine = (line: string): { isValid: boolean; data: any[] } => {
    const trimmedLine = line.trim()

    // 首先尝试使用现有的复杂解析逻辑
    const ventDataArray = parseVentFromText(line, '')
    if (ventDataArray && ventDataArray.length > 0) {
      const dataArray = ventDataArray.map(ventData => ({
        originalInfo: line,
        length: ventData.length,
        width: ventData.width,
        quantity: ventData.quantity,
        color: '象牙白',
        type: ventData.type,
        unitPrice: getDefaultPriceByType(ventData.type),
        notes: ventData.notes
      }))

      return {
        isValid: true,
        data: dataArray
      }
    }

    // 如果现有逻辑无法解析，使用简化的多项目粘贴专用逻辑
    // 检查是否包含风口关键词
    const ventKeywords = ['出风', '回风', '新风', '检修', '维修']
    const hasVentKeyword = ventKeywords.some(keyword => trimmedLine.includes(keyword))

    // 检查是否包含尺寸信息（支持多种分隔符）
    const dimensionPatterns = [
      /(\d+)(?:×|✖️|\*|x|\+|\-)\s*(\d+)/, // 支持 ×、✖️、*、x、+、- 等分隔符（使用分组模式）
      /(\d+)\s*(?:×|✖️|\*|x|\+|\-)\s*(\d+)/,
      /(\d+)\s+(\d+)/ // 空格分隔的数字
    ]

    let hasDimension = false
    let length = 100, width = 100

    for (const pattern of dimensionPatterns) {
      const match = trimmedLine.match(pattern)
      if (match) {
        hasDimension = true
        length = parseInt(match[1]) || 100
        width = parseInt(match[2]) || 100
        break
      }
    }

    if (hasVentKeyword || hasDimension) {
      // 使用增强的类型判断逻辑，确保与单个粘贴保持一致
      let finalType: VentItem['type'] = 'double_white_outlet' // 默认类型

      // 使用与单个粘贴相同的类型判断逻辑
      finalType = determineVentType(trimmedLine, '', width, trimmedLine)

      console.log(`🔍 多项目粘贴风口类型判断: "${trimmedLine}" → ${finalType}`)

      // 提取数量（如果有）
      let quantity = 1
      const quantityMatch = trimmedLine.match(/(\d+)\s*个/)
      if (quantityMatch) {
        quantity = parseInt(quantityMatch[1]) || 1
      }

      return {
        isValid: true,
        data: {
          originalInfo: line,
          length: length,
          width: width,
          quantity: quantity,
          color: '象牙白',
          type: finalType,
          unitPrice: getDefaultPriceByType(finalType),
          notes: trimmedLine.replace(/\d+(?:×|✖️|\*|x|\+|\-)\s*\d+/, '').replace(/\d+\s*个/, '').trim()
        }
      }
    }

    return { isValid: false, data: null }
  }

  // 🔧 重新设计：智能解析多项目粘贴内容（使用统一解析引擎）
  const parseMultiProjectContent = (content: string): TempProjectData[] => {
    console.log('🏗️ 开始智能解析多项目粘贴内容（统一引擎）')
    console.log('📝 原始内容:', content)

    const lines = content.split('\n').map(line => line.trim()).filter(line => line)
    const projects: TempProjectData[] = []
    let currentProject: TempProjectData | null = null
    let currentFloor = ''
    let currentRoom = '' // 🏠 添加房间上下文管理

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i]
      console.log(`🔍 处理第${i + 1}行: "${line}"`)

      // 🔧 智能粘贴不需要OCR修正，因为输入内容本身就是正确的
      // line = autoCorrectOCRErrors(line) // 禁用OCR修正

      // 🔧 智能项目识别：区分纯项目信息和包含风口的复合信息
      // 增强风口信息检测，支持OCR识别的"长×宽"格式和OCR常见错误
      const hasVentKeywords = /(出风[口兴艮良]?|回风[口兴艮良]?|进气[口兴艮良]?|检修[口兴艮良]?|维修[口兴艮良]?|风[口长兴艮良]|风宽|[长兴艮良].*宽|宽.*[长兴艮良])/.test(line)

      // 🔧 增强尺寸检测：支持小数点开头的格式（如 250x.1020）
      const hasDimensions = /\d+(?:\.\d+)?\s*[xX×✘✖*+\-]\s*\.?\d+(?:\.\d+)?/.test(line) ||
                           /\d+(?:\.\d+)?\s*✖️\s*\.?\d+(?:\.\d+)?/.test(line) ||
                           /[长兴艮良]\s*\.?\d+(?:\.\d+)?.*宽\s*\.?\d+(?:\.\d+)?/.test(line) ||
                           /宽\s*\.?\d+(?:\.\d+)?.*[长兴艮良]\s*\.?\d+(?:\.\d+)?/.test(line)

      // 🔧 新规则：即使风口关键词识别错误，只要有尺寸就认为是风口信息
      // 这样可以确保所有包含尺寸的行都被处理，根据尺寸来确定风口类型
      const hasVentInfo = hasDimensions || hasVentKeywords

      console.log(`风口信息检测: 关键词=${hasVentKeywords}, 尺寸=${hasDimensions}, 总体=${hasVentInfo}, 文本="${line}"`)

      // 🔧 优先检查楼层信息（无论是否包含风口信息）
      const floorFromLine = extractFloorFromLine(line)
      if (floorFromLine && floorFromLine !== currentFloor) {
        currentFloor = floorFromLine
        currentRoom = '' // 🏠 重置房间上下文
        console.log(`从行中提取楼层信息: "${line}" → 楼层: "${currentFloor}"`)
      }

      // 🔧 智能识别逻辑：优先级 房间 > 项目名称 > 风口
      if (!hasVentInfo) {
        // 🏢 检查是否为纯楼层信息行
        if (isFloorLine(line)) {
          // 已经在上面处理过了，跳过
          continue
        }

        // 🏠 检查是否为房间信息
        if (isRoomLine(line)) {
          currentRoom = line
          console.log(`识别房间: ${line}`)
          continue
        }

        // 🎯 检查是否为明确的项目名称
        if (isProjectNameLine(line)) {
          if (currentProject) {
            projects.push(currentProject)
          }
          currentProject = {
            name: line,
            floors: []
          }
          console.log(`🏗️ 识别项目信息: ${line}`)
          continue
        }
        // 🔧 第一行特殊处理：只有在没有找到明确项目名称时才考虑第一行
        else if (i === 0 && !lines.some(l => isProjectNameLine(l))) {
          // 🔧 关键修复：即使是第一行，也要排除包含尺寸信息的行
          if (!/\d+(?:\.\d+)?[*×xX✘✖✕⨯·+\-—–/@&:：乘by]\d+(?:\.\d+)?/.test(line)) {
            // 只有当整个文本中都没有明确的项目名称时，且第一行不是尺寸信息，才将第一行作为项目名称
            if (currentProject) {
              projects.push(currentProject)
            }
            currentProject = {
              name: line,
              floors: []
            }
            console.log(`🏗️ 第一行作为备选项目信息: ${line}`)
            continue
          } else {
            console.log(`🚫 第一行包含尺寸信息，不作为项目名称: "${line}"`)
          }
        }
      }

      // 🚫 跳过非风口信息的行（如"白线型"、"√"等），但不跳过尺寸信息（包含斜杠格式）
      // 🔧 修复：正确处理emoji ✖️
      const hasDimensionPattern = /\d+\s*[×xX✘✖*/]\s*\d+/.test(line) || /\d+\s*✖️\s*\d+/.test(line)
      if (!hasVentInfo && !isProjectNameLine(line) && !isFloorLine(line) && !hasDimensionPattern && !/圆口|直径/.test(line) && !/\d+米?\s*[×xX*]\s*\d+\s*=\s*[\u4e00-\u9fa5]+/.test(line)) {
        console.log(`🚫 跳过非风口非项目信息: "${line}"`)
        continue
      }

      // 🏠 特殊处理：包含风口信息的复合行（如"书香华府1201.出风口1920X140..."）
      // 但要排除纯风口信息的行（如"大厅出风口:3090✘150分段"）
      if (hasVentInfo && i === 0) {
        // 检查是否是纯风口信息（房间+风口类型+尺寸）
        const isPureVentInfo = /^[\u4e00-\u9fa5]{1,4}(出风口|回风口|进气口|检修口)[:：]\d+/.test(line)

        if (!isPureVentInfo) {
          // 从复合信息中提取项目名称
          const projectMatch = line.match(/^([^.。，,]+?)(?:[.。，,]|$)/)
          if (projectMatch) {
            const projectInfo = projectMatch[1].trim()
            // 再次验证提取的信息不是纯风口信息
            if (!projectInfo.includes('风口') && !(/\d{3,4}[×xX*]\d+/.test(projectInfo))) {
              if (currentProject) {
                projects.push(currentProject)
              }
              currentProject = {
                name: projectInfo,
                floors: []
              }
              console.log(`🏗️ 从复合信息提取项目: "${projectInfo}"`)
            } else {
              console.log(`🚫 跳过纯风口信息，不作为项目名称: "${projectInfo}"`)
            }
          }
        } else {
          console.log(`🚫 识别为纯风口信息，不提取项目名称: "${line}"`)
        }
      }



      // 🎯 使用连续风口解析引擎解析风口信息
      const ventResults = parseContinuousVents(line, currentRoom)
      if (ventResults.length > 0) {
        console.log(`💨 识别到 ${ventResults.length} 个风口`)

        // 逐个处理每个风口
        for (const ventResult of ventResults) {
        if (!currentProject) {
          currentProject = {
            name: '未命名项目',
            floors: []
          }
        }

        // 🏠 智能楼层创建：从项目信息中提取楼层和房间信息
        let floorName = currentFloor
        let defaultRoomNumber = ''

        // 🔧 优先使用当前楼层上下文
        if (currentFloor) {
          floorName = currentFloor
          console.log(`🏢 使用当前楼层上下文: "${floorName}"`)
        } else if (!floorName && currentProject) {
          // 从项目名称中提取楼层信息（如"书香华府1201" → 楼层"12"）
          const extractedFloor = normalizeFloorName(currentProject.name)
          floorName = extractedFloor

          // 同时提取房间号作为默认备注
          defaultRoomNumber = extractRoomFromProject(currentProject.name)
          console.log(`从项目信息提取: 楼层="${floorName}" 房间="${defaultRoomNumber}"`)
        }

        // 查找或创建楼层
        let floor = currentProject.floors.find(f => f.name === floorName)
        if (!floor) {
          floor = {
            name: floorName || '1', // 使用提取的楼层名或默认值
            vents: []
          }
          currentProject.floors.push(floor)
          console.log(`🏢 创建楼层: "${floor.name}"`)
        }

        // 🔧 直接使用 parseContinuousVents 的结果，避免重复处理
        const combinedNotes = ventResult.notes || ''
        console.log(`    ✨ 使用解析结果备注: "${combinedNotes}"`)

        // 添加风口到楼层
        floor.vents.push({
          type: ventResult.type as VentItem['type'],
          length: ventResult.length,
          width: ventResult.width,
          quantity: ventResult.quantity,
          notes: combinedNotes, // 🏠 使用组合后的备注
          unitPrice: 0,
          totalPrice: 0
        })

          console.log(`✅ 添加风口到楼层 "${floor.name}": ${ventResult.type} ${ventResult.length}×${ventResult.width} 房间:"${combinedNotes}" 备注:"${combinedNotes}"`)
        }
      }
    }

    // 添加最后一个项目
    if (currentProject) {
      projects.push(currentProject)
    }

    // 🔧 后置项目名称逻辑：处理项目名称在最后的情况
    console.log(`🔍 开始后置项目名称检查，当前项目数: ${projects.length}`)
    projects.forEach((p, i) => {
      console.log(`  项目${i + 1}: "${p.name}", 楼层数: ${p.floors.length}, 风口数: ${p.floors.reduce((sum, f) => sum + (f.vents?.length || 0), 0)}`)
    })

    if (projects.length >= 1) {
      // 🔧 更智能的项目识别逻辑
      // 查找有风口但项目名称不合理的项目（包含风口信息、尺寸信息等）
      const projectWithVents = projects.find(p =>
        p.floors.length > 0 &&
        p.floors.some(f => f.vents && f.vents.length > 0) &&
        (
          /^\d+\s*[×xX]\s*\d+/.test(p.name) || // 尺寸格式
          p.name === '未命名项目' ||
          p.name.includes('风口') || // 包含风口信息
          /^[\u4e00-\u9fa5]{1,4}(出风口|回风口)[:：]\d+/.test(p.name) // 房间+风口格式
        )
      )

      // 查找没有风口但有真正项目名称的项目
      const projectWithName = projects.find(p =>
        (!p.floors.length || !p.floors.some(f => f.vents && f.vents.length > 0)) &&
        isProjectNameLine(p.name) &&
        !(/^\d+\s*[×xX]\s*\d+/.test(p.name)) && // 不是尺寸格式
        !p.name.includes('风口') // 不包含风口信息
      )

      console.log(`🔍 项目分析结果:`)
      console.log(`  - 有风口的项目: ${projectWithVents ? `"${projectWithVents.name}"` : '无'}`)
      console.log(`  - 有名称的项目: ${projectWithName ? `"${projectWithName.name}"` : '无'}`)

      if (projectWithVents && projectWithName) {
        console.log(`🔄 发现分离的项目信息:`)
        console.log(`  - 有风口的项目: "${projectWithVents.name}" (${projectWithVents.floors.reduce((sum, f) => sum + (f.vents?.length || 0), 0)} 个风口)`)
        console.log(`  - 有名称的项目: "${projectWithName.name}"`)

        // 合并项目：将风口数据移到有名称的项目中
        projectWithName.name = projectWithName.name
        projectWithName.floors = projectWithVents.floors

        // 从项目列表中移除有风口但名称不对的项目
        const indexToRemove = projects.indexOf(projectWithVents)
        if (indexToRemove > -1) {
          projects.splice(indexToRemove, 1)
        }

        console.log(`🔄 项目合并完成: "${projectWithName.name}" 包含 ${projectWithName.floors.reduce((sum, f) => sum + (f.vents?.length || 0), 0)} 个风口`)
      }
      // 如果只有一个项目，但项目名称像尺寸，尝试从文本中找真正的项目名称
      else if (projects.length === 1 && projectWithVents && !projectWithName) {
        const project = projectWithVents

        // 在所有行中查找真正的项目名称（通常在后面）
        let realProjectName = ''
        let realProjectNameIndex = -1

        for (let i = lines.length - 1; i >= 0; i--) {
          const line = lines[i].trim()
          if (isProjectNameLine(line) && !unifiedVentParser.parseDimensions(line)) {
            // 确保这个项目名称后面没有更多的尺寸信息
            const hasVentInfoAfter = lines.slice(i + 1).some(laterLine =>
              unifiedVentParser.parseDimensions(laterLine.trim())
            )

            if (!hasVentInfoAfter) {
              realProjectName = line
              realProjectNameIndex = i
              break
            }
          }
        }

        if (realProjectName && realProjectNameIndex > 0) {
          console.log(`🔄 发现后置项目名称: "${realProjectName}"，更新项目名称`)
          project.name = realProjectName
        }
      }
    }

    console.log(`🎯 解析完成，共识别 ${projects.length} 个项目`)
    return projects
  }

  // 辅助函数：检测是否为项目名称行
  const isProjectNameLine = (line: string): boolean => {
    const trimmedLine = line.trim()

    // 🚫 排除明显不是项目名称的内容
    if (trimmedLine.length < 2 ||
        /^\d+[A-Z]?$/.test(trimmedLine) || // 纯数字或数字+字母（如"1B"）
        /出风|回风|进气|检修|维修/.test(trimmedLine) || // 包含风口关键词
        /^\d+\s*[×xX*+\-]\s*\d+/.test(trimmedLine) || // 🔧 纯尺寸信息（如"2680×140"）
        /^[A-Z]\d{2,4}$/.test(trimmedLine) || // 房间编号格式（如A101）
        /^白线型$|^黑线型$|^线型$/.test(trimmedLine) || // 🔧 排除风口类型描述
        /^√$|^✓$|^×$/.test(trimmedLine) || // 🔧 排除符号
        /^象牙白$|^纯白$|^白色$|^黑色$|^灰色$|^银色$|^金色$/.test(trimmedLine) || // 🔧 排除颜色信息
        /^\d+(\.\d+)?$/.test(trimmedLine) || // 🔧 排除纯数字（如价格、数量等）
        /^回风$|^出风$/.test(trimmedLine)) { // 🔧 排除风口类型关键词
      return false
    }

    // 🔧 强排除：纯尺寸格式（更严格的检查）
    if (/^\d{3,5}\s*[×xX*]\s*\d{2,4}(\([^)]*\))?$/.test(trimmedLine)) {
      console.log(`🚫 排除纯尺寸格式: "${trimmedLine}"`)
      return false
    }

    // 🔧 强排除：包含尺寸的房间描述（如"楼上客厅出凤口175x1300"、"大厅出风口:3090✘150"）
    if (/[\u4e00-\u9fa5]+.*\d{2,4}\s*[×xX*:：]\s*\d{2,4}/.test(trimmedLine)) {
      console.log(`🚫 排除包含尺寸的房间描述: "${trimmedLine}"`)
      return false
    }

    // 🔧 强排除：尺寸+房间信息格式（如"26米x135=大厅"）
    if (/\d+米?\s*[×xX*]\s*\d+\s*=\s*[\u4e00-\u9fa5]+/.test(trimmedLine)) {
      console.log(`🚫 排除尺寸+房间信息格式: "${trimmedLine}"`)
      return false
    }

    // 🔧 强排除：圆形风口格式（如"圆口=直径160=2个"）
    if (/圆口|直径/.test(trimmedLine)) {
      console.log(`🚫 排除圆形风口格式: "${trimmedLine}"`)
      return false
    }

    // 🔧 强排除：包含风口信息的行（更严格）
    if (trimmedLine.includes('风口') && (/\d{3,4}/.test(trimmedLine) || /[×xX*:：]\d+/.test(trimmedLine))) {
      console.log(`🚫 排除包含风口信息的行: "${trimmedLine}"`)
      return false
    }

    // 🔧 项目名称识别：更精确的匹配，避免误识别房间名
    const projectPatterns = [
      // 🔧 明确的建筑项目关键词（避免误识别房间）
      /小区|花园|广场|大厦|公寓|别墅|商城|中心|大楼|写字楼|苑|城|府|园|庄|村|院|街|路|巷|里|弄/,
      /\d+栋|\d+号楼|\d+期/,
      /房号|地址/,
      // 🔧 项目名称+字母后缀格式（如"松桂苑A"、"松桂苑B"）
      /[\u4e00-\u9fa5]+[A-Z]$/,
      // 🔧 项目名称+数字后缀格式（如"金桂苑1"、"金桂苑2"）
      /[\u4e00-\u9fa5]+\d+$/,
      // 🔧 包含"用"字的项目描述（如"南乡发厅大厅用"）
      /[\u4e00-\u9fa5]+用$/,
      // 🔧 项目名称+房号格式（如"天玺湾56-502"）
      /[\u4e00-\u9fa5]{2,}[\d\-]+$/,
      // 🔧 包含"湾"、"城"等地名后缀的格式
      /[\u4e00-\u9fa5]*[湾城港岛山河湖海][\u4e00-\u9fa5\d\-]*$/
    ]

    // 🚫 强排除：明确的房间类型（避免误识别为项目名称）
    const roomKeywords = [
      '活动室', '客厅', '茶室', '棋牌室', '餐厅', '厨房', 'KTV',
      '主卧', '次卧', '卧室', '书房', '卫生间', '洗手间', '阳台',
      '办公室', '会议室', '接待室', '休息室', '娱乐室', '影音室',
      '大厅', '前台', '包间', '包厢', '储藏室', '杂物间', '过道',
      '走廊', '楼梯间', '电梯间', '机房', '配电室', '弱电间'
    ]

    // 🚫 如果是明确的房间类型，直接排除
    if (roomKeywords.includes(trimmedLine)) {
      console.log(`🚫 排除房间类型: "${trimmedLine}"`)
      return false
    }

    // 检查是否匹配项目模式
    const matchesPattern = projectPatterns.some(pattern => pattern.test(trimmedLine))

    // 🔧 额外检查：如果是纯中文且长度合适，也可能是项目名称
    const isPureChinese = /^[\u4e00-\u9fa5]+$/.test(trimmedLine) && trimmedLine.length >= 3 && trimmedLine.length <= 15

    const result = matchesPattern || isPureChinese
    if (result) {
      console.log(`✅ 识别为项目名称: "${trimmedLine}"`)
    }

    return result
  }

  // 🔧 新增：从任意行中提取楼层信息
  const extractFloorFromLine = (line: string): string | null => {
    const trimmed = line.trim()

    // 1. 匹配行首的楼层信息（如"一楼厨房出风口..."、"二楼客厅..."）
    const floorAtStartMatch = trimmed.match(/^(一|二|三|四|五|六|七|八|九|十|十一|十二)楼/)
    if (floorAtStartMatch) {
      const chineseFloor = floorAtStartMatch[1]
      const floorNumber = convertChineseFloorToNumber(chineseFloor)
      console.log(`🏢 提取行首中文楼层: "${chineseFloor}楼" → "${floorNumber}"`)
      return floorNumber
    }

    // 2. 匹配行首的数字楼层（如"1楼客厅..."、"2F主卧..."）
    const numericFloorMatch = trimmed.match(/^(\d+)[楼层F]/)
    if (numericFloorMatch) {
      const floorNumber = numericFloorMatch[1]
      console.log(`🏢 提取行首数字楼层: "${floorNumber}"`)
      return floorNumber
    }

    return null
  }

  // 🔧 新增：中文楼层转数字
  const convertChineseFloorToNumber = (chineseFloor: string): string => {
    const chineseNumbers: { [key: string]: string } = {
      '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
      '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
    }
    return chineseNumbers[chineseFloor] || chineseFloor
  }

  // 辅助函数：检测是否为纯楼层行
  const isFloorLine = (line: string): boolean => {
    const floorPatterns = [
      /^(一|二|三|四|五|六|七|八|九|十|十一|十二)[楼层]$/,
      /^\d+[楼层]$/,
      /^\d+F$/i,
      /^第\d+层$/,
      /^[一二三四五六七八九十]+层$/
    ]

    return floorPatterns.some(pattern => pattern.test(line.trim()))
  }

  // 🏠 辅助函数：检测是否为房间行
  const isRoomLine = (line: string): boolean => {
    // 房间关键词
    const roomKeywords = [
      '活动室', '客厅', '茶室', '棋牌室', '餐厅', '厨房', 'KTV',
      '主卧', '次卧', '卧室', '书房', '卫生间', '洗手间', '阳台',
      '办公室', '会议室', '接待室', '休息室', '娱乐室', '影音室',
      '大厅', '前台', '包间', '包厢', '储藏室', '杂物间'
    ]

    const trimmedLine = line.trim()

    // 检查是否为纯房间名称（不包含尺寸信息）
    if (roomKeywords.some(keyword => trimmedLine === keyword)) {
      return true
    }

    // 检查字母+数字房间编号（如A201, B302）
    if (/^[A-Z]\d{2,4}$/.test(trimmedLine)) {
      return true
    }

    // 检查数字+房间格式（如1901号房）
    if (/^\d+号?房\d*$/.test(trimmedLine)) {
      return true
    }

    // 🔧 检查纯数字房间号（如101, 102, 103, 104, 105）
    if (/^\d{3,4}$/.test(trimmedLine)) {
      const num = parseInt(trimmedLine)
      console.log(`🏠 检查房间号: ${num}`)

      // 房间号通常是 101-199, 201-299, 1001-1999 等格式
      const lastTwoDigits = num % 100
      console.log(`🏠 房间号末尾两位: ${lastTwoDigits}`)

      // 扩大房间号范围：01-99 都认为是有效房间号
      if (lastTwoDigits >= 1 && lastTwoDigits <= 99) {
        console.log(`✅ 识别为房间号: ${trimmedLine}`)
        return true
      }
    }

    return false
  }

  // 🏢 楼层名称标准化函数：将各种楼层格式转换为数字格式
  const normalizeFloorName = (floorName: string): string => {
    if (!floorName) return '1'

    const trimmed = floorName.trim()
    console.log(`🏢 标准化楼层名称: "${trimmed}"`)

    // 🏠 新增：特殊项目格式楼层识别（如"3-2702"、"3栋2702"、"A座2702"）
    // 支持后面有额外信息的情况（如联系人姓名）
    const specialProjectMatch = trimmed.match(/^(.+?)[-栋座](\d{4})/)
    if (specialProjectMatch) {
      const [, projectPrefix, roomNumber] = specialProjectMatch
      const floor = roomNumber.substring(0, 2) // 取前两位作为楼层
      console.log(`🏠 特殊项目格式楼层识别: "${trimmed}" → 前缀:"${projectPrefix}" 房间:"${roomNumber}" 楼层:"${floor}"`)
      return floor
    }

    // 🏠 新增：复杂项目格式（如"上淮府3-2702"、"天润城16-1905"）
    // 支持后面有额外信息的情况（如联系人姓名）
    const complexProjectMatch = trimmed.match(/^(.+?)(\d+)[-栋座](\d{4})/)
    if (complexProjectMatch) {
      const [, projectName, buildingNum, roomNumber] = complexProjectMatch
      const floor = roomNumber.substring(0, 2) // 取前两位作为楼层
      console.log(`🏠 复杂项目格式楼层识别: "${trimmed}" → 项目:"${projectName}" 栋号:"${buildingNum}" 房间:"${roomNumber}" 楼层:"${floor}"`)
      return floor
    }

    // 🏠 原有逻辑：项目名称+房间号格式（如"书香华府1201"）
    const projectRoomMatch = trimmed.match(/^(.+?)(\d{3,4})$/)
    if (projectRoomMatch) {
      const [, projectName, roomNumber] = projectRoomMatch
      // 从房间号中提取楼层（如1201 → 12楼）
      if (roomNumber.length === 4) {
        const floor = roomNumber.substring(0, 2)
        console.log(`🏠 从项目房间号提取楼层: "${trimmed}" → 项目:"${projectName}" 房间:"${roomNumber}" 楼层:"${floor}"`)
        return floor
      } else if (roomNumber.length === 3) {
        const floor = roomNumber.substring(0, 1)
        console.log(`🏠 从项目房间号提取楼层: "${trimmed}" → 项目:"${projectName}" 房间:"${roomNumber}" 楼层:"${floor}"`)
        return floor
      }
    }

    // 中文数字映射
    const chineseNumbers: { [key: string]: number } = {
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
      '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5, '陆': 6, '柒': 7, '捌': 8, '玖': 9, '拾': 10
    }

    // 1. 处理纯数字格式（如 "1"、"2"）
    if (/^\d+$/.test(trimmed)) {
      console.log(`✅ 纯数字楼层: "${trimmed}" → "${trimmed}"`)
      return trimmed
    }

    // 2. 处理阿拉伯数字+楼/层格式（如 "1楼"、"2层"、"3F"）
    const arabicMatch = trimmed.match(/^(\d+)[楼层F]?$/i)
    if (arabicMatch) {
      const number = arabicMatch[1]
      console.log(`✅ 阿拉伯数字楼层: "${trimmed}" → "${number}"`)
      return number
    }

    // 3. 处理中文数字+楼/层格式（如 "一楼"、"二层"）
    const chineseMatch = trimmed.match(/^([一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾])[楼层]?$/)
    if (chineseMatch) {
      const chineseNum = chineseMatch[1]
      const number = chineseNumbers[chineseNum]
      if (number) {
        console.log(`✅ 中文数字楼层: "${trimmed}" → "${number}"`)
        return number.toString()
      }
    }

    // 4. 处理特殊格式（如 "第1层"、"B1楼"）
    const specialMatch = trimmed.match(/第?(\d+)[层楼]?$|[BF](\d+)[楼]?$/i)
    if (specialMatch) {
      const number = specialMatch[1] || specialMatch[2]
      console.log(`✅ 特殊格式楼层: "${trimmed}" → "${number}"`)
      return number
    }

    // 5. 处理地下楼层（如 "地下1层"、"负1楼"）
    const undergroundMatch = trimmed.match(/(?:地下|负)(\d+)[层楼]?$/)
    if (undergroundMatch) {
      const number = `-${undergroundMatch[1]}`
      console.log(`✅ 地下楼层: "${trimmed}" → "${number}"`)
      return number
    }

    // 6. 如果都不匹配，尝试提取数字（但要排除明显的尺寸数字）
    const numberMatch = trimmed.match(/(\d+)/)
    if (numberMatch) {
      const number = numberMatch[1]

      // 🔧 智能判断：排除明显的尺寸数字
      const numberValue = parseInt(number)

      // 如果数字太大（>1000），很可能是尺寸而不是楼层
      if (numberValue > 1000) {
        console.log(`🚫 数字过大，疑似尺寸: "${trimmed}" → 数字"${number}" (${numberValue}) 跳过楼层提取`)
        return '1' // 使用默认楼层
      }

      // 如果文本包含风口相关关键词，且数字很大，也跳过
      if ((trimmed.includes('风口') || trimmed.includes('×') || trimmed.includes('x') || trimmed.includes('X')) && numberValue > 500) {
        console.log(`🚫 包含风口信息且数字较大: "${trimmed}" → 数字"${number}" (${numberValue}) 跳过楼层提取`)
        return '1' // 使用默认楼层
      }

      console.log(`✅ 提取数字: "${trimmed}" → "${number}"`)
      return number
    }

    // 7. 默认返回1
    console.log(`⚠️ 无法标准化，使用默认值: "${trimmed}" → "1"`)
    return '1'
  }

  // 🏠 从项目信息中提取房间号的函数
  const extractRoomFromProject = (projectInfo: string): string => {
    console.log(`🏠 从项目信息提取房间号: "${projectInfo}"`)

    // 匹配项目名称+房间号格式（如"书香华府1201"）
    const projectRoomMatch = projectInfo.match(/^(.+?)(\d{3,4})$/)
    if (projectRoomMatch) {
      const [, projectName, roomNumber] = projectRoomMatch
      console.log(`✅ 提取房间号: 项目:"${projectName}" 房间:"${roomNumber}"`)
      return roomNumber
    }

    // 如果没有匹配，返回空字符串
    console.log(`❌ 未找到房间号`)
    return ''
  }

  // 🔧 连续风口信息智能分割函数
  const splitContinuousVentInfo = (text: string): string[] => {
    console.log(`🔄 连续风口信息分割: "${text}"`)

    // 检查是否包含多个风口信息（通过风口关键词数量判断）
    const ventKeywords = /(出风口?|回风口?|进气口?|检修口?|维修口?)/g
    const ventMatches = text.match(ventKeywords)

    if (!ventMatches || ventMatches.length <= 1) {
      console.log(`📝 单个风口信息，无需分割`)
      return [text]
    }

    console.log(`🔍 检测到 ${ventMatches.length} 个风口关键词，开始分割`)

    // 🔧 修复：按照逗号、句号、分号分割，但排除数字中的小数点
    // 使用负向前瞻和负向后顾，避免分割数字中的小数点
    const segments = text.split(/[，；,;]|(?<!\d)\.(?!\d)/)
      .map(segment => segment.trim())
      .filter(segment => segment.length > 0)

    console.log(`✂️ 初步分割结果: ${segments.length} 段`)
    segments.forEach((segment, index) => {
      console.log(`  ${index + 1}. "${segment}"`)
    })

    // 智能合并：确保每段都包含完整的风口信息
    const mergedSegments: string[] = []
    let currentSegment = ''

    for (const segment of segments) {
      // 如果当前段包含风口关键词和尺寸信息，认为是完整的
      const hasVentKeyword = /(出风口?|回风口?|进气口?|检修口?|维修口?)/.test(segment)
      const hasDimensions = /\d+\s*[xX×*+\-]\s*\d+/.test(segment)

      if (hasVentKeyword && hasDimensions) {
        // 完整的风口信息
        if (currentSegment) {
          mergedSegments.push(currentSegment.trim())
          currentSegment = ''
        }
        mergedSegments.push(segment)
        console.log(`  ✅ 完整风口信息: "${segment}"`)
      } else if (hasVentKeyword && !hasDimensions) {
        // 只有风口关键词，需要等待尺寸信息
        currentSegment = segment
        console.log(`  🔄 等待尺寸信息: "${segment}"`)
      } else if (!hasVentKeyword && hasDimensions && currentSegment) {
        // 只有尺寸信息，与前面的关键词合并
        mergedSegments.push(`${currentSegment} ${segment}`.trim())
        console.log(`  🔗 合并信息: "${currentSegment} ${segment}"`)
        currentSegment = ''
      } else {
        // 其他情况，添加到当前段
        if (currentSegment) {
          currentSegment += ` ${segment}`
        } else {
          currentSegment = segment
        }
      }
    }

    // 处理最后一段
    if (currentSegment) {
      mergedSegments.push(currentSegment.trim())
    }

    console.log(`🎯 最终分割结果: ${mergedSegments.length} 个风口信息`)
    mergedSegments.forEach((segment, index) => {
      console.log(`  ${index + 1}. "${segment}"`)
    })

    return mergedSegments.length > 0 ? mergedSegments : [text]
  }

  // 🔧 检测内容格式类型
  const detectContentFormat = (content: string): 'continuous' | 'line-by-line' | 'messy-layout' => {
    const lines = content.split('\n').filter(line => line.trim().length > 0)

    // 如果只有一行或包含分号分隔符，判断为连续格式
    if (lines.length === 1 || content.includes('；') || content.includes(';')) {
      return 'continuous'
    }

    // 🔧 检测排版混乱的内容
    const messyLayoutIndicators = {
      // 检查是否有很多短行（可能是OCR识别错误导致的分割）
      shortLines: lines.filter(line => line.length < 10).length,
      // 检查是否有很多只包含数字或符号的行
      numberOnlyLines: lines.filter(line => /^[\d\s×xX*+\-]+$/.test(line)).length,
      // 检查是否有很多单字符行
      singleCharLines: lines.filter(line => line.length === 1).length,
      // 检查是否有很多包含特殊符号但没有明确项目信息的行
      symbolLines: lines.filter(line => /[×xX*+\-]/.test(line) && !/出风|回风|项目|地址|楼/.test(line)).length
    }

    const totalLines = lines.length
    const messyScore = (
      messyLayoutIndicators.shortLines / totalLines * 0.3 +
      messyLayoutIndicators.numberOnlyLines / totalLines * 0.4 +
      messyLayoutIndicators.singleCharLines / totalLines * 0.2 +
      messyLayoutIndicators.symbolLines / totalLines * 0.1
    )

    console.log(`🔍 排版混乱检测:`, {
      totalLines,
      ...messyLayoutIndicators,
      messyScore: messyScore.toFixed(2)
    })

    // 如果混乱度超过0.4，认为是排版混乱的内容
    if (messyScore > 0.4) {
      console.log('🚨 检测到排版混乱的内容，将使用简化解析模式')
      return 'messy-layout'
    }

    // 🔧 改进：支持更多风口识别模式，包括✖️符号和房间号格式
    const linesWithVents = lines.filter(line => {
      // 标准化字符串以支持✖️符号
      const normalizedLine = line.replace(/✖️/g, '×').replace(/✖/g, '×')

      const hasVentKeyword = /出风口|回风口|进气口|出风|回风/.test(line)
      const hasDimension = /\d+\s*[xX×*+\-]\s*\d+/.test(normalizedLine)
      const hasRoomVent = /[A-Z]?\d+[A-Z]?\d*\s*(出风|回风)/.test(line) // 如 A101出风

      return hasVentKeyword || hasDimension || hasRoomVent
    })

    console.log(`🔍 格式检测: 总行数=${lines.length}, 风口行数=${linesWithVents.length}, 比例=${(linesWithVents.length / lines.length).toFixed(2)}`)

    // 🔧 降低阈值：如果超过30%的行包含风口信息，就认为是分行格式
    if (linesWithVents.length >= lines.length * 0.3) {
      return 'line-by-line'
    }

    // 默认使用连续格式
    return 'continuous'
  }

  // 🔧 OCR错误自动修正函数（保守策略）
  const autoCorrectOCRErrors = (text: string): string => {
    let correctedText = text

    // 🔧 只修正明确的OCR错误，避免误伤正常文本

    // 1. 修正明显的风口类型OCR错误（完整词组替换）
    const ventTypeCorrections = {
      // "出风"的明显OCR错误
      '岀风': '出风',
      '岀凤': '出风',
      '凸风': '出风',
      '凹风': '出风',

      // "回风"的明显OCR错误
      '回凤': '回风',
      '回凰': '回风',
      '囘风': '回风',
      '囬风': '回风',

      // "进气"的明显OCR错误
      '进汽': '进气',
      '進气': '进气',
      '進汽': '进气',

      // "检修"的明显OCR错误
      '检休': '检修',
      '检体': '检修',
      '檢修': '检修',
      '檢休': '检修',

      // "维修"的明显OCR错误
      '维体': '维修',
      '维休': '维修',
      '維修': '维修',
      '維休': '维修',
    }

    // 应用风口类型修正（只替换完整词组）
    for (const [wrong, correct] of Object.entries(ventTypeCorrections)) {
      const regex = new RegExp(wrong, 'g')
      correctedText = correctedText.replace(regex, correct)
    }

    // 2. 🔧 非常保守的尺寸格式修正（只修正明显的OCR符号错误）
    // 修正明显的乘号OCR错误
    correctedText = correctedText.replace(/(\d+(?:\.\d+)?)\s*[xX]\s*(\d+(?:\.\d+)?)/g, '$1×$2')

    // 修正明显的emoji乘号
    correctedText = correctedText.replace(/(\d+(?:\.\d+)?)\s*✖️\s*(\d+(?:\.\d+)?)/g, '$1×$2')

    // 🔧 特殊格式修正：处理"长"字缺失的情况
    // 例如："出风1.2×宽0.15x3个" → "出风长1.2×宽0.15x3个"
    correctedText = correctedText.replace(/(出风|回风|进气|检修|维修)(\d+(?:\.\d+)?)\s*[xX×]\s*宽/g, '$1长$2×宽')

    // 🔧 修正数字和单位之间的OCR错误
    // 例如："1.2x宽0.15x3个" 中的小写x应该是×
    correctedText = correctedText.replace(/(\d+(?:\.\d+)?)\s*x\s*/g, '$1×')
    correctedText = correctedText.replace(/(\d+(?:\.\d+)?)\s*X\s*/g, '$1×')

    if (text !== correctedText) {
      console.log(`🔧 OCR自动修正: "${text}" → "${correctedText}"`)
    }
    return correctedText
  }

  // 🔧 解析分行格式
  const parseLineByLineFormat = (content: string): ProjectData[] => {
    console.log('🔍 使用分行格式解析')

    const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0)
    const projects: ProjectData[] = []
    let currentProject: ProjectData | null = null
    let currentFloor: FloorData | null = null

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i]
      console.log(`🔍 处理第${i+1}行: "${line}"`)

      // 🔧 智能粘贴不需要OCR修正
      // line = autoCorrectOCRErrors(line) // 禁用OCR修正

      // 检测项目地址
      const projectMatch = detectProjectAddress(line)
      if (projectMatch.isProject) {
        console.log(`🏠 识别到项目地址: ${projectMatch.address}`)

        // 保存当前项目
        if (currentProject && currentProject.floors.length > 0) {
          projects.push(currentProject)
        }

        // 创建新项目
        currentProject = {
          id: generateUniqueId('project'),
          orderDate: extractDateFromLine(line) || new Date().toISOString().split('T')[0].replace(/-/g, '/'),
          projectAddress: projectMatch.address,
          floors: [],
          totalAmount: 0
        }
        currentFloor = null
        continue
      }

      // 检测楼层信息
      const floorMatch = detectFloorInfo(line)
      if (floorMatch.isFloor) {
        console.log(`🏢 识别到楼层: ${floorMatch.floorName}`)

        if (!currentProject) {
          // 如果没有项目，创建默认项目
          currentProject = {
            id: generateUniqueId('project'),
            orderDate: new Date().toISOString().split('T')[0].replace(/-/g, '/'),
            projectAddress: '未指定项目',
            floors: [],
            totalAmount: 0
          }
        }

        currentFloor = {
          id: generateUniqueId('floor'),
          floorName: normalizeFloorName(floorMatch.floorName), // 🏢 标准化楼层名称
          vents: [],
          totalPrice: 0
        }
        currentProject.floors.push(currentFloor)
        continue
      }

      // 🔧 改进：解析风口信息（支持一行多个风口）
      const ventItems = parseLineVents(line)
      if (ventItems.length > 0) {
        console.log(`💨 在第${i+1}行识别到 ${ventItems.length} 个风口`)

        if (!currentProject) {
          currentProject = {
            id: generateUniqueId('project'),
            orderDate: new Date().toISOString().split('T')[0].replace(/-/g, '/'),
            projectAddress: '未指定项目',
            floors: [],
            totalAmount: 0
          }
        }

        if (!currentFloor) {
          currentFloor = {
            id: generateUniqueId('floor'),
            floorName: '1楼',
            vents: [],
            totalPrice: 0
          }
          currentProject.floors.push(currentFloor)
        }

        // 添加所有识别到的风口
        for (const ventItem of ventItems) {
          const vent: VentData = {
            id: generateUniqueId('vent'),
            originalInfo: ventItem.original,
            length: ventItem.length,
            width: ventItem.width,
            quantity: ventItem.quantity,
            color: '象牙白',
            type: ventItem.type as VentItem['type'],
            unitPrice: getDefaultPriceByType(ventItem.type),
            notes: ventItem.notes,
            totalPrice: 0
          }
          vent.totalPrice = calculateVentPriceLocal(vent)
          currentFloor.vents.push(vent)
          console.log(`✅ 添加风口: ${ventItem.type} ${ventItem.length}×${ventItem.width} 备注:"${ventItem.notes}"`)
        }
      }
    }

    // 保存最后一个项目
    if (currentProject && currentProject.floors.length > 0) {
      projects.push(currentProject)
    }

    // 计算每个项目和楼层的总价 - 精确到一位小数
    projects.forEach(project => {
      project.floors.forEach(floor => {
        const floorTotal = floor.vents.reduce((sum, vent) => sum + (vent.totalPrice || 0), 0)
        floor.totalPrice = Math.round(floorTotal * 10) / 10
      })
      const projectTotal = project.floors.reduce((sum, floor) => sum + (floor.totalPrice || 0), 0)
      project.totalAmount = Math.round(projectTotal * 10) / 10
    })

    console.log(`🏗️ 分行格式解析完成，共识别 ${projects.length} 个项目`)
    return projects
  }

  // 🔧 解析一行中的多个风口
  const parseLineVents = (line: string): Array<{
    original: string,
    type: string,
    length: number,
    width: number,
    quantity: number,
    notes: string
  }> => {
    const vents: Array<{
      original: string,
      type: string,
      length: number,
      width: number,
      quantity: number,
      notes: string
    }> = []

    console.log(`🔍 解析行内容: "${line}"`)

    // 🔧 改进的分割逻辑：支持多种分隔符，但排除数字中的小数点
    const segments = line.split(/[，,、；;]|(?<!\d)\.(?!\d)/).filter(s => s.trim().length > 0)

    for (const segment of segments) {
      const trimmed = segment.trim()
      if (!trimmed) continue

      console.log(`🔍 解析段落: "${trimmed}"`)

      // 🔧 使用改进的风口解析逻辑
      const ventInfo = parseVentSegmentImproved(trimmed)
      if (ventInfo) {
        vents.push(ventInfo)
        console.log(`✅ 成功解析: ${ventInfo.type} ${ventInfo.length}×${ventInfo.width} 备注:"${ventInfo.notes}"`)
      } else {
        console.log(`❌ 无法解析段落: "${trimmed}"`)
      }
    }

    return vents
  }

  // 🔧 改进的风口段落解析
  const parseVentSegmentImproved = (segment: string): {
    original: string,
    type: string,
    length: number,
    width: number,
    quantity: number,
    notes: string
  } | null => {
    console.log(`🔍 改进解析风口段: "${segment}"`)

    // 🔧 新增：智能备注清理函数
    const cleanNotesEnhanced = (rawNotes: string): { cleanedNotes: string, removedItems: string[] } => {
      if (!rawNotes || typeof rawNotes !== 'string') {
        return { cleanedNotes: '', removedItems: [] }
      }

      const removedItems: string[] = []
      let cleanedText = rawNotes.trim()
      const meaningfulParts: string[] = []

      console.log(`🧹 开始清理备注: "${rawNotes}"`)

      // 1. 提取有意义的内容（在清理前先保存）

      // 1.1 提取圆口相关信息
      const circularMatch = cleanedText.match(/(圆口|圆形|圆)/i)
      if (circularMatch) {
        meaningfulParts.push('圆口')
        console.log(`✅ 保留圆口信息: 圆口`)
      }

      // 1.2 提取直径信息
      const diameterMatch = cleanedText.match(/直径\s*(\d+(?:\.\d+)?)\s*(mm|cm|m|毫米|厘米|米)?/i)
      if (diameterMatch) {
        const diameter = diameterMatch[1]
        const unit = diameterMatch[2] || 'mm'
        meaningfulParts.push(`直径${diameter}${unit}`)
        console.log(`✅ 保留直径信息: 直径${diameter}${unit}`)
      }

      // 1.3 提取等号后的房间信息
      const roomMatch = cleanedText.match(/=\s*([^=\d]*[客卧餐厨厅房间卧室厨房卫生间阳台书房办公室会议室走廊过道][^=\d]*)/i)
      if (roomMatch) {
        const roomInfo = roomMatch[1].trim()
        if (roomInfo.length > 0) {
          meaningfulParts.push(roomInfo)
          console.log(`✅ 保留房间信息: ${roomInfo}`)
        }
      }

      // 1.4 提取房间类型信息（不依赖等号）
      const roomPatterns = [
        /[客卧餐厨]厅/,
        /房间|卧室|厨房|卫生间|阳台|书房/,
        /办公室|会议室|走廊|过道/,
        /大厅|小厅|前厅|后厅/,
        /客房|主卧|次卧|储藏室/
      ]

      roomPatterns.forEach(pattern => {
        const match = cleanedText.match(pattern)
        if (match && !meaningfulParts.includes(match[0])) {
          meaningfulParts.push(match[0])
          console.log(`✅ 保留房间类型: ${match[0]}`)
        }
      })

      // 1.5 提取其他有意义内容
      const otherMeaningfulPatterns = [
        /安装|吊顶|贴墙|嵌入|明装|暗装|固定/,
        /静音|降噪|防水|防潮|耐高温|阻燃/,
        /左侧|右侧|中间|角落|靠窗|靠门|顶部|底部/,
        /定制|特殊|加急|自提|送货|包装/,
        /\d+楼|[一二三四五六七八九十]+楼|地下|顶楼/
      ]

      otherMeaningfulPatterns.forEach(pattern => {
        const match = cleanedText.match(pattern)
        if (match && !meaningfulParts.includes(match[0])) {
          meaningfulParts.push(match[0])
          console.log(`✅ 保留其他信息: ${match[0]}`)
        }
      })

      // 2. 现在进行清理，移除无用信息

      // 2.1 移除尺寸信息
      cleanedText = cleanedText.replace(/\d+(?:\.\d+)?\s*[x×*乘]\s*\d+(?:\.\d+)?\s*(mm|cm|m|毫米|厘米|米)?/gi, (match) => {
        removedItems.push(`尺寸信息: ${match}`)
        return ''
      })

      // 2.2 移除价格信息
      cleanedText = cleanedText.replace(/￥\d+(?:\.\d+)?/g, (match) => {
        removedItems.push(`价格: ${match}`)
        return ''
      })

      // 2.3 移除数量信息（纯数字+量词）
      cleanedText = cleanedText.replace(/\b\d+\s*[个只套件张块]\b/g, (match) => {
        removedItems.push(`数量: ${match}`)
        return ''
      })

      // 2.4 移除序号
      cleanedText = cleanedText.replace(/^\d+\s*[、.。]?\s*/, (match) => {
        removedItems.push(`序号: ${match}`)
        return ''
      })

      // 2.5 移除等号和无意义符号
      cleanedText = cleanedText
        .replace(/[×x*=+\-]/g, '') // 移除运算符号
        .replace(/\s+/g, ' ')      // 合并多个空格
        .trim()

      // 3. 组合有意义的内容
      const finalNotes = meaningfulParts.length > 0 ? meaningfulParts.join('、') : ''

      console.log(`🎯 备注清理结果: "${rawNotes}" → "${finalNotes}"`)
      console.log(`🗑️ 移除内容: ${removedItems.join(', ') || '无'}`)

      return { cleanedNotes: finalNotes, removedItems }
    }

    // 🔧 改进：风口类型关键词，包括简化形式
    const ventTypeKeywords = ['出风口', '回风口', '进气口', '检修口', '维修口', '出风', '回风', '进气', '检修', '维修']

    // 🔧 改进：使用增强的尺寸解析器
    const dimensionResult = parseDimensionString(segment)
    if (!dimensionResult) {
      console.log(`❌ 增强解析器未找到尺寸信息`)
      return null
    }

    let ventType = ''
    let length = dimensionResult.length
    let width = dimensionResult.width
    let quantity = 1
    let notes = ''

    console.log(`✅ 增强解析器识别尺寸: ${length}×${width}mm`)

    // 在原始字符串中查找尺寸位置（使用通用模式）
    const dimensionPattern = /(\d+(?:\.\d+)?)\s*[xX×*+\-米厘毫]\s*(\d+(?:\.\d+)?)/
    const originalDimensionMatch = segment.match(dimensionPattern)
    const dimensionPosition = originalDimensionMatch ? segment.indexOf(originalDimensionMatch[0]) : -1
    const dimensionEnd = dimensionPosition + (originalDimensionMatch ? originalDimensionMatch[0].length : 0)

    console.log(`📏 尺寸: ${length}×${width}mm, 位置: ${dimensionPosition}-${dimensionEnd}`)

    // 🔧 步骤2: 查找风口类型和位置
    let ventTypeMatch = null
    let ventTypePosition = -1

    // 查找完整的风口关键词
    for (const keyword of ventTypeKeywords) {
      const index = segment.indexOf(keyword)
      if (index !== -1) {
        ventType = keyword
        ventTypeMatch = keyword
        ventTypePosition = index
        console.log(`🏷️ 找到风口类型: ${ventType}, 位置: ${ventTypePosition}`)
        break
      }
    }

    // 🔧 新增：如果没找到明确关键词，检查是否是房间号+风口格式
    if (!ventType) {
      // 匹配房间号+风口类型，如 A101出风、B202回风
      const roomVentMatch = segment.match(/([A-Z]?\d+[A-Z]?\d*)\s*(出风|回风|进气|检修|维修)/)
      if (roomVentMatch) {
        ventType = roomVentMatch[2]
        ventTypeMatch = roomVentMatch[2]
        ventTypePosition = roomVentMatch.index! + roomVentMatch[1].length
        // 房间号作为备注的一部分
        notes = roomVentMatch[1]
        console.log(`🏠 识别房间号+风口: 房间=${roomVentMatch[1]}, 类型=${ventType}`)
      }
    }

    console.log(`🏷️ 风口类型: ${ventType}, 位置: ${ventTypePosition}`)

    // 🔧 步骤3: 智能提取备注（按照用户需求：风口类型在前则尺寸后为备注，风口类型在后则尺寸前为备注）
    if (ventTypeMatch && ventTypePosition !== -1) {
      if (ventTypePosition < dimensionPosition) {
        // 风口类型在尺寸前面：尺寸后面的内容作为备注
        const afterDimension = segment.substring(dimensionEnd).trim()
        if (afterDimension.length > 0) {
          // 清理备注：移除数量信息
          const cleanedNotes = afterDimension.replace(/\d+\s*个/g, '').trim()
          if (cleanedNotes.length > 0) {
            notes = notes ? `${notes} ${cleanedNotes}` : cleanedNotes
            console.log(`📝 备注（风口类型在前，尺寸后）: "${notes}"`)
          }
        }
      } else {
        // 风口类型在尺寸后面：尺寸前面的内容作为备注
        const beforeDimension = segment.substring(0, dimensionPosition).trim()
        if (beforeDimension.length > 0) {
          // 清理备注：移除可能的项目信息
          let cleanedNotes = beforeDimension
          // 如果包含房间号，保留房间号
          if (!notes || !beforeDimension.includes(notes)) {
            notes = notes ? `${cleanedNotes} ${notes}` : cleanedNotes
            console.log(`📝 备注（风口类型在后，尺寸前）: "${notes}"`)
          }
        }
      }
    } else {
      // 没有明确的风口类型，整个段落除了尺寸部分都是备注
      const beforeDimension = segment.substring(0, dimensionPosition).trim()
      const afterDimension = segment.substring(dimensionEnd).trim()
      const rawNotes = [beforeDimension, afterDimension]
        .filter(s => s.length > 0)
        .join(' ')

      if (rawNotes.length > 0) {
        // 使用增强的备注清理功能
        const { cleanedNotes: cleanedNotesResult } = cleanNotesEnhanced(rawNotes)
        const cleanedNotes = cleanedNotesResult
        if (cleanedNotes.length > 0) {
          notes = notes ? `${notes} ${cleanedNotes}` : cleanedNotes
          console.log(`📝 备注（无明确类型，已清理）: "${notes}"`)
        } else {
          console.log(`🧹 备注清理后为空，原始: "${rawNotes}"`)
        }
      }
    }

    // 🔧 步骤4: 智能尺寸处理（先修正尺寸位置，大的作为长度）
    if (width > length) {
      [length, width] = [width, length]
      console.log(`🔄 交换尺寸: ${length}×${width}`)
    }

    // 🔧 步骤5: 智能判断风口类型（基于修正后的宽度）
    if (!ventType) {
      // 🔧 改进的风口类型判断逻辑
      // 1. 优先基于备注内容判断
      if (notes) {
        if (notes.includes('回风') || notes.includes('回风口')) {
          ventType = '回风口'
          console.log(`🎯 基于备注识别: ${ventType} (备注包含回风关键词)`)
        } else if (notes.includes('出风') || notes.includes('出风口')) {
          ventType = '出风口'
          console.log(`🎯 基于备注识别: ${ventType} (备注包含出风关键词)`)
        } else if (notes.includes('线型') || notes.includes('线性') || notes.includes('条形') || notes.includes('条型')) {
          ventType = '线型风口'
          console.log(`🎯 基于备注识别: ${ventType} (备注包含线型关键词)`)
        }
      }

      // 2. 如果备注没有明确指示，基于修正后的宽度判断
      if (!ventType) {
        // 线型风口：宽度很小（通常≤160mm）
        if (width <= 160) {
          ventType = '线型风口'
          console.log(`🤖 基于修正后宽度判断: ${ventType} (宽度${width}mm ≤ 160mm)`)
        }
        // 回风口：通常尺寸较大，且宽度≥255mm
        else if (width >= 255) {
          ventType = '回风口'
          console.log(`🤖 基于修正后宽度判断: ${ventType} (宽度${width}mm ≥ 255mm)`)
        }
        // 出风口：中等尺寸
        else {
          ventType = '出风口'
          console.log(`🤖 基于修正后宽度判断: ${ventType} (宽度${width}mm 在160-255mm之间)`)
        }
      }
    }

    // 🔧 步骤6: 提取数量（如果有）
    const quantityMatch = segment.match(/(\d+)\s*个/)
    if (quantityMatch) {
      quantity = parseInt(quantityMatch[1])
      console.log(`🔢 数量: ${quantity}`)
    }

    // 🔧 步骤7: 映射到系统风口类型
    const systemType = mapToSystemVentType(ventType, length, width)

    console.log(`✅ 解析结果: ${systemType} ${length}×${width} ×${quantity} 备注:"${notes}"`)

    return {
      original: segment,
      type: systemType,
      length,
      width,
      quantity,
      notes
    }
  }

  // 🔧 检测表格场景类型
  const detectTableScene = (lines: string[]): string => {
    console.log('🔍 检测表格场景类型...')

    const content = lines.join(' ')

    // 场景1: 标准表格 - 包含完整的列标题
    const standardTableHeaders = ['序号', '地址', '原始信息', '数量', '长度', '宽度', '颜色', '单价', '金额', '备注']
    const headerCount = standardTableHeaders.filter(header => content.includes(header)).length
    if (headerCount >= 6) {
      console.log(`📊 标准表格场景 (匹配${headerCount}个标题)`)
      return 'standard-table'
    }

    // 场景2: 简单列表 - 包含序号或项目符号
    const hasNumbering = lines.some(line => /^[1-9]\d*[.、]/.test(line))
    const hasBullets = lines.some(line => /^[•·-]\s/.test(line))
    if (hasNumbering || hasBullets) {
      console.log('📝 简单列表场景')
      return 'simple-list'
    }

    // 场景3: 混合格式 - 包含项目名称和多种格式
    const hasProjectKeywords = lines.some(line =>
      line.includes('项目') || line.includes('工程') || line.includes('订货单位')
    )
    if (hasProjectKeywords && content.includes('：')) {
      console.log('🔀 混合格式场景')
      return 'mixed-format'
    }

    // 场景4: 连续文本 - 没有明显结构
    console.log('📄 连续文本场景')
    return 'continuous-text'
  }

  // 🔧 提取项目信息
  const extractProjectInfo = (lines: string[]) => {
    console.log('🔍 提取项目信息...')

    const projectInfo = {
      orderDate: '',
      address: '',
      company: '',
      projectName: ''
    }

    for (const line of lines) {
      // 提取订货日期
      const dateMatch = line.match(/订货日期[：:]\s*(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})/)
      if (dateMatch) {
        projectInfo.orderDate = dateMatch[1].replace(/-/g, '/')
        console.log(`📅 找到订货日期: ${projectInfo.orderDate}`)
      }

      // 提取订货单位
      const companyMatch = line.match(/订货单位[：:]\s*(.+)/)
      if (companyMatch) {
        projectInfo.company = companyMatch[1].trim()
        console.log(`🏢 找到订货单位: ${projectInfo.company}`)
      }

      // 提取项目名称
      const projectMatch = line.match(/项目[：:]\s*(.+)/) || line.match(/工程[：:]\s*(.+)/)
      if (projectMatch) {
        projectInfo.projectName = projectMatch[1].trim()
        console.log(`🏗️ 找到项目名称: ${projectInfo.projectName}`)
      }

      // 提取地址信息（通用）
      if (!projectInfo.address && (
        line.includes('县') || line.includes('市') || line.includes('区') ||
        line.includes('路') || line.includes('街') || line.includes('栋') ||
        line.includes('号') || line.includes('府') || line.includes('城') ||
        line.includes('广场') || line.includes('小区')
      )) {
        // 排除包含其他信息的行
        if (!line.includes('订货') && !line.includes('日期') && !line.includes('单位')) {
          projectInfo.address = line
          console.log(`🏠 找到地址信息: ${projectInfo.address}`)
        }
      }
    }

    // 如果没有找到地址，使用项目名称或公司名称
    if (!projectInfo.address) {
      projectInfo.address = projectInfo.projectName || projectInfo.company || '图片识别项目'
    }

    return projectInfo
  }

  // 🔧 多场景智能识别系统
  const parseMessyLayoutFormat = (content: string): ProjectData[] => {
    console.log('🔍 开始多场景智能识别...')
    console.log('📝 原始内容:', content)

    const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0)

    // 🔧 步骤1: 识别表格场景类型
    const sceneType = detectTableScene(lines)
    console.log(`🎯 识别到场景类型: ${sceneType}`)

    // 🔧 步骤2: 提取项目信息
    const projectInfo = extractProjectInfo(lines)
    console.log(`📋 项目信息:`, projectInfo)

    // 🔧 步骤3: 根据场景类型选择解析策略
    let ventItems: any[] = []

    switch (sceneType) {
      case 'standard-table':
        ventItems = parseStandardTable(lines)
        break
      case 'simple-list':
        ventItems = parseSimpleList(lines)
        break
      case 'mixed-format':
        ventItems = parseMixedFormat(lines)
        break
      case 'continuous-text':
        ventItems = parseContinuousText(lines)
        break
      default:
        ventItems = parseGenericFormat(lines)
    }

    console.log(`🔍 共识别到 ${ventItems.length} 个风口项目`)

    // 🔧 步骤4: 如果没有找到任何数据，创建一个默认项目
    if (ventItems.length === 0) {
      console.log('⚠️ 未找到有效表格数据，创建默认项目')
      return [{
        id: generateUniqueId('project'),
        orderDate: new Date().toISOString().split('T')[0].replace(/-/g, '/'),
        projectAddress: '未识别项目',
        floors: [{
          id: generateUniqueId('floor'),
          floorName: '1楼',
          vents: [{
            id: generateUniqueId('vent'),
            type: 'double_white_outlet',
            length: 0,
            width: 0,
            quantity: 1,
            notes: '请手动输入尺寸信息',
            unitPrice: 0,
            totalPrice: 0
          }],
          totalPrice: 0
        }],
        totalAmount: 0
      }]
    }



    // 🔧 步骤4: 创建项目
    const project: ProjectData = {
      id: generateUniqueId('project'),
      orderDate: projectInfo.orderDate || new Date().toISOString().split('T')[0].replace(/-/g, '/'),
      projectAddress: projectInfo.address || '图片识别项目',
      floors: [{
        id: generateUniqueId('floor'),
        floorName: '1楼',
        vents: ventItems,
        totalPrice: 0
      }],
      totalAmount: 0
    }

    console.log(`✅ 多场景识别完成，共识别 ${ventItems.length} 个风口`)
    return [project]
  }



  // 🔧 解析标准表格格式
  const parseStandardTable = (lines: string[]): any[] => {
    console.log('📊 解析标准表格格式...')
    return extractTableDataRows(lines, {}).map((rowData: any) => parseTableRowByColumns(rowData, 0)).filter(Boolean)
  }

  // 🔧 解析简单列表格式
  const parseSimpleList = (lines: string[]): any[] => {
    console.log('📝 解析简单列表格式...')
    const ventItems: any[] = []

    for (const line of lines) {
      // 匹配格式：1. 地址 尺寸 数量 备注
      const match = line.match(/^[1-9]\d*[.、]\s*(.+)/)
      if (match) {
        const content = match[1]
        const dimensions = parseDimensionString(content)
        if (dimensions) {
          const quantity = parseQuantity(content) || 1
          const notes = content.replace(/\d+\s*[×xX*+\-]\s*\d+/, '').replace(/\d+[个只件]?/, '').trim()

          ventItems.push({
            id: generateUniqueId('vent'),
            type: mapToSystemVentType(notes, dimensions.length, dimensions.width),
            length: dimensions.length,
            width: dimensions.width,
            quantity: quantity,
            color: '象牙白',
            notes: notes,
            originalInfo: `${dimensions.length}×${dimensions.width}`
          })
        }
      }
    }

    return ventItems
  }

  // 🔧 解析混合格式
  const parseMixedFormat = (lines: string[]): any[] => {
    console.log('🔀 解析混合格式...')
    // 结合多种解析方法
    const standardResults = parseStandardTable(lines)
    const listResults = parseSimpleList(lines)
    const genericResults = parseGenericFormat(lines)

    // 返回结果最多的解析方法
    if (standardResults.length >= listResults.length && standardResults.length >= genericResults.length) {
      return standardResults
    } else if (listResults.length >= genericResults.length) {
      return listResults
    } else {
      return genericResults
    }
  }

  // 🔧 解析连续文本格式
  const parseContinuousText = (lines: string[]): any[] => {
    console.log('📄 解析连续文本格式...')
    return parseGenericFormat(lines)
  }

  // 🔧 通用格式解析（原有逻辑）
  const parseGenericFormat = (lines: string[]): any[] => {
    console.log('🔧 使用通用格式解析...')
    const ventItems: any[] = []

    // 查找所有包含尺寸的行
    lines.forEach((line, index) => {
      const dimensions = parseDimensionString(line)
      if (dimensions && dimensions.length > 0 && dimensions.width > 0) {
        // 使用原有的上下文识别逻辑
        const rowData = parseRowByColumnOrder(lines, index, dimensions)
        if (rowData) {
          const ventItem = parseTableRowByColumns(rowData, index)
          if (ventItem) {
            ventItems.push(ventItem)
          }
        }
      }
    })

    return ventItems
  }

  // 🔧 分析表格结构，识别列标题位置
  const analyzeTableStructure = (lines: string[]) => {
    console.log('🔍 分析表格结构...')

    const structure = {
      headerLine: -1,
      columns: {
        序号: -1,
        地址: -1,
        原始信息: -1,
        数量: -1,
        长度: -1,
        宽度: -1,
        颜色: -1,
        单价: -1,
        金额: -1,
        备注: -1
      }
    }

    // 查找包含列标题的行
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      const headerCount = Object.keys(structure.columns).filter(header =>
        line.includes(header) ||
        (header === '原始信息' && line.includes('原始信息/mm')) ||
        (header === '数量' && line.includes('数量')) ||
        (header === '长度' && line.includes('长度/mm')) ||
        (header === '宽度' && line.includes('宽度/mm')) ||
        (header === '金额' && line.includes('金额/元'))
      ).length

      if (headerCount >= 4) { // 至少匹配4个标题
        structure.headerLine = i
        console.log(`📋 找到表格标题行 (第${i + 1}行): ${line}`)
        break
      }
    }

    return structure
  }

  // 🔧 提取表格数据行
  const extractTableDataRows = (lines: string[], structure: any) => {
    console.log('🔍 提取表格数据行...')

    const dataRows: Array<{
      originalInfo: string
      quantity: number
      address: string
      notes: string
      lineIndex: number
    }> = []

    // 查找所有包含尺寸信息的行（这些是数据行的起点）
    lines.forEach((line, index) => {
      const dimensions = parseDimensionString(line)
      if (dimensions && dimensions.length > 0 && dimensions.width > 0) {
        console.log(`📏 第${index + 1}行发现尺寸: ${line} → ${dimensions.length}×${dimensions.width}`)

        // 🔧 按表格列顺序解析数据
        const rowData = parseRowByColumnOrder(lines, index, dimensions)
        if (rowData) {
          dataRows.push(rowData)
        }
      }
    })

    return dataRows
  }

  // 🔧 按列顺序解析单行数据
  const parseRowByColumnOrder = (lines: string[], dimensionLineIndex: number, dimensions: any) => {
    const dimensionLine = lines[dimensionLineIndex]
    console.log(`🔧 解析第${dimensionLineIndex + 1}行的表格数据: ${dimensionLine}`)

    // 🔍 在尺寸行的后续行中按顺序查找：数量 → 长度 → 宽度 → 颜色 → 单价 → 金额 → 备注
    let quantity = 1
    let address = ''
    let notes = ''

    // 查找数量（应该在尺寸行的下一行或附近）
    const searchRange = 3
    for (let i = dimensionLineIndex + 1; i <= Math.min(lines.length - 1, dimensionLineIndex + searchRange); i++) {
      const line = lines[i].trim()

      // 🔍 查找独立的小数字（数量列）
      if (/^([1-9]|1[0-9]|20)$/.test(line)) {
        const num = parseInt(line)
        if (num >= 1 && num <= 20) {
          quantity = num
          console.log(`🔢 找到数量: ${quantity} (第${i + 1}行)`)
          break
        }
      }
    }

    // 查找地址信息（通常在尺寸行的前面）
    for (let i = Math.max(0, dimensionLineIndex - searchRange); i < dimensionLineIndex; i++) {
      const line = lines[i].trim()
      if (line.includes('县') || line.includes('市') || line.includes('区') ||
          line.includes('路') || line.includes('街') || line.includes('栋') ||
          line.includes('号') || line.includes('府') || line.includes('城') ||
          line.includes('广场') || line.includes('小区')) {
        address = line
        console.log(`🏠 找到地址: ${address} (第${i + 1}行)`)
        break
      }
    }

    // 查找备注信息（通常在数据行的最后）
    for (let i = dimensionLineIndex + 1; i <= Math.min(lines.length - 1, dimensionLineIndex + searchRange * 2); i++) {
      const line = lines[i].trim()
      if (line.includes('出风') || line.includes('回风') || line.includes('检修') ||
          line.includes('新风') || line.includes('线形') || line.includes('弧形')) {
        notes = line
        console.log(`📝 找到备注: ${notes} (第${i + 1}行)`)
        break
      }
    }

    return {
      originalInfo: `${dimensions.length}×${dimensions.width}`,
      quantity: quantity,
      address: address,
      notes: notes,
      lineIndex: dimensionLineIndex
    }
  }

  // 🔧 将行数据转换为风口项目
  const parseTableRowByColumns = (rowData: any, index: number) => {
    const dimensions = parseDimensionString(rowData.originalInfo)
    if (!dimensions) return null

    // 🔧 映射到系统风口类型
    const systemType = mapToSystemVentType(rowData.notes || '', dimensions.length, dimensions.width)

    return {
      id: generateUniqueId('vent'),
      type: systemType,
      length: dimensions.length,
      width: dimensions.width,
      quantity: rowData.quantity,
      color: '象牙白',
      notes: rowData.notes || rowData.address || '',
      originalInfo: rowData.originalInfo
    }
  }

  // 🔧 重新设计：智能解析连续文本格式
  const parseContinuousTextFormat = (content: string): ProjectData[] => {
    console.log('🔍 开始智能解析连续文本格式')
    console.log('📝 原始内容:', content)

    const projects: ProjectData[] = []

    // 🔧 步骤1: 智能分割项目（使用多种分隔符）
    const projectSegments = smartSplitProjects(content)
    console.log(`🏗️ 识别到 ${projectSegments.length} 个项目段落`)

    for (let i = 0; i < projectSegments.length; i++) {
      const segment = projectSegments[i].trim()
      console.log(`\n🏠 解析项目 ${i + 1}: "${segment}"`)

      // 🔧 步骤2: 智能提取项目地址
      const { projectAddress, ventContent } = smartExtractProject(segment)
      console.log(`📍 项目地址: "${projectAddress}"`)
      console.log(`💨 风口内容: "${ventContent}"`)

      // 创建项目
      const project: ProjectData = {
        id: generateUniqueId('project'),
        orderDate: new Date().toISOString().split('T')[0].replace(/-/g, '/'),
        projectAddress: projectAddress,
        floors: [],
        totalAmount: 0
      }

      // 创建默认楼层
      const floor: FloorData = {
        id: generateUniqueId('floor'),
        floorName: '1楼',
        vents: [],
        totalPrice: 0
      }

      // 🔧 步骤3: 智能解析风口信息
      const ventItems = intelligentVentParser(ventContent)
      console.log(`💨 识别到 ${ventItems.length} 个风口项`)

      for (const ventItem of ventItems) {
        console.log(`🔍 处理风口项: "${ventItem.original}"`)
        console.log(`  - 类型: ${ventItem.type}`)
        console.log(`  - 尺寸: ${ventItem.length}×${ventItem.width}`)
        console.log(`  - 备注: "${ventItem.notes}"`)

        const vent: VentData = {
          id: generateUniqueId('vent'),
          originalInfo: ventItem.original,
          length: ventItem.length,
          width: ventItem.width,
          quantity: ventItem.quantity,
          color: '象牙白',
          type: ventItem.type as VentItem['type'],
          unitPrice: getDefaultPriceByType(ventItem.type),
          notes: ventItem.notes,
          totalPrice: 0
        }

        // 计算价格
        vent.totalPrice = calculateVentPriceLocal(vent)
        floor.vents.push(vent)
      }

      // 计算楼层总价 - 精确到一位小数
      const floorTotal = floor.vents.reduce((sum, vent) => sum + (vent.totalPrice || 0), 0)
      floor.totalPrice = Math.round(floorTotal * 10) / 10

      if (floor.vents.length > 0) {
        project.floors.push(floor)
        project.totalAmount = Math.round(floor.totalPrice * 10) / 10
        projects.push(project)
        console.log(`✅ 项目 "${projectAddress}" 解析完成，共 ${floor.vents.length} 个风口`)
      }
    }

    console.log(`🎉 智能解析完成，共识别 ${projects.length} 个项目`)
    return projects
  }

  // 🔧 智能分割项目
  const smartSplitProjects = (content: string): string[] => {
    console.log(`🔍 智能分割项目内容: "${content}"`)

    // 🔧 改进：支持更多分割模式
    let segments: string[] = []

    // 方式1：使用分号分割
    if (content.includes('；') || content.includes(';')) {
      segments = content.split(/[；;]/).filter(s => s.trim().length > 0)
      console.log(`📝 使用分号分割，得到 ${segments.length} 个段落`)
    }
    // 方式2：检查是否是单个项目的连续风口数据（如：书香华府1201.出风口1920X140，回风口1000X300）
    else if (/^[\u4e00-\u9fa5]{2,}\d+\./.test(content)) {
      // 检查是否包含多个项目标识
      const projectMatches = content.match(/[\u4e00-\u9fa5]{2,}\d+\./g) || []
      if (projectMatches.length > 1) {
        // 多个项目：按项目名分割
        segments = content.split(/(?=[\u4e00-\u9fa5]{2,}\d+\.)/).filter(s => s.trim().length > 0)
        console.log(`📝 使用项目名分割，得到 ${segments.length} 个段落`)
      } else {
        // 单个项目：整体处理
        segments = [content.trim()]
        console.log(`📝 识别为单个项目的连续风口数据`)
      }
    }
    // 方式3：检查是否包含多个项目地址（不以句号分隔的格式）
    else if (/[\u4e00-\u9fa5]{3,}[A-Z0-9]+/.test(content)) {
      // 查找可能的项目分隔点
      const projectPattern = /[\u4e00-\u9fa5]{3,}[A-Z0-9]+/g
      const matches = [...content.matchAll(projectPattern)]

      if (matches.length > 1) {
        // 按项目名分割
        let lastIndex = 0
        segments = []

        for (let i = 0; i < matches.length; i++) {
          const match = matches[i]
          const startIndex = match.index!

          if (i > 0) {
            // 添加前一个项目的内容
            const prevContent = content.substring(lastIndex, startIndex).trim()
            if (prevContent) {
              segments.push(prevContent)
            }
          }

          lastIndex = startIndex
        }

        // 添加最后一个项目
        const lastContent = content.substring(lastIndex).trim()
        if (lastContent) {
          segments.push(lastContent)
        }

        console.log(`📝 按项目地址分割，得到 ${segments.length} 个段落`)
      } else {
        segments = [content.trim()]
        console.log(`📝 单个项目地址，整体处理`)
      }
    }
    // 方式4：如果没有明确分隔符，当作单个项目处理
    else {
      segments = [content.trim()]
      console.log(`📝 当作单个项目处理`)
    }

    const result = segments.map(s => s.trim()).filter(s => s.length > 0)
    console.log(`🎯 最终分割结果: ${result.length} 个项目段落`)
    result.forEach((seg, i) => console.log(`  项目 ${i+1}: "${seg.substring(0, 50)}${seg.length > 50 ? '...' : ''}"`))

    return result
  }

  // 🔧 智能提取项目地址
  const smartExtractProject = (segment: string): { projectAddress: string, ventContent: string } => {
    console.log(`🔍 智能提取项目地址: "${segment}"`)

    // 🔧 改进：支持更多项目地址模式
    const projectPatterns = [
      // 模式1：项目名+数字+句号，如 "书香华府1201.出风口..."
      /^([^.]*?[\u4e00-\u9fa5]+\d+)\.(.*)$/,
      // 模式2：项目名+号/栋/单元等
      /^([^.]*?[\u4e00-\u9fa5]+\d+[号栋单元室房间][^.]*?)[\.\s]*(.*)$/,
      // 模式3：项目名+区域标识，如 "粤桂苑A"
      /^([\u4e00-\u9fa5]+[A-Z])\s*(.*)$/,
      // 模式4：纯项目名+数字
      /^([^.]*?[\u4e00-\u9fa5]+\d+)\s*(.*)$/,
      // 模式5：以句号分隔的格式
      /^([^.]*?)[\.\s]+(出风口|回风口|进气口)(.*)$/
    ]

    for (let i = 0; i < projectPatterns.length; i++) {
      const pattern = projectPatterns[i]
      const match = segment.match(pattern)
      if (match) {
        const projectAddress = match[1].trim()
        const ventContent = (match[2] + (match[3] || '')).trim()

        console.log(`✅ 使用模式 ${i+1} 匹配成功:`)
        console.log(`  项目地址: "${projectAddress}"`)
        console.log(`  风口内容: "${ventContent}"`)

        // 验证项目地址的合理性
        if (projectAddress.length >= 2 &&
            /[\u4e00-\u9fa5]/.test(projectAddress) &&
            !projectAddress.includes('出风') &&
            !projectAddress.includes('回风') &&
            !projectAddress.match(/^(出风口|回风口|进气口)/)) {
          return { projectAddress, ventContent }
        }
      }
    }

    // 如果没有明确的项目地址，使用默认
    return { projectAddress: '未指定项目', ventContent: segment }
  }

  // 🧠 智能风口识别引擎（全新设计 - 增强楼层识别）
  const intelligentVentParser = (ventContent: string): Array<{
    original: string,
    type: string,
    length: number,
    width: number,
    quantity: number,
    notes: string,
    floor?: string  // 新增楼层信息
  }> => {
    console.log(`🧠 启动智能风口识别引擎: "${ventContent}"`)

    // 🔧 步骤1: 预处理和标准化
    const normalizedContent = preprocessContent(ventContent)
    console.log(`🔧 预处理后: "${normalizedContent}"`)

    // 🔧 步骤2: 智能楼层分析（新增）
    const floorAnalysis = analyzeFloorStructure(normalizedContent)
    console.log(`🏢 楼层分析结果:`, floorAnalysis)

    // 🔧 步骤3: 智能分段（多策略）
    const segments = intelligentSegmentation(normalizedContent)
    console.log(`📝 智能分段结果: ${segments.length} 个段落`)

    // 🔧 步骤4: 多策略解析每个段落（增强楼层识别）
    const vents: Array<{
      original: string,
      type: string,
      length: number,
      width: number,
      quantity: number,
      notes: string,
      floor?: string
    }> = []

    let currentFloor = ''  // 当前楼层上下文

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i]
      console.log(`\n🔍 解析段落 ${i+1}: "${segment}"`)

      // 首先检查是否为楼层信息
      const floorInfo = extractFloorFromSegment(segment, floorAnalysis)
      if (floorInfo) {
        currentFloor = floorInfo
        console.log(`🏢 更新当前楼层: ${currentFloor}`)
        continue
      }

      // 使用多策略解析风口信息
      const ventInfo = multiStrategyParse(segment, i, segments, currentFloor)
      if (ventInfo) {
        vents.push(ventInfo)
        console.log(`✅ 成功解析: ${ventInfo.type} ${ventInfo.length}×${ventInfo.width} 楼层:"${ventInfo.floor || '未指定'}" 备注:"${ventInfo.notes}"`)
      } else {
        console.log(`❌ 所有策略均失败: "${segment}"`)
        // 尝试模糊匹配恢复
        const fuzzyResult = fuzzyRecoveryParse(segment, currentFloor)
        if (fuzzyResult) {
          vents.push(fuzzyResult)
          console.log(`🔄 模糊恢复成功: ${fuzzyResult.type} ${fuzzyResult.length}×${fuzzyResult.width}`)
        }
      }
    }

    // 🔧 步骤5: 智能楼层分组和合并（新增）
    const groupedVents = intelligentFloorGrouping(vents, floorAnalysis)
    console.log(`🎯 智能解析完成，共识别 ${groupedVents.length} 个风口`)
    return groupedVents
  }

  // 🏢 智能楼层结构分析（新增）
  const analyzeFloorStructure = (content: string): {
    hasFloors: boolean,
    floorPattern: 'before_dimension' | 'after_dimension' | 'mixed' | 'none',
    floors: string[],
    floorPositions: Map<string, number[]>
  } => {
    console.log(`🏢 开始楼层结构分析...`)

    const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0)
    const floors: string[] = []
    const floorPositions = new Map<string, number[]>()
    let beforeDimensionCount = 0
    let afterDimensionCount = 0

    // 楼层识别模式（增强版）
    const floorPatterns = [
      /(\d+楼)/g,
      /(\d+层)/g,
      /(\d+F)/gi,
      /(B\d+楼?)/gi,
      /(地下\d+层)/g,
      /(负\d+楼)/g,
      /(一|二|三|四|五|六|七|八|九|十)[楼层]/g
    ]

    lines.forEach((line, lineIndex) => {
      // 检查每种楼层模式
      floorPatterns.forEach(pattern => {
        let match
        while ((match = pattern.exec(line)) !== null) {
          const floor = match[1] || match[0]
          if (!floors.includes(floor)) {
            floors.push(floor)
            floorPositions.set(floor, [])
          }
          floorPositions.get(floor)!.push(lineIndex)

          // 分析楼层位置模式
          const dimensionMatch = line.match(/\d+\s*[×xX*]\s*\d+/)
          if (dimensionMatch) {
            const floorIndex = line.indexOf(floor)
            const dimensionIndex = line.indexOf(dimensionMatch[0])

            if (floorIndex < dimensionIndex) {
              beforeDimensionCount++
              console.log(`🔍 楼层在尺寸前: "${floor}" < "${dimensionMatch[0]}"`)
            } else {
              afterDimensionCount++
              console.log(`🔍 楼层在尺寸后: "${dimensionMatch[0]}" < "${floor}"`)
            }
          }
        }
      })
    })

    // 确定楼层模式
    let floorPattern: 'before_dimension' | 'after_dimension' | 'mixed' | 'none' = 'none'
    if (beforeDimensionCount > 0 || afterDimensionCount > 0) {
      if (beforeDimensionCount > afterDimensionCount * 2) {
        floorPattern = 'before_dimension'
      } else if (afterDimensionCount > beforeDimensionCount * 2) {
        floorPattern = 'after_dimension'
      } else {
        floorPattern = 'mixed'
      }
    }

    const result = {
      hasFloors: floors.length > 0,
      floorPattern,
      floors,
      floorPositions
    }

    console.log(`🏢 楼层分析完成:`, result)
    return result
  }

  // 🔍 从段落中提取楼层信息（新增）
  const extractFloorFromSegment = (segment: string, floorAnalysis: any): string | null => {
    // 如果没有楼层信息，直接返回
    if (!floorAnalysis.hasFloors) return null

    // 楼层识别模式（与分析时保持一致）
    const floorPatterns = [
      /(\d+楼)/,
      /(\d+层)/,
      /(\d+F)/i,
      /(B\d+楼?)/i,
      /(地下\d+层)/,
      /(负\d+楼)/,
      /(一|二|三|四|五|六|七|八|九|十)[楼层]/
    ]

    for (const pattern of floorPatterns) {
      const match = segment.match(pattern)
      if (match) {
        const floor = match[1] || match[0]
        console.log(`🏢 提取楼层信息: "${floor}" 从 "${segment}"`)
        return floor
      }
    }

    return null
  }

  // 🔧 内容预处理和标准化
  const preprocessContent = (content: string): string => {
    let processed = content

    // 标准化各种分隔符
    processed = processed.replace(/[✖️✖]/g, '×')
    processed = processed.replace(/[xX]/g, '×')
    processed = processed.replace(/[*]/g, '×')

    // 标准化空格
    processed = processed.replace(/\s+/g, ' ')

    // 标准化中文标点
    processed = processed.replace(/，/g, ',')
    processed = processed.replace(/。/g, '.')
    processed = processed.replace(/；/g, ';')

    return processed.trim()
  }

  // 🧠 智能分段（多策略）
  const intelligentSegmentation = (content: string): string[] => {
    console.log(`🧠 开始智能分段分析`)

    // 策略1: 基于标点符号的基础分割，但排除数字中的小数点
    let segments = content.split(/[,;、，；]|(?<!\d)\.(?!\d)/).filter(s => s.trim().length > 0)
    console.log(`📝 基础分割: ${segments.length} 个段落`)

    // 策略2: 检测并修复过度分割
    segments = mergeOversplitSegments(segments)
    console.log(`🔧 修复过度分割后: ${segments.length} 个段落`)

    // 策略3: 检测并分割欠分割的段落
    segments = splitUndersplitSegments(segments)
    console.log(`🔧 修复欠分割后: ${segments.length} 个段落`)

    return segments.map(s => s.trim()).filter(s => s.length > 0)
  }

  // 🔧 修复过度分割（合并应该在一起的段落）
  const mergeOversplitSegments = (segments: string[]): string[] => {
    const merged: string[] = []
    let i = 0

    while (i < segments.length) {
      let current = segments[i].trim()

      // 检查当前段落是否不完整（只有类型没有尺寸，或只有尺寸没有类型）
      const hasType = /出风|回风|进气|检修|维修/.test(current)
      const hasDimension = /\d+\s*×\s*\d+/.test(current)

      if (hasType && !hasDimension && i + 1 < segments.length) {
        // 当前有类型无尺寸，检查下一个段落
        const next = segments[i + 1].trim()
        const nextHasDimension = /\d+\s*×\s*\d+/.test(next)

        if (nextHasDimension) {
          current = `${current} ${next}`
          i++ // 跳过下一个段落
          console.log(`🔗 合并段落: "${segments[i-1]}" + "${segments[i]}" → "${current}"`)
        }
      }

      merged.push(current)
      i++
    }

    return merged
  }

  // 🔧 分割欠分割的段落（一个段落包含多个风口）
  const splitUndersplitSegments = (segments: string[]): string[] => {
    const split: string[] = []

    for (const segment of segments) {
      // 检测段落中是否包含多个风口模式
      const ventPatterns = segment.match(/(?:出风|回风|进气|检修|维修)[^出回进检维]*?\d+\s*×\s*\d+[^出回进检维]*/g)

      if (ventPatterns && ventPatterns.length > 1) {
        console.log(`✂️ 分割多风口段落: "${segment}" → ${ventPatterns.length} 个风口`)
        split.push(...ventPatterns)
      } else {
        split.push(segment)
      }
    }

    return split
  }

  // 🎯 多策略解析（使用统一核心解析引擎 + 智能备注提取）
  const multiStrategyParse = (segment: string, index: number, allSegments: string[], currentFloor: string = ''): {
    original: string,
    type: string,
    length: number,
    width: number,
    quantity: number,
    notes: string,
    floor?: string
  } | null => {
    console.log(`🎯 启动智能多策略解析: "${segment}" (当前楼层: ${currentFloor})`)

    // 🎯 使用统一核心解析引擎
    // 1. 解析尺寸
    const dimensions = unifiedVentParser.parseDimensions(segment)
    if (!dimensions) {
      console.log(`    ❌ 无法解析尺寸`)
      return null
    }

    // 2. 解析数量
    const quantity = unifiedVentParser.parseQuantity(segment)

    // 3. 🔧 改进的备注提取：优先保留原始有意义信息
    let notes = ''

    // 3.1 优先提取地点/房间信息
    const locationInfo = unifiedVentParser.extractLocationInfo(segment)
    if (locationInfo) {
      notes = locationInfo
      console.log(`    📍 提取地点信息: "${locationInfo}"`)
    } else {
      // 3.2 🔧 新策略：从原始文本中智能提取有意义部分
      console.log(`    🔍 智能分析原始文本: "${segment}"`)

      // 提取房间编号（字母+数字格式）
      const roomMatch = segment.match(/([A-Z]\d{2,4})\b/g)
      if (roomMatch) {
        notes = roomMatch[0]
        console.log(`    🏠 提取房间编号: "${notes}"`)
      } else {
        // 提取房间类型（包含数字后缀）
        const roomTypeMatch = segment.match(/(小房间\d*|小房\d*|房间\d*|餐厅|歺厅|客厅|大厅|主厅|卧室|主卧|次卧|书房|厨房|卫生间|洗手间|阳台|走廊|过道|茶室|棋牌室|KTV|活动室|办公室|会议室)/g)
        if (roomTypeMatch) {
          notes = roomTypeMatch[0]
          console.log(`    🏠 提取房间类型: "${notes}"`)
        } else {
          // 提取颜色信息
          const colorMatch = segment.match(/(白色|黑色|红色|蓝色|绿色|黄色|灰色|银色|金色)/g)
          if (colorMatch) {
            notes = colorMatch[0]
            console.log(`    🎨 提取颜色信息: "${notes}"`)
          } else {
            // 提取技术规格
            const techMatch = segment.match(/(普通下出|侧出|上出|下出|四面出风|双向出风|留圆接\d*软管|留圆接\d*|软管连接|硬管连接|法兰连接|铝合金|不锈钢|塑料|ABS|防火|防水|静音|可调|固定)/g)
            if (techMatch) {
              notes = techMatch.join(' ')
              console.log(`    🔧 提取技术规格: "${notes}"`)
            } else {
              console.log(`    ❌ 未找到有意义的备注信息`)
            }
          }
        }
      }
    }

    // 3.3 最终清理（轻量级）
    if (notes) {
      notes = cleanNotes(notes)
      console.log(`    ✨ 最终备注: "${notes}"`)
    }

    // 4. 判断风口类型
    const ventType = unifiedVentParser.determineVentType(segment, dimensions.length, dimensions.width)

    // 5. 映射到系统类型
    const systemType = mapToSystemVentType(ventType, dimensions.length, dimensions.width)

    console.log(`    ✅ 智能解析结果: ${systemType} ${dimensions.length}×${dimensions.width} ×${quantity} 楼层:"${currentFloor}" 备注:"${notes}"`)

    return {
      original: segment,
      type: systemType,
      length: dimensions.length,
      width: dimensions.width,
      quantity,
      notes,
      floor: currentFloor
    }
  }

  // 📋 策略1: 标准格式解析（出风口1920×140）- 增强楼层支持
  const standardFormatParse = (segment: string, currentFloor: string = ''): any => {
    const patterns = [
      // 完整格式：类型+尺寸+数量+备注
      /^(.*?)(出风口|回风口|进气口|检修口|维修口|出风|回风|进气|检修|维修)\s*(\d+)\s*×\s*(\d+)\s*(?:×\s*(\d+)\s*个?)?\s*(.*)$/,
      // 简化格式：类型+尺寸
      /^(.*?)(出风口|回风口|进气口|检修口|维修口|出风|回风|进气|检修|维修)\s*(\d+)\s*×\s*(\d+)\s*(.*)$/,
    ]

    for (const pattern of patterns) {
      const match = segment.match(pattern)
      if (match) {
        const [, prefix, type, length, width, quantity, suffix] = match

        // 提取楼层信息（从前缀或后缀中）
        const floorFromSegment = extractFloorFromText(`${prefix || ''} ${suffix || ''}`)
        const finalFloor = floorFromSegment || currentFloor

        return {
          original: segment,
          type: mapVentType(type, parseInt(width)),
          length: Math.max(parseInt(length), parseInt(width)),
          width: Math.min(parseInt(length), parseInt(width)),
          quantity: quantity ? parseInt(quantity) : 1,
          notes: cleanNotes(`${prefix} ${suffix}`.trim()),
          floor: finalFloor
        }
      }
    }

    return null
  }

  // 🔍 从文本中提取楼层信息的辅助函数
  const extractFloorFromText = (text: string): string | null => {
    const floorPatterns = [
      /(\d+楼)/,
      /(\d+层)/,
      /(\d+F)/i,
      /(B\d+楼?)/i,
      /(地下\d+层)/,
      /(负\d+楼)/,
      /(一|二|三|四|五|六|七|八|九|十)[楼层]/
    ]

    for (const pattern of floorPatterns) {
      const match = text.match(pattern)
      if (match) {
        return match[1] || match[0]
      }
    }

    return null
  }

  // 🔄 策略2: 反向格式解析（1920×140出风口）- 增强楼层支持
  const reverseFormatParse = (segment: string, currentFloor: string = ''): any => {
    const patterns = [
      // 反向格式：前缀+尺寸+类型+后缀
      /^(.*?)\s*(\d+)\s*×\s*(\d+)\s*(?:×\s*(\d+)\s*个?)?\s*(出风口|回风口|进气口|检修口|维修口|出风|回风|进气|检修|维修)\s*(.*)$/,
    ]

    for (const pattern of patterns) {
      const match = segment.match(pattern)
      if (match) {
        const [, prefix, length, width, quantity, type, suffix] = match

        // 提取楼层信息
        const floorFromSegment = extractFloorFromText(`${prefix || ''} ${suffix || ''}`)
        const finalFloor = floorFromSegment || currentFloor

        return {
          original: segment,
          type: mapVentType(type, parseInt(width)),
          length: Math.max(parseInt(length), parseInt(width)),
          width: Math.min(parseInt(length), parseInt(width)),
          quantity: quantity ? parseInt(quantity) : 1,
          notes: cleanNotes(`${prefix} ${suffix}`.trim()),
          floor: finalFloor
        }
      }
    }

    return null
  }

  // 🧠 策略3: 上下文推断解析 - 增强楼层支持
  const contextInferenceParse = (segment: string, index: number, allSegments: string[], currentFloor: string = ''): any => {
    // 如果当前段落只有尺寸，尝试从上下文推断类型
    const dimensionMatch = segment.match(/(\d+)\s*×\s*(\d+)/)
    if (dimensionMatch && !/出风|回风|进气|检修|维修/.test(segment)) {
      const length = parseInt(dimensionMatch[1])
      const width = parseInt(dimensionMatch[2])

      // 从前后段落推断类型
      let inferredType = ''

      // 检查前一个段落
      if (index > 0) {
        const prevSegment = allSegments[index - 1]
        const prevTypeMatch = prevSegment.match(/(出风口|回风口|进气口|检修口|维修口|出风|回风|进气|检修|维修)/)
        if (prevTypeMatch) {
          inferredType = prevTypeMatch[1]
          console.log(`🔍 从前一段落推断类型: ${inferredType}`)
        }
      }

      // 如果没有推断出类型，根据尺寸判断
      if (!inferredType) {
        inferredType = width > 280 ? '回风口' : '出风口'
        console.log(`🔍 根据尺寸推断类型: ${inferredType} (宽度: ${width}mm)`)
      }

      // 提取楼层信息
      const floorFromSegment = extractFloorFromText(segment)
      const finalFloor = floorFromSegment || currentFloor

      return {
        original: segment,
        type: mapVentType(inferredType, width),
        length: Math.max(length, width),
        width: Math.min(length, width),
        quantity: 1,
        notes: cleanNotes(segment.replace(/\d+\s*×\s*\d+/, '').trim()),
        floor: finalFloor
      }
    }

    return null
  }

  // 🔍 策略4: 部分匹配解析 - 增强楼层支持
  const partialMatchParse = (segment: string, currentFloor: string = ''): any => {
    // 尝试提取任何可能的尺寸信息
    const dimensionMatch = segment.match(/(\d+)\s*[×xX*]\s*(\d+)/)
    if (dimensionMatch) {
      const length = parseInt(dimensionMatch[1])
      const width = parseInt(dimensionMatch[2])

      // 尝试提取类型信息
      let type = '出风口' // 默认
      const typeMatch = segment.match(/(出风|回风|进气|检修|维修)/)
      if (typeMatch) {
        type = typeMatch[1].includes('回风') || typeMatch[1].includes('进气') ? '回风口' :
               typeMatch[1].includes('检修') || typeMatch[1].includes('维修') ? '检修口' : '出风口'
      } else if (width > 280) {
        type = '回风口'
      }

      // 提取楼层信息
      const floorFromSegment = extractFloorFromText(segment)
      const finalFloor = floorFromSegment || currentFloor

      return {
        original: segment,
        type: mapVentType(type, width),
        length: Math.max(length, width),
        width: Math.min(length, width),
        quantity: 1,
        notes: cleanNotes(segment.replace(/\d+\s*[×xX*]\s*\d+/, '').trim()),
        floor: finalFloor
      }
    }

    return null
  }

  // 🏢 策略5: 楼层后置解析（新增 - 专门处理楼层在尺寸后面的情况）
  const floorPostfixParse = (segment: string, currentFloor: string = ''): any => {
    console.log(`🏢 尝试楼层后置解析: "${segment}"`)

    // 模式1: 风口类型 + 尺寸 + 楼层 (如: 双层弯叶风口2650×150mm 1楼)
    const pattern1 = /^(.*?)(双层.*?风口|.*?风口|出风|回风|进气|检修|维修)\s*(\d+)\s*[×xX*]\s*(\d+)\s*mm?\s*(\d+楼|\d+层|\d+F|B\d+楼?|地下\d+层|负\d+楼|[一二三四五六七八九十][楼层])\s*(.*)$/i
    let match = segment.match(pattern1)
    if (match) {
      const [, prefix, type, length, width, floor, suffix] = match
      console.log(`✅ 楼层后置模式1匹配: 类型="${type}" 尺寸="${length}×${width}" 楼层="${floor}"`)

      return {
        original: segment,
        type: mapVentType(type, parseInt(width)),
        length: Math.max(parseInt(length), parseInt(width)),
        width: Math.min(parseInt(length), parseInt(width)),
        quantity: 1,
        notes: cleanNotes(`${prefix || ''} ${suffix || ''}`.trim()),
        floor: floor
      }
    }

    // 模式2: 尺寸 + 楼层 (如: 2650×150mm 1楼)
    const pattern2 = /^(\d+)\s*[×xX*]\s*(\d+)\s*mm?\s*(\d+楼|\d+层|\d+F|B\d+楼?|地下\d+层|负\d+楼|[一二三四五六七八九十][楼层])\s*(.*)$/i
    match = segment.match(pattern2)
    if (match) {
      const [, length, width, floor, suffix] = match
      console.log(`✅ 楼层后置模式2匹配: 尺寸="${length}×${width}" 楼层="${floor}"`)

      return {
        original: segment,
        type: parseInt(width) > 280 ? 'white_return' : 'double_white_outlet',
        length: Math.max(parseInt(length), parseInt(width)),
        width: Math.min(parseInt(length), parseInt(width)),
        quantity: 1,
        notes: cleanNotes(suffix || ''),
        floor: floor
      }
    }

    // 模式3: 复杂格式 - 包含价格和数量的情况 (如: 双层弯叶风口2650×150mm 1 ￥130.00 1楼)
    const pattern3 = /^(.*?)(双层.*?风口|.*?风口|出风|回风)\s*(\d+)\s*[×xX*]\s*(\d+)\s*mm?\s*(\d+)\s*￥?[\d.]+\s*(\d+楼|\d+层|\d+F|B\d+楼?|地下\d+层|负\d+楼|[一二三四五六七八九十][楼层])\s*(.*)$/i
    match = segment.match(pattern3)
    if (match) {
      const [, prefix, type, length, width, quantity, floor, suffix] = match
      console.log(`✅ 楼层后置模式3匹配: 类型="${type}" 尺寸="${length}×${width}" 数量="${quantity}" 楼层="${floor}"`)

      return {
        original: segment,
        type: mapVentType(type, parseInt(width)),
        length: Math.max(parseInt(length), parseInt(width)),
        width: Math.min(parseInt(length), parseInt(width)),
        quantity: parseInt(quantity) || 1,
        notes: cleanNotes(`${prefix || ''} ${suffix || ''}`.trim()),
        floor: floor
      }
    }

    console.log(`❌ 楼层后置解析失败`)
    return null
  }

  // 🔄 模糊恢复解析（最后的尝试）- 增强楼层支持
  const fuzzyRecoveryParse = (segment: string, currentFloor: string = ''): any => {
    console.log(`🔄 尝试模糊恢复: "${segment}"`)

    // 尝试提取任何数字作为尺寸
    const numbers = segment.match(/\d+/g)
    if (numbers && numbers.length >= 2) {
      const length = parseInt(numbers[0])
      const width = parseInt(numbers[1])

      if (length > 50 && width > 50 && length < 5000 && width < 1000) {
        console.log(`🔄 模糊恢复找到可能的尺寸: ${length}×${width}`)

        // 提取楼层信息
        const floorFromSegment = extractFloorFromText(segment)
        const finalFloor = floorFromSegment || currentFloor

        return {
          original: segment,
          type: width > 280 ? 'white_return' : 'double_white_outlet',
          length: Math.max(length, width),
          width: Math.min(length, width),
          quantity: 1,
          notes: cleanNotes(segment.replace(/\d+/g, '').trim()),
          floor: finalFloor
        }
      }
    }

    return null
  }

  // 🗺️ 风口类型映射
  const mapVentType = (typeStr: string, width: number): string => {
    // 使用现有的 determineVentType 函数
    return determineVentType(typeStr, '', width, '')
  }

  // 🔧 映射到系统风口类型（增强版 - 支持检修口智能识别）
  const mapToSystemVentType = (ventType: string, length: number, width: number): string => {
    console.log(`🔍 风口类型判断: 备注="${ventType}" 长度=${length} 宽度=${width}`)

    // 🔧 规则1: 长宽相等且≥300mm的默认为检修口
    if (length === width && length >= 300) {
      console.log(`✅ 检修口识别: 长宽相等(${length}×${width})且≥300mm`)
      return 'maintenance'  // 检修口
    }

    // 🔧 规则2: 备注中明确包含检修关键词
    if (ventType.includes('检修') || ventType.includes('维修')) {
      console.log(`✅ 检修口识别: 备注包含检修关键词`)
      return 'maintenance'   // 检修口
    }

    // 🔧 规则3: 备注中明确包含回风关键词
    if (ventType.includes('回风') || ventType.includes('进气') ||
        ventType.includes('进风') || ventType.includes('吸风') ||
        ventType.includes('排风') || ventType.includes('抽风')) {
      console.log(`✅ 回风口识别: 备注包含回风关键词`)
      return 'white_return'  // 白色回风口
    }

    // 🔧 规则4: 根据宽度判断出风口/回风口
    if (width >= 255) {
      console.log(`✅ 回风口识别: 宽度${width}mm ≥ 255mm`)
      return 'white_return'  // 宽度大的当回风口
    } else {
      console.log(`✅ 出风口识别: 宽度${width}mm < 255mm`)
      return 'double_white_outlet'  // 双层白色出风口
    }
  }

  // 🏢 智能楼层分组和合并（新增）
  const intelligentFloorGrouping = (vents: any[], floorAnalysis: any): any[] => {
    console.log(`🏢 开始智能楼层分组，输入 ${vents.length} 个风口`)

    if (!floorAnalysis.hasFloors) {
      console.log(`🏢 无楼层信息，直接返回`)
      return vents
    }

    // 按楼层分组
    const floorGroups = new Map<string, any[]>()
    const unassignedVents: any[] = []

    vents.forEach(vent => {
      if (vent.floor) {
        if (!floorGroups.has(vent.floor)) {
          floorGroups.set(vent.floor, [])
        }
        floorGroups.get(vent.floor)!.push(vent)
      } else {
        unassignedVents.push(vent)
      }
    })

    console.log(`🏢 楼层分组结果:`)
    floorGroups.forEach((vents, floor) => {
      console.log(`  - ${floor}: ${vents.length} 个风口`)
    })
    console.log(`  - 未分配楼层: ${unassignedVents.length} 个风口`)

    // 智能合并相同楼层的相同尺寸风口
    const mergedVents: any[] = []

    floorGroups.forEach((floorVents, floor) => {
      const dimensionGroups = new Map<string, any[]>()

      // 按尺寸和类型分组
      floorVents.forEach(vent => {
        const key = `${vent.type}_${vent.length}x${vent.width}`
        if (!dimensionGroups.has(key)) {
          dimensionGroups.set(key, [])
        }
        dimensionGroups.get(key)!.push(vent)
      })

      // 合并相同尺寸的风口
      dimensionGroups.forEach((sameVents, key) => {
        if (sameVents.length === 1) {
          mergedVents.push(sameVents[0])
        } else {
          // 合并多个相同风口
          const totalQuantity = sameVents.reduce((sum, vent) => sum + vent.quantity, 0)
          const mergedNotes = sameVents
            .map(vent => vent.notes)
            .filter(note => note && note.trim())
            .join(' ')

          const mergedVent = {
            ...sameVents[0],
            quantity: totalQuantity,
            notes: cleanNotes(mergedNotes),
            original: `${sameVents[0].type} ${sameVents[0].length}×${sameVents[0].width} ${totalQuantity}个 ${floor}`
          }

          mergedVents.push(mergedVent)
          console.log(`🔄 合并风口: ${key} ${floor} - ${sameVents.length}个 → 1个(数量:${totalQuantity})`)
        }
      })
    })

    // 处理未分配楼层的风口
    unassignedVents.forEach(vent => {
      mergedVents.push(vent)
    })

    console.log(`🏢 楼层分组完成，输出 ${mergedVents.length} 个风口`)
    return mergedVents
  }

  // 智能楼层提取函数（增强版 - 参考智能粘贴识别逻辑）
  const extractFloorFromProject = (projectInfo: any): string => {
    const projectName = projectInfo.projectName || ''
    const clientInfo = projectInfo.clientInfo || ''
    const fullText = `${projectName} ${clientInfo}`.trim()

    console.log(`🏢 智能楼层提取: "${fullText}"`)

    // 1. 优先识别明确的楼层标识
    const explicitFloorMatch = fullText.match(/(\d+)楼|(\d+)层|(\d+)F/i)
    if (explicitFloorMatch) {
      const floor = explicitFloorMatch[1] || explicitFloorMatch[2] || explicitFloorMatch[3]
      console.log(`✅ 明确楼层标识: "${floor}楼"`)
      return `${floor}楼`
    }

    // 2. 🔧 增强：支持"书香华府8-501"格式 → 5楼
    const buildingRoomMatch = fullText.match(/(\d+)-(\d{3,4})/)
    if (buildingRoomMatch) {
      const roomNumber = buildingRoomMatch[2]
      let floor = ''
      if (roomNumber.length === 4) {
        // 4位房号：前2位是楼层（如501 → 5楼，2101 → 21楼）
        floor = roomNumber.substring(0, 2)
        // 去掉前导0（如05 → 5）
        floor = parseInt(floor).toString()
      } else if (roomNumber.length === 3) {
        // 3位房号：前1位是楼层（如501 → 5楼）
        floor = roomNumber.substring(0, 1)
      }
      if (floor && parseInt(floor) >= 1 && parseInt(floor) <= 99) {
        console.log(`✅ 从"栋-房号"格式提取楼层: "${buildingRoomMatch[0]}" → "${floor}楼"`)
        return `${floor}楼`
      }
    }

    // 3. 🔧 增强：支持"9-2101"格式 → 21楼
    const simpleRoomMatch = fullText.match(/(\d+)-(\d{4})/)
    if (simpleRoomMatch) {
      const roomNumber = simpleRoomMatch[2]
      const floor = parseInt(roomNumber.substring(0, 2)).toString()
      if (parseInt(floor) >= 1 && parseInt(floor) <= 99) {
        console.log(`✅ 从"数字-房号"格式提取楼层: "${simpleRoomMatch[0]}" → "${floor}楼"`)
        return `${floor}楼`
      }
    }

    // 4. 🔧 增强：支持"3-1802"格式 → 18楼
    const shortBuildingMatch = fullText.match(/(\d)-(\d{4})/)
    if (shortBuildingMatch) {
      const roomNumber = shortBuildingMatch[2]
      const floor = parseInt(roomNumber.substring(0, 2)).toString()
      if (parseInt(floor) >= 1 && parseInt(floor) <= 99) {
        console.log(`✅ 从"单数字-房号"格式提取楼层: "${shortBuildingMatch[0]}" → "${floor}楼"`)
        return `${floor}楼`
      }
    }

    // 5. 从房号中提取楼层（支持多种格式）
    // 格式: "1901号房" → "19楼"
    const roomNumberMatch = fullText.match(/(\d{3,4})号?房/)
    if (roomNumberMatch) {
      const roomNumber = roomNumberMatch[1]
      if (roomNumber.length === 4) {
        const floor = parseInt(roomNumber.substring(0, 2)).toString()
        console.log(`✅ 从4位房号提取楼层: "${roomNumber}" → "${floor}楼"`)
        return `${floor}楼`
      } else if (roomNumber.length === 3) {
        const floor = roomNumber.substring(0, 1)
        console.log(`✅ 从3位房号提取楼层: "${roomNumber}" → "${floor}楼"`)
        return `${floor}楼`
      }
    }

    // 6. 格式: "上淮府3-2702" → "27楼"
    const dashRoomMatch = fullText.match(/[-\s](\d{4})/)
    if (dashRoomMatch) {
      const roomNumber = dashRoomMatch[1]
      const floor = parseInt(roomNumber.substring(0, 2)).toString()
      console.log(`✅ 从连字符房号提取楼层: "${roomNumber}" → "${floor}楼"`)
      return `${floor}楼`
    }

    // 7. 🔧 增强：任何4位数字 → 前2位作为楼层
    const anyFourDigitMatch = fullText.match(/(\d{4})/)
    if (anyFourDigitMatch) {
      const roomNumber = anyFourDigitMatch[1]
      const floor = parseInt(roomNumber.substring(0, 2))
      if (floor >= 1 && floor <= 99) {
        console.log(`✅ 从4位数字提取楼层: "${roomNumber}" → "${floor}楼"`)
        return `${floor}楼`
      }
    }

    // 8. 🔧 增强：从项目+房号格式提取（如"金色时代10栋1901" → "19楼"）
    const projectRoomMatch = fullText.match(/(.+?)(\d{3,4})(?:号房?)?$/)
    if (projectRoomMatch) {
      const roomNumber = projectRoomMatch[2]
      if (roomNumber.length === 4) {
        const floor = parseInt(roomNumber.substring(0, 2)).toString()
        console.log(`✅ 从项目房号提取楼层: "${roomNumber}" → "${floor}楼"`)
        return `${floor}楼`
      } else if (roomNumber.length === 3) {
        const floor = roomNumber.substring(0, 1)
        console.log(`✅ 从项目房号提取楼层: "${roomNumber}" → "${floor}楼"`)
        return `${floor}楼`
      }
    }

    // 9. 🔧 增强：支持中文数字楼层
    const chineseFloorMatch = fullText.match(/(一|二|三|四|五|六|七|八|九|十)[楼层]/)
    if (chineseFloorMatch) {
      const chineseNum = chineseFloorMatch[1]
      const floorMap: { [key: string]: string } = {
        '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
        '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
      }
      const floor = floorMap[chineseNum] || '1'
      console.log(`✅ 从中文数字提取楼层: "${chineseFloorMatch[0]}" → "${floor}楼"`)
      return `${floor}楼`
    }

    // 10. 默认返回1楼
    console.log(`⚠️ 无法提取楼层，使用默认值: "1楼"`)
    return '1楼'
  }

  // 智能风口类型识别函数
  const identifyVentType = (vent: any): string => {
    const originalType = vent.originalType || vent.type || ''
    const systemType = vent.systemType || ''
    const length = vent.dimensions?.length || 0
    const width = vent.dimensions?.width || 0

    console.log(`🔍 风口类型识别: 原始="${originalType}", 系统="${systemType}", 尺寸=${length}×${width}`)

    // 🧠 智能类型判断：只识别出风口和回风口

    // 1. 检查关键词指示
    const hasReturnKeywords = /回风|进风|进气|吸风|排风|抽风/.test(originalType)
    const hasOutletKeywords = /出风|送风|供风/.test(originalType)

    // 2. 智能判断逻辑
    if (hasReturnKeywords) {
      console.log(`🎯 关键词识别为回风口: "${originalType}"`)
      return 'white_return'
    }

    if (hasOutletKeywords) {
      console.log(`🎯 关键词识别为出风口: "${originalType}"`)
      return 'double_white_outlet'
    }

    // 3. 根据尺寸判断（宽度≥255mm为回风口）
    if (width >= 255) {
      console.log(`🎯 尺寸识别为回风口: 宽度${width}mm≥255mm`)
      return 'white_return'
    } else {
      console.log(`🎯 尺寸识别为出风口: 宽度${width}mm<255mm`)
      return 'double_white_outlet'
    }

    // 4. 验证AI识别的类型（只允许出风和回风）
    if (systemType && ['double_white_outlet', 'white_return'].includes(systemType)) {
      console.log(`✅ AI识别类型验证通过: ${systemType}`)
      return systemType
    }

    const text = originalType.toLowerCase()

    // 1. 检修口识别（最高优先级）
    if (text.includes('检修') || text.includes('维修') || (length === width && length >= 300)) {
      console.log(`✅ 检修口识别`)
      return 'maintenance'
    }

    // 2. 颜色判断
    const isBlack = text.includes('黑') || text.includes('黑色')

    // 3. 线型风口识别（仅基于关键字）
    if (text.includes('线型') || text.includes('线形') || text.includes('线条') || text.includes('条形') || text.includes('条型')) {
      if (text.includes('回风') || text.includes('进气') || text.includes('进风') ||
          text.includes('吸风') || text.includes('排风') || text.includes('抽风') || width >= 255) {
        console.log(`✅ 线型回风口识别: ${isBlack ? '黑色' : '白色'}`)
        return isBlack ? 'black_linear_return' : 'white_linear_return'
      } else {
        console.log(`✅ 线型出风口识别: ${isBlack ? '黑色' : '白色'}`)
        return isBlack ? 'black_linear' : 'white_linear'
      }
    }

    // 4. 回风口识别
    if (text.includes('回风') || text.includes('进气') || text.includes('进风') ||
        text.includes('吸风') || text.includes('排风') || text.includes('抽风') || width >= 255) {
      console.log(`✅ 回风口识别: ${isBlack ? '黑色' : '白色'}`)
      return isBlack ? 'black_return' : 'white_return'
    }

    // 5. 默认出风口
    console.log(`✅ 出风口识别: ${isBlack ? '黑色' : '白色'}`)
    return isBlack ? 'double_black_outlet' : 'double_white_outlet'
  }

  // 处理AI识别结果
  const handleAIRecognitionResult = (result: any) => {
    console.log('🤖 AI识别结果:', result)

    try {
      if (result.projects && result.projects.length > 0) {
        // 转换AI结果为系统格式
        const convertedProjects = result.projects.map((project: any) => {
          console.log(`🔧 [AI转换] 处理项目: ${project.projectName}, 楼层数: ${project.floors?.length || 0}`)

          const projectData = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            projectAddress: project.projectName || '未知项目',
            clientInfo: project.clientInfo || '',
            floors: project.floors.map((floor: any) => {
              // 🔧 修复：直接使用AI识别的楼层名称，不要重新提取
              let floorName = floor.floorName || '1楼'

              // 如果楼层名称是数字，转换为标准格式
              if (typeof floorName === 'number') {
                floorName = `${floorName}楼`
              } else if (typeof floorName === 'string' && /^\d+$/.test(floorName)) {
                floorName = `${floorName}楼`
              }

              console.log(`🔧 [AI转换] 楼层处理: 原始="${floor.floorName}" → 最终="${floorName}"`)

              return {
                floorName: floorName, // 🔧 修复：使用AI识别的楼层名称
                rooms: floor.rooms.map((room: any) => ({
                roomName: room.roomName || '',
                vents: room.vents
                  .filter((vent: any) => {
                    // 过滤无效风口：尺寸为0或没有有效尺寸的
                    const length = vent.dimensions?.length || 0
                    const width = vent.dimensions?.width || 0
                    const hasValidDimensions = length > 0 && width > 0

                    if (!hasValidDimensions) {
                      console.log(`🚫 过滤无效风口: 尺寸=${length}×${width}, 原始="${vent.originalType}"`)
                      return false
                    }

                    return true
                  })
                  .map((vent: any) => {
                  const systemType = identifyVentType(vent)

                  // 🔧 改进备注信息构建：过滤无意义的房间名
                  let notesArray = []

                  // 只有当房间名有意义时才添加到备注
                  if (room.roomName &&
                      room.roomName !== '未知房间' &&
                      room.roomName !== '房间' &&
                      room.roomName !== '默认房间' &&
                      room.roomName.trim().length > 0) {
                    notesArray.push(room.roomName)
                    console.log(`🏠 添加有意义的房间名到备注: "${room.roomName}"`)
                  }

                  // 🔧 改进备注提取：从原始类型中提取更多信息
                  const extractAdditionalNotes = (originalType: string, existingNotes: string): string[] => {
                    const notes = []
                    const text = originalType || ''

                    // 提取特殊规格
                    const specialSpecs = text.match(/(孤形|弧形|圆形|方形|普通下出|留圆接|软管|检修|维修|单边|双边|大叶片|小叶片)/g)
                    if (specialSpecs) {
                      // 去重：避免与已有备注重复
                      const uniqueSpecs = specialSpecs.filter(spec => !existingNotes.includes(spec))
                      notes.push(...uniqueSpecs)
                    }

                    // 提取颜色信息
                    const colorMatch = text.match(/(黑色|白色|银色|灰色|双拼色)/g)
                    if (colorMatch) {
                      const uniqueColors = colorMatch.filter(color => !existingNotes.includes(color))
                      notes.push(...uniqueColors)
                    }

                    // 提取材质信息
                    const materialMatch = text.match(/(铝合金|不锈钢|塑料|ABS)/g)
                    if (materialMatch) {
                      const uniqueMaterials = materialMatch.filter(material => !existingNotes.includes(material))
                      notes.push(...uniqueMaterials)
                    }

                    return notes
                  }

                  // 添加原始备注（如果有的话）
                  if (vent.notes && vent.notes.trim() && vent.notes.trim() !== '房间') {
                    notesArray.push(vent.notes.trim())
                    console.log(`📝 添加原始备注: "${vent.notes.trim()}"`)
                  }

                  // 从原始类型中提取额外备注（避免重复）
                  const existingNotesText = notesArray.join('，')
                  const additionalNotes = extractAdditionalNotes(vent.originalType || '', existingNotesText)
                  if (additionalNotes.length > 0) {
                    notesArray.push(...additionalNotes)
                    console.log(`🔍 从原始类型提取备注: "${additionalNotes.join('，')}"`)
                  }

                  const combinedNotes = notesArray.join('，')
                  console.log(`📋 最终备注: "${combinedNotes}"`)

                  // 🔧 获取工厂设置的默认单价
                  const defaultPrice = getDefaultPriceByType(systemType)
                  console.log(`🔍 获取默认单价: ${systemType} -> ${defaultPrice}`, defaultUnitPrices)

                  const ventData = {
                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                    type: systemType,
                    length: vent.dimensions?.length || 0,
                    width: vent.dimensions?.width || 0,
                    quantity: vent.quantity || 1,
                    color: vent.color || '',
                    notes: combinedNotes,
                    unitPrice: defaultPrice, // 🔧 使用工厂设置的单价
                    // 保留原始尺寸字符串用于显示
                    dimensions: `${vent.dimensions?.length || 0}×${vent.dimensions?.width || 0}`
                  }

                  console.log(`🔧 [AI识别] 风口数据构建完成:`, ventData)
                  return ventData;
                })
              }))
            }),
            totalAmount: 0
          };
          return projectData;
        })

        console.log('🔄 转换后的项目数据:', convertedProjects)

        // 设置预览数据并打开编辑器
        previewActions.initializePreview('multi_project', { projects: convertedProjects })
        setShowAIRecognitionDialog(false)

        console.log(`🎉 AI识别成功！识别到 ${convertedProjects.length} 个项目`)
        alert(`🎉 AI识别成功！\n识别到 ${convertedProjects.length} 个项目\n请在预览编辑器中确认数据后创建订单`)
      } else {
        console.warn('⚠️ AI识别结果为空')
        alert('AI识别未找到有效的项目数据，请检查输入内容格式')
      }
    } catch (error) {
      console.error('❌ AI识别结果处理失败:', error)
      alert(`AI识别结果处理失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 处理智能粘贴内容（支持单项目和多项目）
  const handleMultiPasteContent = () => {
    if (!multiPasteContent.trim()) {
      alert('请输入要识别的内容')
      return
    }

    try {
      const parsedProjects = parseMultiProjectContent(multiPasteContent)
      if (parsedProjects.length > 0) {
        // 🔧 智能判断：单项目直接添加到当前表单，多项目使用预览编辑器
        if (parsedProjects.length === 1) {
          const singleProject = parsedProjects[0]
          console.log(`🏗️ 识别为单项目，直接添加到表单:`, {
            name: singleProject.name,
            floors: singleProject.floors?.length || 0,
            projectAddress: singleProject.projectAddress
          })

          // 🔧 改进项目地址设置逻辑
          const projectName = singleProject.name || singleProject.projectAddress
          if (projectName && projectName !== '未命名项目' && projectName.trim().length > 0) {
            setProjectAddress(projectName)
            console.log(`🏗️ 设置项目地址: "${projectName}"`)
          } else {
            console.log(`⚠️ 项目名称为空或无效，跳过设置项目地址`)
          }

          // 转换项目数据为楼层数据并添加到当前表单
          const floorsToAdd: FloorData[] = singleProject.floors.map(floor => ({
            id: generateUniqueId('floor'),
            floorName: floor.name, // 直接使用楼层名称，已经是正确格式
            totalPrice: 0,
            vents: floor.vents.map(vent => ({
              ...vent,
              id: generateUniqueId('vent'),
              unitPrice: getDefaultPriceByType(vent.type),
              totalPrice: 0
            }))
          }))

          console.log(`🏗️ 转换楼层数据:`, floorsToAdd.map(f => `"${f.floorName}"`).join(', '))

          // 重新计算每个风口的总价
          floorsToAdd.forEach(floor => {
            floor.vents.forEach(vent => {
              vent.totalPrice = calculateVentPriceLocal(vent)
            })
            floor.totalPrice = floor.vents.reduce((sum, vent) => sum + vent.totalPrice, 0)
          })

          setFloors(prev => [...prev, ...floorsToAdd])
          setShowMultiPasteDialog(false)
          setMultiPasteContent('')
          alert(`✅ 成功识别并添加了 ${floorsToAdd.length} 个楼层的数据`)
          return
        }

        // 多项目：转换数据格式并使用导入预览编辑器
        console.log(`🏢 识别为多项目 (${parsedProjects.length}个)，转换数据格式`)

        // 🔧 转换 TempProjectData[] 为预览编辑器期望的格式
        const convertedProjects = parsedProjects.map(tempProject => ({
          id: generateUniqueId('project'),
          orderDate: getCurrentOrderInfo().orderDate,
          projectAddress: tempProject.name, // 🎯 使用项目名称作为地址
          floors: tempProject.floors.map(tempFloor => ({
            id: generateUniqueId('floor'),
            floorName: tempFloor.name, // 直接使用楼层名称，已经是正确格式
            vents: tempFloor.vents.map(tempVent => ({
              ...tempVent,
              id: generateUniqueId('vent'),
              unitPrice: getDefaultPriceByType(tempVent.type),
              totalPrice: calculateVentPrice(tempVent.type, tempVent.length, tempVent.width, tempVent.quantity, getDefaultPriceByType(tempVent.type))
            })),
            totalPrice: 0 // 会在后续计算中更新
          })),
          totalAmount: 0 // 会在后续计算中更新
        }))

        // 计算每个项目的总价
        convertedProjects.forEach(project => {
          project.floors.forEach(floor => {
            floor.totalPrice = floor.vents.reduce((sum, vent) => sum + vent.totalPrice, 0)
          })
          project.totalAmount = project.floors.reduce((sum, floor) => sum + floor.totalPrice, 0)
        })

        console.log(`🔧 数据转换完成:`, convertedProjects.map(p => `"${p.projectAddress}" (${p.floors.length}楼层)`))

        previewActions.initializePreview('multi_project', { projects: convertedProjects })
        setShowMultiPasteDialog(false)
        setMultiPasteContent('')
        console.log(`🎯 成功识别 ${convertedProjects.length} 个项目，使用编辑器预览`)
      } else {
        alert('未能识别到有效的项目数据，请检查格式')
      }
    } catch (error) {
      console.error('智能粘贴内容解析错误:', error)
      alert('解析失败，请检查内容格式')
    }
  }

  // 智能判断是否为回风口（基于尺寸）
  const isReturnVentByDimension = (length: number, width: number, originalText: string): boolean => {
    // 1. 文本特征判断：包含回风相关关键词（最高优先级）
    if (originalText && (originalText.includes('回风') || originalText.includes('return'))) {
      return true
    }

    // 2. 精确尺寸特征判断：300x1600是典型的回风口尺寸
    if ((length === 300 && width === 1600) || (length === 1600 && width === 300)) {
      return true
    }

    // 3. 回风口特征：宽度为300且长度较大
    if ((width === 300 && length >= 1500) || (length === 300 && width >= 1500)) {
      return true
    }

    // 4. 其他情况默认为出风口
    // 出风口特征：宽度通常为140-150，长度为3000-4500
    const minDim = Math.min(length, width)
    const maxDim = Math.max(length, width)

    // 典型出风口尺寸：140x3420, 150x3460等
    if (minDim >= 140 && minDim <= 200 && maxDim >= 2000 && maxDim <= 5000) {
      return false // 明确是出风口
    }

    return false // 默认为出风口
  }

  // 确定最终的风口类型（增强版）
  const determineVentType = (ventTypeStr: string, colorStr: string, width: number, notes?: string): VentItem['type'] => {
    console.log(`🔍 确定风口类型: 类型="${ventTypeStr}", 颜色="${colorStr}", 宽度=${width}mm, 备注="${notes || ''}"`)

    // 检修口/维修口
    if (ventTypeStr === 'maintenance' ||
        ventTypeStr?.includes('检修') || notes?.includes('检修') ||
        ventTypeStr?.includes('维修') || notes?.includes('维修')) {
      console.log(`  ✅ 识别为检修口`)
      return 'maintenance'
    }

    // 高端风口特殊类型识别
    const combinedText = `${ventTypeStr || ''} ${colorStr || ''} ${notes || ''}`.toLowerCase()

    // 箭型风口/T型风口
    if (combinedText.includes('箭型') || combinedText.includes('箭形') ||
        combinedText.includes('箭头型') || combinedText.includes('箭状') ||
        combinedText.includes('t型') || combinedText.includes('t形')) {
      const isReturn = combinedText.includes('回风') || combinedText.includes('进气') ||
                      combinedText.includes('进风') || combinedText.includes('吸风') ||
                      combinedText.includes('排风') || combinedText.includes('抽风') || width >= 255
      console.log(`  ✅ 识别为箭型${isReturn ? '回风口' : '出风口'}`)
      return isReturn ? 'arrow_return' : 'arrow_outlet'
    }

    // 爪型风口
    if (combinedText.includes('爪型') || combinedText.includes('爪形') || combinedText.includes('爪式') ||
        combinedText.includes('爪状') || combinedText.includes('爪子型')) {
      const isReturn = combinedText.includes('回风') || combinedText.includes('进气') ||
                      combinedText.includes('进风') || combinedText.includes('吸风') ||
                      combinedText.includes('排风') || combinedText.includes('抽风') || width > 280
      console.log(`  ✅ 识别为爪型${isReturn ? '回风口' : '出风口'}`)
      return isReturn ? 'claw_return' : 'claw_outlet'
    }

    // 根据宽度和类型判断：宽度≥255mm通常是回风口
    const isReturn = ventTypeStr === 'return' ||
                    ventTypeStr?.includes('回风') || ventTypeStr?.includes('进气') ||
                    ventTypeStr?.includes('进风') || ventTypeStr?.includes('吸风') ||
                    ventTypeStr?.includes('排风') || ventTypeStr?.includes('抽风') ||
                    width >= 255

    console.log(`  📏 判断类型: isReturn=${isReturn} (宽度=${width}mm, 类型包含回风=${ventTypeStr?.includes('回风') || ventTypeStr?.includes('进气')})`)

    // 根据颜色和类型确定具体类型
    if (isReturn) {
      // 回风口/进气口
      if (colorStr === 'black' || colorStr?.includes('黑')) {
        console.log(`  ✅ 识别为黑色回风口`)
        return 'black_return'
      } else {
        console.log(`  ✅ 识别为白色回风口`)
        return 'white_return' // 默认白色
      }
    } else {
      // 出风口
      if (colorStr === 'black' || colorStr?.includes('黑')) {
        console.log(`  ✅ 识别为双层黑色出风口`)
        return 'double_black_outlet'
      } else {
        console.log(`  ✅ 识别为双层白色出风口`)
        return 'double_white_outlet' // 默认白色
      }
    }
  }

  // 将AI结构化数据转换为项目格式
  const convertAIResultToProjects = (aiResult: any): any[] => {
    try {
      console.log('🔄 开始转换AI结果为项目格式...')

      if (!aiResult.projects || !Array.isArray(aiResult.projects)) {
        console.warn('⚠️ AI结果中没有projects数组')
        return []
      }

      const projects = aiResult.projects.map((aiProject: any, index: number) => {
        const project = {
          id: Date.now().toString() + index,
          projectAddress: aiProject.projectName || aiProject.clientInfo || `AI识别项目${index + 1}`,
          floors: [],
          totalAmount: 0,
          method: 'ai-recognition'
        }

        if (aiProject.floors && Array.isArray(aiProject.floors)) {
          project.floors = aiProject.floors.map((aiFloor: any) => {
            // 🔧 修复楼层名称处理
            let floorName = aiFloor.floorName || '1楼'

            // 如果楼层名称是数字，转换为标准格式
            if (typeof floorName === 'number') {
              floorName = `${floorName}楼`
            } else if (typeof floorName === 'string' && /^\d+$/.test(floorName)) {
              floorName = `${floorName}楼`
            }

            console.log(`🔧 [AI转换] 楼层名称处理: 原始="${aiFloor.floorName}" → 最终="${floorName}"`)

            const floor = {
              floorName: floorName,
              vents: [],
              totalAmount: 0
            }

            if (aiFloor.rooms && Array.isArray(aiFloor.rooms)) {
              // 将所有房间的风口合并到楼层中
              aiFloor.rooms.forEach((room: any) => {
                if (room.vents && Array.isArray(room.vents)) {
                  room.vents.forEach((vent: any) => {
                    if (vent.dimensions && vent.dimensions.length > 0 && vent.dimensions.width > 0) {
                      const ventItem = {
                        type: mapSystemTypeToVentType(vent.systemType),
                        length: vent.dimensions.length,
                        width: vent.dimensions.width,
                        quantity: vent.quantity || 1,
                        notes: [room.roomName !== '默认房间' ? room.roomName : '', vent.notes].filter(Boolean).join(' ').trim(),
                        unitPrice: 0,
                        totalPrice: 0
                      }
                      floor.vents.push(ventItem)
                    }
                  })
                }
              })
            }

            return floor
          })
        }

        return project
      })

      console.log('✅ AI结果转换完成，项目数:', projects.length)
      return projects

    } catch (error) {
      console.error('❌ AI结果转换失败:', error)
      return []
    }
  }

  // 映射AI的systemType到本地的风口类型
  const mapSystemTypeToVentType = (systemType: string): string => {
    const mapping: { [key: string]: string } = {
      'double_white_outlet': 'double_white_outlet',
      'double_black_outlet': 'double_black_outlet',
      'white_return': 'white_return',
      'black_return': 'black_return',
      'white_linear': 'white_linear',
      'black_linear': 'black_linear',
      'white_linear_return': 'white_linear_return',
      'black_linear_return': 'black_linear_return',
      'maintenance': 'maintenance'
    }

    return mapping[systemType] || 'double_white_outlet'
  }

  // 处理OCR识别的文字内容
  const handleOCRTextRecognized = async (recognizedText: string, ocrResult?: any) => {
    console.log('🎯 OCR识别完成，开始解析文字内容:')
    console.log('📝 识别到的原始文字:')
    console.log(recognizedText)

    // 🔧 新增：检查是否有AI结构化数据
    if (ocrResult && ocrResult.projects && ocrResult.projects.length > 0) {
      console.log('✅ 检测到AI结构化数据，直接使用...')
      console.log('🤖 AI识别结果:', ocrResult)

      // 直接使用AI结构化数据创建项目
      const aiProjects = convertAIResultToProjects(ocrResult)

      if (aiProjects.length > 0) {
        console.log('✅ AI数据转换成功，项目数:', aiProjects.length)

        // 直接设置预览数据
        previewActions.initializePreview('multi_project', { projects: aiProjects })

        setTimeout(() => {
          setShowIntelligentOCRDialog(false)
          alert(`🎉 AI识别成功！\n识别到 ${aiProjects.length} 个项目\n请在预览编辑器中确认数据后创建订单`)
        }, 500)

        return
      }
    }

    // 统计原始文字中的行数
    const lines = recognizedText.split('\n').filter(line => line.trim())
    console.log(`📊 原始文字行数统计: ${lines.length} 行`)
    lines.forEach((line, index) => {
      console.log(`  第${index + 1}行: "${line.trim()}"`)
    })
    console.log('📝 文字长度:', recognizedText.length)

    // 🔧 新增：导入数据收集器和增强解析器
    const { ocrDataCollector } = await import('@/lib/ocr-data-collector')
    const { EnhancedOCRParser } = await import('@/lib/enhanced-ocr-parser')

    // 🧠 新增：导入智能表格重组器
    const { TableDataReorganizer } = await import('@/lib/table-data-reorganizer')

    if (!recognizedText.trim()) {
      alert('未识别到有效文字内容')
      return
    }

    try {
      let parsedProjects: any[] = []

      // 🎯 新增：优先使用智能粘贴的简单解析引擎（更精准）
      console.log('🎯 尝试使用智能粘贴解析引擎（简单精准模式）...')
      try {
        const simpleParseResult = parseMultiProjectContent(recognizedText)
        console.log('🔄 简单解析结果:', simpleParseResult)

        if (simpleParseResult.length > 0) {
          console.log('✅ 简单解析成功，项目数:', simpleParseResult.length)
          parsedProjects = simpleParseResult
        } else {
          console.log('⚠️ 简单解析未找到项目，尝试复杂解析...')

          // 回退到智能表格重组分析
          const reorganizedData = TableDataReorganizer.reorganizeTableData(recognizedText)
          console.log('🔄 表格重组分析结果:', reorganizedData)

          if (reorganizedData.confidence > 0.5 && reorganizedData.rows.length > 0) {
            console.log('✅ 智能表格重组成功，置信度:', reorganizedData.confidence)

            // 转换重组数据为项目格式
            parsedProjects = [{
              id: Date.now().toString(),
              projectAddress: '智能图片识别项目',
              floors: [{
                floorName: '1楼',
              vents: reorganizedData.rows.flatMap(row => {
                const items: any[] = []

                console.log(`🔄 转换序号${row.sequence}:`, {
                  mainDimensions: row.mainDimensions,
                  subDimensions: row.subDimensions
                })

                // 主尺寸
                if (row.mainDimensions && row.mainDimensions !== '0x3510') {
                  const mainDim = parseDimensionString(row.mainDimensions)
                  if (mainDim && mainDim.length > 0 && mainDim.width > 0) {
                    // 智能判断风口类型
                    const isReturn = isReturnVentByDimension(mainDim.length, mainDim.width, row.mainDimensions)
                    const ventType = isReturn ? 'white_return' : 'double_white_outlet'

                    items.push({
                      type: ventType,
                      length: mainDim.length,
                      width: mainDim.width,
                      quantity: 1,
                      notes: `序号${row.sequence}${row.floor ? ` ${row.floor}` : ''}`,
                      unitPrice: 0,
                      totalPrice: 0
                    })
                    console.log(`✅ 添加主尺寸: ${mainDim.length}x${mainDim.width} → ${ventType} (isReturn: ${isReturn}, 原文: "${row.mainDimensions}")`)
                  }
                }

                // 子尺寸 - 只添加有效的子尺寸
                row.subDimensions.forEach((subDim, index) => {
                  const dim = parseDimensionString(subDim)
                  if (dim && dim.length > 0 && dim.width > 0) {
                    // 智能判断风口类型
                    const isReturn = isReturnVentByDimension(dim.length, dim.width, subDim)
                    const ventType = isReturn ? 'white_return' : 'double_white_outlet'

                    items.push({
                      type: ventType,
                      length: dim.length,
                      width: dim.width,
                      quantity: 1,
                      notes: `序号${row.sequence}${row.floor ? ` ${row.floor}` : ''} ${isReturn ? '回风' : '出风'}`,
                      unitPrice: 0,
                      totalPrice: 0
                    })
                    console.log(`✅ 添加子尺寸${index + 1}: ${dim.length}x${dim.width} → ${ventType} (isReturn: ${isReturn}, 原文: "${subDim}")`)
                  }
                })

                return items
              })
            }],
            totalAmount: 0,
            method: 'intelligent-reorganize'
          }]

          const totalVents = parsedProjects[0].floors[0].vents.length
          console.log('🎯 智能重组完成，识别到风口数量:', totalVents)

          // 验证风口数量
          const expectedVents = reorganizedData.rows.length * 2 // 双列表格，每行应该有2个风口
          if (totalVents < expectedVents) {
            console.log(`⚠️ 风口数量不足！期望: ${expectedVents}个，实际: ${totalVents}个，缺少: ${expectedVents - totalVents}个`)

            // 分析缺失原因
            let missingMainDimensions = 0
            let missingSubDimensions = 0

            reorganizedData.rows.forEach(row => {
              if (!row.mainDimensions || row.mainDimensions === '') {
                missingMainDimensions++
                console.log(`  缺失主尺寸: 序号${row.sequence}`)
              }
              if (!row.subDimensions || row.subDimensions.length === 0) {
                missingSubDimensions++
                console.log(`  缺失子尺寸: 序号${row.sequence}`)
              }
            })

            console.log(`📊 缺失分析: 主尺寸缺失${missingMainDimensions}个，子尺寸缺失${missingSubDimensions}个`)
          } else {
            console.log(`✅ 风口数量正确: ${totalVents}个`)
          }

          console.log('📋 智能重组项目详情:', JSON.stringify(parsedProjects[0], null, 2))
          }
        }
      } catch (error) {
        console.warn('⚠️ 智能表格重组失败，继续其他方法:', error)
      }

      // 🔧 如果智能重组未成功，尝试表格布局感知识别
      if (parsedProjects.length === 0 && ocrResult && ocrResult.words_result) {
        console.log('🎯 尝试表格布局感知识别...')

        try {
          // 使用新的表格感知意图识别
          const intentResult = recognizeIntentWithTable(recognizedText, ocrResult, true)

          if (intentResult.entities.ventItems.length > 0) {
            console.log('✅ 表格感知识别成功:', intentResult)

            // 转换为项目格式
            parsedProjects = [{
              id: Date.now().toString(),
              projectAddress: intentResult.entities.projectName || '未指定项目',
              floors: [{
                floorName: intentResult.entities.floorInfo || '1楼',
                items: intentResult.entities.ventItems.map((item: any) => ({
                  type: item.type,
                  length: item.length,
                  width: item.width,
                  quantity: item.quantity || 1,
                  notes: item.notes || '',
                  unitPrice: 0,
                  totalPrice: 0
                }))
              }],
              totalAmount: 0,
              method: 'table-aware'
            }]

            console.log('🎯 表格感知识别完成，识别到风口数量:', intentResult.entities.ventItems.length)
          }
        } catch (error) {
          console.warn('⚠️ 表格感知识别失败，回退到传统方法:', error)
        }
      }

      // 回退到传统解析方法
      if (parsedProjects.length === 0) {
        console.log('🔄 使用传统parseMultiProjectContent解析文字...')
        const tempProjects = parseMultiProjectContent(recognizedText)

        // 🔧 转换 TempProjectData 为预览编辑器期望的格式
        parsedProjects = tempProjects.map(tempProject => ({
          id: tempProject.id || generateUniqueId('project'),
          projectAddress: tempProject.name || '未命名项目', // 🎯 确保项目地址正确设置
          floors: tempProject.floors.map(floor => ({
            id: generateUniqueId('floor'),
            floorName: floor.name,
            vents: floor.vents || [],
            items: floor.vents || [], // 兼容性字段
            totalAmount: floor.vents ? floor.vents.reduce((sum, vent) => sum + (vent.totalPrice || 0), 0) : 0
          })),
          totalAmount: tempProject.floors.reduce((sum, floor) =>
            sum + (floor.vents ? floor.vents.reduce((ventSum, vent) => ventSum + (vent.totalPrice || 0), 0) : 0), 0
          ),
          method: 'traditional'
        }))

        console.log('🔧 数据转换完成，项目地址设置:', parsedProjects.map(p => p.projectAddress))
      }

      console.log('📊 解析结果:', {
        projectCount: parsedProjects.length,
        projects: parsedProjects.map(p => ({
          id: p.id,
          address: p.projectAddress,
          floorCount: p.floors.length,
          totalAmount: p.totalAmount
        }))
      })

      // 详细检查每个项目的数据
      parsedProjects.forEach((project, index) => {
        console.log(`🔍 项目${index + 1}详情:`, {
          id: project.id,
          address: project.projectAddress,
          floors: project.floors.length,
          totalItems: project.floors.reduce((sum, floor) => sum + (floor.vents ? floor.vents.length : (floor.items ? floor.items.length : 0)), 0),
          method: project.method
        })

        project.floors.forEach((floor, floorIndex) => {
          const ventCount = floor.vents ? floor.vents.length : (floor.items ? floor.items.length : 0)
          console.log(`  📁 楼层${floorIndex + 1}: ${floor.floorName}, 风口数量: ${ventCount}`)

          const vents = floor.vents || floor.items || []
          if (vents.length > 0) {
            vents.slice(0, 3).forEach((item, itemIndex) => {
              console.log(`    🌪️ 风口${itemIndex + 1}: ${item.type} ${item.length}x${item.width} 数量:${item.quantity} 备注:"${item.notes}"`)
            })
            if (vents.length > 3) {
              console.log(`    ... 还有 ${vents.length - 3} 个风口`)
            }
          }
        })
      })

      // 🔧 新增：收集OCR数据用于分析
      try {
        const sessionId = ocrDataCollector.collectOCRData(recognizedText, ocrResult, parsedProjects)
        console.log('📊 OCR数据收集完成，会话ID:', sessionId)

        // 将会话ID保存到状态中，用于后续的用户反馈收集
        setCurrentOCRSessionId(sessionId)
      } catch (error) {
        console.warn('⚠️ OCR数据收集失败:', error)
      }

      if (parsedProjects.length > 0) {
        console.log('✅ 解析成功，准备显示预览编辑器...')

        // 使用导入预览编辑器显示解析结果
        console.log('📤 准备初始化预览，项目数据:', parsedProjects.length, '个项目')
        previewActions.initializePreview('multi_project', { projects: parsedProjects })

        // 立即检查状态（可能还没更新）
        console.log('🔍 预览状态检查（立即）:', {
          showPreview: previewState.showPreview,
          importType: previewState.importType,
          projectCount: previewState.projects.length
        })

        // 延迟检查状态（等待状态更新）
        setTimeout(() => {
          console.log('🔍 预览状态检查（延迟）:', {
            showPreview: previewState.showPreview,
            importType: previewState.importType,
            projectCount: previewState.projects.length
          })

          // 详细检查预览状态中的项目数据
          if (previewState.projects.length > 0) {
            console.log('📋 预览状态中的项目数据:')
            previewState.projects.forEach((project, index) => {
              console.log(`  项目${index + 1}: ${project.projectAddress}, 楼层数: ${project.floors.length}`)
              project.floors.forEach((floor, floorIndex) => {
                const ventCount = floor.vents ? floor.vents.length : (floor.items ? floor.items.length : 0)
                console.log(`    楼层${floorIndex + 1}: ${floor.floorName}, 风口数: ${ventCount}`)
              })
            })
          } else {
            console.log('⚠️ 预览状态中仍然没有项目数据！')
            console.log('🔍 原始项目数据检查:', parsedProjects.map(p => ({
              id: p.id,
              address: p.projectAddress,
              floors: p.floors.length,
              items: p.floors.reduce((sum, f) => sum + (f.vents ? f.vents.length : (f.items ? f.items.length : 0)), 0)
            })))
          }
        }, 100)

        // 不要立即关闭智能OCR对话框，让用户看到结果
        setTimeout(() => {
          setShowIntelligentOCRDialog(false)

          // 🔧 修改：只在特定条件下显示反馈面板
          if (currentOCRSessionId) {
            // 检查是否需要显示反馈面板
            const shouldShowFeedback = checkShouldShowFeedback()
            if (shouldShowFeedback) {
              setTimeout(() => {
                setShowOCRFeedback(true)
              }, 2000) // 延长到2秒，减少打扰
            }
          }
        }, 500)

        console.log(`🎯 OCR文字解析成功，识别到 ${parsedProjects.length} 个项目，预览编辑器已打开`)

        // 显示成功提示
        alert(`🎉 OCR识别成功！\n识别到 ${parsedProjects.length} 个项目\n请在预览编辑器中确认数据后创建订单`)
      } else {
        console.warn('⚠️ 解析结果为空')
        alert('未能从识别的文字中解析出有效的项目数据，请检查图片内容格式\n\n提示：请确保图片包含项目地址和风口尺寸信息')
      }
    } catch (error) {
      console.error('❌ OCR文字解析失败:', error)
      alert(`文字解析失败: ${error instanceof Error ? error.message : '未知错误'}\n请检查识别内容格式`)
    }
  }

  // 主组件渲染
  return (
    <FactoryRouteGuard>
      <DashboardLayout role="factory">
        <div className="p-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Link href="/factory/orders">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">智能录单系统</h1>
                <p className="text-gray-600">高效表格录单 - 支持批量输入，客户信息完整录入</p>
              </div>
            </div>

          </div>

          {/* 小屏幕汇总信息 */}
          <div className="xl:hidden mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-center text-sm">
                  <div className="flex space-x-4">
                    <span>楼层: <strong>{floors.length}</strong></span>
                    <span>风口: <strong>{floors.reduce((totalSum, floor) => {
                      return totalSum + floor.vents.reduce((floorSum, vent) => {
                        const quantity = typeof vent.quantity === 'number' ? vent.quantity : parseInt(vent.quantity) || 0
                        return floorSum + quantity
                      }, 0)
                    }, 0)}</strong></span>
                  </div>
                  <div className="text-lg font-bold text-blue-600">
                    ¥{(finalDisplayTotal || 0).toFixed(1)}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="flex gap-8">
            {/* 主要内容 */}
            <div className="flex-1 space-y-6 pr-96 max-w-[calc(100vw-26rem)]">
              {/* 客户选择 */}
              <Card>
                <CardHeader>
                  <CardTitle>选择客户</CardTitle>
                  <CardDescription>输入客户名称或电话搜索，或创建新客户</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* 搜索式客户选择 */}
                    <div className="relative client-search-container">
                      <Input
                        value={clientSearchTerm ?? ''}
                        onChange={(e) => {
                          setClientSearchTerm(e.target.value)
                          setShowClientDropdown(true)
                          if (!e.target.value) {
                            setSelectedClient("")
                            setShowClientForm(false)
                          }
                        }}
                        onFocus={() => setShowClientDropdown(true)}
                        placeholder="输入客户名称或电话号码搜索..."
                        className="w-full"
                      />

                      {/* 搜索结果下拉框 */}
                      {showClientDropdown && (clientSearchTerm || filteredClients.length > 0) && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                          {/* 新建客户选项 */}
                          <div
                            onClick={handleNewClient}
                            className="px-4 py-2 hover:bg-blue-50 cursor-pointer border-b border-gray-100 text-blue-600 font-medium"
                          >
                            <Plus className="h-4 w-4 inline mr-2" />
                            创建新客户
                          </div>

                          {/* 现有客户列表 */}
                          {filteredClients.map(client => (
                            <div
                              key={client.id}
                              onClick={() => handleClientSelect(client)}
                              className="px-4 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                            >
                              <div className="font-medium">{client.name}</div>
                              <div className="text-sm text-gray-500">{client.phone}</div>
                            </div>
                          ))}

                          {/* 无搜索结果 */}
                          {clientSearchTerm && filteredClients.length === 0 && (
                            <div className="px-4 py-2 text-gray-500 text-center">
                              未找到匹配的客户
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* 新客户表单 */}
                    {showClientForm && (
                      <div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
                        <h4 className="font-medium text-blue-800 mb-3">新客户信息</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium mb-1">
                              客户名称/公司名称 <span className="text-red-500">*</span>
                            </label>
                            <Input
                              value={newClientData.name ?? ''}
                              onChange={(e) => setNewClientData(prev => ({ ...prev, name: e.target.value }))}
                              placeholder="请输入客户名称或公司名称"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-1">
                              联系电话 <span className="text-red-500">*</span>
                            </label>
                            <Input
                              type="tel"
                              value={newClientData.phone ?? ''}
                              onChange={(e) => handlePhoneChange(e.target.value)}
                              placeholder="请输入11位手机号码"
                              maxLength={11}
                              pattern="[0-9]*"
                            />
                            {phoneError && (
                              <p className="text-xs text-red-500 mt-1">{phoneError}</p>
                            )}
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-1">邮箱</label>
                            <Input
                              value={newClientData.email ?? ''}
                              onChange={(e) => setNewClientData(prev => ({ ...prev, email: e.target.value }))}
                              placeholder="请输入邮箱地址（可选）"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-1">公司名称</label>
                            <Input
                              value={newClientData.company ?? ''}
                              onChange={(e) => setNewClientData(prev => ({ ...prev, company: e.target.value }))}
                              placeholder="请输入公司名称（可选）"
                            />
                          </div>


                          {/* 推荐人信息 */}
                          <div className="md:col-span-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                            <h5 className="font-medium text-yellow-800 mb-2">推荐人信息</h5>
                            <p className="text-sm text-yellow-700 mb-3">
                              ⚠️ 如果此客户是由其他客户推荐的，请务必填写推荐人信息，关系到推荐者的费用！
                            </p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div>
                                <label className="block text-sm font-medium mb-1">推荐人</label>
                                <select
                                  value={newClientData.referrerId ?? ''}
                                  onChange={(e) => {
                                    const selectedReferrer = existingClients.find(c => c.id === e.target.value)
                                    setNewClientData(prev => ({
                                      ...prev,
                                      referrerId: e.target.value,
                                      referrerName: selectedReferrer?.name || ""
                                    }))
                                  }}
                                  className="w-full p-2 border border-gray-300 rounded-md"
                                >
                                  <option value="">无推荐人</option>
                                  {existingClients.map(client => (
                                    <option key={client.id} value={client.id}>
                                      {client.name} - {client.phone}
                                    </option>
                                  ))}
                                </select>
                              </div>
                              <div>
                                <label className="block text-sm font-medium mb-1">推荐人姓名</label>
                                <Input
                                  value={newClientData.referrerName ?? ''}
                                  onChange={(e) => setNewClientData(prev => ({ ...prev, referrerName: e.target.value }))}
                                  placeholder="推荐人姓名"
                                  disabled={!!newClientData.referrerId}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 项目地址 */}
              <Card>
                <CardHeader>
                  <CardTitle>项目地址</CardTitle>
                  <CardDescription>填写本次订单的具体项目地址</CardDescription>
                </CardHeader>
                <CardContent>
                  <Input
                    value={projectAddress ?? ''}
                    onChange={(e) => setProjectAddress(e.target.value)}
                    placeholder="请输入详细的项目地址，如：上海市浦东新区张江高科技园区XX大厦XX层"
                    className="w-full"
                  />
                </CardContent>
              </Card>

              {/* 楼层录单区域 */}
              <Card>
                <CardHeader>
                  <div className="flex flex-col space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>楼层风口录单</CardTitle>
                        <CardDescription>表格式批量录入，高效便捷，支持Excel导入</CardDescription>
                      </div>
                    </div>

                    {/* 工具栏区域 - 响应式布局 */}
                    <div className="border-t border-gray-100 pt-4">
                      <div className="flex flex-col xl:flex-row gap-4 xl:items-center xl:justify-between">
                        {/* 左侧：主要功能按钮 */}
                        <div className="flex flex-wrap gap-2 items-center">
                          {/* Excel导入下拉菜单 */}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="outline"
                                disabled={importing}
                                className="hover:bg-blue-50 hover:border-blue-300"
                              >
                                {importing ? (
                                  <>
                                    <div className="animate-spin h-4 w-4 mr-2 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                                    导入中...
                                  </>
                                ) : (
                                  <>
                                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                                    Excel导入
                                    <ChevronDown className="h-3 w-3 ml-1" />
                                  </>
                                )}
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="start">
                              <DropdownMenuItem asChild>
                                <label htmlFor="single-project-import" className="cursor-pointer w-full">
                                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                                  导入单个项目
                                </label>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <label htmlFor="multi-project-import" className="cursor-pointer w-full">
                                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                                  导入多个项目
                                </label>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>

                          {/* 隐藏的文件输入 */}
                          <input
                            id="single-project-import"
                            type="file"
                            accept=".xlsx,.xls,.csv"
                            onChange={handleSingleProjectImport}
                            className="hidden"
                          />
                          <input
                            id="multi-project-import"
                            type="file"
                            accept=".xlsx,.xls,.csv"
                            onChange={handleMultiProjectImport}
                            className="hidden"
                          />
                        </div>

                        {/* 智能粘贴识别按钮 */}
                        <Button
                          onClick={() => setShowMultiPasteDialog(true)}
                          variant="outline"
                          className="hover:bg-blue-50 hover:border-blue-300"
                        >
                          <ClipboardPaste className="h-4 w-4 mr-2" />
                          智能粘贴识别
                        </Button>

                        {/* 智能OCR识别按钮 */}
                        <Button
                          onClick={() => {
                            console.log('🤖 点击了智能OCR识别按钮')
                            setShowIntelligentOCRDialog(true)
                          }}
                          variant="outline"
                          className="hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 hover:border-purple-300 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200"
                        >
                          <Grid3X3 className="h-4 w-4 mr-2 text-purple-600" />
                          🤖 智能图片识别
                        </Button>

                        {/* AI智能识别按钮 */}
                        <Button
                          onClick={() => {
                            console.log('🧠 点击了AI智能识别按钮')
                            setShowAIRecognitionDialog(true)
                          }}
                          size="lg"
                          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 border-0"
                        >
                          🚀 AI智能识别
                        </Button>

                        {/* AI知识库功能已移除 */}

                        {/* 批量操作下拉菜单 */}
                        {floors.length > 0 && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="outline"
                                className="hover:bg-green-50 hover:border-green-300"
                              >
                                <Settings className="h-4 w-4 mr-2" />
                                批量操作
                                <ChevronDown className="h-3 w-3 ml-1" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="start">
                              <DropdownMenuItem onClick={() => setShowGlobalBatchPriceModal(true)}>
                                <DollarSign className="h-4 w-4 mr-2" />
                                全局批量改价
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => setShowGlobalBatchOutletTypeModal(true)}>
                                <Settings className="h-4 w-4 mr-2" />
                                全局批量改出风口
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => setShowGlobalBatchReturnTypeModal(true)}>
                                <Settings className="h-4 w-4 mr-2" />
                                全局批量改回风口
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}

                        {/* 右侧：批量备注管理按钮 */}
                        {floors.length > 0 && (
                          <div className="batch-notes-container flex flex-wrap items-center gap-2 xl:border-l xl:border-gray-300 xl:pl-4">
                            {/* 批量备注操作组 */}
                            <div className="flex items-center gap-1">
                              <span className="text-sm text-gray-600 font-medium whitespace-nowrap">批量备注:</span>
                              <Button
                                onClick={selectedVentIds.size === floors.reduce((total, floor) => total + floor.vents.length, 0) ? handleDeselectAllVents : handleSelectAllVents}
                                variant="outline"
                                size="sm"
                                className="hover:bg-purple-50 hover:border-purple-300 px-2"
                              >
                                <input
                                  type="checkbox"
                                  checked={selectedVentIds.size > 0 && selectedVentIds.size === floors.reduce((total, floor) => total + floor.vents.length, 0)}
                                  onChange={() => {}}
                                  className="h-3 w-3 mr-1"
                                  readOnly
                                />
                                {selectedVentIds.size === floors.reduce((total, floor) => total + floor.vents.length, 0) ? '取消全选' : '全选'}
                                {selectedVentIds.size > 0 && ` (${selectedVentIds.size})`}
                              </Button>
                            </div>

                            {/* 备注操作按钮 */}
                            <div className="flex gap-1">
                              <Button
                                onClick={handleBatchAddNotes}
                                variant="outline"
                                size="sm"
                                disabled={selectedVentIds.size === 0}
                                className="hover:bg-green-50 hover:border-green-300 disabled:opacity-50 px-2"
                                title="批量添加备注 (Ctrl+Shift+N)"
                              >
                                <FileText className="h-3 w-3 mr-1" />
                                添加
                              </Button>
                              <Button
                                onClick={handleBatchClearNotes}
                                variant="outline"
                                size="sm"
                                disabled={selectedVentIds.size === 0}
                                className="hover:bg-red-50 hover:border-red-300 disabled:opacity-50 px-2"
                                title="批量清除备注 (Ctrl+Shift+D)"
                              >
                                <Trash2 className="h-3 w-3 mr-1" />
                                清除
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {floors.map((floor) => (
                      <div key={floor.id} className="border border-gray-200 rounded-lg p-4">
                        {/* 楼层标题 - 紧凑版 */}
                        <div className="flex items-center justify-between mb-2 p-2 bg-gray-50 rounded">
                          <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="text-sm font-medium text-gray-600">楼层:</span>
                            <input
                              value={floor.floorName ?? ''}
                              onChange={(e) => updateFloorName(floor.id, e.target.value)}
                              className="w-16 px-2 py-1 border border-gray-300 rounded text-sm font-medium"
                              placeholder="1楼"
                            />
                            <div className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs font-medium">
                              风口数量: {floor.vents.reduce((sum, vent) => sum + vent.quantity, 0)}
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-bold text-green-600">
                                楼层总价: {(floor.totalPrice || 0).toFixed(1)}
                              </span>
                              <button
                                onClick={() => addVentToFloor(floor.id)}
                                className="px-2 py-1 text-xs text-blue-600 hover:bg-blue-50 rounded border border-blue-200"
                              >
                                + 添加风口
                              </button>
                            </div>


                            <button
                              onClick={() => removeFloor(floor.id)}
                              className="px-2 py-1 text-xs text-red-600 hover:bg-red-50 rounded border border-red-200"
                            >
                              删除楼层
                            </button>
                          </div>
                        </div>

                        {/* 快速填充工具 - 紧凑版 */}
                        <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded">
                          <div className="flex flex-wrap gap-1 text-xs">
                            <button
                              onClick={() => {
                                // 快速填充常用出风口尺寸 - 使用智能尺寸识别
                                const commonSizes = [
                                  smartDimensionRecognition(600, 400),
                                  smartDimensionRecognition(800, 300),
                                  smartDimensionRecognition(1000, 500)
                                ]
                                commonSizes.forEach(async (size, index) => {
                                  if (floor.vents[index]) {
                                    await updateVent(floor.id, floor.vents[index].id, {
                                      type: 'double_white_outlet',
                                      length: size.length,
                                      width: size.width
                                    })
                                  }
                                })
                              }}
                              className="px-2 py-1 text-blue-600 border border-blue-300 hover:bg-blue-100 rounded"
                            >
                              常用出风口
                            </button>
                            <button
                              onClick={() => {
                                // 快速填充常用回风口尺寸 - 使用智能尺寸识别
                                const commonSizes = [
                                  smartDimensionRecognition(500, 500),
                                  smartDimensionRecognition(600, 600),
                                  smartDimensionRecognition(800, 400)
                                ]
                                commonSizes.forEach(async (size, index) => {
                                  if (floor.vents[index]) {
                                    await updateVent(floor.id, floor.vents[index].id, {
                                      type: 'white_return',
                                      length: size.length,
                                      width: size.width
                                    })
                                  }
                                })
                              }}
                              className="px-2 py-1 text-orange-600 border border-orange-300 hover:bg-orange-100 rounded"
                            >
                              常用回风口
                            </button>
                            <button
                              onClick={() => {
                                setShowBatchPriceModal(floor.id)
                                setBatchPriceInput("")
                              }}
                              className="px-2 py-1 text-green-600 border border-green-300 hover:bg-green-100 rounded"
                            >
                              批量设置单价
                            </button>
                            <button
                              onClick={() => {
                                setShowBatchOutletTypeModal(floor.id)
                                setBatchOutletType('double_white_outlet') // 设置默认类型
                              }}
                              className="px-2 py-1 text-blue-600 border border-blue-300 hover:bg-blue-100 rounded"
                            >
                              批量改出风口
                            </button>
                            <button
                              onClick={() => {
                                setShowBatchReturnTypeModal(floor.id)
                                setBatchReturnType('white_return') // 设置默认类型
                              }}
                              className="px-2 py-1 text-orange-600 border border-orange-300 hover:bg-orange-100 rounded"
                            >
                              批量改回风口
                            </button>
                          </div>
                        </div>

                        {/* 优化的风口表格 - 更大的输入框和选择框 */}
                        <div className="space-y-2">
                          {floor.vents.map((vent) => {
                            const uniqueId = getVentUniqueId(floor.id, vent.id)
                            const isSelected = selectedVentIds.has(uniqueId)
                            return (
                              <div key={vent.id} data-vent-id={vent.id} className={`flex items-center space-x-3 p-3 border rounded transition-all duration-200 ${
                                isSelected
                                  ? 'border-blue-400 bg-blue-50 shadow-md ring-1 ring-blue-200'
                                  : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                              }`}>
                                {/* 选择复选框 */}
                                <div className="flex-shrink-0">
                                  <input
                                    type="checkbox"
                                    checked={isSelected}
                                    onChange={() => handleVentSelection(floor.id, vent.id)}
                                    className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                                  />
                                </div>

                                {/* 产品类型标签 */}
                                <div className="flex-shrink-0 w-20">
                                <span className={`inline-block px-2 py-1 rounded text-sm font-medium ${getVentTypeColor(vent.type)}`}>
                                  {getVentTypeShort(vent.type)}
                                </span>
                              </div>

                              {/* 尺寸输入 - 加大输入框 */}
                              <div className="flex items-center space-x-1">
                                <input
                                  type="number"
                                  min="0"
                                  value={safeValueForInput(vent.length, '')}
                                  onChange={(e) => updateVentImmediate(floor.id, vent.id, { length: Number(e.target.value) || 0 })}
                                  onBlur={() => handleDimensionInputComplete(floor.id, vent.id)}
                                  onKeyDown={(e) => handleVentKeyDown(e, floor.id, vent.id, 'length')}
                                  data-field="length"
                                  className="w-20 px-2 py-1 text-center border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="0"
                                />
                                <span className="text-gray-400 text-sm">×</span>
                                <input
                                  type="number"
                                  min="0"
                                  value={safeValueForInput(vent.width, '')}
                                  onChange={(e) => updateVentImmediate(floor.id, vent.id, { width: Number(e.target.value) || 0 })}
                                  onBlur={() => handleDimensionInputComplete(floor.id, vent.id)}
                                  onKeyDown={(e) => handleVentKeyDown(e, floor.id, vent.id, 'width')}
                                  data-field="width"
                                  className="w-20 px-2 py-1 text-center border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="0"
                                />
                                <span className="text-gray-400 text-sm">mm</span>
                              </div>

                              {/* 产品类型选择 - 确保每行都可编辑 */}
                              <div className="flex-shrink-0">
                                <select
                                  value={vent.type}
                                  onChange={async (e) => {
                                    console.log(`修改风口类型: ${vent.id} -> ${e.target.value}`)
                                    await updateVent(floor.id, vent.id, {
                                      type: e.target.value as VentItem['type']
                                      // 单价会在 updateVent 函数中自动设置
                                    })
                                  }}
                                  className="px-2 py-1 border-2 border-gray-300 rounded text-sm bg-white w-48 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer hover:border-gray-400"
                                  style={{ minWidth: '180px' }}
                                >
                                  <optgroup label="常规风口 (元/㎡)">
                                    <option value="double_white_outlet">双层白色出风口</option>
                                    <option value="double_black_outlet">双层黑色出风口</option>
                                    <option value="white_black_bottom_outlet">白色黑底出风口</option>
                                    <option value="white_black_bottom_return">白色黑底回风口</option>
                                    <option value="white_return">白色回风口</option>
                                    <option value="black_return">黑色回风口</option>
                                    <option value="white_linear">白色线型风口</option>
                                    <option value="black_linear">黑色线型风口</option>
                                    <option value="white_linear_return">白色线型回风口</option>
                                    <option value="black_linear_return">黑色线型回风口</option>
                                    <option value="maintenance">检修口</option>
                                  </optgroup>
                                  <optgroup label="高端风口 (元/m)">
                                    <option value="high_end_white_outlet">高端白色出风口</option>
                                    <option value="high_end_black_outlet">高端黑色出风口</option>
                                    <option value="high_end_white_return">高端白色回风口</option>
                                    <option value="high_end_black_return">高端黑色回风口</option>
                                    <option value="arrow_outlet">箭型出风口</option>
                                    <option value="arrow_return">箭型回风口</option>
                                    <option value="claw_outlet">爪型出风口</option>
                                    <option value="claw_return">爪型回风口</option>
                                    <option value="black_white_dual_outlet">黑白双色出风口</option>
                                    <option value="black_white_dual_return">黑白双色回风口</option>
                                    <option value="wood_grain_outlet">木纹出风口</option>
                                    <option value="wood_grain_return">木纹回风口</option>
                                    <option value="white_putty_outlet">白色腻子粉出风口</option>
                                    <option value="white_putty_return">白色腻子粉回风口</option>
                                    <option value="black_putty_outlet">黑色腻子粉出风口</option>
                                    <option value="black_putty_return">黑色腻子粉回风口</option>
                                    <option value="white_gypsum_outlet">白色石膏板出风口</option>
                                    <option value="white_gypsum_return">白色石膏板回风口</option>
                                    <option value="black_gypsum_outlet">黑色石膏板出风口</option>
                                    <option value="black_gypsum_return">黑色石膏板回风口</option>
                                  </optgroup>
                                </select>
                              </div>

                              {/* 数量 */}
                              <div className="flex items-center">
                                <input
                                  type="number"
                                  min="1"
                                  value={safeValueForInput(vent.quantity, 1)}
                                  onChange={(e) => updateVentSync(floor.id, vent.id, { quantity: Number(e.target.value) || 1 })}
                                  onKeyDown={(e) => handleVentKeyDown(e, floor.id, vent.id, 'quantity')}
                                  data-field="quantity"
                                  className="w-16 px-2 py-1 text-center border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                              </div>

                              {/* 单价 */}
                              <div className="flex items-center space-x-1">
                                <input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  value={safeValueForInput(vent.unitPrice, '')}
                                  onChange={(e) => updateVentSync(floor.id, vent.id, { unitPrice: Number(e.target.value) || 0 })}
                                  onKeyDown={(e) => handleVentKeyDown(e, floor.id, vent.id, 'unitPrice')}
                                  data-field="unitPrice"
                                  className="w-20 px-2 py-1 text-center border border-gray-300 rounded text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="0"
                                />
                                <span className="text-gray-400 text-sm">
                                  {getVentTypeUnit(vent.type)}
                                </span>
                              </div>

                              {/* 备注 - 加宽显示更多内容 */}
                              <div className="flex-shrink-0">
                                <input
                                  type="text"
                                  value={vent.notes ?? ''}
                                  onChange={(e) => updateVentSync(floor.id, vent.id, { notes: e.target.value })}
                                  placeholder="备注"
                                  className="w-40 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  title={vent.notes || ''}
                                />
                              </div>

                              {/* 小计 */}
                              <div className="flex-shrink-0 min-w-[80px] text-right">
                                <span className="font-mono font-bold text-blue-600 text-sm">
                                  {(vent.totalPrice || 0).toFixed(1)}
                                </span>
                              </div>

                              {/* 操作按钮 */}
                              <div className="flex-shrink-0 flex space-x-1">
                                <button
                                  onClick={() => duplicateVent(floor.id, vent.id)}
                                  className="h-8 w-8 flex items-center justify-center text-blue-600 hover:bg-blue-50 rounded border border-blue-200"
                                  title="完整复制这行风口 (Ctrl+D)"
                                >
                                  <Copy className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => duplicateVentWithoutDimensions(floor.id, vent.id)}
                                  className="h-8 w-8 flex items-center justify-center text-green-600 hover:bg-green-50 rounded border border-green-200"
                                  title="复制风口类型和设置，清空尺寸 (Ctrl+R)"
                                >
                                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 8h4v4" />
                                  </svg>
                                </button>
                                <button
                                  onClick={() => removeVent(floor.id, vent.id)}
                                  className="h-8 w-8 flex items-center justify-center text-red-600 hover:bg-red-50 rounded border border-red-200"
                                  title="删除这行风口"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                            )
                          })}
                        </div>

                        {/* 批量设置单价模态框 */}
                        {showBatchPriceModal === floor.id && (
                          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
                              <h3 className="text-lg font-semibold mb-4">批量设置单价</h3>
                              <p className="text-gray-600 mb-4">
                                将为 <span className="font-medium text-blue-600">{floor.floorName}</span> 的所有风口设置相同单价
                              </p>

                              <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">
                                  单价 (元)
                                </label>
                                <input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  value={batchPriceInput ?? ''}
                                  onChange={(e) => setBatchPriceInput(e.target.value)}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                      handleBatchSetPrice(floor.id)
                                    } else if (e.key === 'Escape') {
                                      setShowBatchPriceModal(null)
                                      setBatchPriceInput("")
                                    }
                                  }}
                                  placeholder="请输入单价"
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  autoFocus
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                  将应用到该楼层的 {floor.vents.length} 个风口
                                </p>
                              </div>

                              {/* 常用单价快捷按钮 */}
                              <div className="mb-4">
                                <p className="text-sm font-medium mb-2">常用单价:</p>
                                <div className="flex flex-wrap gap-2">
                                  {[50, 80, 100, 120, 150, 200].map(price => (
                                    <button
                                      key={price}
                                      onClick={() => setBatchPriceInput(price.toString())}
                                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded border"
                                    >
                                      {price}元
                                    </button>
                                  ))}
                                </div>
                              </div>

                              <div className="flex space-x-3">
                                <button
                                  onClick={() => handleBatchSetPrice(floor.id)}
                                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                                >
                                  确认设置
                                </button>
                                <button
                                  onClick={() => {
                                    setShowBatchPriceModal(null)
                                    setBatchPriceInput("")
                                  }}
                                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                  取消
                                </button>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* 批量更改出风口类型模态框 */}
                        {showBatchOutletTypeModal === floor.id && (
                          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full mx-4">
                              <h3 className="text-lg font-semibold mb-4">批量更改出风口类型</h3>
                              <p className="text-gray-600 mb-4">
                                将为 <span className="font-medium text-blue-600">{floor.floorName}</span> 的所有出风口更改为相同类型
                              </p>

                              <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">
                                  出风口类型
                                </label>
                                <select
                                  value={batchOutletType}
                                  onChange={(e) => setBatchOutletType(e.target.value as VentItem['type'])}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  autoFocus
                                >
                                  <optgroup label="常规出风口 (元/㎡)">
                                    <option value="double_white_outlet">双层白色出风口</option>
                                    <option value="double_black_outlet">双层黑色出风口</option>
                                    <option value="white_black_bottom_outlet">白色黑底出风口</option>
                                    <option value="white_linear">白色线型出风口</option>
                                    <option value="black_linear">黑色线型出风口</option>
                                  </optgroup>
                                  <optgroup label="高端出风口 (元/m)">
                                    <option value="arrow_outlet">箭型出风口</option>
                                    <option value="claw_outlet">爪型出风口</option>
                                    <option value="black_white_outlet">黑白双色出风口</option>
                                    <option value="wood_outlet">木纹出风口</option>
                                    <option value="white_putty_outlet">白色腻子粉出风口</option>
                                    <option value="black_putty_outlet">黑色腻子粉出风口</option>
                                    <option value="white_gypsum_outlet">白色石膏板出风口</option>
                                    <option value="black_gypsum_outlet">黑色石膏板出风口</option>
                                  </optgroup>
                                </select>
                                <p className="text-xs text-gray-500 mt-1">
                                  将应用到该楼层的 {floor.vents.filter(v => isOutletVent(v.type)).length} 个出风口，并自动更新单价
                                </p>
                              </div>

                              <div className="flex space-x-3">
                                <button
                                  onClick={() => handleBatchChangeOutletType(floor.id)}
                                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                                >
                                  确认更改
                                </button>
                                <button
                                  onClick={() => {
                                    setShowBatchOutletTypeModal(null)
                                  }}
                                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                  取消
                                </button>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* 批量更改回风口类型模态框 */}
                        {showBatchReturnTypeModal === floor.id && (
                          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full mx-4">
                              <h3 className="text-lg font-semibold mb-4">批量更改回风口类型</h3>
                              <p className="text-gray-600 mb-4">
                                将为 <span className="font-medium text-orange-600">{floor.floorName}</span> 的所有回风口更改为相同类型
                              </p>

                              <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">
                                  回风口类型
                                </label>
                                <select
                                  value={batchReturnType}
                                  onChange={(e) => setBatchReturnType(e.target.value as VentItem['type'])}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                                  autoFocus
                                >
                                  <optgroup label="常规回风口 (元/㎡)">
                                    <option value="white_black_bottom_return">白色黑底回风口</option>
                                    <option value="white_return">白色回风口</option>
                                    <option value="black_return">黑色回风口</option>
                                    <option value="white_linear_return">白色线型回风口</option>
                                    <option value="black_linear_return">黑色线型回风口</option>
                                  </optgroup>
                                  <optgroup label="高端回风口 (元/m)">
                                    <option value="arrow_return">箭型回风口</option>
                                    <option value="claw_return">爪型回风口</option>
                                    <option value="black_white_return">黑白双色回风口</option>
                                    <option value="wood_return">木纹回风口</option>
                                    <option value="white_putty_return">白色腻子粉回风口</option>
                                    <option value="black_putty_return">黑色腻子粉回风口</option>
                                    <option value="white_gypsum_return">白色石膏板回风口</option>
                                    <option value="black_gypsum_return">黑色石膏板回风口</option>
                                  </optgroup>
                                </select>
                                <p className="text-xs text-gray-500 mt-1">
                                  将应用到该楼层的 {floor.vents.filter(v => isReturnVent(v.type)).length} 个回风口，并自动更新单价
                                </p>
                              </div>

                              <div className="flex space-x-3">
                                <button
                                  onClick={() => handleBatchChangeReturnType(floor.id)}
                                  className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 transition-colors"
                                >
                                  确认更改
                                </button>
                                <button
                                  onClick={() => {
                                    setShowBatchReturnTypeModal(null)
                                  }}
                                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                  取消
                                </button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}

                    {floors.length === 0 && (
                      <div className="text-center py-12 text-gray-500">
                        <div className="text-6xl mb-4">📋</div>
                        <p className="text-lg font-medium mb-2">开始表格式录单</p>
                        <p className="text-sm">点击"添加楼层"开始高效录单</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 固定侧边栏 */}
            <div className="fixed top-20 right-4 w-80 h-[calc(100vh-6rem)] overflow-y-auto space-y-6 z-40">
              <Card>
                <CardHeader>
                  <CardTitle>订单汇总</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>楼层数量:</span>
                      <span className="font-medium">{floors.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>风口总数:</span>
                      <span className="font-medium">
                        {floors.reduce((totalSum, floor) => {
                          return totalSum + floor.vents.reduce((floorSum, vent) => {
                            // 确保数量是数字类型
                            const quantity = typeof vent.quantity === 'number' ? vent.quantity : parseInt(vent.quantity) || 0
                            return floorSum + quantity
                          }, 0)
                        }, 0)}
                      </span>
                    </div>
                    <div className="border-t pt-3">
                      <div className="flex justify-between text-lg font-bold">
                        <span>总金额:</span>
                        <span className="text-blue-600">¥{(finalDisplayTotal || 0).toFixed(1)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>产品统计</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    {(() => {
                      const stats = floors.reduce((acc, floor) => {
                        floor.vents.forEach(vent => {
                          const key = getVentTypeShort(vent.type)
                          acc[key] = (acc[key] || 0) + vent.quantity
                        })
                        return acc
                      }, {} as Record<string, number>)

                      return Object.entries(stats).map(([type, count]) => (
                        <div key={type} className="flex justify-between">
                          <span>{type}:</span>
                          <span className="font-medium">{count}个</span>
                        </div>
                      ))
                    })()}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>使用说明</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-sm text-gray-600 space-y-2">
                    <div>• 每个楼层独立管理</div>
                    <div>• 表格式批量录入</div>
                    <div>• 实时计算价格</div>
                    <div>• 颜色区分产品类型</div>
                    <div>• 支持快速填充常用尺寸</div>
                    <div>• 批量操作提高效率</div>
                    <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded">
                      <div className="text-green-700 space-y-1">
                        <div className="flex items-center space-x-1">
                          <span className="text-xs">💡</span>
                          <span className="text-xs font-medium">快捷操作：</span>
                        </div>
                        <div className="text-xs ml-4 space-y-1">
                          <div>• <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Enter</kbd> 在尺寸框按回车跳到下一行第一个输入框</div>
                          <div>• <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Ctrl+D</kbd> 完整复制当前行</div>
                          <div>• <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Ctrl+R</kbd> 复制类型设置，清空尺寸</div>
                          <div>• <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Ctrl+S</kbd> 快速保存</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* 浮动操作按钮 */}
          <div className="fixed bottom-6 right-6 flex flex-col space-y-3 z-50">
            <Button
              onClick={addFloor}
              className="bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-3"
              size="lg"
            >
              <Plus className="h-5 w-5 mr-2" />
              添加楼层
            </Button>
            <Button
              onClick={saveOrder}
              disabled={isSaving}
              className="bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
              size="lg"
            >
              <Save className="h-5 w-5 mr-2" />
              {isSaving ? '保存中...' : '保存订单'}
            </Button>
          </div>
        </div>



        {/* 全局批量设置单价模态框 */}
        {showGlobalBatchPriceModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">全局批量设置单价</h3>
              <p className="text-gray-600 mb-4">
                将为 <span className="font-medium text-blue-600">{floors.length}</span> 个楼层的
                <span className="font-medium text-blue-600">
                  {floors.reduce((total, floor) => total + floor.vents.length, 0)}
                </span> 个风口设置相同单价
              </p>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  单价 (元)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={globalBatchPriceInput ?? ''}
                  onChange={(e) => setGlobalBatchPriceInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleGlobalBatchSetPrice()
                    } else if (e.key === 'Escape') {
                      setShowGlobalBatchPriceModal(false)
                      setGlobalBatchPriceInput("")
                    }
                  }}
                  placeholder="请输入单价"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  autoFocus
                />
                <p className="text-xs text-gray-500 mt-1">
                  将应用到所有楼层的所有风口
                </p>
              </div>

              {/* 常用单价快捷按钮 */}
              <div className="mb-4">
                <p className="text-sm font-medium mb-2">常用单价:</p>
                <div className="flex flex-wrap gap-2">
                  {[50, 80, 100, 120, 150, 200].map(price => (
                    <button
                      key={price}
                      onClick={() => setGlobalBatchPriceInput(price.toString())}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded border"
                    >
                      {price}元
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleGlobalBatchSetPrice}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                >
                  确认设置
                </button>
                <button
                  onClick={() => {
                    setShowGlobalBatchPriceModal(false)
                    setGlobalBatchPriceInput("")
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 全局批量更改出风口类型模态框 */}
        {showGlobalBatchOutletTypeModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">全局批量更改出风口类型</h3>
              <p className="text-gray-600 mb-4">
                将为 <span className="font-medium text-blue-600">{floors.length}</span> 个楼层的
                <span className="font-medium text-blue-600">
                  {floors.reduce((total, floor) => total + floor.vents.filter(v => isOutletVent(v.type)).length, 0)}
                </span> 个出风口更改为相同类型
              </p>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  出风口类型
                </label>
                <select
                  value={globalBatchOutletType}
                  onChange={(e) => setGlobalBatchOutletType(e.target.value as VentItem['type'])}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  autoFocus
                >
                  <optgroup label="常规出风口 (元/㎡)">
                    <option value="double_white_outlet">双层白色出风口</option>
                    <option value="double_black_outlet">双层黑色出风口</option>
                    <option value="white_black_bottom_outlet">白色黑底出风口</option>
                    <option value="white_linear">白色线型出风口</option>
                    <option value="black_linear">黑色线型出风口</option>
                  </optgroup>
                  <optgroup label="高端出风口 (元/m)">
                    <option value="arrow_outlet">箭型出风口</option>
                    <option value="claw_outlet">爪型出风口</option>
                    <option value="black_white_outlet">黑白双色出风口</option>
                    <option value="wood_outlet">木纹出风口</option>
                    <option value="white_putty_outlet">白色腻子粉出风口</option>
                    <option value="black_putty_outlet">黑色腻子粉出风口</option>
                    <option value="white_gypsum_outlet">白色石膏板出风口</option>
                    <option value="black_gypsum_outlet">黑色石膏板出风口</option>
                  </optgroup>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  将应用到所有楼层的所有出风口，并自动更新单价
                </p>
              </div>

              {/* 常用出风口类型快捷按钮 */}
              <div className="mb-4">
                <p className="text-sm font-medium mb-2">常用出风口类型:</p>
                <div className="flex flex-wrap gap-2">
                  {[
                    { type: 'double_white_outlet', name: '双白出' },
                    { type: 'double_black_outlet', name: '双黑出' },
                    { type: 'white_linear', name: '白线型出' },
                    { type: 'black_linear', name: '黑线型出' },
                    { type: 'arrow_outlet', name: '箭型出' },
                    { type: 'claw_outlet', name: '爪型出' }
                  ].map(item => (
                    <button
                      key={item.type}
                      onClick={() => setGlobalBatchOutletType(item.type as VentItem['type'])}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded border"
                    >
                      {item.name}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleGlobalBatchChangeOutletType}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                >
                  确认更改
                </button>
                <button
                  onClick={() => {
                    setShowGlobalBatchOutletTypeModal(false)
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 全局批量更改回风口类型模态框 */}
        {showGlobalBatchReturnTypeModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">全局批量更改回风口类型</h3>
              <p className="text-gray-600 mb-4">
                将为 <span className="font-medium text-orange-600">{floors.length}</span> 个楼层的
                <span className="font-medium text-orange-600">
                  {floors.reduce((total, floor) => total + floor.vents.filter(v => isReturnVent(v.type)).length, 0)}
                </span> 个回风口更改为相同类型
              </p>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  回风口类型
                </label>
                <select
                  value={globalBatchReturnType}
                  onChange={(e) => setGlobalBatchReturnType(e.target.value as VentItem['type'])}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  autoFocus
                >
                  <optgroup label="常规回风口 (元/㎡)">
                    <option value="white_black_bottom_return">白色黑底回风口</option>
                    <option value="white_return">白色回风口</option>
                    <option value="black_return">黑色回风口</option>
                    <option value="white_linear_return">白色线型回风口</option>
                    <option value="black_linear_return">黑色线型回风口</option>
                  </optgroup>
                  <optgroup label="高端回风口 (元/m)">
                    <option value="arrow_return">箭型回风口</option>
                    <option value="claw_return">爪型回风口</option>
                    <option value="black_white_return">黑白双色回风口</option>
                    <option value="wood_return">木纹回风口</option>
                    <option value="white_putty_return">白色腻子粉回风口</option>
                    <option value="black_putty_return">黑色腻子粉回风口</option>
                    <option value="white_gypsum_return">白色石膏板回风口</option>
                    <option value="black_gypsum_return">黑色石膏板回风口</option>
                  </optgroup>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  将应用到所有楼层的所有回风口，并自动更新单价
                </p>
              </div>

              {/* 常用回风口类型快捷按钮 */}
              <div className="mb-4">
                <p className="text-sm font-medium mb-2">常用回风口类型:</p>
                <div className="flex flex-wrap gap-2">
                  {[
                    { type: 'white_return', name: '白回风' },
                    { type: 'black_return', name: '黑回风' },
                    { type: 'white_linear_return', name: '白线型回' },
                    { type: 'black_linear_return', name: '黑线型回' },
                    { type: 'arrow_return', name: '箭型回' },
                    { type: 'claw_return', name: '爪型回' }
                  ].map(item => (
                    <button
                      key={item.type}
                      onClick={() => setGlobalBatchReturnType(item.type as VentItem['type'])}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded border"
                    >
                      {item.name}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleGlobalBatchChangeReturnType}
                  className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 transition-colors"
                >
                  确认更改
                </button>
                <button
                  onClick={() => {
                    setShowGlobalBatchReturnTypeModal(false)
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 导入预览编辑组件 */}
        {previewState.showPreview && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-7xl mx-4 max-h-[95vh] overflow-y-auto flex flex-col">
              <ImportPreviewEditor
                projects={previewState.importType === 'multi_project' ? previewState.projects :
                  previewState.floors.length > 0 ? [{
                    id: 'single-project',
                    orderDate: previewState.singleProjectUpdates?.orderDate || getCurrentOrderInfo().orderDate,
                    projectAddress: previewState.singleProjectUpdates?.projectAddress || getCurrentOrderInfo().projectAddress,
                    floors: previewState.floors,
                    totalAmount: Math.round(previewState.floors.reduce((sum, floor) =>
                      sum + floor.vents.reduce((ventSum, vent) => ventSum + vent.totalPrice, 0), 0
                    ) * 10) / 10,
                    isSelected: true,
                    hasChanges: !!previewState.singleProjectUpdates,
                    validationErrors: []
                  }] : []
                }
                onProjectUpdate={previewActions.updateProject}
                onFloorUpdate={previewActions.updateFloor}
                onVentUpdate={previewActions.updateVent}
                onAddVent={previewActions.addVent}
                onRemoveVent={previewActions.removeVent}
                onDuplicateVent={previewActions.duplicateVent}
                onResetItem={previewActions.resetItem}
                onConfirm={handlePreviewConfirm}
                onCancel={handlePreviewCancel}
                isLoading={importing}
                selectedProjectIds={previewState.selectedProjectIds}
                onToggleProjectSelection={previewActions.toggleProjectSelection}
                onSelectAllProjects={previewActions.selectAllProjects}
                onDeselectAllProjects={previewActions.deselectAllProjects}
                // 客户选择相关 - 在所有模式下都显示，提升用户体验
                showClientSelection={true}
                selectedClient={selectedClient}
                onClientSelect={setSelectedClient}
                existingClients={existingClients}
                newClientData={newClientData}
                onNewClientDataChange={setNewClientData}
              />
            </div>
          </div>
        )}

        {/* 智能粘贴识别对话框 */}
        {showMultiPasteDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">智能粘贴识别</h3>
                <div className="flex items-center space-x-2">
                  <label className="flex items-center space-x-1 text-sm">
                    <input
                      type="checkbox"
                      checked={debugMode}
                      onChange={(e) => setDebugMode(e.target.checked)}
                      className="rounded"
                    />
                    <span>调试模式</span>
                  </label>
                </div>
              </div>

              {debugMode ? (
                <PerformanceMonitor />
              ) : (
                <div>
              <div className="text-sm text-gray-600 mb-4 space-y-2">
                <div>
                  <strong>🏗️ 支持格式示例：</strong><br/>
                  <strong>📋 单项目格式：</strong><br/>
                  • 出风1600×150×2个 (标准格式)<br/>
                  • 1710*300	12	象牙白 (简单表格)<br/>
                  • 1710,300,12,象牙白 (逗号分隔)<br/>
                  <strong>🏢 多项目格式：</strong><br/>
                  <div className="bg-gray-50 p-3 rounded mt-2 font-mono text-xs">
                    银桂苑B栋<br/>
                    一楼<br/>
                    3120*160	1	3120	160	象牙白	90.9	90.9	出风线形<br/>
                    二楼<br/>
                    590*150	1	590	150	象牙白	37.7	37.7	弧形出风线形<br/>
                    <br/>
                    青山府3A-1401<br/>
                    300*290	1	300	290	象牙白	22.7	22.7	检修口长面部<br/>
                    75*75	2	75	75	象牙白	10.0	20.0	新风<br/>
                    <br/>
                    来宾市兴宾区摩尔城11栋105<br/>
                    900*150	2	900	150	象牙白	28.7	57.3	出风
                  </div>
                </div>
                <div>
                  <strong>📋 识别规则：</strong><br/>
                  • <strong>项目地址</strong>：包含栋、苑、府、县、市、区等关键词的行<br/>
                  • <strong>楼层信息</strong>：一楼、二楼、1楼、2楼、F1、B1等格式<br/>
                  • <strong>风口数据</strong>：包含尺寸、数量、颜色等信息的行<br/>
                  • <strong>自动分组</strong>：系统会自动将数据分组到对应项目和楼层
                </div>
                <div className="text-blue-600">
                  <strong>💡 提示：</strong> 每个项目地址会自动创建新项目，楼层信息会创建新楼层
                </div>
                <div className="text-red-600">
                  <strong>⚠️ 注意：</strong> 请确保项目地址格式清晰，避免与风口数据混淆
                </div>
              </div>
              <textarea
                value={multiPasteContent ?? ''}
                onChange={(e) => setMultiPasteContent(e.target.value)}
                placeholder="请粘贴包含多个项目的内容..."
                className="w-full h-80 p-3 border border-gray-300 rounded-lg resize-none font-mono text-sm"
              />
              </div>
              )}
              <div className="flex justify-end space-x-3 mt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowMultiPasteDialog(false)
                    setMultiPasteContent('')
                  }}
                >
                  取消
                </Button>
                <Button
                  onClick={handleMultiPasteContent}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  智能识别
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 智能OCR识别对话框 */}
        {showIntelligentOCRDialog && (
          <IntelligentOCRUpload
            onTextRecognized={handleOCRTextRecognized}
            onClose={() => setShowIntelligentOCRDialog(false)}
            maxFiles={5}
            acceptedTypes={['image/jpeg', 'image/png', 'image/jpg', 'image/bmp', 'image/gif']}
          />
        )}

        {/* AI智能识别对话框 */}
        {showAIRecognitionDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-6xl mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  🤖 AI智能识别 (DeepSeek R1)
                </h3>
                <Button
                  variant="outline"
                  onClick={() => setShowAIRecognitionDialog(false)}
                >
                  关闭
                </Button>
              </div>

              <AIPasteRecognition
                onResult={handleAIRecognitionResult}
              />
            </div>
          </div>
        )}

        {/* 批量备注弹窗 */}
        {showBatchNotesModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">批量添加备注</h3>
              <p className="text-gray-600 mb-4">
                将为 <span className="font-medium text-blue-600">{selectedVentIds.size}</span> 个风口添加备注
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                <p className="text-sm text-blue-700">
                  💡 <strong>快捷键提示:</strong> Ctrl+A 全选/取消全选 | Ctrl+Shift+N 批量添加备注 | Ctrl+Shift+D 批量清除备注
                </p>
              </div>

              {/* 备注模式选择 */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">添加模式</label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="overwrite"
                      checked={batchNotesMode === 'overwrite'}
                      onChange={(e) => setBatchNotesMode(e.target.value as 'overwrite' | 'append')}
                      className="mr-2"
                    />
                    覆盖现有备注
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="append"
                      checked={batchNotesMode === 'append'}
                      onChange={(e) => setBatchNotesMode(e.target.value as 'overwrite' | 'append')}
                      className="mr-2"
                    />
                    追加到现有备注
                  </label>
                </div>
              </div>

              {/* 备注内容输入 */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">备注内容</label>
                <textarea
                  value={batchNotesText}
                  onChange={(e) => setBatchNotesText(e.target.value)}
                  placeholder="请输入备注内容..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  autoFocus
                />
              </div>

              {/* 常用备注快捷按钮 */}
              <div className="mb-4">
                <p className="text-sm font-medium mb-2">常用备注:</p>
                <div className="flex flex-wrap gap-2">
                  {['主卧', '次卧', '客厅', '厨房', '卫生间', '走廊', '阳台', '书房'].map(note => (
                    <button
                      key={note}
                      onClick={() => setBatchNotesText(note)}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded border"
                    >
                      {note}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowBatchNotesModal(false)
                    setBatchNotesText('')
                  }}
                >
                  取消
                </Button>
                <Button onClick={confirmBatchAddNotes}>
                  确认添加
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* OCR反馈面板 */}
        {showOCRFeedback && (
          <OCRFeedbackPanel
            sessionId={currentOCRSessionId}
            onClose={() => {
              setShowOCRFeedback(false)
              setCurrentOCRSessionId(null)
            }}
            onSubmit={() => {
              console.log('✅ 用户反馈已提交')
            }}
          />
        )}

        {/* AI知识库功能已移除 */}


      </DashboardLayout>
    </FactoryRouteGuard>
  );
}

export default CreateTableOrderPage;
