'use client'

import React, { useState, useRef, useEffect } from 'react'
import { SpatialLayout, SpatialTextItem } from '@/lib/spatial-text-analyzer'

interface SpatialLayoutViewerProps {
  layout: SpatialLayout
  originalImage?: string
  onItemClick?: (item: SpatialTextItem) => void
  showRelationships?: boolean
}

export function SpatialLayoutViewer({ 
  layout, 
  originalImage, 
  onItemClick,
  showRelationships = true 
}: SpatialLayoutViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [selectedItem, setSelectedItem] = useState<string | null>(null)
  const [scale, setScale] = useState(1)

  useEffect(() => {
    drawLayout()
  }, [layout, selectedItem, showRelationships, scale])

  const drawLayout = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 设置画布尺寸
    const maxWidth = 800
    const maxHeight = 600
    const scaleX = maxWidth / layout.width
    const scaleY = maxHeight / layout.height
    const currentScale = Math.min(scaleX, scaleY, 1) * scale

    canvas.width = layout.width * currentScale
    canvas.height = layout.height * currentScale

    // 绘制背景图片（如果有）
    if (originalImage) {
      const img = new Image()
      img.onload = () => {
        ctx.globalAlpha = 0.3
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        ctx.globalAlpha = 1
        drawTextItems(ctx, currentScale)
        if (showRelationships) {
          drawRelationships(ctx, currentScale)
        }
      }
      img.src = originalImage
    } else {
      drawTextItems(ctx, currentScale)
      if (showRelationships) {
        drawRelationships(ctx, currentScale)
      }
    }
  }

  const drawTextItems = (ctx: CanvasRenderingContext2D, scale: number) => {
    layout.items.forEach(item => {
      const x = item.position.left * scale
      const y = item.position.top * scale
      const width = item.position.width * scale
      const height = item.position.height * scale

      // 根据类型设置颜色
      const colors = {
        title: '#8B5CF6',
        floor: '#EF4444',
        room: '#10B981',
        dimension: '#3B82F6',
        note: '#F59E0B',
        quantity: '#6B7280',
        unknown: '#9CA3AF'
      }

      const color = colors[item.type] || colors.unknown
      const isSelected = selectedItem === item.id

      // 绘制边框
      ctx.strokeStyle = isSelected ? '#000000' : color
      ctx.lineWidth = isSelected ? 3 : 2
      ctx.setLineDash(isSelected ? [] : [5, 5])
      ctx.strokeRect(x, y, width, height)

      // 绘制填充
      ctx.fillStyle = color + '20' // 20% 透明度
      ctx.fillRect(x, y, width, height)

      // 绘制文本
      ctx.fillStyle = '#000000'
      ctx.font = `${Math.max(10, 12 * scale)}px Arial`
      ctx.textAlign = 'left'
      ctx.textBaseline = 'top'
      
      // 文本换行处理
      const maxTextWidth = width - 4
      const words = item.text.split('')
      let line = ''
      let lineY = y + 2
      
      for (let i = 0; i < words.length; i++) {
        const testLine = line + words[i]
        const metrics = ctx.measureText(testLine)
        
        if (metrics.width > maxTextWidth && line !== '') {
          ctx.fillText(line, x + 2, lineY)
          line = words[i]
          lineY += 14 * scale
        } else {
          line = testLine
        }
      }
      ctx.fillText(line, x + 2, lineY)

      // 绘制类型标签
      ctx.fillStyle = color
      ctx.font = `${Math.max(8, 10 * scale)}px Arial`
      ctx.textAlign = 'right'
      ctx.fillText(item.type.toUpperCase(), x + width - 2, y + 2)

      // 绘制置信度
      ctx.fillStyle = '#666666'
      ctx.font = `${Math.max(6, 8 * scale)}px Arial`
      ctx.textAlign = 'right'
      ctx.fillText(`${(item.confidence * 100).toFixed(0)}%`, x + width - 2, y + height - 10)
    })
  }

  const drawRelationships = (ctx: CanvasRenderingContext2D, scale: number) => {
    layout.relationships.forEach(rel => {
      const fromItem = layout.items.find(item => item.id === rel.fromId)
      const toItem = layout.items.find(item => item.id === rel.toId)
      
      if (!fromItem || !toItem) return

      const fromCenter = {
        x: (fromItem.position.left + fromItem.position.width / 2) * scale,
        y: (fromItem.position.top + fromItem.position.height / 2) * scale
      }
      
      const toCenter = {
        x: (toItem.position.left + toItem.position.width / 2) * scale,
        y: (toItem.position.top + toItem.position.height / 2) * scale
      }

      // 根据关系类型设置样式
      const relationColors = {
        above: '#EF4444',
        below: '#10B981',
        left: '#3B82F6',
        right: '#F59E0B',
        adjacent: '#8B5CF6',
        contains: '#6B7280'
      }

      ctx.strokeStyle = relationColors[rel.type] || '#9CA3AF'
      ctx.lineWidth = Math.max(1, 2 * scale)
      ctx.globalAlpha = 0.6
      ctx.setLineDash([3, 3])

      // 绘制连接线
      ctx.beginPath()
      ctx.moveTo(fromCenter.x, fromCenter.y)
      ctx.lineTo(toCenter.x, toCenter.y)
      ctx.stroke()

      // 绘制箭头
      const angle = Math.atan2(toCenter.y - fromCenter.y, toCenter.x - fromCenter.x)
      const arrowLength = 10 * scale
      const arrowAngle = Math.PI / 6

      ctx.beginPath()
      ctx.moveTo(toCenter.x, toCenter.y)
      ctx.lineTo(
        toCenter.x - arrowLength * Math.cos(angle - arrowAngle),
        toCenter.y - arrowLength * Math.sin(angle - arrowAngle)
      )
      ctx.moveTo(toCenter.x, toCenter.y)
      ctx.lineTo(
        toCenter.x - arrowLength * Math.cos(angle + arrowAngle),
        toCenter.y - arrowLength * Math.sin(angle + arrowAngle)
      )
      ctx.stroke()

      ctx.globalAlpha = 1
      ctx.setLineDash([])
    })
  }

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = (event.clientX - rect.left) / scale
    const y = (event.clientY - rect.top) / scale

    // 查找点击的文本项
    const clickedItem = layout.items.find(item => {
      return x >= item.position.left && 
             x <= item.position.left + item.position.width &&
             y >= item.position.top && 
             y <= item.position.top + item.position.height
    })

    if (clickedItem) {
      setSelectedItem(clickedItem.id)
      onItemClick?.(clickedItem)
    } else {
      setSelectedItem(null)
    }
  }

  const getSelectedItemInfo = () => {
    if (!selectedItem) return null
    
    const item = layout.items.find(item => item.id === selectedItem)
    if (!item) return null

    const relatedItems = layout.items.filter(otherItem => 
      item.relatedItems.includes(otherItem.id)
    )

    return { item, relatedItems }
  }

  const selectedInfo = getSelectedItemInfo()

  return (
    <div className="space-y-4">
      {/* 控制面板 */}
      <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">缩放:</label>
          <input
            type="range"
            min="0.5"
            max="2"
            step="0.1"
            value={scale}
            onChange={(e) => setScale(parseFloat(e.target.value))}
            className="w-20"
          />
          <span className="text-sm text-gray-600">{(scale * 100).toFixed(0)}%</span>
        </div>
        
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={showRelationships}
            onChange={(e) => setShowRelationships?.(e.target.checked)}
          />
          <span className="text-sm">显示关系线</span>
        </label>

        <div className="text-sm text-gray-600">
          总计: {layout.items.length} 个文本项, {layout.relationships.length} 个关系
        </div>
      </div>

      {/* 画布容器 */}
      <div className="border rounded-lg overflow-auto max-h-96">
        <canvas
          ref={canvasRef}
          onClick={handleCanvasClick}
          className="cursor-pointer"
          style={{ maxWidth: '100%', height: 'auto' }}
        />
      </div>

      {/* 图例 */}
      <div className="grid grid-cols-3 gap-2 text-xs">
        {Object.entries({
          title: { color: '#8B5CF6', label: '标题' },
          floor: { color: '#EF4444', label: '楼层' },
          room: { color: '#10B981', label: '房间' },
          dimension: { color: '#3B82F6', label: '尺寸' },
          note: { color: '#F59E0B', label: '备注' },
          quantity: { color: '#6B7280', label: '数量' }
        }).map(([type, { color, label }]) => (
          <div key={type} className="flex items-center gap-1">
            <div 
              className="w-3 h-3 border-2 rounded"
              style={{ borderColor: color, backgroundColor: color + '20' }}
            />
            <span>{label}</span>
          </div>
        ))}
      </div>

      {/* 选中项详情 */}
      {selectedInfo && (
        <div className="p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium mb-2">选中项详情</h3>
          <div className="space-y-1 text-sm">
            <div><strong>文本:</strong> {selectedInfo.item.text}</div>
            <div><strong>类型:</strong> {selectedInfo.item.type}</div>
            <div><strong>置信度:</strong> {(selectedInfo.item.confidence * 100).toFixed(1)}%</div>
            <div><strong>位置:</strong> ({selectedInfo.item.position.left}, {selectedInfo.item.position.top})</div>
            <div><strong>尺寸:</strong> {selectedInfo.item.position.width} × {selectedInfo.item.position.height}</div>
            {selectedInfo.relatedItems.length > 0 && (
              <div>
                <strong>相关项:</strong> {selectedInfo.relatedItems.map(item => item.text).join(', ')}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
