#!/bin/bash
# no_git_update.sh - 无Git环境快速更新脚本
# 使用方法: ./no_git_update.sh <更新包路径> [选项]

set -e

# 配置
PROJECT_DIR="/opt/factorysystem"
BACKUP_DIR="/backups/nogit_$(date +%Y%m%d_%H%M%S)"
FORCE_UPDATE=false
SKIP_BACKUP=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：彩色输出
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：显示帮助
show_help() {
    echo "风口云平台无Git环境更新脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 <更新包路径> [选项]"
    echo ""
    echo "选项:"
    echo "  --force      强制更新，跳过确认"
    echo "  --no-backup  跳过备份（不推荐）"
    echo "  -h, --help   显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 /tmp/factorysystem-update.tar.gz"
    echo "  $0 /tmp/factorysystem-update.tar.gz --force"
    echo ""
    echo "更新包准备方法:"
    echo "  tar -czf factorysystem-update.tar.gz \\"
    echo "    --exclude='node_modules' \\"
    echo "    --exclude='.git' \\"
    echo "    --exclude='*.log' \\"
    echo "    --exclude='.next' \\"
    echo "    ./factorysystem"
}

# 解析命令行参数
UPDATE_PACKAGE=""
while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE_UPDATE=true
            shift
            ;;
        --no-backup)
            SKIP_BACKUP=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -*)
            print_status $RED "❌ 未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [ -z "$UPDATE_PACKAGE" ]; then
                UPDATE_PACKAGE="$1"
            else
                print_status $RED "❌ 多余的参数: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查参数
if [ -z "$UPDATE_PACKAGE" ]; then
    print_status $RED "❌ 请提供更新包路径"
    show_help
    exit 1
fi

if [ ! -f "$UPDATE_PACKAGE" ]; then
    print_status $RED "❌ 更新包不存在: $UPDATE_PACKAGE"
    exit 1
fi

print_status $BLUE "🚀 风口云平台无Git环境更新开始..."
print_status $BLUE "时间: $(date)"
print_status $BLUE "更新包: $UPDATE_PACKAGE"
print_status $BLUE "================================"

# 函数：检查先决条件
check_prerequisites() {
    print_status $YELLOW "🔍 检查更新先决条件..."
    
    # 检查是否在正确的目录
    if [ ! -f "package.json" ] || [ ! -f "docker-compose.yml" ]; then
        print_status $RED "❌ 请在项目根目录执行此脚本"
        exit 1
    fi
    
    # 检查磁盘空间（至少需要2GB）
    AVAILABLE_SPACE=$(df . | awk 'NR==2 {print $4}')
    if [ $AVAILABLE_SPACE -lt 2097152 ]; then
        print_status $RED "❌ 磁盘空间不足，无法执行更新"
        exit 1
    fi
    
    # 检查Docker服务
    if ! docker-compose ps >/dev/null 2>&1; then
        print_status $RED "❌ Docker Compose服务未运行"
        exit 1
    fi
    
    # 检查更新包格式
    if ! tar -tzf "$UPDATE_PACKAGE" >/dev/null 2>&1; then
        print_status $RED "❌ 更新包格式错误或损坏"
        exit 1
    fi
    
    print_status $GREEN "✅ 先决条件检查通过"
}

# 函数：显示更新信息
show_update_info() {
    print_status $YELLOW "📋 更新信息:"
    
    # 显示更新包内容
    echo "更新包内容预览:"
    tar -tzf "$UPDATE_PACKAGE" | head -20
    if [ $(tar -tzf "$UPDATE_PACKAGE" | wc -l) -gt 20 ]; then
        echo "... (还有更多文件)"
    fi
    
    echo ""
    print_status $YELLOW "当前系统状态:"
    docker-compose ps
    
    if [ "$FORCE_UPDATE" = false ]; then
        echo ""
        read -p "是否继续更新？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status $YELLOW "更新已取消"
            exit 0
        fi
    fi
}

# 函数：创建备份
create_backup() {
    if [ "$SKIP_BACKUP" = true ]; then
        print_status $YELLOW "⚠️ 跳过备份（--no-backup选项）"
        return 0
    fi
    
    print_status $YELLOW "💾 创建系统备份..."
    
    mkdir -p $BACKUP_DIR
    
    # 备份数据库
    print_status $BLUE "  📊 备份数据库..."
    if docker-compose exec -T postgres pg_dump -U factorysystem factorysystem > $BACKUP_DIR/database.sql; then
        print_status $GREEN "  ✅ 数据库备份完成"
    else
        print_status $RED "  ❌ 数据库备份失败"
        exit 1
    fi
    
    # 备份应用文件
    print_status $BLUE "  📁 备份应用文件..."
    tar -czf $BACKUP_DIR/app_files.tar.gz ./uploads ./logs 2>/dev/null || true
    
    # 备份当前代码
    print_status $BLUE "  💻 备份当前代码..."
    tar -czf $BACKUP_DIR/current_code.tar.gz \
        --exclude='node_modules' \
        --exclude='.next' \
        --exclude='uploads' \
        --exclude='logs' \
        . 2>/dev/null || true
    
    # 备份配置文件
    print_status $BLUE "  ⚙️ 备份配置文件..."
    cp .env.production $BACKUP_DIR/ 2>/dev/null || cp .env.local $BACKUP_DIR/ 2>/dev/null || true
    cp docker-compose.yml $BACKUP_DIR/
    cp nginx.conf $BACKUP_DIR/ 2>/dev/null || true
    
    # 验证备份
    if [ -f "$BACKUP_DIR/database.sql" ] && [ -s "$BACKUP_DIR/database.sql" ]; then
        print_status $GREEN "✅ 备份创建成功: $BACKUP_DIR"
        echo $BACKUP_DIR > .last_backup
    else
        print_status $RED "❌ 备份创建失败"
        exit 1
    fi
}

# 函数：执行更新
perform_update() {
    print_status $YELLOW "🔄 执行更新..."
    
    # 停止应用服务
    print_status $BLUE "  🛑 停止应用服务..."
    docker-compose stop app
    
    # 解压新版本到临时目录
    print_status $BLUE "  📥 解压新版本..."
    TEMP_DIR="/tmp/factorysystem_update_$$"
    mkdir -p $TEMP_DIR
    tar -xzf "$UPDATE_PACKAGE" -C $TEMP_DIR
    
    # 查找解压后的项目目录
    PROJECT_SUBDIR=$(find $TEMP_DIR -name "package.json" -type f | head -1 | xargs dirname)
    if [ -z "$PROJECT_SUBDIR" ]; then
        print_status $RED "❌ 更新包中未找到有效的项目结构"
        rm -rf $TEMP_DIR
        exit 1
    fi
    
    # 保留重要配置文件
    print_status $BLUE "  🔒 保留配置文件..."
    cp .env.production $PROJECT_SUBDIR/ 2>/dev/null || cp .env.local $PROJECT_SUBDIR/ 2>/dev/null || true
    cp docker-compose.yml $PROJECT_SUBDIR/ 2>/dev/null || true
    cp nginx.conf $PROJECT_SUBDIR/ 2>/dev/null || true
    
    # 替换代码文件
    print_status $BLUE "  🔄 替换代码文件..."
    rsync -av --exclude='uploads' --exclude='logs' --exclude='node_modules' --exclude='.next' \
        $PROJECT_SUBDIR/ $PROJECT_DIR/
    
    # 清理临时文件
    rm -rf $TEMP_DIR
    
    # 检查依赖变化
    print_status $BLUE "  📦 检查依赖..."
    if [ -f "package.json" ]; then
        print_status $BLUE "  📦 安装依赖..."
        npm ci --production
    fi
    
    # 重新构建
    print_status $BLUE "  🏗️ 重新构建..."
    docker-compose build app
    
    # 启动服务
    print_status $BLUE "  🚀 启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    print_status $BLUE "  ⏳ 等待服务启动..."
    sleep 30
}

# 函数：验证更新
verify_update() {
    print_status $YELLOW "✅ 验证更新..."
    
    # 健康检查
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:3000/api/health > /dev/null; then
            print_status $GREEN "  ✅ 健康检查通过"
            break
        fi
        
        print_status $BLUE "  ⏳ 等待服务响应... ($attempt/$max_attempts)"
        sleep 10
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_status $RED "❌ 更新后健康检查失败"
        return 1
    fi
    
    # 检查服务状态
    print_status $BLUE "  🔍 检查服务状态..."
    if ! docker-compose ps | grep -q "Up"; then
        print_status $RED "❌ 服务状态异常"
        return 1
    fi
    
    # 检查数据库连接
    print_status $BLUE "  🗄️ 检查数据库连接..."
    if ! docker-compose exec -T postgres psql -U factorysystem -c "SELECT 1;" > /dev/null 2>&1; then
        print_status $RED "❌ 数据库连接异常"
        return 1
    fi
    
    print_status $GREEN "✅ 更新验证成功"
    return 0
}

# 函数：显示更新摘要
show_summary() {
    echo ""
    print_status $GREEN "📊 更新摘要"
    print_status $BLUE "================================"
    echo "更新时间: $(date)"
    echo "更新包: $(basename $UPDATE_PACKAGE)"
    if [ "$SKIP_BACKUP" = false ]; then
        echo "备份位置: $BACKUP_DIR"
    fi
    echo "服务状态:"
    docker-compose ps
    echo ""
    print_status $GREEN "🎉 更新完成！"
}

# 函数：回滚指导
show_rollback_info() {
    echo ""
    print_status $YELLOW "🔙 如需回滚，请执行："
    if [ "$SKIP_BACKUP" = false ]; then
        echo "  ./backup_manager.sh restore $(basename $BACKUP_DIR)"
    else
        echo "  由于跳过了备份，无法自动回滚"
        echo "  请手动恢复之前的版本"
    fi
}

# 主函数
main() {
    # 执行更新流程
    check_prerequisites
    show_update_info
    create_backup
    
    if perform_update; then
        if verify_update; then
            show_summary
            show_rollback_info
            
            # 记录更新
            echo "$(date): 更新成功 - $(basename $UPDATE_PACKAGE)" >> .update_history
        else
            print_status $RED "❌ 更新验证失败"
            show_rollback_info
            exit 1
        fi
    else
        print_status $RED "❌ 更新执行失败"
        show_rollback_info
        exit 1
    fi
}

# 执行主函数
main "$@"
