/**
 * 🇨🇳 风口云平台 - 主题管理Store
 * 
 * 功能说明：
 * - 管理应用主题状态
 * - 支持浅色、深色、自动主题
 * - 持久化主题设置
 * - 提供多种预设主题
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { useAuthStore } from './auth'

// 主题类型定义
export type ThemeMode = 'light' | 'dark' | 'auto' | 'smart'

// 预设主题配置
export const THEME_PRESETS = {
  light: {
    name: '浅色主题',
    description: '适合白天使用的明亮主题',
    colors: {
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      card: 'hsl(0 0% 100%)',
      cardForeground: 'hsl(222.2 84% 4.9%)',
      popover: 'hsl(0 0% 100%)',
      popoverForeground: 'hsl(222.2 84% 4.9%)',
      primary: 'hsl(221.2 83.2% 53.3%)',
      primaryForeground: 'hsl(210 40% 98%)',
      secondary: 'hsl(210 40% 96%)',
      secondaryForeground: 'hsl(222.2 84% 4.9%)',
      muted: 'hsl(210 40% 96%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      accent: 'hsl(210 40% 96%)',
      accentForeground: 'hsl(222.2 84% 4.9%)',
      destructive: 'hsl(0 84.2% 60.2%)',
      destructiveForeground: 'hsl(210 40% 98%)',
      border: 'hsl(214.3 31.8% 91.4%)',
      input: 'hsl(214.3 31.8% 91.4%)',
      ring: 'hsl(221.2 83.2% 53.3%)',
    }
  },
  dark: {
    name: '深色主题',
    description: '适合夜间使用的深色主题',
    colors: {
      background: 'hsl(0 0% 3.9%)',        // 更深的黑色背景
      foreground: 'hsl(0 0% 98%)',         // 纯白色文字
      card: 'hsl(0 0% 3.9%)',              // 卡片背景与主背景一致
      cardForeground: 'hsl(0 0% 98%)',     // 卡片文字
      popover: 'hsl(0 0% 3.9%)',           // 弹出层背景
      popoverForeground: 'hsl(0 0% 98%)',  // 弹出层文字
      primary: 'hsl(210 40% 98%)',         // 主色调改为白色
      primaryForeground: 'hsl(0 0% 9%)',   // 主色调文字为深色
      secondary: 'hsl(0 0% 14.9%)',        // 次要背景
      secondaryForeground: 'hsl(0 0% 98%)', // 次要文字
      muted: 'hsl(0 0% 14.9%)',            // 静音背景
      mutedForeground: 'hsl(0 0% 63.9%)',  // 静音文字
      accent: 'hsl(0 0% 14.9%)',           // 强调背景
      accentForeground: 'hsl(0 0% 98%)',   // 强调文字
      destructive: 'hsl(0 62.8% 30.6%)',   // 危险色保持不变
      destructiveForeground: 'hsl(0 0% 98%)', // 危险色文字
      border: 'hsl(0 0% 14.9%)',           // 边框颜色
      input: 'hsl(0 0% 14.9%)',            // 输入框背景
      ring: 'hsl(0 0% 83.1%)',             // 焦点环颜色
    }
  },
  blue: {
    name: '蓝色主题',
    description: '专业的蓝色商务主题',
    colors: {
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      primary: 'hsl(221.2 83.2% 53.3%)',
      primaryForeground: 'hsl(210 40% 98%)',
      secondary: 'hsl(210 40% 96%)',
      accent: 'hsl(210 40% 96%)',
    }
  },
  green: {
    name: '绿色主题',
    description: '清新的绿色自然主题',
    colors: {
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      primary: 'hsl(142.1 76.2% 36.3%)',
      primaryForeground: 'hsl(355.7 100% 97.3%)',
      secondary: 'hsl(210 40% 96%)',
      accent: 'hsl(210 40% 96%)',
    }
  },
  purple: {
    name: '紫色主题',
    description: '优雅的紫色创意主题',
    colors: {
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      primary: 'hsl(262.1 83.3% 57.8%)',
      primaryForeground: 'hsl(210 40% 98%)',
      secondary: 'hsl(210 40% 96%)',
      accent: 'hsl(210 40% 96%)',
    }
  }
}

interface ThemeState {
  mode: ThemeMode
  isSystemDark: boolean
  isTimeDark: boolean
  smartThemeSettings: {
    lightStartHour: number  // 浅色主题开始时间（如：6点）
    darkStartHour: number   // 深色主题开始时间（如：18点）
  }
}

interface ThemeActions {
  setMode: (mode: ThemeMode) => void
  setSystemDark: (isDark: boolean) => void
  getCurrentTheme: () => typeof THEME_PRESETS.light
  applyTheme: () => void
  getUserThemeKey: () => string
}

type ThemeStore = ThemeState & ThemeActions

const initialState: ThemeState = {
  mode: 'light',
  isSystemDark: false,
  isTimeDark: false,
  smartThemeSettings: {
    lightStartHour: 6,
    darkStartHour: 18
  }
}

// 获取用户特定的主题存储键
function getUserThemeKey(): string {
  if (typeof window === 'undefined') return 'default'

  try {
    // 动态导入避免循环依赖
    const authData = localStorage.getItem('auth-storage')
    if (authData) {
      const parsed = JSON.parse(authData)
      const { user, role, factoryId } = parsed.state || {}

      if (user && user.id) {
        // 使用用户ID + 角色 + 工厂ID（如果有）作为唯一标识
        const userKey = `${user.id}_${role}`
        return factoryId ? `${userKey}_${factoryId}` : userKey
      }
    }
  } catch (error) {
    console.warn('⚠️ 获取用户主题键失败:', error)
  }

  // 如果无法获取用户信息，使用默认键（兼容未登录状态）
  return 'default'
}

// 用户独立的主题管理器
class UserThemeManager {
  private static instance: UserThemeManager
  private currentUserKey: string = 'default'
  private userThemes: Map<string, ThemeState> = new Map()
  private listeners: Set<(theme: ThemeState) => void> = new Set()

  static getInstance(): UserThemeManager {
    if (!UserThemeManager.instance) {
      UserThemeManager.instance = new UserThemeManager()
    }
    return UserThemeManager.instance
  }

  // 设置当前用户
  setCurrentUser(userKey: string): void {
    this.currentUserKey = userKey
    this.loadUserTheme(userKey)
  }

  // 获取当前用户的主题
  getCurrentTheme(): ThemeState {
    return this.userThemes.get(this.currentUserKey) || { ...initialState }
  }

  // 设置当前用户的主题模式
  setMode(mode: ThemeMode): void {
    const currentTheme = this.getCurrentTheme()
    const newTheme = { ...currentTheme, mode }

    // 更新内存中的主题
    this.userThemes.set(this.currentUserKey, newTheme)

    // 保存到localStorage
    this.saveUserTheme(this.currentUserKey, newTheme)

    // 应用主题
    this.applyTheme(newTheme)

    // 通知监听器
    this.notifyListeners(newTheme)
  }

  // 加载用户主题
  private loadUserTheme(userKey: string): void {
    if (typeof window === 'undefined') return

    try {
      const storageKey = `theme-storage-${userKey}`
      const stored = localStorage.getItem(storageKey)

      if (stored && (stored.startsWith('{') || stored.startsWith('['))) {
        const parsed = JSON.parse(stored)
        const themeData = parsed.state || parsed
        this.userThemes.set(userKey, { ...initialState, ...themeData })
        console.log('✅ 已加载用户主题:', { userKey, theme: themeData })
      } else {
        // 使用默认主题
        this.userThemes.set(userKey, { ...initialState })
        console.log('✅ 使用默认主题:', { userKey })
      }

      // 应用加载的主题
      const theme = this.userThemes.get(userKey)!
      this.applyTheme(theme)
      this.notifyListeners(theme)

    } catch (error) {
      console.warn('⚠️ 加载用户主题失败:', error)
      this.userThemes.set(userKey, { ...initialState })
    }
  }

  // 保存用户主题
  private saveUserTheme(userKey: string, theme: ThemeState): void {
    if (typeof window === 'undefined') return

    try {
      const storageKey = `theme-storage-${userKey}`
      const themeData = {
        state: {
          mode: theme.mode,
          smartThemeSettings: theme.smartThemeSettings
        },
        version: 0
      }
      localStorage.setItem(storageKey, JSON.stringify(themeData))
      console.log('✅ 已保存用户主题:', { userKey, mode: theme.mode })
    } catch (error) {
      console.error('❌ 保存用户主题失败:', error)
    }
  }

  // 应用主题到DOM
  private applyTheme(theme: ThemeState): void {
    if (typeof window === 'undefined') return

    try {
      const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      const isDark = theme.mode === 'dark' || (theme.mode === 'auto' && isSystemDark)

      // 检查当前路径是否为管理界面
      const isAdminPath = window.location.pathname.startsWith('/admin') ||
                         window.location.pathname.startsWith('/factory') ||
                         window.location.pathname.startsWith('/client')

      if (isDark && isAdminPath) {
        document.documentElement.classList.add('dark')
        document.body.classList.add('admin-dark')
      } else {
        document.documentElement.classList.remove('dark')
        document.body.classList.remove('admin-dark')
      }

      console.log('🎨 主题已应用:', isDark ? '深色模式' : '浅色模式')
    } catch (error) {
      console.error('❌ 应用主题失败:', error)
    }
  }

  // 添加监听器
  subscribe(listener: (theme: ThemeState) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  // 通知所有监听器
  private notifyListeners(theme: ThemeState): void {
    this.listeners.forEach(listener => {
      try {
        listener(theme)
      } catch (error) {
        console.error('❌ 主题监听器错误:', error)
      }
    })
  }
}

// 获取主题管理器实例
const themeManager = UserThemeManager.getInstance()

// 创建基于用户的主题store
export const useThemeStore = create<ThemeStore>()((set, get) => ({
  ...initialState,

  getUserThemeKey,

  setMode: (mode) => {
    themeManager.setMode(mode)
    set({ mode })
  },

  setSystemDark: (isSystemDark) => {
    set({ isSystemDark })
    const currentTheme = themeManager.getCurrentTheme()
    themeManager.setMode(currentTheme.mode) // 重新应用主题
  },

      getCurrentTheme: () => {
        const { mode, isSystemDark } = get()

        // 确定当前应该使用的主题模式
        let effectiveMode = mode
        if (mode === 'auto') {
          effectiveMode = isSystemDark ? 'dark' : 'light'
        }

        // 获取对应的主题
        const theme = THEME_PRESETS[effectiveMode as keyof typeof THEME_PRESETS]

        return theme || THEME_PRESETS.light
      },

      applyTheme: () => {
        if (typeof window === 'undefined') return

        try {
          // 延迟执行，确保DOM完全准备好
          requestAnimationFrame(() => {
            const { mode, isSystemDark } = get()
            const root = document.documentElement

            // 设置dark类，但不设置CSS变量到html元素上
            const isDark = mode === 'dark' || (mode === 'auto' && isSystemDark)

            if (isDark) {
              root.classList.add('dark')
            } else {
              root.classList.remove('dark')
            }

            console.log('🎨 主题已应用:', isDark ? '深色模式' : '浅色模式')
          })
        } catch (error) {
          console.error('❌ 主题应用失败:', error)
        }
      }
    }),
    {
      name: 'theme-storage-user',
      partialize: (state) => ({
        mode: state.mode,
        smartThemeSettings: state.smartThemeSettings
      }),
      // 自定义存储实现，支持动态键名
      storage: {
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          try {
            const userKey = getUserThemeKey()
            const actualKey = `theme-storage-${userKey}`
            return localStorage.getItem(actualKey)
          } catch {
            return null
          }
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          try {
            const userKey = getUserThemeKey()
            const actualKey = `theme-storage-${userKey}`
            localStorage.setItem(actualKey, value)
          } catch (error) {
            console.warn('⚠️ 保存主题设置失败:', error)
          }
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          try {
            const userKey = getUserThemeKey()
            const actualKey = `theme-storage-${userKey}`
            localStorage.removeItem(actualKey)
          } catch (error) {
            console.warn('⚠️ 删除主题设置失败:', error)
          }
        }
      }
    }
  )
)

// 监听系统主题变化（仅在客户端）
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

  const handleSystemThemeChange = (e: MediaQueryListEvent) => {
    useThemeStore.getState().setSystemDark(e.matches)
  }

  mediaQuery.addEventListener('change', handleSystemThemeChange)

  // 初始化系统主题状态
  useThemeStore.getState().setSystemDark(mediaQuery.matches)
}
