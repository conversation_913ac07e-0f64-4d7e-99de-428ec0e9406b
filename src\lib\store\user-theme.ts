/**
 * 🇨🇳 风口云平台 - 用户独立主题管理系统
 * 
 * 功能说明：
 * - 每个用户拥有完全独立的主题设置
 * - 用户切换时自动加载对应的主题
 * - 主题设置不会在用户间相互影响
 */

import { create } from 'zustand'

export type ThemeMode = 'light' | 'dark' | 'auto'

interface UserThemeState {
  mode: ThemeMode
  isSystemDark: boolean
}

interface UserThemeStore {
  currentUserKey: string
  userThemes: Record<string, UserThemeState>
  getCurrentTheme: () => UserThemeState
  setCurrentUser: (userKey: string) => void
  setMode: (mode: ThemeMode) => void
  setSystemDark: (isDark: boolean) => void
  applyTheme: () => void
}

// 获取用户特定的主题存储键
function getUserThemeKey(): string {
  if (typeof window === 'undefined') return 'default'
  
  try {
    // 从localStorage读取认证信息
    const authData = localStorage.getItem('auth-storage')
    if (authData) {
      const parsed = JSON.parse(authData)
      const { user, role, factoryId } = parsed.state || {}
      
      if (user && user.id) {
        // 使用用户ID + 角色 + 工厂ID作为唯一标识
        const userKey = `${user.id}_${role}`
        return factoryId ? `${userKey}_${factoryId}` : userKey
      }
    }
  } catch (error) {
    console.warn('⚠️ 获取用户主题键失败:', error)
  }
  
  return 'default'
}

// 从localStorage加载用户主题
function loadUserTheme(userKey: string): UserThemeState {
  if (typeof window === 'undefined') {
    return { mode: 'light', isSystemDark: false }
  }
  
  try {
    const storageKey = `theme-storage-${userKey}`
    const stored = localStorage.getItem(storageKey)
    
    if (stored && (stored.startsWith('{') || stored.startsWith('['))) {
      const parsed = JSON.parse(stored)
      const themeData = parsed.state || parsed
      
      console.log('✅ 已加载用户主题:', { userKey, theme: themeData })
      return {
        mode: themeData.mode || 'light',
        isSystemDark: window.matchMedia('(prefers-color-scheme: dark)').matches
      }
    }
  } catch (error) {
    console.warn('⚠️ 加载用户主题失败:', error)
  }
  
  console.log('✅ 使用默认主题:', { userKey })
  return {
    mode: 'light',
    isSystemDark: window.matchMedia('(prefers-color-scheme: dark)').matches
  }
}

// 保存用户主题到localStorage
function saveUserTheme(userKey: string, theme: UserThemeState): void {
  if (typeof window === 'undefined') return
  
  try {
    const storageKey = `theme-storage-${userKey}`
    const themeData = {
      state: {
        mode: theme.mode
      },
      version: 0
    }
    localStorage.setItem(storageKey, JSON.stringify(themeData))
    console.log('✅ 已保存用户主题:', { userKey, mode: theme.mode })
  } catch (error) {
    console.error('❌ 保存用户主题失败:', error)
  }
}

// 应用主题到DOM
function applyThemeToDOM(theme: UserThemeState): void {
  if (typeof window === 'undefined') return
  
  try {
    const isDark = theme.mode === 'dark' || (theme.mode === 'auto' && theme.isSystemDark)
    
    // 检查当前路径是否为管理界面
    const isAdminPath = window.location.pathname.startsWith('/admin') ||
                       window.location.pathname.startsWith('/factory') ||
                       window.location.pathname.startsWith('/client')

    if (isDark && isAdminPath) {
      document.documentElement.classList.add('dark')
      document.body.classList.add('admin-dark')
    } else {
      document.documentElement.classList.remove('dark')
      document.body.classList.remove('admin-dark')
    }
    
    console.log('🎨 主题已应用:', isDark ? '深色模式' : '浅色模式')
  } catch (error) {
    console.error('❌ 应用主题失败:', error)
  }
}

// 创建用户主题store
export const useUserThemeStore = create<UserThemeStore>((set, get) => ({
  currentUserKey: 'default',
  userThemes: {},

  getCurrentTheme: () => {
    const { currentUserKey, userThemes } = get()
    
    // 如果当前用户的主题不存在，加载它
    if (!userThemes[currentUserKey]) {
      const theme = loadUserTheme(currentUserKey)
      set(state => ({
        userThemes: {
          ...state.userThemes,
          [currentUserKey]: theme
        }
      }))
      return theme
    }
    
    return userThemes[currentUserKey]
  },

  setCurrentUser: (userKey: string) => {
    console.log('🔄 切换用户主题:', { from: get().currentUserKey, to: userKey })
    
    set({ currentUserKey: userKey })
    
    // 加载新用户的主题
    const theme = loadUserTheme(userKey)
    set(state => ({
      userThemes: {
        ...state.userThemes,
        [userKey]: theme
      }
    }))
    
    // 应用主题
    applyThemeToDOM(theme)
  },

  setMode: (mode: ThemeMode) => {
    const { currentUserKey } = get()
    
    console.log('🎨 设置用户主题模式:', { userKey: currentUserKey, mode })
    
    // 更新当前用户的主题
    set(state => {
      const currentTheme = state.userThemes[currentUserKey] || { mode: 'light', isSystemDark: false }
      const newTheme = { ...currentTheme, mode }
      
      return {
        userThemes: {
          ...state.userThemes,
          [currentUserKey]: newTheme
        }
      }
    })
    
    // 获取更新后的主题
    const updatedTheme = get().userThemes[currentUserKey]
    
    // 保存到localStorage
    saveUserTheme(currentUserKey, updatedTheme)
    
    // 应用主题
    applyThemeToDOM(updatedTheme)
  },

  setSystemDark: (isSystemDark: boolean) => {
    const { currentUserKey } = get()
    
    // 更新当前用户的系统暗色模式状态
    set(state => {
      const currentTheme = state.userThemes[currentUserKey] || { mode: 'light', isSystemDark: false }
      const newTheme = { ...currentTheme, isSystemDark }
      
      return {
        userThemes: {
          ...state.userThemes,
          [currentUserKey]: newTheme
        }
      }
    })
    
    // 如果是自动模式，重新应用主题
    const updatedTheme = get().userThemes[currentUserKey]
    if (updatedTheme.mode === 'auto') {
      applyThemeToDOM(updatedTheme)
    }
  },

  applyTheme: () => {
    const theme = get().getCurrentTheme()
    applyThemeToDOM(theme)
  }
}))

// 初始化用户主题系统
export function initializeUserTheme(): void {
  if (typeof window === 'undefined') return
  
  try {
    const userKey = getUserThemeKey()
    const store = useUserThemeStore.getState()
    
    console.log('🚀 初始化用户主题系统:', { userKey })
    
    // 设置当前用户
    store.setCurrentUser(userKey)
    
    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      store.setSystemDark(e.matches)
    }
    
    mediaQuery.addEventListener('change', handleSystemThemeChange)
    store.setSystemDark(mediaQuery.matches)
    
    console.log('✅ 用户主题系统初始化完成')
    
  } catch (error) {
    console.error('❌ 初始化用户主题系统失败:', error)
  }
}

// 当用户登录/切换时调用
export function switchUserTheme(): void {
  const userKey = getUserThemeKey()
  const store = useUserThemeStore.getState()
  
  console.log('🔄 用户切换，更新主题:', { userKey })
  store.setCurrentUser(userKey)
}

// 导出工具函数
export { getUserThemeKey, loadUserTheme, saveUserTheme, applyThemeToDOM }
