/**
 * 🇨🇳 风口云平台 - 工厂路由守卫组件
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 保护工厂端路由，确保只有已认证的工厂用户可以访问
 * - 自动重定向未认证用户到登录页面
 * - 提供加载状态和错误处理
 */

'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

interface FactoryRouteGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function FactoryRouteGuard({ children, fallback }: FactoryRouteGuardProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 检查本地存储的认证信息
        const token = localStorage.getItem('accessToken')
        const userStr = localStorage.getItem('user')
        
        if (!token || !userStr) {
          setIsAuthenticated(false)
          setIsLoading(false)
          return
        }

        const user = JSON.parse(userStr)
        
        // 验证用户类型是否为工厂用户
        if (user.userType !== 'factory_user') {
          setIsAuthenticated(false)
          setIsLoading(false)
          return
        }

        // 可以在这里添加token验证逻辑
        setIsAuthenticated(true)
      } catch (error) {
        console.error('❌ 认证检查失败:', error)
        setIsAuthenticated(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  // 显示加载状态
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">正在验证身份...</p>
          </div>
        </div>
      )
    )
  }

  // 检查用户是否已认证
  if (!isAuthenticated) {
    // 重定向到工厂登录页面
    router.push('/factory/login')
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在跳转到登录页面...</p>
        </div>
      </div>
    )
  }

  // 用户已认证，显示受保护的内容
  return <>{children}</>
}

export default FactoryRouteGuard
