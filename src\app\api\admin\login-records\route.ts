/**
 * 🇨🇳 风口云平台 - 登录记录管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware/auth'

// 获取登录记录
export const GET = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')
    const userType = searchParams.get('userType') as 'admin' | 'factory_user' | null
    const loginStatus = searchParams.get('loginStatus') as 'success' | 'failed' | 'blocked' | null
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    console.log('📋 获取登录记录:', {
      factoryId,
      userType,
      loginStatus,
      limit,
      offset
    })

    const options: any = {
      limit,
      offset
    }

    if (factoryId) {
      options.factoryId = factoryId
    }
    if (userType) {
      options.userType = userType
    }
    if (loginStatus) {
      options.loginStatus = loginStatus
    }

    // 获取登录记录
    const records = await db.getLoginRecords(options)

    return NextResponse.json({
      success: true,
      records,
      pagination: {
        limit,
        offset,
        total: records.length
      }
    })

  } catch (error) {
    console.error('❌ 获取登录记录失败:', error)
    return NextResponse.json(
      { error: '获取登录记录失败' },
      { status: 500 }
    )
  }
})

// 获取登录统计
export const POST = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { factoryId, startDate, endDate } = await request.json()

    console.log('📊 获取登录统计:', {
      factoryId,
      startDate,
      endDate
    })

    // 获取登录统计数据
    const stats = await db.getLoginStats({
      factoryId,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined
    })

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error('❌ 获取登录统计失败:', error)
    return NextResponse.json(
      { error: '获取登录统计失败' },
      { status: 500 }
    )
  }
})
