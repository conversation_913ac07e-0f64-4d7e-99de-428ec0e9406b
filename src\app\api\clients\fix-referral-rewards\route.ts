/**
 * 🇨🇳 风口云平台 - 修复推荐奖励API
 * 
 * 批量修复现有客户的推荐奖励数据
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 收到修复推荐奖励请求')

    // 获取当前工厂ID
    const factoryId = getCurrentFactoryId()
    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '无法获取工厂ID'
      }, { status: 400 })
    }

    console.log('🏭 开始修复工厂推荐奖励:', factoryId)

    // 第一步：批量更新所有客户的基础统计信息（跳过推荐奖励计算）
    console.log('📊 第一步：更新所有客户的基础统计...')
    const allClients = await db.getClientsByFactoryId(factoryId)
    console.log(`📋 找到 ${allClients.length} 个客户`)

    for (const client of allClients) {
      // 传入 true 参数跳过推荐奖励计算，避免重复计算
      await db.updateClientStatistics(client.id, true)
      console.log(`✅ 更新客户统计: ${client.name}`)
    }

    // 第二步：专门处理推荐奖励（统一计算，避免重复）
    console.log('🎁 第二步：批量更新推荐奖励...')
    await db.updateAllReferralRewards(factoryId)

    console.log('✅ 推荐奖励修复完成')
    return NextResponse.json({
      success: true,
      message: '推荐奖励修复完成'
    })

  } catch (error) {
    console.error('❌ 修复推荐奖励失败:', error)
    return NextResponse.json({
      success: false,
      error: `修复失败: ${error.message}`
    }, { status: 500 })
  }
}
