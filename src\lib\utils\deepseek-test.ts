/**
 * DeepSeek API 测试工具
 * 用于验证API配置和连接
 */

import { deepSeekAPI } from '../services/deepseek-api'
import { validateDeepSeekConfig } from '../config/deepseek.config'

/**
 * 测试DeepSeek API连接
 */
export async function testDeepSeekConnection(): Promise<{
  success: boolean
  message: string
  details?: any
}> {
  try {
    // 1. 验证配置
    if (!validateDeepSeekConfig()) {
      return {
        success: false,
        message: 'DeepSeek API配置无效，请检查环境变量'
      }
    }

    // 2. 测试简单的API调用
    console.log('🧪 开始测试DeepSeek API连接...')
    
    const testText = `
测试项目：金色时代10栋1901号房
大厅出风口：300X1500mm
    `

    const result = await deepSeekAPI.analyzeVentOrder(testText.trim())

    if (result && result.projects && result.projects.length > 0) {
      return {
        success: true,
        message: 'DeepSeek API连接成功！',
        details: {
          confidence: result.confidence,
          projectsCount: result.projects.length,
          warnings: result.warnings
        }
      }
    } else {
      return {
        success: false,
        message: 'API调用成功但返回数据格式异常',
        details: result
      }
    }

  } catch (error) {
    console.error('❌ DeepSeek API测试失败:', error)
    
    let errorMessage = 'API连接失败'
    if (error instanceof Error) {
      if (error.message.includes('401')) {
        errorMessage = 'API Key无效，请检查您的DeepSeek API Key'
      } else if (error.message.includes('403')) {
        errorMessage = 'API访问被拒绝，请检查账户余额和权限'
      } else if (error.message.includes('429')) {
        errorMessage = 'API调用频率超限，请稍后重试'
      } else if (error.message.includes('500')) {
        errorMessage = 'DeepSeek服务器错误，请稍后重试'
      } else {
        errorMessage = error.message
      }
    }

    return {
      success: false,
      message: errorMessage,
      details: error
    }
  }
}

/**
 * 获取API使用统计
 */
export async function getAPIUsageStats(): Promise<{
  success: boolean
  data?: {
    totalCalls: number
    successfulCalls: number
    failedCalls: number
    averageResponseTime: number
  }
  message?: string
}> {
  try {
    // 这里可以实现API使用统计逻辑
    // 目前返回模拟数据
    return {
      success: true,
      data: {
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        averageResponseTime: 0
      }
    }
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : '获取统计失败'
    }
  }
}

/**
 * 测试不同模型的性能
 */
export async function testModelPerformance(testText: string): Promise<{
  success: boolean
  results?: Array<{
    model: string
    responseTime: number
    confidence: number
    success: boolean
    error?: string
  }>
  message?: string
}> {
  try {
    const models = ['deepseek-chat', 'deepseek-coder']
    const results = []

    for (const model of models) {
      const startTime = Date.now()
      
      try {
        // 这里需要创建使用特定模型的API实例
        // 目前使用默认模型进行测试
        const result = await deepSeekAPI.analyzeVentOrder(testText)
        const responseTime = Date.now() - startTime

        results.push({
          model,
          responseTime,
          confidence: result.confidence,
          success: true
        })
      } catch (error) {
        results.push({
          model,
          responseTime: Date.now() - startTime,
          confidence: 0,
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    return {
      success: true,
      results
    }
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : '性能测试失败'
    }
  }
}

/**
 * 验证API Key格式
 */
export function validateAPIKeyFormat(apiKey: string): {
  valid: boolean
  message: string
} {
  if (!apiKey) {
    return {
      valid: false,
      message: 'API Key不能为空'
    }
  }

  if (apiKey.length < 20) {
    return {
      valid: false,
      message: 'API Key长度太短，请检查是否完整'
    }
  }

  if (!apiKey.startsWith('sk-')) {
    return {
      valid: false,
      message: 'DeepSeek API Key应该以"sk-"开头'
    }
  }

  return {
    valid: true,
    message: 'API Key格式正确'
  }
}
