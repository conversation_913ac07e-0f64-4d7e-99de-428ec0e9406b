/**
 * 序号和楼层智能识别分析器
 * 专门用于识别各种序号模式和楼层信息，推理数据对应关系
 */

export interface SequenceInfo {
  number: number
  originalText: string
  confidence: number
  position: number // 在文本中的位置
}

export interface FloorInfo {
  floor: string
  originalText: string
  confidence: number
  position: number
}

export interface SequencePattern {
  type: 'continuous' | 'dual_column' | 'floor_based' | 'mixed'
  sequences: SequenceInfo[]
  floors: FloorInfo[]
  columnBreakPoint?: number // 双列模式的分割点
  confidence: number
}

export class SequenceFloorAnalyzer {
  
  /**
   * 分析文本中的序号和楼层模式
   */
  static analyzeSequenceAndFloor(text: string): SequencePattern {
    console.log('🔢 开始序号和楼层分析...')
    
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0)
    
    // 提取序号信息
    const sequences = this.extractSequences(lines)
    console.log('📊 提取到序号:', sequences.map(s => s.number))
    
    // 提取楼层信息
    const floors = this.extractFloors(lines)
    console.log('🏢 提取到楼层:', floors.map(f => f.floor))
    
    // 分析序号模式
    const patternType = this.determinePatternType(sequences, floors)
    console.log('🎯 识别到模式类型:', patternType)
    
    // 计算置信度
    const confidence = this.calculatePatternConfidence(sequences, floors, patternType)
    
    return {
      type: patternType,
      sequences,
      floors,
      columnBreakPoint: this.findColumnBreakPoint(sequences),
      confidence
    }
  }
  
  /**
   * 提取序号信息
   */
  private static extractSequences(lines: string[]): SequenceInfo[] {
    const sequences: SequenceInfo[] = []
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      
      // 匹配各种序号格式
      const patterns = [
        /^(\d+)\./, // 1. 2. 3.
        /^(\d+)、/, // 1、2、3、
        /^(\d+)\s/, // 1 2 3 (后面跟空格)
        /^(\d+)[：:]/, // 1: 2: 3:
        /序号\s*[:：]\s*(\d+)/, // 序号：1
        /第(\d+)项/, // 第1项
      ]
      
      for (const pattern of patterns) {
        const match = line.match(pattern)
        if (match) {
          const number = parseInt(match[1])
          
          // 验证序号的合理性
          if (this.isValidSequenceNumber(number, sequences)) {
            sequences.push({
              number,
              originalText: line,
              confidence: this.calculateSequenceConfidence(match, pattern),
              position: i
            })
            break // 找到匹配就跳出
          }
        }
      }
    }
    
    return sequences.sort((a, b) => a.position - b.position)
  }
  
  /**
   * 提取楼层信息
   */
  private static extractFloors(lines: string[]): FloorInfo[] {
    const floors: FloorInfo[] = []
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      
      // 匹配各种楼层格式
      const patterns = [
        /^(\d+楼)$/, // 1楼 2楼
        /^(\d+层)$/, // 1层 2层
        /^(\d+F)$/i, // 1F 2F
        /^(B\d+楼?)$/i, // B1楼 B2
        /^(地下\d+层)$/, // 地下1层
        /^(负\d+楼)$/, // 负1楼
        /^第(\d+)层$/, // 第1层
        /^(一|二|三|四|五|六|七|八|九|十)[楼层]$/, // 一楼 二层
        /^(地下室)$/, // 地下室
        /^(顶楼|阁楼)$/, // 顶楼 阁楼
      ]
      
      for (const pattern of patterns) {
        const match = line.match(pattern)
        if (match) {
          const floor = match[1] || match[0]
          
          floors.push({
            floor,
            originalText: line,
            confidence: this.calculateFloorConfidence(match, pattern),
            position: i
          })
          break
        }
      }
    }
    
    return floors.sort((a, b) => a.position - b.position)
  }
  
  /**
   * 验证序号的合理性
   */
  private static isValidSequenceNumber(number: number, existingSequences: SequenceInfo[]): boolean {
    // 序号应该在合理范围内
    if (number < 1 || number > 100) return false
    
    // 避免重复序号（除非是双列模式）
    const existing = existingSequences.find(s => s.number === number)
    if (existing) {
      // 如果已存在相同序号，检查是否可能是双列模式
      return existingSequences.length > 5 // 只有在有足够数据时才允许重复
    }
    
    return true
  }
  
  /**
   * 计算序号置信度
   */
  private static calculateSequenceConfidence(match: RegExpMatchArray, pattern: RegExp): number {
    let confidence = 0.7
    
    // 基于匹配模式调整置信度
    if (pattern.source.includes('\\.')) confidence += 0.2 // 1. 格式最常见
    if (pattern.source.includes('、')) confidence += 0.15 // 1、格式也很常见
    if (match[0].length === match.input?.length) confidence += 0.1 // 整行匹配
    
    return Math.min(confidence, 1.0)
  }
  
  /**
   * 计算楼层置信度
   */
  private static calculateFloorConfidence(match: RegExpMatchArray, pattern: RegExp): number {
    let confidence = 0.8
    
    // 基于匹配模式调整置信度
    if (pattern.source.includes('楼')) confidence += 0.1
    if (pattern.source.includes('层')) confidence += 0.1
    if (match[0].length === match.input?.length) confidence += 0.1 // 整行匹配
    
    return Math.min(confidence, 1.0)
  }
  
  /**
   * 确定序号模式类型
   */
  private static determinePatternType(sequences: SequenceInfo[], floors: FloorInfo[]): SequencePattern['type'] {
    // 如果有楼层信息，优先考虑楼层模式
    if (floors.length > 0) {
      return 'floor_based'
    }

    // 分析序号跳跃模式 - 特别检查双列模式
    if (sequences.length >= 4) {
      const numbers = sequences.map(s => s.number).sort((a, b) => a - b)

      // 检查是否符合双列模式：1,2,3...7,8,9...14
      if (this.isDualColumnPattern(numbers)) {
        console.log('🎯 检测到双列模式:', numbers)
        return 'dual_column'
      }

      const hasJump = this.hasSignificantJump(numbers)
      if (hasJump) {
        return 'dual_column'
      }
    }

    // 检查是否为连续序号
    if (this.isContinuousSequence(sequences)) {
      return 'continuous'
    }

    return 'mixed'
  }

  /**
   * 检查是否为双列模式
   */
  private static isDualColumnPattern(numbers: number[]): boolean {
    if (numbers.length < 6) return false

    // 查找最大的跳跃点
    let maxJump = 0
    let jumpIndex = -1

    for (let i = 1; i < numbers.length; i++) {
      const diff = numbers[i] - numbers[i-1]
      if (diff > maxJump && diff > 1) {
        maxJump = diff
        jumpIndex = i
      }
    }

    // 如果有显著跳跃，检查是否符合双列模式
    if (jumpIndex > 0 && maxJump >= 3) {
      const firstPart = numbers.slice(0, jumpIndex)
      const secondPart = numbers.slice(jumpIndex)

      // 检查第一部分是否连续
      const firstContinuous = this.isArrayContinuous(firstPart)
      // 检查第二部分是否连续
      const secondContinuous = this.isArrayContinuous(secondPart)

      console.log('🔍 双列检查:', {
        firstPart,
        secondPart,
        firstContinuous,
        secondContinuous,
        jumpSize: maxJump
      })

      return firstContinuous && secondContinuous
    }

    return false
  }

  /**
   * 检查数组是否连续
   */
  private static isArrayContinuous(arr: number[]): boolean {
    if (arr.length <= 1) return true

    for (let i = 1; i < arr.length; i++) {
      if (arr[i] !== arr[i-1] + 1) {
        return false
      }
    }
    return true
  }
  
  /**
   * 检查是否有显著的序号跳跃
   */
  private static hasSignificantJump(numbers: number[]): boolean {
    for (let i = 1; i < numbers.length; i++) {
      const diff = numbers[i] - numbers[i-1]
      if (diff > 3) { // 跳跃超过3认为是显著跳跃
        return true
      }
    }
    return false
  }
  
  /**
   * 检查是否为连续序号
   */
  private static isContinuousSequence(sequences: SequenceInfo[]): boolean {
    if (sequences.length < 2) return false
    
    const numbers = sequences.map(s => s.number).sort((a, b) => a - b)
    for (let i = 1; i < numbers.length; i++) {
      if (numbers[i] !== numbers[i-1] + 1) {
        return false
      }
    }
    return true
  }
  
  /**
   * 查找双列模式的分割点
   */
  private static findColumnBreakPoint(sequences: SequenceInfo[]): number | undefined {
    if (sequences.length < 4) return undefined
    
    const numbers = sequences.map(s => s.number)
    
    // 查找最大的跳跃点
    let maxJump = 0
    let breakPoint = undefined
    
    for (let i = 1; i < numbers.length; i++) {
      const diff = numbers[i] - numbers[i-1]
      if (diff > maxJump && diff > 3) {
        maxJump = diff
        breakPoint = numbers[i-1]
      }
    }
    
    return breakPoint
  }
  
  /**
   * 计算模式置信度
   */
  private static calculatePatternConfidence(
    sequences: SequenceInfo[], 
    floors: FloorInfo[], 
    patternType: SequencePattern['type']
  ): number {
    let confidence = 0.5
    
    // 基于序号数量
    if (sequences.length >= 5) confidence += 0.2
    if (sequences.length >= 10) confidence += 0.1
    
    // 基于楼层信息
    if (floors.length > 0) confidence += 0.2
    
    // 基于模式类型
    switch (patternType) {
      case 'continuous':
        confidence += 0.2
        break
      case 'dual_column':
        confidence += 0.15
        break
      case 'floor_based':
        confidence += 0.25
        break
    }
    
    return Math.min(confidence, 1.0)
  }
}
