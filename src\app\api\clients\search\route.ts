/**
 * 🇨🇳 风口云平台 - 客户搜索API
 *
 * 支持多种搜索方式：手机号、姓名、公司名称、智能搜索
 * 支持公开访问和认证访问两种模式
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminOrFactoryAuth } from '@/lib/middleware/auth'
import { getUserFromRequest } from '@/lib/auth/jwt'

// 公开搜索处理函数
async function handlePublicSearch(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const searchTerm = searchParams.get('term')
    const searchType = searchParams.get('type') || 'all'
    const isPublic = searchParams.get('public') === 'true'

    console.log('🔍 公开客户搜索API请求:', { searchTerm, searchType, isPublic })

    if (!searchTerm || !searchTerm.trim()) {
      return NextResponse.json({
        success: false,
        error: '搜索关键词不能为空'
      }, { status: 400 })
    }

    // 公开搜索只允许精确匹配手机号
    if (isPublic && searchType !== 'phone') {
      return NextResponse.json({
        success: false,
        error: '公开搜索仅支持手机号查询'
      }, { status: 400 })
    }

    // 验证手机号格式
    if (isPublic && !/^1[3-9]\d{9}$/.test(searchTerm.trim())) {
      return NextResponse.json({
        success: false,
        error: '请输入正确的手机号码格式'
      }, { status: 400 })
    }

    // 获取所有客户数据进行搜索
    const allClients = await db.getAllClients()
    console.log('📋 公开搜索 - 总客户数量:', allClients.length)

    let filteredClients: unknown[] = []

    if (isPublic) {
      // 公开搜索：只允许精确匹配手机号
      filteredClients = allClients.filter(client =>
        client.phone && client.phone === searchTerm.trim()
      )
    } else {
      // 内部搜索：支持模糊匹配
      switch (searchType) {
        case 'phone':
          filteredClients = allClients.filter(client =>
            client.phone && client.phone.includes(searchTerm.trim())
          )
          break

        case 'name':
          filteredClients = allClients.filter(client =>
            client.name && client.name.toLowerCase().includes(searchTerm.trim().toLowerCase())
          )
          break

        case 'company':
          filteredClients = allClients.filter(client =>
            client.company && client.company.toLowerCase().includes(searchTerm.trim().toLowerCase())
          )
          break

        case 'all':
        default:
          const term = searchTerm.trim().toLowerCase()
          filteredClients = allClients.filter(client => {
            return (
              (client.phone && client.phone.includes(searchTerm.trim())) ||
              (client.name && client.name.toLowerCase().includes(term)) ||
              (client.company && client.company.toLowerCase().includes(term)) ||
              (client.address && client.address.toLowerCase().includes(term)) ||
              (client.email && client.email.toLowerCase().includes(term))
            )
          })
          break
      }
    }

    console.log('✅ 搜索完成，找到', filteredClients.length, '个匹配客户')

    // 按相关性排序（优先显示完全匹配的结果）
    filteredClients.sort((a, b) => {
      const term = searchTerm.trim().toLowerCase()

      // 手机号完全匹配优先
      if (a.phone === searchTerm.trim()) return -1
      if (b.phone === searchTerm.trim()) return 1

      // 姓名完全匹配优先
      if (a.name && a.name.toLowerCase() === term) return -1
      if (b.name && b.name.toLowerCase() === term) return 1

      // 按创建时间倒序
      const aTime = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt)
      const bTime = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt)
      return bTime.getTime() - aTime.getTime()
    })

    return NextResponse.json({
      success: true,
      clients: filteredClients,
      total: filteredClients.length,
      searchTerm,
      searchType,
      isPublic
    })

  } catch (error) {
    console.error('❌ 公开客户搜索API错误:', error)
    return NextResponse.json({
      success: false,
      error: '搜索失败，请重试'
    }, { status: 500 })
  }
}

// 认证搜索处理函数
const handleAuthenticatedSearch = withAdminOrFactoryAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const searchTerm = searchParams.get('term')
    const searchType = searchParams.get('type') || 'all'

    console.log('🔍 认证客户搜索API请求:', { searchTerm, searchType, userType: user.userType })

    if (!searchTerm || !searchTerm.trim()) {
      return NextResponse.json({
        success: false,
        error: '搜索关键词不能为空'
      }, { status: 400 })
    }

    // 根据用户权限获取客户数据
    let allClients = []

    if (user.userType === 'admin') {
      // 管理员可以搜索所有客户
      allClients = await db.getAllClients()
      console.log('📋 管理员搜索 - 总客户数量:', allClients.length)
    } else if (user.factoryId) {
      // 工厂用户只能搜索自己工厂的客户
      allClients = await db.getClientsByFactoryId(user.factoryId)
      console.log('📋 工厂用户搜索 - 工厂客户数量:', allClients.length, '工厂ID:', user.factoryId)
    } else {
      console.log('❌ 用户没有工厂权限')
      return NextResponse.json({
        success: false,
        error: '无权限搜索客户'
      }, { status: 403 })
    }

    let filteredClients = []

    switch (searchType) {
      case 'phone':
        // 手机号搜索 - 精确匹配或部分匹配
        filteredClients = allClients.filter(client =>
          client.phone && client.phone.includes(searchTerm.trim())
        )
        break

      case 'name':
        // 姓名搜索 - 模糊匹配
        filteredClients = allClients.filter(client =>
          client.name && client.name.toLowerCase().includes(searchTerm.trim().toLowerCase())
        )
        break

      case 'company':
        // 公司名称搜索 - 模糊匹配
        filteredClients = allClients.filter(client =>
          client.company && client.company.toLowerCase().includes(searchTerm.trim().toLowerCase())
        )
        break

      case 'all':
      default:
        // 智能搜索 - 在所有字段中搜索
        const term = searchTerm.trim().toLowerCase()
        filteredClients = allClients.filter(client => {
          return (
            (client.phone && client.phone.includes(searchTerm.trim())) ||
            (client.name && client.name.toLowerCase().includes(term)) ||
            (client.company && client.company.toLowerCase().includes(term)) ||
            (client.address && client.address.toLowerCase().includes(term)) ||
            (client.email && client.email.toLowerCase().includes(term))
          )
        })
        break
    }

    console.log('✅ 认证搜索完成，找到', filteredClients.length, '个匹配客户')

    // 按相关性排序（优先显示完全匹配的结果）
    filteredClients.sort((a, b) => {
      const term = searchTerm.trim().toLowerCase()

      // 手机号完全匹配优先
      if (a.phone === searchTerm.trim()) return -1
      if (b.phone === searchTerm.trim()) return 1

      // 姓名完全匹配优先
      if (a.name && a.name.toLowerCase() === term) return -1
      if (b.name && b.name.toLowerCase() === term) return 1

      // 按创建时间倒序
      const aTime = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt)
      const bTime = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt)
      return bTime.getTime() - aTime.getTime()
    })

    return NextResponse.json({
      success: true,
      clients: filteredClients,
      total: filteredClients.length,
      searchTerm,
      searchType
    })

  } catch (error) {
    console.error('❌ 认证客户搜索API错误:', error)
    return NextResponse.json({
      success: false,
      error: '搜索失败，请重试'
    }, { status: 500 })
  }
})

// 主要的GET处理函数
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const isPublic = searchParams.get('public') === 'true'

  if (isPublic) {
    // 公开搜索，不需要认证
    return handlePublicSearch(request)
  } else {
    // 认证搜索，需要登录
    return handleAuthenticatedSearch(request)
  }
}
