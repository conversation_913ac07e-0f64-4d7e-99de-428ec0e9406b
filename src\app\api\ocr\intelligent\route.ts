import { NextRequest, NextResponse } from 'next/server'
import sharp from 'sharp'
import { SpatialTextAnalyzer } from '@/lib/spatial-text-analyzer'
import { TableLayoutAnalyzer } from '@/lib/table-layout-analyzer'

// 配置 API 路由的请求体大小限制
export const runtime = 'nodejs'
export const maxDuration = 60 // 60秒超时
export const dynamic = 'force-dynamic'

// 配置请求体大小限制
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
    responseLimit: '50mb',
  },
}

// 百度OCR配置
const BAIDU_OCR_CONFIG = {
  API_KEY: 'XYi8m9nIgFvHQ1jOpr20TpAr',
  SECRET_KEY: 'P7ieKMlibFjDu0jXObf2frJ1wx3IXh9g',
  APP_ID: '119490576',
  // API端点
  TOKEN_URL: 'https://aip.baidubce.com/oauth/2.0/token',
  // 文字识别API
  OCR_GENERAL_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic',
  OCR_ACCURATE_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic',
  // 手写文字识别API (专门针对手写内容)
  HANDWRITING_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting',
  // 表格识别API (V2版本，更准确)
  TABLE_OCR_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/table',
  TABLE_OCR_V2_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/table_recognize',
  // 通用文字识别（含位置信息版）
  OCR_GENERAL_ENHANCED_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/general',
  OCR_ACCURATE_ENHANCED_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/accurate',
}

// Access Token缓存
let cachedToken: string | null = null
let tokenExpireTime: number = 0

// 获取百度OCR Access Token
async function getBaiduAccessToken(): Promise<string> {
  const now = Date.now()
  
  // 如果token还有效，直接返回
  if (cachedToken && now < tokenExpireTime) {
    return cachedToken
  }

  try {
    console.log('🔑 获取百度OCR Access Token...')
    
    const params = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: BAIDU_OCR_CONFIG.API_KEY,
      client_secret: BAIDU_OCR_CONFIG.SECRET_KEY
    })

    const response = await fetch(BAIDU_OCR_CONFIG.TOKEN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params
    })

    if (!response.ok) {
      throw new Error(`获取token失败: ${response.status}`)
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(`Token错误: ${data.error_description}`)
    }

    cachedToken = data.access_token
    // 提前5分钟过期，确保token有效性
    tokenExpireTime = now + (data.expires_in - 300) * 1000
    
    console.log('✅ Access Token获取成功')
    return cachedToken

  } catch (error) {
    console.error('❌ 获取Access Token失败:', error)
    throw error
  }
}

// 将文件转换为Base64
async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const result = reader.result as string
      // 移除data:image/...;base64,前缀
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

// 图像预处理函数（提升手写/表单准确性）
async function preprocessImage(file: File): Promise<string> {
  try {
    console.log('🔧 开始图像预处理...')
    const buffer = await file.arrayBuffer()

    // 使用sharp进行图像预处理
    const processed = await sharp(Buffer.from(buffer))
      .grayscale() // 灰度化，减少颜色干扰
      .blur(0.5) // 轻微模糊去噪
      .normalize() // 标准化对比度
      .threshold(128) // 二值化，提升文字对比度
      .toBuffer()

    const base64 = processed.toString('base64')
    console.log('✅ 图像预处理完成')
    return base64
  } catch (error) {
    console.warn('⚠️ 图像预处理失败，使用原图:', error)
    // 预处理失败时回退到原始图像
    return await fileToBase64(file)
  }
}

// 智能图像质量分析
async function analyzeImageQuality(file: File): Promise<{
  brightness: number
  contrast: number
  sharpness: number
  noise: number
  recommendedMode: 'text' | 'layout' | 'mixed' | 'handwriting'
}> {
  try {
    console.log('🔍 分析图像质量...')
    const buffer = await file.arrayBuffer()
    const image = sharp(Buffer.from(buffer))
    const { width, height } = await image.metadata()

    // 获取图像统计信息
    const stats = await image.stats()
    const channels = stats.channels

    // 计算亮度（基于灰度值）
    const brightness = channels[0].mean / 255

    // 计算对比度（基于标准差）
    const contrast = channels[0].stdev / 128

    // 简单的清晰度估算（基于边缘检测）
    const edgeDetected = await image
      .grayscale()
      .convolve({
        width: 3,
        height: 3,
        kernel: [-1, -1, -1, -1, 8, -1, -1, -1, -1]
      })
      .stats()

    const sharpness = edgeDetected.channels[0].stdev / 128

    // 噪声估算（基于高频成分）
    const noise = Math.min(channels[0].stdev / channels[0].mean, 1)

    // 根据分析结果推荐预处理模式
    let recommendedMode: 'text' | 'layout' | 'mixed' | 'handwriting' = 'mixed'

    if (brightness < 0.3 || brightness > 0.8) {
      recommendedMode = 'text' // 亮度异常，需要文字优化
    } else if (contrast < 0.3) {
      recommendedMode = 'text' // 对比度低，需要增强
    } else if (sharpness < 0.2) {
      recommendedMode = 'text' // 模糊图像，需要锐化
    } else if (noise > 0.4) {
      recommendedMode = 'handwriting' // 噪声大，可能是手写
    } else if (sharpness > 0.6 && contrast > 0.5) {
      recommendedMode = 'layout' // 清晰度高，适合排版识别
    }

    console.log(`📊 图像质量分析: 亮度=${brightness.toFixed(2)}, 对比度=${contrast.toFixed(2)}, 清晰度=${sharpness.toFixed(2)}, 噪声=${noise.toFixed(2)}`)
    console.log(`💡 推荐预处理模式: ${recommendedMode}`)

    return { brightness, contrast, sharpness, noise, recommendedMode }
  } catch (error) {
    console.warn('⚠️ 图像质量分析失败，使用默认模式:', error)
    return { brightness: 0.5, contrast: 0.5, sharpness: 0.5, noise: 0.3, recommendedMode: 'mixed' }
  }
}

// 增强的多尺度图像预处理
async function enhancedPreprocessImage(file: File, mode: 'text' | 'layout' | 'mixed' | 'handwriting' = 'mixed'): Promise<string> {
  try {
    console.log(`🔧 开始增强图像预处理 (${mode}模式)...`)

    // 先分析图像质量
    const quality = await analyzeImageQuality(file)

    const buffer = await file.arrayBuffer()
    let processor = sharp(Buffer.from(buffer))

    // 基础处理
    processor = processor
      .resize({ width: 2000, height: 2000, fit: 'inside', withoutEnlargement: true })
      .grayscale()

    // 根据模式和图像质量选择不同的处理策略
    switch (mode) {
      case 'text':
        // 专门优化文字识别
        const textBlur = quality.noise > 0.4 ? 0.5 : 0.3
        const textContrast = quality.contrast < 0.3 ? 1.5 : 1.3
        const textThreshold = quality.brightness < 0.4 ? 100 : (quality.brightness > 0.7 ? 130 : 115)

        processor = processor
          .blur(textBlur)
          .normalize()
          .linear(textContrast, -40)
          .sharpen({ sigma: 1, m1: 0.5, m2: 2 })
          .threshold(textThreshold)
        break

      case 'layout':
        // 专门优化排版识别
        const layoutThreshold = quality.contrast < 0.4 ? 120 : 130

        processor = processor
          .normalize()
          .convolve({
            width: 3,
            height: 3,
            kernel: [0, -1, 0, -1, 5, -1, 0, -1, 0] // 增强边缘
          })
          .threshold(layoutThreshold)
        break

      case 'handwriting':
        // 专门优化手写识别
        processor = processor
          .blur(0.6) // 更强的去噪
          .normalize()
          .linear(1.4, -50) // 更强的对比度增强
          .convolve({
            width: 3,
            height: 3,
            kernel: [0, -1, 0, -1, 6, -1, 0, -1, 0] // 温和的边缘增强
          })
          .threshold(110) // 更低的阈值，保留更多细节
        break

      case 'mixed':
      default:
        // 平衡处理，根据图像质量自适应调整
        const mixedBlur = quality.noise > 0.3 ? 0.5 : 0.4
        const mixedContrast = quality.contrast < 0.4 ? 1.3 : 1.2
        const mixedThreshold = quality.brightness < 0.4 ? 110 : (quality.brightness > 0.7 ? 130 : 120)

        processor = processor
          .blur(mixedBlur)
          .normalize()
          .linear(mixedContrast, -30)
          .convolve({
            width: 3,
            height: 3,
            kernel: [-1, -1, -1, -1, 8, -1, -1, -1, -1] // 锐化
          })
          .threshold(mixedThreshold)
        break
    }

    const processed = await processor.toBuffer()
    const base64 = processed.toString('base64')

    console.log('✅ 增强图像预处理完成')
    return base64
  } catch (error) {
    console.warn('⚠️ 增强预处理失败，回退到基础预处理:', error)
    return await preprocessImage(file)
  }
}

// 抽象通用OCR调用函数（减少重复代码）
async function performOCR(
  url: string,
  file: File,
  params: Record<string, string>,
  isHandwriting: boolean = false
): Promise<any> {
  try {
    console.log(`🔍 执行OCR调用: ${url}`)

    // 获取访问令牌
    const accessToken = await getBaiduAccessToken()

    // 根据是否为手写内容选择预处理策略
    const imageBase64 = isHandwriting ? await preprocessImage(file) : await fileToBase64(file)

    const formData = new URLSearchParams({
      image: imageBase64,
      ...params
    })

    const response = await fetch(`${url}?access_token=${accessToken}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: formData
    })

    if (!response.ok) {
      throw new Error(`OCR API调用失败: ${response.status}`)
    }

    const result = await response.json()

    if (result.error_code) {
      throw new Error(`OCR错误: ${result.error_msg}`)
    }

    return result
  } catch (error) {
    console.error('❌ OCR调用失败:', error)
    throw error
  }
}

// 优化OCR参数配置
function getOptimizedOCRParams(recognitionType: string, isHandwriting: boolean = false) {
  const baseParams = {
    language_type: 'CHN_ENG',
    detect_direction: 'true',
    paragraph: 'true',
    probability: 'true'
  }

  if (isHandwriting) {
    return {
      ...baseParams,
      recognize_granularity: 'big',
      words_type: 'handwring_only'
    }
  }

  if (recognitionType === 'table') {
    return {
      language_type: 'CHN_ENG',
      detect_direction: 'true',
      return_excel: 'false',
      cell_contents: 'true',
      request_type: 'excel'
    }
  }

  // 针对手写清单的优化参数
  return {
    ...baseParams,
    vertexes_location: 'true', // 返回文字外接多边形顶点位置
    recognize_granularity: 'big', // 识别粒度
    detect_language: 'true' // 检测语言
  }
}

// 智能检测图片内容类型
async function detectImageContentType(file: File): Promise<'text' | 'table'> {
  try {
    console.log('🔍 智能检测图片内容类型...')

    // 获取访问令牌
    const accessToken = await getBaiduAccessToken()

    // 首先尝试表格识别，如果检测到表格结构则认为是表格
    const imageBase64 = await fileToBase64(file)

    const formData = new URLSearchParams({
      image: imageBase64,
      return_excel: 'false'
    })

    const response = await fetch(`${BAIDU_OCR_CONFIG.TABLE_OCR_URL}?access_token=${accessToken}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: formData
    })

    if (response.ok) {
      const result = await response.json()
      
      // 如果检测到表格且表格数量大于0，认为是表格
      if (result.table_num && result.table_num > 0) {
        console.log('📊 检测到表格结构，使用表格识别')
        return 'table'
      }
    }
    
    console.log('📝 未检测到表格结构，使用文字识别')
    return 'text'
    
  } catch (error) {
    console.warn('⚠️ 内容类型检测失败，默认使用文字识别:', error)
    return 'text'
  }
}

// 智能检测是否为手写内容（基于多维度分析）
async function detectHandwriting(imageBase64: string, accessToken: string): Promise<boolean> {
  try {
    console.log('🔍 智能检测手写内容...')

    // 先用通用OCR快速检测置信度
    const formData = new URLSearchParams({
      image: imageBase64,
      language_type: 'CHN_ENG',
      detect_direction: 'true',
      probability: 'true'
    })

    const response = await fetch(`${BAIDU_OCR_CONFIG.OCR_GENERAL_URL}?access_token=${accessToken}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: formData
    })

    if (!response.ok) {
      console.log('⚠️ 手写检测API调用失败，默认为非手写')
      return false
    }

    const result = await response.json()

    if (result.error_code || !result.words_result || result.words_result.length === 0) {
      console.log('⚠️ 手写检测结果异常，默认为非手写')
      return false
    }

    const words = result.words_result
    const confidences = words.map((word: any) => word.probability?.average || 0.8)

    // 1. 计算平均置信度
    const avgConfidence = confidences.reduce((sum: number, conf: number) => sum + conf, 0) / confidences.length

    // 2. 计算置信度分布（手写文字置信度通常更不均匀）
    const stdDev = calculateStandardDeviation(confidences)

    // 3. 计算低置信度文字比例
    const lowConfidenceCount = confidences.filter(conf => conf < 0.7).length
    const lowConfidenceRatio = lowConfidenceCount / confidences.length

    // 4. 分析文字长度分布（手写文字长度通常更不规律）
    const wordLengths = words.map((word: any) => word.words.length)
    const avgWordLength = wordLengths.reduce((sum: number, len: number) => sum + len, 0) / wordLengths.length
    const wordLengthStdDev = calculateStandardDeviation(wordLengths)

    // 综合判断逻辑（更加智能和准确）
    let handwritingScore = 0

    // 置信度因素（权重40%）
    if (avgConfidence < 0.65) handwritingScore += 40
    else if (avgConfidence < 0.72) handwritingScore += 25
    else if (avgConfidence < 0.78) handwritingScore += 10

    // 置信度分布因素（权重25%）
    if (stdDev > 0.15) handwritingScore += 25
    else if (stdDev > 0.10) handwritingScore += 15
    else if (stdDev > 0.08) handwritingScore += 8

    // 低置信度比例因素（权重20%）
    if (lowConfidenceRatio > 0.4) handwritingScore += 20
    else if (lowConfidenceRatio > 0.25) handwritingScore += 12
    else if (lowConfidenceRatio > 0.15) handwritingScore += 6

    // 文字长度不规律性因素（权重15%）
    const lengthIrregularity = wordLengthStdDev / Math.max(avgWordLength, 1)
    if (lengthIrregularity > 0.8) handwritingScore += 15
    else if (lengthIrregularity > 0.6) handwritingScore += 10
    else if (lengthIrregularity > 0.4) handwritingScore += 5

    // 判断阈值：60分以上认为是手写
    const isHandwriting = handwritingScore >= 60

    console.log(`📊 手写检测详细分析:`)
    console.log(`   平均置信度: ${avgConfidence.toFixed(3)}`)
    console.log(`   置信度标准差: ${stdDev.toFixed(3)}`)
    console.log(`   低置信度比例: ${(lowConfidenceRatio * 100).toFixed(1)}%`)
    console.log(`   文字长度不规律性: ${lengthIrregularity.toFixed(3)}`)
    console.log(`   手写评分: ${handwritingScore}/100`)
    console.log(`   判断结果: ${isHandwriting ? '手写' : '打印'}内容`)

    return isHandwriting

  } catch (error) {
    console.log('⚠️ 手写检测失败，默认为非手写:', error)
    return false
  }
}

// 执行手写文字识别
async function performHandwritingOCR(file: File) {
  try {
    console.log('✍️ 执行手写文字识别...')

    const params = {
      recognize_granularity: 'big', // 识别粒度：big(整体识别) / small(单字识别)
      language_type: 'CHN_ENG',
      detect_direction: 'true',
      words_type: 'handwring_only' // 只识别手写文字
    }

    // 使用抽象函数，启用预处理
    const result = await performOCR(BAIDU_OCR_CONFIG.HANDWRITING_URL, file, params, true)

    const words = result.words_result || []
    let mergedText = words.map((item: any) => item.words).join('\n')

    // 后处理：校正常见手写错误
    mergedText = postProcessHandwritingText(mergedText)

    console.log(`✅ 手写识别完成，识别到 ${words.length} 行文字`)

    return {
      success: true,
      type: 'text',
      data: {
        text: mergedText,
        words_result: words,
        mergedText: mergedText,
        recognitionMethod: 'handwriting'
      }
    }

  } catch (error) {
    console.error('❌ 手写识别失败:', error)
    throw error
  }
}

// 后处理手写文本，校正常见错误
function postProcessHandwritingText(text: string): string {
  return text
    // 数字校正
    .replace(/O/g, '0')  // 字母O替换为数字0
    .replace(/I/g, '1')  // 字母I替换为数字1
    .replace(/l/g, '1')  // 小写l替换为数字1
    .replace(/S/g, '5')  // 在数字上下文中，S可能是5
    // 常见符号校正
    .replace(/×/g, 'x')  // 统一乘号
    .replace(/X/g, 'x')  // 统一乘号
    // 去除多余空格
    .replace(/\s+/g, ' ')
    .trim()
}

// 执行增强版文字识别（含位置信息）
async function performEnhancedTextOCR(file: File, useAccurate: boolean = true) {
  try {
    console.log(`📝 执行增强文字识别 (${useAccurate ? '高精度' : '标准'}模式)...`)

    const ocrUrl = useAccurate ? BAIDU_OCR_CONFIG.OCR_ACCURATE_ENHANCED_URL : BAIDU_OCR_CONFIG.OCR_GENERAL_ENHANCED_URL

    const params = {
      language_type: 'CHN_ENG',
      detect_direction: 'true',
      paragraph: 'true',
      probability: 'true',
      detect_language: 'true', // 检测语言
      vertexes_location: 'true', // 返回文字外接多边形顶点位置
      recognize_granularity: 'big' // 识别粒度
    }

    // 使用抽象函数
    const result = await performOCR(ocrUrl, file, params)

    // 处理识别结果
    const words = result.words_result || []
    const allText = words.map((item: any) => item.words).join('\n')

    console.log(`✅ 增强文字识别完成，识别到 ${words.length} 行文字`)

    return {
      success: true,
      type: 'text',
      data: {
        mergedText: allText,
        words_result: words,
        words_result_num: result.words_result_num || 0,
        direction: result.direction,
        paragraphs_result: result.paragraphs_result,
        recognitionMethod: 'enhanced',
        language: result.language || 'unknown',
        statistics: {
          total: 1,
          success: 1,
          failed: 0,
          totalWords: words.length,
          totalChars: allText.length
        }
      }
    }

  } catch (error) {
    console.error('❌ 增强文字识别失败:', error)
    throw error
  }
}

// 执行标准文字识别（兼容旧版本）
async function performStandardTextOCR(file: File, useAccurate: boolean = true) {
  try {
    console.log(`📝 执行标准文字识别 (${useAccurate ? '高精度' : '标准'}模式)...`)

    const ocrUrl = useAccurate ? BAIDU_OCR_CONFIG.OCR_ACCURATE_URL : BAIDU_OCR_CONFIG.OCR_GENERAL_URL

    const params = {
      language_type: 'CHN_ENG',
      detect_direction: 'true',
      paragraph: 'true',
      probability: 'true'
    }

    // 使用抽象函数
    const result = await performOCR(ocrUrl, file, params)

    const words = result.words_result || []
    const mergedText = words.map((item: any) => item.words).join('\n')

    console.log(`✅ 标准文字识别完成，识别到 ${words.length} 行文字`)

    return {
      success: true,
      type: 'text',
      data: {
        text: mergedText,
        words_result: words,
        mergedText: mergedText,
        recognitionMethod: 'standard'
      }
    }

  } catch (error) {
    console.error('❌ 标准文字识别失败:', error)
    throw error
  }
}

// 多尺度OCR识别（新增）
async function performMultiScaleOCR(file: File, useAccurate: boolean = true) {
  try {
    console.log('🎯 执行多尺度OCR识别...')

    const ocrUrl = useAccurate ? BAIDU_OCR_CONFIG.OCR_ACCURATE_URL : BAIDU_OCR_CONFIG.OCR_GENERAL_URL

    // 使用增强的预处理
    const accessToken = await getBaiduAccessToken()
    const enhancedImage = await enhancedPreprocessImage(file, 'mixed')

    const params = {
      language_type: 'CHN_ENG',
      detect_direction: 'true',
      paragraph: 'true',
      probability: 'true',
      recognize_granularity: 'big', // 大粒度识别
      words_type: 'location' // 包含位置信息
    }

    const formData = new URLSearchParams({
      image: enhancedImage,
      ...params
    })

    const response = await fetch(`${ocrUrl}?access_token=${accessToken}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: formData
    })

    if (!response.ok) {
      throw new Error(`多尺度OCR API调用失败: ${response.status}`)
    }

    const result = await response.json()
    const words = result.words_result || []

    // 后处理：增强备注和楼层信息识别
    const enhancedWords = enhanceNotesAndFloorRecognition(words)
    const mergedText = enhancedWords.map((item: any) => item.words).join('\n')

    console.log(`✅ 多尺度识别完成，识别到 ${enhancedWords.length} 行文字`)

    return {
      success: true,
      type: 'text',
      data: {
        text: mergedText,
        words_result: enhancedWords,
        mergedText: mergedText,
        recognitionMethod: 'multiscale',
        enhanced: true
      }
    }

  } catch (error) {
    console.error('❌ 多尺度识别失败:', error)
    throw error
  }
}

// 增强备注和楼层信息识别
function enhanceNotesAndFloorRecognition(words: any[]): any[] {
  console.log('🔍 增强备注和楼层信息识别...')

  return words.map((word, index) => {
    const originalText = word.words
    let enhancedText = originalText

    // 楼层信息修正
    enhancedText = enhancedText
      .replace(/([一二三四五六七八九十])搂/g, '$1楼') // 搂 -> 楼
      .replace(/([一二三四五六七八九十])屋/g, '$1楼') // 屋 -> 楼
      .replace(/(\d+)搂/g, '$1楼') // 数字+搂 -> 楼
      .replace(/(\d+)屋/g, '$1楼') // 数字+屋 -> 楼

    // 房间信息修正
    enhancedText = enhancedText
      .replace(/客广/g, '客厅') // 客广 -> 客厅
      .replace(/歺厅/g, '餐厅') // 歺厅 -> 餐厅
      .replace(/卫生问/g, '卫生间') // 卫生问 -> 卫生间
      .replace(/书房间/g, '书房') // 书房间 -> 书房

    // 备注信息修正
    enhancedText = enhancedText
      .replace(/普通下出/g, '普通下出') // 保持不变，但标记为重要
      .replace(/留圆接/g, '留圆接') // 保持不变，但标记为重要
      .replace(/软管/g, '软管') // 保持不变，但标记为重要

    // 如果文本有变化，标记为增强过的
    if (enhancedText !== originalText) {
      console.log(`   📝 文本增强: "${originalText}" -> "${enhancedText}"`)
      return {
        ...word,
        words: enhancedText,
        original_words: originalText,
        enhanced: true
      }
    }

    return word
  })
}

// 智能文字识别策略（多重识别方法）- 优化版本
async function performIntelligentTextOCR(file: File, useAccurate: boolean = true) {
  console.log('🧠 开始智能文字识别策略...')

  // 先分析图像质量，指导策略选择
  const imageQuality = await analyzeImageQuality(file)
  console.log(`📊 图像质量分析完成，推荐模式: ${imageQuality.recommendedMode}`)

  let bestResult: any = null
  let bestScore = 0
  const results: any[] = []
  const strategyOrder: string[] = []

  // 根据图像质量智能调整策略顺序
  if (imageQuality.recommendedMode === 'handwriting') {
    strategyOrder.push('handwriting', 'multiscale', 'enhanced', 'standard')
  } else if (imageQuality.recommendedMode === 'text') {
    strategyOrder.push('multiscale', 'enhanced', 'handwriting', 'standard')
  } else if (imageQuality.recommendedMode === 'layout') {
    strategyOrder.push('enhanced', 'multiscale', 'standard', 'handwriting')
  } else {
    strategyOrder.push('enhanced', 'multiscale', 'handwriting', 'standard')
  }

  console.log(`🎯 智能策略顺序: ${strategyOrder.join(' → ')}`)

  try {
    // 按照智能顺序执行识别策略
    for (const strategy of strategyOrder) {
      try {
        let result: any = null

        switch (strategy) {
          case 'handwriting':
            console.log('📝 尝试手写识别...')
            result = await performHandwritingOCR(file)
            break

          case 'enhanced':
            console.log('🔍 尝试增强版识别...')
            result = await performEnhancedTextOCR(file, useAccurate)
            break

          case 'multiscale':
            console.log('🎯 尝试多尺度增强识别...')
            result = await performMultiScaleOCR(file, useAccurate)
            break

          case 'standard':
            console.log('📄 尝试标准识别...')
            result = await performStandardTextOCR(file, useAccurate)
            break
        }

        if (result) {
          const score = calculateRecognitionScore(result.data)
          results.push({ method: strategy, result, score })

          // 动态阈值：根据图像质量调整接受标准
          const acceptanceThreshold = calculateAcceptanceThreshold(imageQuality, strategy)

          console.log(`📊 ${strategy}识别得分: ${score.toFixed(1)}, 接受阈值: ${acceptanceThreshold.toFixed(1)}`)

          if (score > bestScore) {
            bestResult = result
            bestScore = score
          }

          // 如果得分足够高，可以提前结束（避免不必要的计算）
          if (score >= acceptanceThreshold && score > 80) {
            console.log(`✅ ${strategy}识别效果良好，提前结束策略尝试`)
            break
          }
        }
      } catch (error) {
        console.log(`⚠️ ${strategy}识别失败，继续尝试下一个方法:`, error)
      }
    }

    // 结果验证和选择
    if (bestResult) {
      const bestMethod = results.find(r => r.result === bestResult)?.method || 'unknown'

      // 交叉验证：检查最佳结果是否合理
      const validationResult = validateRecognitionResult(bestResult.data, imageQuality)

      console.log(`🎯 选择最佳识别方法: ${bestMethod} (得分: ${bestScore.toFixed(1)})`)
      console.log(`🔍 结果验证: ${validationResult.isValid ? '通过' : '存疑'} - ${validationResult.reason}`)

      // 如果最佳结果存疑且有备选方案，考虑使用备选
      if (!validationResult.isValid && results.length > 1) {
        const alternativeResult = findAlternativeResult(results, bestResult)
        if (alternativeResult) {
          console.log(`🔄 使用备选识别结果: ${alternativeResult.method}`)
          bestResult = alternativeResult.result
          bestScore = alternativeResult.score
        }
      }

      // 添加识别方法信息到结果中
      bestResult.data.intelligentStrategy = {
        selectedMethod: results.find(r => r.result === bestResult)?.method || bestMethod,
        score: bestScore,
        imageQuality: imageQuality,
        validation: validationResult,
        allResults: results.map(r => ({
          method: r.method,
          score: r.score,
          wordCount: r.result.data.words_result?.length || 0
        }))
      }

      return bestResult
    } else {
      throw new Error('所有识别方法都失败了')
    }

  } catch (error) {
    console.error('❌ 智能文字识别失败:', error)
    throw error
  }
}

// 计算接受阈值（根据图像质量动态调整）
function calculateAcceptanceThreshold(imageQuality: any, strategy: string): number {
  let baseThreshold = 60 // 基础阈值

  // 根据图像质量调整
  if (imageQuality.brightness < 0.3 || imageQuality.brightness > 0.8) {
    baseThreshold -= 10 // 亮度异常，降低要求
  }
  if (imageQuality.contrast < 0.3) {
    baseThreshold -= 10 // 对比度低，降低要求
  }
  if (imageQuality.noise > 0.4) {
    baseThreshold -= 15 // 噪声大，降低要求
  }

  // 根据策略调整
  switch (strategy) {
    case 'handwriting':
      baseThreshold -= 5 // 手写识别本身难度较高
      break
    case 'enhanced':
      baseThreshold += 5 // 增强识别期望更好
      break
    case 'multiscale':
      baseThreshold += 10 // 多尺度识别期望最好
      break
  }

  return Math.max(baseThreshold, 30) // 最低阈值30
}

// 验证识别结果的合理性
function validateRecognitionResult(data: any, imageQuality: any): { isValid: boolean, reason: string } {
  const text = data.mergedText || data.text || ''
  const wordCount = data.words_result?.length || 0

  // 基本合理性检查
  if (wordCount === 0) {
    return { isValid: false, reason: '未识别到任何文字' }
  }

  if (text.length < 5) {
    return { isValid: false, reason: '识别文字过少' }
  }

  // 检查是否有明显的识别错误模式
  const errorPatterns = [
    /^[^\u4e00-\u9fa5\w\s]+$/, // 全是特殊符号
    /(.)\1{10,}/, // 重复字符过多
    /^[a-zA-Z]{1,2}$/ // 只有1-2个英文字母
  ]

  for (const pattern of errorPatterns) {
    if (pattern.test(text)) {
      return { isValid: false, reason: '检测到异常识别模式' }
    }
  }

  // 检查置信度分布
  if (data.words_result) {
    const confidences = data.words_result.map((word: any) => word.probability?.average || 0.5)
    const avgConfidence = confidences.reduce((sum: number, conf: number) => sum + conf, 0) / confidences.length

    if (avgConfidence < 0.3) {
      return { isValid: false, reason: '整体置信度过低' }
    }
  }

  return { isValid: true, reason: '识别结果合理' }
}

// 寻找备选识别结果
function findAlternativeResult(results: any[], currentBest: any): any | null {
  // 排除当前最佳结果，按得分排序
  const alternatives = results
    .filter(r => r.result !== currentBest)
    .sort((a, b) => b.score - a.score)

  if (alternatives.length === 0) return null

  const alternative = alternatives[0]

  // 备选结果的得分不能太低
  if (alternative.score < 40) return null

  // 验证备选结果
  const validation = validateRecognitionResult(alternative.result.data, {})
  if (!validation.isValid) return null

  return alternative
}

// 计算识别结果得分（用于选择最佳识别方法）- 优化版本
function calculateRecognitionScore(data: any): number {
  let score = 0
  const scoreDetails: any = {}

  // 基础分数：识别到的文字行数
  const wordCount = data.words_result?.length || 0
  const wordCountScore = wordCount * 3 // 降低权重，避免过度偏向行数多的结果
  score += wordCountScore
  scoreDetails.wordCount = { count: wordCount, score: wordCountScore }

  // 文字长度分数（适度权重）
  const textLength = data.mergedText?.length || data.text?.length || 0
  const textLengthScore = Math.min(textLength * 0.2, 50) // 设置上限，避免过长文本获得过高分数
  score += textLengthScore
  scoreDetails.textLength = { length: textLength, score: textLengthScore }

  // 置信度分数（最重要的客观指标）
  let confidenceScore = 0
  let avgConfidence = 0
  if (data.words_result && wordCount > 0) {
    const confidences = data.words_result.map((word: any) => word.probability?.average || 0.5)
    avgConfidence = confidences.reduce((sum: number, conf: number) => sum + conf, 0) / confidences.length

    // 置信度分数采用非线性计算，高置信度获得更多奖励
    confidenceScore = Math.pow(avgConfidence, 2) * 120 // 平方函数，奖励高置信度
    score += confidenceScore

    // 置信度分布均匀性奖励
    const stdDev = calculateStandardDeviation(confidences)
    const uniformityScore = (1 - Math.min(stdDev, 1)) * 15 // 分布越均匀，质量越好
    score += uniformityScore
    scoreDetails.confidence = {
      avg: avgConfidence,
      stdDev: stdDev,
      confidenceScore: confidenceScore,
      uniformityScore: uniformityScore
    }
  }

  // 内容质量评估（降低特定内容偏向）
  const text = data.mergedText || data.text || ''

  // 数字和尺寸信息（适度奖励）
  const dimensionPattern = /\d+[xX×*]\d+/g
  const dimensionMatches = text.match(dimensionPattern) || []
  const dimensionScore = Math.min(dimensionMatches.length * 5, 25) // 设置上限
  score += dimensionScore
  scoreDetails.dimensions = { count: dimensionMatches.length, score: dimensionScore }

  // 数字内容比例（客观指标）
  const numberPattern = /\d+/g
  const numberMatches = text.match(numberPattern) || []
  const numberRatio = numberMatches.length / Math.max(wordCount, 1)
  const numberScore = Math.min(numberRatio * 20, 15) // 数字比例合理性
  score += numberScore
  scoreDetails.numbers = { ratio: numberRatio, score: numberScore }

  // 文本结构化程度（客观指标）
  const structureScore = calculateTextStructureScore(text)
  score += structureScore
  scoreDetails.structure = { score: structureScore }

  // 特殊内容识别（大幅降低权重，避免过度偏向）
  const floorPattern = /\d+楼|\d+层/g
  const floorMatches = text.match(floorPattern) || []
  const floorScore = Math.min(floorMatches.length * 3, 10) // 大幅降低权重
  score += floorScore

  const ventPattern = /出风|回风|风口|叶片/g
  const ventMatches = text.match(ventPattern) || []
  const ventScore = Math.min(ventMatches.length * 4, 12) // 大幅降低权重
  score += ventScore

  scoreDetails.content = {
    floors: floorMatches.length,
    vents: ventMatches.length,
    floorScore: floorScore,
    ventScore: ventScore
  }

  // 识别方法奖励（鼓励使用更先进的方法）
  const methodBonus = getMethodBonus(data.recognitionMethod)
  score += methodBonus
  scoreDetails.methodBonus = methodBonus

  console.log(`📊 优化识别得分详情:`, scoreDetails)
  console.log(`📊 总分: ${score.toFixed(1)} (置信度: ${(avgConfidence * 100).toFixed(1)}%)`)

  return score
}

// 计算文本结构化程度
function calculateTextStructureScore(text: string): number {
  let structureScore = 0

  // 检查是否有序号结构
  const sequencePattern = /^\d+[\.、\s]/gm
  const sequenceMatches = text.match(sequencePattern) || []
  structureScore += Math.min(sequenceMatches.length * 2, 10)

  // 检查是否有表格结构（多列对齐）
  const lines = text.split('\n').filter(line => line.trim().length > 0)
  if (lines.length > 3) {
    const avgLineLength = lines.reduce((sum, line) => sum + line.length, 0) / lines.length
    const lengthVariance = lines.reduce((sum, line) => sum + Math.pow(line.length - avgLineLength, 2), 0) / lines.length
    const lengthStdDev = Math.sqrt(lengthVariance)

    // 行长度越均匀，结构化程度越高
    if (lengthStdDev < avgLineLength * 0.3) {
      structureScore += 8
    }
  }

  return structureScore
}

// 获取识别方法奖励
function getMethodBonus(method: string): number {
  switch (method) {
    case 'multiscale': return 5 // 多尺度识别
    case 'enhanced': return 3   // 增强识别
    case 'handwriting': return 2 // 手写识别
    case 'standard': return 1   // 标准识别
    default: return 0
  }
}

// 计算标准差
function calculateStandardDeviation(values: number[]): number {
  if (values.length === 0) return 0
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  return Math.sqrt(variance)
}

// 执行增强表格识别（回滚到V1版本）
async function performTableOCR(file: File) {
  try {
    console.log('📊 执行增强表格识别...')

    const params = {
      return_excel: 'false',
      cell_contents: 'true',
      request_type: 'excel', // 指定为Excel类型表格
      detect_direction: 'true', // 检测图像方向
      language_type: 'CHN_ENG' // 中英文混合
    }

    // 暂时回滚到V1版本，确保功能正常
    const result = await performOCR(BAIDU_OCR_CONFIG.TABLE_OCR_URL, file, params)

    // 🔍 详细调试OCR返回结果
    console.log('🔍 百度OCR原始返回结果:')
    console.log('  - 完整结果:', JSON.stringify(result, null, 2))
    console.log('  - 结果键名:', Object.keys(result))
    console.log('  - tables_result:', result.tables_result)
    console.log('  - forms:', result.forms)
    console.log('  - words_result:', result.words_result)

    // 处理表格识别结果
    const tables = result.tables_result || []

    console.log(`🔍 表格数量: ${tables.length}`)
    if (tables.length > 0) {
      console.log('🔍 第一个表格结构:', JSON.stringify(tables[0], null, 2))
    }

    if (tables.length === 0) {
      // 如果没有tables_result，尝试使用forms字段
      const forms = result.forms || []
      console.log(`🔍 尝试使用forms字段，数量: ${forms.length}`)

      if (forms.length === 0) {
        throw new Error('未检测到表格结构')
      }

      // 使用forms数据构建表格
      return await parseFormsDataToTable(result)
    }

    // 构建表格数据
    const tableData = {
      headers: [],
      rows: [],
      originalText: '',
      confidence: 0.9,
      tableDetected: true
    }

    // 处理第一个表格（如果有多个表格，这里简化处理）
    const firstTable = tables[0]
    const { body } = firstTable

    if (body && body.length > 0) {
      // 构建表格矩阵
      const cellMatrix: { [key: string]: string } = {}
      let maxRow = 0
      let maxCol = 0

      body.forEach((cell: any) => {
        const { row_start, col_start, words } = cell
        // 后处理表格单元格内容，校正常见错误
        const processedWords = postProcessTableCell(words || '')
        cellMatrix[`${row_start}-${col_start}`] = processedWords
        maxRow = Math.max(maxRow, row_start)
        maxCol = Math.max(maxCol, col_start)
      })

      // 构建二维数组
      const rows: string[][] = []
      for (let r = 0; r <= maxRow; r++) {
        const row: string[] = []
        for (let c = 0; c <= maxCol; c++) {
          const cellValue = cellMatrix[`${r}-${c}`] || ''
          row.push(cellValue)
        }
        rows.push(row)
      }

      // 第一行作为表头
      tableData.headers = rows.length > 0 ? rows[0] : []
      tableData.rows = rows.slice(1)

      // 增加详细的表格结构调试信息
      console.log('📊 表格结构解析详情:')
      console.log(`   表头 (${tableData.headers.length}列):`, tableData.headers)
      console.log(`   数据行数: ${tableData.rows.length}`)

      // 分析数量列位置
      const quantityColumnIndex = tableData.headers.findIndex(header =>
        header && (header.includes('数量') || header.toLowerCase().includes('quantity'))
      )

      if (quantityColumnIndex !== -1) {
        console.log(`   🔢 数量列位置: 第${quantityColumnIndex + 1}列 (索引${quantityColumnIndex})`)

        // 验证数量列的数据
        tableData.rows.forEach((row, index) => {
          const quantityValue = row[quantityColumnIndex] || ''
          console.log(`   第${index + 1}行数量: "${quantityValue}"`)
        })
      } else {
        console.log('   ⚠️ 未找到数量列，可能影响数量识别准确性')
      }
    }

    console.log(`✅ 表格识别完成，识别到 ${tables.length} 个表格`)

    // 应用数量验证和修正
    const validatedTableData = await validateAndCorrectTableQuantities(tableData)

    return {
      success: true,
      type: 'table',
      data: validatedTableData
    }

  } catch (error) {
    console.error('❌ 表格识别失败:', error)
    throw error
  }
}

// 后处理表格单元格内容（增强版：更精确的数据清理）
function postProcessTableCell(cellText: string): string {
  if (!cellText) return ''

  return cellText
    // 数字校正（在表格中特别重要）
    .replace(/O/g, '0')  // 字母O替换为数字0
    .replace(/I/g, '1')  // 字母I替换为数字1
    .replace(/l/g, '1')  // 小写l替换为数字1
    .replace(/S/g, '5')  // 大写S替换为数字5
    .replace(/Z/g, '2')  // 大写Z替换为数字2
    // 尺寸符号标准化（保持×符号，便于后续解析）
    .replace(/✖️/g, '×')
    .replace(/✖/g, '×')
    .replace(/✘/g, '×')
    .replace(/✕/g, '×')
    .replace(/[xX*]/g, '×')
    // 去除多余空格
    .replace(/\s+/g, ' ')
    .trim()
}

// 验证和修正表格数量数据
async function validateAndCorrectTableQuantities(tableData: any): Promise<any> {
  console.log('🔢 开始验证和修正表格数量数据...')

  const { headers, rows } = tableData

  // 找到数量列位置
  const quantityColumnIndex = headers.findIndex((header: string) =>
    header && (header.includes('数量') || header.toLowerCase().includes('quantity'))
  )

  if (quantityColumnIndex === -1) {
    console.log('⚠️ 未找到数量列，跳过数量验证')
    return tableData
  }

  console.log(`📋 数量列位置: 第${quantityColumnIndex + 1}列`)

  // 验证和修正每行的数量
  const correctedRows = rows.map((row: string[], index: number) => {
    const originalQuantity = row[quantityColumnIndex] || ''
    const correctedQuantity = validateAndCorrectQuantity(originalQuantity, row, index + 1)

    // 创建新行，替换数量值
    const newRow = [...row]
    newRow[quantityColumnIndex] = correctedQuantity.toString()

    if (originalQuantity !== correctedQuantity.toString()) {
      console.log(`   第${index + 1}行数量修正: "${originalQuantity}" -> "${correctedQuantity}"`)
    }

    return newRow
  })

  return {
    ...tableData,
    rows: correctedRows
  }
}

// 验证和修正单个数量值
function validateAndCorrectQuantity(quantityText: string, rowData: string[], rowNumber: number): number {
  if (!quantityText) return 1

  const cleanText = quantityText.trim()
  if (!cleanText) return 1

  // 解析数量
  const parsedQuantity = parseInt(cleanText)
  if (isNaN(parsedQuantity) || parsedQuantity <= 0) {
    console.log(`   第${rowNumber}行: 无效数量"${cleanText}"，使用默认值1`)
    return 1
  }

  // 检查是否为常见的OCR错误（尺寸被误识别为数量）
  const commonDimensions = [100, 125, 140, 150, 160, 200, 250, 295, 300, 400, 500, 600, 800, 1000, 1100, 1200, 1500, 2000, 2500, 3000, 3500, 4000]

  if (commonDimensions.includes(parsedQuantity)) {
    console.log(`   第${rowNumber}行: 数量${parsedQuantity}疑似尺寸数据，修正为1`)
    return 1
  }

  // 检查数量是否异常大（表格中的数量通常不会超过50）
  if (parsedQuantity > 50) {
    console.log(`   第${rowNumber}行: 数量${parsedQuantity}异常大，修正为1`)
    return 1
  }

  // 检查是否包含尺寸信息（如果数量字段包含×符号，说明是尺寸）
  if (quantityText.includes('×') || quantityText.includes('x')) {
    console.log(`   第${rowNumber}行: 数量字段"${quantityText}"包含尺寸符号，修正为1`)
    return 1
  }

  // 检查是否在行数据中重复出现（可能是尺寸）
  const duplicateCount = rowData.filter(cell => cell === cleanText).length
  if (duplicateCount > 1) {
    console.log(`   第${rowNumber}行: 数量${parsedQuantity}在行中重复出现，可能是尺寸，修正为1`)
    return 1
  }

  return parsedQuantity
}

// 增强表格数据解析（提取尺寸、备注等信息）
async function enhanceTableDataParsing(tableData: any): Promise<any> {
  console.log('🔧 开始增强表格数据解析...')

  const { headers, rows } = tableData

  // 找到关键列的位置
  const columnMapping = findTableColumns(headers)
  console.log('📋 列映射:', columnMapping)

  // 增强每行数据
  const enhancedRows = rows.map((row: string[], index: number) => {
    return enhanceRowData(row, columnMapping, index + 1)
  })

  return {
    ...tableData,
    rows: enhancedRows,
    columnMapping // 添加列映射信息供前端使用
  }
}

// 查找表格中的关键列
function findTableColumns(headers: string[]): any {
  const mapping: any = {
    序号: -1,
    地址: -1,
    尺寸: -1,
    数量: -1,
    长度: -1,
    宽度: -1,
    颜色: -1,
    备注: -1
  }

  headers.forEach((header, index) => {
    const h = header.toLowerCase().trim()

    if (h.includes('序号') || h.includes('号')) mapping.序号 = index
    if (h.includes('地址') || h.includes('地点') || h.includes('项目')) mapping.地址 = index
    if (h.includes('原始信息') || h.includes('尺寸') || h.includes('规格')) mapping.尺寸 = index
    if (h.includes('数量') || h.includes('qty')) mapping.数量 = index
    if (h.includes('长度') || h.includes('长')) mapping.长度 = index
    if (h.includes('宽度') || h.includes('宽')) mapping.宽度 = index
    if (h.includes('颜色') || h.includes('色')) mapping.颜色 = index
    if (h.includes('备注') || h.includes('说明') || h.includes('注')) mapping.备注 = index
  })

  return mapping
}

// 增强单行数据
function enhanceRowData(row: string[], columnMapping: any, rowNumber: number): string[] {
  const enhancedRow = [...row]

  // 处理尺寸列
  if (columnMapping.尺寸 >= 0 && columnMapping.尺寸 < row.length) {
    const sizeCell = row[columnMapping.尺寸]
    if (sizeCell) {
      // 标准化尺寸格式
      const normalizedSize = normalizeDimensionFormat(sizeCell)
      if (normalizedSize !== sizeCell) {
        enhancedRow[columnMapping.尺寸] = normalizedSize
        console.log(`   第${rowNumber}行尺寸标准化: "${sizeCell}" -> "${normalizedSize}"`)
      }
    }
  }

  return enhancedRow
}

// 标准化尺寸格式
function normalizeDimensionFormat(sizeText: string): string {
  if (!sizeText) return sizeText

  // 移除单位
  let normalized = sizeText.replace(/mm|㎜|毫米/g, '').trim()

  // 标准化分隔符为×
  normalized = normalized.replace(/[xX*✖️✖✘✕]/g, '×')

  // 提取数字
  const match = normalized.match(/(\d+)\s*×\s*(\d+)/)
  if (match) {
    const length = parseInt(match[1])
    const width = parseInt(match[2])

    // 智能排序：大的作为长度，小的作为宽度
    const finalLength = Math.max(length, width)
    const finalWidth = Math.min(length, width)

    return `${finalLength}×${finalWidth}`
  }

  return normalized
}

// 识别结果后验证和自动修正
async function postValidateAndCorrectResult(result: any, originalFile: File): Promise<any> {
  try {
    console.log('🔍 开始识别结果后验证和修正...')

    if (!result.data) return result

    const originalText = result.data.mergedText || result.data.text || ''
    let correctedText = originalText
    let correctionCount = 0
    const corrections: string[] = []

    // 1. 常见OCR错误修正
    const ocrCorrections = [
      { pattern: /(?<!\d)[O](?=\d|\s|$)/g, replacement: '0', description: '字母O修正为数字0' },
      { pattern: /(?<!\w)[I](?=\d|\s|$)/g, replacement: '1', description: '字母I修正为数字1' },
      { pattern: /(?<!\w)[l](?=\d|\s|$)/g, replacement: '1', description: '小写l修正为数字1' },
      { pattern: /(?<!\w)[S](?=\d|\s|$)/g, replacement: '5', description: '字母S修正为数字5' },
      { pattern: /(?<!\w)[Z](?=\d|\s|$)/g, replacement: '2', description: '字母Z修正为数字2' },
      { pattern: /[×X*✖️✖✘✕]/g, replacement: 'x', description: '统一乘号格式' },
      { pattern: /(\d+)\s*[xX×]\s*(\d+)/g, replacement: '$1x$2', description: '标准化尺寸格式' }
    ]

    for (const correction of ocrCorrections) {
      const beforeLength = correctedText.length
      correctedText = correctedText.replace(correction.pattern, correction.replacement)
      if (correctedText.length !== beforeLength || correctedText !== originalText) {
        correctionCount++
        corrections.push(correction.description)
      }
    }

    // 2. 数字序列连续性检查和修正
    const numberSequenceCorrection = correctNumberSequences(correctedText)
    if (numberSequenceCorrection.corrected) {
      correctedText = numberSequenceCorrection.text
      correctionCount += numberSequenceCorrection.corrections.length
      corrections.push(...numberSequenceCorrection.corrections)
    }

    // 3. 尺寸信息合理性检查
    const dimensionCorrection = validateAndCorrectDimensions(correctedText)
    if (dimensionCorrection.corrected) {
      correctedText = dimensionCorrection.text
      correctionCount += dimensionCorrection.corrections.length
      corrections.push(...dimensionCorrection.corrections)
    }

    // 4. 重复内容检测和清理
    const deduplicationResult = removeDuplicateContent(correctedText)
    if (deduplicationResult.cleaned) {
      correctedText = deduplicationResult.text
      correctionCount++
      corrections.push('移除重复内容')
    }

    // 5. 更新结果
    if (correctionCount > 0) {
      console.log(`✅ 识别结果修正完成: ${correctionCount}项修正`)
      corrections.forEach(correction => console.log(`   - ${correction}`))

      // 更新文本内容
      result.data.mergedText = correctedText
      result.data.text = correctedText

      // 重新解析words_result（如果需要）
      if (result.data.words_result) {
        result.data.words_result = updateWordsResult(result.data.words_result, originalText, correctedText)
      }

      // 添加修正信息
      result.data.postValidation = {
        correctionCount,
        corrections,
        originalLength: originalText.length,
        correctedLength: correctedText.length,
        qualityImprovement: calculateQualityImprovement(originalText, correctedText)
      }
    } else {
      console.log('✅ 识别结果验证通过，无需修正')
      result.data.postValidation = {
        correctionCount: 0,
        corrections: [],
        qualityImprovement: 0
      }
    }

    return result

  } catch (error) {
    console.warn('⚠️ 识别结果后验证失败:', error)
    return result // 返回原始结果，不影响主流程
  }
}

// 修正数字序列连续性
function correctNumberSequences(text: string): { text: string, corrected: boolean, corrections: string[] } {
  const corrections: string[] = []
  let correctedText = text
  let corrected = false

  // 查找数字序列模式 (如: 1. 2. 3. 或 1、2、3、)
  const sequencePattern = /(\d+)[\.、]\s*/g
  const matches = [...text.matchAll(sequencePattern)]

  if (matches.length >= 3) {
    const numbers = matches.map(match => parseInt(match[1]))
    const gaps = []

    // 检查序列中的间隔
    for (let i = 1; i < numbers.length; i++) {
      const gap = numbers[i] - numbers[i-1]
      gaps.push(gap)
    }

    // 如果大部分间隔是1，但有个别异常，尝试修正
    const normalGaps = gaps.filter(gap => gap === 1).length
    const totalGaps = gaps.length

    if (normalGaps / totalGaps >= 0.7) { // 70%以上是正常间隔
      // 找出异常的数字并修正
      for (let i = 1; i < numbers.length; i++) {
        const expectedNumber = numbers[0] + i
        if (numbers[i] !== expectedNumber) {
          const wrongNumber = numbers[i]
          correctedText = correctedText.replace(
            new RegExp(`${wrongNumber}([\.、])`),
            `${expectedNumber}$1`
          )
          corrections.push(`序号修正: ${wrongNumber} → ${expectedNumber}`)
          corrected = true
        }
      }
    }
  }

  return { text: correctedText, corrected, corrections }
}

// 验证和修正尺寸信息
function validateAndCorrectDimensions(text: string): { text: string, corrected: boolean, corrections: string[] } {
  const corrections: string[] = []
  let correctedText = text
  let corrected = false

  // 查找尺寸模式
  const dimensionPattern = /(\d+)\s*[xX×]\s*(\d+)/g
  const matches = [...text.matchAll(dimensionPattern)]

  for (const match of matches) {
    const length = parseInt(match[1])
    const width = parseInt(match[2])
    const originalDimension = match[0]

    // 检查尺寸合理性
    let needCorrection = false
    let correctedLength = length
    let correctedWidth = width

    // 1. 检查是否有明显的OCR错误（如过大或过小的数字）
    if (length > 10000 || width > 10000) {
      // 可能是OCR错误，尝试修正
      if (length > 10000) correctedLength = Math.floor(length / 10)
      if (width > 10000) correctedWidth = Math.floor(width / 10)
      needCorrection = true
    }

    // 2. 检查长宽比是否合理（风口尺寸通常不会相差太大）
    const ratio = Math.max(correctedLength, correctedWidth) / Math.min(correctedLength, correctedWidth)
    if (ratio > 20) {
      // 比例过大，可能有错误
      needCorrection = true
    }

    // 3. 应用修正
    if (needCorrection) {
      const correctedDimension = `${correctedLength}x${correctedWidth}`
      correctedText = correctedText.replace(originalDimension, correctedDimension)
      corrections.push(`尺寸修正: ${originalDimension} → ${correctedDimension}`)
      corrected = true
    }
  }

  return { text: correctedText, corrected, corrections }
}

// 移除重复内容
function removeDuplicateContent(text: string): { text: string, cleaned: boolean } {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0)
  const uniqueLines = [...new Set(lines)]

  const cleaned = uniqueLines.length < lines.length
  const cleanedText = uniqueLines.join('\n')

  return { text: cleanedText, cleaned }
}

// 更新words_result以反映文本修正
function updateWordsResult(wordsResult: any[], originalText: string, correctedText: string): any[] {
  // 简单的更新策略：如果文本长度变化不大，保持原有结构
  if (Math.abs(originalText.length - correctedText.length) < originalText.length * 0.1) {
    return wordsResult.map(word => ({
      ...word,
      words: word.words // 保持原有的单词分割
    }))
  }

  // 如果变化较大，重新分割
  const correctedLines = correctedText.split('\n').filter(line => line.trim().length > 0)
  return correctedLines.map((line, index) => ({
    words: line.trim(),
    location: wordsResult[index]?.location || { left: 0, top: 0, width: 0, height: 0 },
    probability: wordsResult[index]?.probability || { average: 0.8 }
  }))
}

// 计算质量改进程度
function calculateQualityImprovement(originalText: string, correctedText: string): number {
  // 简单的质量评估：基于文本结构化程度
  const originalScore = calculateTextQualityScore(originalText)
  const correctedScore = calculateTextQualityScore(correctedText)

  return correctedScore - originalScore
}

// 计算文本质量分数
function calculateTextQualityScore(text: string): number {
  let score = 0

  // 数字比例
  const numberMatches = text.match(/\d+/g) || []
  score += numberMatches.length * 2

  // 尺寸信息
  const dimensionMatches = text.match(/\d+\s*[xX×]\s*\d+/g) || []
  score += dimensionMatches.length * 5

  // 序号结构
  const sequenceMatches = text.match(/\d+[\.、]/g) || []
  score += sequenceMatches.length * 3

  // 中文内容
  const chineseMatches = text.match(/[\u4e00-\u9fa5]+/g) || []
  score += chineseMatches.length * 1

  return score
}

// 主要的智能OCR处理函数
export async function POST(request: NextRequest) {
  try {
    console.log('🤖 开始智能OCR识别处理...')

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const useAccurate = formData.get('useAccurate') === 'true'
    const recognitionType = formData.get('recognitionType') as string
    const testMode = formData.get('testMode') as string

    // 🧪 测试模式：直接测试数量识别
    if (testMode === 'quantity') {
      const testText = formData.get('testText') as string
      if (testText) {
        console.log('🧪 进入数量识别测试模式')
        const { recognizeIntent } = await import('@/lib/nlp/intent-recognition')
        const result = recognizeIntent(testText, true)
        return NextResponse.json({
          success: true,
          testMode: true,
          input: testText,
          result: result,
          message: '数量识别测试完成'
        })
      }
    }

    if (!files || files.length === 0) {
      return NextResponse.json({
        success: false,
        error: '没有上传文件'
      }, { status: 400 })
    }

    console.log(`📁 接收到 ${files.length} 个文件，识别类型: ${recognitionType || 'auto'}`)

    const results = []
    let successCount = 0
    let failedCount = 0

    // 优化：使用限制并发的批量处理
    const processFile = async (file: File, index: number): Promise<any> => {
      console.log(`📸 处理第${index + 1}/${files.length}个文件: ${file.name}`)

      try {
        let result: any

        if (recognitionType === 'auto') {
          // 智能检测内容类型和最佳识别方法
          const detectedType = await detectImageContentType(file)

          if (detectedType === 'table') {
            console.log('🔍 检测到表格内容，使用表格识别')
            result = await performTableOCR(file)
          } else {
            // 对于文字内容，使用多重识别策略
            console.log('🔍 检测到文字内容，使用智能文字识别策略')
            result = await performIntelligentTextOCR(file, useAccurate)
          }
        } else if (recognitionType === 'table') {
          // 强制使用表格识别
          result = await performTableOCR(file)
        } else if (recognitionType === 'handwriting') {
          // 强制使用手写识别
          console.log('✍️ 强制使用手写识别')
          result = await performHandwritingOCR(file)
        } else {
          // 强制使用文字识别
          result = await performIntelligentTextOCR(file, useAccurate)
        }

        // 🔍 结果后验证和自动修正
        if (result && result.success) {
          console.log('🔍 开始识别结果后验证...')
          result = await postValidateAndCorrectResult(result, file)
        }

        console.log(`✅ 文件 ${file.name} 识别成功 (类型: ${result.type})`)
        return { success: true, result, fileName: file.name }

      } catch (error) {
        console.error(`❌ 文件 ${file.name} 识别失败:`, error)
        return {
          success: false,
          result: {
            success: false,
            type: 'error',
            error: error instanceof Error ? error.message : '识别失败'
          },
          fileName: file.name
        }
      }
    }

    // 批量处理文件，限制并发数为2（避免API限制）
    const batchSize = 2
    const allResults: any[] = []

    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize)
      const batchPromises = batch.map((file) =>
        processFile(file, i)
      )

      const batchResults = await Promise.allSettled(batchPromises)

      batchResults.forEach((settledResult) => {
        if (settledResult.status === 'fulfilled') {
          const { success, result } = settledResult.value
          allResults.push(result)
          if (success) {
            successCount++
          } else {
            failedCount++
          }
        } else {
          console.error(`批处理失败:`, settledResult.reason)
          allResults.push({
            success: false,
            type: 'error',
            error: '批处理失败'
          })
          failedCount++
        }
      })

      // 批次间添加延迟
      if (i + batchSize < files.length) {
        await new Promise(resolve => setTimeout(resolve, 300))
      }
    }

    results.push(...allResults)

    // 合并文字识别结果
    const textResults = results.filter(r => r.success && r.type === 'text')
    const mergedText = textResults
      .map(r => r.data.mergedText)
      .filter(text => text && text.trim())
      .join('\n\n')

    // 空间布局分析（针对第一个成功的文字识别结果）
    let spatialAnalysis = null
    let tableAnalysis = null

    if (textResults.length > 0 && textResults[0].data.words_result) {
      console.log('🗺️ 开始空间布局分析...')
      try {
        const spatialLayout = SpatialTextAnalyzer.analyzeSpatialLayout(textResults[0].data)
        spatialAnalysis = {
          layout: spatialLayout,
          result: SpatialTextAnalyzer.getSpatialRecognitionResult(spatialLayout)
        }
        console.log(`✅ 空间分析完成: ${spatialLayout.items.length} 个文本项, ${spatialLayout.relationships.length} 个关系`)
      } catch (error) {
        console.warn('⚠️ 空间分析失败:', error)
      }

      // 表格布局分析
      console.log('📊 开始表格布局分析...')
      try {
        const tableLayout = TableLayoutAnalyzer.analyzeTableLayout(textResults[0].data)
        const analysisReport = TableLayoutAnalyzer.generateAnalysisReport(tableLayout)

        tableAnalysis = {
          layout: tableLayout,
          report: analysisReport
        }
        console.log(`✅ 表格分析完成: ${tableLayout.columns.length} 列, ${tableLayout.rows.length} 行`)
      } catch (error) {
        console.warn('⚠️ 表格分析失败:', error)
      }
    }

    console.log('📊 智能OCR识别完成统计:')
    console.log(`  - 总文件数: ${files.length}`)
    console.log(`  - 成功: ${successCount}`)
    console.log(`  - 失败: ${failedCount}`)
    console.log(`  - 文字识别: ${textResults.length}`)
    console.log(`  - 表格识别: ${results.filter(r => r.success && r.type === 'table').length}`)

    return NextResponse.json({
      success: true,
      data: {
        results,
        mergedText,
        spatialAnalysis,
        tableAnalysis,
        statistics: {
          total: files.length,
          success: successCount,
          failed: failedCount,
          totalWords: textResults.reduce((sum, r) => sum + (r.data.statistics?.totalWords || 0), 0),
          totalChars: mergedText.length,
          spatialItems: spatialAnalysis?.layout?.items?.length || 0,
          spatialRelationships: spatialAnalysis?.layout?.relationships?.length || 0,
          tableColumns: tableAnalysis?.layout?.columns?.length || 0,
          tableRows: tableAnalysis?.layout?.rows?.length || 0
        }
      },
      message: '智能图片识别完成'
    })

  } catch (error) {
    console.error('❌ 智能OCR识别处理失败:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '智能OCR识别失败',
      message: '智能OCR识别处理失败'
    }, { status: 500 })
  }
}

// 解析forms格式的表格数据
async function parseFormsDataToTable(result: any) {
  console.log('🔄 使用forms数据构建表格...')

  const forms = result.forms || []
  if (forms.length === 0) {
    throw new Error('forms数据为空')
  }

  const form = forms[0]
  const { body } = form

  if (!body || body.length === 0) {
    throw new Error('表格内容为空')
  }

  // 构建表格矩阵
  const cellMatrix: { [key: string]: string } = {}
  let maxRow = 0
  let maxCol = 0

  body.forEach((cell: any) => {
    const { row, column, word } = cell
    cellMatrix[`${row}-${column}`] = word || ''
    maxRow = Math.max(maxRow, row)
    maxCol = Math.max(maxCol, column)
  })

  // 构建二维数组
  const rows: string[][] = []
  for (let r = 0; r <= maxRow; r++) {
    const row: string[] = []
    for (let c = 0; c <= maxCol; c++) {
      row.push(cellMatrix[`${r}-${c}`] || '')
    }
    rows.push(row)
  }

  // 第一行作为表头
  const headers = rows.length > 0 ? rows[0] : []
  const dataRows = rows.slice(1)

  console.log('📋 forms表格解析完成:', {
    headers,
    rowCount: dataRows.length,
    colCount: headers.length
  })

  const tableData = {
    headers,
    rows: dataRows,
    originalText: result.words_result?.map((item: any) => item.words).join('\n') || '',
    confidence: 0.9,
    tableDetected: true
  }

  // 应用数量验证和修正
  const validatedTableData = await validateAndCorrectTableQuantities(tableData)

  // 增强表格数据解析（提取尺寸、备注等信息）
  const enhancedTableData = await enhanceTableDataParsing(validatedTableData)

  return {
    success: true,
    type: 'table',
    data: enhancedTableData
  }
}
