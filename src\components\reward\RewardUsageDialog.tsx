'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  Loader2,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import { api } from '@/lib/api/client'
import { useAuthStore } from '@/lib/store/auth'
import type { Client, RewardUsageType } from '@/types'

interface RewardUsageDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  client: Client | null
  onSuccess?: () => void
  // 🆕 实时奖励数据，优先使用这些值而不是client中的旧值
  realtimeRewardData?: {
    availableReward: number
    totalReward: number
    pendingReward: number
  }
}

interface ValidationResult {
  canUse: boolean
  reason?: string
  maxUsableAmount?: number
  restrictions?: string[]
  validationDetails?: unknown
}

export function RewardUsageDialog({ open, onOpenChange, client, onSuccess, realtimeRewardData }: RewardUsageDialogProps) {
  const { isAuthenticated, accessToken, user } = useAuthStore()
  const [usageType, setUsageType] = useState<RewardUsageType>('CASH_OUT')
  const [amount, setAmount] = useState('')
  const [description, setDescription] = useState('')
  const [notes, setNotes] = useState('')
  const [paymentMethod, setPaymentMethod] = useState('')
  const [loading, setLoading] = useState(false)
  const [validating, setValidating] = useState(false)
  const [validation, setValidation] = useState<ValidationResult | null>(null)
  const [error, setError] = useState('')

  // 重置表单
  const resetForm = () => {
    setUsageType('CASH_OUT')
    setAmount('')
    setDescription('')
    setNotes('')
    setPaymentMethod('')
    setValidation(null)
    setError('')
  }

  // 当对话框打开时重置表单并获取限制信息
  useEffect(() => {
    if (open && client) {
      resetForm()
      // 只有在认证状态就绪时才获取限制信息
      if (isAuthenticated && accessToken) {
        fetchUsageLimits()
      } else {
        console.log('⏳ 等待认证状态就绪...')
      }
    }
  }, [open, client, isAuthenticated, accessToken])

  // 获取奖励使用限制信息
  const fetchUsageLimits = async () => {
    if (!client) return

    // 检查认证状态
    if (!isAuthenticated || !accessToken) {
      console.warn('⚠️ 认证状态未就绪，延迟获取奖励限制:', {
        isAuthenticated,
        hasAccessToken: !!accessToken,
        userName: user?.name
      })
      // 延迟重试
      setTimeout(() => {
        if (isAuthenticated && accessToken) {
          fetchUsageLimits()
        }
      }, 1000)
      return
    }

    console.log('🔍 开始获取奖励使用限制:', {
      clientId: client.id,
      factoryId: client.factoryId,
      clientName: client.name,
      isAuthenticated,
      hasAccessToken: !!accessToken,
      userName: user?.name,
      timestamp: new Date().toISOString()
    })

    try {
      setValidating(true)
      const response = await api.post('/api/reward-usage/validate', {
        clientId: client.id,
        factoryId: client.factoryId
      })

      if (response.success) {
        console.log('🔍 奖励使用限制验证结果:', response.data)
        console.log('🔧 验证数据详细信息:', {
          完整响应: response,
          数据对象: response.data,
          canUse属性: response.data?.canUse,
          canUse类型: typeof response.data?.canUse,
          maxUsableAmount: response.data?.maxUsableAmount,
          restrictions: response.data?.restrictions
        })
        setValidation(response.data)
        console.log('🔧 设置验证状态后:', {
          canUse: response.data.canUse,
          maxUsableAmount: response.data.maxUsableAmount,
          restrictions: response.data.restrictions
        })
      } else {
        console.error('❌ 获取奖励限制信息失败:', response.error)
        setError(response.error || '获取奖励限制信息失败')
      }
    } catch (err) {
      setError('获取奖励限制信息失败')
    } finally {
      setValidating(false)
    }
  }

  // 验证使用金额
  const validateAmount = async (amountValue: string) => {
    if (!client || !amountValue) return

    // 检查认证状态
    if (!isAuthenticated || !accessToken) {
      console.warn('⚠️ 认证状态未就绪，跳过金额验证')
      return
    }

    const usageAmount = parseFloat(amountValue)
    if (isNaN(usageAmount) || usageAmount <= 0) return

    try {
      setValidating(true)
      const response = await api.post('/api/reward-usage/validate', {
        clientId: client.id,
        amount: usageAmount,
        factoryId: client.factoryId
      })

      if (response.success) {
        setValidation(response.data)
      }
    } catch (err) {
      console.error('验证金额失败:', err)
    } finally {
      setValidating(false)
    }
  }

  // 处理金额输入变化
  const handleAmountChange = (value: string) => {
    console.log('💰 金额输入变化:', value)
    setAmount(value)

    // 防抖验证
    const timeoutId = setTimeout(() => {
      validateAmount(value)
    }, 500)

    return () => clearTimeout(timeoutId)
  }

  // 提交奖励使用
  const handleSubmit = async () => {
    if (!client || !amount || !usageType) {
      setError('请填写完整信息')
      return
    }

    const usageAmount = parseFloat(amount)
    if (isNaN(usageAmount) || usageAmount <= 0) {
      setError('请输入有效的使用金额')
      return
    }

    if (validation && !validation.canUse) {
      setError(validation.reason || '不满足使用条件')
      return
    }

    try {
      setLoading(true)
      setError('')

      const response = await api.post('/api/reward-usage', {
        clientId: client.id,
        usageType,
        amount: usageAmount,
        description: description || undefined,
        paymentMethod: paymentMethod || undefined,
        notes: notes || undefined,
        factoryId: client.factoryId
      })

      if (response.success) {
        console.log('✅ 奖励使用成功，返回数据:', response.data)

        // 🆕 如果返回了实时奖励状态，触发数据刷新
        if (response.data?.realtimeRewardStatus) {
          console.log('🔄 触发奖励数据刷新:', response.data.realtimeRewardStatus)
          // 这里可以触发全局状态更新或者父组件刷新
        }

        onSuccess?.()
        onOpenChange(false)
        resetForm()
      } else {
        setError(response.error || '奖励使用失败')
      }
    } catch (err) {
      setError('奖励使用失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const formatAmount = (amount: number) => `¥${amount.toFixed(1)}`

  // 实时检查按钮状态
  // 🔧 修复：只有在验证状态已加载时才检查 canUse，避免初始状态导致按钮被禁用
  const isButtonDisabled = loading ||
    (validation !== null && !validation?.canUse) ||
    !amount ||
    parseFloat(amount) <= 0

  // 添加调试信息
  React.useEffect(() => {
    console.log('🔧 按钮状态实时检查:', {
      loading,
      'validation状态': validation === null ? 'null(未加载)' : validation === undefined ? 'undefined' : '已加载',
      'validation?.canUse': validation?.canUse,
      amount,
      'parseFloat(amount)': amount ? parseFloat(amount) : 'N/A',
      'parseFloat(amount) <= 0': amount ? parseFloat(amount) <= 0 : 'N/A',
      isButtonDisabled,
      '禁用原因': {
        loading: loading ? '正在加载' : false,
        validation: (validation !== null && !validation?.canUse) ? '验证失败' : false,
        amount: !amount ? '未输入金额' : false,
        amountValue: (amount && parseFloat(amount) <= 0) ? '金额无效' : false
      }
    })
  }, [loading, validation, amount, isButtonDisabled])

  const usageTypeOptions = [
    { value: 'CASH_OUT', label: '现金兑现', description: '直接兑现为现金' },
    { value: 'ORDER_DISCOUNT', label: '抵扣货款', description: '用于抵扣新订单货款' },
    { value: 'TRANSFER', label: '转账兑现', description: '转账到指定账户' },
    { value: 'OTHER', label: '其他方式', description: '其他兑现方式' }
  ]

  const paymentMethodOptions = [
    { value: 'CASH', label: '现金' },
    { value: 'BANK_TRANSFER', label: '银行转账' },
    { value: 'ALIPAY', label: '支付宝' },
    { value: 'WECHAT', label: '微信支付' },
    { value: 'OTHER', label: '其他' }
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>使用推荐奖励</DialogTitle>
          <DialogDescription>
            选择奖励使用方式并填写相关信息，系统将验证使用条件并处理奖励兑现。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 客户信息 */}
          {client && (
            <div className="bg-gray-50 p-3 rounded">
              <div className="font-medium">{client.name}</div>
              <div className="text-sm text-gray-600">{client.phone}</div>
              <div className="text-sm">
                可用奖励: <span className="font-medium text-green-600">
                  {formatAmount(realtimeRewardData?.availableReward ?? (client.availableReward as number || 0))}
                </span>
              </div>
            </div>
          )}

          {/* 验证状态 */}
          {validating && (
            <Alert>
              <Loader2 className="h-4 w-4 animate-spin" />
              <AlertDescription>正在验证使用条件...</AlertDescription>
            </Alert>
          )}

          {/* 限制信息 */}
          {validation && !validation.canUse && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {validation.reason}
                {validation.restrictions && validation.restrictions.length > 0 && (
                  <ul className="mt-2 list-disc list-inside">
                    {validation.restrictions.map((restriction, index) => (
                      <li key={index} className="text-sm">{restriction}</li>
                    ))}
                  </ul>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* 使用方式 */}
          <div>
            <Label htmlFor="usageType">使用方式</Label>
            <Select value={usageType} onValueChange={(value) => setUsageType(value as RewardUsageType)}>
              <SelectTrigger>
                <SelectValue placeholder="选择使用方式" />
              </SelectTrigger>
              <SelectContent>
                {usageTypeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    <div>
                      <div>{option.label}</div>
                      <div className="text-xs text-gray-500">{option.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 使用金额 */}
          <div>
            <Label htmlFor="amount">使用金额</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0"
              max={validation?.maxUsableAmount || realtimeRewardData?.availableReward || (client?.availableReward as number) || 0}
              value={amount}
              onChange={(e) => handleAmountChange(e.target.value)}
              placeholder="输入使用金额"
            />
            {validation?.maxUsableAmount !== undefined && (
              <div className="text-xs text-gray-500 mt-1">
                最大可用: {formatAmount(validation.maxUsableAmount)}
              </div>
            )}
          </div>

          {/* 兑现方式（仅现金兑现和转账兑现时显示） */}
          {(usageType === 'CASH_OUT' || usageType === 'TRANSFER') && (
            <div>
              <Label htmlFor="paymentMethod">兑现方式</Label>
              <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                <SelectTrigger>
                  <SelectValue placeholder="选择兑现方式" />
                </SelectTrigger>
                <SelectContent>
                  {paymentMethodOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* 使用说明 */}
          <div>
            <Label htmlFor="description">使用说明</Label>
            <Input
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="简要说明使用用途"
            />
          </div>

          {/* 备注 */}
          <div>
            <Label htmlFor="notes">备注</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="其他备注信息"
              rows={2}
            />
          </div>

          {/* 错误信息 */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 成功验证提示 */}
          {validation?.canUse && amount && parseFloat(amount) > 0 && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                可以使用 {formatAmount(parseFloat(amount))} 奖励
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button
            onClick={() => {
              console.log('🔧 按钮点击状态检查:', {
                loading,
                'validation?.canUse': validation?.canUse,
                amount,
                'parseFloat(amount)': amount ? parseFloat(amount) : 'N/A',
                'parseFloat(amount) <= 0': amount ? parseFloat(amount) <= 0 : 'N/A',
                disabled: loading || !validation?.canUse || !amount || parseFloat(amount) <= 0
              })
              handleSubmit()
            }}
            disabled={isButtonDisabled}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-2 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-2 border-blue-400"
            size="lg"
          >
            {loading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
            <span className="text-base">
              {loading ? '处理中...' : `确认使用 ${amount ? formatAmount(parseFloat(amount)) : ''}`}
            </span>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
