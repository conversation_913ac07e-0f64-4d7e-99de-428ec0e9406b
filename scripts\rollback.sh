#!/bin/bash
# rollback.sh - 风口云平台回滚脚本
# 使用方法: ./rollback.sh [backup_dir] [--full]

set -e

FULL_ROLLBACK=false
BACKUP_DIR=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --full)
      FULL_ROLLBACK=true
      shift
      ;;
    -h|--help)
      echo "使用方法: $0 [备份目录] [选项]"
      echo "选项:"
      echo "  --full       完整回滚（包含数据库）"
      echo "  -h, --help   显示帮助信息"
      echo ""
      echo "示例:"
      echo "  $0                           # 交互式选择备份"
      echo "  $0 /backups/20250622_140000  # 指定备份目录"
      echo "  $0 --full                    # 完整回滚"
      exit 0
      ;;
    *)
      if [ -z "$BACKUP_DIR" ]; then
        BACKUP_DIR="$1"
      else
        echo "未知选项: $1"
        exit 1
      fi
      shift
      ;;
  esac
done

echo "🔙 风口云平台回滚工具"
echo "时间: $(date)"
echo "================================"

# 函数：选择备份
select_backup() {
  if [ -n "$BACKUP_DIR" ]; then
    if [ ! -d "$BACKUP_DIR" ]; then
      echo "❌ 指定的备份目录不存在: $BACKUP_DIR"
      exit 1
    fi
    return 0
  fi
  
  echo "📁 可用的备份:"
  if [ ! -d "/backups" ]; then
    echo "❌ 备份目录不存在"
    exit 1
  fi
  
  local backups=($(ls -1t /backups/ 2>/dev/null | head -10))
  
  if [ ${#backups[@]} -eq 0 ]; then
    echo "❌ 没有找到可用的备份"
    exit 1
  fi
  
  echo ""
  for i in "${!backups[@]}"; do
    local backup_path="/backups/${backups[$i]}"
    local backup_time=$(date -r "$backup_path" "+%Y-%m-%d %H:%M:%S")
    echo "  $((i+1)). ${backups[$i]} ($backup_time)"
  done
  
  echo ""
  read -p "请选择要回滚的备份 (1-${#backups[@]}): " choice
  
  if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#backups[@]} ]; then
    BACKUP_DIR="/backups/${backups[$((choice-1))]}"
    echo "✅ 选择备份: $BACKUP_DIR"
  else
    echo "❌ 无效的选择"
    exit 1
  fi
}

# 函数：验证备份
validate_backup() {
  echo "🔍 验证备份完整性..."
  
  if [ ! -f "$BACKUP_DIR/database.sql" ]; then
    echo "❌ 数据库备份文件不存在"
    exit 1
  fi
  
  if [ ! -s "$BACKUP_DIR/database.sql" ]; then
    echo "❌ 数据库备份文件为空"
    exit 1
  fi
  
  echo "✅ 备份验证通过"
}

# 函数：确认回滚
confirm_rollback() {
  echo ""
  echo "⚠️ 回滚确认"
  echo "================================"
  echo "备份目录: $BACKUP_DIR"
  echo "回滚类型: $([ "$FULL_ROLLBACK" = true ] && echo "完整回滚（包含数据库）" || echo "代码回滚")"
  echo ""
  echo "🚨 警告: 此操作将覆盖当前的系统状态！"
  echo ""
  
  read -p "确定要继续回滚吗？(yes/no): " confirmation
  
  if [ "$confirmation" != "yes" ]; then
    echo "回滚已取消"
    exit 0
  fi
}

# 函数：创建回滚前备份
create_pre_rollback_backup() {
  echo "💾 创建回滚前备份..."
  
  local pre_rollback_dir="/backups/pre_rollback_$(date +%Y%m%d_%H%M%S)"
  mkdir -p "$pre_rollback_dir"
  
  # 备份当前数据库
  docker-compose exec -T postgres pg_dump -U factorysystem factorysystem > "$pre_rollback_dir/database.sql"
  
  # 备份当前代码版本信息
  git rev-parse HEAD > "$pre_rollback_dir/current_commit.txt"
  git log --oneline -5 > "$pre_rollback_dir/recent_commits.txt"
  
  echo "✅ 回滚前备份完成: $pre_rollback_dir"
}

# 函数：代码回滚
rollback_code() {
  echo "🔄 执行代码回滚..."
  
  # 如果备份中有commit信息，回滚到指定版本
  if [ -f "$BACKUP_DIR/current_commit.txt" ]; then
    local target_commit=$(cat "$BACKUP_DIR/current_commit.txt")
    echo "  📝 回滚到提交: $target_commit"
    git checkout "$target_commit"
  else
    echo "  ⚠️ 备份中没有找到提交信息，跳过代码回滚"
  fi
  
  # 恢复配置文件
  if [ -f "$BACKUP_DIR/.env.production" ]; then
    echo "  ⚙️ 恢复生产环境配置..."
    cp "$BACKUP_DIR/.env.production" .env.production
  elif [ -f "$BACKUP_DIR/.env.local" ]; then
    echo "  ⚙️ 恢复本地环境配置..."
    cp "$BACKUP_DIR/.env.local" .env.local
  fi
  
  if [ -f "$BACKUP_DIR/docker-compose.yml" ]; then
    echo "  🐳 恢复Docker配置..."
    cp "$BACKUP_DIR/docker-compose.yml" docker-compose.yml
  fi
  
  if [ -f "$BACKUP_DIR/nginx.conf" ]; then
    echo "  🌐 恢复Nginx配置..."
    cp "$BACKUP_DIR/nginx.conf" nginx.conf
  fi
}

# 函数：数据库回滚
rollback_database() {
  echo "🗄️ 执行数据库回滚..."
  
  # 停止应用服务
  echo "  🛑 停止应用服务..."
  docker-compose stop app
  
  # 确保数据库服务运行
  echo "  🔄 确保数据库服务运行..."
  docker-compose up -d postgres
  sleep 10
  
  # 创建新的数据库（删除旧的）
  echo "  🗑️ 删除当前数据库..."
  docker-compose exec -T postgres psql -U postgres -c "DROP DATABASE IF EXISTS factorysystem;"
  
  echo "  🆕 创建新数据库..."
  docker-compose exec -T postgres psql -U postgres -c "CREATE DATABASE factorysystem;"
  
  # 恢复数据库
  echo "  📥 恢复数据库数据..."
  docker-compose exec -T postgres psql -U factorysystem factorysystem < "$BACKUP_DIR/database.sql"
  
  echo "✅ 数据库回滚完成"
}

# 函数：恢复应用文件
restore_app_files() {
  echo "📁 恢复应用文件..."
  
  if [ -f "$BACKUP_DIR/app_files.tar.gz" ]; then
    echo "  📦 解压应用文件..."
    tar -xzf "$BACKUP_DIR/app_files.tar.gz"
    echo "✅ 应用文件恢复完成"
  else
    echo "  ⚠️ 备份中没有找到应用文件"
  fi
}

# 函数：重启服务
restart_services() {
  echo "🔄 重启服务..."
  
  # 重新构建（如果需要）
  echo "  🏗️ 重新构建应用..."
  docker-compose build app
  
  # 启动所有服务
  echo "  🚀 启动所有服务..."
  docker-compose up -d
  
  # 等待服务启动
  echo "  ⏳ 等待服务启动..."
  sleep 30
}

# 函数：验证回滚
verify_rollback() {
  echo "✅ 验证回滚..."
  
  # 健康检查
  local max_attempts=10
  local attempt=1
  
  while [ $attempt -le $max_attempts ]; do
    if curl -f -s http://localhost:3000/api/health > /dev/null; then
      echo "  ✅ 健康检查通过"
      break
    fi
    
    echo "  ⏳ 等待服务响应... ($attempt/$max_attempts)"
    sleep 10
    attempt=$((attempt + 1))
  done
  
  if [ $attempt -gt $max_attempts ]; then
    echo "  ❌ 健康检查失败"
    return 1
  fi
  
  # 检查服务状态
  echo "  🔍 检查服务状态..."
  docker-compose ps
  
  # 检查数据库连接
  if [ "$FULL_ROLLBACK" = true ]; then
    echo "  🗄️ 检查数据库连接..."
    if docker-compose exec -T postgres psql -U factorysystem -c "SELECT COUNT(*) FROM factories;" > /dev/null 2>&1; then
      echo "  ✅ 数据库连接正常"
    else
      echo "  ❌ 数据库连接异常"
      return 1
    fi
  fi
  
  echo "✅ 回滚验证成功"
  return 0
}

# 函数：显示回滚摘要
show_rollback_summary() {
  echo ""
  echo "📊 回滚摘要"
  echo "================================"
  echo "回滚时间: $(date)"
  echo "备份来源: $BACKUP_DIR"
  echo "回滚类型: $([ "$FULL_ROLLBACK" = true ] && echo "完整回滚" || echo "代码回滚")"
  echo "当前版本: $(git rev-parse --short HEAD)"
  echo ""
  echo "服务状态:"
  docker-compose ps
  echo ""
  echo "🎉 回滚完成！"
}

# 主函数
main() {
  select_backup
  validate_backup
  confirm_rollback
  
  create_pre_rollback_backup
  
  # 执行回滚
  rollback_code
  
  if [ "$FULL_ROLLBACK" = true ]; then
    rollback_database
    restore_app_files
  fi
  
  restart_services
  
  if verify_rollback; then
    show_rollback_summary
    echo "✅ 回滚成功完成！"
  else
    echo "❌ 回滚验证失败，请检查系统状态"
    exit 1
  fi
}

# 执行主函数
main "$@"
