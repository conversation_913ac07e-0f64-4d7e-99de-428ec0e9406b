'use client'

import React, { useState } from 'react'
import { SpatialLayoutViewer } from '@/components/spatial-layout-viewer'
import { SpatialLayout, SpatialTextItem } from '@/lib/spatial-text-analyzer'

export default function SpatialOCRTestPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [ocrResult, setOcrResult] = useState<any>(null)
  const [spatialLayout, setSpatialLayout] = useState<SpatialLayout | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [selectedItem, setSelectedItem] = useState<SpatialTextItem | null>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      
      // 创建图片预览
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
      
      // 清空之前的结果
      setOcrResult(null)
      setSpatialLayout(null)
      setSelectedItem(null)
    }
  }

  const handleOCRProcess = async () => {
    if (!selectedFile) return

    setIsProcessing(true)
    try {
      const formData = new FormData()
      formData.append('files', selectedFile)
      formData.append('useAccurate', 'true')
      formData.append('recognitionType', 'auto')

      const response = await fetch('/api/ocr/intelligent', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`OCR请求失败: ${response.status}`)
      }

      const result = await response.json()
      console.log('OCR结果:', result)

      if (result.success) {
        setOcrResult(result)
        if (result.data.spatialAnalysis?.layout) {
          setSpatialLayout(result.data.spatialAnalysis.layout)
        }
      } else {
        throw new Error(result.error || 'OCR识别失败')
      }
    } catch (error) {
      console.error('OCR处理失败:', error)
      alert(`OCR处理失败: ${error.message}`)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleItemClick = (item: SpatialTextItem) => {
    setSelectedItem(item)
  }

  const getRecognitionSummary = () => {
    if (!spatialLayout) return null

    const typeCount = spatialLayout.items.reduce((acc, item) => {
      acc[item.type] = (acc[item.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return typeCount
  }

  const summary = getRecognitionSummary()

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h1 className="text-2xl font-bold mb-4">🗺️ 空间感知OCR识别测试</h1>
        <p className="text-gray-600 mb-6">
          上传图片进行OCR识别，查看文字在原始坐标位置的空间布局分析
        </p>

        {/* 文件上传 */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">选择图片文件</label>
            <input
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>

          {selectedFile && (
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                已选择: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
              </span>
              <button
                onClick={handleOCRProcess}
                disabled={isProcessing}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? '识别中...' : '开始识别'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 处理进度 */}
      {isProcessing && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            <span className="text-blue-800">正在进行空间感知OCR识别...</span>
          </div>
        </div>
      )}

      {/* 识别结果概览 */}
      {ocrResult && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">📊 识别结果概览</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {ocrResult.data.statistics.totalWords || 0}
              </div>
              <div className="text-sm text-gray-600">识别文字数</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {ocrResult.data.statistics.spatialItems || 0}
              </div>
              <div className="text-sm text-gray-600">空间文本项</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {ocrResult.data.statistics.spatialRelationships || 0}
              </div>
              <div className="text-sm text-gray-600">空间关系</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {spatialLayout?.width || 0} × {spatialLayout?.height || 0}
              </div>
              <div className="text-sm text-gray-600">图像尺寸</div>
            </div>
          </div>

          {/* 类型统计 */}
          {summary && (
            <div className="mt-4">
              <h3 className="font-medium mb-2">文本类型分布</h3>
              <div className="flex flex-wrap gap-2">
                {Object.entries(summary).map(([type, count]) => (
                  <span
                    key={type}
                    className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    {type}: {count}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 空间布局可视化 */}
      {spatialLayout && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">🗺️ 空间布局可视化</h2>
          <SpatialLayoutViewer
            layout={spatialLayout}
            originalImage={imagePreview || undefined}
            onItemClick={handleItemClick}
            showRelationships={true}
          />
        </div>
      )}

      {/* 原始图片预览 */}
      {imagePreview && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">📷 原始图片</h2>
          <div className="max-w-full overflow-auto">
            <img
              src={imagePreview}
              alt="原始图片"
              className="max-w-full h-auto border rounded-lg"
              style={{ maxHeight: '400px' }}
            />
          </div>
        </div>
      )}

      {/* 识别文本详情 */}
      {spatialLayout && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">📝 识别文本详情</h2>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {spatialLayout.items.map((item, index) => (
              <div
                key={item.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedItem?.id === item.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedItem(item)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-mono text-gray-500">
                      #{index + 1}
                    </span>
                    <span className="font-medium">{item.text}</span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      item.type === 'floor' ? 'bg-red-100 text-red-800' :
                      item.type === 'room' ? 'bg-green-100 text-green-800' :
                      item.type === 'dimension' ? 'bg-blue-100 text-blue-800' :
                      item.type === 'note' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {item.type}
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">
                    置信度: {(item.confidence * 100).toFixed(0)}%
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  位置: ({item.position.left}, {item.position.top}) 
                  尺寸: {item.position.width} × {item.position.height}
                  {item.relatedItems.length > 0 && (
                    <span className="ml-2">
                      关联: {item.relatedItems.length} 个
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 原始OCR数据 */}
      {ocrResult && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">🔍 原始OCR数据</h2>
          <details className="space-y-2">
            <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
              点击查看详细数据
            </summary>
            <pre className="bg-gray-50 p-4 rounded-lg text-xs overflow-auto max-h-96">
              {JSON.stringify(ocrResult, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  )
}
