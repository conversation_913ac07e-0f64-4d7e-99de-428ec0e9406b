/**
 * 🇨🇳 风口云平台 - 工厂公告组件
 * 
 * 在工厂页面顶部显示相关公告的组件
 * 支持显示、标记已读、关闭等功能
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  X,
  ChevronDown,
  ChevronUp,
  AlertTriangle,
  Info,
  AlertCircle,
  CheckCircle
} from "lucide-react"
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'

interface Announcement {
  id: string
  title: string
  content: string
  type: 'info' | 'warning' | 'urgent'
  publishedAt: Date
  expiresAt?: Date
  targetFactories: string[]
}

interface AnnouncementReadStatus {
  [announcementId: string]: boolean
}

export function FactoryAnnouncements() {
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [readStatus, setReadStatus] = useState<AnnouncementReadStatus>({})
  const [loading, setLoading] = useState(true)
  const [expandedAnnouncements, setExpandedAnnouncements] = useState<Set<string>>(new Set())

  useEffect(() => {
    loadAnnouncements()
  }, [])

  const loadAnnouncements = async () => {
    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()

      if (!factoryId) {
        console.log('⚠️ 无法获取工厂ID，跳过公告加载')
        return
      }

      console.log('📢 加载工厂活跃公告...', factoryId)

      // 获取工厂活跃公告（只显示未读且未关闭的公告）
      const activeAnnouncements = await db.getActiveAnnouncementsForFactory(factoryId)
      console.log('📋 获取到活跃公告:', activeAnnouncements.length, '条')

      // 获取已读状态（虽然活跃公告都是未读的，但为了保持一致性）
      await loadReadStatus(factoryId, activeAnnouncements)

      setAnnouncements(activeAnnouncements)
    } catch (error) {
      console.error('❌ 加载公告失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadReadStatus = async (factoryId: string, announcements: Announcement[]) => {
    try {
      const response = await fetch(`/api/announcements/read-status?factoryId=${factoryId}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setReadStatus(data.readStatus || {})
          console.log('✅ 已读状态加载成功:', Object.keys(data.readStatus || {}).length, '条记录')
        }
      }
    } catch (error) {
      console.error('❌ 加载已读状态失败:', error)
    }
  }

  const markAsRead = async (announcementId: string) => {
    try {
      const factoryId = getCurrentFactoryId()
      if (!factoryId) return

      const response = await fetch('/api/announcements/mark-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factoryId,
          announcementId
        })
      })

      if (!response.ok) {
        console.error('❌ 标记已读失败')
      } else {
        console.log('✅ 公告已标记为已读:', announcementId)
        // 重新加载公告列表，已读的公告将不再显示
        await loadAnnouncements()
      }
    } catch (error) {
      console.error('❌ 标记已读失败:', error)
    }
  }

  const dismissAnnouncement = async (announcementId: string) => {
    try {
      const factoryId = getCurrentFactoryId()
      if (!factoryId) return

      const response = await fetch('/api/announcements/dismiss', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factoryId,
          announcementId
        })
      })

      if (!response.ok) {
        console.error('❌ 关闭公告失败')
      } else {
        console.log('✅ 公告已关闭:', announcementId)
        // 重新加载公告列表，已关闭的公告将不再显示
        await loadAnnouncements()
      }
    } catch (error) {
      console.error('❌ 关闭公告失败:', error)
    }
  }

  const toggleExpanded = async (announcementId: string) => {
    setExpandedAnnouncements(prev => {
      const newSet = new Set(prev)
      if (newSet.has(announcementId)) {
        newSet.delete(announcementId)
      } else {
        newSet.add(announcementId)
        // 展开时自动标记为已读
        if (!readStatus[announcementId]) {
          markAsRead(announcementId)
        }
      }
      return newSet
    })
  }

  const getAnnouncementIcon = (type: string) => {
    switch (type) {
      case 'urgent':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getAnnouncementColor = (type: string) => {
    switch (type) {
      case 'urgent':
        return 'border-red-200 bg-red-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      case 'info':
      default:
        return 'border-blue-200 bg-blue-50'
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'urgent':
        return '紧急'
      case 'warning':
        return '警告'
      case 'info':
      default:
        return '通知'
    }
  }

  // 如果没有公告或正在加载，不显示组件
  if (loading || announcements.length === 0) {
    return null
  }

  return (
    <div className="mb-6 space-y-3">
      {announcements.map((announcement) => {
        const isRead = readStatus[announcement.id]
        const isExpanded = expandedAnnouncements.has(announcement.id)
        
        return (
          <Card 
            key={announcement.id} 
            className={`${getAnnouncementColor(announcement.type)} border-l-4 ${
              isRead ? 'opacity-75' : ''
            }`}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  {getAnnouncementIcon(announcement.type)}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-semibold text-gray-900">{announcement.title}</h4>
                      <Badge variant="outline" className="text-xs">
                        {getTypeText(announcement.type)}
                      </Badge>
                      {!isRead && (
                        <Badge variant="destructive" className="text-xs">
                          未读
                        </Badge>
                      )}
                      {isRead && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-3">
                      {isExpanded ? (
                        <div className="whitespace-pre-wrap">{announcement.content}</div>
                      ) : (
                        <div className="line-clamp-2">{announcement.content}</div>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-gray-500">
                        发布时间: {new Date(announcement.publishedAt).toLocaleString('zh-CN')}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(announcement.id)}
                          className="text-xs"
                        >
                          {isExpanded ? (
                            <>
                              收起 <ChevronUp className="h-3 w-3 ml-1" />
                            </>
                          ) : (
                            <>
                              展开 <ChevronDown className="h-3 w-3 ml-1" />
                            </>
                          )}
                        </Button>
                        
                        {!isRead && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => markAsRead(announcement.id)}
                            className="text-xs text-green-600 hover:text-green-700"
                          >
                            标记已读
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => dismissAnnouncement(announcement.id)}
                  className="text-gray-400 hover:text-gray-600 ml-2"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
