/**
 * 测试复杂度检测
 */

import { complexityDetector } from './services/complexity-detector'

export function testComplexityDetection() {
  const testText = `客厅回风141*30     141*30      客出风568.2*14.5    房间出风205.2*14.7     房间回风93.7*30（单边）  房间回风98*30（单边）   出风271.8*15.1    房间回风88*30（单边）房间出风口243.2*15.1`
  
  console.log('🧪 测试复杂度检测')
  console.log('📝 测试文本:', testText)
  
  const result = complexityDetector.analyzeComplexity(testText)
  
  console.log('📊 复杂度分析结果:')
  console.log('- 是否复杂:', result.isComplex)
  console.log('- 复杂度评分:', result.score)
  console.log('- 推荐模型:', result.recommendedModel)
  console.log('- 置信度:', result.confidence)
  console.log('- 复杂原因:', result.reasons)
  
  return result
}

// 在浏览器控制台中可以调用
if (typeof window !== 'undefined') {
  (window as any).testComplexity = testComplexityDetection
}
