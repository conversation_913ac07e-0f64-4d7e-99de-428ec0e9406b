"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle  } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, AlertTriangle, Trash2, Shield } from "lucide-react"
import { db } from '@/lib/database/client'
import { useAuthStore } from '@/lib/store/auth'
import type { Client, User } from '@/types'

// 安全的用户名获取函数，避免在客户端导入Prisma
function safeGetUserUsername(user: User): string | undefined {
  if (user && 'username' in user) {
    return user.username
  }
  return undefined
}

interface DeleteClientDialogProps {
  client: Client | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onClientDeleted?: (clientId: string) => void
}

export function DeleteClientDialog({
  client,
  open,
  onOpenChange,
  onClientDeleted
}: DeleteClientDialogProps) {
  const [loading, setLoading] = useState(false)
  const [userPassword, setUserPassword] = useState("")
  const [error, setError] = useState("")
  const [step, setStep] = useState<'confirm' | 'password'>('confirm')
  const { user } = useAuthStore()

  // 重置状态
  const resetState = () => {
    setUserPassword("")
    setError("")
    setStep('confirm')
    setLoading(false)
  }

  // 处理对话框关闭
  const handleClose = () => {
    resetState()
    onOpenChange(false)
  }

  // 确认删除
  const handleConfirmDelete = () => {
    setStep('password')
    setError("")
  }

  // 验证工厂用户密码并删除客户
  const handleDeleteClient = async () => {
    if (!client || !userPassword.trim()) {
      setError("请输入您的登录密码")
      return
    }

    const username = safeGetUserUsername(user as User)
    if (!username) {
      setError("无法获取当前用户信息")
      return
    }

    try {
      setLoading(true)
      setError("")

      // 验证当前工厂用户密码
      console.log('🔐 验证工厂用户密码...', username)
      const isValidPassword = await db.verifyFactoryUserPassword(username, userPassword)

      if (!isValidPassword) {
        setError("密码错误，请输入您的登录密码")
        return
      }

      console.log('✅ 工厂用户密码验证成功，开始删除客户...')

      // 删除客户
      await db.deleteClient(client.id)

      console.log('✅ 客户删除成功')

      // 通知父组件
      if (onClientDeleted) {
        onClientDeleted(client.id)
      }

      // 关闭对话框
      handleClose()
    } catch (error) {
      console.error('❌ 删除客户失败:', error)
      setError((error as Error).message || "删除客户失败，请重试")
    } finally {
      setLoading(false)
    }
  }

  // 返回上一步
  const handleBack = () => {
    setStep('confirm')
    setError("")
    setUserPassword("")
  }

  if (!client) return null

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 text-red-600">
            <Trash2 className="h-5 w-5" />
            <span>删除客户</span>
          </DialogTitle>
          <DialogDescription>
            {step === 'confirm' ? '此操作不可撤销，请确认是否删除客户' : '请输入您的登录密码以确认删除操作'}
          </DialogDescription>
        </DialogHeader>

        {step === 'confirm' && (
          <div className="space-y-4">
            {/* 客户信息 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">即将删除的客户信息：</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p><strong>客户姓名：</strong>{client.name}</p>
                <p><strong>联系电话：</strong>{client.phone}</p>
                {client.company && <p><strong>公司名称：</strong>{client.company}</p>}
                <p><strong>地址：</strong>{client.address}</p>
              </div>
            </div>

            {/* 警告信息 */}
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <strong>警告：</strong>删除客户将会：
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>永久删除客户的所有基本信息</li>
                  <li>如果客户有订单，需要先删除相关订单</li>
                  <li><strong>自动解除该客户的所有推荐关系</strong></li>
                  <li>此操作不可撤销</li>
                </ul>
              </AlertDescription>
            </Alert>

            {/* 推荐关系提示 */}
            <Alert className="border-blue-200 bg-blue-50">
              <AlertTriangle className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <strong>推荐关系处理：</strong>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>如果该客户推荐了其他客户，系统会自动解除推荐关系</li>
                  <li>被推荐的客户将变为普通客户，不再有推荐人</li>
                  <li>推荐人的奖励统计会自动更新</li>
                </ul>
              </AlertDescription>
            </Alert>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={handleClose}>
                取消
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleConfirmDelete}
                className="bg-red-600 hover:bg-red-700"
              >
                确认删除
              </Button>
            </div>
          </div>
        )}

        {step === 'password' && (
          <div className="space-y-4">
            {/* 当前用户信息提示 */}
            <div className="bg-blue-50 p-3 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>当前用户：</strong>{user?.name || '未知用户'} ({safeGetUserUsername(user as User) || '未知'})
              </p>
              <p className="text-xs text-blue-600 mt-1">
                请输入您的登录密码以确认删除操作
              </p>
            </div>

            {/* 用户密码输入 */}
            <div className="space-y-2">
              <Label htmlFor="userPassword" className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-blue-600" />
                <span>您的登录密码</span>
              </Label>
              <Input
                id="userPassword"
                type="password"
                value={userPassword}
                onChange={(e) => setUserPassword(e.target.value)}
                placeholder="请输入您的登录密码"
                disabled={loading}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !loading) {
                    handleDeleteClient()
                  }
                }}
              />
            </div>

            {/* 错误提示 */}
            {error && (
              <Alert className="border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={handleBack} disabled={loading}>
                返回
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteClient}
                disabled={loading || !userPassword.trim()}
                className="bg-red-600 hover:bg-red-700"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    删除中...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    确认删除
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
