"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle  } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  BarChart3,
  PieChart,
  Download,
  Loader2,
  AlertCircle,
  Users
} from "lucide-react"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import { safeAmount } from "@/lib/utils/number-utils"
import { formatAmount, formatPercentage, getShareholderTypeText } from "@/lib/utils/profit-calculator"

interface ProfitData {
  period: string
  revenue: number      // 收入
  costs: number        // 成本
  expenses: number     // 费用
  profit: number       // 利润
  profitMargin: number // 利润率
}

// 股东利润分配接口
interface ShareholderProfit {
  id: string
  name: string
  shareholderType: string
  sharePercentage: number
  investmentAmount: number
  profitShare: number
}

interface ShareholderProfitAnalysisProps {
  className?: string
}

export function ShareholderProfitAnalysis({ className }: ShareholderProfitAnalysisProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [viewType, setViewType] = useState<'monthly' | 'quarterly' | 'yearly'>('monthly')
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedQuarter, setSelectedQuarter] = useState(Math.floor(new Date().getMonth() / 3) + 1)
  const [profitData, setProfitData] = useState<ProfitData[]>([])
  const [shareholderProfits, setShareholderProfits] = useState<ShareholderProfit[]>([])
  const [summary, setSummary] = useState({
    totalRevenue: 0,
    totalCosts: 0,
    totalProfit: 0,
    averageProfitMargin: 0,
    bestMonth: '',
    worstMonth: ''
  })

  // 生成年份选项（最近5年）
  const yearOptions = Array.from({ length: 5 }, (_, i) => {
    const year = new Date().getFullYear() - i
    return { value: year, label: `${year}年` }
  })

  // 生成月份选项
  const monthOptions = Array.from({ length: 12 }, (_, i) => ({
    value: i + 1,
    label: `${i + 1}月`
  }))

  // 生成季度选项
  const quarterOptions = [
    { value: 1, label: '第1季度' },
    { value: 2, label: '第2季度' },
    { value: 3, label: '第3季度' },
    { value: 4, label: '第4季度' }
  ]

  // 加载利润数据
  const loadProfitData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        setError("无法获取工厂信息")
        return
      }

      const params = new URLSearchParams({
        factoryId,
        viewType,
        year: selectedYear.toString(),
        ...(viewType === 'monthly' && { month: selectedMonth.toString() }),
        ...(viewType === 'quarterly' && { quarter: selectedQuarter.toString() })
      })

      const response = await fetch(`/api/shareholders/profit-analysis?${params}`)
      const result = await response.json()

      if (result.success) {
        setProfitData(result.data || [])
        setShareholderProfits(result.shareholders || [])
        setSummary(result.summary || {
          totalRevenue: 0,
          totalCosts: 0,
          totalProfit: 0,
          averageProfitMargin: 0,
          bestMonth: '',
          worstMonth: ''
        })
      } else {
        setError(result.error || "获取利润数据失败")
      }
    } catch (error) {
      console.error('加载利润数据失败:', error)
      setError("加载利润数据失败")
    } finally {
      setLoading(false)
    }
  }

  // 导出报表
  const handleExportReport = async () => {
    try {
      const factoryId = getCurrentFactoryId()
      if (!factoryId) return

      const params = new URLSearchParams({
        factoryId,
        viewType,
        year: selectedYear.toString(),
        ...(viewType === 'monthly' && { month: selectedMonth.toString() }),
        ...(viewType === 'quarterly' && { quarter: selectedQuarter.toString() }),
        export: 'true'
      })

      const response = await fetch(`/api/shareholders/profit-analysis?${params}`)
      const blob = await response.blob()
      
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url

      let fileName = `股东利润分析报表_${selectedYear}`
      if (viewType === 'monthly') {
        fileName += `_${selectedMonth}月`
      } else if (viewType === 'quarterly') {
        fileName += `_第${selectedQuarter}季度`
      } else {
        fileName += '年'
      }
      fileName += '.xlsx'

      a.download = fileName
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('导出报表失败:', error)
      alert('导出报表失败，请重试')
    }
  }

  // 计算利润趋势
  const getProfitTrend = () => {
    if (profitData.length < 2) return 'stable'
    const recent = profitData.slice(-2)
    const change = recent[1].profit - recent[0].profit
    return change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
  }

  // 页面加载和参数变化时重新加载数据
  useEffect(() => {
    loadProfitData()
  }, [viewType, selectedYear, selectedMonth, selectedQuarter])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            <span>利润分析</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4">
            {/* 查看类型 */}
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">查看方式:</label>
              <Select value={viewType} onValueChange={(value: 'monthly' | 'quarterly' | 'yearly') => setViewType(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">按月查看</SelectItem>
                  <SelectItem value="quarterly">按季度查看</SelectItem>
                  <SelectItem value="yearly">按年查看</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 年份选择 */}
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">年份:</label>
              <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {yearOptions.map(option => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 月份选择（仅月度查看时显示） */}
            {viewType === 'monthly' && (
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">月份:</label>
                <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {monthOptions.map(option => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* 季度选择（仅季度查看时显示） */}
            {viewType === 'quarterly' && (
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">季度:</label>
                <Select value={selectedQuarter.toString()} onValueChange={(value) => setSelectedQuarter(parseInt(value))}>
                  <SelectTrigger className="w-28">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {quarterOptions.map(option => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* 导出按钮 */}
            <Button
              variant="outline"
              onClick={handleExportReport}
              disabled={loading || profitData.length === 0}
              className="ml-auto"
            >
              <Download className="h-4 w-4 mr-2" />
              导出报表
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 加载状态 */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">加载利润数据中...</span>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={loadProfitData} variant="outline">
              重试
            </Button>
          </div>
        </div>
      )}

      {/* 数据展示 */}
      {!loading && !error && (
        <>
          {/* 汇总统计 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <DollarSign className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总收入</p>
                    <p className="text-2xl font-bold text-gray-900">{safeAmount(summary.totalRevenue)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总利润</p>
                    <p className="text-2xl font-bold text-gray-900">{safeAmount(summary.totalProfit)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <PieChart className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">平均利润率</p>
                    <p className="text-2xl font-bold text-gray-900">{summary.averageProfitMargin.toFixed(1)}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Calendar className="h-8 w-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">利润趋势</p>
                    <div className="flex items-center space-x-1">
                      {getProfitTrend() === 'up' && <TrendingUp className="h-4 w-4 text-green-600" />}
                      {getProfitTrend() === 'down' && <TrendingDown className="h-4 w-4 text-red-600" />}
                      <Badge className={
                        getProfitTrend() === 'up' ? 'bg-green-100 text-green-800' :
                        getProfitTrend() === 'down' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }>
                        {getProfitTrend() === 'up' ? '上升' : getProfitTrend() === 'down' ? '下降' : '稳定'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 详细数据标签页 */}
          <Card>
            <CardContent className="p-6">
              <Tabs defaultValue="profit-details" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="profit-details" className="flex items-center space-x-2">
                    <BarChart3 className="h-4 w-4" />
                    <span>利润明细</span>
                  </TabsTrigger>
                  <TabsTrigger value="shareholder-allocation" className="flex items-center space-x-2">
                    <Users className="h-4 w-4" />
                    <span>股东分配</span>
                  </TabsTrigger>
                </TabsList>

                {/* 利润明细标签页 */}
                <TabsContent value="profit-details" className="mt-6">
                  {profitData.length > 0 ? (
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        {viewType === 'monthly' ? `${selectedYear}年${selectedMonth}月` :
                         viewType === 'quarterly' ? `${selectedYear}年第${selectedQuarter}季度` :
                         `${selectedYear}年`}利润明细
                      </h3>
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-3 font-medium text-gray-900">期间</th>
                              <th className="text-right p-3 font-medium text-gray-900">收入</th>
                              <th className="text-right p-3 font-medium text-gray-900">成本</th>
                              <th className="text-right p-3 font-medium text-gray-900">费用</th>
                              <th className="text-right p-3 font-medium text-gray-900">利润</th>
                              <th className="text-right p-3 font-medium text-gray-900">利润率</th>
                            </tr>
                          </thead>
                          <tbody>
                            {profitData.map((item, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50">
                                <td className="p-3 text-gray-900">{item.period}</td>
                                <td className="p-3 text-right text-green-600 font-medium">{safeAmount(item.revenue)}</td>
                                <td className="p-3 text-right text-red-600">{safeAmount(item.costs)}</td>
                                <td className="p-3 text-right text-orange-600">{safeAmount(item.expenses)}</td>
                                <td className="p-3 text-right font-medium">
                                  <span className={item.profit >= 0 ? 'text-green-600' : 'text-red-600'}>
                                    {safeAmount(item.profit)}
                                  </span>
                                </td>
                                <td className="p-3 text-right">
                                  <Badge className={
                                    item.profitMargin >= 20 ? 'bg-green-100 text-green-800' :
                                    item.profitMargin >= 10 ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }>
                                    {item.profitMargin.toFixed(1)}%
                                  </Badge>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">暂无利润数据</h3>
                      <p className="text-gray-600">选择的时间段内暂无利润数据</p>
                    </div>
                  )}
                </TabsContent>

                {/* 股东分配标签页 */}
                <TabsContent value="shareholder-allocation" className="mt-6">
                  {shareholderProfits.length > 0 ? (
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">
                          股东利润分配 (总利润: {formatAmount(summary.totalProfit)})
                        </h3>
                        <Badge variant="outline" className="text-sm">
                          {shareholderProfits.length} 位活跃股东
                        </Badge>
                      </div>
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-3 font-medium text-gray-900">股东姓名</th>
                              <th className="text-left p-3 font-medium text-gray-900">股东类型</th>
                              <th className="text-right p-3 font-medium text-gray-900">持股比例</th>
                              <th className="text-right p-3 font-medium text-gray-900">投资金额</th>
                              <th className="text-right p-3 font-medium text-gray-900">应分利润</th>
                              <th className="text-right p-3 font-medium text-gray-900">投资回报率</th>
                            </tr>
                          </thead>
                          <tbody>
                            {shareholderProfits.map((shareholder, index) => {
                              const roi = shareholder.investmentAmount > 0 ?
                                (shareholder.profitShare / shareholder.investmentAmount) * 100 : 0
                              return (
                                <tr key={index} className="border-b hover:bg-gray-50">
                                  <td className="p-3 text-gray-900 font-medium">{shareholder.name}</td>
                                  <td className="p-3">
                                    <Badge variant="outline" className="text-xs">
                                      {getShareholderTypeText(shareholder.shareholderType)}
                                    </Badge>
                                  </td>
                                  <td className="p-3 text-right font-medium">
                                    {formatPercentage(shareholder.sharePercentage)}
                                  </td>
                                  <td className="p-3 text-right text-gray-600">
                                    {formatAmount(shareholder.investmentAmount)}
                                  </td>
                                  <td className="p-3 text-right font-medium">
                                    <span className={shareholder.profitShare >= 0 ? 'text-green-600' : 'text-red-600'}>
                                      {formatAmount(shareholder.profitShare)}
                                    </span>
                                  </td>
                                  <td className="p-3 text-right">
                                    <Badge className={
                                      roi >= 10 ? 'bg-green-100 text-green-800' :
                                      roi >= 5 ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-red-100 text-red-800'
                                    }>
                                      {formatPercentage(roi)}
                                    </Badge>
                                  </td>
                                </tr>
                              )
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">暂无股东数据</h3>
                      <p className="text-gray-600">当前工厂暂无活跃股东信息</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
