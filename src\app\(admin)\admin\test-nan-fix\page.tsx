"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { safeNumber, safeWanYuan, safeLocaleString, safeDivide } from "@/lib/utils/number-utils"
import { AlertCircle, CheckCircle, RefreshCw } from "lucide-react"

export default function TestNaNFixPage() {
  const [testResults, setTestResults] = useState<unknown[]>([])
  const [loading, setLoading] = useState(false)

  const runTests = () => {
    setLoading(true)
    
    // 模拟各种可能导致NaN的数据情况
    const testCases = [
      {
        name: "正常数据",
        totalRevenue: 15680000,
        totalOrders: 3567,
        expected: "正常显示"
      },
      {
        name: "空值数据",
        totalRevenue: null,
        totalOrders: 0,
        expected: "显示为0"
      },
      {
        name: "undefined数据",
        totalRevenue: undefined,
        totalOrders: undefined,
        expected: "显示为0"
      },
      {
        name: "NaN数据",
        totalRevenue: NaN,
        totalOrders: NaN,
        expected: "显示为0"
      },
      {
        name: "字符串数据",
        totalRevenue: "2500034405.5",
        totalOrders: "100",
        expected: "正确转换"
      },
      {
        name: "除零情况",
        totalRevenue: 1000000,
        totalOrders: 0,
        expected: "避免除零错误"
      }
    ]

    const results = testCases.map(testCase => {
      const safeRevenue = safeNumber(testCase.totalRevenue)
      const safeOrders = safeNumber(testCase.totalOrders)
      const averageOrderValue = safeDivide(safeRevenue, safeOrders)
      
      return {
        ...testCase,
        results: {
          safeRevenue,
          safeOrders,
          averageOrderValue,
          revenueDisplay: safeWanYuan(safeRevenue, 1),
          averageDisplay: safeLocaleString(averageOrderValue),
          isValid: !isNaN(safeRevenue) && !isNaN(safeOrders) && !isNaN(averageOrderValue)
        }
      }
    })

    setTimeout(() => {
      setTestResults(results)
      setLoading(false)
    }, 1000)
  }

  useEffect(() => {
    runTests()
  }, [])

  return (
    <DashboardLayout role="admin">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <AlertCircle className="h-6 w-6 mr-2 text-orange-600" />
              NaN值修复测试
            </h1>
            <p className="text-gray-600">测试数字处理工具函数是否正确处理各种异常数据</p>
          </div>
          <Button onClick={runTests} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            重新测试
          </Button>
        </div>

        {/* 测试结果 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {testResults.map((test, index) => (
            <Card key={index} className={`border-2 ${test.results.isValid ? 'border-green-200' : 'border-red-200'}`}>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  {test.results.isValid ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-600" />
                  )}
                  <span>{test.name}</span>
                </CardTitle>
                <CardDescription>
                  预期结果: {test.expected}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">原始营收:</p>
                      <p className="font-mono">{String(test.totalRevenue)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">原始订单数:</p>
                      <p className="font-mono">{String(test.totalOrders)}</p>
                    </div>
                  </div>
                  
                  <div className="border-t pt-3">
                    <div className="grid grid-cols-1 gap-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">安全营收:</span>
                        <span className="font-medium">{test.results.safeRevenue}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">安全订单数:</span>
                        <span className="font-medium">{test.results.safeOrders}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">平均订单价值:</span>
                        <span className="font-medium">¥{test.results.averageDisplay}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">营收显示:</span>
                        <span className="font-medium text-purple-600">{test.results.revenueDisplay}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className={`text-xs p-2 rounded ${test.results.isValid ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
                    {test.results.isValid ? '✅ 测试通过' : '❌ 测试失败'}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 修复说明 */}
        <Card className="mt-8 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-800">修复说明</CardTitle>
          </CardHeader>
          <CardContent className="text-blue-700">
            <div className="space-y-2 text-sm">
              <p>• <strong>safeNumber():</strong> 安全地将任何值转换为数字，无效值返回0</p>
              <p>• <strong>safeWanYuan():</strong> 安全地格式化万元显示，避免NaN万的情况</p>
              <p>• <strong>safeDivide():</strong> 安全的除法运算，避免除零错误</p>
              <p>• <strong>safeLocaleString():</strong> 安全的千分位格式化</p>
              <p>• 所有数据计算和显示都使用这些安全函数，确保不会出现NaN值</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
