/**
 * 🇨🇳 风口云平台 - HTTP客户端
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 统一的API请求客户端
 * - 自动处理认证令牌
 * - 自动令牌刷新
 * - 错误处理和重试机制
 */

import { useAuthStore } from '@/lib/store/auth'

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 请求配置接口
export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: unknown
  requireAuth?: boolean
  retryOnTokenExpired?: boolean
}

// HTTP客户端类
class HttpClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>

  constructor() {
    // 🔧 修复：正确处理API基础URL
    // 在开发环境中，如果没有设置NEXT_PUBLIC_API_BASE_URL，使用当前域名
    this.baseURL = process.env.NEXT_PUBLIC_API_BASE_URL ||
                   (typeof window !== 'undefined' ? window.location.origin : '')
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  /**
   * 获取认证头
   */
  private getAuthHeaders(): Record<string, string> {
    const { accessToken, tokenType, isAuthenticated, user } = useAuthStore.getState()

    console.log('🔍 获取认证头状态:', {
      hasAccessToken: !!accessToken,
      tokenType,
      isAuthenticated,
      userName: user?.name,
      timestamp: new Date().toISOString()
    })

    if (!accessToken || !tokenType) {
      console.warn('⚠️ 认证信息缺失:', { accessToken: !!accessToken, tokenType })
      return {}
    }

    return {
      'Authorization': `${tokenType} ${accessToken}`
    }
  }

  /**
   * 刷新令牌
   */
  private async refreshToken(): Promise<boolean> {
    try {
      const { refreshToken, updateTokens, logout } = useAuthStore.getState()
      
      if (!refreshToken) {
        logout()
        return false
      }

      const response = await fetch(`${this.baseURL}/api/auth/refresh`, {
        method: 'POST',
        headers: this.defaultHeaders,
        body: JSON.stringify({ refreshToken })
      })

      if (!response.ok) {
        logout()
        return false
      }

      const data = await response.json()
      
      if (data.success) {
        updateTokens({
          accessToken: data.accessToken,
          refreshToken: data.refreshToken,
          tokenType: data.tokenType,
          expiresIn: data.expiresIn
        })
        return true
      }

      logout()
      return false
    } catch (error) {
      console.error('❌ 令牌刷新失败:', error)
      useAuthStore.getState().logout()
      return false
    }
  }

  /**
   * 发送HTTP请求
   */
  async request<T = any>(
    url: string, 
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      requireAuth = true,
      retryOnTokenExpired = true
    } = config

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`

    // 构建请求头
    let requestHeaders = { ...this.defaultHeaders, ...headers }
    
    if (requireAuth) {
      const authHeaders = this.getAuthHeaders()
      if (Object.keys(authHeaders).length === 0) {
        return {
          success: false,
          error: '未登录',
          message: '请先登录'
        }
      }
      requestHeaders = { ...requestHeaders, ...authHeaders }
    }

    // 构建请求配置
    const requestConfig: RequestInit = {
      method,
      headers: requestHeaders,
    }

    if (body && method !== 'GET') {
      requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body)
    }

    try {
      const response = await fetch(fullUrl, requestConfig)
      
      // 如果是401错误且需要认证，尝试刷新令牌
      if (response.status === 401 && requireAuth && retryOnTokenExpired) {
        const refreshSuccess = await this.refreshToken()
        
        if (refreshSuccess) {
          // 重新构建认证头并重试请求
          const newAuthHeaders = this.getAuthHeaders()
          requestHeaders = { ...requestHeaders, ...newAuthHeaders }
          requestConfig.headers = requestHeaders
          
          const retryResponse = await fetch(fullUrl, requestConfig)
          return await this.handleResponse<T>(retryResponse)
        }
      }

      return await this.handleResponse<T>(response)
    } catch (error) {
      console.error('❌ 请求失败:', error)
      return {
        success: false,
        error: '网络错误',
        message: error instanceof Error ? error.message : '请求失败'
      }
    }
  }

  /**
   * 处理响应
   */
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      const data = await response.json()

      if (response.ok) {
        // 🔧 修复：如果服务器返回的是标准格式 {success: true, data: ...}，则提取data字段
        if (data.success === true && data.data !== undefined) {
          return {
            success: true,
            data: data.data,
            message: data.message
          }
        }
        // 否则直接返回原始数据
        return {
          success: true,
          data: data,
          message: data.message
        }
      } else {
        return {
          success: false,
          error: data.error || '请求失败',
          message: data.message || response.statusText
        }
      }
    } catch (error) {
      return {
        success: false,
        error: '响应解析失败',
        message: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'GET' })
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, body?: unknown, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'POST', body })
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, body?: unknown, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'PUT', body })
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'DELETE' })
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, body?: unknown, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'PATCH', body })
  }
}

// 创建全局HTTP客户端实例
export const httpClient = new HttpClient()

// 导出便捷方法
export const api = {
  get: httpClient.get.bind(httpClient),
  post: httpClient.post.bind(httpClient),
  put: httpClient.put.bind(httpClient),
  delete: httpClient.delete.bind(httpClient),
  patch: httpClient.patch.bind(httpClient),
}

export default httpClient
