/**
 * 🇨🇳 风口云平台 - 客户管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminOrFactoryAuth, validateFactoryAccess } from '@/lib/middleware/auth'

// 获取客户列表
export const GET = withAdminOrFactoryAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')

    if (!factoryId) {
      return NextResponse.json(
        { error: '缺少工厂ID参数' },
        { status: 400 }
      )
    }

    // 验证工厂访问权限
    const validatedFactoryId = validateFactoryAccess(request, user)
    if (!validatedFactoryId || validatedFactoryId !== factoryId) {
      console.log('❌ 工厂访问权限验证失败:', {
        userType: user.userType,
        userFactoryId: user.factoryId,
        requestFactoryId: factoryId,
        validatedFactoryId
      })
      return NextResponse.json(
        { error: '无权访问该工厂数据' },
        { status: 403 }
      )
    }

    console.log('✅ 工厂访问权限验证通过:', {
      userType: user.userType,
      factoryId: validatedFactoryId
    })

    const clients = await db.getClientsByFactoryId(factoryId)
    return NextResponse.json({ success: true, clients })

  } catch (error) {
    console.error('❌ 获取客户失败:', error)
    return NextResponse.json(
      { error: '获取客户失败' },
      { status: 500 }
    )
  }
})

// 创建客户
export const POST = withAdminOrFactoryAuth(async (request: NextRequest, user) => {
  try {
    const rawClientData = await request.json()

    console.log('🔍 创建客户请求详情:', {
      userType: user.userType,
      userFactoryId: user.factoryId,
      requestFactoryId: rawClientData.factoryId,
      userName: user.username,
      clientName: rawClientData.name,
      clientPhone: rawClientData.phone
    })

    if (!rawClientData.factoryId || !rawClientData.name || !rawClientData.phone) {
      return NextResponse.json(
        { error: '缺少必要的客户信息' },
        { status: 400 }
      )
    }

    // 验证工厂访问权限（管理员可以在任何工厂创建客户）
    if (user.userType !== 'admin') {
      console.log('🔐 验证工厂用户权限...')

      // 工厂用户只能在自己的工厂创建客户
      if (user.factoryId !== rawClientData.factoryId) {
        console.log('❌ 权限验证失败:', {
          userFactoryId: user.factoryId,
          requestFactoryId: rawClientData.factoryId
        })
        return NextResponse.json(
          { error: '无权在该工厂创建客户' },
          { status: 403 }
        )
      }

      console.log('✅ 权限验证通过')
    } else {
      console.log('✅ 管理员权限，跳过工厂权限检查')
    }

    // 过滤掉不存在的字段，只保留数据库schema中存在的字段
    const clientData = {
      factoryId: rawClientData.factoryId,
      name: rawClientData.name,
      phone: rawClientData.phone,
      email: rawClientData.email,
      company: rawClientData.company,
      address: rawClientData.address,
      referrerId: rawClientData.referrerId,
      referrerName: rawClientData.referrerName
      // 不传递统计字段，让数据库使用默认值
    }

    const client = await db.createClient(clientData)

    if (!client) {
      return NextResponse.json(
        { error: '创建客户失败' },
        { status: 500 }
      )
    }

    console.log('✅ 客户创建成功:', {
      clientId: client.id,
      clientName: client.name,
      clientPhone: client.phone
    })

    return NextResponse.json({
      success: true,
      client
    })

  } catch (error) {
    console.error('❌ 创建客户失败:', error)

    // 检查是否是唯一性约束违反错误
    if (error.code === 'P2002' && error.meta?.target?.includes('phone')) {
      console.log('📞 检测到电话号码重复错误:', {
        phone: rawClientData.phone,
        factoryId: rawClientData.factoryId
      })

      return NextResponse.json(
        {
          error: '该电话号码已存在，请检查是否重复创建客户',
          code: 'DUPLICATE_PHONE'
        },
        { status: 409 }
      )
    }

    // 其他错误
    return NextResponse.json(
      { error: '创建客户失败' },
      { status: 500 }
    )
  }
})
