/**
 * 调试API - 查看工厂用户
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const users = await db.getAllFactoryUsers()
    
    return NextResponse.json({
      success: true,
      users: users.map(user => ({
        id: user.id,
        username: user.username,
        name: user.name,
        factoryId: user.factoryId,
        role: user.role,
        isActive: user.isActive,
        factory: user.factory ? {
          id: user.factory.id,
          name: user.factory.name,
          code: user.factory.code,
          status: user.factory.status
        } : null
      }))
    })
  } catch (error) {
    console.error('获取工厂用户失败:', error)
    return NextResponse.json({
      success: false,
      error: '获取工厂用户失败'
    }, { status: 500 })
  }
}
