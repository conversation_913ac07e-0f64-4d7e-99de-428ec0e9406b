/**
 * 文本复杂度检测器
 * 用于判断是否需要使用R1推理模型
 */

export interface ComplexityAnalysis {
  isComplex: boolean
  score: number
  reasons: string[]
  recommendedModel: 'deepseek-chat' | 'deepseek-reasoner'
  confidence: number
}

export class ComplexityDetector {
  
  /**
   * 分析文本复杂度
   */
  analyzeComplexity(text: string): ComplexityAnalysis {
    let score = 0
    const reasons: string[] = []
    
    // 1. 文本长度检测
    const textLength = text.length
    if (textLength > 1000) {
      score += 2
      reasons.push('文本较长，可能包含复杂信息')
    }
    
    // 2. 项目数量检测
    const projectCount = this.countProjects(text)
    if (projectCount > 1) {
      score += 3
      reasons.push(`包含${projectCount}个项目，需要复杂解析`)
    }
    
    // 3. 特殊格式检测
    const hasSpecialFormats = this.detectSpecialFormats(text)
    if (hasSpecialFormats.length > 0) {
      score += 2
      reasons.push(`包含特殊格式: ${hasSpecialFormats.join(', ')}`)
    }
    
    // 4. 异常尺寸格式检测
    const abnormalDimensions = this.detectAbnormalDimensions(text)
    if (abnormalDimensions.length > 0) {
      score += 3  // 增加权重，因为尺寸异常需要R1的推理能力
      reasons.push(`包含异常尺寸格式: ${abnormalDimensions.join(', ')}`)
    }
    
    // 5. 复杂房间结构检测
    const roomComplexity = this.analyzeRoomComplexity(text)
    if (roomComplexity > 5) {
      score += 2
      reasons.push('房间结构复杂')
    }
    
    // 6. 模糊信息检测
    const hasAmbiguousInfo = this.detectAmbiguousInfo(text)
    if (hasAmbiguousInfo.length > 0) {
      score += 3
      reasons.push(`包含模糊信息: ${hasAmbiguousInfo.join(', ')}`)
    }
    
    // 7. 错误格式检测
    const hasErrors = this.detectFormatErrors(text)
    if (hasErrors.length > 0) {
      score += 4
      reasons.push(`包含格式错误: ${hasErrors.join(', ')}`)
    }
    
    // 8. 多楼层检测
    const floorCount = this.countFloors(text)
    if (floorCount > 1) {
      score += 2
      reasons.push(`包含${floorCount}个楼层`)
    }
    
    // 判断复杂度 - 但基于测试结果，V3在风口识别任务上表现更好
    const isComplex = score >= 5
    const recommendedModel = 'deepseek-chat'  // 强制推荐V3，因为在风口识别上更准确
    const confidence = Math.min(score / 10, 1)
    
    return {
      isComplex,
      score,
      reasons,
      recommendedModel,
      confidence
    }
  }
  
  /**
   * 检测项目数量
   */
  private countProjects(text: string): number {
    // 检测多个项目名称的模式
    const projectPatterns = [
      /[一二三四五六七八九十\d]+[栋楼号]/g,
      /[^\s]+小区|[^\s]+花园|[^\s]+大厦|[^\s]+广场/g,
      /项目[：:]/g
    ]
    
    const projects = new Set<string>()
    projectPatterns.forEach(pattern => {
      const matches = text.match(pattern)
      if (matches) {
        matches.forEach(match => projects.add(match))
      }
    })
    
    return Math.max(projects.size, 1)
  }
  
  /**
   * 检测特殊格式
   */
  private detectSpecialFormats(text: string): string[] {
    const formats: string[] = []
    
    // 表格格式
    if (text.includes('|') && text.split('|').length > 3) {
      formats.push('表格格式')
    }
    
    // 编号列表
    if (/^\s*\d+[.、]/m.test(text)) {
      formats.push('编号列表')
    }
    
    // 特殊分隔符
    if (text.includes('---') || text.includes('===')) {
      formats.push('特殊分隔符')
    }
    
    // 多种尺寸分隔符混用
    const separators = [text.includes('×'), text.includes('X'), text.includes('✘'), text.includes('*')]
    if (separators.filter(Boolean).length > 2) {
      formats.push('多种尺寸分隔符')
    }
    
    return formats
  }
  
  /**
   * 检测异常尺寸格式
   */
  private detectAbnormalDimensions(text: string): string[] {
    const abnormalTypes: string[] = [];

    // 检测异常的尺寸格式
    const patterns = [
      { pattern: /\d+[×X✘*]\d+[×X✘*]\d+/g, type: '三维尺寸' },
      { pattern: /\d+\.\d+[×X✘*]\d+/g, type: '小数尺寸' },
      { pattern: /\d+[×X✘*]\d+\.\d+/g, type: '小数尺寸' },
      { pattern: /[×X✘*]\d+[×X✘*]/g, type: '连续分隔符' },
      { pattern: /\d+[×X✘*]$/g, type: '不完整尺寸' },
      { pattern: /^[×X✘*]\d+/g, type: '不完整尺寸' },
      { pattern: /\d{1,2}[×X✘*]\d{1,2}(?!\d)/g, type: '疑似单位错误' } // 如141*30
    ];

    patterns.forEach(({ pattern, type }) => {
      // 重置正则表达式的lastIndex
      pattern.lastIndex = 0;
      if (pattern.test(text) && !abnormalTypes.includes(type)) {
        abnormalTypes.push(type);
      }
    });

    return abnormalTypes;
  }
  
  /**
   * 分析房间复杂度
   */
  private analyzeRoomComplexity(text: string): number {
    let complexity = 0
    
    // 房间数量
    const rooms = text.match(/[客餐主次卧厅房间厨卫浴阳台书办公]/g)
    if (rooms) {
      complexity += Math.min(rooms.length, 5)
    }
    
    // 特殊房间类型
    const specialRooms = text.match(/储藏室|设备间|走廊|过道|玄关|衣帽间/g)
    if (specialRooms) {
      complexity += specialRooms.length * 2
    }
    
    return complexity
  }
  
  /**
   * 检测模糊信息
   */
  private detectAmbiguousInfo(text: string): string[] {
    const ambiguous: string[] = []
    
    // 模糊的位置描述
    if (/左边|右边|上面|下面|旁边|附近/.test(text)) {
      ambiguous.push('模糊位置描述')
    }
    
    // 不确定的数量
    if (/大概|约|左右|差不多|可能/.test(text)) {
      ambiguous.push('不确定数量')
    }
    
    // 缺失信息
    if (/待定|未定|不确定|不清楚/.test(text)) {
      ambiguous.push('信息缺失')
    }
    
    // 多选项
    if (/或者|或|要么/.test(text)) {
      ambiguous.push('多选项信息')
    }
    
    return ambiguous
  }
  
  /**
   * 检测格式错误
   */
  private detectFormatErrors(text: string): string[] {
    const errors: string[] = []
    
    // 尺寸格式错误
    if (/\d+[×X✘*]\s*[×X✘*]\d+/.test(text)) {
      errors.push('尺寸分隔符重复')
    }
    
    // 单位混乱
    if (text.includes('mm') && text.includes('cm') && text.includes('m')) {
      errors.push('单位混乱')
    }
    
    // 数字格式错误
    if (/\d+[,，]\d+/.test(text) && !/\d+[,，]\d{3}/.test(text)) {
      errors.push('数字格式错误')
    }
    
    // 中英文混乱
    if (/[a-zA-Z][×X✘*][一二三四五六七八九十]/.test(text)) {
      errors.push('中英文数字混用')
    }
    
    return errors
  }
  
  /**
   * 检测楼层数量
   */
  private countFloors(text: string): number {
    const floorPatterns = [
      /[一二三四五六七八九十\d]+楼/g,
      /[一二三四五六七八九十\d]+层/g,
      /[一二三四五六七八九十\d]+F/gi
    ]
    
    const floors = new Set<string>()
    floorPatterns.forEach(pattern => {
      const matches = text.match(pattern)
      if (matches) {
        matches.forEach(match => floors.add(match))
      }
    })
    
    return Math.max(floors.size, 1)
  }
}

export const complexityDetector = new ComplexityDetector()
