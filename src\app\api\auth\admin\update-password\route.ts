/**
 * 🇨🇳 风口云平台 - 管理员密码更新API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const { adminId, currentPassword, newPassword } = await request.json()

    if (!adminId || !currentPassword || !newPassword) {
      return NextResponse.json(
        { error: '管理员ID、当前密码和新密码不能为空' },
        { status: 400 }
      )
    }

    // 验证新密码长度
    if (newPassword.length < 6) {
      return NextResponse.json(
        { error: '新密码长度至少6位' },
        { status: 400 }
      )
    }

    // 更新管理员密码
    const success = await db.updateAdminPassword(adminId, currentPassword, newPassword)

    if (success) {
      return NextResponse.json({
        success: true,
        data: { success: true },
        message: '密码更新成功'
      })
    } else {
      return NextResponse.json(
        { error: '密码更新失败，请检查当前密码是否正确' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('❌ 更新管理员密码失败:', error)
    return NextResponse.json(
      { error: '密码更新服务异常' },
      { status: 500 }
    )
  }
}
