"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { RefreshCw, CheckCircle, AlertCircle, Users, Gift } from "lucide-react"
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'

import { safeAmountFormat } from '@/lib/utils/number-utils'

interface ClientRewardComparison {
  clientId: string
  clientName: string
  phone: string
  referralCount: number
  // 数据库中的奖励数据
  dbReward: {
    referralReward: number
    availableReward: number
    pendingReward: number
  }
  // 实时计算的奖励数据
  calculatedReward: {
    totalReward: number
    availableReward: number
    pendingReward: number
  }
  // 是否有差异
  hasDifference: boolean
}

export default function RewardSyncTestPage() {
  const [loading, setLoading] = useState(false)
  const [comparisons, setComparisons] = useState<ClientRewardComparison[]>([])
  const [syncResult, setSyncResult] = useState<string>('')

  // 检查奖励数据同步状态
  const checkRewardSync = async () => {
    setLoading(true)
    try {
      console.log('🔍 开始检查奖励数据同步状态')

      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        alert('无法获取工厂ID')
        return
      }

      // 获取所有客户
      const clients = await db.getClientsByFactoryId(factoryId)
      console.log(`📋 找到 ${clients.length} 个客户`)

      const results: ClientRewardComparison[] = []

      for (const client of clients) {
        // 计算推荐数量
        const referralCount = clients.filter(c => c.referrerId === client.id).length
        
        if (referralCount > 0) {
          // 获取数据库中的奖励数据
          const dbReward = {
            referralReward: client.referralReward || 0,
            availableReward: client.availableReward || 0,
            pendingReward: client.pendingReward || 0
          }

          // 实时计算奖励数据
          let calculatedReward = {
            totalReward: 0,
            availableReward: 0,
            pendingReward: 0
          }

          try {
            const response = await fetch(`/api/clients/${client.id}/reward-status?factoryId=${factoryId}`)
            if (response.ok) {
              const result = await response.json()
              if (result.success) {
                const rewardResult = result.data
                calculatedReward = {
                  totalReward: rewardResult.totalReward,
                  availableReward: rewardResult.availableReward,
                  pendingReward: rewardResult.pendingReward
                }
              }
            }
          } catch (error) {
            console.warn(`⚠️ 获取客户 ${client.name} 奖励失败:`, error)
          }

          // 检查是否有差异
          const hasDifference = 
            Math.abs(dbReward.referralReward - calculatedReward.totalReward) > 0.01 ||
            Math.abs(dbReward.availableReward - calculatedReward.availableReward) > 0.01 ||
            Math.abs(dbReward.pendingReward - calculatedReward.pendingReward) > 0.01

          results.push({
            clientId: client.id,
            clientName: client.name,
            phone: client.phone,
            referralCount,
            dbReward,
            calculatedReward,
            hasDifference
          })
        }
      }

      setComparisons(results)
      console.log('✅ 奖励数据检查完成:', results)

    } catch (error) {
      console.error('❌ 检查奖励数据失败:', error)
      alert('检查失败，请查看控制台')
    } finally {
      setLoading(false)
    }
  }

  // 同步奖励数据
  const syncRewards = async () => {
    setLoading(true)
    try {
      console.log('🔄 开始同步奖励数据')

      const response = await fetch('/api/clients/sync-rewards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const result = await response.json()

      if (result.success) {
        setSyncResult(`同步成功！更新了 ${result.data.updatedCount} 个客户的奖励数据。`)
        // 重新检查同步状态
        await checkRewardSync()
      } else {
        setSyncResult(`同步失败: ${result.error}`)
      }

    } catch (error) {
      console.error('❌ 同步奖励数据失败:', error)
      setSyncResult('同步失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  const hasAnyDifference = comparisons.some(c => c.hasDifference)

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">推荐奖励同步测试</h1>
            <p className="text-gray-600 mt-2">检查和修复客户推荐奖励显示不同步的问题</p>
          </div>
          <div className="flex space-x-4">
            <Button
              onClick={checkRewardSync}
              disabled={loading}
              variant="outline"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              检查同步状态
            </Button>
            <Button
              onClick={syncRewards}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Gift className="h-4 w-4 mr-2" />
              同步奖励数据
            </Button>
          </div>
        </div>

        {/* 同步结果 */}
        {syncResult && (
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className={`flex items-center ${syncResult.includes('成功') ? 'text-green-600' : 'text-red-600'}`}>
                {syncResult.includes('成功') ? (
                  <CheckCircle className="h-5 w-5 mr-2" />
                ) : (
                  <AlertCircle className="h-5 w-5 mr-2" />
                )}
                {syncResult}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 总体状态 */}
        {comparisons.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                同步状态总览
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{comparisons.length}</div>
                  <div className="text-sm text-gray-600">有推荐的客户</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {comparisons.filter(c => !c.hasDifference).length}
                  </div>
                  <div className="text-sm text-gray-600">数据同步正常</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {comparisons.filter(c => c.hasDifference).length}
                  </div>
                  <div className="text-sm text-gray-600">数据不同步</div>
                </div>
              </div>
              
              {hasAnyDifference && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <div className="flex items-center text-yellow-800">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    <span className="text-sm">
                      发现数据不同步问题，建议点击"同步奖励数据"按钮进行修复
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* 详细对比 */}
        {comparisons.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>详细对比结果</CardTitle>
              <CardDescription>数据库存储值 vs 实时计算值</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {comparisons.map((comparison) => (
                  <div
                    key={comparison.clientId}
                    className={`p-4 border rounded-lg ${
                      comparison.hasDifference ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="font-semibold">{comparison.clientName}</h3>
                        <p className="text-sm text-gray-600">
                          {comparison.phone} • 推荐了 {comparison.referralCount} 个客户
                        </p>
                      </div>
                      <div className="flex items-center">
                        {comparison.hasDifference ? (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        ) : (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium text-gray-700 mb-2">数据库存储值</h4>
                        <div className="space-y-1 text-sm">
                          <div>总奖励: ¥{safeAmountFormat(comparison.dbReward.referralReward)}</div>
                          <div>可用奖励: ¥{safeAmountFormat(comparison.dbReward.availableReward)}</div>
                          <div>待结算: ¥{safeAmountFormat(comparison.dbReward.pendingReward)}</div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-700 mb-2">实时计算值</h4>
                        <div className="space-y-1 text-sm">
                          <div>总奖励: ¥{safeAmountFormat(comparison.calculatedReward.totalReward)}</div>
                          <div>可用奖励: ¥{safeAmountFormat(comparison.calculatedReward.availableReward)}</div>
                          <div>待结算: ¥{safeAmountFormat(comparison.calculatedReward.pendingReward)}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {comparisons.length === 0 && !loading && (
          <Card>
            <CardContent className="text-center py-12">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-gray-500">暂无推荐客户数据，请先点击"检查同步状态"</p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
