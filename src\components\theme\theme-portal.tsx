/**
 * 🇨🇳 风口云平台 - 主题感知Portal组件
 * 
 * 功能说明：
 * - 确保Portal组件能正确继承主题
 * - 解决二级页面主题不一致的问题
 * - 为Radix UI Portal提供主题容器
 */

'use client'

import { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'

interface ThemePortalProps {
  children: React.ReactNode
  container?: Element | null
}

export function ThemePortal({ children, container }: ThemePortalProps) {
  const [mounted, setMounted] = useState(false)
  const [portalContainer, setPortalContainer] = useState<Element | null>(null)

  useEffect(() => {
    setMounted(true)
    
    // 获取或创建Portal容器
    let targetContainer = container
    
    if (!targetContainer) {
      targetContainer = document.getElementById('portal-root')
      
      // 如果没有找到portal-root，创建一个
      if (!targetContainer) {
        targetContainer = document.createElement('div')
        targetContainer.id = 'portal-root'
        document.body.appendChild(targetContainer)
      }
    }
    
    // 确保Portal容器继承主题类名
    const syncThemeClass = () => {
      if (targetContainer) {
        const htmlElement = document.documentElement
        const isDark = htmlElement.classList.contains('dark')
        
        if (isDark) {
          targetContainer.classList.add('dark')
        } else {
          targetContainer.classList.remove('dark')
        }
      }
    }
    
    // 初始同步
    syncThemeClass()
    
    // 监听主题变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          syncThemeClass()
        }
      })
    })
    
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })
    
    setPortalContainer(targetContainer)
    
    return () => {
      observer.disconnect()
    }
  }, [container])

  if (!mounted || !portalContainer) {
    return null
  }

  return createPortal(children, portalContainer)
}

/**
 * 主题感知的Portal Hook
 * 用于替换Radix UI的默认Portal
 */
export function useThemePortal() {
  const [portalContainer, setPortalContainer] = useState<Element | null>(null)

  useEffect(() => {
    // 获取或创建Portal容器
    let container = document.getElementById('portal-root')
    
    if (!container) {
      container = document.createElement('div')
      container.id = 'portal-root'
      document.body.appendChild(container)
    }
    
    // 确保Portal容器继承主题类名
    const syncThemeClass = () => {
      if (container) {
        const htmlElement = document.documentElement
        const isDark = htmlElement.classList.contains('dark')
        
        if (isDark) {
          container.classList.add('dark')
        } else {
          container.classList.remove('dark')
        }
      }
    }
    
    // 初始同步
    syncThemeClass()
    
    // 监听主题变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          syncThemeClass()
        }
      })
    })
    
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })
    
    setPortalContainer(container)
    
    return () => {
      observer.disconnect()
    }
  }, [])

  return portalContainer
}
