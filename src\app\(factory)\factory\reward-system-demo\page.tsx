"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Gift, Users, DollarSign, TrendingUp } from 'lucide-react'

interface DemoData {
  referrer: {
    id: string
    name: string
    phone: string
    totalReward: number
    availableReward: number
    pendingReward: number
  }
  referredClients: Array<{
    id: string
    name: string
    phone: string
    orderCount: number
    totalAmount: number
    rewardGenerated: number
    isSettled: boolean
  }>
}

export default function RewardSystemDemoPage() {
  const [demoData, setDemoData] = useState<DemoData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 模拟从演示工厂获取数据
    const loadDemoData = async () => {
      try {
        const response = await fetch('/api/read-raw-data?factoryId=cmbx0onx0000gun48lje6r02s')
        const data = await response.json()
        
        if (data.success && data.data.referrers.length > 0) {
          const referrerData = data.data.referrers[0]
          setDemoData({
            referrer: {
              id: referrerData.referrerId,
              name: referrerData.referrerName,
              phone: referrerData.referrerPhone,
              totalReward: referrerData.calculatedReward,
              availableReward: referrerData.availableReward,
              pendingReward: referrerData.pendingReward
            },
            referredClients: referrerData.referredDetails.map((client: any) => ({
              id: client.clientId,
              name: client.clientName,
              phone: client.clientPhone,
              orderCount: client.orderCount,
              totalAmount: client.totalAmount,
              rewardGenerated: client.rewardGenerated,
              isSettled: client.totalAmount > 0 // 简化的结清判断
            }))
          })
        }
      } catch (error) {
        console.error('加载演示数据失败:', error)
      } finally {
        setLoading(false)
      }
    }

    loadDemoData()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>正在加载推荐奖励演示数据...</p>
        </div>
      </div>
    )
  }

  if (!demoData) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">暂无演示数据</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">🎁 推荐奖励系统演示</h1>
        <p className="text-gray-600">
          以下是推荐奖励系统的实际运行效果，数据来自演示工厂的真实计算结果。
        </p>
      </div>

      {/* 推荐人信息 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            推荐人：{demoData.referrer.name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                ¥{demoData.referrer.totalReward.toFixed(1)}
              </div>
              <div className="text-sm text-gray-500">总奖励</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                ¥{demoData.referrer.availableReward.toFixed(1)}
              </div>
              <div className="text-sm text-gray-500">可用奖励</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                ¥{demoData.referrer.pendingReward.toFixed(1)}
              </div>
              <div className="text-sm text-gray-500">待结算</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {demoData.referredClients.length}
              </div>
              <div className="text-sm text-gray-500">推荐客户</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 推荐客户列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            推荐客户详情
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {demoData.referredClients.map((client, index) => (
              <div key={client.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h3 className="font-semibold">{client.name}</h3>
                    <p className="text-sm text-gray-500">{client.phone}</p>
                  </div>
                  <Badge variant={client.isSettled ? "default" : "secondary"}>
                    {client.isSettled ? "已结清" : "有欠款"}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="font-semibold text-blue-600">{client.orderCount}</div>
                    <div className="text-gray-500">订单数</div>
                  </div>
                  <div>
                    <div className="font-semibold text-green-600">¥{client.totalAmount.toLocaleString()}</div>
                    <div className="text-gray-500">订单总额</div>
                  </div>
                  <div>
                    <div className="font-semibold text-orange-600">¥{client.rewardGenerated.toFixed(1)}</div>
                    <div className="text-gray-500">产生奖励</div>
                  </div>
                  <div>
                    <div className="font-semibold text-purple-600">
                      {((client.rewardGenerated / client.totalAmount) * 100).toFixed(1)}%
                    </div>
                    <div className="text-gray-500">奖励比例</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 系统说明 */}
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">✅ 推荐奖励系统特点</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• <strong>实时计算</strong>：奖励金额根据最新订单数据实时计算</li>
          <li>• <strong>按比例分配</strong>：根据订单金额按2%比例计算奖励</li>
          <li>• <strong>结清状态管理</strong>：只有客户完全结清的订单才能使用奖励</li>
          <li>• <strong>精确到分</strong>：所有金额计算精确到小数点后一位</li>
          <li>• <strong>数据一致性</strong>：前端显示与后端计算完全一致</li>
        </ul>
      </div>

      <div className="mt-4 text-center">
        <Button 
          onClick={() => window.location.href = '/factory/clients'}
          className="mr-4"
        >
          返回客户管理
        </Button>
        <Button 
          variant="outline"
          onClick={() => window.open('/api/read-raw-data?factoryId=cmbx0onx0000gun48lje6r02s', '_blank')}
        >
          查看原始数据
        </Button>
      </div>
    </div>
  )
}
