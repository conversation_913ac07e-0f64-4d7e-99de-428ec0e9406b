# 🚀 风口云平台 - 生产环境部署检查清单

## 📋 部署前检查

### ✅ 代码准备
- [ ] 所有功能测试通过
- [ ] 代码已提交到版本控制
- [ ] 移除了调试代码和console.log
- [ ] 更新了版本号
- [ ] 清理了临时文件

### ✅ 配置检查
- [ ] `.env` 文件配置正确
- [ ] 数据库连接字符串正确
- [ ] JWT密钥已设置
- [ ] 文件上传路径配置
- [ ] 邮件/短信配置（如需要）

### ✅ 安全检查
- [ ] 管理员密码已更新为: `admin@yhg2025`
- [ ] 移除了默认测试账户
- [ ] API密钥已更新
- [ ] 敏感信息不在代码中
- [ ] HTTPS配置正确

### ✅ 数据库准备
- [ ] 数据库备份已创建
- [ ] 迁移脚本准备就绪
- [ ] 生产数据验证
- [ ] 索引优化完成

## 🔐 密码更新信息

### 新的管理员密码
- **用户名**: `admin`
- **密码**: `admin@yhg2025`
- **更新时间**: 2025-01-19
- **哈希强度**: bcrypt rounds 12

### 密码特点
- ✅ 包含小写字母
- ✅ 包含数字
- ✅ 包含特殊字符 (@)
- ✅ 长度适中 (12位)
- ✅ 易于记忆但足够安全

## 🚀 部署步骤

### 1. 自动部署（推荐）
```bash
# 运行自动部署脚本
./scripts/deploy-to-production.sh
```

### 2. 手动部署
```bash
# 1. 连接服务器
ssh root@**************

# 2. 进入项目目录
cd /root/factorysystem

# 3. 拉取最新代码
git pull origin main

# 4. 安装依赖
npm ci --production

# 5. 构建项目
npm run build

# 6. 数据库迁移
npx prisma generate
npx prisma db push

# 7. 更新管理员密码
node scripts/update-admin-password.js

# 8. 重启服务
systemctl restart factorysystem
```

## 🔍 部署后验证

### ✅ 服务状态检查
```bash
# 检查服务状态
systemctl status factorysystem

# 检查端口监听
netstat -tlnp | grep :3000

# 检查应用响应
curl http://localhost:3000
```

### ✅ 功能验证
- [ ] 管理员登录正常
- [ ] 工厂用户登录正常
- [ ] 数据库连接正常
- [ ] 文件上传功能正常
- [ ] API接口响应正常

### ✅ 性能检查
- [ ] 页面加载速度正常
- [ ] 数据库查询性能正常
- [ ] 内存使用合理
- [ ] CPU使用正常

## 🛡️ 安全验证

### ✅ 密码安全
- [ ] 管理员密码已更新
- [ ] 默认密码已移除
- [ ] 密码强度符合要求
- [ ] 登录日志正常

### ✅ 系统安全
- [ ] 防火墙配置正确
- [ ] SSL证书有效
- [ ] 敏感端口已关闭
- [ ] 日志记录正常

## 📞 应急联系

### 🚨 如果部署失败
1. **立即回滚**
   ```bash
   # 恢复备份
   cp /root/backups/factorysystem_backup_* /root/factorysystem/
   systemctl restart factorysystem
   ```

2. **检查日志**
   ```bash
   # 查看应用日志
   journalctl -u factorysystem -f
   
   # 查看系统日志
   tail -f /var/log/syslog
   ```

3. **联系技术支持**
   - 提供错误日志
   - 说明操作步骤
   - 描述错误现象

## 📊 部署记录

### 本次部署信息
- **部署日期**: 2025-01-19
- **部署版本**: v1.0.0
- **部署人员**: 系统管理员
- **主要更新**: 管理员密码更新
- **备份位置**: `/root/backups/`

### 重要变更
1. ✅ 管理员密码更新为 `admin@yhg2025`
2. ✅ 提高密码哈希强度 (bcrypt rounds 12)
3. ✅ 清理临时文件和测试代码
4. ✅ 优化部署脚本

## 🎯 后续维护

### 定期任务
- [ ] 每周检查服务状态
- [ ] 每月更新依赖包
- [ ] 每季度更换密码
- [ ] 每半年备份数据

### 监控指标
- [ ] 服务可用性 > 99%
- [ ] 响应时间 < 2秒
- [ ] 错误率 < 1%
- [ ] 磁盘使用率 < 80%

---

**部署完成后请在此签名确认**: ________________

**日期**: ________________
