/**
 * 🇨🇳 风口云平台 - 图片存储服务（兼容性模块）
 * 
 * 这个模块提供了与旧版本代码的兼容性，主要用于轮播组件
 * 实际的图片存储功能请使用 image-database.ts
 */

// 简单的本地存储图片管理器
class ImageStorage {
  private storageKey = 'carousel-images'

  /**
   * 获取指定分类的图片URL列表
   */
  getImageUrls(category: string): string[] {
    try {
      if (typeof window === 'undefined') {
        // 服务端环境，返回空数组
        return []
      }

      const stored = localStorage.getItem(`${this.storageKey}-${category}`)
      if (stored) {
        const data = JSON.parse(stored)
        return Array.isArray(data) ? data : []
      }
      return []
    } catch (error) {
      console.error('获取本地图片失败:', error)
      return []
    }
  }

  /**
   * 保存图片URL列表到本地存储
   */
  saveImageUrls(category: string, urls: string[]): void {
    try {
      if (typeof window === 'undefined') {
        // 服务端环境，不执行任何操作
        return
      }

      localStorage.setItem(`${this.storageKey}-${category}`, JSON.stringify(urls))
      
      // 触发更新事件
      this.notifyListeners(category)
    } catch (error) {
      console.error('保存本地图片失败:', error)
    }
  }

  /**
   * 添加图片URL到指定分类
   */
  addImageUrl(category: string, url: string): void {
    const currentUrls = this.getImageUrls(category)
    if (!currentUrls.includes(url)) {
      currentUrls.push(url)
      this.saveImageUrls(category, currentUrls)
    }
  }

  /**
   * 从指定分类中移除图片URL
   */
  removeImageUrl(category: string, url: string): void {
    const currentUrls = this.getImageUrls(category)
    const filteredUrls = currentUrls.filter(u => u !== url)
    this.saveImageUrls(category, filteredUrls)
  }

  /**
   * 清空指定分类的所有图片
   */
  clearImages(category: string): void {
    try {
      if (typeof window === 'undefined') {
        return
      }

      localStorage.removeItem(`${this.storageKey}-${category}`)
      this.notifyListeners(category)
    } catch (error) {
      console.error('清空本地图片失败:', error)
    }
  }

  /**
   * 获取所有分类
   */
  getAllCategories(): string[] {
    try {
      if (typeof window === 'undefined') {
        return []
      }

      const categories: string[] = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith(this.storageKey)) {
          const category = key.replace(`${this.storageKey}-`, '')
          categories.push(category)
        }
      }
      return categories
    } catch (error) {
      console.error('获取分类列表失败:', error)
      return []
    }
  }

  // 事件监听器管理
  private listeners: Array<(category: string) => void> = []

  /**
   * 监听图片更新事件
   */
  onImagesUpdated(callback: (category: string) => void): () => void {
    this.listeners.push(callback)
    
    // 返回清理函数
    return () => {
      const index = this.listeners.indexOf(callback)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(category: string): void {
    this.listeners.forEach(callback => {
      try {
        callback(category)
      } catch (error) {
        console.error('图片更新回调执行失败:', error)
      }
    })
  }

  /**
   * 获取存储统计信息
   */
  getStorageStats(): {
    totalCategories: number
    totalImages: number
    byCategory: Record<string, number>
  } {
    const categories = this.getAllCategories()
    const stats = {
      totalCategories: categories.length,
      totalImages: 0,
      byCategory: {} as Record<string, number>
    }

    categories.forEach(category => {
      const urls = this.getImageUrls(category)
      stats.byCategory[category] = urls.length
      stats.totalImages += urls.length
    })

    return stats
  }

  /**
   * 检查是否有指定分类的图片
   */
  hasImages(category: string): boolean {
    return this.getImageUrls(category).length > 0
  }

  /**
   * 批量导入图片URL
   */
  importImages(category: string, urls: string[]): void {
    const validUrls = urls.filter(url => url && typeof url === 'string')
    this.saveImageUrls(category, validUrls)
  }

  /**
   * 导出指定分类的图片URL
   */
  exportImages(category: string): string[] {
    return this.getImageUrls(category)
  }

  /**
   * 清空所有图片数据
   */
  clearAllImages(): void {
    const categories = this.getAllCategories()
    categories.forEach(category => {
      this.clearImages(category)
    })
  }
}

// 导出单例实例
export const imageStorage = new ImageStorage()

// 默认导出
export default imageStorage

// 类型定义
export interface ImageStorageInterface {
  getImageUrls(category: string): string[]
  saveImageUrls(category: string, urls: string[]): void
  addImageUrl(category: string, url: string): void
  removeImageUrl(category: string, url: string): void
  clearImages(category: string): void
  getAllCategories(): string[]
  onImagesUpdated(callback: (category: string) => void): () => void
  hasImages(category: string): boolean
  importImages(category: string, urls: string[]): void
  exportImages(category: string): string[]
  clearAllImages(): void
}
