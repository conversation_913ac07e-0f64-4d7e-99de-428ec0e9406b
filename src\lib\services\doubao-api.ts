/**
 * 豆包AI（火山引擎）API 服务
 * 支持火山引擎豆包大模型的API调用，兼容OpenAI API格式
 */

import { complexityDetector } from './complexity-detector'
import { DIMENSION_SEPARATORS } from '../utils/dimension-utils'

// 豆包AI配置接口
interface DoubaoConfig {
  apiKey: string
  baseURL: string
  timeout: number
}

// 豆包AI请求接口
interface DoubaoRequest {
  model: string
  messages: Array<{
    role: 'user' | 'assistant' | 'system'
    content: string
  }>
  max_tokens?: number
  temperature?: number
  stream?: boolean
}

// 豆包AI响应接口
interface DoubaoResponse {
  choices: Array<{
    message: {
      content: string
      role: string
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

// 智能楼层识别函数
function extractFloorFromText(text: string): string {
  // 1. 优先识别明确的楼层表达
  const explicitFloorMatch = text.match(/(\d+)[楼层F]|([一二三四五六七八九十]+)楼/);
  if (explicitFloorMatch) {
    const floorNum = explicitFloorMatch[1] || convertChineseToNumber(explicitFloorMatch[2]);
    return `${floorNum}楼`;
  }

  // 2. 从房号中提取楼层（4位数房号，前2位为楼层）
  const roomNumberMatch = text.match(/(\d{4})/);
  if (roomNumberMatch) {
    const roomNumber = roomNumberMatch[1];
    const floorNumber = parseInt(roomNumber.substring(0, 2));
    if (floorNumber >= 1 && floorNumber <= 99) {
      return `${floorNumber}楼`;
    }
  }

  // 3. 从3位数房号中提取楼层（前1位为楼层）
  const shortRoomMatch = text.match(/(\d{3})/);
  if (shortRoomMatch) {
    const roomNumber = shortRoomMatch[1];
    const floorNumber = parseInt(roomNumber.substring(0, 1));
    if (floorNumber >= 1 && floorNumber <= 9) {
      return `${floorNumber}楼`;
    }
  }

  // 4. 默认返回1楼
  return '1楼';
}

// 中文数字转换
function convertChineseToNumber(chinese: string): string {
  const map: { [key: string]: string } = {
    '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
    '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
  };
  return map[chinese] || '1';
}

export class DoubaoAPI {
  private config: DoubaoConfig
  private lastInputText: string = ''

  constructor(apiKey: string) {
    this.config = {
      apiKey,
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
      timeout: 60000 // 60秒超时
    }
  }

  /**
   * 检测API密钥格式并返回正确的认证头
   */
  private getAuthHeaders(): Record<string, string> {
    const { apiKey } = this.config

    // 如果是标准的API Key格式（sk-开头）
    if (apiKey.startsWith('sk-')) {
      return {
        'Authorization': `Bearer ${apiKey}`
      }
    }

    // 如果是UUID格式，可能是Access Key，尝试作为API Key使用
    if (apiKey.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      console.log('🔧 [豆包AI] 检测到UUID格式密钥，尝试作为API Key使用')
      return {
        'Authorization': `Bearer ${apiKey}`
      }
    }

    // 默认使用Bearer认证
    return {
      'Authorization': `Bearer ${apiKey}`
    }
  }

  /**
   * 调用豆包AI API（带重试机制）
   */
  async callAPI(prompt: string, model: string = 'doubao-seed-1-6-250615', retries: number = 1): Promise<DoubaoResponse> {
    const startTime = performance.now()
    console.log('🚀 [豆包AI] 开始API调用:', new Date().toISOString())
    console.log('🚀 [豆包AI] 使用模型:', model)
    console.log('🚀 [豆包AI] Prompt长度:', prompt.length, '字符')

    const requestBody: DoubaoRequest = {
      model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.2,
      stream: false
    }

    let response: Response
    let lastError: Error | null = null

    for (let attempt = 0; attempt <= retries; attempt++) {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

      const fetchStartTime = performance.now()

      try {
        if (attempt > 0) {
          console.log(`🔄 [豆包AI] 第${attempt + 1}次尝试...`)
        }

        const authHeaders = this.getAuthHeaders()

        response = await fetch(`${this.config.baseURL}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...authHeaders
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal
        })

        const fetchTime = performance.now() - fetchStartTime
        console.log(`🌐 [豆包AI] Fetch请求耗时: ${fetchTime.toFixed(2)}ms`)
        clearTimeout(timeoutId)

        // 成功获得响应，跳出重试循环
        break

      } catch (error) {
        clearTimeout(timeoutId)
        lastError = error instanceof Error ? error : new Error('未知错误')
        
        if (attempt === retries) {
          console.error(`❌ [豆包AI] 所有重试失败，最终错误:`, lastError)
          throw new Error(`豆包AI API调用失败: ${lastError.message}`)
        }
        
        const waitTime = Math.pow(2, attempt) * 1000 // 指数退避
        console.warn(`⚠️ [豆包AI] 第${attempt + 1}次尝试失败，${waitTime}ms后重试:`, lastError.message)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }

    if (!response!) {
      throw new Error('豆包AI API调用失败: 无法获得响应')
    }

    const totalTime = performance.now() - startTime
    console.log(`⏱️ [豆包AI] 总请求耗时: ${totalTime.toFixed(2)}ms (${(totalTime/1000).toFixed(2)}秒)`)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ [豆包AI] API请求失败:', response.status, errorText)

      // 特殊处理模型或认证错误
      if (response.status === 404 && errorText.includes('InvalidEndpointOrModel.NotFound')) {
        const keyFormat = this.config.apiKey.startsWith('sk-') ? 'API Key' :
                         this.config.apiKey.match(/^[0-9a-f-]{36}$/i) ? 'UUID格式密钥' : '未知格式密钥'

        throw new Error(`豆包AI 模型不存在或无权限访问: ${model}

当前密钥格式: ${keyFormat}
密钥值: ${this.config.apiKey.substring(0, 20)}...

可能的解决方案：
1. 确认已在火山引擎控制台开通豆包大模型服务
2. 检查API密钥是否正确（需要有效的API Key或Access Key）
3. 确认账号有访问 ${model} 模型的权限
4. 访问 https://console.volcengine.com/ark 检查配置

支持的模型: doubao-seed-1-6-250615`)
      }

      if (response.status === 401) {
        throw new Error(`豆包AI 认证失败: 密钥无效

当前密钥: ${this.config.apiKey.substring(0, 20)}...

请检查：
1. 密钥是否正确配置
2. 是否已在火山引擎控制台创建有效的API Key
3. 密钥是否已过期或被禁用
4. 账号是否已开通豆包大模型服务权限`)
      }

      throw new Error(`豆包AI API请求失败: ${response.status} ${errorText}`)
    }

    const parseStartTime = performance.now()
    const data: DoubaoResponse = await response.json()
    const parseTime = performance.now() - parseStartTime
    console.log(`📊 [豆包AI] JSON解析耗时: ${parseTime.toFixed(2)}ms`)
    console.log(`📊 [豆包AI] 响应数据大小: ${JSON.stringify(data).length} 字符`)

    if (!data.choices || data.choices.length === 0) {
      throw new Error('豆包AI API返回数据格式错误')
    }

    return data
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<{ success: boolean; duration: number; error?: string }> {
    const startTime = performance.now()

    try {
      console.log('🔧 [豆包AI] 开始连接测试...')

      const requestBody = {
        model: 'doubao-seed-1-6-250615',
        messages: [{ role: 'user', content: '测试' }],
        max_tokens: 5,
        temperature: 0,
        stream: false
      }

      const authHeaders = this.getAuthHeaders()

      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(15000) // 15秒超时
      })

      const duration = performance.now() - startTime

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ [豆包AI] 连接测试失败:', response.status, errorText)

        let errorMessage = `HTTP ${response.status}: ${errorText}`

        // 特殊处理认证和模型错误
        if (response.status === 404 && errorText.includes('InvalidEndpointOrModel.NotFound')) {
          errorMessage = `模型不存在或无权限: doubao-seed-1-6-250615

请检查：
1. API Key是否正确
2. 是否已开通豆包大模型服务
3. 访问: https://console.volcengine.com/ark`
        } else if (response.status === 401) {
          errorMessage = `API Key认证失败

请检查API Key是否正确配置`
        }

        return {
          success: false,
          duration,
          error: errorMessage
        }
      }

      const data = await response.json()
      console.log('✅ [豆包AI] 连接测试成功:', data)

      return {
        success: true,
        duration
      }
    } catch (error) {
      const duration = performance.now() - startTime
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      console.error('❌ [豆包AI] 连接测试异常:', errorMessage)

      return {
        success: false,
        duration,
        error: errorMessage
      }
    }
  }

  /**
   * 分析风口订单（使用豆包AI）
   */
  async analyzeVentOrder(text: string): Promise<any> {
    const startTime = performance.now()
    console.log('🚀 [豆包AI] 开始风口订单分析...')

    // 保存输入文本供备用解析使用
    this.lastInputText = text;

    // 使用快速分析prompt
    const prompt = this.buildFastPrompt(text)

    try {
      const response = await this.callAPI(prompt, 'doubao-seed-1-6-250615')

      const parseStartTime = performance.now()
      const result = this.parseResponse(response, text)

      // 修复systemType问题
      this.fixSystemTypes(result)

      console.log(`🔧 [豆包AI] JSON解析耗时: ${(performance.now() - parseStartTime).toFixed(2)}ms`)

      const totalTime = performance.now() - startTime
      console.log(`🎉 [豆包AI] 风口分析总耗时: ${totalTime.toFixed(2)}ms (${(totalTime/1000).toFixed(2)}秒)`)

      return result
    } catch (error) {
      console.error('❌ [豆包AI] 风口分析失败:', error)
      throw error
    }
  }

  /**
   * 构建快速分析prompt（参考通义千问的识别规则）
   */
  private buildFastPrompt(text: string): string {
    return `你是一位资深的暖通空调工程师和数据分析专家，拥有20年的风口订单处理经验。请运用你的专业知识，深度分析以下文本内容，理解其结构和含义。

🎯 核心任务：
你需要像一个真正的工程师一样思考，理解文本的业务逻辑，而不是简单的模式匹配。

📊 智能分析维度：

1. **文本结构理解**：
   - 分析文本的组织方式和层级关系
   - 识别项目信息、楼层分布、房间布局
   - 理解上下文关系和业务逻辑

2. **专业术语识别**：
   - 风口类型：出风口、回风口、送风口、排风口、新风口等
   - 房间类型：办公室、会议室、走廊、卫生间、机房等
   - 工程术语：检修口、维修口、软管连接、普通下出等

3. **尺寸智能解析**：
   - 识别各种尺寸表达方式：×、✖️、*、x、X、乘、by等
   - 智能单位推断：<100的数字可能是厘米，需转换为毫米
   - 处理OCR错误：568.2可能是5682的误识别
   - 理解工程惯例：长×宽的表达习惯

4. **数量和规格理解**：
   - 识别明确的数量表达：22个、14个、3个等
   - 理解隐含数量：未明确说明时默认为1个
   - 识别特殊规格：普通下出、留圆接软管等

5. **项目结构分析**：
   - 智能判断是单项目还是多项目
   - 理解楼层-房间-风口的层级关系
   - 识别项目地址和客户信息

🧠 智能推理规则：

- **风口类型判断**：根据尺寸特征智能判断，宽度≥255mm通常是回风口，<255mm通常是出风口
- **楼层识别**：理解各种楼层表达方式，包括中文数字、阿拉伯数字、特殊标记
- **房间归属**：理解房间名称后的风口都属于该房间，直到遇到新的房间名称
- **项目归属**：理解整个文本通常属于同一个项目，除非有明确的项目分隔

💡 创新理解能力：
- 能够处理从未见过的格式和表达方式
- 理解工程师的表达习惯和简写方式
- 智能纠错和补全不完整的信息
- 适应不同地区和公司的表达习惯

📝 待分析文本：
${text}

🔍 请按以下步骤进行深度分析：

**第一步：文本结构分析**
- 通读全文，理解整体结构和业务场景
- 识别项目信息、楼层分布、房间布局
- 分析文本的组织逻辑和层级关系

**第二步：专业术语解析**
- 识别所有风口相关术语，包括非标准表达
- 理解房间类型和功能区域
- 解析工程规格和特殊要求

**第三步：尺寸数据提取**
- 识别所有尺寸信息，处理各种分隔符
- 智能判断单位并进行必要转换
- 纠正可能的OCR错误

**第四步：逻辑关系构建**
- 建立楼层-房间-风口的层级关系
- 确定每个风口的归属和规格
- 推断隐含的信息（如默认数量）

🧠 **智能风口类型识别**（基于工程经验和上下文理解）：

**出风口智能识别**：
- 明确关键词：出风、送风、供风、新风、客出风、餐出风等
- 尺寸特征：宽度通常<255mm
- 上下文线索：与"送风"、"供气"、"新风"相关的描述
- 房间功能：办公区域的小尺寸风口通常是出风口
- 系统类型：double_white_outlet（双层白色出风口）

**回风口智能识别**：
- 明确关键词：回风、进风、排风、抽风、客回风、餐回风等
- 尺寸特征：宽度通常≥255mm，面积较大
- 上下文线索：与"回收"、"排出"、"抽取"相关的描述
- 房间功能：大面积风口通常用于回风
- 系统类型：white_return（白色回风口）

**重要提醒**：
- 只识别出风口和回风口两种类型
- 不要识别线性风口、检修口等其他类型
- 所有风口都归类为出风口或回风口

**智能推理规则**：
- 当关键词不明确时，优先根据尺寸特征判断
- 考虑房间功能和风口用途的匹配性
- 理解工程师的表达习惯和地方方言
- 结合上下文和工程常识进行综合判断
- 处理OCR错误和非标准表达方式

🏗️ **智能楼层和房间识别**：

**楼层智能识别策略**：
- 明确标识：一楼、二楼、3楼、B1楼、地下一层等
- 房号推断：从1901、2702等房号推断楼层（19楼、27楼）
- 上下文理解：理解楼层在文本中的层级关系
- 格式统一：输出标准格式（如"19楼"、"一楼"）
- 特殊情况：地下室、夹层、设备层等特殊楼层

**房间智能识别策略**：
- 功能房间：活动室、客厅、茶室、棋牌室、餐厅、厨房、KTV等
- 编号房间：A201、B302、1901号房等标准房间编号
- **重要**：当风口描述中包含房间编号时（如"A101出风3350×160"），应将A101识别为房间名
- 区域描述：大厅、走廊、过道、前台等公共区域
- 专业房间：机房、配电间、设备间、消防间等技术房间
- 上下文关联：理解房间名称后的风口都属于该房间

**项目信息智能提取**：
- 项目名称：通常在文本开头，如"金桂苑"、"万科城"等
- 客户信息：从项目地址中提取客户姓名或单位
- 地址解析：理解完整的项目地址结构
- 业务场景：区分住宅、办公、商业等不同场景

系统风口类型代码：
- double_white_outlet: 双层白色出风口
- double_black_outlet: 双层黑色出风口
- white_return: 白色回风口
- black_return: 黑色回风口
- white_linear: 白色线型风口
- black_linear: 黑色线型风口
- white_linear_return: 白色线型回风口
- black_linear_return: 黑色线型回风口
- maintenance: 检修口

尺寸和备注解析规则：
1. 尺寸格式识别：支持所有分隔符：×、✖️、✖、✘、✕、⨯、·、*、+、-、—、–、/、@、&、:、：、乘、by、x、X等
2. 🔧 智能单位识别和转换（重要）：
   - 如果数字<100，推断为厘米，转换为毫米：15.5/266.5 → 155×2665mm
   - 如果数字≥100，推断为毫米：568*145 → 568×145mm
   - 小数点处理：15.5 → 155mm，266.5 → 2665mm
   - 所有尺寸最终统一为毫米单位
3. 🔧 智能尺寸排序：确保大值作为长度，小值作为宽度
4. 🔧 重复尺寸处理：
   - 文本中每行的每个尺寸都要单独识别
   - 相同尺寸出现多次时，每次都要单独处理
5. 备注提取和映射（🔥 严格执行 🔥）：
   - 🎯 核心规则：房间词汇紧跟风口类型的，该风口notes = 房间名
   - 🎯 房间词汇识别：客厅、房间、餐厅、卧室、主卧、次卧、歺、柜、客、卧、主、次
   - 🎯 补充信息：括号内容如（单边）、（双边）要加到房间名后面

   📝 识别示例（必须严格遵守）：
   - "客厅回风141*30" → notes: "客厅"
   - "客出风568.2*14.5" → notes: "客厅"
   - "房间出风205.2*14.7" → notes: "房间"
   - "房间回风93.7*30（单边）" → notes: "房间 单边"
   - "4455*145(一分二)" → notes: "一分二"
   - "5580*150(一分二)" → notes: "一分二"
   - "出风271.8*15.1" → notes: ""（无房间前缀）
   - "141*30" → notes: ""（重复尺寸）

   🚫 严禁错误：
   - 不要只识别括号内容而忽略房间名！
   - 不要给无房间标识的风口添加notes！
6. 楼层识别规则：
   - 明确楼层：一楼→1，二楼→2，三楼→3，1F→1，2F→2等
   - 房号楼层：2702→27楼，1205→12楼，3401→34楼等
   - 提取规则：4位房号取前2位作为楼层号
7. 项目地址识别：
   - 完整保留项目信息：如"上淮府3-2702 淮安李勇"
   - 不要拆分或简化项目地址
   - 从房号提取楼层：2702→27楼，1205→12楼
8. 特殊备注：分段、加急、定制、一分二、一分三等工艺要求

📋 返回JSON格式（必须严格按照此格式）：
{
  "projects": [{
    "projectName": "项目名称",
    "clientInfo": "客户信息",
    "floors": [{
      "floorName": "楼层",
      "rooms": [{
        "roomName": "房间",
        "vents": [{
          "systemType": "系统风口类型代码（必须是上面列表中的一个）",
          "originalType": "原始风口描述（如：客厅回风、客出风、出风口、回风口等）",
          "dimensions": {"length": 数值, "width": 数值, "unit": "mm"},
          "color": "white",
          "quantity": 数量,
          "notes": "备注（仅包含技术规格和特殊要求，无特殊备注时留空）"
        }]
      }]
    }]
  }],
  "confidence": 0.95,
  "warnings": []
}

🔧 systemType必须从以下列表中选择：
- double_white_outlet: 双层白色出风口（默认出风口类型）
- double_black_outlet: 双层黑色出风口
- white_return: 白色回风口（默认回风口类型）
- black_return: 黑色回风口
- white_linear: 白色线型风口
- black_linear: 黑色线型风口
- white_linear_return: 白色线型回风口
- black_linear_return: 黑色线型回风口
- maintenance: 检修口

⚠️ 严禁使用其他类型如white_supply等，必须使用上述标准类型！

⚠️ 最重要的提醒：
1. 🎯 不要默认所有风口都是回风口！必须根据关键词和尺寸准确判断
2. 📤 有"出风"、"送风"关键词或宽度<255mm时，识别为出风口
3. 📥 有"回风"、"进气"关键词或宽度≥255mm时，识别为回风口
4. 📝 originalType字段不能为空，必须填写描述性文字
5. 🔧 systemType必须从上述列表中准确选择，不能随意填写

现在开始分析文本，严格按照上述规则识别每个风口的类型，只返回JSON格式，不要添加任何解释！`
  }

  /**
   * 修复systemType问题
   */
  private fixSystemTypes(result: any): void {
    if (!result.projects || !Array.isArray(result.projects)) {
      return
    }

    result.projects.forEach((project: any) => {
      if (!project.floors || !Array.isArray(project.floors)) {
        return
      }

      project.floors.forEach((floor: any) => {
        if (!floor.rooms || !Array.isArray(floor.rooms)) {
          return
        }

        floor.rooms.forEach((room: any) => {
          if (!room.vents || !Array.isArray(room.vents)) {
            return
          }

          room.vents.forEach((vent: any) => {
            // 修复不正确的systemType
            if (vent.systemType === 'white_supply') {
              vent.systemType = 'double_white_outlet'
              console.log('🔧 [豆包AI] 修复systemType: white_supply → double_white_outlet')
            } else if (vent.systemType === 'black_supply') {
              vent.systemType = 'double_black_outlet'
              console.log('🔧 [豆包AI] 修复systemType: black_supply → double_black_outlet')
            }

            // 确保所有必需字段存在
            if (!vent.color) {
              vent.color = 'white'
            }

            if (!vent.quantity) {
              vent.quantity = 1
            }

            if (!vent.notes) {
              vent.notes = ''
            }
          })
        })
      })
    })
  }

  /**
   * 构建快速分析prompt（参考通义千问的识别规则）
   */
  private buildFastPrompt(text: string): string {
    return `你是一位资深的暖通空调工程师和数据分析专家，拥有20年的风口订单处理经验。请运用你的专业知识，深度分析以下文本内容，理解其结构和含义。

🎯 核心任务：
你需要像一个真正的工程师一样思考，理解文本的业务逻辑，而不是简单的模式匹配。

📊 智能分析维度：

1. **文本结构理解**：
   - 分析文本的组织方式和层级关系
   - 识别项目信息、楼层分布、房间布局
   - 理解上下文关系和业务逻辑

2. **专业术语识别**：
   - 风口类型：出风口、回风口、送风口、排风口、新风口等
   - 房间类型：办公室、会议室、走廊、卫生间、机房等
   - 工程术语：检修口、维修口、软管连接、普通下出等

3. **尺寸智能解析**：
   - 识别各种尺寸表达方式：×、✖️、*、x、X、乘、by等
   - 智能单位推断：<100的数字可能是厘米，需转换为毫米
   - 处理OCR错误：568.2可能是5682的误识别
   - 理解工程惯例：长×宽的表达习惯

4. **数量和规格理解**：
   - 识别明确的数量表达：22个、14个、3个等
   - 理解隐含数量：未明确说明时默认为1个
   - 识别特殊规格：普通下出、留圆接软管等

5. **项目结构分析**：
   - 智能判断是单项目还是多项目
   - 理解楼层-房间-风口的层级关系
   - 识别项目地址和客户信息

🧠 智能推理规则：

- **风口类型判断**：根据尺寸特征智能判断，宽度≥255mm通常是回风口，<255mm通常是出风口
- **楼层识别**：理解各种楼层表达方式，包括中文数字、阿拉伯数字、特殊标记
- **房间归属**：理解房间名称后的风口都属于该房间，直到遇到新的房间名称
- **项目归属**：理解整个文本通常属于同一个项目，除非有明确的项目分隔

💡 创新理解能力：
- 能够处理从未见过的格式和表达方式
- 理解工程师的表达习惯和简写方式
- 智能纠错和补全不完整的信息
- 适应不同地区和公司的表达习惯

📝 待分析文本：
${text}

🔍 请按以下步骤进行深度分析：

**第一步：文本结构分析**
- 通读全文，理解整体结构和业务场景
- 识别项目信息、楼层分布、房间布局
- 分析文本的组织逻辑和层级关系

**第二步：专业术语解析**
- 识别所有风口相关术语，包括非标准表达
- 理解房间类型和功能区域
- 解析工程规格和特殊要求

**第三步：尺寸数据提取**
- 识别所有尺寸信息，处理各种分隔符
- 智能判断单位并进行必要转换
- 纠正可能的OCR错误

**第四步：逻辑关系构建**
- 建立楼层-房间-风口的层级关系
- 确定每个风口的归属和规格
- 推断隐含的信息（如默认数量）

🧠 **智能风口类型识别**（基于工程经验和上下文理解）：

**出风口智能识别**：
- 明确关键词：出风、送风、供风、新风、客出风、餐出风等
- 尺寸特征：宽度通常<255mm
- 上下文线索：与"送风"、"供气"、"新风"相关的描述
- 房间功能：办公区域的小尺寸风口通常是出风口
- 系统类型：double_white_outlet（双层白色出风口）

**回风口智能识别**：
- 明确关键词：回风、进风、排风、抽风、客回风、餐回风等
- 尺寸特征：宽度通常≥255mm，面积较大
- 上下文线索：与"回收"、"排出"、"抽取"相关的描述
- 房间功能：大面积风口通常用于回风
- 系统类型：white_return（白色回风口）

**重要提醒**：
- 只识别出风口和回风口两种类型
- 不要识别线性风口、检修口等其他类型
- 所有风口都归类为出风口或回风口

**智能推理规则**：
- 当关键词不明确时，优先根据尺寸特征判断
- 考虑房间功能和风口用途的匹配性
- 理解工程师的表达习惯和地方方言
- 结合上下文和工程常识进行综合判断
- 处理OCR错误和非标准表达方式

🏗️ **智能楼层和房间识别**：

**楼层智能识别策略**：
- 明确标识：一楼、二楼、3楼、B1楼、地下一层等
- 房号推断：从1901、2702等房号推断楼层（19楼、27楼）
- 上下文理解：理解楼层在文本中的层级关系
- 格式统一：输出标准格式（如"19楼"、"一楼"）
- 特殊情况：地下室、夹层、设备层等特殊楼层

**房间智能识别策略**：
- 功能房间：活动室、客厅、茶室、棋牌室、餐厅、厨房、KTV等
- 编号房间：A201、B302、1901号房等标准房间编号
- **重要**：当风口描述中包含房间编号时（如"A101出风3350×160"），应将A101识别为房间名
- 区域描述：大厅、走廊、过道、前台等公共区域
- 专业房间：机房、配电间、设备间、消防间等技术房间
- 上下文关联：理解房间名称后的风口都属于该房间

**项目信息智能提取**：
- 项目名称：通常在文本开头，如"金桂苑"、"万科城"等
- 客户信息：从项目地址中提取客户姓名或单位
- 地址解析：理解完整的项目地址结构
- 业务场景：区分住宅、办公、商业等不同场景

系统风口类型代码：
- double_white_outlet: 双层白色出风口
- double_black_outlet: 双层黑色出风口
- white_return: 白色回风口
- black_return: 黑色回风口
- white_linear: 白色线型风口
- black_linear: 黑色线型风口
- white_linear_return: 白色线型回风口
- black_linear_return: 黑色线型回风口
- maintenance: 检修口

尺寸和备注解析规则：
1. 尺寸格式识别：支持所有分隔符：×、✖️、✖、✘、✕、⨯、·、*、+、-、—、–、/、@、&、:、：、乘、by、x、X等
2. 🔧 智能单位识别和转换（重要）：
   - 如果数字<100，推断为厘米，转换为毫米：15.5/266.5 → 155×2665mm
   - 如果数字≥100，推断为毫米：568*145 → 568×145mm
   - 小数点处理：15.5 → 155mm，266.5 → 2665mm
   - 所有尺寸最终统一为毫米单位
3. 🔧 智能尺寸排序：确保大值作为长度，小值作为宽度
4. 🔧 重复尺寸处理：
   - 文本中每行的每个尺寸都要单独识别
   - 相同尺寸出现多次时，每次都要单独处理
5. 备注提取和映射（🔥 严格执行 🔥）：
   - 🎯 核心规则：房间词汇紧跟风口类型的，该风口notes = 房间名
   - 🎯 房间词汇识别：客厅、房间、餐厅、卧室、主卧、次卧、歺、柜、客、卧、主、次
   - 🎯 补充信息：括号内容如（单边）、（双边）要加到房间名后面

   📝 识别示例（必须严格遵守）：
   - "客厅回风141*30" → notes: "客厅"
   - "客出风568.2*14.5" → notes: "客厅"
   - "房间出风205.2*14.7" → notes: "房间"
   - "房间回风93.7*30（单边）" → notes: "房间 单边"
   - "4455*145(一分二)" → notes: "一分二"
   - "5580*150(一分二)" → notes: "一分二"
   - "出风271.8*15.1" → notes: ""（无房间前缀）
   - "141*30" → notes: ""（重复尺寸）

   🚫 严禁错误：
   - 不要只识别括号内容而忽略房间名！
   - 不要给无房间标识的风口添加notes！
6. 楼层识别规则：
   - 明确楼层：一楼→1，二楼→2，三楼→3，1F→1，2F→2等
   - 房号楼层：2702→27楼，1205→12楼，3401→34楼等
   - 提取规则：4位房号取前2位作为楼层号
7. 项目地址识别：
   - 完整保留项目信息：如"上淮府3-2702 淮安李勇"
   - 不要拆分或简化项目地址
   - 从房号提取楼层：2702→27楼，1205→12楼
8. 特殊备注：分段、加急、定制、一分二、一分三等工艺要求

📋 返回JSON格式（必须严格按照此格式）：
{
  "projects": [{
    "projectName": "项目名称",
    "clientInfo": "客户信息",
    "floors": [{
      "floorName": "楼层",
      "rooms": [{
        "roomName": "房间",
        "vents": [{
          "systemType": "系统风口类型代码（必须是上面列表中的一个）",
          "originalType": "原始风口描述（如：客厅回风、客出风、出风口、回风口等）",
          "dimensions": {"length": 数值, "width": 数值, "unit": "mm"},
          "color": "white",
          "quantity": 数量,
          "notes": "备注（仅包含技术规格和特殊要求，无特殊备注时留空）"
        }]
      }]
    }]
  }],
  "confidence": 0.95,
  "warnings": []
}

🔧 systemType必须从以下列表中选择：
- double_white_outlet: 双层白色出风口（默认出风口类型）
- double_black_outlet: 双层黑色出风口
- white_return: 白色回风口（默认回风口类型）
- black_return: 黑色回风口
- white_linear: 白色线型风口
- black_linear: 黑色线型风口
- white_linear_return: 白色线型回风口
- black_linear_return: 黑色线型回风口
- maintenance: 检修口

⚠️ 最重要的提醒：
1. 🎯 不要默认所有风口都是回风口！必须根据关键词和尺寸准确判断
2. 📤 有"出风"、"送风"关键词或宽度<255mm时，识别为出风口
3. 📥 有"回风"、"进气"关键词或宽度≥255mm时，识别为回风口
4. 📝 originalType字段不能为空，必须填写描述性文字
5. 🔧 systemType必须从上述列表中准确选择，不能随意填写

现在开始分析文本，严格按照上述规则识别每个风口的类型，只返回JSON格式，不要添加任何解释！`
  }

  /**
   * 解析API响应
   */
  private parseResponse(response: DoubaoResponse, originalText: string = ''): any {
    try {
      const content = response.choices[0].message.content
      console.log('🔧 [豆包AI] 原始响应长度:', content.length)
      console.log('🔧 [豆包AI] 原始响应内容预览:', content.substring(0, 500) + '...')

      // 优化解析顺序：先尝试直接解析，再尝试修复

      // 1. 首先尝试直接解析整个内容（最常见的情况）
      try {
        const directResult = JSON.parse(content.trim())
        console.log('✅ [豆包AI] 直接解析成功!')

        // 🔧 应用智能楼层识别后处理
        this.applySmartFloorRecognition(directResult, originalText)

        directResult.provider = 'doubao'
        directResult.modelUsed = 'doubao-lite-4k'
        directResult.rawResponse = content
        return directResult
      } catch (directError) {
        console.log('🔧 [豆包AI] 直接解析失败，尝试提取和修复JSON...')
      }

      // 2. 尝试提取和修复JSON
      let jsonStr = this.extractAndFixJSON(content)

      if (jsonStr) {
        try {
          console.log('🔧 [豆包AI] 使用修复后的JSON解析...')
          const result = JSON.parse(jsonStr)

          // 🔧 应用智能楼层识别后处理
          this.applySmartFloorRecognition(result, originalText)

          result.provider = 'doubao'
          result.modelUsed = 'doubao-lite-4k'
          result.rawResponse = content
          result.warnings = result.warnings || []
          result.warnings.push('JSON已自动修复')
          console.log('✅ [豆包AI] 修复后解析成功!')
          return result
        } catch (fixError) {
          console.warn('🔧 [豆包AI] 修复后解析仍失败:', fixError.message)
        }
      }

      // 3. 尝试智能截断修复
      const repairedJson = this.repairTruncatedJSON(content)
      if (repairedJson) {
        try {
          const repairedResult = JSON.parse(repairedJson)
          console.log('✅ [豆包AI] 截断修复解析成功!')

          // 🔧 应用智能楼层识别后处理
          this.applySmartFloorRecognition(repairedResult, originalText)

          repairedResult.provider = 'doubao'
          repairedResult.modelUsed = 'doubao-lite-4k'
          repairedResult.rawResponse = content
          repairedResult.warnings = repairedResult.warnings || []
          repairedResult.warnings.push('JSON被截断，已自动修复')
          return repairedResult
        } catch (repairError) {
          console.warn('🔧 [豆包AI] 截断修复解析失败:', repairError.message)
        }
      }

      throw new Error('响应中未找到有效的JSON格式数据')

    } catch (error) {
      // 先尝试备用解析方法
      const fallbackResult = this.fallbackParse(response.choices[0].message.content)
      if (fallbackResult) {
        console.log('✅ [豆包AI] 备用解析成功，跳过JSON错误显示')
        return fallbackResult
      }

      // 只有备用解析也失败时才显示错误
      console.error('❌ [豆包AI] JSON解析失败:', error)
      console.error('❌ [豆包AI] 错误详情:', error instanceof Error ? error.message : '未知错误')
      console.error('❌ [豆包AI] 原始内容长度:', response.choices[0].message.content.length)

      // 显示原始内容的前500个字符用于调试
      const preview = response.choices[0].message.content.substring(0, 500)
      console.error('❌ [豆包AI] 内容预览:', preview)
      console.error('❌ [豆包AI] 备用解析也失败')

      // 返回最终备用结果
      return {
        projects: [{
          projectName: '解析失败',
          clientInfo: '豆包AI解析错误',
          floors: [{
            floorName: '1',
            rooms: [{
              roomName: '默认房间',
              vents: []
            }]
          }]
        }],
        confidence: 0.1,
        warnings: [`豆包AI解析失败: ${error instanceof Error ? error.message : '未知错误'}`],
        provider: 'doubao',
        rawResponse: response.choices[0].message.content
      }
    }
  }

  /**
   * 应用智能楼层识别后处理
   */
  private applySmartFloorRecognition(result: any, originalText: string): void {
    try {
      if (!result.projects || !Array.isArray(result.projects)) {
        return;
      }

      const lines = originalText.split('\n').map(line => line.trim()).filter(line => line);
      if (lines.length === 0) return;

      const firstLine = lines[0];

      // 检查第一行是否包含项目地址信息（不包含尺寸和材质信息）
      const hasVentInfo = this.hasVentPattern(firstLine);
      const hasMultipleDimensions = (firstLine.match(/\d+[×✖️✖\*]\d+/g) || []).length > 2;
      const isValidProjectName = !hasVentInfo && !hasMultipleDimensions && !firstLine.includes('线性');

      if (firstLine && isValidProjectName) {
        console.log('🔍 [豆包AI] 检测到项目地址信息:', firstLine);

        // 智能楼层识别
        const extractedFloor = extractFloorFromText(firstLine);
        console.log('🏢 [豆包AI] 提取的楼层信息:', extractedFloor);

        result.projects.forEach((project: any) => {
          // 更新项目名称为完整地址
          if (!project.projectName || project.projectName === '风口订单' || project.projectName === '项目名称') {
            project.projectName = firstLine;
            console.log('🏗️ [豆包AI] 更新项目名称:', firstLine);
          }

          // 尝试提取客户姓名
          const nameMatch = firstLine.match(/([^\d\-\s]+)$/);
          if (nameMatch && (!project.clientInfo || project.clientInfo === '客户订单' || project.clientInfo === '客户信息')) {
            project.clientInfo = nameMatch[1];
            console.log('👤 [豆包AI] 提取客户信息:', nameMatch[1]);
          }

          // 更新楼层信息
          if (project.floors && Array.isArray(project.floors) && extractedFloor !== '1楼') {
            project.floors.forEach((floor: any) => {
              if (floor.floorName === '1' || floor.floorName === '1楼' || floor.floorName === '默认楼层') {
                floor.floorName = extractedFloor;
                console.log('🏢 [豆包AI] 更新楼层名称:', extractedFloor);
              }
            });
          }
        });
      }
    } catch (error) {
      console.warn('⚠️ [豆包AI] 智能楼层识别后处理失败:', error);
    }
  }

  /**
   * 检查是否包含风口模式
   */
  private hasVentPattern(line: string): boolean {
    // 先标准化符号，将各种分隔符统一为×
    const normalizedLine = line
      .replace(/✖️/g, '×')  // 先处理复合字符
      .replace(/[✖✘✕⨯*xX]/g, '×');  // 再处理其他字符
    return /\d+\s*×\s*\d+/.test(normalizedLine) ||
           /(出风|回风|进风|排风|送风)/.test(line);
  }

  /**
   * 提取和修复JSON字符串
   */
  private extractAndFixJSON(content: string): string | null {
    try {
      // 方法1: 寻找完整的JSON对象
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        let jsonStr = jsonMatch[0];

        // 先检查是否需要修复（避免不必要的修复）
        try {
          JSON.parse(jsonStr);
          console.log('🔧 [豆包AI] JSON无需修复，直接返回')
          return jsonStr;
        } catch (parseError) {
          console.log('🔧 [豆包AI] JSON需要修复，开始修复...')
          // 尝试修复常见的JSON问题
          jsonStr = this.fixCommonJSONIssues(jsonStr);

          // 验证修复后的JSON是否有效
          JSON.parse(jsonStr);
          return jsonStr;
        }
      }

      // 方法2: 寻找代码块中的JSON
      const codeBlockMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (codeBlockMatch) {
        let jsonStr = codeBlockMatch[1];

        try {
          JSON.parse(jsonStr);
          return jsonStr;
        } catch (parseError) {
          jsonStr = this.fixCommonJSONIssues(jsonStr);
          JSON.parse(jsonStr);
          return jsonStr;
        }
      }

      return null;
    } catch (error) {
      console.warn('🔧 [豆包AI] JSON提取/修复失败:', error.message);
      return null;
    }
  }

  /**
   * 修复常见的JSON问题
   */
  private fixCommonJSONIssues(jsonStr: string): string {
    const originalLength = jsonStr.length;

    // 移除注释
    jsonStr = jsonStr.replace(/\/\/.*$/gm, '');
    jsonStr = jsonStr.replace(/\/\*[\s\S]*?\*\//g, '');

    // 修复尾随逗号
    jsonStr = jsonStr.replace(/,(\s*[}\]])/g, '$1');

    // 处理截断的JSON - 检查是否以不完整的属性结尾
    if (jsonStr.endsWith('"width"') || jsonStr.endsWith('"width":') || jsonStr.endsWith('"width": ')) {
      // 移除不完整的width属性
      jsonStr = jsonStr.replace(/,?\s*"width"\s*:?\s*$/, '');
    }

    // 处理其他可能的截断情况
    const incompletePatterns = [
      /,?\s*"[^"]*"?\s*:?\s*$/,  // 不完整的属性名或值
      /,?\s*"[^"]*$/,            // 不完整的字符串
      /,?\s*\d+\.?\d*$/          // 不完整的数字
    ];

    for (const pattern of incompletePatterns) {
      if (pattern.test(jsonStr)) {
        jsonStr = jsonStr.replace(pattern, '');
        break;
      }
    }

    // 修复未闭合的数组和对象
    const openBraces = (jsonStr.match(/\{/g) || []).length;
    const closeBraces = (jsonStr.match(/\}/g) || []).length;
    const openBrackets = (jsonStr.match(/\[/g) || []).length;
    const closeBrackets = (jsonStr.match(/\]/g) || []).length;

    // 添加缺失的闭合括号
    const missingBraces = openBraces - closeBraces;
    const missingBrackets = openBrackets - closeBrackets;

    if (missingBraces > 0 || missingBrackets > 0) {
      console.log(`🔧 [豆包AI] 修复括号: 添加${missingBraces}个}，${missingBrackets}个]`);
      for (let i = 0; i < missingBraces; i++) {
        jsonStr += '}';
      }
      for (let i = 0; i < missingBrackets; i++) {
        jsonStr += ']';
      }
    }

    // 只在实际修复时输出日志
    if (jsonStr.length !== originalLength || missingBraces > 0 || missingBrackets > 0) {
      console.log(`🔧 [豆包AI] JSON修复完成: ${originalLength} → ${jsonStr.length} 字符`);
    }

    return jsonStr;
  }

  /**
   * 智能修复截断的JSON
   */
  private repairTruncatedJSON(content: string): string | null {
    try {
      console.log('🔧 [豆包AI] 开始智能截断修复...')

      let jsonStr = content.trim()

      // 找到最后一个完整的对象或数组
      let lastCompletePos = -1
      let braceCount = 0
      let bracketCount = 0
      let inString = false
      let escapeNext = false

      for (let i = 0; i < jsonStr.length; i++) {
        const char = jsonStr[i]

        if (escapeNext) {
          escapeNext = false
          continue
        }

        if (char === '\\') {
          escapeNext = true
          continue
        }

        if (char === '"') {
          inString = !inString
          continue
        }

        if (inString) continue

        if (char === '{') {
          braceCount++
        } else if (char === '}') {
          braceCount--
          if (braceCount === 0 && bracketCount === 0) {
            lastCompletePos = i
          }
        } else if (char === '[') {
          bracketCount++
        } else if (char === ']') {
          bracketCount--
          if (braceCount === 0 && bracketCount === 0) {
            lastCompletePos = i
          }
        }
      }

      if (lastCompletePos > 0) {
        const repairedJson = jsonStr.substring(0, lastCompletePos + 1)
        console.log('🔧 [豆包AI] 找到截断位置，修复后长度:', repairedJson.length)

        // 验证修复后的JSON
        JSON.parse(repairedJson)
        return repairedJson
      }

      console.log('🔧 [豆包AI] 未找到可修复的截断位置')
      return null

    } catch (error) {
      console.error('🔧 [豆包AI] 智能截断修复失败:', error)
      return null
    }
  }

  /**
   * 备用解析方法 - 使用正则表达式提取关键信息
   */
  private fallbackParse(content: string): any | null {
    try {
      console.log('🔄 [豆包AI] 尝试备用解析方法...');

      // 从原始输入文本中解析，而不是从损坏的JSON中解析
      const inputText = this.lastInputText || content;
      const lines = inputText.split('\n').filter(line => line.trim());

      const floors: any[] = [];
      let currentFloor = '1';
      let currentRoom = '默认房间';
      let projectName = '风口订单';

      // 提取项目名称 - 增强检测逻辑
      if (lines.length > 0) {
        const firstLine = lines[0].trim();
        const hasVentInfo = this.hasVentPattern(firstLine);
        const hasMultipleDimensions = (firstLine.match(/\d+[×✖️✖\*]\d+/g) || []).length > 2;
        const isValidProjectName = !hasVentInfo && !hasMultipleDimensions && !firstLine.includes('线性');

        if (firstLine && isValidProjectName) {
          projectName = firstLine.replace(/^[；;]/, ''); // 移除开头的分号
          console.log('🏗️ [豆包AI] 备用解析识别项目名称:', projectName);
        } else {
          console.log('🏗️ [豆包AI] 第一行包含风口信息，使用默认项目名称');
        }
      }

      console.log(`🔄 [豆包AI] 开始解析 ${lines.length} 行数据`);

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        console.log(`🔄 [豆包AI] 处理行: "${trimmedLine}"`);

        // 识别楼层（只识别明确的楼层标识）
        const floorMatch = trimmedLine.match(/^([一二三四五六七八九十]楼|[1-9]\d*楼)$/);
        if (floorMatch) {
          const floorText = floorMatch[1];
          if (floorText.includes('一')) currentFloor = '1';
          else if (floorText.includes('二')) currentFloor = '2';
          else if (floorText.includes('三')) currentFloor = '3';
          else currentFloor = floorText.replace(/[楼F]/g, '');
          console.log(`🔄 [豆包AI] 识别楼层: "${trimmedLine}" → "${currentFloor}"`);
          continue;
        }

        // 识别房间
        if (this.isRoomName(trimmedLine)) {
          currentRoom = trimmedLine;
          console.log(`🔄 [豆包AI] 识别房间: "${trimmedLine}"`);
          continue;
        }

        // 识别风口
        if (this.hasVentPattern(trimmedLine)) {
          console.log(`🔄 [豆包AI] 识别风口: "${trimmedLine}" (楼层: ${currentFloor}, 房间: ${currentRoom})`);
          this.addVentToFloors(floors, currentFloor, currentRoom, trimmedLine);
        } else {
          console.log(`🔄 [豆包AI] 跳过行: "${trimmedLine}" (不匹配任何模式)`);
        }
      }

      const result = {
        projects: [{
          projectName,
          clientInfo: projectName,
          floors
        }],
        provider: 'doubao',
        modelUsed: 'doubao-lite-4k'
      };

      console.log('🔄 [豆包AI] 备用解析结果:', JSON.stringify(result, null, 2));
      console.log('🔄 [豆包AI] 解析到的楼层数:', floors.length);
      floors.forEach((floor, i) => {
        console.log(`🔄 [豆包AI] 楼层${i+1}: ${floor.floorName}, 房间数: ${floor.rooms.length}`);
        floor.rooms.forEach((room: any, j: number) => {
          console.log(`🔄 [豆包AI] 房间${j+1}: ${room.roomName}, 风口数: ${room.vents.length}`);
        });
      });

      return result;

    } catch (error) {
      console.error('🔄 [豆包AI] 备用解析失败:', error);
      return null;
    }
  }

  /**
   * 判断是否为房间名称
   */
  private isRoomName(line: string): boolean {
    const roomPatterns = [
      /^(活动室|客厅|茶室|棋牌室|餐厅|厨房|KTV)$/,
      /^[A-Z]\d{3}$/,  // A201, B302等
      /^(主卧|次卧|卧室|书房|办公室|会议室|大厅|走廊|检修口)$/
    ];
    return roomPatterns.some(pattern => pattern.test(line));
  }

  /**
   * 添加风口到楼层结构
   */
  private addVentToFloors(floors: any[], floorName: string, roomName: string, ventLine: string) {
    // 解析风口信息
    const ventInfo = this.parseVentLine(ventLine);
    if (!ventInfo) return;

    // 查找或创建楼层
    let floor = floors.find(f => f.floorName === floorName);
    if (!floor) {
      floor = { floorName, rooms: [] };
      floors.push(floor);
    }

    // 查找或创建房间
    let room = floor.rooms.find(r => r.roomName === roomName);
    if (!room) {
      room = { roomName, vents: [] };
      floor.rooms.push(room);
    }

    // 添加风口
    room.vents.push(ventInfo);
  }

  /**
   * 解析风口行信息
   */
  private parseVentLine(line: string): any | null {
    console.log(`🔄 [豆包AI] 解析风口行: "${line}"`);

    // 先标准化符号，将各种分隔符统一为×
    const normalizedLine = line
      .replace(/✖️/g, '×')  // 先处理复合字符
      .replace(/[✖✘✕⨯*xX]/g, '×');  // 再处理其他字符
    console.log(`🔄 [豆包AI] 标准化后: "${normalizedLine}"`);

    // 提取尺寸
    const dimensionMatch = normalizedLine.match(/(\d+)\s*×\s*(\d+)/);
    if (!dimensionMatch) {
      console.log(`🔄 [豆包AI] 未找到尺寸匹配: "${normalizedLine}"`);
      return null;
    }

    let length = parseInt(dimensionMatch[1]);
    let width = parseInt(dimensionMatch[2]);
    console.log(`🔄 [豆包AI] 原始尺寸: ${length}×${width}`);

    // 单位转换
    if (length < 100) length *= 10;
    if (width < 100) width *= 10;
    console.log(`🔄 [豆包AI] 转换后尺寸: ${length}×${width}`);

    // 提取数量
    const quantityMatch = line.match(/(\d+)个/);
    const quantity = quantityMatch ? parseInt(quantityMatch[1]) : 1;
    console.log(`🔄 [豆包AI] 数量: ${quantity}`);

    // 判断类型
    const hasReturnKeywords = /(回风|进风|排风)/.test(line);
    const systemType = hasReturnKeywords || width >= 255 ? 'white_return' : 'double_white_outlet';
    console.log(`🔄 [豆包AI] 类型判断: 关键词=${hasReturnKeywords}, 宽度=${width}, 类型=${systemType}`);

    // 提取备注
    const notes = this.extractNotes(line);
    console.log(`🔄 [豆包AI] 备注: "${notes}"`);

    const result = {
      systemType,
      originalType: line,
      dimensions: { length, width, unit: 'mm' },
      quantity,
      notes
    };

    console.log(`🔄 [豆包AI] 风口解析结果:`, result);
    return result;
  }

  /**
   * 提取备注信息
   */
  private extractNotes(line: string): string {
    const notes = [];

    // 提取特殊规格
    const specialSpecs = line.match(/(孤形|弧形|圆形|方形|普通下出|留圆接|软管|检修|维修|单边|双边|大叶片|小叶片)/g);
    if (specialSpecs) {
      notes.push(...specialSpecs);
    }

    return notes.join('，');
  }
}

// 创建豆包AI API实例的工厂函数
export const getDoubaoAPI = (apiKey: string): DoubaoAPI => {
  return new DoubaoAPI(apiKey)
}

export type { DoubaoConfig, DoubaoRequest, DoubaoResponse }
