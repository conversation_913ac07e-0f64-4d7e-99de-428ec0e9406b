"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { api } from '@/lib/api/client'
import { useAuthStore } from '@/lib/store/auth'
import { getCurrentFactoryId } from '@/lib/utils/factory'

export default function TestRewardApiPage() {
  const [clientId, setClientId] = useState('cmcz07r3l0003unnos3nr2w5o') // 张三的ID
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const { isAuthenticated, accessToken, user } = useAuthStore()

  const testRewardApi = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('🧪 开始测试推荐奖励API')
      console.log('🔐 认证状态:', { isAuthenticated, hasToken: !!accessToken, userName: user?.name })

      const factoryId = getCurrentFactoryId()
      console.log('🏭 工厂ID:', factoryId)

      if (!factoryId) {
        throw new Error('无法获取工厂ID')
      }

      const response = await api.get(`/api/clients/${clientId}/reward-status?factoryId=${factoryId}`)
      
      console.log('📡 API响应:', response)
      
      if (response.success) {
        setResult(response.data)
      } else {
        setError(response.error || '未知错误')
      }
    } catch (err) {
      console.error('❌ 测试失败:', err)
      setError(err instanceof Error ? err.message : '测试失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>推荐奖励API测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>认证状态</Label>
            <div className="p-3 bg-gray-50 rounded">
              <p>已登录: {isAuthenticated ? '✅ 是' : '❌ 否'}</p>
              <p>用户: {user?.name || '未知'}</p>
              <p>令牌: {accessToken ? '✅ 存在' : '❌ 不存在'}</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="clientId">客户ID</Label>
            <Input
              id="clientId"
              value={clientId}
              onChange={(e) => setClientId(e.target.value)}
              placeholder="输入客户ID"
            />
          </div>

          <Button 
            onClick={testRewardApi} 
            disabled={loading || !isAuthenticated}
            className="w-full"
          >
            {loading ? '测试中...' : '测试推荐奖励API'}
          </Button>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded">
              <p className="text-red-600">❌ 错误: {error}</p>
            </div>
          )}

          {result && (
            <div className="p-3 bg-green-50 border border-green-200 rounded">
              <h3 className="font-semibold text-green-800 mb-2">✅ 测试成功</h3>
              <pre className="text-sm text-green-700 whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
