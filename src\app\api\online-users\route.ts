/**
 * 🇨🇳 风口云平台 - 在线用户API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')

    // 获取在线用户
    const onlineUsers = await db.getOnlineUsers(factoryId || undefined)

    return NextResponse.json({
      success: true,
      data: onlineUsers,
      count: onlineUsers.length
    })

  } catch (error) {
    console.error('❌ 获取在线用户失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: '获取在线用户失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
