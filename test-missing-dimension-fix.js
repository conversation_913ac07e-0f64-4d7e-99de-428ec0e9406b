// 测试缺失尺寸修复效果
const testText = `江苏·淮安市安豪栅栏有限公司
一楼厨房出风口125x1435
回风口245x1120
客厅出风口130×2770
回风口150x800243x1750
次卧出风口150x800
回风口245x950
主卧出风口150x900
回风口250x.1020
二楼客厅出凤口175x1300
回风口295x1650
主卧出风口140x1500
回风口250x1500
次卧出风口140x800
同风口245x850`;

console.log('🧪 测试缺失尺寸修复效果');
console.log('📝 测试文本:');
console.log(testText);

console.log('\n🔍 问题分析:');
console.log('❌ 原问题: "回风口250x.1020" 被跳过，没有识别尺寸');
console.log('🔧 修复方案:');
console.log('  1. 增强尺寸检测正则，支持小数点开头格式');
console.log('  2. 修改风口检测规则：有尺寸就认为是风口');
console.log('  3. 处理小数点开头的4位数（OCR错误）');

// 测试新的尺寸检测逻辑
function testDimensionDetection(line) {
  // 新的尺寸检测正则（支持小数点开头）
  const hasDimensions = /\d+(?:\.\d+)?\s*[xX×✘✖*+\-]\s*\.?\d+(?:\.\d+)?/.test(line);
  return hasDimensions;
}

// 测试小数点开头数字处理
function testDecimalFix(dimensionStr) {
  const match = dimensionStr.match(/(\d+(?:\.\d+)?)[xX×](\.\d+)/);
  if (match) {
    let part1 = match[1];
    let part2 = match[2];
    
    // 如果第二个数字以小数点开头且是4位数，移除小数点
    if (part2.startsWith('.') && /^\.\d{4}$/.test(part2)) {
      part2 = part2.substring(1);
      console.log(`  🔧 修正: "${match[2]}" → "${part2}"`);
    }
    
    return {
      original: dimensionStr,
      corrected: `${part1}x${part2}`,
      num1: parseFloat(part1),
      num2: parseFloat(part2)
    };
  }
  return null;
}

console.log('\n📊 逐行测试:');
const lines = testText.split('\n').filter(line => line.trim());

lines.forEach((line, index) => {
  console.log(`\n第${index + 1}行: "${line}"`);
  
  // 测试尺寸检测
  const hasDimensions = testDimensionDetection(line);
  console.log(`  尺寸检测: ${hasDimensions ? '✅' : '❌'}`);
  
  // 特别测试问题行
  if (line.includes('250x.1020')) {
    console.log(`  🎯 问题行测试:`);
    const result = testDecimalFix('250x.1020');
    if (result) {
      console.log(`    原始: ${result.original}`);
      console.log(`    修正: ${result.corrected}`);
      console.log(`    数值: ${result.num1} × ${result.num2}`);
      console.log(`    类型: ${result.num2 >= 255 ? '回风口' : '出风口'} (宽度${result.num2}mm)`);
    }
  }
  
  // 提取所有尺寸
  const dimensionMatches = line.match(/\d+(?:\.\d+)?\s*[xX×]\s*\.?\d+(?:\.\d+)?/g);
  if (dimensionMatches) {
    console.log(`  找到尺寸: ${dimensionMatches.join(', ')}`);
  }
});

console.log('\n🎯 预期修复效果:');
console.log('✅ "回风口250x.1020" 应该被识别为风口信息');
console.log('✅ "250x.1020" 应该被修正为 "250x1020"');
console.log('✅ 识别为回风口（宽度1020mm ≥ 255mm）');
console.log('✅ 总尺寸数量从13个增加到14个');

console.log('\n📈 修复规则总结:');
console.log('1. 🔍 增强尺寸检测：支持 x.数字 格式');
console.log('2. 🎯 优先尺寸识别：有尺寸就认为是风口');
console.log('3. 🔧 智能修正：.4位数 → 4位数（OCR错误）');
console.log('4. 📏 根据尺寸确定类型：不依赖关键词准确性');
