/**
 * 📊 表格布局分析器
 * 基于OCR坐标信息进行表格结构识别和数据提取
 */

export interface TableCell {
  id: string
  text: string
  position: {
    left: number
    top: number
    width: number
    height: number
  }
  confidence: number
  row: number
  col: number
  cellType: 'header' | 'data' | 'empty'
}

export interface TableColumn {
  id: string
  name: string
  type: 'sequence' | 'floor' | 'location' | 'product' | 'dimension' | 'quantity' | 'price' | 'note' | 'unknown'
  position: {
    left: number
    width: number
  }
  cells: TableCell[]
}

export interface TableRow {
  id: string
  type: 'header' | 'data'
  position: {
    top: number
    height: number
  }
  cells: TableCell[]
}

export interface TableLayout {
  columns: TableColumn[]
  rows: TableRow[]
  cells: TableCell[]
  structure: {
    hasHeader: boolean
    columnCount: number
    rowCount: number
    confidence: number
  }
}

export class TableLayoutAnalyzer {
  /**
   * 分析OCR结果的表格布局
   */
  static analyzeTableLayout(ocrResult: any): TableLayout {
    console.log('📊 开始表格布局分析...')
    
    const words = ocrResult.words_result || []
    if (words.length === 0) {
      return this.createEmptyLayout()
    }

    // 1. 创建单元格
    const cells = this.createTableCells(words)
    console.log(`📋 创建了 ${cells.length} 个单元格`)

    // 2. 识别行结构
    const rows = this.identifyRows(cells)
    console.log(`📏 识别了 ${rows.length} 行`)

    // 3. 识别列结构
    const columns = this.identifyColumns(cells, rows)
    console.log(`📐 识别了 ${columns.length} 列`)

    // 4. 分类列类型
    this.classifyColumnTypes(columns, rows)

    // 5. 计算结构置信度
    const structure = this.analyzeStructure(columns, rows)

    return {
      columns,
      rows,
      cells,
      structure
    }
  }

  /**
   * 创建表格单元格
   */
  private static createTableCells(words: any[]): TableCell[] {
    return words.map((word, index) => {
      const location = word.location || { left: 0, top: 0, width: 0, height: 0 }
      
      return {
        id: `cell_${index}`,
        text: word.words.trim(),
        position: {
          left: location.left,
          top: location.top,
          width: location.width,
          height: location.height
        },
        confidence: word.probability || 0.8,
        row: -1, // 待分配
        col: -1, // 待分配
        cellType: 'data'
      }
    })
  }

  /**
   * 识别行结构
   */
  private static identifyRows(cells: TableCell[]): TableRow[] {
    console.log('📏 开始识别行结构...')
    
    // 按Y坐标排序
    const sortedCells = [...cells].sort((a, b) => a.position.top - b.position.top)
    
    const rows: TableRow[] = []
    let currentRow: TableCell[] = []
    let lastY = -1
    const rowThreshold = 30 // 行间距阈值

    for (const cell of sortedCells) {
      const cellY = cell.position.top
      
      // 判断是否是新行
      if (lastY === -1 || Math.abs(cellY - lastY) > rowThreshold) {
        // 保存上一行
        if (currentRow.length > 0) {
          const rowTop = Math.min(...currentRow.map(c => c.position.top))
          const rowHeight = Math.max(...currentRow.map(c => c.position.top + c.position.height)) - rowTop
          
          // 改进的行类型判断
          const rowType = this.determineRowType(currentRow, rows.length)

          rows.push({
            id: `row_${rows.length}`,
            type: rowType,
            position: { top: rowTop, height: rowHeight },
            cells: [...currentRow]
          })
          
          // 更新单元格的行号
          currentRow.forEach((c, index) => {
            c.row = rows.length - 1
          })
        }
        
        // 开始新行
        currentRow = [cell]
        lastY = cellY
      } else {
        // 同一行
        currentRow.push(cell)
      }
    }
    
    // 处理最后一行
    if (currentRow.length > 0) {
      const rowTop = Math.min(...currentRow.map(c => c.position.top))
      const rowHeight = Math.max(...currentRow.map(c => c.position.top + c.position.height)) - rowTop
      
      rows.push({
        id: `row_${rows.length}`,
        type: rows.length === 0 ? 'header' : 'data',
        position: { top: rowTop, height: rowHeight },
        cells: [...currentRow]
      })
      
      currentRow.forEach((c, index) => {
        c.row = rows.length - 1
      })
    }

    console.log(`📏 识别完成: ${rows.length} 行`)
    return rows
  }

  /**
   * 识别列结构
   */
  private static identifyColumns(cells: TableCell[], rows: TableRow[]): TableColumn[] {
    console.log('📐 开始识别列结构...')
    
    if (rows.length === 0) return []
    
    // 使用第一行（表头）来确定列结构
    const headerRow = rows[0]
    const headerCells = [...headerRow.cells].sort((a, b) => a.position.left - b.position.left)
    
    const columns: TableColumn[] = []
    
    headerCells.forEach((headerCell, index) => {
      const column: TableColumn = {
        id: `col_${index}`,
        name: headerCell.text,
        type: this.guessColumnType(headerCell.text),
        position: {
          left: headerCell.position.left,
          width: headerCell.position.width
        },
        cells: []
      }
      
      // 为所有行分配此列的单元格
      rows.forEach(row => {
        const cellInColumn = this.findCellInColumn(row.cells, column.position)
        if (cellInColumn) {
          cellInColumn.col = index
          column.cells.push(cellInColumn)
        }
      })
      
      columns.push(column)
    })

    console.log(`📐 识别完成: ${columns.length} 列`)
    return columns
  }

  /**
   * 根据列标题猜测列类型（增强版）
   */
  private static guessColumnType(headerText: string): TableColumn['type'] {
    const text = headerText.toLowerCase()

    // 序号列
    if (/序号|编号|no|num|项目/.test(text)) return 'sequence'

    // 楼层列
    if (/楼层|楼|层|floor/.test(text)) return 'floor'

    // 位置列
    if (/位置|地点|房间|location/.test(text)) return 'location'

    // 产品类型列 - 扩展识别
    if (/产品|型号|规格|类型|风口|product|type/.test(text)) return 'product'

    // 尺寸列 - 扩展识别
    if (/尺寸|规格|size|dimension|长.*宽|宽.*高/.test(text)) return 'dimension'

    // 数量列
    if (/数量|qty|quantity|个数|件数/.test(text)) return 'quantity'

    // 价格列
    if (/价格|单价|金额|price|amount|￥|¥/.test(text)) return 'price'

    // 备注列 - 扩展识别
    if (/备注|说明|note|remark|颜色|材质/.test(text)) return 'note'

    // 如果包含数字和乘号，可能是尺寸列
    if (/\d+\s*[x×*+\-]\s*\d+/.test(text)) return 'dimension'

    // 如果包含"黑色"、"白色"等，可能是产品或备注列
    if (/黑色|白色|出风|回风|线型/.test(text)) {
      return text.includes('风口') ? 'product' : 'note'
    }

    return 'unknown'
  }

  /**
   * 判断行类型（增强版）
   */
  private static determineRowType(rowCells: TableCell[], rowIndex: number): 'header' | 'data' {
    // 第一行通常是表头
    if (rowIndex === 0) return 'header'

    // 检查是否包含表头关键词
    const rowText = rowCells.map(cell => cell.text).join(' ').toLowerCase()
    const headerKeywords = ['序号', '楼层', '尺寸', '数量', '备注', '产品', '类型', '风口', '价格']

    if (headerKeywords.some(keyword => rowText.includes(keyword))) {
      return 'header'
    }

    // 检查是否包含数据特征（尺寸、数字等）
    const hasSize = /\d+\s*[x×*+\-]\s*\d+/.test(rowText)
    const hasNumbers = /\d+/.test(rowText)

    if (hasSize || hasNumbers) {
      return 'data'
    }

    // 默认为数据行
    return 'data'
  }

  /**
   * 在指定列位置查找单元格
   */
  private static findCellInColumn(rowCells: TableCell[], columnPosition: { left: number, width: number }): TableCell | null {
    const columnLeft = columnPosition.left
    const columnRight = columnPosition.left + columnPosition.width
    const tolerance = 50 // 容错范围
    
    for (const cell of rowCells) {
      const cellLeft = cell.position.left
      const cellRight = cell.position.left + cell.position.width
      
      // 检查水平重叠
      const overlapLeft = Math.max(columnLeft - tolerance, cellLeft)
      const overlapRight = Math.min(columnRight + tolerance, cellRight)
      
      if (overlapLeft < overlapRight) {
        return cell
      }
    }
    
    return null
  }

  /**
   * 分类列类型（基于内容分析）
   */
  private static classifyColumnTypes(columns: TableColumn[], rows: TableRow[]): void {
    console.log('🏷️ 开始列类型分类...')
    
    columns.forEach(column => {
      if (column.type !== 'unknown') return // 已经确定类型的跳过
      
      // 分析列内容
      const dataCells = column.cells.filter(cell => cell.row > 0) // 排除表头
      const contents = dataCells.map(cell => cell.text)
      
      // 基于内容模式判断
      if (contents.every(text => /^\d+$/.test(text))) {
        column.type = 'sequence'
      } else if (contents.some(text => /\d+[x×*]\d+/.test(text))) {
        column.type = 'dimension'
      } else if (contents.some(text => /￥|¥|\d+\.\d+/.test(text))) {
        column.type = 'price'
      } else if (contents.some(text => /楼|层|F\d+/.test(text))) {
        column.type = 'floor'
      } else if (contents.some(text => /风口|回风|出风/.test(text))) {
        column.type = 'product'
      } else {
        // 根据位置推断：最后几列通常是备注
        const columnIndex = columns.indexOf(column)
        if (columnIndex >= columns.length - 2) {
          column.type = 'note'
        }
      }
      
      console.log(`🏷️ 列 "${column.name}" 分类为: ${column.type}`)
    })
  }

  /**
   * 分析表格结构
   */
  private static analyzeStructure(columns: TableColumn[], rows: TableRow[]): TableLayout['structure'] {
    const hasHeader = rows.length > 0 && rows[0].type === 'header'
    const columnCount = columns.length
    const rowCount = rows.length
    
    // 计算置信度
    let confidence = 0.5
    
    if (hasHeader) confidence += 0.2
    if (columnCount >= 3) confidence += 0.2
    if (rowCount >= 2) confidence += 0.1
    
    return {
      hasHeader,
      columnCount,
      rowCount,
      confidence: Math.min(confidence, 1.0)
    }
  }

  /**
   * 提取干净的备注信息
   */
  static extractCleanNotes(tableLayout: TableLayout, rowIndex: number): string | undefined {
    console.log(`📝 提取第${rowIndex}行的干净备注...`)
    
    if (rowIndex >= tableLayout.rows.length) return undefined
    
    const row = tableLayout.rows[rowIndex]
    const noteColumns = tableLayout.columns.filter(col => col.type === 'note')
    
    if (noteColumns.length === 0) {
      console.log('❌ 未找到备注列')
      return undefined
    }
    
    const notes: string[] = []
    
    noteColumns.forEach(noteColumn => {
      const cell = noteColumn.cells.find(c => c.row === rowIndex)
      if (cell && cell.text.trim()) {
        let cleanNote = cell.text
          .replace(/mm|㎜|毫米/g, '') // 移除单位
          .replace(/￥\d+(\.\d+)?/g, '') // 移除价格
          .replace(/¥\d+(\.\d+)?/g, '') // 移除价格
          .replace(/\d+\s*个/g, '') // 移除数量
          .replace(/^\d+\s*/, '') // 移除开头数字
          .replace(/\s+/g, ' ') // 合并空格
          .trim()
        
        if (cleanNote.length > 1) {
          notes.push(cleanNote)
        }
      }
    })
    
    const result = notes.join(' ').trim()
    console.log(`📝 提取到干净备注: "${result}"`)
    return result || undefined
  }

  /**
   * 获取指定行列的数据
   */
  static getCellData(tableLayout: TableLayout, rowIndex: number, columnType: TableColumn['type']): string | undefined {
    const column = tableLayout.columns.find(col => col.type === columnType)
    if (!column) return undefined
    
    const cell = column.cells.find(c => c.row === rowIndex)
    return cell?.text.trim()
  }

  /**
   * 创建空布局
   */
  private static createEmptyLayout(): TableLayout {
    return {
      columns: [],
      rows: [],
      cells: [],
      structure: {
        hasHeader: false,
        columnCount: 0,
        rowCount: 0,
        confidence: 0
      }
    }
  }

  /**
   * 生成表格分析报告
   */
  static generateAnalysisReport(tableLayout: TableLayout): any {
    const report = {
      structure: {
        columns: tableLayout.columns.length,
        rows: tableLayout.rows.length,
        hasHeader: tableLayout.structure.hasHeader,
        confidence: tableLayout.structure.confidence
      },
      columnTypes: tableLayout.columns.map(col => ({
        name: col.name,
        type: col.type,
        cellCount: col.cells.length
      })),
      dataQuality: {
        totalCells: tableLayout.cells.length,
        filledCells: tableLayout.cells.filter(c => c.text.trim().length > 0).length,
        averageConfidence: tableLayout.cells.reduce((sum, c) => sum + c.confidence, 0) / tableLayout.cells.length
      }
    }
    
    console.log('📊 表格分析报告:', report)
    return report
  }
}
