'use client'

import { useState } from 'react'

export default function TestSmartOrderPage() {
  const [inputText, setInputText] = useState('')
  const [result, setResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  // 示例文本
  const exampleTexts = [
    {
      name: '手写风口清单',
      text: `1. 140x240 300x1600
2. 140x3480 300x1600
3. 140x3510 300x1600
4. 140x3460 300x1600
5. 150x2460 300x1600
6. 150x3160 300x1600
7. 140x4525 300x1600
8. 140x2600 300x1600
9. 150x2470 300x1600
10. 150x1600 300x1600
11. 150x3540 300x1600
12. 150x3970 300x1600
13. 150x1600 300x1600
14. 150x2970 300x1600`
    },
    {
      name: '项目风口清单',
      text: `粤桂苑A栋 3楼
出风口 1300x300 5个
回风口 1000x300 3个
线型风口 1920x140 2个`
    },
    {
      name: '简单风口信息',
      text: `书香华府1201
出风口1920X140，数量2
回风口1000X300，数量1`
    }
  ]

  const handleGenerate = async () => {
    if (!inputText.trim()) {
      alert('请输入文本内容')
      return
    }

    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/ocr/smart-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: inputText,
          factoryId: 'test-factory-001',
          createdBy: 'test-user-001'
        })
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        error: '网络请求失败: ' + (error instanceof Error ? error.message : '未知错误')
      })
    } finally {
      setIsLoading(false)
    }
  }

  const loadExample = (text: string) => {
    setInputText(text)
    setResult(null)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🤖 智能OCR订单生成测试
          </h1>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 输入区域 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                📝 输入文本
              </h2>
              
              {/* 示例按钮 */}
              <div className="mb-4">
                <div className="text-sm text-gray-600 mb-2">快速加载示例:</div>
                <div className="flex flex-wrap gap-2">
                  {exampleTexts.map((example, index) => (
                    <button
                      key={index}
                      onClick={() => loadExample(example.text)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200 transition-colors"
                    >
                      {example.name}
                    </button>
                  ))}
                </div>
              </div>

              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="请输入包含风口信息的文本，例如：
140x240 出风口 5个
回风口 300x160 数量3
线型风口 1200x140 x2"
                className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />

              <button
                onClick={handleGenerate}
                disabled={isLoading || !inputText.trim()}
                className="w-full mt-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? '🔄 正在生成智能订单...' : '🧠 生成智能订单'}
              </button>
            </div>

            {/* 结果区域 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                📊 生成结果
              </h2>

              {!result ? (
                <div className="bg-gray-100 rounded-lg p-8 text-center text-gray-500">
                  请输入文本并点击生成按钮
                </div>
              ) : result.success ? (
                <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                  <div className="flex items-center mb-4">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="font-medium text-green-800">订单生成成功</span>
                  </div>

                  {/* 基本信息 */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="bg-white rounded p-3">
                      <div className="text-sm text-gray-600">项目地址</div>
                      <div className="font-medium">{result.data.orderData.projectAddress}</div>
                    </div>
                    <div className="bg-white rounded p-3">
                      <div className="text-sm text-gray-600">识别置信度</div>
                      <div className="font-medium text-blue-600">
                        {(result.data.confidence * 100).toFixed(1)}%
                      </div>
                    </div>
                    <div className="bg-white rounded p-3">
                      <div className="text-sm text-gray-600">项目数量</div>
                      <div className="font-medium">{result.data.orderData.items.length} 个</div>
                    </div>
                    <div className="bg-white rounded p-3">
                      <div className="text-sm text-gray-600">订单总金额</div>
                      <div className="font-medium text-green-600">
                        ¥{result.data.orderData.totalAmount.toFixed(1)}
                      </div>
                    </div>
                  </div>

                  {/* 项目明细 */}
                  <div className="bg-white rounded-lg p-4 mb-4">
                    <h4 className="font-medium text-gray-800 mb-3">📋 订单项目明细</h4>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {result.data.orderData.items.map((item: any, index: number) => (
                        <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                          <div className="flex-1">
                            <div className="font-medium text-gray-800">{item.productName}</div>
                            <div className="text-sm text-gray-600">
                              {item.specifications} × {item.quantity}个
                              {item.notes && <span className="ml-2 text-blue-600">({item.notes})</span>}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium text-gray-800">¥{item.totalPrice.toFixed(1)}</div>
                            <div className="text-sm text-gray-500">¥{item.unitPrice}/㎡</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 建议和警告 */}
                  {result.data.orderResult.suggestions && result.data.orderResult.suggestions.length > 0 && (
                    <div className="bg-blue-50 rounded p-3 mb-4">
                      <h5 className="font-medium text-blue-800 mb-2">💡 建议</h5>
                      <ul className="text-sm text-blue-700 space-y-1">
                        {result.data.orderResult.suggestions.map((suggestion: string, index: number) => (
                          <li key={index}>• {suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {result.data.orderResult.warnings && result.data.orderResult.warnings.length > 0 && (
                    <div className="bg-yellow-50 rounded p-3">
                      <h5 className="font-medium text-yellow-800 mb-2">⚠️ 警告</h5>
                      <ul className="text-sm text-yellow-700 space-y-1">
                        {result.data.orderResult.warnings.map((warning: string, index: number) => (
                          <li key={index}>• {warning}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                  <div className="flex items-center mb-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <span className="font-medium text-red-800">订单生成失败</span>
                  </div>
                  <div className="text-red-700 mb-3">{result.error}</div>

                  {result.data?.suggestions && (
                    <div className="bg-white rounded p-3">
                      <h5 className="font-medium text-gray-800 mb-2">💡 建议</h5>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {result.data.suggestions.map((suggestion: string, index: number) => (
                          <li key={index}>• {suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 功能说明 */}
          <div className="mt-8 bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-4">
              🔧 智能图片识别功能说明
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-blue-700 mb-2">🧠 NLP意图识别</h4>
                <ul className="text-sm text-blue-600 space-y-1">
                  <li>• 自动识别订单创建意图</li>
                  <li>• 智能提取风口类型和尺寸</li>
                  <li>• 支持中文数字和阿拉伯数字</li>
                  <li>• 自动智能尺寸排序(长×宽)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-blue-700 mb-2">⚡ 智能订单生成</h4>
                <ul className="text-sm text-blue-600 space-y-1">
                  <li>• 自动匹配风口类型和单价</li>
                  <li>• 智能计算订单总金额</li>
                  <li>• 提供识别置信度评估</li>
                  <li>• 生成优化建议和警告</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
