/**
 * 🇨🇳 风口云平台 - 单个分红操作API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 获取单个分红信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: dividendId } = await params

    if (!dividendId) {
      return NextResponse.json(
        { error: '分红ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📞 获取分红信息请求:', dividendId)

    // 获取分红信息
    const dividend = await db.getDividendById(dividendId)

    if (!dividend) {
      return NextResponse.json(
        { error: '分红不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      dividend
    })

  } catch (error) {
    console.error('❌ 获取分红信息失败:', error)
    return NextResponse.json(
      { error: '获取分红信息失败' },
      { status: 500 }
    )
  }
}

// 更新单个分红信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: dividendId } = await params
    const updates = await request.json()

    if (!dividendId) {
      return NextResponse.json(
        { error: '分红ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📞 更新分红信息请求:', dividendId)

    // 更新分红信息
    const dividend = await db.updateDividend(dividendId, updates)

    if (!dividend) {
      return NextResponse.json(
        { error: '更新分红信息失败' },
        { status: 500 }
      )
    }

    console.log('✅ 分红信息更新成功:', dividend.title)

    return NextResponse.json({
      success: true,
      dividend
    })

  } catch (error) {
    console.error('❌ 更新分红信息失败:', error)
    return NextResponse.json(
      { error: '更新分红信息失败' },
      { status: 500 }
    )
  }
}

// 删除单个分红
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: dividendId } = await params

    if (!dividendId) {
      return NextResponse.json(
        { error: '分红ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📞 删除分红请求:', dividendId)

    // 删除分红
    const success = await db.deleteDividend(dividendId)

    if (!success) {
      return NextResponse.json(
        { error: '删除分红失败' },
        { status: 500 }
      )
    }

    console.log('✅ 分红删除成功')

    return NextResponse.json({
      success: true
    })

  } catch (error) {
    console.error('❌ 删除分红失败:', error)
    return NextResponse.json(
      { error: '删除分红失败' },
      { status: 500 }
    )
  }
}
