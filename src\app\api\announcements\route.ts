/**
 * 🇨🇳 风口云平台 - 公告管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 获取公告列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')

    let announcements
    if (factoryId) {
      // 获取特定工厂的公告
      announcements = await db.getAnnouncementsForFactory(factoryId)
    } else {
      // 获取所有公告
      announcements = await db.getAnnouncements()
    }

    return NextResponse.json({ success: true, announcements })

  } catch (error) {
    console.error('❌ 获取公告失败:', error)
    return NextResponse.json(
      { error: '获取公告失败' },
      { status: 500 }
    )
  }
}

// 创建公告
export async function POST(request: NextRequest) {
  try {
    console.log('📢 接收到创建公告请求')
    const announcementData = await request.json()

    if (!announcementData.title || !announcementData.content) {
      console.log('❌ 缺少必要的公告信息')
      return NextResponse.json(
        { error: '缺少必要的公告信息' },
        { status: 400 }
      )
    }

    console.log('📝 创建公告:', announcementData.title)
    const announcement = await db.createAnnouncement(announcementData)

    if (!announcement) {
      console.log('❌ 创建公告失败：返回结果为空')
      return NextResponse.json(
        { error: '创建公告失败：数据保存异常' },
        { status: 500 }
      )
    }

    console.log('✅ 公告创建成功:', announcement.id)

    return NextResponse.json({
      success: true,
      announcement
    })

  } catch (error) {
    console.error('❌ 创建公告失败:', error)
    return NextResponse.json(
      { error: `创建公告失败: ${error.message || '未知错误'}` },
      { status: 500 }
    )
  }
}

// 更新公告
export async function PUT(request: NextRequest) {
  try {
    const { id, ...updates } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: '缺少公告ID' },
        { status: 400 }
      )
    }

    const announcement = await db.updateAnnouncement(id, updates)

    if (!announcement) {
      return NextResponse.json(
        { error: '更新公告失败' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      announcement
    })

  } catch (error) {
    console.error('❌ 更新公告失败:', error)
    return NextResponse.json(
      { error: '更新公告失败' },
      { status: 500 }
    )
  }
}

// 删除公告
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: '缺少公告ID' },
        { status: 400 }
      )
    }

    const success = await db.deleteAnnouncement(id)

    if (!success) {
      return NextResponse.json(
        { error: '删除公告失败' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true
    })

  } catch (error) {
    console.error('❌ 删除公告失败:', error)
    return NextResponse.json(
      { error: '删除公告失败' },
      { status: 500 }
    )
  }
}
