/**
 * 🇨🇳 风口云平台 - 修复奖励显示API
 *
 * 专门修复客户奖励显示问题的API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { calculateTotalReferralReward } from '@/lib/utils/reward-calculator'
import { safeNumber } from '@/lib/utils/number-utils'
import { getFactorySettings } from '@/lib/utils/factory-settings'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 收到修复奖励显示请求')

    // 获取当前工厂ID
    const factoryId = getCurrentFactoryId()
    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '无法获取工厂ID'
      }, { status: 400 })
    }

    console.log('🏭 开始修复工厂奖励显示:', factoryId)

    // 获取所有客户和订单
    const clients = await db.getClientsByFactoryId(factoryId)
    const orders = await db.getOrdersByFactoryId(factoryId)

    console.log(`📊 数据统计: ${clients.length} 个客户, ${orders.length} 个订单`)

    // 找出所有推荐人
    const referrers = clients.filter(client => 
      clients.some(c => c.referrerId === client.id)
    )

    console.log(`👥 找到 ${referrers.length} 个推荐人`)

    const results = []
    let successCount = 0
    let errorCount = 0
    let totalRewardFixed = 0

    for (const referrer of referrers) {
      try {
        console.log(`\n🔧 修复推荐人: ${referrer.name}`)

        // 记录修复前的状态
        const before = {
          referralReward: safeNumber(referrer.referralReward || 0),
          availableReward: safeNumber(referrer.availableReward || 0),
          pendingReward: safeNumber(referrer.pendingReward || 0),
          referralCount: safeNumber(referrer.referralCount || 0)
        }

        // 获取被推荐的客户
        const referredClients = clients.filter(c => c.referrerId === referrer.id)
        console.log(`  推荐了 ${referredClients.length} 个客户`)

        // 重新计算总奖励
        let totalCalculatedReward = 0
        const rewardDetails = []

        for (const referred of referredClients) {
          const clientOrders = orders.filter(order => order.clientId === referred.id)
          const rewardResult = calculateTotalReferralReward(clientOrders, factoryId)
          totalCalculatedReward += rewardResult.totalReward

          rewardDetails.push({
            clientName: referred.name,
            orderCount: clientOrders.length,
            totalAmount: clientOrders.reduce((sum, order) => sum + safeNumber(order.totalAmount), 0),
            rewardAmount: rewardResult.totalReward,
            breakdown: rewardResult.breakdown
          })
        }

        console.log(`  计算得出总奖励: ¥${totalCalculatedReward.toFixed(2)}`)
        console.log(`  奖励明细:`, rewardDetails)

        // 根据工厂设置判断奖励是否可用
        const settings = getFactorySettings(factoryId)
        const requireFullPayment = settings.rewardSettings.rewardRules.requireFullPayment

        // 检查推荐人是否满足使用奖励的条件
        const referrerOrders = orders.filter(order => order.clientId === referrer.id)
        const canUseReward = !requireFullPayment || referrerOrders.every(order =>
          safeNumber(order.paidAmount) >= safeNumber(order.totalAmount)
        )

        // 更新数据库中的奖励信息
        await db.updateClient(referrer.id, {
          referralReward: totalCalculatedReward,
          availableReward: canUseReward ? totalCalculatedReward : 0,
          pendingReward: canUseReward ? 0 : totalCalculatedReward,
          referralCount: referredClients.length
        })

        // 记录修复后的状态
        const after = {
          referralReward: totalCalculatedReward,
          availableReward: canUseReward ? totalCalculatedReward : 0,
          pendingReward: canUseReward ? 0 : totalCalculatedReward,
          referralCount: referredClients.length
        }

        const rewardDifference = Math.abs(totalCalculatedReward - before.referralReward)
        totalRewardFixed += rewardDifference

        results.push({
          clientId: referrer.id,
          clientName: referrer.name,
          before,
          after,
          status: 'success',
          message: `奖励已更新: ¥${before.referralReward.toFixed(2)} → ¥${totalCalculatedReward.toFixed(2)}`,
          rewardDetails,
          canUseReward,
          requireFullPayment: settings.rewardSettings.rewardRules.requireFullPayment
        })

        successCount++
        console.log(`✅ 修复成功: ${referrer.name} - 奖励: ¥${totalCalculatedReward.toFixed(2)} (${canUseReward ? '可用' : '待结算'})`)

      } catch (error) {
        console.error(`❌ 修复失败: ${referrer.name}`, error)
        
        results.push({
          clientId: referrer.id,
          clientName: referrer.name,
          before: {
            referralReward: safeNumber(referrer.referralReward || 0),
            availableReward: safeNumber(referrer.availableReward || 0),
            pendingReward: safeNumber(referrer.pendingReward || 0),
            referralCount: safeNumber(referrer.referralCount || 0)
          },
          after: {
            referralReward: 0,
            availableReward: 0,
            pendingReward: 0,
            referralCount: 0
          },
          status: 'error',
          message: `修复失败: ${error.message}`
        })

        errorCount++
      }
    }

    console.log('✅ 奖励显示修复完成')
    
    return NextResponse.json({
      success: true,
      message: '奖励显示修复完成',
      summary: {
        totalProcessed: referrers.length,
        successCount,
        errorCount,
        totalRewardFixed
      },
      results
    })

  } catch (error) {
    console.error('❌ 修复奖励显示API错误:', error)
    return NextResponse.json({
      success: false,
      error: `服务器错误: ${error.message}`
    }, { status: 500 })
  }
}
