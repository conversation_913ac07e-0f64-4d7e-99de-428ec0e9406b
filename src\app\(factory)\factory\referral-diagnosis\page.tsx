'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Users,
  Gift
} from "lucide-react"
import { db } from '@/lib/database'
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { safeNumber, safeAmountFormat } from '@/lib/utils/number-utils'
import { calculateTotalReferralReward } from '@/lib/utils/reward-calculator'

interface DiagnosisResult {
  clientId: string
  clientName: string
  referrerId?: string
  referrerName?: string
  hasReferrer: boolean
  orderCount: number
  totalOrderAmount: number
  calculatedReward: number
  currentReward: number
  availableReward: number
  pendingReward: number
  rewardStatus: 'correct' | 'missing' | 'incorrect'
  issues: string[]
}

export default function ReferralDiagnosisPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<DiagnosisResult[]>([])
  const [summary, setSummary] = useState({
    totalClients: 0,
    clientsWithReferrers: 0,
    correctRewards: 0,
    missingRewards: 0,
    incorrectRewards: 0
  })

  // 诊断推荐奖励问题
  const runDiagnosis = async () => {
    try {
      setLoading(true)
      setResults([])
      
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        alert('无法获取工厂ID')
        return
      }

      console.log('🔍 开始诊断推荐奖励问题...')

      // 获取所有客户
      const clients = await db.getClientsByFactoryId(factoryId)
      console.log(`📋 找到 ${clients.length} 个客户`)

      // 获取所有订单
      const orders = await db.getOrdersByFactoryId(factoryId)
      console.log(`📦 找到 ${orders.length} 个订单`)

      const diagnosisResults: DiagnosisResult[] = []
      let correctCount = 0
      let missingCount = 0
      let incorrectCount = 0
      let clientsWithReferrersCount = 0

      for (const client of clients) {
        const hasReferrer = !!client.referrerId
        if (hasReferrer) clientsWithReferrersCount++

        // 获取客户的订单
        const clientOrders = orders.filter(order => order.clientId === client.id)
        const totalOrderAmount = clientOrders.reduce((sum, order) => sum + safeNumber(order.totalAmount), 0)

        // 计算应该获得的奖励
        let calculatedReward = 0
        if (hasReferrer && clientOrders.length > 0) {
          const rewardResult = calculateTotalReferralReward(clientOrders, factoryId)
          calculatedReward = rewardResult.totalReward
        }

        // 获取当前奖励状态
        const currentReward = safeNumber(client.referralReward || 0)
        const availableReward = safeNumber(client.availableReward || 0)
        const pendingReward = safeNumber(client.pendingReward || 0)

        // 判断奖励状态
        let rewardStatus: 'correct' | 'missing' | 'incorrect' = 'correct'
        const issues: string[] = []

        if (hasReferrer && clientOrders.length > 0) {
          // 有推荐人且有订单，应该有奖励
          if (calculatedReward > 0 && currentReward === 0) {
            rewardStatus = 'missing'
            issues.push('应该有推荐奖励但当前为0')
            missingCount++
          } else if (Math.abs(calculatedReward - currentReward) > 0.01) {
            rewardStatus = 'incorrect'
            issues.push(`奖励金额不正确：应为${safeAmountFormat(calculatedReward)}，实际为${safeAmountFormat(currentReward)}`)
            incorrectCount++
          } else {
            correctCount++
          }

          // 检查推荐人信息
          if (!client.referrerName) {
            issues.push('缺少推荐人姓名')
          }
        } else if (!hasReferrer && currentReward > 0) {
          rewardStatus = 'incorrect'
          issues.push('无推荐人但有奖励记录')
          incorrectCount++
        } else if (hasReferrer && clientOrders.length === 0) {
          // 有推荐人但无订单，奖励应该为0
          if (currentReward > 0) {
            rewardStatus = 'incorrect'
            issues.push('无订单但有奖励记录')
            incorrectCount++
          } else {
            correctCount++
          }
        } else {
          correctCount++
        }

        diagnosisResults.push({
          clientId: client.id,
          clientName: client.name,
          referrerId: client.referrerId || undefined,
          referrerName: client.referrerName || undefined,
          hasReferrer,
          orderCount: clientOrders.length,
          totalOrderAmount,
          calculatedReward,
          currentReward,
          availableReward,
          pendingReward,
          rewardStatus,
          issues
        })
      }

      setResults(diagnosisResults)
      setSummary({
        totalClients: clients.length,
        clientsWithReferrers: clientsWithReferrersCount,
        correctRewards: correctCount,
        missingRewards: missingCount,
        incorrectRewards: incorrectCount
      })

      console.log('✅ 诊断完成:', {
        总客户数: clients.length,
        有推荐人的客户: clientsWithReferrersCount,
        奖励正确: correctCount,
        奖励缺失: missingCount,
        奖励错误: incorrectCount
      })

    } catch (error) {
      console.error('❌ 诊断失败:', error)
      alert('诊断失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 修复推荐奖励
  const fixRewards = async () => {
    try {
      setLoading(true)
      
      const response = await fetch('/api/clients/fix-referral-rewards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const result = await response.json()
      
      if (result.success) {
        alert('推荐奖励修复成功！')
        // 重新运行诊断
        await runDiagnosis()
      } else {
        alert(`修复失败：${result.error}`)
      }
    } catch (error) {
      console.error('❌ 修复失败:', error)
      alert('修复失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'correct': return 'bg-green-100 text-green-800'
      case 'missing': return 'bg-red-100 text-red-800'
      case 'incorrect': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'correct': return <CheckCircle className="h-4 w-4" />
      case 'missing': 
      case 'incorrect': return <AlertCircle className="h-4 w-4" />
      default: return null
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">推荐奖励诊断</h1>
            <p className="text-gray-600">检查和修复客户推荐奖励问题</p>
          </div>
          <div className="flex space-x-3">
            <Button 
              onClick={runDiagnosis} 
              disabled={loading}
              variant="outline"
            >
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  诊断中...
                </>
              ) : (
                <>
                  <AlertCircle className="h-4 w-4 mr-2" />
                  开始诊断
                </>
              )}
            </Button>
            <Button 
              onClick={fixRewards} 
              disabled={loading || results.length === 0}
            >
              <Gift className="h-4 w-4 mr-2" />
              修复奖励
            </Button>
          </div>
        </div>

        {/* 诊断摘要 */}
        {results.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总客户数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summary.totalClients}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">有推荐人</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summary.clientsWithReferrers}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">奖励正确</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{summary.correctRewards}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">奖励缺失</CardTitle>
                <AlertCircle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{summary.missingRewards}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">奖励错误</CardTitle>
                <AlertCircle className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{summary.incorrectRewards}</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 详细结果 */}
        {results.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>诊断详情</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results
                  .filter(result => result.rewardStatus !== 'correct' || result.hasReferrer)
                  .map((result) => (
                  <div key={result.clientId} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <h3 className="font-semibold">{result.clientName}</h3>
                        <Badge className={getStatusColor(result.rewardStatus)}>
                          {getStatusIcon(result.rewardStatus)}
                          <span className="ml-1">
                            {result.rewardStatus === 'correct' ? '正确' : 
                             result.rewardStatus === 'missing' ? '缺失' : '错误'}
                          </span>
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        {result.hasReferrer ? `推荐人: ${result.referrerName}` : '无推荐人'}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">订单数量</p>
                        <p className="font-medium">{result.orderCount}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">订单总额</p>
                        <p className="font-medium">{safeAmountFormat(result.totalOrderAmount)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">应得奖励</p>
                        <p className="font-medium">{safeAmountFormat(result.calculatedReward)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">当前奖励</p>
                        <p className="font-medium">{safeAmountFormat(result.currentReward)}</p>
                      </div>
                    </div>

                    {result.issues.length > 0 && (
                      <div className="mt-3">
                        <p className="text-sm font-medium text-red-600 mb-1">问题:</p>
                        <ul className="text-sm text-red-600 space-y-1">
                          {result.issues.map((issue, index) => (
                            <li key={index}>• {issue}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {results.length === 0 && !loading && (
          <Card>
            <CardContent className="p-8 text-center">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">开始诊断</h3>
              <p className="text-gray-600 mb-4">点击"开始诊断"按钮来检查推荐奖励问题
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
