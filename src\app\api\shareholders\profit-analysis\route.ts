/**
 * 🇨🇳 风口云平台 - 股东利润分析API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import * as XLSX from 'xlsx'

// 获取利润分析数据
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const factoryId = searchParams.get('factoryId')
    const viewType = searchParams.get('viewType') || 'monthly'
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString())
    const month = searchParams.get('month') ? parseInt(searchParams.get('month')!) : undefined
    const quarter = searchParams.get('quarter') ? parseInt(searchParams.get('quarter')!) : undefined
    const isExport = searchParams.get('export') === 'true'

    if (!factoryId) {
      return NextResponse.json(
        { error: '工厂ID不能为空' },
        { status: 400 }
      )
    }

    console.log('📊 获取利润分析数据:', {
      factoryId,
      viewType,
      year,
      month,
      quarter,
      isExport
    })

    // 获取利润分析数据
    const result = await db.getProfitAnalysis({
      factoryId,
      viewType: viewType as 'monthly' | 'quarterly' | 'yearly',
      year,
      month,
      quarter
    })

    // 获取股东信息用于利润分配计算
    const shareholders = await db.getShareholdersByFactoryId(factoryId)

    if (isExport) {
      // 导出Excel文件
      const excelBuffer = await generateExcelReport(
        result.data,
        result.summary,
        shareholders,
        viewType,
        year,
        month,
        quarter
      )

      const fileName = getExportFileName(viewType, year, month, quarter)
      return new NextResponse(excelBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${fileName}"`
        }
      })
    }

    // 计算股东利润分配
    const shareholderProfits = calculateShareholderProfits(result.summary.totalProfit, shareholders)

    return NextResponse.json({
      success: true,
      data: result.data,
      summary: result.summary,
      shareholders: shareholderProfits
    })

  } catch (error) {
    console.error('❌ 获取利润分析数据失败:', error)
    return NextResponse.json(
      { error: '获取利润分析数据失败' },
      { status: 500 }
    )
  }
}

// 计算股东利润分配
function calculateShareholderProfits(totalProfit: number, shareholders: unknown[]) {
  return shareholders
    .filter(s => s.status === 'active')
    .map(shareholder => ({
      id: shareholder.id,
      name: shareholder.name,
      shareholderType: shareholder.shareholderType,
      sharePercentage: parseFloat(shareholder.sharePercentage.toString()),
      investmentAmount: parseFloat(shareholder.investmentAmount.toString()),
      profitShare: (totalProfit * parseFloat(shareholder.sharePercentage.toString())) / 100
    }))
    .sort((a, b) => b.sharePercentage - a.sharePercentage)
}

// 获取导出文件名
function getExportFileName(viewType: string, year: number, month?: number, quarter?: number): string {
  let period = `${year}年`

  if (viewType === 'monthly' && month) {
    period += `${month}月`
  } else if (viewType === 'quarterly' && quarter) {
    period += `第${quarter}季度`
  }

  return `股东利润分析报表_${period}.xlsx`
}

// 生成Excel报表
async function generateExcelReport(
  data: unknown[],
  summary: unknown,
  shareholders: unknown[],
  viewType: string,
  year: number,
  month?: number,
  quarter?: number
): Promise<Buffer> {
  const wb = XLSX.utils.book_new()

  // 利润分析数据工作表
  const profitData = [
    ['股东利润分析报表', ''],
    ['生成时间', new Date().toLocaleString('zh-CN')],
    ['统计周期', getExportFileName(viewType, year, month, quarter).replace('.xlsx', '')],
    ['', ''],
    ['利润分析明细', ''],
    ['期间', '收入(元)', '成本(元)', '费用(元)', '利润(元)', '利润率(%)'],
    ...data.map(item => [
      item.period,
      item.revenue,
      item.costs,
      item.expenses,
      item.profit,
      item.profitMargin.toFixed(2)
    ]),
    ['', ''],
    ['汇总信息', ''],
    ['项目', '金额(元)'],
    ['总收入', summary.totalRevenue],
    ['总成本', summary.totalCosts],
    ['总利润', summary.totalProfit],
    ['平均利润率', `${summary.averageProfitMargin.toFixed(2)}%`],
    ['最佳期间', summary.bestMonth],
    ['最差期间', summary.worstMonth]
  ]

  const profitWs = XLSX.utils.aoa_to_sheet(profitData)
  profitWs['!cols'] = [
    { wch: 20 }, // 期间/项目列
    { wch: 15 }  // 数值列
  ]
  XLSX.utils.book_append_sheet(wb, profitWs, '利润分析')

  // 股东利润分配工作表
  const shareholderProfits = calculateShareholderProfits(summary.totalProfit, shareholders)
  const shareholderData = [
    ['股东利润分配明细', ''],
    ['总利润', summary.totalProfit],
    ['分配日期', new Date().toLocaleDateString('zh-CN')],
    ['', ''],
    ['股东姓名', '股东类型', '持股比例(%)', '投资金额(元)', '应分利润(元)'],
    ...shareholderProfits.map(sh => [
      sh.name,
      getShareholderTypeText(sh.shareholderType),
      sh.sharePercentage.toFixed(2),
      sh.investmentAmount,
      sh.profitShare.toFixed(2)
    ]),
    ['', ''],
    ['分配汇总', ''],
    ['活跃股东数', shareholderProfits.length],
    ['总分配金额', shareholderProfits.reduce((sum, sh) => sum + sh.profitShare, 0).toFixed(2)],
    ['分配比例', '100.00%']
  ]

  const shareholderWs = XLSX.utils.aoa_to_sheet(shareholderData)
  shareholderWs['!cols'] = [
    { wch: 15 }, // 股东姓名
    { wch: 12 }, // 股东类型
    { wch: 12 }, // 持股比例
    { wch: 15 }, // 投资金额
    { wch: 15 }  // 应分利润
  ]
  XLSX.utils.book_append_sheet(wb, shareholderWs, '股东分配')

  return XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' })
}

// 获取股东类型显示文本
function getShareholderTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    'FOUNDER': '创始股东',
    'INVESTOR': '投资股东',
    'EMPLOYEE': '员工股东',
    'PARTNER': '合伙人股东'
  }
  return typeMap[type] || type
}
