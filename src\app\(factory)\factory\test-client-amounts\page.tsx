"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle  } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { RefreshCw, Users, ShoppingCart } from "lucide-react"
import { getCurrentFactoryId } from '@/lib/utils/factory'
import { db } from '@/lib/database/client'
import { safeAmount } from '@/lib/utils/number-utils'
import type { Client, Order } from '@/types'

export default function TestClientAmountsPage() {
  const [clients, setClients] = useState<Client[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(false)

  const loadData = async () => {
    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()

      if (!factoryId) {
        console.error('无法获取工厂ID')
        return
      }

      console.log('🔄 加载客户和订单数据...')

      // 加载客户数据
      const clientsData = await db.getClientsByFactoryId(factoryId)
      console.log('✅ 客户数据:', clientsData)
      console.log('📋 客户ID列表:', clientsData.map(c => `${c.name}(${c.id})`))
      setClients(clientsData)

      // 加载订单数据
      const ordersData = await db.getOrdersByFactoryId(factoryId)
      console.log('✅ 订单数据:', ordersData)
      console.log('📋 订单客户关联:', ordersData.map(o => `订单${o.id} -> 客户${o.clientId} (${o.clientName || '无名称'})`))
      setOrders(ordersData)

      // 检查数据一致性
      console.log('\n🔍 数据一致性检查:')
      const clientIds = new Set(clientsData.map(c => c.id))
      const orderClientIds = new Set(ordersData.map(o => o.clientId))

      console.log('客户ID数量:', clientIds.size)
      console.log('订单中的客户ID数量:', orderClientIds.size)

      // 找出订单中没有对应客户的情况
      const orphanOrders = ordersData.filter(order => !clientIds.has(order.clientId))
      if (orphanOrders.length > 0) {
        console.warn('⚠️ 发现孤立订单（没有对应客户）:', orphanOrders.map(o => `${o.id}(客户ID:${o.clientId})`))
      }

      // 找出客户没有订单的情况
      const clientsWithoutOrders = clientsData.filter(client => !orderClientIds.has(client.id))
      if (clientsWithoutOrders.length > 0) {
        console.log('📝 没有订单的客户:', clientsWithoutOrders.map(c => c.name))
      }

    } catch (error) {
      console.error('❌ 加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [])

  // 计算客户统计信息
  const getClientStats = (clientId: string) => {
    const clientOrders = orders.filter(order => order.clientId === clientId)
    const totalOrders = clientOrders.length
    const totalAmount = clientOrders.reduce((sum, order) => sum + (order.totalAmount || 0), 0)

    return {
      totalOrders,
      totalAmount,
      orders: clientOrders
    }
  }

  // 修复数据关联
  const fixDataAssociation = async () => {
    try {
      setLoading(true)
      console.log('🔧 开始修复数据关联...')

      const factoryId = getCurrentFactoryId()
      if (!factoryId) return

      // 重新加载数据
      const clientsData = await db.getClientsByFactoryId(factoryId)
      const ordersData = await db.getOrdersByFactoryId(factoryId)

      console.log('📋 当前客户数量:', clientsData.length)
      console.log('📋 当前订单数量:', ordersData.length)

      // 找出孤立订单（没有对应客户的订单）
      const clientIds = new Set(clientsData.map(c => c.id))
      const orphanOrders = ordersData.filter(order => !clientIds.has(order.clientId))

      console.log('🔍 发现孤立订单数量:', orphanOrders.length)

      // 为孤立订单创建客户记录
      for (const order of orphanOrders) {
        console.log(`🔧 为订单 ${order.id} 创建客户记录...`)

        // 检查订单是否有客户信息
        if (order.clientName && order.clientPhone) {
          const clientData = {
            factoryId: factoryId,
            name: order.clientName,
            phone: order.clientPhone,
            email: '',
            company: '',
            address: order.projectAddress || '未填写',
            status: 'active' as const,
            totalOrders: 0,
            totalAmount: 0,
            referralCount: 0,
            referralReward: 0,
            paidAmount: 0,
            unpaidAmount: 0,
            overdueAmount: 0
          }

          console.log('📝 创建客户数据:', clientData)

          // 创建客户记录，使用订单中的clientId
          if (typeof db.createClient === 'function') {
            // 这里我们需要手动设置客户ID来匹配订单
            const createdClient = await db.createClient(clientData)
            if (createdClient) {
              console.log(`✅ 客户创建成功: ${createdClient.name} (${createdClient.id})`)

              // 如果创建的客户ID与订单中的不匹配，我们需要更新订单的clientId
              if (createdClient.id !== order.clientId) {
                console.log(`🔄 更新订单 ${order.id} 的客户ID: ${order.clientId} -> ${createdClient.id}`)
                // 这里需要一个更新订单的方法
              }
            }
          }
        } else {
          console.warn(`⚠️ 订单 ${order.id} 缺少客户信息，无法创建客户记录`)
          console.log('订单客户信息:', {
            clientId: order.clientId,
            clientName: order.clientName,
            clientPhone: order.clientPhone
          })
        }
      }

      // 重新加载数据
      await loadData()

      console.log('✅ 数据关联修复完成')

    } catch (error) {
      console.error('❌ 修复失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">客户金额测试</h1>
            <p className="text-gray-600">测试客户和订单的关联及金额计算</p>
          </div>
          <div className="flex space-x-4">
            <Button onClick={fixDataAssociation} disabled={loading} variant="outline">
              修复关联
            </Button>
            <Button onClick={loadData} disabled={loading}>
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  加载中...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  刷新数据
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 客户统计 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>客户统计信息</span>
              </CardTitle>
              <CardDescription>显示每个客户的订单数量和总金额</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {clients.map(client => {
                  const stats = getClientStats(client.id)
                  return (
                    <div key={client.id} className="p-4 border rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-medium">{client.name}</h3>
                          <p className="text-sm text-gray-600">ID: {client.id}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">
                            {stats.totalOrders} 个订单
                          </div>
                          <div className="text-lg font-bold text-green-600">
                            {safeAmount(stats.totalAmount)}
                          </div>
                        </div>
                      </div>
                      
                      {/* 显示该客户的订单详情 */}
                      {stats.orders.length > 0 && (
                        <div className="mt-3 pt-3 border-t">
                          <p className="text-sm font-medium text-gray-700 mb-2">订单详情:</p>
                          {stats.orders.map(order => (
                            <div key={order.id} className="text-sm text-gray-600 flex justify-between">
                              <span>{order.orderNumber || order.id}</span>
                              <span>{safeAmount(order.totalAmount)}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )
                })}
                
                {clients.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    暂无客户数据
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 订单列表 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ShoppingCart className="h-5 w-5" />
                <span>订单列表</span>
              </CardTitle>
              <CardDescription>显示所有订单及其客户关联</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {orders.map(order => {
                  const client = clients.find(c => c.id === order.clientId)
                  return (
                    <div key={order.id} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{order.orderNumber || order.id}</h4>
                          <p className="text-sm text-gray-600">
                            客户ID: {order.clientId}
                          </p>
                          <p className="text-sm text-gray-600">
                            客户名称: {client?.name || order.clientName || '未找到客户'}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-green-600">
                            {safeAmount(order.totalAmount)}
                          </div>
                          <div className="text-sm text-gray-600">
                            {order.items.length} 个项目
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
                
                {orders.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    暂无订单数据
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 数据概览 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>数据概览</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{clients.length}</div>
                <p className="text-sm text-gray-600">总客户数</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{orders.length}</div>
                <p className="text-sm text-gray-600">总订单数</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {safeAmount(orders.reduce((sum, order) => sum + order.totalAmount, 0))}
                </div>
                <p className="text-sm text-gray-600">总金额</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {orders.filter(order => {
                    const client = clients.find(c => c.id === order.clientId)
                    return !client
                  }).length}
                </div>
                <p className="text-sm text-gray-600">未关联订单</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
