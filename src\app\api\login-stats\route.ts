/**
 * 🇨🇳 风口云平台 - 登录统计API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 获取查询参数
    const factoryId = searchParams.get('factoryId')
    const userType = searchParams.get('userType') as 'admin' | 'factory_user' | null
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // 构建查询选项
    const options: unknown = {}

    if (factoryId) options.factoryId = factoryId
    if (userType) options.userType = userType
    if (startDate) options.startDate = new Date(startDate)
    if (endDate) options.endDate = new Date(endDate)

    // 获取登录统计
    const stats = await db.getLoginStats(options)

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('❌ 获取登录统计失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: '获取登录统计失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
