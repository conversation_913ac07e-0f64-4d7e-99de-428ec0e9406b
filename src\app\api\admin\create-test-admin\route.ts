/**
 * 🇨🇳 风口云平台 - 创建测试管理员账号API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 创建测试管理员账号...')
    
    // 创建测试管理员账号
    const adminData = {
      username: 'admin',
      password: 'admin@yhg2025',
      name: '系统管理员',
      email: '<EMAIL>',
      role: 'admin' as const
    }

    const admin = await db.createAdmin(adminData)
    
    if (admin) {
      console.log('✅ 测试管理员账号创建成功:', admin.username)
      return NextResponse.json({
        success: true,
        message: '测试管理员账号创建成功',
        admin: {
          id: admin.id,
          username: admin.username,
          name: admin.name,
          email: admin.email
        }
      })
    } else {
      return NextResponse.json({
        success: false,
        message: '管理员账号可能已存在'
      })
    }
    
  } catch (error) {
    console.error('❌ 创建测试管理员账号失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: '创建失败',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}
