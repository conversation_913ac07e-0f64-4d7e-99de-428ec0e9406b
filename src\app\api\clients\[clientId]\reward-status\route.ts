import { NextRequest, NextResponse } from 'next/server'
import { calculateProportionalReward } from '@/lib/utils/reward-calculator'
import { getUserFromRequest } from '@/lib/auth/jwt'

// 获取认证用户信息 - 使用JWT认证，不依赖会话
async function getAuthenticatedUser(request: NextRequest) {
  try {
    // 直接使用JWT认证，不依赖会话验证
    const authResult = getUserFromRequest(request)
    if (authResult.success && authResult.user) {
      const user = authResult.user
      return {
        success: true,
        user: {
          id: user.userId,
          userType: user.userType,
          factoryId: user.factoryId,
          username: user.username,
          name: user.name
        }
      }
    }
  } catch (error) {
    console.log('JWT认证失败:', error)
  }

  return {
    success: false,
    error: '认证失败'
  }
}

/**
 * 处理 CORS 预检请求
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

/**
 * 获取客户的推荐奖励状态
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { clientId: string } }
) {
  try {
    const { clientId } = params
    const { searchParams } = new URL(request.url)
    const isPublic = searchParams.get('public') === 'true'

    console.log('🔍 获取客户推荐奖励状态:', { clientId, isPublic })

    if (!clientId) {
      return NextResponse.json({
        success: false,
        error: '客户ID不能为空'
      }, { status: 400 })
    }

    let factoryId: string | null = null
    let user: any = null

    if (isPublic) {
      // 🆕 公开访问模式：从查询参数获取工厂ID
      factoryId = searchParams.get('factoryId')
      console.log('🌐 公开访问模式，工厂ID:', factoryId)
    } else {
      // 🔒 认证访问模式
      const authResult = await getAuthenticatedUser(request)
      if (!authResult.success) {
        return NextResponse.json({
          success: false,
          error: '认证失败，请重新登录'
        }, { status: 401 })
      }

      user = authResult.user
      factoryId = searchParams.get('factoryId') || user?.factoryId
      console.log('🔒 认证访问模式，用户类型:', user?.userType, '工厂ID:', factoryId)
    }

    if (!factoryId) {
      return NextResponse.json({
        success: false,
        error: '无法获取工厂信息'
      }, { status: 400 })
    }

    // 计算推荐奖励状态
    const rewardStatus = await calculateProportionalReward(clientId, factoryId)

    console.log('💰 推荐奖励状态计算完成:', {
      clientId,
      totalReward: rewardStatus.totalReward,
      availableReward: rewardStatus.availableReward,
      pendingReward: rewardStatus.pendingReward,
      settledClients: rewardStatus.settledClients,
      totalClients: rewardStatus.totalClients
    })

    return NextResponse.json({
      success: true,
      data: rewardStatus
    })

  } catch (error) {
    console.error('❌ 获取客户推荐奖励状态失败:', error)
    return NextResponse.json({
      success: false,
      error: '获取推荐奖励状态失败'
    }, { status: 500 })
  }
}
