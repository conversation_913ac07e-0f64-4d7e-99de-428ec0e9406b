/**
 * 🇨🇳 风口云平台 - 订单状态API
 *
 * 处理订单状态更新
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminOrFactoryAuth } from '@/lib/middleware/auth'

// 🔧 添加GET方法用于测试API连通性
export async function GET() {
  return NextResponse.json({
    success: true,
    message: '订单状态API正常运行',
    timestamp: new Date().toISOString()
  })
}

// 🔧 修复：添加身份验证中间件
export const PUT = withAdminOrFactoryAuth(async (request: NextRequest, user) => {
  try {
    console.log('🔄 订单状态更新API开始处理')
    console.log('👤 认证用户信息:', {
      userId: user.userId,
      username: user.username,
      userType: user.userType,
      factoryId: user.factoryId
    })

    const body = await request.json()
    const { orderId, status, notes } = body

    console.log('📞 收到订单状态更新请求:', {
      orderId,
      status,
      notes,
      userType: user.userType,
      userId: user.userId
    })

    // 验证必需参数
    if (!orderId) {
      return NextResponse.json({
        success: false,
        error: '订单ID不能为空'
      }, { status: 400 })
    }

    // 🔧 修复：添加权限验证 - 检查用户是否有权限修改该订单
    const order = await db.getOrderById(orderId)
    if (!order) {
      return NextResponse.json({
        success: false,
        error: '订单不存在'
      }, { status: 404 })
    }

    // 权限检查：只有管理员或订单所属工厂的用户可以修改状态
    if (user.userType === 'factory_user' && user.factoryId !== order.factoryId) {
      return NextResponse.json({
        success: false,
        error: '无权限修改此订单状态'
      }, { status: 403 })
    }

    // 调用数据库服务更新订单状态信息
    const success = await db.updateOrderStatus(orderId, {
      status,
      notes
    })

    if (success) {
      console.log('✅ 订单状态更新成功:', orderId)
      return NextResponse.json({
        success: true,
        message: '订单状态更新成功'
      })
    } else {
      console.error('❌ 订单状态更新失败:', orderId)
      return NextResponse.json({
        success: false,
        error: '订单状态更新失败'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('❌ 订单状态更新API错误:', error)
    console.error('❌ 错误详情:', {
      name: (error as any)?.name,
      message: (error as any)?.message,
      stack: (error as any)?.stack,
      cause: (error as any)?.cause
    })

    return NextResponse.json({
      success: false,
      error: `服务器错误: ${(error as Error).message}`,
      details: process.env.NODE_ENV === 'development' ? {
        name: (error as any)?.name,
        stack: (error as any)?.stack
      } : undefined
    }, { status: 500 })
  }
})
