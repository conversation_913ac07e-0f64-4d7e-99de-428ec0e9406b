'use client'

import React, { useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Upload } from 'lucide-react'

interface SimpleUploadButtonProps {
  onFileSelect: (files: File[]) => void
  accept?: string
  multiple?: boolean
  disabled?: boolean
  children?: React.ReactNode
  className?: string
}

export function SimpleUploadButton({
  onFileSelect,
  accept = '*/*',
  multiple = false,
  disabled = false,
  children = '上传文件',
  className
}: SimpleUploadButtonProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      onFileSelect(Array.from(files))
    }
  }

  return (
    <>
      <Button
        onClick={handleClick}
        disabled={disabled}
        className={className}
      >
        <Upload className="h-4 w-4 mr-2" />
        {children}
      </Button>

      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileChange}
        className="hidden"
      />
    </>
  )
}
