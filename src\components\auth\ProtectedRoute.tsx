/**
 * 🇨🇳 风口云平台 - 路由保护组件
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 保护需要认证的路由
 * - 自动重定向未认证用户
 * - 支持角色权限检查
 */

'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { AuthService } from '@/lib/services/auth.service'
import { useAuthStore } from '@/lib/store/auth'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'admin' | 'owner' | 'manager' | 'operator'
  redirectTo?: string
  allowedRoles?: string[]
}

export function ProtectedRoute({ 
  children, 
  requiredRole, 
  redirectTo = '/login',
  allowedRoles = []
}: ProtectedRouteProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthorized, setIsAuthorized] = useState(false)
  const router = useRouter()
  const { isAuthenticated, role, isTokenExpired } = useAuthStore()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 检查是否已认证
        if (!isAuthenticated || !AuthService.isAuthenticated()) {
          console.log('❌ 用户未认证，重定向到登录页')
          router.push(redirectTo)
          return
        }

        // 检查令牌是否过期
        if (isTokenExpired()) {
          console.log('⏰ 令牌已过期，尝试刷新')
          const refreshSuccess = await AuthService.refreshToken()
          
          if (!refreshSuccess) {
            console.log('❌ 令牌刷新失败，重定向到登录页')
            router.push(redirectTo)
            return
          }
        }

        // 检查角色权限
        if (requiredRole && role !== requiredRole) {
          // 管理员拥有所有权限
          if (role !== 'admin') {
            console.log('❌ 权限不足，当前角色:', role, '需要角色:', requiredRole)
            router.push('/unauthorized')
            return
          }
        }

        // 检查允许的角色列表
        if (allowedRoles.length > 0 && !allowedRoles.includes(role || '')) {
          // 管理员拥有所有权限
          if (role !== 'admin') {
            console.log('❌ 角色不在允许列表中，当前角色:', role, '允许角色:', allowedRoles)
            router.push('/unauthorized')
            return
          }
        }

        setIsAuthorized(true)
      } catch (error) {
        console.error('❌ 认证检查失败:', error)
        router.push(redirectTo)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [isAuthenticated, role, requiredRole, allowedRoles, redirectTo, router, isTokenExpired])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">验证身份中...</p>
        </div>
      </div>
    )
  }

  if (!isAuthorized) {
    return null // 重定向中，不显示任何内容
  }

  return <>{children}</>
}

// 管理员路由保护
export function AdminProtectedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute 
      requiredRole="admin" 
      redirectTo="/admin/login"
    >
      {children}
    </ProtectedRoute>
  )
}

// 工厂用户路由保护
export function FactoryProtectedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute
      allowedRoles={['owner', 'manager', 'employee']}
      redirectTo="/factory/login"
    >
      {children}
    </ProtectedRoute>
  )
}

// 工厂管理员路由保护
export function FactoryManagerProtectedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute 
      allowedRoles={['owner', 'manager']}
      redirectTo="/factory/login"
    >
      {children}
    </ProtectedRoute>
  )
}

// 工厂所有者路由保护
export function FactoryOwnerProtectedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute 
      requiredRole="owner"
      redirectTo="/factory/login"
    >
      {children}
    </ProtectedRoute>
  )
}
