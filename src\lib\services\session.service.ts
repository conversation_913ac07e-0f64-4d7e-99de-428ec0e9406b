/**
 * 🇨🇳 风口云平台 - 会话管理服务
 * 
 * 功能说明：
 * - 单点登录检查
 * - 会话状态监控
 * - 自动登出处理
 */

import { useAuthStore } from '@/lib/store/auth'
import { api } from '@/lib/api/client'

export class SessionService {
  private static checkInterval: NodeJS.Timeout | null = null
  private static isChecking = false

  /**
   * 开始会话监控（已简化，仅保留接口兼容性）
   */
  static startSessionMonitoring(): void {
    console.log('✅ 会话监控已简化，仅在API请求时验证会话')
    // 不再启动定期检查，只在API请求时验证
  }

  /**
   * 停止会话监控
   */
  static stopSessionMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
      console.log('⏹️ 会话监控已停止')
    }
  }

  /**
   * 检查会话状态
   */
  static async checkSessionStatus(): Promise<boolean> {
    // 避免并发检查
    if (this.isChecking) {
      return true
    }

    this.isChecking = true

    try {
      const { isAuthenticated } = useAuthStore.getState()
      
      // 如果用户未登录，不需要检查
      if (!isAuthenticated) {
        return true
      }

      console.log('🔍 检查会话状态...')

      const response = await api.get('/api/auth/session')

      if (response.success) {
        console.log('✅ 会话状态正常')
        return true
      } else {
        console.log('❌ 会话状态异常:', response.error)
        
        // 如果需要登出
        if ((response as unknown).shouldLogout) {
          await this.handleSessionExpired(response.error || '会话已过期')
        }
        
        return false
      }

    } catch (error) {
      console.error('❌ 会话状态检查失败:', error)
      
      // 网络错误等情况，不强制登出
      if ((error as unknown).status === 401) {
        await this.handleSessionExpired('您的账号在其他地方登录，当前会话已失效')
      }
      
      return false
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 处理会话过期
   */
  static async handleSessionExpired(message: string): Promise<void> {
    console.log('🚪 处理会话过期:', message)

    // 停止监控
    this.stopSessionMonitoring()

    // 清除本地状态
    useAuthStore.getState().logout()

    // 显示提示信息
    this.showSessionExpiredNotification(message)

    // 跳转到登录页面
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname
      
      if (currentPath.startsWith('/admin')) {
        window.location.href = '/admin/login'
      } else if (currentPath.startsWith('/factory')) {
        window.location.href = '/factory/login'
      } else {
        window.location.href = '/'
      }
    }
  }

  /**
   * 显示会话过期通知
   */
  static showSessionExpiredNotification(message: string): void {
    if (typeof window !== 'undefined') {
      // 动态导入通知管理器
      import('@/components/ui/session-notification').then(({ SessionNotificationManager }) => {
        SessionNotificationManager.show(message, 'error')
      }).catch(() => {
        // 如果导入失败，使用简单的alert
        alert(`会话已失效：${message}`)
      })
    }
  }

  /**
   * 手动登出
   */
  static async logout(): Promise<void> {
    try {
      console.log('🚪 用户手动登出')

      // 调用登出API
      await api.delete('/api/auth/session')

      // 停止监控
      this.stopSessionMonitoring()

      // 清除本地状态
      useAuthStore.getState().logout()

      console.log('✅ 登出成功')

    } catch (error) {
      console.error('❌ 登出失败:', error)
      
      // 即使API调用失败，也要清除本地状态
      this.stopSessionMonitoring()
      useAuthStore.getState().logout()
    }
  }

  /**
   * 用户登录后启动监控
   */
  static onUserLogin(): void {
    console.log('👤 用户登录，启动会话监控')
    this.startSessionMonitoring()
  }

  /**
   * 用户登出后停止监控
   */
  static onUserLogout(): void {
    console.log('👤 用户登出，停止会话监控')
    this.stopSessionMonitoring()
  }
}

// 导出单例
export const sessionService = SessionService
