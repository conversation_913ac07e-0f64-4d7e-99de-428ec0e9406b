/**
 * 🇨🇳 风口云平台 - 员工状态切换API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// 切换员工状态
export async function PUT(request: NextRequest) {
  try {
    const { id } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: '员工ID不能为空' },
        { status: 400 }
      )
    }

    // 切换员工状态
    const employee = await db.toggleEmployeeStatus(id)

    if (!employee) {
      return NextResponse.json(
        { error: '切换员工状态失败' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      employee
    })

  } catch (error) {
    console.error('❌ 切换员工状态失败:', error)
    return NextResponse.json(
      { error: '切换员工状态失败' },
      { status: 500 }
    )
  }
}
