/**
 * 🇨🇳 风口云平台 - 会话管理中间件
 * 
 * 功能说明：
 * - 单点登录验证
 * - 会话有效性检查
 * - 防止多地登录
 */

import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest } from '@/lib/auth/jwt'
import { db } from '@/lib/database'

/**
 * 验证会话有效性的中间件
 */
export async function validateSession(request: NextRequest): Promise<{
  isValid: boolean
  user?: unknown
  error?: string
  shouldLogout?: boolean
}> {
  try {
    // 获取用户信息
    const authResult = getUserFromRequest(request)
    
    if (!authResult.success || !authResult.user) {
      return {
        isValid: false,
        error: authResult.error || '认证失败'
      }
    }

    const user = authResult.user

    // 🔧 新增：检查会话是否被踢出（单点登录）
    if (user.sessionId) {
      console.log('🔍 检查会话状态:', user.sessionId)

      const { SingleSessionService } = await import('@/lib/services/single-session.service')
      const isSessionValid = await SingleSessionService.validateSession(user.sessionId)

      if (!isSessionValid) {
        console.log('❌ 会话已失效（被踢出或过期）:', user.sessionId)
        return {
          isValid: false,
          error: '会话已失效，请重新登录',
          shouldLogout: true
        }
      }

      console.log('✅ 会话状态正常:', user.sessionId)
    }

    console.log('✅ 会话验证通过:', user.username)
    return {
      isValid: true,
      user
    }

  } catch (error) {
    console.error('❌ 会话验证失败:', error)
    return {
      isValid: false,
      error: '会话验证异常'
    }
  }
}

/**
 * 创建会话验证错误响应
 */
export function createSessionErrorResponse(
  message: string, 
  shouldLogout: boolean = false
): NextResponse {
  return NextResponse.json(
    {
      error: '会话验证失败',
      message,
      shouldLogout,
      timestamp: new Date().toISOString()
    },
    { status: 401 }
  )
}

/**
 * API路由会话验证装饰器
 */
export function withSessionValidation(
  handler: (request: NextRequest, validatedUser: unknown) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const sessionResult = await validateSession(request)
    
    if (!sessionResult.isValid) {
      return createSessionErrorResponse(
        sessionResult.error || '会话无效',
        sessionResult.shouldLogout
      )
    }

    // 会话有效，继续处理请求
    return handler(request, sessionResult.user)
  }
}

/**
 * 获取请求的IP地址和用户代理信息
 */
export function getRequestInfo(request: NextRequest): {
  ipAddress: string
  userAgent: string
  deviceInfo: string
} {
  // 获取真实IP地址（考虑代理和负载均衡）
  const ipAddress = 
    request.headers.get('x-forwarded-for')?.split(',')[0] ||
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') ||
    (request as unknown).ip ||
    '127.0.0.1'

  // 获取用户代理
  const userAgent = request.headers.get('user-agent') || 'Unknown'

  // 简单的设备信息解析
  let deviceInfo = 'Unknown Device'
  if (userAgent.includes('Mobile')) {
    deviceInfo = 'Mobile Device'
  } else if (userAgent.includes('Tablet')) {
    deviceInfo = 'Tablet Device'
  } else if (userAgent.includes('Windows')) {
    deviceInfo = 'Windows PC'
  } else if (userAgent.includes('Mac')) {
    deviceInfo = 'Mac Computer'
  } else if (userAgent.includes('Linux')) {
    deviceInfo = 'Linux Computer'
  }

  return {
    ipAddress,
    userAgent,
    deviceInfo
  }
}

/**
 * 记录登录信息的辅助函数
 */
export async function recordLoginWithRequestInfo(
  request: NextRequest,
  loginData: {
    userId: string
    userType: 'admin' | 'factory_user'
    username: string
    userName: string
    factoryId?: string
    factoryName?: string
    loginStatus: 'success' | 'failed'
    failReason?: string
  }
): Promise<unknown> {
  const requestInfo = getRequestInfo(request)
  
  return await db.recordLogin({
    ...loginData,
    ipAddress: requestInfo.ipAddress,
    userAgent: requestInfo.userAgent,
    deviceInfo: requestInfo.deviceInfo
  })
}
