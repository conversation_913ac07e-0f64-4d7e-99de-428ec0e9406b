/**
 * 🇨🇳 风口云平台 - 订单服务
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 订单数据管理
 * - 替代localStorage操作
 * - 统一的订单API调用
 */

import { api } from '@/lib/api/client'
import type { Order } from '@/types'
import { AuthService } from './auth.service'

// 订单查询参数
export interface OrderQueryParams {
  factoryId?: string
  clientId?: string
  status?: string
  startDate?: string
  endDate?: string
  limit?: number
  offset?: number
}

// 创建订单请求
export interface CreateOrderRequest {
  factoryId: string
  clientId: string
  clientName?: string
  clientPhone?: string
  projectAddress?: string
  items: unknown[]
  totalAmount: number
  status?: string
  notes?: string
}

/**
 * 订单服务类
 */
export class OrderService {
  /**
   * 获取工厂订单列表
   */
  static async getFactoryOrders(factoryId: string): Promise<Order[]> {
    try {
      const response = await api.get(`/api/orders?factoryId=${factoryId}`)
      
      if (response.success && response.data) {
        return response.data.orders || []
      }

      console.error('❌ 获取工厂订单失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 获取工厂订单异常:', error)
      return []
    }
  }

  /**
   * 获取客户订单列表
   */
  static async getClientOrders(clientId: string): Promise<Order[]> {
    try {
      const response = await api.get(`/api/orders?clientId=${clientId}`)
      
      if (response.success && response.data) {
        return response.data.orders || []
      }

      console.error('❌ 获取客户订单失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 获取客户订单异常:', error)
      return []
    }
  }

  /**
   * 获取当前用户工厂的订单
   */
  static async getCurrentFactoryOrders(): Promise<Order[]> {
    const factoryId = AuthService.getCurrentFactoryId()
    
    if (!factoryId) {
      console.error('❌ 无法获取当前工厂ID')
      return []
    }

    return this.getFactoryOrders(factoryId)
  }

  /**
   * 根据ID获取订单详情
   */
  static async getOrderById(orderId: string): Promise<Order | null> {
    try {
      const response = await api.get(`/api/orders/${orderId}`)
      
      if (response.success && response.data) {
        return response.data.order || null
      }

      console.error('❌ 获取订单详情失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 获取订单详情异常:', error)
      return null
    }
  }

  /**
   * 创建订单
   */
  static async createOrder(orderData: CreateOrderRequest): Promise<Order | null> {
    try {
      // 确保有工厂ID
      if (!orderData.factoryId) {
        const factoryId = AuthService.getCurrentFactoryId()
        if (!factoryId) {
          console.error('❌ 无法获取工厂ID')
          return null
        }
        orderData.factoryId = factoryId
      }

      const response = await api.post('/api/orders', orderData)
      
      if (response.success && response.data) {
        console.log('✅ 订单创建成功:', response.data.order?.id)
        return response.data.order || null
      }

      console.error('❌ 创建订单失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 创建订单异常:', error)
      return null
    }
  }

  /**
   * 更新订单
   */
  static async updateOrder(orderId: string, updates: Partial<Order>): Promise<Order | null> {
    try {
      const response = await api.put(`/api/orders/${orderId}`, updates)
      
      if (response.success && response.data) {
        console.log('✅ 订单更新成功:', orderId)
        return response.data.order || null
      }

      console.error('❌ 更新订单失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 更新订单异常:', error)
      return null
    }
  }

  /**
   * 删除订单
   */
  static async deleteOrder(orderId: string): Promise<boolean> {
    try {
      const response = await api.delete(`/api/orders/${orderId}`)
      
      if (response.success) {
        console.log('✅ 订单删除成功:', orderId)
        return true
      }

      console.error('❌ 删除订单失败:', response.error)
      return false
    } catch (error) {
      console.error('❌ 删除订单异常:', error)
      return false
    }
  }

  /**
   * 搜索订单
   */
  static async searchOrders(params: OrderQueryParams): Promise<Order[]> {
    try {
      const queryString = new URLSearchParams()
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryString.append(key, String(value))
        }
      })

      const response = await api.get(`/api/orders/search?${queryString.toString()}`)
      
      if (response.success && response.data) {
        return response.data.orders || []
      }

      console.error('❌ 搜索订单失败:', response.error)
      return []
    } catch (error) {
      console.error('❌ 搜索订单异常:', error)
      return []
    }
  }

  /**
   * 获取订单统计信息
   */
  static async getOrderStats(factoryId?: string): Promise<unknown> {
    try {
      const targetFactoryId = factoryId || AuthService.getCurrentFactoryId()
      
      if (!targetFactoryId) {
        console.error('❌ 无法获取工厂ID')
        return null
      }

      const response = await api.get(`/api/orders/stats?factoryId=${targetFactoryId}`)
      
      if (response.success && response.data) {
        return response.data.stats || null
      }

      console.error('❌ 获取订单统计失败:', response.error)
      return null
    } catch (error) {
      console.error('❌ 获取订单统计异常:', error)
      return null
    }
  }

  /**
   * 批量操作订单
   */
  static async batchUpdateOrders(orderIds: string[], updates: Partial<Order>): Promise<boolean> {
    try {
      const response = await api.patch('/api/orders/batch', {
        orderIds,
        updates
      })
      
      if (response.success) {
        console.log('✅ 批量更新订单成功:', orderIds.length)
        return true
      }

      console.error('❌ 批量更新订单失败:', response.error)
      return false
    } catch (error) {
      console.error('❌ 批量更新订单异常:', error)
      return false
    }
  }
}

export default OrderService
