# 🚀 阿里云服务器连接和部署操作指南

## 📋 目录
1. [连接阿里云服务器](#1-连接阿里云服务器)
2. [检查服务器环境](#2-检查服务器环境)
3. [获取最新代码](#3-获取最新代码)
4. [配置数据库](#4-配置数据库)
5. [安装依赖和构建](#5-安装依赖和构建)
6. [启动生产服务](#6-启动生产服务)
7. [验证部署](#7-验证部署)
8. [常用维护命令](#8-常用维护命令)

---

## 1. 连接阿里云服务器

### 1.1 通过SSH连接
```bash
# 方式1：使用IP地址连接
ssh root@您的服务器IP

# 方式2：使用域名连接（如果已配置）
ssh <EMAIL>

# 如果使用密钥文件
ssh -i /path/to/your/key.pem root@服务器IP
```

### 1.2 连接成功标志
```
Welcome to Alibaba Cloud Elastic Compute Service !
Last login: Thu Jun 26 11:40:25 2025 from xxx.xxx.xxx.xxx
[root@iZf8zbfdp7cq0plrmwtxtpZ ~]#
```

---

## 2. 检查服务器环境

### 2.1 系统资源检查
```bash
# 查看内存使用情况
free -h

# 查看磁盘空间
df -h

# 查看CPU信息
lscpu

# 查看系统版本
cat /etc/os-release
```

### 2.2 检查已安装服务
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 检查PM2状态
pm2 list

# 检查数据库服务
systemctl list-units --type=service | grep -E "(mysql|mariadb|postgres)"
```

---

## 3. 获取最新代码

### 3.1 进入项目目录
```bash
# 进入项目根目录
cd /opt/factorysystem

# 查看当前目录内容
ls -la
```

### 3.2 更新代码
```bash
# 查看当前Git状态
git status

# 暂存本地更改（如果有）
git stash push -m "保存生产环境本地修改-$(date +%Y%m%d-%H%M%S)"

# 拉取最新代码
git pull origin master

# 查看更新日志
git log --oneline -5
```

---

## 4. 配置数据库

### 4.1 检查数据库服务
```bash
# 检查PostgreSQL服务状态
systemctl status postgresql

# 查看数据库进程
ps aux | grep postgres

# 检查数据库端口
netstat -tlnp | grep 5432
```

### 4.2 配置数据库连接
```bash
# 修改Prisma schema使用PostgreSQL
sed -i 's/provider = "mysql"/provider = "postgresql"/' prisma/schema.prisma

# 验证修改
head -15 prisma/schema.prisma
```

### 4.3 连接数据库
```bash
# 修改PostgreSQL认证配置（如果需要）
sudo cp /var/lib/pgsql/data/pg_hba.conf /var/lib/pgsql/data/pg_hba.conf.backup
sudo sed -i 's/ident/trust/g' /var/lib/pgsql/data/pg_hba.conf
sudo sed -i 's/md5/trust/g' /var/lib/pgsql/data/pg_hba.conf

# 重启PostgreSQL
sudo systemctl restart postgresql

# 连接数据库
sudo -u postgres psql
```

### 4.4 创建数据库和用户（在psql中执行）
```sql
-- 查看现有数据库
\l

-- 创建数据库（如果不存在）
CREATE DATABASE factorysystem;

-- 创建用户（如果不存在）
CREATE USER factorysystem WITH PASSWORD 'FactorySystem2024!';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE factorysystem TO factorysystem;

-- 退出
\q
```

---

## 5. 安装依赖和构建

### 5.1 创建交换空间（如果内存不足）
```bash
# 创建2GB交换文件
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 验证交换空间
free -h
```

### 5.2 安装依赖
```bash
# 清理旧依赖
rm -rf node_modules

# 清理npm缓存
npm cache clean --force

# 安装依赖
npm install

# 验证安装
ls -la node_modules/ | head -5
```

### 5.3 配置环境变量
```bash
# 创建生产环境配置
cat > .env.production << 'EOF'
# PostgreSQL 数据库配置
DATABASE_URL="postgresql://factorysystem:FactorySystem2024!@localhost:5432/factorysystem"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-change-in-production-min-32-chars"
JWT_EXPIRES_IN="7d"
REFRESH_TOKEN_EXPIRES_IN="30d"

# API配置
NEXT_PUBLIC_API_BASE_URL="https://fengkouyun.cn"
INTERNAL_API_KEY="your-internal-api-key-for-service-communication"

# 应用配置
NEXT_PUBLIC_APP_NAME="风口云平台"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NODE_ENV=production

# 安全配置
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-key"
EOF

# 复制配置文件
cp .env.production .env
```

### 5.4 数据库迁移
```bash
# 生成Prisma客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy

# 或推送数据库结构（开发环境）
# npx prisma db push
```

### 5.5 构建应用
```bash
# 构建生产版本
npm run build

# 检查构建结果
ls -la .next/
```

---

## 6. 启动生产服务

### 6.1 使用PM2启动（推荐）
```bash
# 停止现有进程
pm2 stop factorysystem || true
pm2 delete factorysystem || true

# 启动生产服务
pm2 start npm --name "factorysystem" -- start

# 设置开机自启
pm2 startup
pm2 save

# 查看服务状态
pm2 status
pm2 logs factorysystem
```

### 6.2 直接启动（备选方案）
```bash
# 设置环境变量并启动
NODE_ENV=production npm start
```

---

## 7. 验证部署

### 7.1 检查服务状态
```bash
# 查看PM2状态
pm2 status

# 查看应用日志
pm2 logs factorysystem --lines 50

# 检查端口占用
netstat -tlnp | grep :3000

# 测试本地访问
curl http://localhost:3000
```

### 7.2 检查系统资源
```bash
# 查看内存使用
free -h

# 查看磁盘使用
df -h

# 查看系统负载
top
```

---

## 8. 常用维护命令

### 8.1 PM2 管理命令
```bash
# 重启应用
pm2 restart factorysystem

# 重新加载应用（零停机）
pm2 reload factorysystem

# 查看实时日志
pm2 logs factorysystem --follow

# 清理日志
pm2 flush factorysystem

# 查看详细信息
pm2 show factorysystem

# 监控面板
pm2 monit
```

### 8.2 代码更新流程
```bash
# 1. 进入项目目录
cd /opt/factorysystem

# 2. 暂存本地更改
git stash push -m "更新前保存-$(date +%Y%m%d-%H%M%S)"

# 3. 拉取最新代码
git pull origin master

# 4. 安装新依赖（如果package.json有变化）
npm ci --production

# 5. 重新构建
npm run build

# 6. 重启服务
pm2 restart factorysystem

# 7. 验证更新
pm2 logs factorysystem --lines 20
```

### 8.3 数据库维护
```bash
# 连接数据库
sudo -u postgres psql factorysystem

# 备份数据库
sudo -u postgres pg_dump factorysystem > backup_$(date +%Y%m%d_%H%M%S).sql

# 查看数据库大小
sudo -u postgres psql -c "SELECT pg_size_pretty(pg_database_size('factorysystem'));"
```

### 8.4 系统维护
```bash
# 清理系统缓存
sync && echo 3 > /proc/sys/vm/drop_caches

# 查看系统日志
journalctl -u postgresql --since "1 hour ago"

# 更新系统安全补丁
dnf upgrade-minimal --security

# 重启系统（谨慎使用）
# reboot
```

---

## 🚨 故障排除

### 常见问题和解决方案

1. **内存不足**：创建交换空间
2. **端口被占用**：`lsof -i :3000` 查看占用进程
3. **数据库连接失败**：检查DATABASE_URL配置
4. **权限问题**：`chown -R root:root /opt/factorysystem`
5. **PM2进程异常**：`pm2 delete all && pm2 start npm --name factorysystem -- start`

### 紧急联系信息
- 服务器IP：您的服务器IP
- 域名：fengkouyun.cn
- 项目路径：/opt/factorysystem
- 数据库：PostgreSQL (端口5432)
- 应用端口：3000

---

**注意事项：**
- 所有操作前请先备份重要数据
- 生产环境操作需谨慎，建议先在测试环境验证
- 定期检查系统资源使用情况
- 保持代码和系统的定期更新
