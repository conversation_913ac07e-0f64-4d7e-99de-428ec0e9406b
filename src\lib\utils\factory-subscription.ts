/**
 * 🇨🇳 风口云平台 - 工厂订阅管理工具
 * 处理工厂使用时间、订阅状态等功能
 */

import { Factory } from '@/types'

// 订阅类型配置
export const SUBSCRIPTION_CONFIGS = {
  trial: { name: '试用期', duration: 30, unit: 'days' },
  monthly: { name: '按月', duration: 1, unit: 'months' },
  quarterly: { name: '按季度', duration: 3, unit: 'months' },
  yearly: { name: '按年', duration: 12, unit: 'months' },
  permanent: { name: '永久', duration: 0, unit: 'permanent' }
} as const

export type SubscriptionType = keyof typeof SUBSCRIPTION_CONFIGS

/**
 * 计算订阅结束时间（统一使用固定天数避免月份差异）
 */
export function calculateSubscriptionEnd(
  startDate: Date,
  subscriptionType: SubscriptionType
): Date | null {
  if (subscriptionType === 'permanent') {
    return null // 永久订阅没有结束时间
  }

  const start = new Date(startDate)
  let daysToAdd: number

  // 🔧 统一使用固定天数计算，避免月份天数差异
  switch (subscriptionType) {
    case 'trial':
      daysToAdd = 30
      break
    case 'monthly':
      daysToAdd = 30
      break
    case 'quarterly':
      daysToAdd = 90
      break
    case 'yearly':
      daysToAdd = 365
      break
    default:
      daysToAdd = 30
      break
  }

  return new Date(start.getTime() + daysToAdd * 24 * 60 * 60 * 1000)
}

/**
 * 检查工厂是否已过期（考虑暂停时间）
 */
export function isFactoryExpired(factory: Factory): boolean {
  if (!factory) return false

  if (factory.isPermanent || factory.subscriptionType === 'permanent') {
    return false
  }

  if (!factory.subscriptionEnd) {
    return false
  }

  // 🔧 考虑暂停时间的影响
  const now = new Date()
  const endDate = new Date(factory.subscriptionEnd)

  // 计算暂停时间累计，调整实际结束时间
  const totalSuspendedTime = calculateTotalSuspendedTime(factory)
  const adjustedEndDate = new Date(endDate.getTime() + totalSuspendedTime)

  return now > adjustedEndDate
}

/**
 * 检查工厂是否即将过期（7天内，考虑暂停时间）
 */
export function isFactoryExpiringSoon(factory: Factory): boolean {
  if (!factory) return false

  if (factory.isPermanent || factory.subscriptionType === 'permanent') {
    return false
  }

  if (!factory.subscriptionEnd) {
    return false
  }

  // 🔧 考虑暂停时间的影响
  const now = new Date()
  const endDate = new Date(factory.subscriptionEnd)

  // 计算暂停时间累计，调整实际结束时间
  const totalSuspendedTime = calculateTotalSuspendedTime(factory)
  const adjustedEndDate = new Date(endDate.getTime() + totalSuspendedTime)

  const daysUntilExpiry = Math.ceil((adjustedEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  return daysUntilExpiry <= 7 && daysUntilExpiry > 0
}

/**
 * 获取剩余天数（考虑暂停时间）
 */
export function getRemainingDays(factory: Factory): number | null {
  if (!factory) return null

  if (factory.isPermanent || factory.subscriptionType === 'permanent') {
    return null // 永久订阅
  }

  // 🔧 修复：如果还没有首次登录，但有订阅结束时间，说明是预设的订阅
  if (!factory.firstLoginAt) {
    // 如果有订阅结束时间，按照结束时间计算
    if (factory.subscriptionEnd) {
      const now = new Date()
      const endDate = new Date(factory.subscriptionEnd)
      const diffTime = endDate.getTime() - now.getTime()
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    }

    // 如果没有订阅结束时间，返回订阅类型对应的总天数
    const config = SUBSCRIPTION_CONFIGS[factory.subscriptionType]
    if (config.unit === 'days') {
      return config.duration
    } else if (config.unit === 'months') {
      return config.duration * 30 // 简化计算，每月按30天
    }
    return 30 // 默认30天
  }

  // 🔧 修复：如果没有设置订阅结束时间，但已经登录，说明需要设置订阅
  if (!factory.subscriptionEnd) {
    // 对于试用期用户，如果已经登录但没有设置结束时间，返回0表示需要设置订阅
    if (factory.subscriptionType === 'trial') {
      return 0 // 试用期已过期，需要设置正式订阅
    }
    return null // 其他类型返回null
  }

  const now = new Date()
  const endDate = new Date(factory.subscriptionEnd)

  // 计算暂停时间累计
  const totalSuspendedTime = calculateTotalSuspendedTime(factory)

  // 实际结束时间 = 原结束时间 + 暂停时间累计
  const adjustedEndDate = new Date(endDate.getTime() + totalSuspendedTime)

  const remainingMs = adjustedEndDate.getTime() - now.getTime()

  if (remainingMs <= 0) {
    return 0 // 已过期
  }

  return Math.ceil(remainingMs / (1000 * 60 * 60 * 24))
}

/**
 * 计算总暂停时间（毫秒）- 优化版，避免重复累计
 */
export function calculateTotalSuspendedTime(factory: Factory): number {
  if (!factory) return 0

  // 🔧 从数据库获取已累计的暂停时间（历史暂停时间）
  let totalSuspendedMs = Number(factory.totalSuspendedMs || 0)

  // 🔧 只有当前正在暂停中时，才加上当前暂停的时间
  // 这样避免了重复累计已经保存在数据库中的暂停时间
  if (factory.status === 'suspended' && factory.suspendedAt) {
    const suspendedAt = new Date(factory.suspendedAt)
    const now = new Date()
    const currentSuspendedMs = now.getTime() - suspendedAt.getTime()

    // 确保当前暂停时间为正数
    if (currentSuspendedMs > 0) {
      totalSuspendedMs += currentSuspendedMs
    }
  }

  return totalSuspendedMs
}

/**
 * 获取订阅状态文本
 */
export function getSubscriptionStatusText(factory: Factory): {
  status: string
  color: string
  description: string
} {
  // 安全检查
  if (!factory) {
    return {
      status: '未知',
      color: 'text-gray-600',
      description: '工厂信息不完整'
    }
  }

  if (factory.status === 'suspended') {
    return {
      status: '已暂停',
      color: 'text-orange-600',
      description: factory.suspendedReason || '工厂已被暂停使用'
    }
  }

  if (factory.isPermanent || factory.subscriptionType === 'permanent') {
    return {
      status: '永久授权',
      color: 'text-green-600',
      description: '拥有永久使用权限'
    }
  }

  if (isFactoryExpired(factory)) {
    return {
      status: '已过期',
      color: 'text-red-600',
      description: '订阅已过期，请联系管理员续费'
    }
  }

  if (isFactoryExpiringSoon(factory)) {
    const days = getRemainingDays(factory)
    return {
      status: '即将过期',
      color: 'text-yellow-600',
      description: `还有 ${days} 天到期`
    }
  }

  const days = getRemainingDays(factory)
  const subscriptionType = factory.subscriptionType || 'trial'
  const config = SUBSCRIPTION_CONFIGS[subscriptionType]

  if (!config) {
    return {
      status: '配置错误',
      color: 'text-red-600',
      description: '订阅类型配置错误'
    }
  }

  // 🔧 修复：处理没有设置订阅结束时间的情况
  if (days === null) {
    return {
      status: '未设置',
      color: 'text-gray-600',
      description: '需要设置订阅时间'
    }
  }

  if (days === 0) {
    return {
      status: '已过期',
      color: 'text-red-600',
      description: '订阅已过期，请联系管理员续费'
    }
  }

  return {
    status: '正常',
    color: 'text-green-600',
    description: `${config.name}订阅，还有 ${days} 天`
  }
}

/**
 * 检查工厂是否可以使用
 */
export function canFactoryOperate(factory: Factory): boolean {
  if (!factory) return false

  // 被暂停的工厂不能使用
  if (factory.status === 'suspended') {
    return false
  }

  // 永久授权可以使用
  if (factory.isPermanent || factory.subscriptionType === 'permanent') {
    return true
  }

  // 检查是否过期
  return !isFactoryExpired(factory)
}

/**
 * 格式化订阅类型显示
 */
export function formatSubscriptionType(type: SubscriptionType): string {
  return SUBSCRIPTION_CONFIGS[type].name
}

/**
 * 创建订阅配置
 */
export function createSubscriptionConfig(
  type: SubscriptionType,
  startDate?: Date
): {
  subscriptionType: SubscriptionType
  subscriptionStart: Date
  subscriptionEnd: Date | null
  isPermanent: boolean
} {
  const start = startDate || new Date()
  const end = calculateSubscriptionEnd(start, type)
  
  return {
    subscriptionType: type,
    subscriptionStart: start,
    subscriptionEnd: end,
    isPermanent: type === 'permanent'
  }
}
