/**
 * 🇨🇳 风口云平台 - 订单录单员姓名数据迁移API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 开始执行订单录单员姓名数据迁移...')

    // 执行数据迁移
    const success = await db.migrateOrderCreatedByNames()

    if (success) {
      return NextResponse.json({
        success: true,
        message: '订单录单员姓名数据迁移完成'
      })
    } else {
      return NextResponse.json(
        { error: '数据迁移失败' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ 数据迁移API失败:', error)
    return NextResponse.json(
      { error: '数据迁移服务异常' },
      { status: 500 }
    )
  }
}
