-- CreateTable
CREATE TABLE "login_records" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "user_id" TEXT NOT NULL,
    "user_type" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "user_name" TEXT NOT NULL,
    "factory_id" TEXT,
    "factory_name" TEXT,
    "login_time" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "device_info" TEXT,
    "location" TEXT,
    "login_status" TEXT NOT NULL,
    "fail_reason" TEXT,
    "session_id" TEXT,
    "logout_time" DATETIME,
    "session_duration" INTEGER,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
