/**
 * 测试工厂登录API - 使用真实数据库
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 测试工厂登录API开始')

    const { username, password } = await request.json()
    console.log('📝 接收到登录请求:', { username, password: '***' })

    if (!username || !password) {
      return NextResponse.json(
        { error: '用户名和密码不能为空' },
        { status: 400 }
      )
    }

    // 使用真实的数据库认证
    console.log('🔐 开始数据库认证...')
    const user = await db.authenticateFactoryUser(username, password, {
      ipAddress: '127.0.0.1',
      userAgent: 'test',
      deviceInfo: 'test'
    })

    console.log('✅ 认证结果:', user ? '成功' : '失败')

    if (!user) {
      return NextResponse.json(
        { error: '用户名或密码错误' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      success: true,
      message: '登录成功',
      user: {
        username: user.username,
        name: user.name,
        factoryName: user.factory?.name
      }
    })

  } catch (error) {
    console.error('❌ 测试工厂登录API错误:', error)
    const errorMessage = error instanceof Error ? error.message : '认证服务异常'

    return NextResponse.json(
      {
        error: errorMessage,
        details: error instanceof Error ? error.stack : '未知错误'
      },
      { status: 500 }
    )
  }
}
