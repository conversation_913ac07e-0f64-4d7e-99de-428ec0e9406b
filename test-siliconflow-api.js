/**
 * 硅基流动 API 测试脚本
 * 使用 Node.js 运行
 */

const https = require('https');

async function testSiliconFlowAPI() {
  console.log('🚀 开始测试硅基流动 DeepSeek V3 API...');
  
  const apiKey = 'sk-szczomdkrprhzlzuzwlblenwfvvuuuyxbxnjgmrcetorftth';
  const testText = `项目：速度测试项目
客户：测试客户

一楼：
出风口 2665×155 白色 1个
出风口 1525×255 白色 1个
回风口 1200×300 白色 1个
出风口 1600×110 白色 1个`;

  const prompt = `识别风口订单，返回JSON。

文本：${testText}

规则：
1. 风口类型（严格按尺寸判断）：
   - 宽度≤254mm → "systemType": "double_white_outlet", "originalType": "出风口"
   - 宽度≥255mm → "systemType": "white_return", "originalType": "回风口"

2. 尺寸：数字<100为厘米需×10，大值为length，小值为width

必须返回完整JSON：
{
  "projects": [{
    "projectName": "",
    "clientInfo": "",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": ""
          }
        ]
      }]
    }]
  }]
}`;

  const requestBody = JSON.stringify({
    model: 'deepseek-ai/DeepSeek-V3',
    messages: [
      {
        role: 'user',
        content: prompt
      }
    ],
    max_tokens: 1500,
    temperature: 0.1,
    stream: false
  });

  const options = {
    hostname: 'api.siliconflow.cn',
    port: 443,
    path: '/v1/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
      'Content-Length': Buffer.byteLength(requestBody)
    }
  };

  const startTime = Date.now();
  console.log('⏰ 开始时间:', new Date().toISOString());
  console.log('📤 请求体大小:', requestBody.length, '字符');

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        console.log('⏱️ 总响应时间:', duration + 'ms', `(${(duration/1000).toFixed(2)}秒)`);
        console.log('📊 响应状态:', res.statusCode);

        if (res.statusCode === 200) {
          try {
            const response = JSON.parse(data);
            const content = response.choices[0]?.message?.content;
            
            console.log('✅ API调用成功！');
            console.log('🤖 使用模型:', response.model);
            console.log('📈 Token使用情况:');
            console.log('   输入Token:', response.usage?.prompt_tokens || 'N/A');
            console.log('   输出Token:', response.usage?.completion_tokens || 'N/A');
            console.log('   总Token:', response.usage?.total_tokens || 'N/A');
            
            if (response.usage) {
              const inputCost = (response.usage.prompt_tokens * 2.00 / 1000000).toFixed(4);
              const outputCost = (response.usage.completion_tokens * 8.00 / 1000000).toFixed(4);
              const totalCost = (parseFloat(inputCost) + parseFloat(outputCost)).toFixed(4);
              console.log('💰 费用估算:');
              console.log('   输入费用: ¥' + inputCost);
              console.log('   输出费用: ¥' + outputCost);
              console.log('   总费用: ¥' + totalCost);
            }
            
            console.log('📝 AI响应内容:');
            console.log(content);
            
            // 尝试解析JSON
            try {
              let jsonStr = content.trim();
              jsonStr = jsonStr.replace(/^```json\s*/i, '');
              jsonStr = jsonStr.replace(/\s*```\s*$/, '');
              
              const firstBrace = jsonStr.indexOf('{');
              const lastBrace = jsonStr.lastIndexOf('}');
              
              if (firstBrace !== -1 && lastBrace !== -1) {
                jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
              }
              
              const parsed = JSON.parse(jsonStr);
              
              console.log('🎯 JSON解析成功！');
              console.log('📋 解析后的数据:');
              console.log(JSON.stringify(parsed, null, 2));
              
              // 验证风口类型识别
              const vents = parsed.projects?.[0]?.floors?.[0]?.rooms?.[0]?.vents || [];
              console.log('🔍 风口类型验证:');
              
              let correctCount = 0;
              vents.forEach((vent, index) => {
                const width = vent.dimensions?.width || 0;
                const systemType = vent.systemType;
                const expectedType = width >= 255 ? 'white_return' : 'double_white_outlet';
                const isCorrect = systemType === expectedType;
                
                if (isCorrect) correctCount++;
                
                console.log(`   风口${index + 1}: ${vent.dimensions?.length}×${width}mm → ${systemType} ${isCorrect ? '✅ 正确' : '❌ 错误'}`);
              });
              
              // 性能评估
              console.log('🚀 性能评估:');
              if (duration < 5000) {
                console.log('   速度: 🟢 极快 (<5秒)');
              } else if (duration < 10000) {
                console.log('   速度: 🟡 较快 (5-10秒)');
              } else if (duration < 30000) {
                console.log('   速度: 🟠 一般 (10-30秒)');
              } else {
                console.log('   速度: 🔴 较慢 (>30秒)');
              }
              
              const accuracy = vents.length > 0 ? (correctCount / vents.length * 100).toFixed(1) : 0;
              console.log(`   准确性: ${correctCount}/${vents.length} (${accuracy}%) ${accuracy == 100 ? '🟢 完全正确' : accuracy >= 75 ? '🟡 基本正确' : '🔴 需要改进'}`);
              
              console.log('\n🎉 测试完成！硅基流动 API 表现:', duration < 10000 && accuracy >= 75 ? '🟢 优秀' : '🟡 良好');
              
              resolve({
                success: true,
                duration: duration,
                data: parsed,
                accuracy: accuracy
              });
              
            } catch (parseError) {
              console.error('❌ JSON解析失败:', parseError.message);
              console.log('原始响应内容:', content);
              resolve({
                success: false,
                error: 'JSON解析失败',
                duration: duration
              });
            }
            
          } catch (error) {
            console.error('❌ 响应解析失败:', error.message);
            resolve({
              success: false,
              error: '响应解析失败',
              duration: duration
            });
          }
        } else {
          console.error('❌ API请求失败');
          console.error('状态码:', res.statusCode);
          console.error('错误信息:', data);
          resolve({
            success: false,
            error: `API请求失败: ${res.statusCode}`,
            duration: duration
          });
        }
      });
    });

    req.on('error', (error) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.error('❌ 网络错误');
      console.error('耗时:', duration + 'ms');
      console.error('错误:', error.message);
      resolve({
        success: false,
        error: error.message,
        duration: duration
      });
    });

    req.write(requestBody);
    req.end();
  });
}

// 运行测试
testSiliconFlowAPI().then(result => {
  console.log('\n📊 最终测试结果:', result);
}).catch(error => {
  console.error('测试失败:', error);
});
