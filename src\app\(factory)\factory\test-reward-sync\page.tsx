'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { getCurrentFactoryId } from '@/lib/utils/factory'

interface RewardTestResult {
  clientId: string
  clientName: string
  databaseReward: {
    availableReward: number
    referralReward: number
    usedReward: number
  }
  calculatedReward: {
    totalReward: number
    availableReward: number
    usedReward: number
    pendingReward: number
  }
  isConsistent: boolean
}

export default function TestRewardSyncPage() {
  const [clientId, setClientId] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<RewardTestResult | null>(null)
  const [error, setError] = useState('')

  const testRewardSync = async () => {
    if (!clientId.trim()) {
      setError('请输入客户ID')
      return
    }

    setLoading(true)
    setError('')
    setResult(null)

    try {
      const factoryId = getCurrentFactoryId()
      if (!factoryId) {
        throw new Error('无法获取工厂ID')
      }

      console.log('🧪 开始测试奖励同步:', { clientId, factoryId })

      // 1. 获取数据库中的客户信息
      const clientResponse = await fetch(`/api/clients?factoryId=${factoryId}`)
      if (!clientResponse.ok) {
        throw new Error('获取客户信息失败')
      }
      const clientsData = await clientResponse.json()
      const client = clientsData.clients?.find((c: any) => c.id === clientId)
      
      if (!client) {
        throw new Error('客户不存在')
      }

      // 2. 获取实时计算的奖励状态
      const rewardResponse = await fetch(`/api/clients/${clientId}/reward-status?factoryId=${factoryId}`)
      if (!rewardResponse.ok) {
        throw new Error('获取实时奖励状态失败')
      }
      const rewardData = await rewardResponse.json()
      
      if (!rewardData.success) {
        throw new Error(rewardData.error || '获取奖励状态失败')
      }

      const calculatedReward = rewardData.data

      // 3. 比较数据库值和计算值
      const databaseReward = {
        availableReward: Number(client.availableReward || 0),
        referralReward: Number(client.referralReward || 0),
        usedReward: Number(client.usedReward || 0)
      }

      const isConsistent = 
        Math.abs(databaseReward.availableReward - calculatedReward.availableReward) < 0.01 &&
        Math.abs(databaseReward.referralReward - calculatedReward.totalReward) < 0.01 &&
        Math.abs(databaseReward.usedReward - calculatedReward.usedReward) < 0.01

      setResult({
        clientId,
        clientName: client.name,
        databaseReward,
        calculatedReward,
        isConsistent
      })

      console.log('🧪 测试结果:', {
        databaseReward,
        calculatedReward,
        isConsistent
      })

    } catch (err) {
      console.error('❌ 测试失败:', err)
      setError(err instanceof Error ? err.message : '测试失败')
    } finally {
      setLoading(false)
    }
  }

  const syncReward = async () => {
    if (!result) return

    setLoading(true)
    try {
      const factoryId = getCurrentFactoryId()
      const response = await fetch('/api/clients/sync-rewards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ factoryId })
      })

      if (response.ok) {
        alert('奖励同步成功！请重新测试查看结果。')
        setResult(null)
      } else {
        throw new Error('同步失败')
      }
    } catch (err) {
      setError('同步失败：' + (err instanceof Error ? err.message : '未知错误'))
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-8">奖励同步测试</h1>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>测试客户奖励同步</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="clientId">客户ID</Label>
                <Input
                  id="clientId"
                  value={clientId}
                  onChange={(e) => setClientId(e.target.value)}
                  placeholder="输入要测试的客户ID"
                />
              </div>
              
              <div className="flex space-x-4">
                <Button onClick={testRewardSync} disabled={loading}>
                  {loading ? '测试中...' : '测试奖励同步'}
                </Button>
                {result && !result.isConsistent && (
                  <Button onClick={syncReward} disabled={loading} variant="outline">
                    修复奖励数据
                  </Button>
                )}
              </div>

              {error && (
                <div className="text-red-600 text-sm">{error}</div>
              )}
            </CardContent>
          </Card>

          {result && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>测试结果 - {result.clientName}</span>
                  <span className={`px-2 py-1 rounded text-sm ${
                    result.isConsistent 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {result.isConsistent ? '数据一致' : '数据不一致'}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 数据库中的值 */}
                  <div>
                    <h3 className="font-semibold mb-3 text-gray-700">数据库中的值</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>总奖励:</span>
                        <span className="font-mono">¥{result.databaseReward.referralReward.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>可用奖励:</span>
                        <span className="font-mono">¥{result.databaseReward.availableReward.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>已使用:</span>
                        <span className="font-mono">¥{result.databaseReward.usedReward.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  {/* 实时计算的值 */}
                  <div>
                    <h3 className="font-semibold mb-3 text-gray-700">实时计算的值</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>总奖励:</span>
                        <span className="font-mono">¥{result.calculatedReward.totalReward.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>可用奖励:</span>
                        <span className="font-mono">¥{result.calculatedReward.availableReward.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>已使用:</span>
                        <span className="font-mono">¥{result.calculatedReward.usedReward.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>待结算:</span>
                        <span className="font-mono">¥{result.calculatedReward.pendingReward.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {!result.isConsistent && (
                  <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
                    <p className="text-yellow-800 text-sm">
                      ⚠️ 检测到数据不一致，建议点击"修复奖励数据"按钮进行同步。
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
