/**
 * 🇨🇳 风口云平台 - 客户统计更新API
 *
 * 处理客户统计信息更新
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { clientId } = body

    console.log('📞 收到客户统计更新请求:', { clientId })

    // 验证必需参数
    if (!clientId) {
      return NextResponse.json({
        success: false,
        error: '客户ID不能为空'
      }, { status: 400 })
    }

    // 调用数据库服务更新客户统计信息
    await db.updateClientStatistics(clientId)

    console.log('✅ 客户统计信息更新成功')
    return NextResponse.json({
      success: true,
      message: '客户统计信息更新成功'
    })

  } catch (error) {
    console.error('❌ 客户统计更新API错误:', error)
    return NextResponse.json({
      success: false,
      error: `服务器错误: ${error.message}`
    }, { status: 500 })
  }
}
