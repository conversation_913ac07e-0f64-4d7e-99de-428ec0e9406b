/**
 * 🇨🇳 风口云平台 - 工厂状态管理 API
 * 处理工厂状态的暂停/启动操作
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware/auth'

export const PUT = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { 
      factoryId, 
      status, 
      suspendedAt, 
      suspendedReason 
    } = body

    console.log('🔄 更新工厂状态:', {
      factoryId,
      status,
      suspendedAt,
      suspendedReason
    })

    // 验证必需字段
    if (!factoryId || !status) {
      return NextResponse.json({
        success: false,
        error: '缺少必需的字段'
      }, { status: 400 })
    }

    // 验证状态值
    const validStatuses = ['active', 'inactive', 'suspended', 'expired']
    if (!validStatuses.includes(status)) {
      return NextResponse.json({
        success: false,
        error: '无效的状态值'
      }, { status: 400 })
    }

    // 获取当前工厂信息
    const factory = await db.getFactoryById(factoryId)
    if (!factory) {
      return NextResponse.json({
        success: false,
        error: '工厂不存在'
      }, { status: 404 })
    }

    // 更新工厂状态
    const updateData: any = {
      status,
      updatedAt: new Date()
    }

    // 如果是暂停状态，添加暂停相关信息
    if (status === 'suspended') {
      updateData.suspendedAt = suspendedAt ? new Date(suspendedAt) : new Date()
      updateData.suspendedReason = suspendedReason || '管理员手动暂停'
    } else {
      // 如果从暂停状态恢复，需要累计暂停时间
      if (factory.status === 'suspended' && factory.suspendedAt) {
        const suspendedAt = new Date(factory.suspendedAt)
        const now = new Date()
        const suspendedDuration = now.getTime() - suspendedAt.getTime()
        const totalSuspendedMs = BigInt(Number(factory.totalSuspendedMs || 0) + suspendedDuration)

        updateData.totalSuspendedMs = totalSuspendedMs
      }

      // 清除暂停相关信息
      updateData.suspendedAt = null
      updateData.suspendedReason = null
    }

    const updatedFactory = await db.updateFactory(factoryId, updateData)

    if (!updatedFactory) {
      return NextResponse.json({
        success: false,
        error: '工厂不存在或更新失败'
      }, { status: 404 })
    }

    console.log('✅ 工厂状态更新成功:', updatedFactory.id, '新状态:', status)

    // 处理BigInt序列化问题
    const factoryForResponse = {
      ...updatedFactory,
      totalSuspendedMs: updatedFactory.totalSuspendedMs ? Number(updatedFactory.totalSuspendedMs) : 0
    }

    return NextResponse.json({
      success: true,
      factory: factoryForResponse,
      message: `工厂状态已更新为: ${getStatusText(status)}`
    })

  } catch (error) {
    console.error('❌ 更新工厂状态失败:', error)
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
})

// 辅助函数：获取状态文本
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    active: '正常运行',
    inactive: '未激活',
    suspended: '暂停使用',
    expired: '已过期'
  }
  return statusMap[status] || status
}
