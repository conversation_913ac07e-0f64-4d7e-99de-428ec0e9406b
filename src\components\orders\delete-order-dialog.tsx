/**
 * 🇨🇳 风口云平台 - 删除订单对话框组件
 */

'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { AlertTriangle, Loader2 } from 'lucide-react'
import { useAuthStore } from '@/lib/store/auth'
import { db } from '@/lib/database/client'
import type { Order, User } from '@/types'

// 安全的用户名获取函数，避免在客户端导入Prisma
function safeGetUserUsername(user: User): string | undefined {
  if (user && 'username' in user) {
    return user.username
  }
  return undefined
}

interface DeleteOrderDialogProps {
  order: Order | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onOrderDeleted: () => void
}

export function DeleteOrderDialog({
  order,
  open,
  onOpenChange,
  onOrderDeleted
}: DeleteOrderDialogProps) {
  const [password, setPassword] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState('')
  const { user } = useAuthStore()

  const handleDelete = async () => {
    if (!order || !user) return

    // 验证密码
    if (!password.trim()) {
      setError('请输入密码')
      return
    }

    setIsDeleting(true)
    setError('')

    try {
      // 验证用户密码
      const response = await fetch('/api/auth/factory/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: safeGetUserUsername(user as User),
          password: password
        })
      })

      const verifyResult = await response.json()

      if (!verifyResult.success) {
        setError('密码错误，请重新输入')
        setIsDeleting(false)
        return
      }

      // 密码验证成功，删除订单
      await db.deleteOrder(order.id)

      console.log('✅ 订单删除成功')
      
      // 重置状态
      setPassword('')
      setError('')
      onOpenChange(false)
      onOrderDeleted()

    } catch (error) {
      console.error('❌ 删除订单失败:', error)
      setError(error instanceof Error ? error.message : '删除订单失败')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleCancel = () => {
    setPassword('')
    setError('')
    onOpenChange(false)
  }

  if (!order) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            删除订单确认
          </DialogTitle>
          <DialogDescription className="text-left">
            您即将删除以下订单，此操作不可撤销：
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 订单信息 */}
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">订单号：</span>
              <span className="text-sm font-medium">{order.orderNumber || order.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">客户：</span>
              <span className="text-sm font-medium">{order.clientName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">订单金额：</span>
              <span className="text-sm font-medium">¥{Number(order.totalAmount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">创建时间：</span>
              <span className="text-sm font-medium">
                {new Date(order.createdAt).toLocaleDateString('zh-CN')}
              </span>
            </div>
          </div>

          {/* 用户信息 */}
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-sm text-blue-800">
              <strong>操作用户：</strong> {user?.name} ({safeGetUserUsername(user as User)})
            </div>
            <div className="text-xs text-blue-600 mt-1">
              请输入您的登录密码以确认删除操作
            </div>
          </div>

          {/* 密码输入 */}
          <div className="space-y-2">
            <Label htmlFor="password">确认密码</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="请输入您的登录密码"
              disabled={isDeleting}
            />
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isDeleting}
          >
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting || !password.trim()}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                删除中...
              </>
            ) : (
              '确认删除'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
