/**
 * 测试 DeepSeek API 的 JSON 修复功能
 */

// 模拟截断的 JSON 响应
const truncatedJsonSamples = [
  // 1. 在对象中间截断
  `{
    "projects": [{
      "projectName": "",
      "clientInfo": "",
      "floors": [{
        "floorName": "一楼",
        "rooms": [{
          "roomName": "房间",
          "vents": [
            {
              "systemType": "double_white_outlet",
              "originalType": "出风口",
              "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
              "color": "white",
              "quantity": 1,
              "notes": ""
            },
            {
              "systemType": "double_white_outlet",
              "originalType": "出风口",
              "dimensions": {"length": 1525, "width": 255, "unit": "mm"},
              "color": "white",
              "quantity": 1,
              "notes": ""
            },
            {
              "systemType": "double_white_outlet",
              "originalType": "出风口",
              "`,

  // 2. 在数组中间截断
  `{
    "projects": [{
      "projectName": "",
      "clientInfo": "",
      "floors": [{
        "floorName": "一楼",
        "rooms": [{
          "roomName": "房间",
          "vents": [
            {
              "systemType": "double_white_outlet",
              "originalType": "出风口",
              "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
              "color": "white",
              "quantity": 1,
              "notes": ""
            }`,

  // 3. 缺少逗号的 JSON
  `{
    "projects": [{
      "projectName": "",
      "clientInfo": "",
      "floors": [{
        "floorName": "一楼",
        "rooms": [{
          "roomName": "房间",
          "vents": [
            {
              "systemType": "double_white_outlet",
              "originalType": "出风口",
              "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
              "color": "white",
              "quantity": 1,
              "notes": ""
            }
            {
              "systemType": "double_white_outlet",
              "originalType": "出风口",
              "dimensions": {"length": 1525, "width": 255, "unit": "mm"},
              "color": "white",
              "quantity": 1,
              "notes": ""
            }
          ]
        }]
      }]
    }]
  }`
];

// 测试函数
function testJsonFix() {
  console.log('🧪 开始测试 JSON 修复功能...');
  
  // 这里我们需要导入 DeepSeekAPI 类来测试
  // 由于这是一个简单的测试文件，我们先创建基本的测试结构
  
  truncatedJsonSamples.forEach((sample, index) => {
    console.log(`\n📝 测试样本 ${index + 1}:`);
    console.log('原始JSON长度:', sample.length);
    console.log('原始JSON结尾:', sample.substring(Math.max(0, sample.length - 100)));
    
    try {
      JSON.parse(sample);
      console.log('✅ 原始JSON有效');
    } catch (error) {
      console.log('❌ 原始JSON无效:', error.message);
      
      // 这里应该调用修复函数
      // const fixed = deepSeekAPI.fixCommonJsonErrors(sample);
      // 但由于我们在测试文件中，暂时跳过实际修复测试
    }
  });
}

// 如果直接运行此文件
if (typeof window === 'undefined') {
  testJsonFix();
}

export { truncatedJsonSamples, testJsonFix };
