/**
 * 🇨🇳 风口云平台 - 主题测试页面
 * 
 * 功能说明：
 * - 测试主题在各种组件中的一致性
 * - 验证Portal组件的主题继承
 * - 检查二级页面的主题显示
 */

'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ThemeToggle } from '@/components/theme/theme-toggle'
import { Badge } from '@/components/ui/badge'
import { 
  Sun, 
  Moon, 
  Monitor, 
  Settings,
  ChevronDown,
  TestTube,
  Palette,
  Eye,
  CheckCircle
} from 'lucide-react'

export default function ThemeTestPage() {
  const [dialogOpen, setDialogOpen] = useState(false)

  return (
    <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold flex items-center">
                <TestTube className="h-8 w-8 mr-3 text-blue-500" />
                主题一致性测试
              </h1>
              <p className="text-muted-foreground mt-2">
                测试各种组件在不同主题下的显示效果
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="outline">测试页面</Badge>
              <ThemeToggle showLabel />
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 基础组件测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette className="h-5 w-5 mr-2" />
                基础组件测试
              </CardTitle>
              <CardDescription>
                测试基础UI组件的主题显示
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-semibold">按钮组件</h4>
                <div className="flex space-x-2">
                  <Button variant="default">默认按钮</Button>
                  <Button variant="secondary">次要按钮</Button>
                  <Button variant="outline">边框按钮</Button>
                  <Button variant="ghost">幽灵按钮</Button>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">徽章组件</h4>
                <div className="flex space-x-2">
                  <Badge variant="default">默认</Badge>
                  <Badge variant="secondary">次要</Badge>
                  <Badge variant="outline">边框</Badge>
                  <Badge variant="destructive">危险</Badge>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">文字颜色测试</h4>
                <div className="space-y-1">
                  <p className="text-foreground">主要文字 (foreground)</p>
                  <p className="text-muted-foreground">次要文字 (muted-foreground)</p>
                  <p className="text-gray-900">灰色文字 900</p>
                  <p className="text-gray-700">灰色文字 700</p>
                  <p className="text-gray-500">灰色文字 500</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Portal组件测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                Portal组件测试
              </CardTitle>
              <CardDescription>
                测试弹出组件的主题继承（这是重点测试区域）
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-semibold">对话框 (Dialog)</h4>
                <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline">打开对话框</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>主题测试对话框</DialogTitle>
                      <DialogDescription>
                        这个对话框应该正确继承当前主题。
                        如果您看到白色文字在白色背景上，说明主题没有正确应用。
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <p className="text-foreground">
                        这是主要文字颜色，应该在任何主题下都清晰可见。
                      </p>
                      <p className="text-muted-foreground">
                        这是次要文字颜色，应该稍微淡一些但仍然可读。
                      </p>
                      <div className="flex space-x-2">
                        <Button onClick={() => setDialogOpen(false)}>关闭</Button>
                        <Button variant="outline">测试按钮</Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">下拉菜单 (DropdownMenu)</h4>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline">
                      打开菜单 <ChevronDown className="h-4 w-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem>
                      <Settings className="h-4 w-4 mr-2" />
                      设置选项
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Sun className="h-4 w-4 mr-2" />
                      浅色主题
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Moon className="h-4 w-4 mr-2" />
                      深色主题
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Monitor className="h-4 w-4 mr-2" />
                      跟随系统
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">选择器 (Select)</h4>
                <Select>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="选择一个选项" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="option1">选项 1 - 这应该清晰可见</SelectItem>
                    <SelectItem value="option2">选项 2 - 文字应该对比明显</SelectItem>
                    <SelectItem value="option3">选项 3 - 背景应该正确</SelectItem>
                    <SelectItem value="option4">选项 4 - 悬停效果正常</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* 测试结果 */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
                测试检查清单
              </CardTitle>
              <CardDescription>
                请在不同主题下检查以下项目
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-semibold">浅色主题检查</h4>
                  <ul className="space-y-1 text-sm">
                    <li>✓ 主界面背景为白色</li>
                    <li>✓ 文字为深色，清晰可读</li>
                    <li>✓ 对话框背景为白色</li>
                    <li>✓ 下拉菜单背景为白色</li>
                    <li>✓ 选择器下拉背景为白色</li>
                    <li>✓ 所有文字都清晰可见</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">深色主题检查</h4>
                  <ul className="space-y-1 text-sm">
                    <li>✓ 主界面背景为深色</li>
                    <li>✓ 文字为浅色，清晰可读</li>
                    <li>✓ 对话框背景为深色</li>
                    <li>✓ 下拉菜单背景为深色</li>
                    <li>✓ 选择器下拉背景为深色</li>
                    <li>✓ 没有白色文字在深色背景上不可见的情况</li>
                  </ul>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                  🎯 重点测试说明
                </h4>
                <p className="text-blue-700 dark:text-blue-300 text-sm">
                  请特别注意Portal组件（对话框、下拉菜单、选择器）的主题一致性。
                  如果这些组件的主题与主界面不一致，说明Portal主题继承存在问题。
                  修复后，所有二级页面都应该与主界面保持相同的主题。
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
