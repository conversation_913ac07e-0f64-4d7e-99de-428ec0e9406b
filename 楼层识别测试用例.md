# 🏢 楼层识别功能测试用例

## 📋 测试说明

AI识别功能现在已经增强了楼层识别能力，可以从项目名称中智能提取楼层信息。

## 🧪 测试用例

### 测试用例1: 书香华府8-501
**输入内容:**
```
书香华府8-501
出风口145x1870
回风口290x1770
出风口145x2990
回风口295x1655
```

**期望结果:**
- 项目名称: 书香华府8-501
- 楼层: 5楼 (从501中提取)
- 风口数量: 4个

### 测试用例2: 9-2101格式
**输入内容:**
```
9-2101
出风口1600x200
回风口2640x300
```

**期望结果:**
- 项目名称: 9-2101
- 楼层: 21楼 (从2101中提取)
- 风口数量: 2个

### 测试用例3: 3-1802格式
**输入内容:**
```
3-1802
出风口145x2990
回风口295x1655
出风口1600x200
```

**期望结果:**
- 项目名称: 3-1802
- 楼层: 18楼 (从1802中提取)
- 风口数量: 3个

### 测试用例4: 明确楼层标识
**输入内容:**
```
万科城5楼
出风口1600x200
回风口2640x300
出风口145x2990
```

**期望结果:**
- 项目名称: 万科城5楼
- 楼层: 5楼 (明确标识)
- 风口数量: 3个

### 测试用例5: 中文数字楼层
**输入内容:**
```
恒大华府三楼
出风口1600x200
回风口290x1770
```

**期望结果:**
- 项目名称: 恒大华府三楼
- 楼层: 3楼 (中文数字转换)
- 风口数量: 2个

## 🔧 支持的楼层识别格式

### 1. 明确楼层标识
- `5楼`、`15楼`
- `3层`、`18层`
- `5F`、`15F`
- `三楼`、`五层`

### 2. 房号格式提取
- `书香华府8-501` → 5楼
- `9-2101` → 21楼
- `3-1802` → 18楼
- `上淮府3-2702` → 27楼
- `1901号房` → 19楼

### 3. 项目+房号格式
- `金色时代10栋1901` → 19楼
- `容桂壹号7栋2单元903` → 9楼

### 4. 中文数字楼层
- `一楼` → 1楼
- `二层` → 2楼
- `三楼` → 3楼
- `十楼` → 10楼

## 🎯 测试步骤

1. 打开录单页面: http://localhost:3000/factory/orders/create-table
2. 点击"AI识别"按钮
3. 在输入框中粘贴测试用例内容
4. 点击"开始识别"
5. 查看识别结果中的楼层信息是否正确

## 🔍 验证要点

1. **楼层提取正确性**: 检查提取的楼层是否符合预期
2. **风口识别准确性**: 确保风口类型和尺寸识别正确
3. **价格计算正确性**: 验证楼层总价和订单汇总是否正确显示
4. **数据完整性**: 确保所有风口数据都被正确识别和转换

## 🐛 调试信息

在浏览器控制台中可以看到详细的调试信息：
- `🏢 智能楼层提取: "项目名称"`
- `✅ 从"栋-房号"格式提取楼层: "8-501" → "5楼"`
- `🔧 [价格计算] 输入参数: {...}`
- `🔧 [楼层总价] 楼层总价计算: ...`

## 📊 测试结果记录

| 测试用例 | 输入 | 期望楼层 | 实际楼层 | 状态 |
|---------|------|----------|----------|------|
| 书香华府8-501 | 501 | 5楼 | ? | ⏳ |
| 9-2101 | 2101 | 21楼 | ? | ⏳ |
| 3-1802 | 1802 | 18楼 | ? | ⏳ |
| 万科城5楼 | 5楼 | 5楼 | ? | ⏳ |
| 恒大华府三楼 | 三楼 | 3楼 | ? | ⏳ |

## 🎉 预期改进效果

1. **智能楼层识别**: 无需手动输入楼层信息
2. **提高录单效率**: 减少手动编辑步骤
3. **减少错误**: 自动提取减少人为输入错误
4. **更好的用户体验**: 一键识别项目和楼层信息
