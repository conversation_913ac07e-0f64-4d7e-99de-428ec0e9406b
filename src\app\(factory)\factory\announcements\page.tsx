/**
 * 🇨🇳 风口云平台 - 工厂公告查看页面
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 显示所有总部发布的公告
 * - 支持公告搜索和筛选
 * - 提供公告详细查看功能
 * - 支持标记已读/未读状态
 */

"use client"

import { useState, useEffect } from "react"
import { DashboardLayout  } from "@/components/layout/dashboard-layout"
import { FactoryRouteGuard } from "@/components/auth/route-guard"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Bell,
  Search,
  Filter,
  AlertTriangle,
  Info,
  Clock,
  Building,
  Eye,
  EyeOff,
  RefreshCw
} from "lucide-react"
import { db } from "@/lib/database"
import { getCurrentFactoryId } from "@/lib/utils/factory"
import type { Announcement } from "@/types"

export default function FactoryAnnouncementsPage() {
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState<string>("all")
  const [readStatus, setReadStatus] = useState<Record<string, boolean>>({})

  // 加载已读状态
  const loadReadStatus = async (factoryId: string, announcements: Announcement[]) => {
    try {
      // 获取该工厂的已读状态记录
      const response = await fetch(`/api/announcements/read-status?factoryId=${factoryId}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.readStatus) {
          setReadStatus(data.readStatus)
          return
        }
      }

      // 如果API调用失败，设置为空对象
      setReadStatus({})
    } catch (error) {
      console.error('❌ 加载已读状态失败:', error)
      setReadStatus({})
    }
  }

  // 加载公告数据
  const loadAnnouncements = async () => {
    try {
      setLoading(true)
      const factoryId = getCurrentFactoryId()

      if (!factoryId) {
        console.warn('⚠️ 无法获取工厂ID')
        return
      }

      console.log('🔄 加载工厂公告...')

      // 从数据库API获取工厂相关公告
      const factoryAnnouncements = await db.getAnnouncementsForFactory(factoryId)

      // 转换数据格式并排序
      const formattedAnnouncements = factoryAnnouncements.map((announcement: unknown) => ({
        ...announcement,
        publishedAt: new Date(announcement.publishedAt),
        expiresAt: announcement.expiresAt ? new Date(announcement.expiresAt) : null,
        // 处理目标工厂数据结构
        targetFactories: announcement.targets ? announcement.targets.map((t: unknown) => t.factoryId) : []
      })).sort((a: Announcement, b: Announcement) =>
        b.publishedAt.getTime() - a.publishedAt.getTime()
      )

      setAnnouncements(formattedAnnouncements)

      // 从数据库加载已读状态
      await loadReadStatus(factoryId, formattedAnnouncements)

      console.log(`✅ 从数据库加载了${formattedAnnouncements.length}个公告`)
    } catch (error) {
      console.error('❌ 加载公告失败:', error)
      // 设置空数组，不使用localStorage备用数据
      setAnnouncements([])
    } finally {
      setLoading(false)
    }
  }

  // 标记公告为已读
  const markAsRead = async (announcementId: string) => {
    const factoryId = getCurrentFactoryId()
    if (!factoryId) return

    try {
      // 先更新本地状态
      const newReadStatus = { ...readStatus, [announcementId]: true }
      setReadStatus(newReadStatus)

      // 通过API标记为已读
      const response = await fetch('/api/announcements/mark-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factoryId: factoryId,
          announcementId: announcementId
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || '标记已读失败')
      }

      console.log('✅ 公告已标记为已读')
    } catch (error) {
      console.error('❌ 标记已读失败:', error)
      // 如果API调用失败，回滚本地状态
      const revertedStatus = { ...readStatus, [announcementId]: false }
      setReadStatus(revertedStatus)
    }
  }

  // 标记公告为未读
  const markAsUnread = async (announcementId: string) => {
    const factoryId = getCurrentFactoryId()
    if (!factoryId) return

    try {
      // 先更新本地状态
      const newReadStatus = { ...readStatus }
      delete newReadStatus[announcementId]
      setReadStatus(newReadStatus)

      // 通过API标记为未读
      const response = await fetch('/api/announcements/mark-unread', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          factoryId: factoryId,
          announcementId: announcementId
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || '标记未读失败')
      }

      console.log('✅ 公告已标记为未读')
    } catch (error) {
      console.error('❌ 标记未读失败:', error)
      // 如果API调用失败，回滚本地状态
      const revertedStatus = { ...readStatus, [announcementId]: true }
      setReadStatus(revertedStatus)
    }
  }

  // 获取公告类型图标
  const getAnnouncementIcon = (type: string) => {
    switch (type) {
      case 'urgent':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  // 获取公告类型样式
  const getAnnouncementStyle = (type: string) => {
    switch (type) {
      case 'urgent':
        return {
          cardClass: 'border-l-4 border-l-red-500',
          badgeClass: 'bg-red-100 text-red-800',
          titleClass: 'text-red-900'
        }
      case 'warning':
        return {
          cardClass: 'border-l-4 border-l-yellow-500',
          badgeClass: 'bg-yellow-100 text-yellow-800',
          titleClass: 'text-yellow-900'
        }
      case 'info':
      default:
        return {
          cardClass: 'border-l-4 border-l-blue-500',
          badgeClass: 'bg-blue-100 text-blue-800',
          titleClass: 'text-blue-900'
        }
    }
  }

  // 获取公告类型文本
  const getAnnouncementTypeText = (type: string) => {
    switch (type) {
      case 'urgent': return '紧急公告'
      case 'warning': return '重要通知'
      case 'info': return '信息公告'
      default: return '公告'
    }
  }

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 过滤公告
  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesSearch = announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         announcement.content.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = filterType === 'all' || announcement.type === filterType
    
    return matchesSearch && matchesType
  })

  // 统计信息
  const totalCount = announcements.length
  const unreadCount = announcements.filter(a => !readStatus[a.id]).length
  const urgentCount = announcements.filter(a => a.type === 'urgent').length

  useEffect(() => {
    loadAnnouncements()
  }, [])

  return (
    <FactoryRouteGuard>
      <DashboardLayout role="factory">
        <div className="p-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
                <Bell className="h-8 w-8 text-blue-500" />
                <span>公告通知</span>
              </h1>
              <p className="text-gray-600 mt-1">查看总部发布的所有公告和通知（包括已读和未读）</p>
            </div>
            <Button onClick={loadAnnouncements} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Bell className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总公告数</p>
                    <p className="text-2xl font-bold text-gray-900">{totalCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <EyeOff className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">未读公告</p>
                    <p className="text-2xl font-bold text-gray-900">{unreadCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">紧急公告</p>
                    <p className="text-2xl font-bold text-gray-900">{urgentCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Building className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">来源</p>
                    <p className="text-lg font-bold text-gray-900">总部</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 搜索和筛选 */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索公告标题或内容..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex space-x-2">
              <Button
                variant={filterType === 'all' ? 'default' : 'outline'}
                onClick={() => setFilterType('all')}
                size="sm"
              >
                全部
              </Button>
              <Button
                variant={filterType === 'urgent' ? 'default' : 'outline'}
                onClick={() => setFilterType('urgent')}
                size="sm"
              >
                紧急
              </Button>
              <Button
                variant={filterType === 'warning' ? 'default' : 'outline'}
                onClick={() => setFilterType('warning')}
                size="sm"
              >
                重要
              </Button>
              <Button
                variant={filterType === 'info' ? 'default' : 'outline'}
                onClick={() => setFilterType('info')}
                size="sm"
              >
                信息
              </Button>
            </div>
          </div>

          {/* 公告列表 */}
          {loading ? (
            <div className="text-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">加载公告中...</p>
            </div>
          ) : filteredAnnouncements.length === 0 ? (
            <div className="text-center py-12">
              <Bell className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">
                {searchTerm || filterType !== 'all' ? '没有找到匹配的公告' : '暂无公告'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAnnouncements.map((announcement) => {
                const style = getAnnouncementStyle(announcement.type)
                const isRead = readStatus[announcement.id]
                const isExpired = announcement.expiresAt && announcement.expiresAt < new Date()
                
                return (
                  <Card 
                    key={announcement.id} 
                    className={`${style.cardClass} ${!isRead ? 'bg-blue-50' : ''} ${isExpired ? 'opacity-60' : ''} hover:shadow-md transition-shadow`}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4 flex-1">
                          {getAnnouncementIcon(announcement.type)}
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h3 className={`font-semibold text-lg ${style.titleClass}`}>
                                {announcement.title}
                              </h3>
                              <Badge className={style.badgeClass}>
                                {getAnnouncementTypeText(announcement.type)}
                              </Badge>
                              {!isRead && (
                                <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                                  未读
                                </Badge>
                              )}
                              {isExpired && (
                                <Badge variant="secondary" className="bg-gray-100 text-gray-600">
                                  已过期
                                </Badge>
                              )}
                            </div>
                            
                            <div className="text-gray-700 mb-4 leading-relaxed">
                              {announcement.content}
                            </div>
                            
                            <div className="flex items-center space-x-6 text-sm text-gray-500">
                              <div className="flex items-center space-x-1">
                                <Building className="h-4 w-4" />
                                <span>总部发布</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-4 w-4" />
                                <span>{formatTime(announcement.publishedAt)}</span>
                              </div>
                              {announcement.expiresAt && (
                                <div className="flex items-center space-x-1">
                                  <AlertTriangle className="h-4 w-4" />
                                  <span>有效期至 {announcement.expiresAt.toLocaleDateString('zh-CN')}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex space-x-2">
                          {isRead ? (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => markAsUnread(announcement.id)}
                              className="text-gray-500 hover:text-gray-700"
                            >
                              <EyeOff className="h-4 w-4 mr-1" />
                              标为未读
                            </Button>
                          ) : (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => markAsRead(announcement.id)}
                              className="text-blue-600 hover:text-blue-700"
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              标为已读
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>
      </DashboardLayout>
    </FactoryRouteGuard>
  )
}
