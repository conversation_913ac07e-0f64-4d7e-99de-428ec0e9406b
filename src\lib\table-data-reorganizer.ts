/**
 * 表格数据智能重排器
 * 将识别到的混乱数据按照推理出的规律重新排列为正确表格格式
 */

import { IntelligentTableReorganizer, ReorganizedTable } from './intelligent-table-reorganizer'
import { SequenceFloorAnalyzer, SequencePattern } from './sequence-floor-analyzer'

export interface ReorganizedRow {
  sequence: number
  mainDimensions: string
  subDimensions: string[]
  floor?: string
  notes?: string
  confidence: number
}

export interface ReorganizedTableData {
  rows: ReorganizedRow[]
  columns: number
  pattern: string
  confidence: number
  originalText: string
}

export class TableDataReorganizer {
  
  /**
   * 主要入口：重新组织表格数据
   */
  static reorganizeTableData(text: string): ReorganizedTableData {
    console.log('🔄 开始表格数据智能重排...')
    console.log('📝 原始文字:', text)
    
    // 步骤1: 分析序号和楼层模式
    const sequencePattern = SequenceFloorAnalyzer.analyzeSequenceAndFloor(text)
    console.log('🔢 序号模式分析结果:', sequencePattern)
    
    // 步骤2: 使用智能表格重组器分析
    const tableAnalysis = IntelligentTableReorganizer.analyzeAndReorganize(text)
    console.log('📊 表格分析结果:', tableAnalysis)
    
    // 步骤3: 根据分析结果重新组织数据
    const reorganizedData = this.createReorganizedTable(text, sequencePattern, tableAnalysis)
    console.log('✅ 重排完成')
    
    return reorganizedData
  }
  
  /**
   * 创建重新组织的表格
   */
  private static createReorganizedTable(
    originalText: string,
    sequencePattern: SequencePattern,
    tableAnalysis: ReorganizedTable
  ): ReorganizedTableData {

    console.log('🔄 创建重组表格，序号模式:', sequencePattern.type)
    console.log('📊 表格分析结果:', tableAnalysis.detectedPattern)

    // 优先使用表格分析结果，如果是双列模式
    if (tableAnalysis.detectedPattern === 'dual_column' || sequencePattern.type === 'dual_column') {
      console.log('✅ 使用双列模式创建表格')
      return this.createDualColumnTable(originalText, sequencePattern, tableAnalysis)
    }

    // 根据序号模式确定重排策略
    switch (sequencePattern.type) {
      case 'floor_based':
        return this.createFloorBasedTable(originalText, sequencePattern, tableAnalysis)
      case 'continuous':
        return this.createContinuousTable(originalText, sequencePattern, tableAnalysis)
      default:
        console.log('⚠️ 使用默认表格创建')
        return this.createDefaultTable(originalText, sequencePattern, tableAnalysis)
    }
  }
  
  /**
   * 创建双列表格（如用户示例的格式）
   */
  private static createDualColumnTable(
    originalText: string,
    sequencePattern: SequencePattern,
    tableAnalysis: ReorganizedTable
  ): ReorganizedTableData {

    const rows: ReorganizedRow[] = []
    const lines = originalText.split('\n').map(line => line.trim()).filter(line => line.length > 0)

    console.log('🔄 创建双列表格，原始行数:', lines.length)

    // 解析每一行数据
    const dataItems = this.parseDataItems(lines)
    console.log('📋 解析到数据项:', dataItems.length)

    // 按序号分组
    const sequenceGroups = this.groupBySequence(dataItems)
    console.log('🔢 序号分组:', Object.keys(sequenceGroups).map(k => `${k}:${sequenceGroups[k].length}项`))

    // 创建行数据
    for (const [sequence, items] of Object.entries(sequenceGroups)) {
      const seqNum = parseInt(sequence)

      console.log(`🔍 处理序号${seqNum}:`, items.map(item => item.dimensions || item.originalLine))

      // 找到主尺寸（通常是第一个较大的尺寸）
      const mainDimension = this.findMainDimension(items)

      // 找到子尺寸（通常是较小的尺寸）
      let subDimensions = this.findSubDimensions(items, mainDimension)

      // 如果没有子尺寸，但根据双列模式，每个序号都应该有300x1600
      if (subDimensions.length === 0 && mainDimension) {
        // 检查是否应该添加默认的300x1600子尺寸
        if (!mainDimension.includes('300x1600')) {
          subDimensions = ['300x1600']
          console.log(`📐 为序号${seqNum}添加默认子尺寸: 300x1600`)
        }
      }

      console.log(`📏 序号${seqNum} - 主尺寸:${mainDimension}, 子尺寸:[${subDimensions.join(', ')}]`)

      rows.push({
        sequence: seqNum,
        mainDimensions: mainDimension || '',
        subDimensions,
        confidence: 0.9
      })
    }

    // 按序号排序
    rows.sort((a, b) => a.sequence - b.sequence)

    // 检查是否有缺失的序号（双列模式应该是连续的）
    if (rows.length > 0) {
      const sequences = rows.map(row => row.sequence).sort((a, b) => a - b)
      const minSeq = sequences[0]
      const maxSeq = sequences[sequences.length - 1]

      console.log(`🔍 序号范围: ${minSeq} - ${maxSeq}，实际数量: ${sequences.length}`)

      // 检查缺失的序号
      for (let i = minSeq; i <= maxSeq; i++) {
        if (!sequences.includes(i)) {
          console.log(`⚠️ 缺失序号: ${i}`)

          // 智能推断缺失序号的主尺寸
          let inferredMainDimension = ''

          // 方法1：查找相邻序号的主尺寸模式
          const prevRow = rows.find(r => r.sequence === i - 1)
          const nextRow = rows.find(r => r.sequence === i + 1)

          if (prevRow && prevRow.mainDimensions) {
            inferredMainDimension = prevRow.mainDimensions
            console.log(`🔍 从前一个序号${i-1}推断主尺寸: ${inferredMainDimension}`)
          } else if (nextRow && nextRow.mainDimensions) {
            inferredMainDimension = nextRow.mainDimensions
            console.log(`🔍 从后一个序号${i+1}推断主尺寸: ${inferredMainDimension}`)
          } else {
            // 方法2：使用最常见的主尺寸
            const commonDimensions = rows
              .map(r => r.mainDimensions)
              .filter(d => d && d !== '')

            if (commonDimensions.length > 0) {
              // 找到最常见的尺寸
              const dimensionCounts = commonDimensions.reduce((acc, dim) => {
                acc[dim] = (acc[dim] || 0) + 1
                return acc
              }, {} as Record<string, number>)

              inferredMainDimension = Object.entries(dimensionCounts)
                .sort(([,a], [,b]) => b - a)[0][0]
              console.log(`🔍 使用最常见的主尺寸: ${inferredMainDimension}`)
            } else {
              // 方法3：使用默认尺寸
              inferredMainDimension = '150x3500'
              console.log(`🔍 使用默认主尺寸: ${inferredMainDimension}`)
            }
          }

          // 为缺失的序号添加推断的行
          rows.push({
            sequence: i,
            mainDimensions: inferredMainDimension,
            subDimensions: ['300x1600'], // 默认子尺寸
            confidence: 0.6 // 中等置信度
          })
        }
      }

      // 重新排序
      rows.sort((a, b) => a.sequence - b.sequence)
    }

    console.log('✅ 双列表格创建完成，行数:', rows.length)

    const confidence = Math.min((sequencePattern.confidence + tableAnalysis.confidence) / 2, 1.0)
    console.log('📊 双列表格置信度:', confidence)

    return {
      rows,
      columns: 2, // 双列格式
      pattern: 'dual_column',
      confidence,
      originalText
    }
  }
  
  /**
   * 创建基于楼层的表格
   */
  private static createFloorBasedTable(
    originalText: string,
    sequencePattern: SequencePattern,
    tableAnalysis: ReorganizedTable
  ): ReorganizedTableData {
    
    const rows: ReorganizedRow[] = []
    const lines = originalText.split('\n').map(line => line.trim()).filter(line => line.length > 0)
    
    // 解析数据项并关联楼层
    const dataItems = this.parseDataItems(lines)
    const floorAssignments = this.assignFloorsToData(dataItems, sequencePattern.floors)
    
    // 按序号分组并添加楼层信息
    const sequenceGroups = this.groupBySequence(dataItems)
    
    for (const [sequence, items] of Object.entries(sequenceGroups)) {
      const seqNum = parseInt(sequence)
      const floor = floorAssignments.get(seqNum)
      
      const mainDimension = this.findMainDimension(items)
      const subDimensions = this.findSubDimensions(items, mainDimension)
      
      rows.push({
        sequence: seqNum,
        mainDimensions: mainDimension || '',
        subDimensions,
        floor,
        confidence: 0.8
      })
    }
    
    rows.sort((a, b) => a.sequence - b.sequence)
    
    return {
      rows,
      columns: 1,
      pattern: 'floor_based',
      confidence: Math.min(sequencePattern.confidence + tableAnalysis.confidence, 2) / 2,
      originalText
    }
  }
  
  /**
   * 创建连续表格
   */
  private static createContinuousTable(
    originalText: string,
    sequencePattern: SequencePattern,
    tableAnalysis: ReorganizedTable
  ): ReorganizedTableData {
    
    const rows: ReorganizedRow[] = []
    const lines = originalText.split('\n').map(line => line.trim()).filter(line => line.length > 0)
    
    const dataItems = this.parseDataItems(lines)
    const sequenceGroups = this.groupBySequence(dataItems)
    
    for (const [sequence, items] of Object.entries(sequenceGroups)) {
      const seqNum = parseInt(sequence)
      
      const mainDimension = this.findMainDimension(items)
      const subDimensions = this.findSubDimensions(items, mainDimension)
      
      rows.push({
        sequence: seqNum,
        mainDimensions: mainDimension || '',
        subDimensions,
        confidence: 0.8
      })
    }
    
    rows.sort((a, b) => a.sequence - b.sequence)
    
    return {
      rows,
      columns: 1,
      pattern: 'continuous',
      confidence: Math.min(sequencePattern.confidence + tableAnalysis.confidence, 2) / 2,
      originalText
    }
  }
  
  /**
   * 创建默认表格
   */
  private static createDefaultTable(
    originalText: string,
    sequencePattern: SequencePattern,
    tableAnalysis: ReorganizedTable
  ): ReorganizedTableData {
    
    return {
      rows: [],
      columns: 1,
      pattern: 'unknown',
      confidence: 0.3,
      originalText
    }
  }
  
  /**
   * 解析数据项
   */
  private static parseDataItems(lines: string[]): Array<{
    sequence?: number
    dimensions?: string
    originalLine: string
    lineIndex: number
  }> {

    const items: Array<{
      sequence?: number
      dimensions?: string
      originalLine: string
      lineIndex: number
    }> = []

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]

      console.log(`🔍 解析第${i+1}行: "${line}"`)

      // 提取序号
      const sequenceMatch = line.match(/^(\d+)\./)
      const sequence = sequenceMatch ? parseInt(sequenceMatch[1]) : undefined

      // 提取尺寸 - 改进正则表达式
      const dimensionMatch = line.match(/(\d+[x×*]\d+)/)
      let dimensions = dimensionMatch ? dimensionMatch[1].replace(/[×*]/g, 'x') : undefined

      // 如果没有序号但有尺寸，可能是子尺寸行
      if (!sequence && dimensions) {
        console.log(`📏 发现无序号尺寸: ${dimensions}`)
      }

      if (sequence) {
        console.log(`🔢 发现序号: ${sequence}`)
      }

      if (dimensions) {
        console.log(`📐 发现尺寸: ${dimensions}`)
      }

      items.push({
        sequence,
        dimensions,
        originalLine: line,
        lineIndex: i
      })
    }

    console.log('📋 数据项解析完成:', items.filter(item => item.dimensions).length, '个尺寸项')
    return items
  }
  
  /**
   * 按序号分组
   */
  private static groupBySequence(items: Array<any>): Record<string, Array<any>> {
    const groups: Record<string, Array<any>> = {}
    let currentSequence: number | null = null

    console.log('🔢 开始按序号分组...')
    console.log('📋 输入项目:', items.map(item => `${item.sequence || 'N/A'}:${item.dimensions || item.originalLine}`))

    for (const item of items) {
      // 如果当前项有序号，更新当前序号
      if (item.sequence !== undefined) {
        currentSequence = item.sequence
        console.log(`🔄 切换到序号: ${currentSequence}`)
      }

      // 只处理有尺寸的项目，并且确保序号正确
      if (item.dimensions && currentSequence !== null) {
        const key = currentSequence.toString()
        if (!groups[key]) {
          groups[key] = []
        }

        // 避免重复添加相同的尺寸
        const existingDimensions = groups[key].map(g => g.dimensions)
        if (!existingDimensions.includes(item.dimensions)) {
          groups[key].push(item)
          console.log(`📋 序号${key}添加尺寸: ${item.dimensions}`)
        } else {
          console.log(`⚠️ 序号${key}跳过重复尺寸: ${item.dimensions}`)
        }
      }
    }

    console.log('🔢 分组完成:', Object.keys(groups).map(k => `${k}:${groups[k].length}项`))

    // 调试输出每个分组的详细内容
    Object.keys(groups).forEach(key => {
      console.log(`📊 序号${key}的尺寸:`, groups[key].map(item => item.dimensions))
    })

    return groups
  }
  
  /**
   * 找到主尺寸
   */
  private static findMainDimension(items: Array<any>): string | undefined {
    const dimensions = items
      .map(item => item.dimensions)
      .filter(dim => {
        if (!dim) return false

        // 特殊处理：0x3510可能是140x3510的OCR错误
        if (dim === '0x3510') {
          console.log('🔧 检测到0x3510，可能是140x3510的OCR错误，尝试修复')
          return false // 暂时过滤，后续会在其他地方修复
        }

        // 过滤掉其他0x开头的无效尺寸
        return !dim.startsWith('0x')
      })

    if (dimensions.length === 0) {
      console.log('⚠️ 没有找到有效的主尺寸候选')

      // 检查是否有0x3510这种特殊情况，尝试修复
      const zeroXItems = items.filter(item => item.dimensions === '0x3510')
      if (zeroXItems.length > 0) {
        console.log('🔧 尝试修复0x3510为140x3510')
        return '140x3510'
      }

      return undefined
    }

    console.log('🔍 查找主尺寸，候选:', dimensions)

    // 排除300x1600这种标准回风口尺寸，优先选择其他尺寸作为主尺寸
    const nonStandardDimensions = dimensions.filter(dim =>
      !dim.includes('300x1600') && !dim.includes('300x100')
    )

    let mainDim: string

    if (nonStandardDimensions.length > 0) {
      // 如果有非标准尺寸，选择其中最大的
      mainDim = nonStandardDimensions[0]

      if (nonStandardDimensions.length > 1) {
        for (const dim of nonStandardDimensions) {
          const [w1, h1] = mainDim.split('x').map(n => parseInt(n))
          const [w2, h2] = dim.split('x').map(n => parseInt(n))

          // 比较面积，选择较大的
          if ((w2 * h2) > (w1 * h1)) {
            mainDim = dim
          }
        }
      }
    } else {
      // 如果只有标准尺寸，选择第一个
      mainDim = dimensions[0]
    }

    console.log('📏 选择主尺寸:', mainDim)
    return mainDim
  }

  /**
   * 找到子尺寸
   */
  private static findSubDimensions(items: Array<any>, mainDimension?: string): string[] {
    const dimensions = items
      .map(item => item.dimensions)
      .filter(dim => dim && dim !== mainDimension)

    console.log('📐 查找子尺寸，排除主尺寸:', mainDimension, '剩余:', dimensions)
    return dimensions
  }
  
  /**
   * 为数据分配楼层
   */
  private static assignFloorsToData(items: Array<any>, floors: Array<any>): Map<number, string> {
    const assignments = new Map<number, string>()
    
    // 简单的分配策略：按位置就近分配
    for (const item of items) {
      if (item.sequence) {
        const nearestFloor = this.findNearestFloor(item.lineIndex, floors)
        if (nearestFloor) {
          assignments.set(item.sequence, nearestFloor.floor)
        }
      }
    }
    
    return assignments
  }
  
  /**
   * 找到最近的楼层
   */
  private static findNearestFloor(lineIndex: number, floors: Array<any>): any {
    let nearest = null
    let minDistance = Infinity
    
    for (const floor of floors) {
      const distance = Math.abs(floor.position - lineIndex)
      if (distance < minDistance) {
        minDistance = distance
        nearest = floor
      }
    }
    
    return nearest
  }
}
