/**
 * 🚨 错误处理工具函数
 * 统一处理项目中的错误类型转换和消息提取
 */

/**
 * 安全的错误消息提取
 * @param error 任意类型的错误对象
 * @returns 错误消息字符串
 */
export const getErrorMessage = (error: unknown): string => {
  if (error === null || error === undefined) {
    return '未知错误'
  }

  // 如果是字符串，直接返回
  if (typeof error === 'string') {
    return error
  }

  // 如果是Error对象或类似对象
  if (error && typeof error === 'object') {
    // 标准Error对象
    if ('message' in error && typeof error.message === 'string') {
      return error.message
    }

    // 可能的API错误响应
    if ('error' in error && typeof error.error === 'string') {
      return error.error
    }

    // 尝试转换为字符串
    try {
      return String(error)
    } catch {
      return '错误对象无法转换为字符串'
    }
  }

  // 其他类型，尝试转换为字符串
  try {
    return String(error)
  } catch {
    return '未知错误类型'
  }
}

/**
 * 安全的错误名称提取
 * @param error 任意类型的错误对象
 * @returns 错误名称字符串
 */
export const getErrorName = (error: unknown): string => {
  if (error && typeof error === 'object' && 'name' in error && typeof error.name === 'string') {
    return error.name
  }
  return 'Error'
}

/**
 * 安全的错误堆栈提取
 * @param error 任意类型的错误对象
 * @returns 错误堆栈字符串或undefined
 */
export const getErrorStack = (error: unknown): string | undefined => {
  if (error && typeof error === 'object' && 'stack' in error && typeof error.stack === 'string') {
    return error.stack
  }
  return undefined
}

/**
 * 安全的错误代码提取
 * @param error 任意类型的错误对象
 * @returns 错误代码字符串或undefined
 */
export const getErrorCode = (error: unknown): string | undefined => {
  if (error && typeof error === 'object' && 'code' in error) {
    if (typeof error.code === 'string' || typeof error.code === 'number') {
      return String(error.code)
    }
  }
  return undefined
}

/**
 * 创建标准化的错误对象
 * @param error 原始错误
 * @returns 标准化的错误信息对象
 */
export const createErrorInfo = (error: unknown) => {
  return {
    name: getErrorName(error),
    message: getErrorMessage(error),
    stack: getErrorStack(error),
    code: getErrorCode(error)
  }
}

/**
 * 检查是否为网络错误
 * @param error 错误对象
 * @returns 是否为网络错误
 */
export const isNetworkError = (error: unknown): boolean => {
  const message = getErrorMessage(error).toLowerCase()
  return message.includes('network') || 
         message.includes('fetch') || 
         message.includes('connection') ||
         message.includes('timeout')
}

/**
 * 检查是否为认证错误
 * @param error 错误对象
 * @returns 是否为认证错误
 */
export const isAuthError = (error: unknown): boolean => {
  const message = getErrorMessage(error).toLowerCase()
  const code = getErrorCode(error)
  return message.includes('unauthorized') || 
         message.includes('authentication') ||
         message.includes('token') ||
         code === '401' ||
         code === '403'
}

/**
 * 检查是否为验证错误
 * @param error 错误对象
 * @returns 是否为验证错误
 */
export const isValidationError = (error: unknown): boolean => {
  const message = getErrorMessage(error).toLowerCase()
  const code = getErrorCode(error)
  return message.includes('validation') || 
         message.includes('invalid') ||
         message.includes('required') ||
         code === '400'
}

/**
 * 格式化错误消息用于用户显示
 * @param error 错误对象
 * @param defaultMessage 默认消息
 * @returns 用户友好的错误消息
 */
export const formatErrorForUser = (error: unknown, defaultMessage: string = '操作失败'): string => {
  const message = getErrorMessage(error)
  
  // 如果是网络错误，返回友好提示
  if (isNetworkError(error)) {
    return '网络连接失败，请检查网络后重试'
  }
  
  // 如果是认证错误，返回友好提示
  if (isAuthError(error)) {
    return '身份验证失败，请重新登录'
  }
  
  // 如果是验证错误，返回具体消息
  if (isValidationError(error)) {
    return message || '输入数据有误，请检查后重试'
  }
  
  // 其他错误，返回消息或默认消息
  return message || defaultMessage
}

/**
 * 日志记录错误（用于开发调试）
 * @param error 错误对象
 * @param context 错误上下文
 */
export const logError = (error: unknown, context?: string) => {
  const errorInfo = createErrorInfo(error)
  const prefix = context ? `[${context}]` : '[Error]'
  
  console.error(`${prefix} ${errorInfo.name}: ${errorInfo.message}`)
  if (errorInfo.stack) {
    console.error('Stack:', errorInfo.stack)
  }
  if (errorInfo.code) {
    console.error('Code:', errorInfo.code)
  }
}
