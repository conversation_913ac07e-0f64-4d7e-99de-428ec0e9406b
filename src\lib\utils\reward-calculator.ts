// 客户奖励计算工具

import { getFactorySettingsSync } from './factory-settings'
import type { Order, OrderItem } from '@/types'
import { isPremiumVent as isPremiumVentClassifier } from '@/lib/utils/vent-type-classifier'
import { safeNumber, safeAmountSum } from './number-utils'

/**
 * 奖励计算结果接口
 */
export interface RewardCalculationResult {
  totalReward: number           // 总奖励金额
  regularVentReward: number     // 普通风口奖励
  premiumVentReward: number     // 高端风口奖励
  breakdown: {
    regularVentAmount: number   // 普通风口订单金额
    premiumVentAmount: number   // 高端风口订单金额
    regularVentRate: number     // 普通风口奖励比例
    premiumVentDetails: Array<{
      orderAmount: number
      rewardAmount: number
      tier: string
    }>
  }
}

/**
 * 判断产品是否为高端风口
 * @param productType 产品类型
 * @param productName 产品名称（可选，用于更精确的判断）
 * @returns 是否为高端风口
 */
export const isPremiumVent = (productType: string, productName?: string): boolean => {
  // 使用统一的风口类型分类器
  return isPremiumVentClassifier(productType, productName)
}

/**
 * 计算普通风口奖励比例
 * @param orderAmount 订单金额
 * @param factoryId 工厂ID
 * @returns 奖励比例
 */
export const calculateRegularVentRewardRate = (orderAmount: number, factoryId: string): number => {
  try {
    const settings = getFactorySettingsSync(factoryId)

    // 检查奖励设置是否存在，如果不存在则使用默认值
    if (!settings.rewardSettings || !settings.rewardSettings.regularVentRewards) {
      console.warn(`⚠️ 工厂 ${factoryId} 缺少奖励设置，使用默认值`)
      return 0.02 // 默认2%奖励
    }

    const { regularVentRewards } = settings.rewardSettings

    // 根据订单金额确定奖励比例
    if (orderAmount >= regularVentRewards.tier2Amount) {
      return Math.min(regularVentRewards.tier2Rate, regularVentRewards.maxRate)
    } else if (orderAmount >= regularVentRewards.tier1Amount) {
      return regularVentRewards.tier1Rate
    } else {
      return regularVentRewards.baseRate
    }
  } catch (error) {
    console.error(`❌ 计算普通风口奖励比例失败:`, error)
    return 0.02 // 出错时返回默认2%
  }
}

/**
 * 计算高端风口奖励金额
 * @param orderAmount 单笔订单金额
 * @param factoryId 工厂ID
 * @returns 奖励金额和档位信息
 */
export const calculatePremiumVentReward = (orderAmount: number, factoryId: string): { amount: number, tier: string } => {
  try {
    const settings = getFactorySettingsSync(factoryId)

    // 检查奖励设置是否存在，如果不存在则使用默认值
    if (!settings.rewardSettings || !settings.rewardSettings.premiumVentRewards) {
      console.warn(`⚠️ 工厂 ${factoryId} 缺少高端风口奖励设置，使用默认值`)
      // 默认高端风口奖励：按订单金额的2%计算
      const defaultReward = orderAmount * 0.02
      return { amount: defaultReward, tier: '默认档(2%)' }
    }

    const { premiumVentRewards } = settings.rewardSettings

    // 根据订单金额确定奖励档位
    if (orderAmount >= premiumVentRewards.tier3Amount) {
      return { amount: premiumVentRewards.tier3Reward, tier: '超级档(≥5万)' }
    } else if (orderAmount >= premiumVentRewards.tier2Amount) {
      return { amount: premiumVentRewards.tier2Reward, tier: '高级档(≥2万)' }
    } else if (orderAmount >= premiumVentRewards.tier1Amount) {
      return { amount: premiumVentRewards.tier1Reward, tier: '中级档(≥1万)' }
    } else {
      return { amount: premiumVentRewards.baseAmount, tier: '基础档(<1万)' }
    }
  } catch (error) {
    console.error(`❌ 计算高端风口奖励失败:`, error)
    // 出错时返回默认2%奖励
    const defaultReward = orderAmount * 0.02
    return { amount: defaultReward, tier: '默认档(2%)' }
  }
}

/**
 * 计算单个订单的推荐奖励
 * @param order 订单信息
 * @param factoryId 工厂ID
 * @returns 奖励计算结果
 */
export const calculateOrderReward = (order: Order, factoryId: string): RewardCalculationResult => {
  const result: RewardCalculationResult = {
    totalReward: 0,
    regularVentReward: 0,
    premiumVentReward: 0,
    breakdown: {
      regularVentAmount: 0,
      premiumVentAmount: 0,
      regularVentRate: 0,
      premiumVentDetails: []
    }
  }
  
  if (!order.items || order.items.length === 0) {
    return result
  }
  
  // 分类计算普通风口和高端风口的金额
  const regularVentAmounts: number[] = []
  const premiumVentAmounts: number[] = []
  const premiumVentOrders: Array<{ amount: number, type: string }> = []

  // 解析订单项目（可能是JSON字符串）
  let items: OrderItem[] = []
  if (typeof order.items === 'string') {
    try {
      items = JSON.parse(order.items)
    } catch (error) {
      console.error('❌ 解析订单项目失败:', error)
      items = []
    }
  } else if (Array.isArray(order.items)) {
    items = order.items
  }

  items.forEach((item: OrderItem) => {
    const itemAmount = safeNumber(item.totalPrice || 0)

    if (isPremiumVent(item.productType, item.productName)) {
      premiumVentAmounts.push(itemAmount)
      premiumVentOrders.push({
        amount: itemAmount,
        type: item.productType
      })
    } else {
      regularVentAmounts.push(itemAmount)
    }
  })

  // 🔧 修复：使用精度安全的累加函数
  const regularVentAmount = safeAmountSum(regularVentAmounts)
  const premiumVentAmount = safeAmountSum(premiumVentAmounts)
  
  // 计算普通风口奖励（按比例）
  if (regularVentAmount > 0) {
    const rewardRate = calculateRegularVentRewardRate(regularVentAmount, factoryId)
    result.regularVentReward = regularVentAmount * rewardRate
    result.breakdown.regularVentAmount = regularVentAmount
    result.breakdown.regularVentRate = rewardRate
  }
  
  // 计算高端风口奖励（按订单固定金额）
  if (premiumVentOrders.length > 0) {
    // 对于高端风口，每个产品类型的订单分别计算奖励
    const productTypeAmounts = new Map<string, number>()
    
    premiumVentOrders.forEach(({ amount, type }) => {
      const currentAmount = productTypeAmounts.get(type) || 0
      productTypeAmounts.set(type, currentAmount + amount)
    })
    
    // 为每个产品类型计算奖励
    productTypeAmounts.forEach((amount, type) => {
      const { amount: rewardAmount, tier } = calculatePremiumVentReward(amount, factoryId)
      result.premiumVentReward += rewardAmount
      result.breakdown.premiumVentDetails.push({
        orderAmount: amount,
        rewardAmount,
        tier
      })
    })
    
    result.breakdown.premiumVentAmount = premiumVentAmount
  }
  
  // 计算总奖励
  result.totalReward = result.regularVentReward + result.premiumVentReward
  
  return result
}

/**
 * 计算客户的总推荐奖励
 * @param referredOrders 被推荐客户的所有订单
 * @param factoryId 工厂ID
 * @returns 总奖励计算结果
 */
export const calculateTotalReferralReward = (referredOrders: Order[], factoryId: string): RewardCalculationResult => {
  const totalResult: RewardCalculationResult = {
    totalReward: 0,
    regularVentReward: 0,
    premiumVentReward: 0,
    breakdown: {
      regularVentAmount: 0,
      premiumVentAmount: 0,
      regularVentRate: 0,
      premiumVentDetails: []
    }
  }
  
  // 🔧 修复：使用数组收集，然后安全累加
  const totalRewards: number[] = []
  const regularVentRewards: number[] = []
  const premiumVentRewards: number[] = []
  const regularVentAmounts: number[] = []
  const premiumVentAmounts: number[] = []

  referredOrders.forEach(order => {
    const orderReward = calculateOrderReward(order, factoryId)

    totalRewards.push(orderReward.totalReward)
    regularVentRewards.push(orderReward.regularVentReward)
    premiumVentRewards.push(orderReward.premiumVentReward)
    regularVentAmounts.push(orderReward.breakdown.regularVentAmount)
    premiumVentAmounts.push(orderReward.breakdown.premiumVentAmount)

    totalResult.breakdown.premiumVentDetails.push(...orderReward.breakdown.premiumVentDetails)
  })

  // 使用安全累加函数
  totalResult.totalReward = safeAmountSum(totalRewards)
  totalResult.regularVentReward = safeAmountSum(regularVentRewards)
  totalResult.premiumVentReward = safeAmountSum(premiumVentRewards)
  totalResult.breakdown.regularVentAmount = safeAmountSum(regularVentAmounts)
  totalResult.breakdown.premiumVentAmount = safeAmountSum(premiumVentAmounts)

  // 重新计算普通风口的平均奖励比例
  if (totalResult.breakdown.regularVentAmount > 0) {
    totalResult.breakdown.regularVentRate = totalResult.regularVentReward / totalResult.breakdown.regularVentAmount
  }

  return totalResult
}

/**
 * 检查奖励是否可以使用
 * @param clientId 客户ID
 * @param factoryId 工厂ID
 * @param orders 客户的所有订单
 * @returns 是否可以使用奖励
 */
export const canUseReward = (clientId: string, factoryId: string, orders: Order[]): boolean => {
  try {
    const settings = getFactorySettingsSync(factoryId)

    // 检查奖励设置是否存在
    if (!settings.rewardSettings || !settings.rewardSettings.rewardRules) {
      console.warn(`⚠️ 工厂 ${factoryId} 缺少奖励规则设置，默认允许使用奖励`)
      return true // 默认允许使用奖励
    }

    // 如果不要求结清货款，则可以直接使用
    if (!settings.rewardSettings.rewardRules.requireFullPayment) {
      return true
    }

    // 检查是否有未结清的订单
    const hasUnpaidOrders = orders.some(order => {
      const paidAmount = order.paidAmount || 0
      const totalAmount = order.totalAmount || 0
      return paidAmount < totalAmount
    })

    return !hasUnpaidOrders
  } catch (error) {
    console.error(`❌ 检查奖励使用权限失败:`, error)
    return true // 出错时默认允许使用
  }
}

/**
 * 🆕 计算推荐人的按比例奖励状态
 * @param referrerId 推荐人ID
 * @param factoryId 工厂ID
 * @returns 详细的奖励状态信息
 */
export const calculateProportionalReward = async (referrerId: string, factoryId: string): Promise<{
  totalReward: number
  availableReward: number
  pendingReward: number
  usedReward: number
  settledClients: number
  totalClients: number
  details: Array<{
    clientId: string
    clientName: string
    orderAmount: number
    rewardAmount: number
    isSettled: boolean
    paidAmount: number
    unpaidAmount: number
  }>
}> => {
  try {
    console.log('🧮 计算按比例奖励状态:', { referrerId, factoryId })

    // 获取工厂设置
    const settings = getFactorySettingsSync(factoryId)
    const rewardRate = (settings.rewardSettings?.rewardRules as unknown)?.rewardRate || 0.02

    // 获取所有被推荐客户
    const { db } = await import('@/lib/database')
    const allClients = await db.getClientsByFactoryId(factoryId)
    const referredClients = allClients.filter((client: unknown) => client.referrerId === referrerId)

    console.log(`📋 推荐人 ${referrerId} 推荐了 ${referredClients.length} 个客户:`, referredClients.map((c: unknown) => c.name))

    let totalReward = 0
    let availableReward = 0
    let settledClients = 0
    const details: unknown[] = []

    // 逐个计算每个客户的奖励状态
    for (const client of referredClients) {
      const orders = await db.getOrdersByClientId(client.id)
      // 🔧 修复：使用安全累加函数处理精度问题
      const orderAmount = safeAmountSum(orders.map((order: unknown) => order.totalAmount || 0))
      const paidAmount = safeAmountSum(orders.map((order: unknown) => order.paidAmount || 0))
      const unpaidAmount = Math.max(0, Math.round((orderAmount - paidAmount) * 10) / 10)

      // 🔧 修复：使用阶梯式奖励计算，而不是固定比例
      const clientRewardResult = calculateTotalReferralReward(orders, factoryId)
      const rewardAmount = clientRewardResult.totalReward
      totalReward += rewardAmount

      // 判断是否完全结清
      const isSettled = unpaidAmount <= 0.01 // 允许1分钱的误差
      if (isSettled) {
        availableReward += rewardAmount
        settledClients++
      }

      details.push({
        clientId: client.id,
        clientName: client.name,
        orderAmount,
        rewardAmount,
        isSettled,
        paidAmount,
        unpaidAmount
      })

      console.log(`📋 客户 ${client.name}: 订单¥${orderAmount}, 奖励¥${rewardAmount.toFixed(2)} (阶梯式), ${isSettled ? '已结清' : `欠款¥${unpaidAmount.toFixed(2)}`}`)
      console.log(`   奖励详情: 普通风口¥${clientRewardResult.regularVentReward.toFixed(2)} + 高端风口¥${clientRewardResult.premiumVentReward.toFixed(2)}`)
      console.log(`   💰 奖励累加: 总奖励 ${totalReward.toFixed(2)} → ${(totalReward + rewardAmount).toFixed(2)}, 可用奖励 ${availableReward.toFixed(2)} → ${isSettled ? (availableReward + rewardAmount).toFixed(2) : availableReward.toFixed(2)}`)
    }

    // 🆕 获取已使用的奖励金额 - 修复：统一使用数据库服务
    let usedReward = 0
    try {
      // 检查是否在服务器端环境
      if (typeof window === 'undefined') {
        // 服务器端：直接使用 Prisma
        const { default: prisma } = await import('@/lib/prisma')
        const usedRewardRecords = await prisma.rewardUsage.findMany({
          where: {
            clientId: referrerId,
            factoryId,
            status: 'approved'
          },
          select: {
            amount: true
          }
        })
        usedReward = usedRewardRecords.reduce((sum, record) => sum + (record.amount || 0), 0)
        console.log(`💰 已使用奖励查询成功 (服务器端): ¥${usedReward.toFixed(2)}`)
      } else {
        // 客户端：使用数据库服务
        const usedRewardRecords = await db.getRewardUsageRecords(referrerId, factoryId)
        usedReward = usedRewardRecords
          .filter(record => record.status === 'approved')
          .reduce((sum, record) => sum + (record.amount || 0), 0)
        console.log(`💰 已使用奖励查询成功 (客户端): ¥${usedReward.toFixed(2)}`)
      }
    } catch (error) {
      console.error('❌ 获取已使用奖励失败:', error)
      usedReward = 0
    }

    // 🔧 修正：可用奖励 = 理论可用奖励 - 已使用奖励
    const actualAvailableReward = Math.max(0, availableReward - usedReward)

    const result = {
      totalReward,
      availableReward: actualAvailableReward,
      pendingReward: totalReward - availableReward,
      usedReward,
      settledClients,
      totalClients: referredClients.length,
      details
    }

    console.log('✅ 按比例奖励计算完成:', {
      总奖励: totalReward.toFixed(2),
      理论可用奖励: availableReward.toFixed(2),
      已使用奖励: usedReward.toFixed(2),
      实际可用奖励: actualAvailableReward.toFixed(2),
      待结算奖励: result.pendingReward.toFixed(2),
      结清客户: `${settledClients}/${referredClients.length}`
    })

    return result
  } catch (error) {
    console.error('❌ 计算按比例奖励失败:', error)
    return {
      totalReward: 0,
      availableReward: 0,
      pendingReward: 0,
      usedReward: 0,
      settledClients: 0,
      totalClients: 0,
      details: []
    }
  }
}

/**
 * 格式化奖励详情为可读文本
 * @param result 奖励计算结果
 * @returns 格式化的奖励详情
 */
export const formatRewardDetails = (result: RewardCalculationResult): string => {
  const details: string[] = []
  
  if (result.regularVentReward > 0) {
    const rate = (result.breakdown.regularVentRate * 100).toFixed(1)
    const formattedAmount = Number(result.breakdown.regularVentAmount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    details.push(`普通风口奖励: ¥${formattedAmount} × ${rate}% = ¥${result.regularVentReward.toFixed(2)}`)
  }

  if (result.breakdown.premiumVentDetails.length > 0) {
    details.push('高端风口奖励:')
    result.breakdown.premiumVentDetails.forEach(detail => {
      const formattedOrderAmount = Number(detail.orderAmount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      details.push(`  ${detail.tier}: ¥${formattedOrderAmount} → ¥${detail.rewardAmount}`)
    })
  }
  
  details.push(`总奖励: ¥${result.totalReward.toFixed(2)}`)
  
  return details.join('\n')
}

console.log('✅ 奖励计算工具初始化完成')
