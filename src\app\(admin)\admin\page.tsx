"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { AdminRouteGuard } from "@/components/auth/route-guard"

export default function AdminPage() {
  const router = useRouter()

  useEffect(() => {
    // 自动重定向到管理员仪表板
    router.replace('/admin/dashboard')
  }, [router])

  return (
    <AdminRouteGuard>
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在跳转到管理员仪表板...</p>
        </div>
      </div>
    </AdminRouteGuard>
  )
}
