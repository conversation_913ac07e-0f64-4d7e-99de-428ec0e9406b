import { NextRequest, NextResponse } from 'next/server'

// AI分析API端点
export async function POST(request: NextRequest) {
  try {
    console.log('🤖 开始AI文本分析...')

    const body = await request.json()
    const { text, provider = 'qwen', model = 'auto' } = body // 默认使用通义千问

    if (!text || !text.trim()) {
      return NextResponse.json({
        success: false,
        error: '文本内容不能为空'
      }, { status: 400 })
    }

    console.log('📝 接收到文本:', text.substring(0, 100) + '...')
    console.log('🔧 使用AI服务:', provider, '模型:', model)

    let analysisResult: any

    // 根据provider选择对应的AI服务
    if (provider === 'qwen') {
      // 使用通义千问API进行分析
      console.log('🚀 使用通义千问进行AI分析...')
      const { getQwenAPI } = await import('@/lib/services/qwen-api')
      const { API_KEYS } = await import('@/lib/config/api-keys')

      try {
        console.log('🔧 创建通义千问实例，API Key:', API_KEYS.QWEN.substring(0, 20) + '...')
        const qwen = getQwenAPI(API_KEYS.QWEN)

        console.log('🔧 开始调用通义千问分析...')
        analysisResult = await qwen.analyzeVentOrder(text)
        analysisResult.provider = 'qwen'
        console.log('✅ 通义千问分析成功')
      } catch (qwenError) {
        console.error('❌ 通义千问调用详细错误:', qwenError)
        console.warn('🔄 通义千问API失败，自动切换到DeepSeek官方API:', qwenError)

        // 自动回退到DeepSeek官方API
        const { getDeepSeekAPI } = await import('@/lib/services/deepseek-api')
        const deepSeek = getDeepSeekAPI(API_KEYS.DEEPSEEK)
        // 使用DeepSeek的默认模型，不传递通义千问的模型名称
        analysisResult = await deepSeek.analyzeVentOrder(text)
        analysisResult.provider = 'deepseek-fallback'
        analysisResult.fallbackReason = qwenError instanceof Error ? qwenError.message : '通义千问服务不可用'
      }
    } else if (provider === 'doubao') {
      // 使用豆包AI进行分析
      console.log('🚀 使用豆包AI进行AI分析...')
      const { getDoubaoAPI } = await import('@/lib/services/doubao-api')
      const { API_KEYS } = await import('@/lib/config/api-keys')

      try {
        console.log('🔧 创建豆包AI实例，API Key:', API_KEYS.DOUBAO.substring(0, 20) + '...')
        const doubao = getDoubaoAPI(API_KEYS.DOUBAO)

        console.log('🔧 开始调用豆包AI分析...')
        analysisResult = await doubao.analyzeVentOrder(text)
        analysisResult.provider = 'doubao'
        console.log('✅ 豆包AI分析成功')
      } catch (doubaoError) {
        console.error('❌ 豆包AI调用详细错误:', doubaoError)
        console.warn('🔄 豆包AI失败，自动切换到DeepSeek官方API:', doubaoError)

        // 自动回退到DeepSeek官方API
        const { getDeepSeekAPI } = await import('@/lib/services/deepseek-api')
        const deepSeek = getDeepSeekAPI(API_KEYS.DEEPSEEK)
        // 使用DeepSeek的默认模型，不传递豆包的模型名称
        analysisResult = await deepSeek.analyzeVentOrder(text)
        analysisResult.provider = 'deepseek-fallback'
        analysisResult.fallbackReason = doubaoError instanceof Error ? doubaoError.message : '豆包AI服务不可用'
      }
    } else if (provider === 'siliconflow') {
      // 使用硅基流动API进行分析
      console.log('🚀 使用硅基流动进行AI分析...')
      const { getSiliconFlowAPI } = await import('@/lib/services/siliconflow-api')
      const { API_KEYS } = await import('@/lib/config/api-keys')

      const siliconFlow = getSiliconFlowAPI(API_KEYS.SILICONFLOW)
      analysisResult = await siliconFlow.analyzeVentOrder(text)
      analysisResult.provider = 'siliconflow'
    } else {
      // 使用DeepSeek API进行分析
      console.log('🚀 使用DeepSeek进行AI分析...')
      const { getDeepSeekAPI } = await import('@/lib/services/deepseek-api')
      const { API_KEYS } = await import('@/lib/config/api-keys')

      const deepSeek = getDeepSeekAPI(API_KEYS.DEEPSEEK)
      analysisResult = await deepSeek.analyzeVentOrder(text, model)
      analysisResult.provider = 'deepseek'
    }

    console.log('✅ AI分析完成:', analysisResult)

    return NextResponse.json({
      success: true,
      data: analysisResult,
      metadata: {
        provider: analysisResult.provider || provider,
        model: analysisResult.modelUsed || model,
        textLength: text.length,
        processingTime: Date.now(),
        fallbackReason: analysisResult.fallbackReason
      }
    })

  } catch (error) {
    console.error('❌ AI分析失败:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '分析失败',
      details: process.env.NODE_ENV === 'development' ? error : undefined
    }, { status: 500 })
  }
}
