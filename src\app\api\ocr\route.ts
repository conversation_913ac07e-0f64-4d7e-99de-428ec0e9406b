import { NextRequest, NextResponse } from 'next/server'

// 配置 API 路由的请求体大小限制
export const runtime = 'nodejs'
export const maxDuration = 60 // 60秒超时
export const dynamic = 'force-dynamic'

// 配置请求体大小限制
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
    responseLimit: '50mb',
  },
}

// 百度OCR配置
const BAIDU_OCR_CONFIG = {
  API_KEY: 'XYi8m9nIgFvHQ1jOpr20TpAr',
  SECRET_KEY: 'P7ieKMlibFjDu0jXObf2frJ1wx3IXh9g',
  TOKEN_URL: 'https://aip.baidubce.com/oauth/2.0/token',
  OCR_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic',
  OCR_ACCURATE_URL: 'https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic',
}

// Access Token缓存
let cachedToken: string | null = null
let tokenExpireTime: number = 0

// 获取百度API访问令牌
async function getAccessToken(): Promise<string> {
  try {
    const now = Date.now()
    if (cachedToken && now < tokenExpireTime - 5 * 60 * 1000) {
      return cachedToken
    }

    const params = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: BAIDU_OCR_CONFIG.API_KEY,
      client_secret: BAIDU_OCR_CONFIG.SECRET_KEY
    })

    const response = await fetch(BAIDU_OCR_CONFIG.TOKEN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params
    })

    if (!response.ok) {
      throw new Error(`获取token失败: ${response.status}`)
    }

    const data = await response.json()

    if (data.error) {
      throw new Error(`百度API错误: ${data.error}`)
    }

    cachedToken = data.access_token
    tokenExpireTime = now + (data.expires_in * 1000)

    return cachedToken
  } catch (error) {
    throw new Error(`获取访问令牌失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 将文件转换为Base64
async function fileToBase64(file: File): Promise<string> {
  const arrayBuffer = await file.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)
  return buffer.toString('base64')
}

// 调用百度OCR API
async function callOCRAPI(imageBase64: string, useAccurate: boolean): Promise<any> {
  try {
    const accessToken = await getAccessToken()
    const ocrUrl = useAccurate ? BAIDU_OCR_CONFIG.OCR_ACCURATE_URL : BAIDU_OCR_CONFIG.OCR_URL

    const params = new URLSearchParams({
      access_token: accessToken
    })

    const formData = new URLSearchParams({
      image: imageBase64,
      language_type: 'CHN_ENG',
      detect_direction: 'true',
      paragraph: 'false',
      probability: 'false'
    })

    const response = await fetch(`${ocrUrl}?${params}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: formData
    })

    if (!response.ok) {
      throw new Error(`OCR API调用失败: ${response.status}`)
    }

    const result = await response.json()

    if (result.error_code) {
      throw new Error(`百度OCR错误: ${result.error_code} - ${result.error_msg}`)
    }

    return result
  } catch (error) {
    throw error
  }
}

/**
 * OCR文字识别API路由
 * POST /api/ocr
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔍 收到OCR识别请求')

    // 解析表单数据
    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const useAccurate = formData.get('useAccurate') === 'true'

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: '未收到图片文件' },
        { status: 400 }
      )
    }

    console.log(`📸 开始处理 ${files.length} 个文件，使用${useAccurate ? '高精度' : '通用'}识别`)

    // 验证文件
    for (const file of files) {
      if (!file.type.startsWith('image/')) {
        return NextResponse.json(
          { success: false, error: `文件 ${file.name} 不是图片格式` },
          { status: 400 }
        )
      }
      // 增加文件大小限制到8MB，因为前端已经压缩过
      if (file.size > 8 * 1024 * 1024) {
        return NextResponse.json(
          { success: false, error: `文件 ${file.name} 大小超过8MB限制，请压缩后重试` },
          { status: 413 } // 使用413状态码表示请求实体过大
        )
      }
      console.log(`📊 文件信息: ${file.name}, 大小: ${(file.size / 1024 / 1024).toFixed(2)}MB`)
    }

    // 执行真实的OCR识别
    const results = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      console.log(`📸 处理第${i + 1}/${files.length}个文件: ${file.name}`)

      try {
        // 转换为Base64
        const imageBase64 = await fileToBase64(file)

        // 检查Base64编码后的大小
        const base64Size = imageBase64.length * 0.75 // Base64编码大约增加33%，所以实际大小约为75%
        console.log(`📊 Base64编码后大小: ${(base64Size / 1024 / 1024).toFixed(2)}MB`)

        if (base64Size > 6 * 1024 * 1024) { // 6MB限制
          throw new Error(`图片编码后过大 (${(base64Size / 1024 / 1024).toFixed(2)}MB)，请使用更小的图片`)
        }

        // 调用OCR API
        const ocrResult = await callOCRAPI(imageBase64, useAccurate)

        // 处理识别结果
        const words = ocrResult.words_result || []
        const allText = words.map((item: any) => item.words).join('\n')

        results.push({
          success: true,
          text: allText,
          words: words.map((item: any) => ({
            words: item.words,
            location: item.location || { left: 0, top: 0, width: 0, height: 0 }
          }))
        })

        console.log(`✅ 文件 ${file.name} 识别成功: ${words.length}行文字`)

        // 添加延迟避免API调用过于频繁
        if (i < files.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }

      } catch (error) {
        console.error(`❌ 文件 ${file.name} 识别失败:`, error)
        results.push({
          success: false,
          text: '',
          words: [],
          error: error instanceof Error ? error.message : '识别失败'
        })
      }
    }

    // 合并所有成功识别的文本
    const mergedText = results
      .filter(r => r.success && r.text.trim())
      .map(r => r.text.trim())
      .join('\n\n')

    // 统计识别结果
    const successCount = results.filter(r => r.success).length
    const failCount = results.length - successCount

    console.log(`✅ OCR识别完成: 成功 ${successCount}/${results.length}`)

    return NextResponse.json({
      success: true,
      data: {
        results,
        mergedText,
        statistics: {
          total: results.length,
          success: successCount,
          failed: failCount,
          totalWords: results.reduce((sum, r) => sum + (r.words?.length || 0), 0),
          totalChars: mergedText.length
        }
      }
    })

  } catch (error) {
    console.error('❌ OCR API处理失败:', error)

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '服务器内部错误'
      },
      { status: 500 }
    )
  }
}

/**
 * 检查OCR服务状态
 * GET /api/ocr
 */
export async function GET() {
  try {
    // 这里可以添加服务状态检查逻辑
    return NextResponse.json({
      success: true,
      status: 'available',
      message: '百度OCR服务可用'
    })
  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        status: 'unavailable',
        error: error instanceof Error ? error.message : '服务不可用' 
      },
      { status: 500 }
    )
  }
}
