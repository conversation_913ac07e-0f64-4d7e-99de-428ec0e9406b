/*
  Warnings:

  - You are about to drop the column `dividend_amount` on the `dividends` table. All the data in the column will be lost.
  - You are about to drop the column `paid_at` on the `dividends` table. All the data in the column will be lost.
  - You are about to drop the column `period` on the `dividends` table. All the data in the column will be lost.
  - You are about to drop the column `share_percent` on the `dividends` table. All the data in the column will be lost.
  - You are about to drop the column `shareholder_id` on the `dividends` table. All the data in the column will be lost.
  - You are about to drop the column `total_profit` on the `dividends` table. All the data in the column will be lost.
  - You are about to drop the column `order_id` on the `reward_usages` table. All the data in the column will be lost.
  - You are about to drop the column `processed_at` on the `reward_usages` table. All the data in the column will be lost.
  - You are about to drop the column `reward_amount` on the `reward_usages` table. All the data in the column will be lost.
  - You are about to drop the column `used_amount` on the `reward_usages` table. All the data in the column will be lost.
  - You are about to drop the column `invest_amount` on the `shareholders` table. All the data in the column will be lost.
  - You are about to drop the column `is_active` on the `shareholders` table. All the data in the column will be lost.
  - You are about to drop the column `share_percent` on the `shareholders` table. All the data in the column will be lost.
  - Added the required column `base_amount` to the `dividends` table without a default value. This is not possible if the table is not empty.
  - Added the required column `created_by` to the `dividends` table without a default value. This is not possible if the table is not empty.
  - Added the required column `dividend_rate` to the `dividends` table without a default value. This is not possible if the table is not empty.
  - Added the required column `period_end` to the `dividends` table without a default value. This is not possible if the table is not empty.
  - Added the required column `period_start` to the `dividends` table without a default value. This is not possible if the table is not empty.
  - Added the required column `title` to the `dividends` table without a default value. This is not possible if the table is not empty.
  - Added the required column `total_amount` to the `dividends` table without a default value. This is not possible if the table is not empty.
  - Added the required column `amount` to the `reward_usages` table without a default value. This is not possible if the table is not empty.
  - Added the required column `created_by` to the `reward_usages` table without a default value. This is not possible if the table is not empty.
  - Added the required column `created_by` to the `shareholders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `investment_amount` to the `shareholders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `join_date` to the `shareholders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `share_count` to the `shareholders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `share_percentage` to the `shareholders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `shareholder_type` to the `shareholders` table without a default value. This is not possible if the table is not empty.

*/
-- CreateTable
CREATE TABLE "dividend_records" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "dividend_id" TEXT NOT NULL,
    "shareholder_id" TEXT NOT NULL,
    "amount" REAL NOT NULL,
    "percentage" REAL NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "paid_at" DATETIME,
    "notes" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "dividend_records_dividend_id_fkey" FOREIGN KEY ("dividend_id") REFERENCES "dividends" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "dividend_records_shareholder_id_fkey" FOREIGN KEY ("shareholder_id") REFERENCES "shareholders" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_dividends" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "factory_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "total_amount" REAL NOT NULL,
    "base_amount" REAL NOT NULL,
    "dividend_rate" REAL NOT NULL,
    "period_start" DATETIME NOT NULL,
    "period_end" DATETIME NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "approved_at" DATETIME,
    "distributed_at" DATETIME,
    "created_by" TEXT NOT NULL,
    "approved_by" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "dividends_factory_id_fkey" FOREIGN KEY ("factory_id") REFERENCES "factories" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_dividends" ("created_at", "factory_id", "id", "status", "updated_at") SELECT "created_at", "factory_id", "id", "status", "updated_at" FROM "dividends";
DROP TABLE "dividends";
ALTER TABLE "new_dividends" RENAME TO "dividends";
CREATE TABLE "new_reward_usages" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "factory_id" TEXT NOT NULL,
    "client_id" TEXT NOT NULL,
    "related_order_id" TEXT,
    "usage_type" TEXT NOT NULL,
    "amount" REAL NOT NULL,
    "description" TEXT,
    "payment_method" TEXT,
    "payment_reference" TEXT,
    "paid_at" DATETIME,
    "notes" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "approved_by" TEXT,
    "approved_at" DATETIME,
    "created_by" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "reward_usages_factory_id_fkey" FOREIGN KEY ("factory_id") REFERENCES "factories" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "reward_usages_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "clients" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "reward_usages_related_order_id_fkey" FOREIGN KEY ("related_order_id") REFERENCES "orders" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
INSERT INTO "new_reward_usages" ("client_id", "created_at", "factory_id", "id", "status", "updated_at", "usage_type") SELECT "client_id", "created_at", "factory_id", "id", "status", "updated_at", "usage_type" FROM "reward_usages";
DROP TABLE "reward_usages";
ALTER TABLE "new_reward_usages" RENAME TO "reward_usages";
CREATE TABLE "new_shareholders" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "factory_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "id_card" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "address" TEXT,
    "shareholder_type" TEXT NOT NULL,
    "share_count" INTEGER NOT NULL,
    "share_percentage" REAL NOT NULL,
    "investment_amount" REAL NOT NULL,
    "join_date" DATETIME NOT NULL,
    "exit_date" DATETIME,
    "status" TEXT NOT NULL DEFAULT 'active',
    "notes" TEXT,
    "created_by" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "shareholders_factory_id_fkey" FOREIGN KEY ("factory_id") REFERENCES "factories" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_shareholders" ("created_at", "factory_id", "id", "name", "updated_at") SELECT "created_at", "factory_id", "id", "name", "updated_at" FROM "shareholders";
DROP TABLE "shareholders";
ALTER TABLE "new_shareholders" RENAME TO "shareholders";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
