/**
 * 🇨🇳 风口云平台 - 工厂管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware/auth'

// 获取工厂列表
export const GET = withAdminAuth(async (request: NextRequest, user) => {
  try {
    console.log('📋 获取工厂列表')

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // 获取工厂列表
    const factories = await db.getFactories({
      status: status as any,
      limit,
      offset
    })

    console.log('✅ 获取工厂列表成功:', factories.length)
    console.log('🔍 原始工厂数据示例:', factories[0] ? {
      id: factories[0].id,
      name: factories[0].name,
      subscriptionType: factories[0].subscriptionType,
      subscriptionEnd: factories[0].subscriptionEnd
    } : '无数据')

    // 处理BigInt字段，转换为字符串以避免JSON序列化错误
    const serializedFactories = JSON.parse(JSON.stringify(factories, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    ))

    console.log('🔍 序列化后工厂数据长度:', serializedFactories.length)
    console.log('🔍 序列化后工厂数据示例:', serializedFactories[0] ? {
      id: serializedFactories[0].id,
      name: serializedFactories[0].name,
      subscriptionType: serializedFactories[0].subscriptionType,
      subscriptionEnd: serializedFactories[0].subscriptionEnd
    } : '无数据')

    return NextResponse.json({
      success: true,
      factories: serializedFactories,
      pagination: {
        limit,
        offset,
        total: factories.length
      }
    })

  } catch (error) {
    console.error('❌ 获取工厂列表失败:', error)
    return NextResponse.json(
      { error: '获取工厂列表失败' },
      { status: 500 }
    )
  }
})

// 创建新工厂
export const POST = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const factoryData = await request.json()

    console.log('🏭 创建新工厂:', factoryData.name)

    // 创建工厂
    const factory = await db.createFactory(factoryData)

    if (!factory) {
      return NextResponse.json(
        { error: '创建工厂失败' },
        { status: 500 }
      )
    }

    console.log('✅ 工厂创建成功:', factory.id)

    return NextResponse.json({
      success: true,
      factory,
      message: '工厂创建成功'
    })

  } catch (error) {
    console.error('❌ 创建工厂失败:', error)
    return NextResponse.json(
      { error: '创建工厂失败' },
      { status: 500 }
    )
  }
})
