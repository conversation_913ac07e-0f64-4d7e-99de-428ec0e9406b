# 🏭 项目所有风口款式用料计算公式总结

## 📋 文档说明
本文档基于PDF培训文件和Excel模板分析，总结项目中所有风口款式的材料用量计算公式。

## 🎯 一、风口分类体系

### 1.1 常规风口系列（按面积计价 元/㎡）
| 系统代码 | 中文名称 | 材料构成 | 计价方式 |
|---------|---------|---------|---------|
| `double_white_outlet` | 双层白色出风口 | 外框+卡座+弯叶+菱形叶 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `double_black_outlet` | 双层黑色出风口 | 外框+卡座+弯叶+菱形叶 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `white_black_bottom_outlet` | 白色黑底出风口 | 外框+卡座+弯叶+菱形叶 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `white_black_bottom_return` | 白色黑底回风口 | 外框+内框+卡座+斜叶 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `white_return` | 白色回风口 | 外框+内框+卡座+斜叶 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `black_return` | 黑色回风口 | 外框+内框+卡座+斜叶 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `white_linear` | 白色线型风口 | 外框+卡座+线型叶片 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `black_linear` | 黑色线型风口 | 外框+卡座+线型叶片 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `white_linear_return` | 白色线型回风口 | 外框+卡座+线型叶片 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `black_linear_return` | 黑色线型回风口 | 外框+卡座+线型叶片 | (长+60)×(宽+60)÷1,000,000×单价×数量 |
| `maintenance` | 检修口 | 外框+卡座+线型叶片 | (长+60)×(宽+60)÷1,000,000×单价×数量 |

### 1.2 高端风口系列（按长度计价 元/m）
| 系统代码 | 中文名称 | 材料构成 | 计价方式 |
|---------|---------|---------|---------|
| `arrow_outlet` | 箭型出风口 | 外框+卡座+线型叶片 | (长+120)÷1,000×单价×数量 |
| `arrow_return` | 箭型回风口 | 外框+内框+卡座+斜叶 | (长+120)÷1,000×单价×数量 |
| `claw_outlet` | 爪型出风口 | 外框+卡座+线型叶片 | (长+120)÷1,000×单价×数量 |
| `claw_return` | 爪型回风口 | 外框+内框+卡座+斜叶 | (长+120)÷1,000×单价×数量 |
| `black_white_dual_outlet` | 黑白双色出风口 | 外框+卡座+线型叶片 | (长+120)÷1,000×单价×数量 |
| `black_white_dual_return` | 黑白双色回风口 | 外框+内框+卡座+斜叶 | (长+120)÷1,000×单价×数量 |
| `wood_grain_outlet` | 木纹出风口 | 外框+卡座+线型叶片 | (长+120)÷1,000×单价×数量 |
| `wood_grain_return` | 木纹回风口 | 外框+内框+卡座+斜叶 | (长+120)÷1,000×单价×数量 |
| `white_putty_outlet` | 白色腻子粉出风口 | 外框+卡座+线型叶片 | (长+120)÷1,000×单价×数量 |
| `white_putty_return` | 白色腻子粉回风口 | 外框+内框+卡座+斜叶 | (长+120)÷1,000×单价×数量 |
| `black_putty_outlet` | 黑色腻子粉出风口 | 外框+卡座+线型叶片 | (长+120)÷1,000×单价×数量 |
| `black_putty_return` | 黑色腻子粉回风口 | 外框+内框+卡座+斜叶 | (长+120)÷1,000×单价×数量 |
| `white_gypsum_outlet` | 白色石膏板出风口 | 外框+卡座+线型叶片 | (长+120)÷1,000×单价×数量 |
| `white_gypsum_return` | 白色石膏板回风口 | 外框+内框+卡座+斜叶 | (长+120)÷1,000×单价×数量 |
| `black_gypsum_outlet` | 黑色石膏板出风口 | 外框+卡座+线型叶片 | (长+120)÷1,000×单价×数量 |
| `black_gypsum_return` | 黑色石膏板回风口 | 外框+内框+卡座+斜叶 | (长+120)÷1,000×单价×数量 |

## 🔧 二、材料用量计算公式体系

### 2.1 双层出风口系列用料计算
**适用类型**：`double_white_outlet`、`double_black_outlet`、`white_black_bottom_outlet`

**材料构成**：外框 + 卡座 + 弯叶片 + 菱形叶片

#### 📏 尺寸计算公式
```javascript
// 基础参数
const 风口安装尺寸 = 10  // mm
const 叶片离边框距离 = 2.5  // mm
const 菱形叶片间距 = 30  // mm
const 卡座间距 = 22  // mm

// 尺寸计算
外框长度 = 洞口长 - 10
外框宽度 = 洞口宽 - 10
卡座长度 = 洞口宽 - 15
菱形叶片长度 = 洞口宽 - 15
弯叶片长度 = 洞口长 - 20

// 数量计算
卡座数量 = Math.floor(洞口长 ÷ 200)
菱形叶片数量 = Math.floor(洞口长 ÷ 30) - 卡座数量
弯叶片数量 = Math.floor(卡座长度 ÷ 22)
```

#### 🧮 材料用量计算
```javascript
// 双层出风口总用料
外框总长度 = (外框长度 + 外框宽度) × 2
卡座总长度 = 卡座数量 × 卡座长度
菱形叶片总长度 = 菱形叶片数量 × 菱形叶片长度
弯叶片总长度 = 弯叶片数量 × 弯叶片长度

// 总材料用量
总用料长度 = 外框总长度 + 卡座总长度 + 菱形叶片总长度 + 弯叶片总长度
```

### 2.2 回风口系列用料计算
**适用类型**：`white_return`、`black_return`、`white_black_bottom_return`、所有高端回风口

**材料构成**：外框 + 内框 + 回风卡座 + 回风斜叶片

#### 📏 尺寸计算公式
```javascript
// 基础参数（T32×21内框）
const T32_21_内框安装尺寸 = 13  // mm
const T32_21_内外框组装间隙 = 1  // mm
const 回风卡座间距 = 25  // mm

// 基础参数（34×22内框）
const 34_22_内框安装尺寸 = 13.5  // mm

// 尺寸计算（T32×21内框）
外框长度 = 洞口长 - 10
外框宽度 = 洞口宽 - 10
内框长度 = 洞口长 - 41
内框宽度 = 洞口宽 - 41
回风卡座长度 = 洞口宽 - 46
回风斜叶片长度 = 洞口长 - 48

// 尺寸计算（34×22内框）
内框长度 = 洞口长 - 44
内框宽度 = 洞口宽 - 44
回风卡座长度 = 洞口宽 - 49
回风斜叶片长度 = 洞口长 - 50

// 数量计算
回风卡座数量 = Math.floor(洞口长 ÷ 250)
回风斜叶片数量 = Math.floor(回风卡座长度 ÷ 22)
铰链数量 = 回风卡座数量
碰珠数量 = 回风卡座数量
```

#### 🧮 材料用量计算
```javascript
// 回风口总用料
外框总长度 = (外框长度 + 外框宽度) × 2
内框总长度 = (内框长度 + 内框宽度) × 2
回风卡座总长度 = 回风卡座数量 × 回风卡座长度
回风斜叶片总长度 = 回风斜叶片数量 × 回风斜叶片长度

// 总材料用量
总用料长度 = 外框总长度 + 内框总长度 + 回风卡座总长度 + 回风斜叶片总长度
```

### 2.3 线型风口系列用料计算
**适用类型**：`white_linear`、`black_linear`、`white_linear_return`、`black_linear_return`、所有高端线型风口

**材料构成**：外框 + 卡座 + 线型叶片

#### 📏 尺寸计算公式
```javascript
// 基础参数
const 线型卡座间距 = 15  // mm

// 普通线型风口
外框长度 = 洞口长 - 10
外框宽度 = 洞口宽 - 10
卡座长度 = 洞口宽 - 15
线型叶片长度 = 洞口长 - 18

// 黑白双色线型风口
卡座长度 = 洞口宽 - 11
线型叶片长度 = 洞口长 - 14

// 预埋腻子线型风口
外框长度 = 洞口长 - 7
外框宽度 = 洞口宽 - 7
卡座长度 = 洞口宽 - 13
线型叶片长度 = 洞口长 - 16

// 预埋石膏板线型风口
外框长度 = 洞口长 + 23
外框宽度 = 洞口宽 + 23
卡座长度 = 洞口宽 - 15
线型叶片长度 = 洞口长 - 18

// 数量计算
卡座数量 = Math.floor(洞口长 ÷ 200)
线型叶片数量 = 根据具体规格计算（取整数部分）
```

#### 🧮 材料用量计算
```javascript
// 线型风口总用料
外框总长度 = (外框长度 + 外框宽度) × 2
卡座总长度 = 卡座数量 × 卡座长度
线型叶片总长度 = 线型叶片数量 × 线型叶片长度

// 总材料用量
总用料长度 = 外框总长度 + 卡座总长度 + 线型叶片总长度
```

### 2.4 检修口用料计算
**适用类型**：`maintenance`

**材料构成**：外框 + 卡座 + 线型叶片（简化结构）

#### 📏 尺寸计算公式
```javascript
// 检修口（简化结构）
外框长度 = 洞口长 - 10
外框宽度 = 洞口宽 - 10
卡座长度 = 洞口宽 - 15
线型叶片长度 = 洞口长 - 18

// 数量计算（简化）
卡座数量 = Math.floor(洞口长 ÷ 200)
线型叶片数量 = Math.floor(卡座长度 ÷ 22)
```

## 🎯 三、快速计算对照表

| 风口分类 | 外框计算 | 卡座计算 | 叶片计算 | 卡座间距 | 特殊说明 |
|---------|---------|---------|---------|---------|---------|
| **双层出风口** | 洞口-10 | 洞口宽-15 | 洞口长-20 | 22mm | 菱形叶片：洞口宽-15 |
| **回风口(T32×21)** | 洞口-10 | 洞口宽-46 | 洞口长-48 | 25mm | 内框：洞口-41 |
| **回风口(34×22)** | 洞口-10 | 洞口宽-49 | 洞口长-50 | 25mm | 内框：洞口-44 |
| **普通线型** | 洞口-10 | 洞口宽-15 | 洞口长-18 | 15mm | 简化结构 |
| **黑白双色线型** | 洞口-10 | 洞口宽-11 | 洞口长-14 | 15mm | 高端产品 |
| **预埋腻子线型** | 洞口-7 | 洞口宽-13 | 洞口长-16 | 15mm | 预埋安装 |
| **预埋石膏板线型** | 洞口+23 | 洞口宽-15 | 洞口长-18 | 15mm | 特殊安装 |
| **检修口** | 洞口-10 | 洞口宽-15 | 洞口长-18 | 22mm | 简化结构 |

## 🔩 四、通用计算函数

### 4.1 JavaScript实现
```javascript
// 通用风口用料计算函数
function calculateVentMaterials(ventType, holeLength, holeWidth) {
  const result = {
    frameLength: 0,
    seatLength: 0,
    bladeLength: 0,
    frameCount: 1,
    seatCount: 0,
    bladeCount: 0,
    totalLength: 0
  }
  
  // 根据风口类型选择计算方式
  if (ventType.includes('outlet') && !ventType.includes('linear')) {
    // 双层出风口
    result.frameLength = (holeLength - 10 + holeWidth - 10) * 2
    result.seatLength = Math.floor(holeLength / 200) * (holeWidth - 15)
    result.bladeLength = Math.floor((holeWidth - 15) / 22) * (holeLength - 20)

  } else if (ventType.includes('return')) {
    // 回风口
    result.frameLength = (holeLength - 10 + holeWidth - 10) * 2
    const innerFrameLength = (holeLength - 44 + holeWidth - 44) * 2
    result.seatLength = Math.floor(holeLength / 250) * (holeWidth - 49)
    result.bladeLength = Math.floor((holeWidth - 49) / 22) * (holeLength - 50)
    result.frameLength += innerFrameLength

  } else if (ventType.includes('linear')) {
    // 线型风口
    result.frameLength = (holeLength - 10 + holeWidth - 10) * 2
    result.seatLength = Math.floor(holeLength / 200) * (holeWidth - 15)
    result.bladeLength = holeLength - 18

  } else if (ventType === 'maintenance') {
    // 检修口
    result.frameLength = (holeLength - 10 + holeWidth - 10) * 2
    result.seatLength = Math.floor(holeLength / 200) * (holeWidth - 15)
    result.bladeLength = holeLength - 18
  }
  
  result.totalLength = result.frameLength + result.seatLength + result.bladeLength
  return result
}
```

---

## 📝 总结

本文档涵盖了项目中所有27种风口款式的完整用料计算公式，包括：
- **11种常规风口**（按面积计价）
- **16种高端风口**（按长度计价）

每种风口都有明确的材料构成、尺寸计算公式和用量计算方法，为实际生产提供了完整的技术标准。

**生成时间**：2025年1月6日  
**数据来源**：项目代码分析 + PDF培训文件 + Excel模板验证
