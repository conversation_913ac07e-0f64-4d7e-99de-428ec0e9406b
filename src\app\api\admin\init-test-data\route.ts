/**
 * 🇨🇳 风口云平台 - 初始化测试数据API
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 手动触发数据库初始化...')
    
    // 重新初始化数据库
    await db.initializeDatabase()
    
    return NextResponse.json({
      success: true,
      message: '测试数据初始化成功'
    })
  } catch (error) {
    console.error('❌ 初始化测试数据失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: '初始化失败',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}
