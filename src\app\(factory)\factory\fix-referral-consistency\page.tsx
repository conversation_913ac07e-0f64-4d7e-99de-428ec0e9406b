'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface FixResult {
  success: boolean
  message?: string
  summary?: {
    totalClients: number
    inconsistentCount: number
    fixedCount: number
    consistentCount: number
  }
  details?: Array<{
    clientId: string
    clientName: string
    clientPhone: string
    status: 'fixed' | 'consistent' | 'error'
    before?: unknown
    after?: unknown
    current?: unknown
    referredClients?: unknown[]
    error?: string
  }>
}

export default function FixReferralConsistencyPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<FixResult | null>(null)
  const [error, setError] = useState('')

  const fixConsistency = async () => {
    setLoading(true)
    setError('')
    setResult(null)

    try {
      console.log('🔧 开始修复推荐数据一致性')

      const response = await fetch('/api/fix-referral-data-consistency', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
        console.log('✅ 修复完成:', data)
      } else {
        setError(data.error || '修复失败')
      }

    } catch (err) {
      console.error('❌ 修复请求失败:', err)
      setError(err instanceof Error ? err.message : '修复请求失败')
    } finally {
      setLoading(false)
    }
  }

  const formatAmount = (amount: number | undefined) => {
    return `¥${(amount || 0).toFixed(2)}`
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'fixed':
        return <Badge variant="destructive">已修复</Badge>
      case 'consistent':
        return <Badge variant="secondary">数据一致</Badge>
      case 'error':
        return <Badge variant="outline">错误</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🔧 推荐数据一致性修复工具</CardTitle>
          <p className="text-sm text-gray-600">
            修复推荐客户数据和奖励数据不一致的问题，确保客户查询界面显示正确的推荐信息。
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 p-4 rounded">
            <h4 className="font-medium text-yellow-800 mb-2">⚠️ 使用说明</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• 此工具会检查所有客户的推荐关系和奖励数据</li>
              <li>• 自动修复数据不一致的问题</li>
              <li>• 重新计算推荐数量和奖励金额</li>
              <li>• 建议在发现推荐数据问题时使用</li>
            </ul>
          </div>

          <Button onClick={fixConsistency} disabled={loading} className="w-full">
            {loading ? '修复中...' : '开始修复推荐数据一致性'}
          </Button>

          {error && (
            <div className="text-red-600 bg-red-50 p-3 rounded">
              ❌ {error}
            </div>
          )}
        </CardContent>
      </Card>

      {result && (
        <div className="space-y-6">
          {/* 修复摘要 */}
          <Card>
            <CardHeader>
              <CardTitle>📊 修复摘要</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded">
                  <div className="text-2xl font-bold text-blue-600">
                    {result.summary?.totalClients || 0}
                  </div>
                  <div className="text-sm text-gray-600">总客户数</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded">
                  <div className="text-2xl font-bold text-red-600">
                    {result.summary?.inconsistentCount || 0}
                  </div>
                  <div className="text-sm text-gray-600">数据不一致</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded">
                  <div className="text-2xl font-bold text-green-600">
                    {result.summary?.fixedCount || 0}
                  </div>
                  <div className="text-sm text-gray-600">已修复</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded">
                  <div className="text-2xl font-bold text-gray-600">
                    {result.summary?.consistentCount || 0}
                  </div>
                  <div className="text-sm text-gray-600">数据一致</div>
                </div>
              </div>
              
              {result.message && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                  ✅ {result.message}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 详细结果 */}
          {result.details && result.details.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>📋 详细修复结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {result.details.map((detail, index) => (
                    <div key={detail.clientId || index} className="border rounded p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium">{detail.clientName}</h4>
                          <p className="text-sm text-gray-600">{detail.clientPhone}</p>
                        </div>
                        {getStatusBadge(detail.status)}
                      </div>

                      {detail.status === 'fixed' && detail.before && detail.after && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                          <div className="bg-red-50 p-3 rounded">
                            <h5 className="font-medium text-red-800 mb-2">修复前</h5>
                            <div className="text-sm space-y-1">
                              <div>推荐数量: {detail.before.referralCount}</div>
                              <div>推荐奖励: {formatAmount(detail.before.referralReward)}</div>
                              <div>可用奖励: {formatAmount(detail.before.availableReward)}</div>
                            </div>
                          </div>
                          <div className="bg-green-50 p-3 rounded">
                            <h5 className="font-medium text-green-800 mb-2">修复后</h5>
                            <div className="text-sm space-y-1">
                              <div>推荐数量: {detail.after.referralCount}</div>
                              <div>推荐奖励: {formatAmount(detail.after.referralReward)}</div>
                              <div>可用奖励: {formatAmount(detail.after.availableReward)}</div>
                              <div>待结算: {formatAmount(detail.after.pendingReward)}</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {detail.status === 'consistent' && detail.current && (
                        <div className="bg-gray-50 p-3 rounded mt-3">
                          <h5 className="font-medium text-gray-800 mb-2">当前数据</h5>
                          <div className="text-sm space-y-1">
                            <div>推荐数量: {detail.current.referralCount}</div>
                            <div>推荐奖励: {formatAmount(detail.current.referralReward)}</div>
                            <div>可用奖励: {formatAmount(detail.current.availableReward)}</div>
                          </div>
                        </div>
                      )}

                      {detail.status === 'error' && detail.error && (
                        <div className="bg-red-50 p-3 rounded mt-3">
                          <h5 className="font-medium text-red-800 mb-2">错误信息</h5>
                          <p className="text-sm text-red-700">{detail.error}</p>
                        </div>
                      )}

                      {detail.referredClients && detail.referredClients.length > 0 && (
                        <div className="mt-3">
                          <h5 className="font-medium mb-2">推荐的客户 ({detail.referredClients.length}个)</h5>
                          <div className="space-y-2">
                            {detail.referredClients.map((referred, idx) => (
                              <div key={idx} className="bg-gray-50 p-2 rounded text-sm">
                                <div className="flex justify-between">
                                  <span>{referred.clientName} ({referred.clientPhone})</span>
                                  <span>订单: {referred.orderCount}个, 奖励: {formatAmount(referred.rewardAmount)}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}
