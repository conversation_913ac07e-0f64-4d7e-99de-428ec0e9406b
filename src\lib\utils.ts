import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number | string | null | undefined): string {
  // 安全的数值转换
  const num = typeof amount === 'number' ? amount :
              typeof amount === 'string' ? parseFloat(amount) || 0 : 0

  if (num === 0) return '¥0.0'
  if (isNaN(num)) return '¥0.0'

  // 🔧 修改为一位小数精度，四舍五入
  const rounded = Math.round(num * 10) / 10
  const formatted = rounded.toFixed(1)
  // 添加千分位分隔符和货币符号
  return '¥' + formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export function formatDate(date: Date | string): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(new Date(date))
}

export function formatDateTime(date: Date | string): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}
