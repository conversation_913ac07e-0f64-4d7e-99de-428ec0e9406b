/**
 * 🇨🇳 风口云平台 - 数据同步事件API
 */

import { NextRequest, NextResponse } from 'next/server'
import { dataSyncService } from '@/lib/services/data-sync'

export async function GET(request: NextRequest) {
  try {
    console.log('📋 获取数据同步事件...')

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const factoryId = searchParams.get('factoryId')
    const eventType = searchParams.get('type')

    // 获取所有同步事件
    let syncEvents = dataSyncService.getSyncEvents()

    // 按工厂ID过滤
    if (factoryId) {
      syncEvents = syncEvents.filter(event => event.factoryId === factoryId)
    }

    // 按事件类型过滤
    if (eventType) {
      syncEvents = syncEvents.filter(event => event.type === eventType)
    }

    // 限制返回数量
    const limitedEvents = syncEvents.slice(0, limit)

    // 格式化事件数据
    const formattedEvents = limitedEvents.map(event => ({
      id: event.id,
      type: event.type,
      factoryId: event.factoryId,
      timestamp: event.timestamp,
      synced: event.synced,
      dataPreview: typeof event.data === 'object' ? 
        Object.keys(event.data).join(', ') : 
        String(event.data).substring(0, 100)
    }))

    const response = {
      success: true,
      data: {
        events: formattedEvents,
        total: syncEvents.length,
        filtered: limitedEvents.length
      },
      message: '数据同步事件获取成功'
    }

    console.log(`✅ 数据同步事件获取成功，返回 ${limitedEvents.length} 个事件`)
    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ 获取数据同步事件失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '获取数据同步事件失败',
      message: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 创建数据同步事件...')

    const body = await request.json()
    const { factoryId, eventType, data } = body

    if (!factoryId || !eventType) {
      return NextResponse.json({
        success: false,
        error: '参数错误',
        message: '缺少必要参数: factoryId, eventType'
      }, { status: 400 })
    }

    // 创建同步事件
    await dataSyncService.syncFactoryDataToHeadquarters(factoryId, eventType, data)

    const response = {
      success: true,
      message: '数据同步事件创建成功'
    }

    console.log('✅ 数据同步事件创建成功')
    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ 创建数据同步事件失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '创建数据同步事件失败',
      message: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
