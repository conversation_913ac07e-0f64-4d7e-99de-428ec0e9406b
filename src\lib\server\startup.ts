/**
 * 🇨🇳 风口云平台 - 服务器启动初始化
 * 
 * 在服务器启动时执行的初始化任务
 */

import { startFactoryStatusScheduler } from '@/lib/tasks/factory-status-checker'

let isInitialized = false

/**
 * 初始化服务器任务
 */
export function initializeServer() {
  if (isInitialized) {
    console.log('⚠️ 服务器已经初始化，跳过重复初始化')
    return
  }

  console.log('🚀 开始初始化服务器任务...')

  try {
    // 启动工厂状态定时检查器
    startFactoryStatusScheduler()

    // 可以在这里添加其他初始化任务
    // 例如：数据库连接检查、缓存预热等

    isInitialized = true
    console.log('✅ 服务器初始化完成')

  } catch (error) {
    console.error('❌ 服务器初始化失败:', error)
    // 不抛出错误，避免影响应用启动
  }
}

/**
 * 检查是否已初始化
 */
export function isServerInitialized(): boolean {
  return isInitialized
}
