'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calculator, CheckCircle, XCircle } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { calculateRegularVentRewardRate, calculateOrderReward } from '@/lib/utils/reward-calculator'

interface TestResult {
  testName: string
  status: 'pass' | 'fail'
  expected: unknown
  actual: unknown
  description: string
}

export default function TestTieredRewardsPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const runTests = async () => {
    setIsRunning(true)
    const results: TestResult[] = []

    try {
      console.log('🧪 开始测试阶梯式奖励计算...')

      // 测试1: 基础档奖励比例 (5万元 -> 2%)
      const rate1 = calculateRegularVentRewardRate(50000, 'test-factory')
      results.push({
        testName: '基础档奖励比例',
        status: Math.abs(rate1 - 0.02) < 0.001 ? 'pass' : 'fail',
        expected: '2%',
        actual: `${(rate1 * 100).toFixed(1)}%`,
        description: '5万元订单应该获得2%奖励'
      })

      // 测试2: 第一档奖励比例 (12万元 -> 3%)
      const rate2 = calculateRegularVentRewardRate(120000, 'test-factory')
      results.push({
        testName: '第一档奖励比例',
        status: Math.abs(rate2 - 0.03) < 0.001 ? 'pass' : 'fail',
        expected: '3%',
        actual: `${(rate2 * 100).toFixed(1)}%`,
        description: '12万元订单应该获得3%奖励'
      })

      // 测试3: 第二档奖励比例 (25万元 -> 4%)
      const rate3 = calculateRegularVentRewardRate(250000, 'test-factory')
      results.push({
        testName: '第二档奖励比例',
        status: Math.abs(rate3 - 0.04) < 0.001 ? 'pass' : 'fail',
        expected: '4%',
        actual: `${(rate3 * 100).toFixed(1)}%`,
        description: '25万元订单应该获得4%奖励'
      })

      // 测试4: 边界值测试 - 正好10万元
      const rate4 = calculateRegularVentRewardRate(100000, 'test-factory')
      results.push({
        testName: '边界值测试(10万)',
        status: Math.abs(rate4 - 0.03) < 0.001 ? 'pass' : 'fail',
        expected: '3%',
        actual: `${(rate4 * 100).toFixed(1)}%`,
        description: '正好10万元应该获得3%奖励'
      })

      // 测试5: 边界值测试 - 正好20万元
      const rate5 = calculateRegularVentRewardRate(200000, 'test-factory')
      results.push({
        testName: '边界值测试(20万)',
        status: Math.abs(rate5 - 0.04) < 0.001 ? 'pass' : 'fail',
        expected: '4%',
        actual: `${(rate5 * 100).toFixed(1)}%`,
        description: '正好20万元应该获得4%奖励'
      })

      // 测试6: 完整订单计算 - 普通风口
      const testOrder1 = {
        items: [
          {
            productType: 'square',
            productName: '普通出风口',
            totalPrice: 150000
          }
        ]
      }
      const orderResult1 = calculateOrderReward(testOrder1, 'test-factory')
      const expectedReward1 = 150000 * 0.03 // 15万元 * 3%
      results.push({
        testName: '普通风口订单计算',
        status: Math.abs(orderResult1.totalReward - expectedReward1) < 1 ? 'pass' : 'fail',
        expected: `¥${expectedReward1.toFixed(2)}`,
        actual: `¥${orderResult1.totalReward.toFixed(2)}`,
        description: '15万元普通风口订单应该获得4500元奖励'
      })

      // 测试7: 混合订单计算
      const testOrder2 = {
        items: [
          {
            productType: 'square',
            productName: '普通出风口',
            totalPrice: 180000 // 18万元，应该是3%
          },
          {
            productType: 'rectangular',
            productName: '黑白双色风口',
            totalPrice: 25000 // 2.5万元高端风口，应该是100元固定奖励
          }
        ]
      }
      const orderResult2 = calculateOrderReward(testOrder2, 'test-factory')
      const expectedRegularReward = 180000 * 0.03 // 5400元
      const expectedPremiumReward = 100 // 100元固定
      const expectedTotal = expectedRegularReward + expectedPremiumReward
      
      results.push({
        testName: '混合订单计算',
        status: Math.abs(orderResult2.totalReward - expectedTotal) < 1 ? 'pass' : 'fail',
        expected: `¥${expectedTotal.toFixed(2)} (普通¥${expectedRegularReward} + 高端¥${expectedPremiumReward})`,
        actual: `¥${orderResult2.totalReward.toFixed(2)} (普通¥${orderResult2.regularVentReward.toFixed(2)} + 高端¥${orderResult2.premiumVentReward.toFixed(2)})`,
        description: '混合订单应该正确分别计算普通和高端风口奖励'
      })

      // 测试8: 大额订单测试
      const rate8 = calculateRegularVentRewardRate(500000, 'test-factory')
      results.push({
        testName: '大额订单测试(50万)',
        status: Math.abs(rate8 - 0.04) < 0.001 ? 'pass' : 'fail',
        expected: '4%',
        actual: `${(rate8 * 100).toFixed(1)}%`,
        description: '50万元订单应该获得4%奖励（最高档）'
      })

    } catch (error) {
      results.push({
        testName: '测试执行',
        status: 'fail',
        expected: '正常执行',
        actual: `错误: ${error.message}`,
        description: '测试执行过程中发生错误'
      })
    }

    setTestResults(results)
    setIsRunning(false)
  }

  const passedTests = testResults.filter(r => r.status === 'pass').length
  const totalTests = testResults.length

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">阶梯式奖励计算测试</h1>
            <p className="text-muted-foreground">验证修复后的奖励计算是否正确使用阶梯式规则</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              测试控制台
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <Button 
                onClick={runTests} 
                disabled={isRunning}
                className="flex items-center gap-2"
              >
                <Calculator className="h-4 w-4" />
                {isRunning ? '测试中...' : '开始测试'}
              </Button>
              
              {testResults.length > 0 && (
                <div className="flex items-center gap-2">
                  <Badge variant={passedTests === totalTests ? "default" : "destructive"}>
                    {passedTests}/{totalTests} 通过
                  </Badge>
                  {passedTests === totalTests && (
                    <span className="text-green-600 text-sm">✅ 所有测试通过！</span>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>测试结果</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div 
                    key={index}
                    className={`p-4 rounded-lg border ${
                      result.status === 'pass' 
                        ? 'border-green-200 bg-green-50' 
                        : 'border-red-200 bg-red-50'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      {result.status === 'pass' ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                      <h3 className="font-semibold">{result.testName}</h3>
                      <Badge variant={result.status === 'pass' ? "default" : "destructive"}>
                        {result.status === 'pass' ? '通过' : '失败'}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{result.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">期望结果: </span>
                        <span className="text-green-700">{result.expected}</span>
                      </div>
                      <div>
                        <span className="font-medium">实际结果: </span>
                        <span className={result.status === 'pass' ? 'text-green-700' : 'text-red-700'}>
                          {result.actual}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>阶梯式奖励规则说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <h4 className="font-semibold">普通风口奖励（按比例）：</h4>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>基础档：订单金额 &lt; 10万，奖励比例 2%</li>
                <li>第一档：订单金额 ≥ 10万，奖励比例 3%</li>
                <li>第二档：订单金额 ≥ 20万，奖励比例 4%</li>
                <li>最高比例：5%</li>
              </ul>
              
              <h4 className="font-semibold mt-4">高端风口奖励（固定金额）：</h4>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>基础档：单笔 &lt; 1万，奖励 30元</li>
                <li>中级档：单笔 ≥ 1万，奖励 50元</li>
                <li>高级档：单笔 ≥ 2万，奖励 100元</li>
                <li>超级档：单笔 ≥ 5万，奖励 500元</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
