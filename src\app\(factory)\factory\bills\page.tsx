"use client"

import { useState, useEffect } from "react"
import { DashboardLayout  } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Search,
  Filter,
  Download,
  Eye,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  TrendingUp,
  TrendingDown,
  Loader2,
  RefreshCw,
  Calendar
} from "lucide-react"
import { useAuthStore } from "@/lib/store/auth"
import { db } from "@/lib/database/client"
import { safeNumber, safeAmount, safeAmountFormat, safeAmountSum } from "@/lib/utils/number-utils"
import { getProductTypeName } from "@/lib/pricing"
import { extractDimensionsFromItem } from "@/lib/utils/dimension-utils"
import * as XLSX from 'xlsx'
import ExcelJS from 'exceljs'
import { exportPurchaseDetailExcel } from '@/lib/utils/purchase-detail-export'

interface BillSummary {
  clientId: string
  clientName: string
  clientPhone: string
  totalOrders: number
  totalAmount: number
  paidAmount: number
  unpaidAmount: number
  lastOrderDate: string
  paymentStatus: 'paid' | 'partial' | 'unpaid' | 'overdue'
  lastPaymentDate?: string
  dataConsistent?: boolean // 🆕 数据一致性标记
}

export default function BillsManagementPage() {
  const { factoryId } = useAuthStore()
  const [bills, setBills] = useState<BillSummary[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState<string>("all")
  const [selectedClient, setSelectedClient] = useState<BillSummary | null>(null)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [showUnpaidDialog, setShowUnpaidDialog] = useState(false) // 🆕 待收款详情对话框
  const [paymentAmount, setPaymentAmount] = useState(0)
  const [paymentNotes, setPaymentNotes] = useState("")
  const [paymentLoading, setPaymentLoading] = useState(false)
  const [paymentSuccess, setPaymentSuccess] = useState("")
  const [paymentError, setPaymentError] = useState("")
  const [syncingData, setSyncingData] = useState(false)
  const [syncResult, setSyncResult] = useState<string>("")
  const [unpaidOrders, setUnpaidOrders] = useState<unknown[]>([]) // 🆕 未付款订单列表
  const [loadingUnpaidOrders, setLoadingUnpaidOrders] = useState(false) // 🆕 加载状态

  // 🆕 阶段采购单汇总相关状态
  const [showPeriodDialog, setShowPeriodDialog] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month')
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedQuarter, setSelectedQuarter] = useState(Math.ceil((new Date().getMonth() + 1) / 3))
  const [periodSummary, setPeriodSummary] = useState<unknown>(null)
  const [loadingPeriodSummary, setLoadingPeriodSummary] = useState(false)

  // 🆕 选择性导出相关状态
  const [selectedClients, setSelectedClients] = useState<Set<string>>(new Set())
  const [selectAll, setSelectAll] = useState(false)

  // 🆕 采购明细导出相关状态
  const [showPurchaseExportDialog, setShowPurchaseExportDialog] = useState(false)
  const [purchaseExportLoading, setPurchaseExportLoading] = useState(false)
  const [purchaseExportType, setPurchaseExportType] = useState<'client' | 'period'>('client')
  const [selectedClientForPurchase, setSelectedClientForPurchase] = useState<BillSummary | null>(null)

  // 统计数据
  const [stats, setStats] = useState({
    totalClients: 0,
    totalAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0,
    overdueAmount: 0
  })

  useEffect(() => {
    loadBillsData()
  }, [factoryId])

  const loadBillsData = async () => {
    if (!factoryId) return

    try {
      setLoading(true)
      console.log('📊 开始加载账单数据...')

      // 获取所有客户
      console.log('🔄 获取客户数据...')
      const clients = await db.getClientsByFactoryId(factoryId)
      console.log(`✅ 获取到 ${clients.length} 个客户`)

      // 获取所有订单
      console.log('🔄 获取订单数据...')
      const orders = await db.getOrdersByFactoryId(factoryId)
      console.log(`✅ 获取到 ${orders.length} 个订单`)

      // 计算每个客户的账单汇总
      const billSummaries: BillSummary[] = []
      let totalAmount = 0
      let totalPaid = 0
      let totalUnpaid = 0
      let totalOverdue = 0

      console.log('🔄 开始计算账单汇总...')
      for (const client of clients) {
        const clientOrders = orders.filter(order => order.clientId === client.id)

        if (clientOrders.length === 0) continue

        // 🔧 修复：使用专门的累加函数处理精度问题
        const clientTotalAmount = safeAmountSum(clientOrders.map(order => order.totalAmount))
        const clientPaidAmountFromOrders = safeAmountSum(clientOrders.map(order => order.paidAmount || 0))

        // 对比客户表中的已付款金额，检查数据一致性
        const clientPaidAmountFromClient = safeNumber(client.paidAmount || 0)
        const clientUnpaidAmount = Math.round((clientTotalAmount - clientPaidAmountFromOrders) * 10) / 10

        console.log(`📋 客户 ${client.name} 账单计算:`, {
          订单数: clientOrders.length,
          订单总额: clientTotalAmount,
          订单表已付款汇总: clientPaidAmountFromOrders,
          客户表已付款: clientPaidAmountFromClient,
          数据是否一致: Math.abs(clientPaidAmountFromOrders - clientPaidAmountFromClient) < 0.01,
          计算待付款: clientUnpaidAmount
        })

        // 如果数据不一致，发出警告
        if (Math.abs(clientPaidAmountFromOrders - clientPaidAmountFromClient) > 0.01) {
          console.warn(`⚠️ 数据不一致警告 - 客户 ${client.name}:`, {
            订单表汇总: clientPaidAmountFromOrders,
            客户表记录: clientPaidAmountFromClient,
            差异: Math.abs(clientPaidAmountFromOrders - clientPaidAmountFromClient)
          })
        }

        // 🔧 修复：基于订单表数据确定付款状态，确保与订单管理一致
        let paymentStatus: 'paid' | 'partial' | 'unpaid' | 'overdue' = 'unpaid'

        // 检查是否有任何订单标记为逾期
        const hasOverdueOrders = clientOrders.some(order => order.paymentStatus === 'overdue')

        // 🔧 修复：使用精度处理避免浮点数比较问题
        if (Math.abs(clientPaidAmountFromOrders - clientTotalAmount) < 0.01 || clientPaidAmountFromOrders >= clientTotalAmount) {
          paymentStatus = 'paid'
        } else if (clientPaidAmountFromOrders > 0) {
          paymentStatus = 'partial'
        } else if (hasOverdueOrders) {
          paymentStatus = 'overdue'
        } else {
          // 检查是否逾期（超过30天未付款）
          const lastOrderDate = new Date(Math.max(...clientOrders.map(o => new Date(o.createdAt).getTime())))
          const daysSinceLastOrder = (Date.now() - lastOrderDate.getTime()) / (1000 * 60 * 60 * 24)
          if (daysSinceLastOrder > 30) {
            paymentStatus = 'overdue'
          }
        }

        // 🔧 修复：使用订单表的实际已付款金额
        billSummaries.push({
          clientId: client.id,
          clientName: client.name,
          clientPhone: client.phone,
          totalOrders: clientOrders.length,
          totalAmount: clientTotalAmount,
          paidAmount: clientPaidAmountFromOrders, // 使用订单表汇总的已付款金额
          unpaidAmount: clientUnpaidAmount,
          lastOrderDate: clientOrders[clientOrders.length - 1]?.createdAt
            ? new Date(clientOrders[clientOrders.length - 1].createdAt).toLocaleDateString('zh-CN')
            : '',
          paymentStatus,
          lastPaymentDate: client.lastPaymentDate
            ? new Date(client.lastPaymentDate).toLocaleDateString('zh-CN')
            : undefined,
          // 🆕 添加数据一致性标记
          dataConsistent: Math.abs(clientPaidAmountFromOrders - clientPaidAmountFromClient) < 0.01
        })

        // 累计统计
        totalAmount += clientTotalAmount
        totalPaid += clientPaidAmountFromOrders // 使用订单表的实际数据
        totalUnpaid += clientUnpaidAmount
        if (paymentStatus === 'overdue') {
          totalOverdue += clientUnpaidAmount
        }
      }

      console.log('📊 账单汇总统计:', {
        客户总数: billSummaries.length,
        总金额: totalAmount,
        已收款: totalPaid,
        待收款: totalUnpaid,
        逾期金额: totalOverdue
      })

      setBills(billSummaries)
      setStats({
        totalClients: billSummaries.length,
        totalAmount,
        paidAmount: totalPaid,
        unpaidAmount: totalUnpaid,
        overdueAmount: totalOverdue
      })

      console.log('✅ 账单数据加载完成')

    } catch (error) {
      console.error('❌ 加载账单数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 🆕 生成阶段采购单汇总
  const generatePeriodSummary = async (period: 'month' | 'quarter' | 'year', year: number, month?: number, quarter?: number) => {
    if (!factoryId) return null

    try {
      setLoadingPeriodSummary(true)
      console.log('📊 开始生成阶段采购单汇总:', { period, year, month, quarter })

      // 获取所有订单
      const allOrders = await db.getOrdersByFactoryId(factoryId)
      const allClients = await db.getClientsByFactoryId(factoryId)
      const clientMap = new Map(allClients.map(client => [client.id, client]))

      // 确定时间范围
      let startDate: Date, endDate: Date, periodName: string

      if (period === 'month' && month) {
        startDate = new Date(year, month - 1, 1)
        endDate = new Date(year, month, 0, 23, 59, 59)
        periodName = `${year}年${month}月`
      } else if (period === 'quarter' && quarter) {
        const quarterStartMonth = (quarter - 1) * 3
        startDate = new Date(year, quarterStartMonth, 1)
        endDate = new Date(year, quarterStartMonth + 3, 0, 23, 59, 59)
        periodName = `${year}年第${quarter}季度`
      } else if (period === 'year') {
        startDate = new Date(year, 0, 1)
        endDate = new Date(year, 11, 31, 23, 59, 59)
        periodName = `${year}年`
      } else {
        throw new Error('无效的阶段参数')
      }

      console.log('📅 时间范围:', { startDate, endDate, periodName })

      // 筛选阶段内的订单
      const periodOrders = allOrders.filter(order => {
        const orderDate = new Date(order.createdAt)
        return orderDate >= startDate && orderDate <= endDate
      })

      console.log(`📋 ${periodName}订单数量:`, periodOrders.length)

      // 按客户汇总
      const clientSummaries = new Map()

      for (const order of periodOrders) {
        const client = clientMap.get(order.clientId)
        if (!client) continue

        const clientId = client.id
        const totalAmount = safeNumber(order.totalAmount)
        const paidAmount = safeNumber(order.paidAmount || 0)
        const unpaidAmount = totalAmount - paidAmount

        if (!clientSummaries.has(clientId)) {
          clientSummaries.set(clientId, {
            clientId,
            clientName: client.name,
            clientPhone: client.phone,
            orders: [],
            totalOrders: 0,
            totalAmount: 0,
            paidAmount: 0,
            unpaidAmount: 0
          })
        }

        const summary = clientSummaries.get(clientId)
        summary.orders.push(order)
        summary.totalOrders++
        summary.totalAmount += totalAmount
        summary.paidAmount += paidAmount
        summary.unpaidAmount += unpaidAmount
      }

      const summaryList = Array.from(clientSummaries.values())

      // 计算总统计
      const totalStats = {
        totalClients: summaryList.length,
        totalOrders: periodOrders.length,
        totalAmount: summaryList.reduce((sum, s) => sum + s.totalAmount, 0),
        paidAmount: summaryList.reduce((sum, s) => sum + s.paidAmount, 0),
        unpaidAmount: summaryList.reduce((sum, s) => sum + s.unpaidAmount, 0)
      }

      const result = {
        period,
        periodName,
        startDate,
        endDate,
        clientSummaries: summaryList,
        totalStats,
        orders: periodOrders
      }

      console.log('✅ 阶段采购单汇总生成完成:', result)
      return result

    } catch (error) {
      console.error('❌ 生成阶段采购单汇总失败:', error)
      throw error
    } finally {
      setLoadingPeriodSummary(false)
    }
  }

  // 🆕 处理阶段采购单汇总
  const handlePeriodSummary = async () => {
    try {
      const summary = await generatePeriodSummary(selectedPeriod, selectedYear, selectedMonth, selectedQuarter)
      setPeriodSummary(summary)
      setSelectedClients(new Set()) // 重置选择
      setSelectAll(false)
      setShowPeriodDialog(true)
    } catch (error) {
      console.error('❌ 生成阶段采购单汇总失败:', error)
      alert('生成汇总失败，请重试')
    }
  }

  // 🆕 处理客户选择
  const handleClientSelect = (clientId: string, checked: boolean) => {
    const newSelected = new Set(selectedClients)
    if (checked) {
      newSelected.add(clientId)
    } else {
      newSelected.delete(clientId)
    }
    setSelectedClients(newSelected)

    // 更新全选状态
    if (periodSummary) {
      setSelectAll(newSelected.size === periodSummary.clientSummaries.length)
    }
  }

  // 🆕 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked)
    if (checked && periodSummary) {
      setSelectedClients(new Set(periodSummary.clientSummaries.map((client: unknown) => client.clientId)))
    } else {
      setSelectedClients(new Set())
    }
  }

  // 🆕 导出项目详细明细Excel（使用ExcelJS实现真正的美化效果）
  const handleExportProjectDetails = async () => {
    if (!periodSummary) return

    try {
      console.log('📊 开始导出项目详细明细Excel...')

      // 筛选选中的客户数据
      const selectedClientSummaries = selectedClients.size > 0
        ? periodSummary.clientSummaries.filter((client: unknown) => selectedClients.has(client.clientId))
        : periodSummary.clientSummaries

      // 筛选选中客户的订单
      const selectedOrders = selectedClients.size > 0
        ? periodSummary.orders.filter((order: unknown) => selectedClients.has(order.clientId))
        : periodSummary.orders

      // 创建工作簿
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('项目详细明细')

      // 设置列宽
      worksheet.columns = [
        { width: 8 },   // A: 序号
        { width: 25 },  // B: 项目地址
        { width: 16 },  // C: 原始信息
        { width: 8 },   // D: 数量
        { width: 10 },  // E: 长度
        { width: 10 },  // F: 宽度
        { width: 10 },  // G: 颜色
        { width: 12 },  // H: 单价
        { width: 12 },  // I: 金额
        { width: 20 },  // J: 备注
        { width: 20 }   // K: 客户信息
      ]

      let currentRow = 1

      // 1. 添加主标题（绿色边框）
      const titleCell = worksheet.getCell('A1')
      titleCell.value = `${periodSummary.periodName}项目详细明细`
      worksheet.mergeCells('A1:K1')
      titleCell.style = {
        font: { bold: true, size: 18, color: { argb: 'FF000000' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thick', color: { argb: 'FF00B050' } },
          bottom: { style: 'thick', color: { argb: 'FF00B050' } },
          left: { style: 'thick', color: { argb: 'FF00B050' } },
          right: { style: 'thick', color: { argb: 'FF00B050' } }
        }
      }
      worksheet.getRow(1).height = 35
      currentRow++

      // 2. 添加信息行
      const infoRows = [
        ['导出时间', new Date().toLocaleString('zh-CN')],
        ['导出范围', selectedClients.size > 0 ? `选中的${selectedClientSummaries.length}个客户` : '全部客户']
      ]

      for (const [label, value] of infoRows) {
        const labelCell = worksheet.getCell(`A${currentRow}`)
        const valueCell = worksheet.getCell(`B${currentRow}`)
        labelCell.value = label
        valueCell.value = value

        // 合并信息行
        worksheet.mergeCells(`B${currentRow}:K${currentRow}`)

        // 设置信息行样式
        for (let col = 1; col <= 11; col++) {
          const cell = worksheet.getCell(currentRow, col)
          cell.style = {
            font: { size: 11, color: { argb: 'FF666666' } },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } },
            alignment: { horizontal: 'left', vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
            }
          }
        }
        worksheet.getRow(currentRow).height = 22
        currentRow++
      }

      // 3. 添加空行
      currentRow++

      // 4. 添加表头（蓝色背景）
      const headers = ['序号', '项目地址', '原始信息/mm', '数量', '长度/mm', '宽度/mm', '颜色', '单价/元', '金额/元', '备注', '客户信息']
      const headerRow = worksheet.getRow(currentRow)

      headers.forEach((header, index) => {
        const cell = headerRow.getCell(index + 1)
        cell.value = header
        cell.style = {
          font: { bold: true, size: 12, color: { argb: 'FFFFFFFF' } },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
          alignment: { horizontal: 'center', vertical: 'middle' },
          border: {
            top: { style: 'medium', color: { argb: 'FF4472C4' } },
            bottom: { style: 'medium', color: { argb: 'FF4472C4' } },
            left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
            right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
          }
        }
      })
      headerRow.height = 25
      currentRow++

      // 5. 添加数据行
      let globalRowIndex = 1
      const projectGroups = new Map()

      for (const order of selectedOrders) {
        const client = selectedClientSummaries.find((c: unknown) => c.clientId === order.clientId)
        const clientInfo = `${client?.clientName || '未知客户'} (${client?.clientPhone || '无'})`
        const projectAddress = order.projectAddress || '未指定项目地址'
        const projectKey = `${order.clientId}-${projectAddress}`

        if (!order.items || order.items.length === 0) {
          // 无明细订单
          const rowData = [
            globalRowIndex,
            projectAddress,
            '无明细',
            1,
            '',
            '',
            '象牙白',
            safeNumber(order.totalAmount).toFixed(1),
            safeNumber(order.totalAmount).toFixed(1),
            `订单号: ${order.orderNumber || order.id}${order.notes ? ` | ${order.notes}` : ''}`,
            clientInfo
          ]

          const dataRow = worksheet.getRow(currentRow)
          rowData.forEach((value, index) => {
            const cell = dataRow.getCell(index + 1)
            cell.value = value

            // 设置数据行样式
            const isEvenRow = (currentRow - 5) % 2 === 0
            const fillColor = isEvenRow ? 'FFF8F9FA' : 'FFFFFFFF'

            let alignment: 'center' | 'left' | 'right' = 'center'
            if (index === 1) alignment = 'center'  // 项目地址
            else if (index === 2 || index === 6 || index === 9) alignment = 'left'  // 规格、颜色、备注
            else if (index === 7 || index === 8) alignment = 'right'  // 单价、金额
            else if (index === 10) alignment = 'left'  // 客户信息

            cell.style = {
              font: { size: 11 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
              alignment: { horizontal: alignment, vertical: 'middle' },
              border: {
                top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
              }
            }
          })

          dataRow.height = 22
          projectGroups.set(projectKey, { startRow: currentRow, endRow: currentRow })
          currentRow++
          globalRowIndex++
        } else {
          // 有明细的订单
          const itemStartRow = currentRow

          order.items.forEach((item: unknown, itemIndex: number) => {
            // 使用统一的尺寸提取函数
            const { originalInfo, length, width, color } = extractDimensionsFromItem(item)

            // 使用统一的产品类型识别函数，确保总是有有意义的类型名称
            const typeName = getProductTypeName(item.productType) ||
                           item.productName ||
                           item.productType ||
                           '风口'

            if (item.color) {
              color = item.color
            } else if (item.productType === 'rectangular' || item.productType === 'black_white_outlet' || item.productType === 'black_white_return') {
              color = '黑白双色'
            }

            const notes = `${typeName}${item.notes ? ` | ${item.notes}` : ''}${itemIndex === 0 ? ` | 订单号: ${order.orderNumber || order.id}` : ''}`

            const rowData = [
              globalRowIndex,
              itemIndex === 0 ? projectAddress : '', // 只在第一行显示项目地址
              typeName, // 风口类型
              originalInfo, // 尺寸信息
              item.quantity || 1,
              safeNumber(item.unitPrice || 0).toFixed(1),
              safeNumber(item.totalPrice || 0).toFixed(1)
            ]

            const dataRow = worksheet.getRow(currentRow)
            rowData.forEach((value, index) => {
              const cell = dataRow.getCell(index + 1)
              cell.value = value

              // 设置数据行样式
              const isEvenRow = (currentRow - 5) % 2 === 0
              const fillColor = isEvenRow ? 'FFF8F9FA' : 'FFFFFFFF'

              let alignment: 'center' | 'left' | 'right' = 'center'
              if (index === 1) alignment = 'center'  // 项目地址
              else if (index === 2 || index === 6 || index === 9) alignment = 'left'  // 规格、颜色、备注
              else if (index === 7 || index === 8) alignment = 'right'  // 单价、金额
              else if (index === 10) alignment = 'left'  // 客户信息

              cell.style = {
                font: { size: 11 },
                fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
                alignment: { horizontal: alignment, vertical: 'middle' },
                border: {
                  top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                  bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                  left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                  right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
                }
              }
            })

            dataRow.height = 22
            currentRow++
            globalRowIndex++
          })

          // 记录项目分组信息
          projectGroups.set(projectKey, { startRow: itemStartRow, endRow: currentRow - 1 })
        }
      }

      // 6. 设置合并单元格
      for (const [projectKey, group] of projectGroups) {
        if (group.endRow > group.startRow) {
          // 合并项目地址列
          worksheet.mergeCells(`B${group.startRow}:B${group.endRow}`)
          // 合并客户信息列
          worksheet.mergeCells(`K${group.startRow}:K${group.endRow}`)

          // 设置合并单元格的对齐方式
          const projectCell = worksheet.getCell(`B${group.startRow}`)
          const clientCell = worksheet.getCell(`K${group.startRow}`)

          projectCell.alignment = { horizontal: 'center', vertical: 'middle' }
          clientCell.alignment = { horizontal: 'left', vertical: 'middle' }
        }
      }

      // 导出Excel文件
      const fileNameSuffix = selectedClients.size > 0 ? `_选中${selectedClientSummaries.length}客户` : '_全部客户'
      const fileName = `${periodSummary.periodName}项目详细明细${fileNameSuffix}_${new Date().toISOString().split('T')[0]}.xlsx`

      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.click()
      window.URL.revokeObjectURL(url)

      console.log('✅ 项目详细明细Excel导出成功')
      const successMessage = selectedClients.size > 0
        ? `项目详细明细导出成功！已导出${selectedClientSummaries.length}个选中客户的详细数据。`
        : '项目详细明细导出成功！已导出全部客户的详细数据。'
      alert(successMessage)

    } catch (error) {
      console.error('❌ 导出项目详细明细Excel失败:', error)
      alert('导出失败，请重试')
    }
  }

  // 🆕 综合导出阶段采购单汇总Excel（包含账单汇总和项目明细，使用ExcelJS）
  const handleExportComprehensiveSummary = async () => {
    if (!periodSummary) return

    try {
      console.log('📊 开始导出阶段采购单汇总Excel...')

      // 筛选选中的客户数据
      const selectedClientSummaries = selectedClients.size > 0
        ? periodSummary.clientSummaries.filter((client: unknown) => selectedClients.has(client.clientId))
        : periodSummary.clientSummaries

      // 筛选选中客户的订单
      const selectedOrders = selectedClients.size > 0
        ? periodSummary.orders.filter((order: unknown) => selectedClients.has(order.clientId))
        : periodSummary.orders

      // 重新计算选中客户的统计数据
      const selectedStats = {
        totalClients: selectedClientSummaries.length,
        totalOrders: selectedOrders.length,
        totalAmount: selectedClientSummaries.reduce((sum: number, client: unknown) => sum + client.totalAmount, 0),
        paidAmount: selectedClientSummaries.reduce((sum: number, client: unknown) => sum + client.paidAmount, 0),
        unpaidAmount: selectedClientSummaries.reduce((sum: number, client: unknown) => sum + client.unpaidAmount, 0)
      }

      console.log('📋 导出数据统计:', {
        总客户数: periodSummary.clientSummaries.length,
        选中客户数: selectedClientSummaries.length,
        总订单数: periodSummary.orders.length,
        选中订单数: selectedOrders.length
      })

      // 创建工作簿
      const workbook = new ExcelJS.Workbook()

      // 1. 创建汇总概览工作表
      const summaryWorksheet = workbook.addWorksheet('汇总概览')

      // 设置列宽
      summaryWorksheet.columns = [
        { width: 25 },  // A: 统计项目
        { width: 25 }   // B: 数值
      ]

      let currentRow = 1

      // 添加主标题
      const exportTitle = selectedClients.size > 0
        ? `${periodSummary.periodName}账单汇总报表（已选择${selectedClientSummaries.length}个客户）`
        : `${periodSummary.periodName}账单汇总报表（全部客户）`

      const titleCell = summaryWorksheet.getCell('A1')
      titleCell.value = exportTitle
      summaryWorksheet.mergeCells('A1:B1')
      titleCell.style = {
        font: { bold: true, size: 18, color: { argb: 'FF000000' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thick', color: { argb: 'FF00B050' } },
          bottom: { style: 'thick', color: { argb: 'FF00B050' } },
          left: { style: 'thick', color: { argb: 'FF00B050' } },
          right: { style: 'thick', color: { argb: 'FF00B050' } }
        }
      }
      summaryWorksheet.getRow(1).height = 35
      currentRow++

      // 添加信息行
      const infoRows = [
        ['生成时间', new Date().toLocaleString('zh-CN')],
        ['统计周期', periodSummary.periodName],
        ['起始日期', periodSummary.startDate.toLocaleDateString('zh-CN')],
        ['结束日期', periodSummary.endDate.toLocaleDateString('zh-CN')],
        ['导出范围', selectedClients.size > 0 ? `选中的${selectedClientSummaries.length}个客户` : '全部客户']
      ]

      for (const [label, value] of infoRows) {
        const labelCell = summaryWorksheet.getCell(`A${currentRow}`)
        const valueCell = summaryWorksheet.getCell(`B${currentRow}`)
        labelCell.value = label
        valueCell.value = value

        // 设置信息行样式
        for (let col = 1; col <= 2; col++) {
          const cell = summaryWorksheet.getCell(currentRow, col)
          cell.style = {
            font: { size: 11, color: { argb: 'FF666666' } },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } },
            alignment: { horizontal: 'left', vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
            }
          }
        }
        summaryWorksheet.getRow(currentRow).height = 22
        currentRow++
      }

      // 添加空行
      currentRow++

      // 添加汇总统计标题
      const summaryTitleCell = summaryWorksheet.getCell(`A${currentRow}`)
      summaryTitleCell.value = '汇总统计'
      summaryWorksheet.mergeCells(`A${currentRow}:B${currentRow}`)
      summaryTitleCell.style = {
        font: { bold: true, size: 14, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF70AD47' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'medium', color: { argb: 'FF70AD47' } },
          bottom: { style: 'medium', color: { argb: 'FF70AD47' } },
          left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
        }
      }
      summaryWorksheet.getRow(currentRow).height = 28
      currentRow++

      // 添加表头
      const headerCell1 = summaryWorksheet.getCell(`A${currentRow}`)
      const headerCell2 = summaryWorksheet.getCell(`B${currentRow}`)
      headerCell1.value = '统计项目'
      headerCell2.value = '数值'

      for (let col = 1; col <= 2; col++) {
        const cell = summaryWorksheet.getCell(currentRow, col)
        cell.style = {
          font: { bold: true, size: 12, color: { argb: 'FFFFFFFF' } },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
          alignment: { horizontal: 'center', vertical: 'middle' },
          border: {
            top: { style: 'medium', color: { argb: 'FF4472C4' } },
            bottom: { style: 'medium', color: { argb: 'FF4472C4' } },
            left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
            right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
          }
        }
      }
      summaryWorksheet.getRow(currentRow).height = 25
      currentRow++

      // 添加统计数据行
      const statsData = [
        ['客户总数', `${selectedStats.totalClients} 人`],
        ['订单总数', `${selectedStats.totalOrders} 个`],
        ['订单总金额', `¥${safeNumber(selectedStats.totalAmount).toFixed(1)}`],
        ['已收款金额', `¥${safeNumber(selectedStats.paidAmount).toFixed(1)}`],
        ['待收款金额', `¥${safeNumber(selectedStats.unpaidAmount).toFixed(1)}`],
        ['收款率', `${selectedStats.totalAmount > 0 ? ((selectedStats.paidAmount / selectedStats.totalAmount) * 100).toFixed(1) : '0.0'}%`]
      ]

      for (let i = 0; i < statsData.length; i++) {
        const [label, value] = statsData[i]
        const labelCell = summaryWorksheet.getCell(`A${currentRow}`)
        const valueCell = summaryWorksheet.getCell(`B${currentRow}`)
        labelCell.value = label
        valueCell.value = value

        // 设置数据行样式
        const isEvenRow = i % 2 === 0
        const fillColor = isEvenRow ? 'FFF8F9FA' : 'FFFFFFFF'

        for (let col = 1; col <= 2; col++) {
          const cell = summaryWorksheet.getCell(currentRow, col)
          cell.style = {
            font: { size: 12, bold: col === 1 },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
            alignment: {
              horizontal: col === 1 ? 'left' : 'right',
              vertical: 'middle'
            },
            border: {
              top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
            }
          }
        }
        summaryWorksheet.getRow(currentRow).height = 25
        currentRow++
      }

      // 2. 创建客户明细工作表
      const clientWorksheet = workbook.addWorksheet('客户明细')

      // 设置列宽
      clientWorksheet.columns = [
        { width: 15 }, // 客户姓名
        { width: 15 }, // 联系电话
        { width: 12 }, // 订单数量
        { width: 15 }, // 订单总额
        { width: 15 }, // 已付款
        { width: 15 }, // 待付款
        { width: 12 }  // 收款率
      ]

      currentRow = 1

      // 添加主标题
      const clientTitleCell = clientWorksheet.getCell('A1')
      clientTitleCell.value = `${periodSummary.periodName}客户账单明细`
      clientWorksheet.mergeCells('A1:G1')
      clientTitleCell.style = {
        font: { bold: true, size: 18, color: { argb: 'FF000000' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thick', color: { argb: 'FF00B050' } },
          bottom: { style: 'thick', color: { argb: 'FF00B050' } },
          left: { style: 'thick', color: { argb: 'FF00B050' } },
          right: { style: 'thick', color: { argb: 'FF00B050' } }
        }
      }
      clientWorksheet.getRow(1).height = 35
      currentRow++

      // 添加信息行
      const clientInfoRows = [
        ['导出时间', new Date().toLocaleString('zh-CN')],
        ['导出范围', selectedClients.size > 0 ? `选中的${selectedClientSummaries.length}个客户` : '全部客户']
      ]

      for (const [label, value] of clientInfoRows) {
        const labelCell = clientWorksheet.getCell(`A${currentRow}`)
        const valueCell = clientWorksheet.getCell(`B${currentRow}`)
        labelCell.value = label
        valueCell.value = value

        // 设置信息行样式
        for (let col = 1; col <= 7; col++) {
          const cell = clientWorksheet.getCell(currentRow, col)
          cell.style = {
            font: { size: 11, color: { argb: 'FF666666' } },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } },
            alignment: { horizontal: 'left', vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
            }
          }
        }
        clientWorksheet.getRow(currentRow).height = 22
        currentRow++
      }

      // 添加空行
      currentRow++

      // 添加表头
      const clientHeaders = ['客户姓名', '联系电话', '订单数量', '订单总额(元)', '已付款(元)', '待付款(元)', '收款率(%)']
      for (let col = 0; col < clientHeaders.length; col++) {
        const cell = clientWorksheet.getCell(currentRow, col + 1)
        cell.value = clientHeaders[col]
        cell.style = {
          font: { bold: true, size: 12, color: { argb: 'FFFFFFFF' } },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
          alignment: { horizontal: 'center', vertical: 'middle' },
          border: {
            top: { style: 'medium', color: { argb: 'FF4472C4' } },
            bottom: { style: 'medium', color: { argb: 'FF4472C4' } },
            left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
            right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
          }
        }
      }
      clientWorksheet.getRow(currentRow).height = 25
      currentRow++

      // 添加客户数据行
      for (let i = 0; i < selectedClientSummaries.length; i++) {
        const client = selectedClientSummaries[i]
        const clientData = [
          client.clientName,
          client.clientPhone,
          client.totalOrders,
          safeNumber(client.totalAmount).toFixed(1),
          safeNumber(client.paidAmount).toFixed(1),
          safeNumber(client.unpaidAmount).toFixed(1),
          client.totalAmount > 0 ? ((client.paidAmount / client.totalAmount) * 100).toFixed(1) : '0.0'
        ]

        for (let col = 0; col < clientData.length; col++) {
          const cell = clientWorksheet.getCell(currentRow, col + 1)
          cell.value = clientData[col]

          // 设置数据行样式
          const isEvenRow = i % 2 === 0
          const fillColor = isEvenRow ? 'FFF8F9FA' : 'FFFFFFFF'

          // 根据列设置不同的对齐方式
          let alignment: 'center' | 'left' | 'right' = 'center'
          if (col === 0 || col === 1) { // 客户姓名、电话左对齐
            alignment = 'left'
          } else if (col >= 2) { // 数字列右对齐
            alignment = 'right'
          }

          cell.style = {
            font: { size: 11 },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
            alignment: { horizontal: alignment, vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
            }
          }
        }
        clientWorksheet.getRow(currentRow).height = 22
        currentRow++
      }

      // 3. 创建项目详细工作表
      const projectWorksheet = workbook.addWorksheet('项目详细明细')

      // 设置列宽
      projectWorksheet.columns = [
        { width: 8 },  // 序号
        { width: 25 }, // 项目地址
        { width: 15 }, // 原始信息
        { width: 8 },  // 数量
        { width: 12 }, // 长度
        { width: 12 }, // 宽度
        { width: 12 }, // 颜色
        { width: 12 }, // 单价
        { width: 12 }, // 金额
        { width: 20 }, // 备注
        { width: 20 }  // 客户信息
      ]

      currentRow = 1

      // 添加主标题
      const projectTitleCell = projectWorksheet.getCell('A1')
      projectTitleCell.value = `${periodSummary.periodName}项目详细明细`
      projectWorksheet.mergeCells('A1:K1')
      projectTitleCell.style = {
        font: { bold: true, size: 18, color: { argb: 'FF000000' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thick', color: { argb: 'FF00B050' } },
          bottom: { style: 'thick', color: { argb: 'FF00B050' } },
          left: { style: 'thick', color: { argb: 'FF00B050' } },
          right: { style: 'thick', color: { argb: 'FF00B050' } }
        }
      }
      projectWorksheet.getRow(1).height = 35
      currentRow++

      // 添加信息行
      const projectInfoRows = [
        ['导出时间', new Date().toLocaleString('zh-CN')],
        ['导出范围', selectedClients.size > 0 ? `选中客户的${selectedOrders.length}个订单` : '全部订单']
      ]

      for (const [label, value] of projectInfoRows) {
        const labelCell = projectWorksheet.getCell(`A${currentRow}`)
        const valueCell = projectWorksheet.getCell(`B${currentRow}`)
        labelCell.value = label
        valueCell.value = value

        // 设置信息行样式
        for (let col = 1; col <= 11; col++) {
          const cell = projectWorksheet.getCell(currentRow, col)
          cell.style = {
            font: { size: 11, color: { argb: 'FF666666' } },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } },
            alignment: { horizontal: 'left', vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
            }
          }
        }
        projectWorksheet.getRow(currentRow).height = 22
        currentRow++
      }

      // 添加空行
      currentRow++

      // 添加表头
      const projectHeaders = ['序号', '项目地址', '原始信息/mm', '数量', '长度/mm', '宽度/mm', '颜色', '单价', '金额/元', '备注', '客户信息']
      for (let col = 0; col < projectHeaders.length; col++) {
        const cell = projectWorksheet.getCell(currentRow, col + 1)
        cell.value = projectHeaders[col]
        cell.style = {
          font: { bold: true, size: 12, color: { argb: 'FFFFFFFF' } },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
          alignment: { horizontal: 'center', vertical: 'middle' },
          border: {
            top: { style: 'medium', color: { argb: 'FF4472C4' } },
            bottom: { style: 'medium', color: { argb: 'FF4472C4' } },
            left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
            right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
          }
        }
      }
      projectWorksheet.getRow(currentRow).height = 25
      currentRow++

      // 添加项目数据行
      let globalRowIndex = 1
      const mergeRanges: unknown[] = []

      for (const order of selectedOrders) {
        const client = selectedClientSummaries.find((c: unknown) => c.clientId === order.clientId)
        const clientInfo = `${client?.clientName || '未知客户'} (${client?.clientPhone || '无'})`
        const projectAddress = order.projectAddress || '未指定项目地址'

        if (!order.items || order.items.length === 0) {
          // 如果没有项目明细，添加一行基本信息
          const projectData = [
            globalRowIndex,
            projectAddress,
            '无明细',
            1,
            '',
            '',
            '',
            safeNumber(order.totalAmount).toFixed(1),
            safeNumber(order.totalAmount).toFixed(1),
            order.notes || '',
            clientInfo
          ]

          for (let col = 0; col < projectData.length; col++) {
            const cell = projectWorksheet.getCell(currentRow, col + 1)
            cell.value = projectData[col]

            // 设置数据行样式
            const isEvenRow = (globalRowIndex - 1) % 2 === 0
            const fillColor = isEvenRow ? 'FFF8F9FA' : 'FFFFFFFF'

            // 根据列设置不同的对齐方式
            let alignment: 'center' | 'left' | 'right' = 'center'
            if (col === 1 || col === 9 || col === 10) { // 项目地址、备注、客户信息左对齐
              alignment = 'left'
            } else if (col >= 3 && col <= 8) { // 数字列右对齐
              alignment = 'right'
            }

            cell.style = {
              font: { size: 11 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
              alignment: { horizontal: alignment, vertical: 'middle' },
              border: {
                top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
              }
            }
          }
          projectWorksheet.getRow(currentRow).height = 22
          currentRow++
          globalRowIndex++
        } else {
          // 处理有明细的订单
          const startRow = currentRow

          for (let itemIndex = 0; itemIndex < order.items.length; itemIndex++) {
            const item = order.items[itemIndex]

            // 使用统一的尺寸提取函数
            const { originalInfo, length, width, color } = extractDimensionsFromItem(item)

            // 使用统一的产品类型识别函数，确保总是有有意义的类型名称
            const typeName = getProductTypeName(item.productType) ||
                           item.productName ||
                           item.productType ||
                           '风口'

            // 颜色信息
            if (item.color) {
              color = item.color
            } else if (item.productType === 'rectangular' || item.productType === 'black_white_outlet' || item.productType === 'black_white_return') {
              color = '黑白双色'
            }

            const projectData = [
              globalRowIndex,
              itemIndex === 0 ? projectAddress : '', // 只在第一行显示项目地址
              typeName, // 风口类型
              originalInfo, // 尺寸信息
              item.quantity || 1,
              safeNumber(item.unitPrice || 0).toFixed(1),
              safeNumber(item.totalPrice || 0).toFixed(1)
            ]

            for (let col = 0; col < projectData.length; col++) {
              const cell = projectWorksheet.getCell(currentRow, col + 1)
              cell.value = projectData[col]

              // 设置数据行样式
              const isEvenRow = (globalRowIndex - 1) % 2 === 0
              const fillColor = isEvenRow ? 'FFF8F9FA' : 'FFFFFFFF'

              // 根据列设置不同的对齐方式
              let alignment: 'center' | 'left' | 'right' = 'center'
              if (col === 1 || col === 9 || col === 10) { // 项目地址、备注、客户信息左对齐
                alignment = 'left'
              } else if (col >= 3 && col <= 8) { // 数字列右对齐
                alignment = 'right'
              }

              cell.style = {
                font: { size: 11 },
                fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
                alignment: { horizontal: alignment, vertical: 'middle' },
                border: {
                  top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                  bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                  left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                  right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
                }
              }
            }
            projectWorksheet.getRow(currentRow).height = 22
            currentRow++
            globalRowIndex++
          }

          // 记录需要合并的单元格范围
          if (order.items.length > 1) {
            // 合并项目地址列
            mergeRanges.push(`B${startRow}:B${currentRow - 1}`)
            // 合并客户信息列
            mergeRanges.push(`K${startRow}:K${currentRow - 1}`)
          }
        }
      }

      // 执行单元格合并
      for (const range of mergeRanges) {
        try {
          projectWorksheet.mergeCells(range)
        } catch (error) {
          console.warn('合并单元格失败:', range, error)
        }
      }

      // 4. 创建订单汇总工作表
      const orderWorksheet = workbook.addWorksheet('订单汇总')

      // 设置列宽
      orderWorksheet.columns = [
        { width: 20 }, // 订单号
        { width: 15 }, // 客户姓名
        { width: 15 }, // 联系电话
        { width: 20 }, // 项目地址
        { width: 12 }, // 订单日期
        { width: 15 }, // 订单金额
        { width: 15 }, // 已付款
        { width: 15 }, // 待付款
        { width: 12 }  // 付款状态
      ]

      currentRow = 1

      // 添加主标题
      const orderTitleCell = orderWorksheet.getCell('A1')
      orderTitleCell.value = `${periodSummary.periodName}订单汇总`
      orderWorksheet.mergeCells('A1:I1')
      orderTitleCell.style = {
        font: { bold: true, size: 18, color: { argb: 'FF000000' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thick', color: { argb: 'FF00B050' } },
          bottom: { style: 'thick', color: { argb: 'FF00B050' } },
          left: { style: 'thick', color: { argb: 'FF00B050' } },
          right: { style: 'thick', color: { argb: 'FF00B050' } }
        }
      }
      orderWorksheet.getRow(1).height = 35
      currentRow++

      // 添加信息行
      const orderInfoRows = [
        ['导出时间', new Date().toLocaleString('zh-CN')],
        ['导出范围', selectedClients.size > 0 ? `选中客户的${selectedOrders.length}个订单` : '全部订单']
      ]

      for (const [label, value] of orderInfoRows) {
        const labelCell = orderWorksheet.getCell(`A${currentRow}`)
        const valueCell = orderWorksheet.getCell(`B${currentRow}`)
        labelCell.value = label
        valueCell.value = value

        // 设置信息行样式
        for (let col = 1; col <= 9; col++) {
          const cell = orderWorksheet.getCell(currentRow, col)
          cell.style = {
            font: { size: 11, color: { argb: 'FF666666' } },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } },
            alignment: { horizontal: 'left', vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
            }
          }
        }
        orderWorksheet.getRow(currentRow).height = 22
        currentRow++
      }

      // 添加空行
      currentRow++

      // 添加表头
      const orderHeaders = ['订单号', '客户姓名', '联系电话', '项目地址', '订单日期', '订单金额(元)', '已付款(元)', '待付款(元)', '付款状态']
      for (let col = 0; col < orderHeaders.length; col++) {
        const cell = orderWorksheet.getCell(currentRow, col + 1)
        cell.value = orderHeaders[col]
        cell.style = {
          font: { bold: true, size: 12, color: { argb: 'FFFFFFFF' } },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
          alignment: { horizontal: 'center', vertical: 'middle' },
          border: {
            top: { style: 'medium', color: { argb: 'FF4472C4' } },
            bottom: { style: 'medium', color: { argb: 'FF4472C4' } },
            left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
            right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
          }
        }
      }
      orderWorksheet.getRow(currentRow).height = 25
      currentRow++

      // 添加订单数据行
      for (let i = 0; i < selectedOrders.length; i++) {
        const order = selectedOrders[i]
        const client = selectedClientSummaries.find((c: unknown) => c.clientId === order.clientId)
        const totalAmount = safeNumber(order.totalAmount)
        const paidAmount = safeNumber(order.paidAmount || 0)
        const unpaidAmount = totalAmount - paidAmount

        let paymentStatus = '未付款'
        if (paidAmount >= totalAmount) {
          paymentStatus = '已付清'
        } else if (paidAmount > 0) {
          paymentStatus = '部分付款'
        }

        const orderData = [
          order.orderNumber || order.id,
          client?.clientName || '未知客户',
          client?.clientPhone || '无',
          order.projectAddress || '未指定',
          new Date(order.createdAt).toLocaleDateString('zh-CN'),
          totalAmount.toFixed(1),
          paidAmount.toFixed(1),
          unpaidAmount.toFixed(1),
          paymentStatus
        ]

        for (let col = 0; col < orderData.length; col++) {
          const cell = orderWorksheet.getCell(currentRow, col + 1)
          cell.value = orderData[col]

          // 设置数据行样式
          const isEvenRow = i % 2 === 0
          const fillColor = isEvenRow ? 'FFF8F9FA' : 'FFFFFFFF'

          // 根据列设置不同的对齐方式
          let alignment: 'center' | 'left' | 'right' = 'center'
          if (col === 0 || col === 1 || col === 2 || col === 3 || col === 8) { // 订单号、客户、电话、地址、状态居中
            alignment = 'center'
          } else if (col === 4) { // 日期居中
            alignment = 'center'
          } else if (col >= 5 && col <= 7) { // 金额列右对齐
            alignment = 'right'
          }

          cell.style = {
            font: { size: 11 },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
            alignment: { horizontal: alignment, vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
              right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
            }
          }
        }
        orderWorksheet.getRow(currentRow).height = 22
        currentRow++
      }

      // 3. 创建项目明细工作表
      const detailWorksheet = workbook.addWorksheet('项目明细')

      // 设置列宽
      detailWorksheet.columns = [
        { width: 8 },   // A: 序号
        { width: 25 },  // B: 项目地址
        { width: 16 },  // C: 原始信息
        { width: 8 },   // D: 数量
        { width: 10 },  // E: 长度
        { width: 10 },  // F: 宽度
        { width: 10 },  // G: 颜色
        { width: 12 },  // H: 单价
        { width: 12 },  // I: 金额
        { width: 20 },  // J: 备注
        { width: 20 }   // K: 客户信息
      ]

      let projectCurrentRow = 1

      // 添加项目明细标题
      const detailTitleCell = detailWorksheet.getCell('A1')
      detailTitleCell.value = `${periodSummary.periodName}项目详细明细`
      detailWorksheet.mergeCells('A1:K1')
      detailTitleCell.style = {
        font: { bold: true, size: 18, color: { argb: 'FF000000' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thick', color: { argb: 'FF00B050' } },
          bottom: { style: 'thick', color: { argb: 'FF00B050' } },
          left: { style: 'thick', color: { argb: 'FF00B050' } },
          right: { style: 'thick', color: { argb: 'FF00B050' } }
        }
      }
      detailWorksheet.getRow(1).height = 35
      projectCurrentRow++

      // 添加项目明细信息行
      const detailInfoRows = [
        ['导出时间', new Date().toLocaleString('zh-CN')],
        ['导出范围', selectedClients.size > 0 ? `选中的${selectedClientSummaries.length}个客户` : '全部客户']
      ]

      for (const [label, value] of detailInfoRows) {
        const labelCell = detailWorksheet.getCell(`A${projectCurrentRow}`)
        const valueCell = detailWorksheet.getCell(`B${projectCurrentRow}`)
        labelCell.value = label
        valueCell.value = value

        // 合并信息行
        detailWorksheet.mergeCells(`B${projectCurrentRow}:K${projectCurrentRow}`)

        // 设置信息行样式
        for (let col = 1; col <= 11; col++) {
          const cell = detailWorksheet.getCell(projectCurrentRow, col)
          cell.style = {
            font: { size: 11, color: { argb: 'FF666666' } },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } },
            alignment: { horizontal: 'left', vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
            }
          }
        }
        detailWorksheet.getRow(projectCurrentRow).height = 22
        projectCurrentRow++
      }

      // 添加空行
      projectCurrentRow++

      // 添加项目明细表头
      const detailHeaders = ['序号', '项目地址', '原始信息/mm', '数量', '长度/mm', '宽度/mm', '颜色', '单价/元', '金额/元', '备注', '客户信息']
      const detailHeaderRow = detailWorksheet.getRow(projectCurrentRow)

      detailHeaders.forEach((header, index) => {
        const cell = detailHeaderRow.getCell(index + 1)
        cell.value = header
        cell.style = {
          font: { bold: true, size: 12, color: { argb: 'FFFFFFFF' } },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
          alignment: { horizontal: 'center', vertical: 'middle' },
          border: {
            top: { style: 'medium', color: { argb: 'FF4472C4' } },
            bottom: { style: 'medium', color: { argb: 'FF4472C4' } },
            left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
            right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
          }
        }
      })
      detailHeaderRow.height = 25
      projectCurrentRow++

      // 添加项目明细数据行
      let detailRowIndex = 1
      const detailGroups = new Map()

      for (const order of selectedOrders) {
        const client = selectedClientSummaries.find((c: unknown) => c.clientId === order.clientId)
        const clientInfo = `${client?.clientName || '未知客户'} (${client?.clientPhone || '无'})`
        const projectAddress = order.projectAddress || '未指定项目地址'
        const projectKey = `${order.clientId}-${projectAddress}`

        if (!order.items || order.items.length === 0) {
          // 无明细订单
          const rowData = [
            detailRowIndex,
            projectAddress,
            '无明细',
            1,
            '',
            '',
            '象牙白',
            safeNumber(order.totalAmount).toFixed(1),
            safeNumber(order.totalAmount).toFixed(1),
            `订单号: ${order.orderNumber || order.id}${order.notes ? ` | ${order.notes}` : ''}`,
            clientInfo
          ]

          const dataRow = detailWorksheet.getRow(projectCurrentRow)
          rowData.forEach((value, index) => {
            const cell = dataRow.getCell(index + 1)
            cell.value = value

            // 设置数据行样式
            const isEvenRow = (projectCurrentRow - 5) % 2 === 0
            const fillColor = isEvenRow ? 'FFF8F9FA' : 'FFFFFFFF'

            let alignment: 'center' | 'left' | 'right' = 'center'
            if (index === 1) alignment = 'center'  // 项目地址
            else if (index === 2 || index === 6 || index === 9) alignment = 'left'  // 规格、颜色、备注
            else if (index === 7 || index === 8) alignment = 'right'  // 单价、金额
            else if (index === 10) alignment = 'left'  // 客户信息

            cell.style = {
              font: { size: 11 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
              alignment: { horizontal: alignment, vertical: 'middle' },
              border: {
                top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
              }
            }
          })

          dataRow.height = 22
          detailGroups.set(projectKey, { startRow: projectCurrentRow, endRow: projectCurrentRow })
          projectCurrentRow++
          detailRowIndex++
        } else {
          // 有明细的订单
          const itemStartRow = projectCurrentRow

          order.items.forEach((item: unknown, itemIndex: number) => {
            // 使用统一的尺寸提取函数
            const { originalInfo, length, width, color } = extractDimensionsFromItem(item)

            // 使用统一的产品类型识别函数，确保总是有有意义的类型名称
            const typeName = getProductTypeName(item.productType) ||
                           item.productName ||
                           item.productType ||
                           '风口'

            if (item.color) {
              color = item.color
            } else if (item.productType === 'rectangular' || item.productType === 'black_white_outlet' || item.productType === 'black_white_return') {
              color = '黑白双色'
            }

            const notes = `${typeName}${item.notes ? ` | ${item.notes}` : ''}${itemIndex === 0 ? ` | 订单号: ${order.orderNumber || order.id}` : ''}`

            const rowData = [
              detailRowIndex,
              itemIndex === 0 ? projectAddress : '', // 只在第一行显示项目地址
              typeName, // 风口类型
              originalInfo, // 尺寸信息
              item.quantity || 1,
              safeNumber(item.unitPrice || 0).toFixed(1),
              safeNumber(item.totalPrice || 0).toFixed(1)
            ]

            const dataRow = detailWorksheet.getRow(projectCurrentRow)
            rowData.forEach((value, index) => {
              const cell = dataRow.getCell(index + 1)
              cell.value = value

              // 设置数据行样式
              const isEvenRow = (projectCurrentRow - 5) % 2 === 0
              const fillColor = isEvenRow ? 'FFF8F9FA' : 'FFFFFFFF'

              let alignment: 'center' | 'left' | 'right' = 'center'
              if (index === 1) alignment = 'center'  // 项目地址
              else if (index === 2 || index === 6 || index === 9) alignment = 'left'  // 规格、颜色、备注
              else if (index === 7 || index === 8) alignment = 'right'  // 单价、金额
              else if (index === 10) alignment = 'left'  // 客户信息

              cell.style = {
                font: { size: 11 },
                fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
                alignment: { horizontal: alignment, vertical: 'middle' },
                border: {
                  top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                  bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                  left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                  right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
                }
              }
            })

            dataRow.height = 22
            projectCurrentRow++
            detailRowIndex++
          })

          // 记录项目分组信息
          detailGroups.set(projectKey, { startRow: itemStartRow, endRow: projectCurrentRow - 1 })
        }
      }

      // 设置项目明细合并单元格
      for (const [projectKey, group] of detailGroups) {
        if (group.endRow > group.startRow) {
          // 合并项目地址列
          detailWorksheet.mergeCells(`B${group.startRow}:B${group.endRow}`)
          // 合并客户信息列
          detailWorksheet.mergeCells(`K${group.startRow}:K${group.endRow}`)

          // 设置合并单元格的对齐方式
          const projectCell = detailWorksheet.getCell(`B${group.startRow}`)
          const clientCell = detailWorksheet.getCell(`K${group.startRow}`)

          projectCell.alignment = { horizontal: 'center', vertical: 'middle' }
          clientCell.alignment = { horizontal: 'left', vertical: 'middle' }
        }
      }

      // 导出Excel文件
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

      const fileNameSuffix = selectedClients.size > 0 ? `_选中${selectedClientSummaries.length}客户` : '_全部客户'
      const fileName = `${periodSummary.periodName}综合账单报告${fileNameSuffix}_${new Date().toISOString().split('T')[0]}.xlsx`

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      console.log('✅ 综合账单报告Excel导出成功')
      const successMessage = selectedClients.size > 0
        ? `综合账单报告导出成功！已导出${selectedClientSummaries.length}个选中客户的汇总数据和项目明细。`
        : '综合账单报告导出成功！已导出全部客户的汇总数据和项目明细。'
      alert(successMessage)

    } catch (error) {
      console.error('❌ 导出阶段采购单汇总Excel失败:', error)
      alert('导出失败，请重试')
    }
  }

  // 导出账单数据为Excel
  const handleExportBills = () => {
    try {
      // 准备Excel数据
      const excelData = [
        // 标题行
        ['客户姓名', '联系电话', '订单数量', '订单总额(元)', '已付款(元)', '待付款(元)', '付款状态', '最后订单日期', '最后付款日期'],
        // 数据行
        ...filteredBills.map(bill => [
          bill.clientName,
          bill.clientPhone,
          bill.totalOrders,
          safeNumber(bill.totalAmount),
          safeNumber(bill.paidAmount),
          safeNumber(bill.unpaidAmount),
          getPaymentStatusInfo(bill.paymentStatus).text,
          bill.lastOrderDate ? new Date(bill.lastOrderDate).toLocaleDateString('zh-CN') : '无',
          bill.lastPaymentDate ? new Date(bill.lastPaymentDate).toLocaleDateString('zh-CN') : '无'
        ])
      ]

      // 创建工作簿和工作表
      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.aoa_to_sheet(excelData)

      // 设置列宽
      ws['!cols'] = [
        { wch: 12 }, // 客户姓名
        { wch: 15 }, // 联系电话
        { wch: 10 }, // 订单数量
        { wch: 15 }, // 订单总额
        { wch: 15 }, // 已付款
        { wch: 15 }, // 待付款
        { wch: 12 }, // 付款状态
        { wch: 15 }, // 最后订单日期
        { wch: 15 }  // 最后付款日期
      ]

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '客户账单')

      // 添加统计汇总工作表
      const summaryData = [
        ['统计项目', '数值'],
        ['客户总数', stats.totalClients],
        ['订单总金额(元)', safeNumber(stats.totalAmount)],
        ['已收款金额(元)', safeNumber(stats.paidAmount)],
        ['待收款金额(元)', safeNumber(stats.unpaidAmount)],
        ['逾期欠款(元)', safeNumber(stats.overdueAmount)],
        ['收款率(%)', safeNumber(stats.totalAmount) > 0 ? ((safeNumber(stats.paidAmount) / safeNumber(stats.totalAmount)) * 100).toFixed(1) : '0.0'],
        ['导出时间', new Date().toLocaleString('zh-CN')]
      ]
      const summaryWs = XLSX.utils.aoa_to_sheet(summaryData)
      summaryWs['!cols'] = [{ wch: 20 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(wb, summaryWs, '统计汇总')

      // 导出Excel文件
      const fileName = `客户账单_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(wb, fileName)

      console.log('✅ 账单数据导出成功 (Excel格式)')
    } catch (error) {
      console.error('❌ 导出账单数据失败:', error)
      alert('导出失败，请重试')
    }
  }

  // 生成财务报表为Excel
  const handleGenerateReport = () => {
    try {
      // 创建工作簿
      const wb = XLSX.utils.book_new()

      // 财务概况工作表
      const financialData = [
        ['财务报表', ''],
        ['生成时间', new Date().toLocaleString('zh-CN')],
        ['工厂编号', factoryId],
        ['统计周期', '全部时间'],
        ['', ''],
        ['财务概况', ''],
        ['项目', '数值'],
        ['客户总数', `${stats.totalClients}`],
        ['订单总金额', safeAmount(stats.totalAmount)],
        ['已收款金额', safeAmount(stats.paidAmount)],
        ['待收款金额', safeAmount(stats.unpaidAmount)],
        ['逾期欠款', safeAmount(stats.overdueAmount)],
        ['收款率', `${safeNumber(stats.totalAmount) > 0 ? ((safeNumber(stats.paidAmount) / safeNumber(stats.totalAmount)) * 100).toFixed(1) : '0.0'}%`],
        ['', ''],
        ['客户分析', ''],
        ['付款状态', '客户数量'],
        ['已付清', `${bills.filter(b => b.paymentStatus === 'paid').length}`],
        ['部分付款', `${bills.filter(b => b.paymentStatus === 'partial').length}`],
        ['未付款', `${bills.filter(b => b.paymentStatus === 'unpaid').length}`],
        ['逾期', `${bills.filter(b => b.paymentStatus === 'overdue').length}`]
      ]

      const financialWs = XLSX.utils.aoa_to_sheet(financialData)
      financialWs['!cols'] = [
        { wch: 20 }, // 项目列
        { wch: 25 }  // 数值列
      ]
      XLSX.utils.book_append_sheet(wb, financialWs, '财务报表')

      // 客户账单明细工作表
      const billsDetailData = [
        ['客户账单明细', ''],
        ['导出时间', new Date().toLocaleString('zh-CN')],
        ['', ''],
        ['客户姓名', '联系电话', '订单数量', '订单总额(元)', '已付款(元)', '待付款(元)', '付款状态', '最后订单日期', '最后付款日期'],
        ...filteredBills.map(bill => [
          bill.clientName,
          bill.clientPhone,
          bill.totalOrders,
          safeNumber(bill.totalAmount),
          safeNumber(bill.paidAmount),
          safeNumber(bill.unpaidAmount),
          getPaymentStatusInfo(bill.paymentStatus).text,
          bill.lastOrderDate ? new Date(bill.lastOrderDate).toLocaleDateString('zh-CN') : '无',
          bill.lastPaymentDate ? new Date(bill.lastPaymentDate).toLocaleDateString('zh-CN') : '无'
        ])
      ]

      const billsDetailWs = XLSX.utils.aoa_to_sheet(billsDetailData)
      billsDetailWs['!cols'] = [
        { wch: 12 }, // 客户姓名
        { wch: 15 }, // 联系电话
        { wch: 10 }, // 订单数量
        { wch: 15 }, // 订单总额
        { wch: 15 }, // 已付款
        { wch: 15 }, // 待付款
        { wch: 12 }, // 付款状态
        { wch: 15 }, // 最后订单日期
        { wch: 15 }  // 最后付款日期
      ]
      XLSX.utils.book_append_sheet(wb, billsDetailWs, '客户账单明细')

      // 导出Excel文件
      const fileName = `财务报表_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(wb, fileName)

      console.log('✅ 财务报表生成成功 (Excel格式)')
    } catch (error) {
      console.error('❌ 生成财务报表失败:', error)
      alert('生成报表失败，请重试')
    }
  }

  // 查看客户详情
  const handleViewDetails = (bill: BillSummary) => {
    setSelectedClient(bill)
    setShowDetailDialog(true)
  }

  // 记录付款
  const handleRecordPayment = (bill: BillSummary) => {
    setSelectedClient(bill)
    setPaymentAmount(0)
    setPaymentNotes("")
    setPaymentError("")
    setPaymentSuccess("")
    setShowPaymentDialog(true)
  }

  // 🆕 查看待收款详情
  const handleViewUnpaidDetails = async () => {
    if (!factoryId) return

    try {
      setLoadingUnpaidOrders(true)
      setShowUnpaidDialog(true)
      console.log('🔍 开始加载待收款订单详情...')

      // 获取所有订单
      const allOrders = await db.getOrdersByFactoryId(factoryId)

      // 获取所有客户信息
      const allClients = await db.getClientsByFactoryId(factoryId)
      const clientMap = new Map(allClients.map(client => [client.id, client]))

      // 筛选出有待付款的订单
      const unpaidOrdersList = allOrders
        .filter(order => {
          const totalAmount = safeNumber(order.totalAmount)
          const paidAmount = safeNumber(order.paidAmount || 0)
          return totalAmount > paidAmount // 有未付款金额
        })
        .map(order => {
          const client = clientMap.get(order.clientId)
          const totalAmount = safeNumber(order.totalAmount)
          const paidAmount = safeNumber(order.paidAmount || 0)
          const unpaidAmount = totalAmount - paidAmount

          return {
            ...order,
            clientName: client?.name || '未知客户',
            clientPhone: client?.phone || '无',
            totalAmount,
            paidAmount,
            unpaidAmount,
            orderDate: new Date(order.createdAt).toLocaleDateString('zh-CN'),
            daysSinceOrder: Math.floor((Date.now() - new Date(order.createdAt).getTime()) / (1000 * 60 * 60 * 24))
          }
        })
        .sort((a, b) => {
          // 按客户名称分组，然后按订单日期排序
          if (a.clientName !== b.clientName) {
            return a.clientName.localeCompare(b.clientName)
          }
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        })

      setUnpaidOrders(unpaidOrdersList)

      console.log(`✅ 加载到 ${unpaidOrdersList.length} 个待收款订单`)
      console.log('📋 待收款订单详情:', unpaidOrdersList.map(order => ({
        客户: order.clientName,
        订单号: order.orderNumber,
        订单金额: order.totalAmount,
        已付款: order.paidAmount,
        待付款: order.unpaidAmount,
        订单日期: order.orderDate
      })))

    } catch (error) {
      console.error('❌ 加载待收款订单详情失败:', error)
    } finally {
      setLoadingUnpaidOrders(false)
    }
  }

  // 修复数据同步问题
  const handleFixDataSync = async () => {
    if (!factoryId) return

    try {
      setSyncingData(true)
      setSyncResult("")

      console.log('🔧 开始修复数据同步问题...')

      // 调用数据同步修复功能
      const result = await db.fixDataSyncIssues(factoryId)

      if (result.success) {
        setSyncResult(`✅ ${result.message}`)
        console.log('✅ 数据同步修复成功:', result.details)

        // 重新加载账单数据
        await loadBillsData()
      } else {
        setSyncResult(`❌ ${result.message}`)
        console.error('❌ 数据同步修复失败:', result.details)
      }

      // 5秒后清除结果消息
      setTimeout(() => {
        setSyncResult("")
      }, 5000)

    } catch (error) {
      console.error('❌ 修复数据同步问题失败:', error)
      setSyncResult(`❌ 修复失败: ${error instanceof Error ? error.message : '未知错误'}`)

      setTimeout(() => {
        setSyncResult("")
      }, 5000)
    } finally {
      setSyncingData(false)
    }
  }

  // 处理付款提交
  const handlePaymentSubmit = async () => {
    if (!selectedClient || paymentAmount <= 0) {
      setPaymentError("请输入有效的付款金额")
      return
    }

    if (paymentAmount > selectedClient.unpaidAmount) {
      setPaymentError("付款金额不能超过待付款金额")
      return
    }

    try {
      setPaymentLoading(true)
      setPaymentError("")

      console.log('💰 开始处理付款:', {
        客户: selectedClient.clientName,
        付款金额: paymentAmount,
        待付款金额: selectedClient.unpaidAmount
      })

      // 获取客户的所有订单
      const clientOrders = await db.getOrdersByClientId(selectedClient.clientId)

      // 智能分配付款到订单（优先付早期订单）
      const unpaidOrders = clientOrders
        .filter(order => (Number(order.totalAmount) - Number(order.paidAmount || 0)) > 0)
        .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())

      let remainingAmount = paymentAmount
      const allocations: { orderId: string, amount: number }[] = []

      for (const order of unpaidOrders) {
        if (remainingAmount <= 0) break

        const unpaidAmount = Number(order.totalAmount) - Number(order.paidAmount || 0)
        const allocationAmount = Math.min(remainingAmount, unpaidAmount)

        allocations.push({
          orderId: order.id,
          amount: allocationAmount
        })

        remainingAmount -= allocationAmount
      }

      console.log('📋 付款分配方案:', allocations)

      // 更新每个订单的付款状态
      for (const allocation of allocations) {
        const order = clientOrders.find(o => o.id === allocation.orderId)
        if (order) {
          // 🔧 修复：使用 safeNumber 确保数字类型正确
          const currentPaidAmount = safeNumber(order.paidAmount || 0)
          const allocationAmount = safeNumber(allocation.amount)
          const newPaidAmount = currentPaidAmount + allocationAmount
          const orderTotalAmount = safeNumber(order.totalAmount)
          // 🔧 修复：使用精度处理避免浮点数比较问题
          const paymentStatus = (Math.abs(newPaidAmount - orderTotalAmount) < 0.01 || newPaidAmount >= orderTotalAmount) ? 'paid' : 'partial'

          console.log(`🔄 更新订单 ${order.id} 付款状态:`, {
            原已付款: currentPaidAmount,
            新增付款: allocationAmount,
            新已付款: newPaidAmount,
            订单总额: orderTotalAmount,
            付款状态: paymentStatus,
            数据类型检查: {
              原已付款类型: typeof order.paidAmount,
              原已付款值: order.paidAmount,
              新增付款类型: typeof allocation.amount,
              新增付款值: allocation.amount,
              计算结果类型: typeof newPaidAmount,
              计算结果值: newPaidAmount
            }
          })

          await db.updateOrderPayment(order.id, {
            paymentStatus,
            paidAmount: newPaidAmount,
            paymentNotes: paymentNotes || `客户付款 ${safeAmount(allocationAmount)}`
          })
        }
      }

      console.log('✅ 所有订单付款状态更新完成')

      // 更新客户统计信息
      console.log('🔄 开始更新客户统计信息...')
      await db.updateClientStatistics(selectedClient.clientId)
      console.log('✅ 客户统计信息更新完成')

      // 显示成功消息
      setPaymentSuccess(`付款 ${safeAmount(paymentAmount)} 记录成功！`)

      // 等待一小段时间确保数据库操作完成
      await new Promise(resolve => setTimeout(resolve, 500))

      // 重新加载账单数据
      console.log('🔄 重新加载账单数据...')
      await loadBillsData()
      console.log('✅ 账单数据重新加载完成')

      // 🔧 修复：等待状态更新后，从最新的账单数据中获取更新后的客户信息
      if (selectedClient) {
        // 等待一小段时间确保状态更新完成
        await new Promise(resolve => setTimeout(resolve, 100))

        // 重新获取最新的账单数据来更新客户详情
        if (!factoryId) return
        const clients = await db.getClientsByFactoryId(factoryId)
        const orders = await db.getOrdersByFactoryId(factoryId)

        const clientOrders = orders.filter(order => order.clientId === selectedClient.clientId)
        const clientTotalAmount = safeNumber(clientOrders.reduce((sum, order) => sum + safeNumber(order.totalAmount), 0))
        const clientPaidAmount = safeNumber(clientOrders.reduce((sum, order) => sum + safeNumber(order.paidAmount || 0), 0))
        const clientUnpaidAmount = safeNumber(clientTotalAmount - clientPaidAmount)

        // 实时计算付款状态 - 🔧 修复：使用精度处理避免浮点数比较问题
        let paymentStatus: 'paid' | 'partial' | 'unpaid' = 'unpaid'
        if (Math.abs(clientPaidAmount - clientTotalAmount) < 0.01 || clientPaidAmount >= clientTotalAmount) {
          paymentStatus = 'paid'
        } else if (clientPaidAmount > 0) {
          paymentStatus = 'partial'
        }

        const updatedClientBill: BillSummary = {
          ...selectedClient,
          totalAmount: clientTotalAmount,
          paidAmount: clientPaidAmount,
          unpaidAmount: clientUnpaidAmount,
          paymentStatus
        }

        setSelectedClient(updatedClientBill)
        console.log('✅ 客户详情数据已更新:', {
          客户: updatedClientBill.clientName,
          新已付款: updatedClientBill.paidAmount,
          新待付款: updatedClientBill.unpaidAmount,
          新付款状态: updatedClientBill.paymentStatus
        })
      }

      // 延长对话框显示时间，让用户看到数据更新
      setTimeout(() => {
        setShowPaymentDialog(false)
        setPaymentSuccess("")
        console.log('✅ 付款处理流程完全结束')
      }, 3000)

    } catch (error) {
      console.error('❌ 记录付款失败:', error instanceof Error ? error.message : String(error))
      setPaymentError(`付款记录失败: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setPaymentLoading(false)
    }
  }

  // 导出客户账单Excel（美化版本）
  const handlePrintBill = async (bill: BillSummary) => {
    try {
      console.log('📊 开始导出客户账单Excel:', bill.clientName)

      // 获取客户的所有订单详情
      const clientOrders = await db.getOrdersByClientId(bill.clientId)
      console.log('📋 获取到订单数量:', clientOrders.length)

      // 创建工作簿
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('客户账单明细')

      // 设置列宽
      worksheet.columns = [
        { width: 8 },   // A: 序号
        { width: 25 },  // B: 项目地址
        { width: 16 },  // C: 风口类型
        { width: 20 },  // D: 尺寸信息
        { width: 8 },   // E: 数量
        { width: 12 },  // F: 单价
        { width: 12 }   // G: 金额
      ]

      let currentRow = 1

      // 1. 添加主标题
      const titleCell = worksheet.getCell('A1')
      titleCell.value = `${bill.clientName} - 客户账单明细`
      worksheet.mergeCells('A1:G1')
      titleCell.style = {
        font: { bold: true, size: 18, color: { argb: 'FF000000' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thick', color: { argb: 'FF00B050' } },
          bottom: { style: 'thick', color: { argb: 'FF00B050' } },
          left: { style: 'thick', color: { argb: 'FF00B050' } },
          right: { style: 'thick', color: { argb: 'FF00B050' } }
        }
      }
      worksheet.getRow(1).height = 35
      currentRow++

      // 2. 添加客户信息
      const infoRows = [
        ['客户姓名', bill.clientName],
        ['联系电话', bill.clientPhone],
        ['生成时间', new Date().toLocaleString('zh-CN')]
      ]

      for (const [label, value] of infoRows) {
        const labelCell = worksheet.getCell(`A${currentRow}`)
        const valueCell = worksheet.getCell(`B${currentRow}`)
        labelCell.value = label
        valueCell.value = value

        // 合并信息行
        worksheet.mergeCells(`B${currentRow}:G${currentRow}`)

        // 设置信息行样式
        for (let col = 1; col <= 7; col++) {
          const cell = worksheet.getCell(currentRow, col)
          cell.style = {
            font: { size: 11, color: { argb: 'FF666666' } },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } },
            alignment: { horizontal: 'left', vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
            }
          }
        }
        worksheet.getRow(currentRow).height = 22
        currentRow++
      }

      // 3. 添加空行
      currentRow++

      // 4. 添加表头
      const headers = ['序号', '项目地址', '风口类型', '尺寸/mm', '数量', '单价(元)', '金额(元)']
      const headerRow = worksheet.getRow(currentRow)

      headers.forEach((header, index) => {
        const cell = headerRow.getCell(index + 1)
        cell.value = header
        cell.style = {
          font: { bold: true, size: 12, color: { argb: 'FFFFFFFF' } },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
          alignment: { horizontal: 'center', vertical: 'middle' },
          border: {
            top: { style: 'medium', color: { argb: 'FF4472C4' } },
            bottom: { style: 'medium', color: { argb: 'FF4472C4' } },
            left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
            right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
          }
        }
      })
      headerRow.height = 25
      currentRow++

      // 5. 添加项目明细数据
      let billRowIndex = 1
      const billGroups = new Map()

      for (const order of clientOrders) {
        const projectAddress = order.projectAddress || '未指定项目地址'
        const projectKey = `${order.clientId}-${projectAddress}`

        if (!order.items || order.items.length === 0) {
          // 无明细订单
          const rowData = [
            billRowIndex,
            projectAddress,
            '订单总额',
            '',
            1,
            safeNumber(order.totalAmount),
            safeNumber(order.totalAmount)
          ]

          const dataRow = worksheet.getRow(currentRow)
          rowData.forEach((value, index) => {
            const cell = dataRow.getCell(index + 1)
            cell.value = value
            cell.style = {
              font: { size: 11 },
              alignment: {
                horizontal: index === 0 || index === 4 ? 'center' : (index >= 5 ? 'right' : 'left'),
                vertical: 'middle'
              },
              border: {
                top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
                bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
                left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
                right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
              }
            }
          })
          dataRow.height = 20
          currentRow++
          billRowIndex++
        } else {
          // 有明细的订单
          const itemCount = order.items.length
          const projectStartRow = currentRow

          order.items.forEach((item, itemIndex) => {
            // 使用统一的尺寸提取函数
            const { originalInfo: sizeInfo } = extractDimensionsFromItem(item)

            // 使用统一的产品类型识别函数，确保总是有有意义的类型名称
            const typeName = getProductTypeName(item.productType) ||
                           item.productName ||
                           item.productType ||
                           '风口'

            const rowData = [
              billRowIndex,
              itemIndex === 0 ? projectAddress : '', // 只在第一行显示项目地址
              typeName,
              sizeInfo,
              item.quantity,
              safeNumber(item.unitPrice),
              safeNumber(item.totalPrice)
            ]

            const dataRow = worksheet.getRow(currentRow)
            rowData.forEach((value, index) => {
              const cell = dataRow.getCell(index + 1)
              cell.value = value
              cell.style = {
                font: { size: 11 },
                alignment: {
                  horizontal: index === 0 || index === 4 ? 'center' : (index >= 5 ? 'right' : 'left'),
                  vertical: 'middle'
                },
                border: {
                  top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
                  bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
                  left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
                  right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
                }
              }
            })
            dataRow.height = 20
            currentRow++
            billRowIndex++
          })

          // 合并项目地址单元格
          if (itemCount > 1) {
            worksheet.mergeCells(`B${projectStartRow}:B${projectStartRow + itemCount - 1}`)
            const mergedCell = worksheet.getCell(`B${projectStartRow}`)
            mergedCell.style = {
              ...mergedCell.style,
              alignment: { horizontal: 'left', vertical: 'middle' }
            }
          }
        }
      }

      // 6. 添加汇总信息
      currentRow += 2 // 空行

      const summaryStartRow = currentRow
      const summaryData = [
        ['汇总信息', ''],
        ['项目总数', `${bill.totalOrders} 个`],
        ['订单总额', `¥${safeAmountFormat(bill.totalAmount)}`],
        ['已付款金额', `¥${safeAmountFormat(bill.paidAmount)}`],
        ['待付款金额', `¥${safeAmountFormat(bill.unpaidAmount)}`]
      ]

      summaryData.forEach((row, index) => {
        const summaryRow = worksheet.getRow(currentRow)
        summaryRow.getCell(1).value = row[0]
        summaryRow.getCell(2).value = row[1]

        // 合并汇总信息行
        worksheet.mergeCells(`B${currentRow}:G${currentRow}`)

        // 设置汇总信息样式
        for (let col = 1; col <= 7; col++) {
          const cell = summaryRow.getCell(col)
          cell.style = {
            font: {
              bold: index === 0 || index === 4,
              size: index === 0 ? 14 : 12,
              color: { argb: index === 4 ? 'FFDC3545' : (index === 3 ? 'FF28A745' : 'FF000000') }
            },
            fill: {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: index === 0 ? 'FFE9ECEF' : (index === 4 ? 'FFFFF3CD' : 'FFFFFFFF') }
            },
            alignment: { horizontal: 'left', vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
              right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
            }
          }
        }
        summaryRow.height = index === 0 ? 25 : 22
        currentRow++
      })

      // 导出Excel文件
      const fileName = `客户账单_${bill.clientName}_${new Date().toISOString().split('T')[0]}.xlsx`
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.click()
      window.URL.revokeObjectURL(url)

      console.log('✅ 客户账单Excel导出成功')
      alert(`账单导出成功！文件名：${fileName}`)
    } catch (error) {
      console.error('❌ 导出客户账单失败:', error)
      alert('导出账单失败，请重试')
    }
  }

  // 🆕 导出客户采购明细
  const handleExportClientPurchaseDetail = async (bill: BillSummary) => {
    try {
      setPurchaseExportLoading(true)
      console.log('🔄 开始导出客户采购明细...', bill.clientName)

      // 获取客户的所有订单
      const clientOrders = await db.getOrdersByClientId(bill.clientId)
      console.log('📋 获取到订单数量:', clientOrders.length)

      if (clientOrders.length === 0) {
        alert('该客户暂无订单数据')
        return
      }

      // 生成标题
      const currentDate = new Date()
      const title = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月份风口采购明细`

      // 调用导出函数
      await exportPurchaseDetailExcel({
        title,
        orders: clientOrders,
        clientName: bill.clientName
      })

      console.log('✅ 客户采购明细导出成功')
    } catch (error) {
      console.error('❌ 导出客户采购明细失败:', error)
      alert('导出失败，请重试')
    } finally {
      setPurchaseExportLoading(false)
    }
  }

  // 🆕 按阶段导出采购明细
  const handleExportPeriodPurchaseDetail = async () => {
    try {
      setPurchaseExportLoading(true)
      console.log('🔄 开始导出阶段采购明细...')

      if (!factoryId) {
        alert('工厂信息不完整')
        return
      }

      // 获取订单数据
      let allOrders
      if (selectedClientForPurchase) {
        // 如果选择了特定客户，只获取该客户的订单
        allOrders = await db.getOrdersByClientId(selectedClientForPurchase.clientId)
        console.log('📋 获取到客户订单数量:', allOrders.length, '客户:', selectedClientForPurchase.clientName)
      } else {
        // 否则获取所有订单
        allOrders = await db.getOrdersByFactoryId(factoryId)
        console.log('📋 获取到订单数量:', allOrders.length)
      }

      if (allOrders.length === 0) {
        alert(selectedClientForPurchase ? '该客户暂无订单数据' : '暂无订单数据')
        return
      }

      // 根据选择的阶段筛选订单
      let filteredOrders = allOrders
      let title = ''

      if (selectedPeriod === 'month') {
        const startDate = new Date(selectedYear, selectedMonth - 1, 1)
        const endDate = new Date(selectedYear, selectedMonth, 0)
        filteredOrders = allOrders.filter(order => {
          const orderDate = new Date(order.createdAt)
          return orderDate >= startDate && orderDate <= endDate
        })
        const baseTitle = `${selectedYear}年${selectedMonth}月份风口采购明细`
        title = selectedClientForPurchase ? `${selectedClientForPurchase.clientName}_${baseTitle}` : baseTitle
      } else if (selectedPeriod === 'quarter') {
        const startMonth = (selectedQuarter - 1) * 3
        const startDate = new Date(selectedYear, startMonth, 1)
        const endDate = new Date(selectedYear, startMonth + 3, 0)
        filteredOrders = allOrders.filter(order => {
          const orderDate = new Date(order.createdAt)
          return orderDate >= startDate && orderDate <= endDate
        })
        const baseTitle = `${selectedYear}年第${selectedQuarter}季度风口采购明细`
        title = selectedClientForPurchase ? `${selectedClientForPurchase.clientName}_${baseTitle}` : baseTitle
      } else if (selectedPeriod === 'year') {
        const startDate = new Date(selectedYear, 0, 1)
        const endDate = new Date(selectedYear, 11, 31)
        filteredOrders = allOrders.filter(order => {
          const orderDate = new Date(order.createdAt)
          return orderDate >= startDate && orderDate <= endDate
        })
        const baseTitle = `${selectedYear}年度风口采购明细`
        title = selectedClientForPurchase ? `${selectedClientForPurchase.clientName}_${baseTitle}` : baseTitle
      }

      if (filteredOrders.length === 0) {
        alert('选择的阶段内暂无订单数据')
        return
      }

      // 调用导出函数
      await exportPurchaseDetailExcel({
        title,
        orders: filteredOrders,
        clientName: selectedClientForPurchase?.clientName
      })

      console.log('✅ 阶段采购明细导出成功')
      setShowPurchaseExportDialog(false)
      setSelectedClientForPurchase(null) // 清除选择的客户
    } catch (error) {
      console.error('❌ 导出阶段采购明细失败:', error)
      alert('导出失败，请重试')
    } finally {
      setPurchaseExportLoading(false)
    }
  }



  // 筛选账单
  const filteredBills = bills.filter(bill => {
    const matchesSearch = bill.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bill.clientPhone.includes(searchTerm)
    
    const matchesFilter = filterStatus === "all" || bill.paymentStatus === filterStatus
    
    return matchesSearch && matchesFilter
  })

  // 获取付款状态颜色和文本
  const getPaymentStatusInfo = (status: string) => {
    switch (status) {
      case 'paid':
        return { color: 'bg-green-100 text-green-800', text: '已付清', icon: CheckCircle }
      case 'partial':
        return { color: 'bg-yellow-100 text-yellow-800', text: '部分付款', icon: Clock }
      case 'unpaid':
        return { color: 'bg-gray-100 text-gray-800', text: '未付款', icon: AlertCircle }
      case 'overdue':
        return { color: 'bg-red-100 text-red-800', text: '逾期', icon: AlertCircle }
      default:
        return { color: 'bg-gray-100 text-gray-800', text: '未知', icon: AlertCircle }
    }
  }

  if (loading) {
    return (
      <DashboardLayout role="factory">
        <div className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">正在加载账单数据...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout role="factory">
      <div className="p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">账单管理</h1>
            <p className="text-gray-600">管理客户账单、付款记录和欠款统计</p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handlePeriodSummary}
              disabled={loadingPeriodSummary}
              className="text-blue-600 border-blue-300 hover:bg-blue-50"
            >
              {loadingPeriodSummary ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  生成中...
                </>
              ) : (
                <>
                  <TrendingUp className="h-4 w-4 mr-2" />
                  时间段汇总
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={handleFixDataSync}
              disabled={syncingData}
              className="text-orange-600 border-orange-300 hover:bg-orange-50"
            >
              {syncingData ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-600 mr-2"></div>
                  修复中...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  修复数据同步
                </>
              )}
            </Button>
            <Button variant="outline" onClick={handleExportBills}>
              <Download className="h-4 w-4 mr-2" />
              导出账单
            </Button>
            <Button variant="outline" onClick={handleGenerateReport}>
              <FileText className="h-4 w-4 mr-2" />
              生成报表
            </Button>

          </div>
        </div>

        {/* 数据同步结果提示 */}
        {syncResult && (
          <div className={`mb-6 p-4 rounded-lg border ${
            syncResult.startsWith('✅')
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          }`}>
            <div className="flex items-center space-x-2">
              <div className="font-medium">{syncResult}</div>
            </div>
          </div>
        )}

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">客户总数</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalClients}</div>
              <p className="text-xs text-muted-foreground">有订单记录的客户</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总订单金额</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{safeAmount(stats.totalAmount)}</div>
              <p className="text-xs text-muted-foreground">所有订单总额</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已收款</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{safeAmount(stats.paidAmount)}</div>
              <p className="text-xs text-muted-foreground">已收到的付款</p>
            </CardContent>
          </Card>

          <Card
            className="cursor-pointer hover:shadow-lg transition-shadow duration-200"
            onClick={handleViewUnpaidDetails}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">待收款</CardTitle>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4 text-yellow-600" />
                <Eye className="h-3 w-3 text-gray-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{safeAmount(stats.unpaidAmount)}</div>
              <p className="text-xs text-muted-foreground">点击查看详情</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">逾期欠款</CardTitle>
              <AlertCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{safeAmount(stats.overdueAmount)}</div>
              <p className="text-xs text-muted-foreground">超过30天未付款</p>
            </CardContent>
          </Card>

          <Card
            className="cursor-pointer hover:shadow-lg transition-shadow duration-200 border-blue-200 bg-blue-50"
            onClick={() => setShowPeriodDialog(true)}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-700">时间段汇总</CardTitle>
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4 text-blue-600" />
                <Eye className="h-3 w-3 text-blue-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">📊</div>
              <p className="text-xs text-blue-600">月度/季度/年度</p>
            </CardContent>
          </Card>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索客户姓名或手机号..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="all">全部状态</option>
                  <option value="paid">已付清</option>
                  <option value="partial">部分付款</option>
                  <option value="unpaid">未付款</option>
                  <option value="overdue">逾期</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 账单列表 */}
        <div className="space-y-4">
          {filteredBills.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无账单数据</h3>
                <p className="text-gray-600">
                  {searchTerm || filterStatus !== "all"
                    ? "没有找到符合条件的账单记录"
                    : "还没有客户订单，请先创建订单"}
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredBills.map((bill) => {
              const statusInfo = getPaymentStatusInfo(bill.paymentStatus)
              const StatusIcon = statusInfo.icon

              return (
                <Card key={bill.clientId} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-3">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">{bill.clientName}</h3>
                            <p className="text-sm text-gray-600">{bill.clientPhone}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            {/* 🆕 数据一致性指示器 */}
                            {bill.dataConsistent === false && (
                              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                数据需同步
                              </Badge>
                            )}
                            <Badge className={statusInfo.color}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {statusInfo.text}
                            </Badge>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">订单数量</p>
                            <p className="font-medium">{bill.totalOrders} 个</p>
                          </div>
                          <div>
                            <p className="text-gray-600">订单总额</p>
                            <p className="font-medium">{safeAmount(bill.totalAmount)}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">已付款</p>
                            <p className="font-medium text-green-600">{safeAmount(bill.paidAmount)}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">待付款</p>
                            <p className={`font-medium ${safeNumber(bill.unpaidAmount) > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                              {safeAmount(bill.unpaidAmount)}
                            </p>
                          </div>
                        </div>

                        {bill.lastOrderDate && (
                          <div className="mt-3 text-xs text-gray-500">
                            最后订单: {new Date(bill.lastOrderDate).toLocaleDateString('zh-CN')}
                            {bill.lastPaymentDate && (
                              <span className="ml-4">
                                最后付款: {new Date(bill.lastPaymentDate).toLocaleDateString('zh-CN')}
                              </span>
                            )}
                          </div>
                        )}
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center space-x-2 ml-6">
                        <Button size="sm" variant="outline" onClick={() => handleViewDetails(bill)}>
                          <Eye className="h-4 w-4 mr-1" />
                          查看详情
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handlePrintBill(bill)} className="text-blue-600 border-blue-300 hover:bg-blue-50">
                          <Download className="h-4 w-4 mr-1" />
                          导出账单
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedClientForPurchase(bill)
                            setShowPurchaseExportDialog(true)
                          }}
                          disabled={purchaseExportLoading}
                          className="text-purple-600 border-purple-300 hover:bg-purple-50"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          阶段采购明细
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleExportClientPurchaseDetail(bill)}
                          disabled={purchaseExportLoading}
                          className="text-orange-600 border-orange-300 hover:bg-orange-50"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          {purchaseExportLoading ? '导出中...' : '全部导出'}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })
          )}
        </div>

        {/* 分页 */}
        {filteredBills.length > 0 && (
          <div className="mt-8 flex justify-center">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                上一页
              </Button>
              <span className="text-sm text-gray-600">
                显示 {filteredBills.length} 条记录
              </span>
              <Button variant="outline" size="sm" disabled>
                下一页
              </Button>
            </div>
          </div>
        )}

        {/* 客户详情对话框 */}
        <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>客户账单详情</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => selectedClient && handlePrintBill(selectedClient)}
                  className="text-blue-600 border-blue-300 hover:bg-blue-50"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出账单
                </Button>
              </DialogTitle>
              <DialogDescription>
                查看客户的完整账单信息和付款记录
              </DialogDescription>
            </DialogHeader>
            {selectedClient && (
              <div className="space-y-6">
                {/* 客户基本信息 */}
                <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">客户姓名</Label>
                    <p className="text-lg font-semibold">{selectedClient.clientName}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">联系电话</Label>
                    <p className="text-lg">{selectedClient.clientPhone}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">最后订单日期</Label>
                    <p>{selectedClient.lastOrderDate ? new Date(selectedClient.lastOrderDate).toLocaleDateString('zh-CN') : '无'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">最后付款日期</Label>
                    <p>{selectedClient.lastPaymentDate ? new Date(selectedClient.lastPaymentDate).toLocaleDateString('zh-CN') : '无'}</p>
                  </div>
                </div>

                {/* 金额汇总信息 */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                    <DollarSign className="h-5 w-5 mr-2" />
                    金额汇总
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600 mb-1">项目数量</p>
                      <p className="text-2xl font-bold text-blue-600">{selectedClient.totalOrders} 个</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600 mb-1">订单总额</p>
                      <p className="text-2xl font-bold text-gray-900">{safeAmount(selectedClient.totalAmount)}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600 mb-1">已付款金额</p>
                      <p className="text-2xl font-bold text-green-600">{safeAmount(selectedClient.paidAmount)}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600 mb-1">待付款金额</p>
                      <p className="text-2xl font-bold text-red-600">{safeAmount(selectedClient.unpaidAmount)}</p>
                    </div>
                  </div>

                  {/* 付款进度条 */}
                  <div className="mt-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">付款进度</span>
                      {(() => {
                        // 🔧 修复：实时计算付款状态，确保显示准确
                        const totalAmount = selectedClient.totalAmount || 0
                        const paidAmount = selectedClient.paidAmount || 0
                        let realTimePaymentStatus = 'unpaid'

                        if (paidAmount >= totalAmount) {
                          realTimePaymentStatus = 'paid'
                        } else if (paidAmount > 0) {
                          realTimePaymentStatus = 'partial'
                        }

                        return (
                          <Badge className={getPaymentStatusInfo(realTimePaymentStatus).color}>
                            {getPaymentStatusInfo(realTimePaymentStatus).text}
                          </Badge>
                        )
                      })()}
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-green-500 h-3 rounded-full transition-all duration-300"
                        style={{
                          width: `${selectedClient.totalAmount > 0 ? (selectedClient.paidAmount / selectedClient.totalAmount) * 100 : 0}%`
                        }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>已付: {selectedClient.totalAmount > 0 ? ((selectedClient.paidAmount / selectedClient.totalAmount) * 100).toFixed(1) : '0'}%</span>
                      <span>待付: {selectedClient.totalAmount > 0 ? ((selectedClient.unpaidAmount / selectedClient.totalAmount) * 100).toFixed(1) : '0'}%</span>
                    </div>
                  </div>
                </div>

                {/* 快捷操作 */}
                {safeNumber(selectedClient.unpaidAmount) > 0 && (
                  <div className="flex justify-center">
                    <Button
                      onClick={() => handleRecordPayment(selectedClient)}
                      className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold px-8 py-3 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-2 border-green-400"
                      size="lg"
                    >
                      <DollarSign className="h-5 w-5 mr-2" />
                      <span className="text-base">记录付款 ¥{safeAmountFormat(selectedClient.unpaidAmount, 1)}</span>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* 付款记录对话框 */}
        <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>记录付款</span>
              </DialogTitle>
              <DialogDescription>
                为客户 {selectedClient?.clientName} 记录新的付款信息
              </DialogDescription>
            </DialogHeader>
            {selectedClient && (
              <div className="space-y-4">
                {/* 成功提示 */}
                {paymentSuccess && (
                  <div className="p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>{paymentSuccess}</span>
                  </div>
                )}

                {/* 错误提示 */}
                {paymentError && (
                  <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                    {paymentError}
                  </div>
                )}

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">待付款金额</span>
                    <span className="text-lg font-bold text-red-600">{safeAmount(selectedClient.unpaidAmount)}</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="paymentAmount">付款金额 *</Label>
                  <Input
                    id="paymentAmount"
                    type="number"
                    step="0.01"
                    placeholder="请输入付款金额"
                    min="0"
                    max={safeNumber(selectedClient.unpaidAmount)}
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                    disabled={paymentLoading}
                  />
                  <div className="flex gap-2 mt-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setPaymentAmount(safeNumber(selectedClient.unpaidAmount))}
                      disabled={paymentLoading}
                    >
                      全部付清
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setPaymentAmount(safeNumber(selectedClient.unpaidAmount) / 2)}
                      disabled={paymentLoading}
                    >
                      付一半
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="paymentNotes">付款备注</Label>
                  <Input
                    id="paymentNotes"
                    placeholder="请输入付款备注（可选）"
                    value={paymentNotes}
                    onChange={(e) => setPaymentNotes(e.target.value)}
                    disabled={paymentLoading}
                  />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowPaymentDialog(false)}
                    disabled={paymentLoading}
                  >
                    取消
                  </Button>
                  <Button
                    onClick={handlePaymentSubmit}
                    disabled={paymentLoading || paymentAmount <= 0}
                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-2 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-2 border-blue-400"
                    size="lg"
                  >
                    {paymentLoading ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        <span className="text-base">处理中...</span>
                      </>
                    ) : (
                      <>
                        <DollarSign className="h-5 w-5 mr-2" />
                        <span className="text-base">确认付款 {safeAmount(paymentAmount)}</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* 🆕 阶段采购单汇总对话框 */}
        <Dialog open={showPeriodDialog} onOpenChange={setShowPeriodDialog}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                <span>时间段账单汇总</span>
              </DialogTitle>
              <DialogDescription>
                选择时间段生成账单汇总报表，支持月度、季度、年度统计
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* 阶段选择 */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <Label className="text-sm font-medium">汇总类型</Label>
                  <select
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value as 'month' | 'quarter' | 'year')}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="month">月度汇总</option>
                    <option value="quarter">季度汇总</option>
                    <option value="year">年度汇总</option>
                  </select>
                </div>

                <div>
                  <Label className="text-sm font-medium">年份</Label>
                  <select
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                  >
                    {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                      <option key={year} value={year}>{year}年</option>
                    ))}
                  </select>
                </div>

                {selectedPeriod === 'month' && (
                  <div>
                    <Label className="text-sm font-medium">月份</Label>
                    <select
                      value={selectedMonth}
                      onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                        <option key={month} value={month}>{month}月</option>
                      ))}
                    </select>
                  </div>
                )}

                {selectedPeriod === 'quarter' && (
                  <div>
                    <Label className="text-sm font-medium">季度</Label>
                    <select
                      value={selectedQuarter}
                      onChange={(e) => setSelectedQuarter(parseInt(e.target.value))}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value={1}>第1季度</option>
                      <option value={2}>第2季度</option>
                      <option value={3}>第3季度</option>
                      <option value={4}>第4季度</option>
                    </select>
                  </div>
                )}

                <div className="flex items-end">
                  <Button
                    onClick={handlePeriodSummary}
                    disabled={loadingPeriodSummary}
                    className="w-full"
                  >
                    {loadingPeriodSummary ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <Search className="h-4 w-4 mr-2" />
                        生成汇总
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {/* 汇总结果显示 */}
              {periodSummary && (
                <div className="space-y-6">
                  {/* 汇总标题和导出按钮 */}
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {periodSummary.periodName}账单汇总
                    </h3>
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => {
                          setSelectedClients(new Set(periodSummary.clientSummaries.map((client: unknown) => client.clientId)))
                          setSelectAll(true)
                          setTimeout(() => handleExportComprehensiveSummary(), 100)
                        }}
                        variant="outline"
                        className="text-green-600 border-green-300 hover:bg-green-50"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        导出综合采购单报告
                      </Button>
                    </div>
                  </div>

                  {/* 统计概览 */}
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-sm text-gray-600">客户数量</p>
                        <p className="text-2xl font-bold text-blue-600">{periodSummary.totalStats.totalClients}</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-sm text-gray-600">订单数量</p>
                        <p className="text-2xl font-bold text-blue-600">{periodSummary.totalStats.totalOrders}</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-sm text-gray-600">订单总额</p>
                        <p className="text-2xl font-bold">{safeAmount(periodSummary.totalStats.totalAmount)}</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-sm text-gray-600">已收款</p>
                        <p className="text-2xl font-bold text-green-600">{safeAmount(periodSummary.totalStats.paidAmount)}</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-sm text-gray-600">待收款</p>
                        <p className="text-2xl font-bold text-red-600">{safeAmount(periodSummary.totalStats.unpaidAmount)}</p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 客户明细表格 */}
                  <div>
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-md font-medium text-gray-900">客户明细</h4>
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-600">
                          已选择 {selectedClients.size} / {periodSummary.clientSummaries.length} 个客户
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleExportComprehensiveSummary}
                          disabled={selectedClients.size === 0}
                          className="text-green-600 border-green-300 hover:bg-green-50"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          导出综合采购单报告
                        </Button>
                      </div>
                    </div>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse border border-gray-300">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border border-gray-300 px-4 py-2 text-center w-16">
                              <input
                                type="checkbox"
                                checked={selectAll}
                                onChange={(e) => handleSelectAll(e.target.checked)}
                                className="rounded border-gray-300"
                              />
                            </th>
                            <th className="border border-gray-300 px-4 py-2 text-left">客户姓名</th>
                            <th className="border border-gray-300 px-4 py-2 text-left">联系电话</th>
                            <th className="border border-gray-300 px-4 py-2 text-center">订单数量</th>
                            <th className="border border-gray-300 px-4 py-2 text-right">订单总额</th>
                            <th className="border border-gray-300 px-4 py-2 text-right">已付款</th>
                            <th className="border border-gray-300 px-4 py-2 text-right">待付款</th>
                            <th className="border border-gray-300 px-4 py-2 text-center">收款率</th>
                          </tr>
                        </thead>
                        <tbody>
                          {periodSummary.clientSummaries.map((client: unknown, index: number) => (
                            <tr key={client.clientId} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} ${selectedClients.has(client.clientId) ? 'ring-2 ring-blue-200 bg-blue-50' : ''}`}>
                              <td className="border border-gray-300 px-4 py-2 text-center">
                                <input
                                  type="checkbox"
                                  checked={selectedClients.has(client.clientId)}
                                  onChange={(e) => handleClientSelect(client.clientId, e.target.checked)}
                                  className="rounded border-gray-300"
                                />
                              </td>
                              <td className="border border-gray-300 px-4 py-2">{client.clientName}</td>
                              <td className="border border-gray-300 px-4 py-2">{client.clientPhone}</td>
                              <td className="border border-gray-300 px-4 py-2 text-center">{client.totalOrders}</td>
                              <td className="border border-gray-300 px-4 py-2 text-right">{safeAmount(client.totalAmount)}</td>
                              <td className="border border-gray-300 px-4 py-2 text-right text-green-600">{safeAmount(client.paidAmount)}</td>
                              <td className="border border-gray-300 px-4 py-2 text-right text-red-600">{safeAmount(client.unpaidAmount)}</td>
                              <td className="border border-gray-300 px-4 py-2 text-center">
                                {client.totalAmount > 0 ? ((client.paidAmount / client.totalAmount) * 100).toFixed(1) : '0.0'}%
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* 🆕 待收款详情对话框 */}
        <Dialog open={showUnpaidDialog} onOpenChange={setShowUnpaidDialog}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-yellow-600" />
                <span>待收款详情</span>
              </DialogTitle>
              <DialogDescription>
                查看所有未付款的订单详情，按客户分组显示
              </DialogDescription>
            </DialogHeader>

            {loadingUnpaidOrders ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-yellow-600" />
                  <p className="text-gray-600">正在加载待收款订单...</p>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* 统计概览 */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div>
                      <p className="text-sm text-gray-600">待收款订单</p>
                      <p className="text-xl font-bold text-yellow-600">{unpaidOrders.length} 个</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">涉及客户</p>
                      <p className="text-xl font-bold text-yellow-600">
                        {new Set(unpaidOrders.map(order => order.clientId)).size} 人
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">待收款总额</p>
                      <p className="text-xl font-bold text-yellow-600">
                        {safeAmount(unpaidOrders.reduce((sum, order) => sum + order.unpaidAmount, 0))}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">平均欠款天数</p>
                      <p className="text-xl font-bold text-yellow-600">
                        {unpaidOrders.length > 0
                          ? Math.round(unpaidOrders.reduce((sum, order) => sum + order.daysSinceOrder, 0) / unpaidOrders.length)
                          : 0
                        } 天
                      </p>
                    </div>
                  </div>
                </div>

                {/* 订单列表 */}
                {unpaidOrders.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">太棒了！</h3>
                    <p className="text-gray-600">目前没有待收款的订单</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* 按客户分组显示 */}
                    {Object.entries(
                      unpaidOrders.reduce((groups, order) => {
                        const key = order.clientName
                        if (!groups[key]) {
                          groups[key] = []
                        }
                        groups[key].push(order)
                        return groups
                      }, {} as Record<string, unknown[]>)
                    ).map(([clientName, clientOrders]) => {
                      const orders = clientOrders as unknown[]
                      return (
                      <Card key={clientName} className="border-l-4 border-l-yellow-500">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900">{clientName}</h3>
                              <p className="text-sm text-gray-600">{orders[0].clientPhone}</p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-gray-600">客户待付款总额</p>
                              <p className="text-lg font-bold text-red-600">
                                {safeAmount(orders.reduce((sum, order) => sum + order.unpaidAmount, 0))}
                              </p>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {orders.map((order) => (
                              <div key={order.id} className="bg-gray-50 rounded-lg p-4">
                                <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-sm">
                                  <div>
                                    <p className="text-gray-600">订单号</p>
                                    <p className="font-medium">{order.orderNumber}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-600">订单日期</p>
                                    <p className="font-medium">{order.orderDate}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-600">订单金额</p>
                                    <p className="font-medium">{safeAmount(order.totalAmount)}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-600">已付款</p>
                                    <p className="font-medium text-green-600">{safeAmount(order.paidAmount)}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-600">待付款</p>
                                    <p className="font-medium text-red-600">{safeAmount(order.unpaidAmount)}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-600">欠款天数</p>
                                    <p className={`font-medium ${order.daysSinceOrder > 30 ? 'text-red-600' : 'text-yellow-600'}`}>
                                      {order.daysSinceOrder} 天
                                    </p>
                                  </div>
                                </div>

                                {/* 付款状态标识 */}
                                <div className="mt-3 flex items-center justify-between">
                                  <Badge
                                    className={
                                      order.paymentStatus === 'overdue'
                                        ? 'bg-red-100 text-red-800'
                                        : order.paymentStatus === 'partial'
                                        ? 'bg-yellow-100 text-yellow-800'
                                        : 'bg-gray-100 text-gray-800'
                                    }
                                  >
                                    {order.paymentStatus === 'overdue' && '逾期'}
                                    {order.paymentStatus === 'partial' && '部分付款'}
                                    {order.paymentStatus === 'unpaid' && '未付款'}
                                  </Badge>

                                  {/* 快速付款按钮 */}
                                  <Button
                                    size="sm"
                                    onClick={() => {
                                      // 找到对应的客户账单
                                      const clientBill = bills.find(bill => bill.clientName === clientName)
                                      if (clientBill) {
                                        setShowUnpaidDialog(false)
                                        handleRecordPayment(clientBill)
                                      }
                                    }}
                                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium px-4 py-1 rounded-md shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 border border-blue-400"
                                  >
                                    <DollarSign className="h-3 w-3 mr-1" />
                                    <span className="text-sm">记录付款</span>
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                      )
                    })}
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* 🆕 采购明细导出对话框 */}
        <Dialog open={showPurchaseExportDialog} onOpenChange={setShowPurchaseExportDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {selectedClientForPurchase ? `${selectedClientForPurchase.clientName} - 按阶段导出采购明细` : '按阶段导出采购明细'}
              </DialogTitle>
              <DialogDescription>
                选择时间范围导出该客户的采购明细
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* 阶段选择 */}
              <div className="space-y-4">
                  {/* 阶段类型 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">阶段类型</Label>
                    <div className="flex space-x-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="period-month"
                          name="periodType"
                          value="month"
                          checked={selectedPeriod === 'month'}
                          onChange={(e) => setSelectedPeriod(e.target.value as 'month' | 'quarter' | 'year')}
                          className="w-4 h-4 text-blue-600"
                        />
                        <label htmlFor="period-month" className="text-sm">月</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="period-quarter"
                          name="periodType"
                          value="quarter"
                          checked={selectedPeriod === 'quarter'}
                          onChange={(e) => setSelectedPeriod(e.target.value as 'month' | 'quarter' | 'year')}
                          className="w-4 h-4 text-blue-600"
                        />
                        <label htmlFor="period-quarter" className="text-sm">季度</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="period-year"
                          name="periodType"
                          value="year"
                          checked={selectedPeriod === 'year'}
                          onChange={(e) => setSelectedPeriod(e.target.value as 'month' | 'quarter' | 'year')}
                          className="w-4 h-4 text-blue-600"
                        />
                        <label htmlFor="period-year" className="text-sm">年</label>
                      </div>
                    </div>
                  </div>

                  {/* 年份选择 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">年份</Label>
                    <select
                      value={selectedYear}
                      onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                        <option key={year} value={year}>{year}年</option>
                      ))}
                    </select>
                  </div>

                  {/* 月份选择 */}
                  {selectedPeriod === 'month' && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">月份</Label>
                      <select
                        value={selectedMonth}
                        onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                        className="w-full p-2 border border-gray-300 rounded-md text-sm"
                      >
                        {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                          <option key={month} value={month}>{month}月</option>
                        ))}
                      </select>
                    </div>
                  )}

                  {/* 季度选择 */}
                  {selectedPeriod === 'quarter' && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">季度</Label>
                      <select
                        value={selectedQuarter}
                        onChange={(e) => setSelectedQuarter(parseInt(e.target.value))}
                        className="w-full p-2 border border-gray-300 rounded-md text-sm"
                      >
                        <option value={1}>第1季度</option>
                        <option value={2}>第2季度</option>
                        <option value={3}>第3季度</option>
                        <option value={4}>第4季度</option>
                      </select>
                    </div>
                  )}
            </div>

              {/* 操作按钮 */}
              <div className="flex space-x-3 pt-4">
                <Button
                  onClick={handleExportPeriodPurchaseDetail}
                  disabled={purchaseExportLoading}
                  className="flex-1"
                >
                  {purchaseExportLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      导出中...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      确认导出
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowPurchaseExportDialog(false)}
                  disabled={purchaseExportLoading}
                  className="flex-1"
                >
                  取消
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
