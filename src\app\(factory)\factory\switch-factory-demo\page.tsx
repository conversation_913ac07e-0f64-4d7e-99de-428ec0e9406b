"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useRouter } from 'next/navigation'

export default function SwitchFactoryDemoPage() {
  const [switching, setSwitching] = useState(false)
  const router = useRouter()

  const factories = [
    {
      id: 'cmcp1mtn60000unrghva9oeiv',
      name: '当前工厂（空数据）',
      description: '这是您当前登录的工厂，没有客户和订单数据',
      status: 'current',
      clients: 0,
      orders: 0,
      rewards: 0
    },
    {
      id: 'cmbx0onx0000gun48lje6r02s',
      name: '演示工厂（有推荐奖励数据）',
      description: '这个工厂有完整的推荐奖励数据，可以看到推荐奖励系统的实际效果',
      status: 'demo',
      clients: 3,
      orders: 6,
      rewards: 180
    }
  ]

  const switchToFactory = async (factoryId: string) => {
    setSwitching(true)
    try {
      // 这里可以添加切换工厂的逻辑
      // 目前只是跳转到对应的页面
      if (factoryId === 'cmbx0onx0000gun48lje6r02s') {
        // 跳转到有数据的工厂演示
        window.open(`http://localhost:3000/api/read-raw-data?factoryId=${factoryId}`, '_blank')
      }
    } catch (error) {
      console.error('切换工厂失败:', error)
    } finally {
      setSwitching(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">工厂切换演示</h1>
        <p className="text-gray-600">
          您当前登录的工厂没有推荐奖励数据。为了演示推荐奖励系统的功能，您可以查看有数据的演示工厂。
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {factories.map((factory) => (
          <Card key={factory.id} className={factory.status === 'current' ? 'border-blue-500' : ''}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{factory.name}</CardTitle>
                {factory.status === 'current' && (
                  <Badge variant="default">当前</Badge>
                )}
                {factory.status === 'demo' && (
                  <Badge variant="secondary">演示</Badge>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">{factory.description}</p>
              
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{factory.clients}</div>
                  <div className="text-xs text-gray-500">客户数</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{factory.orders}</div>
                  <div className="text-xs text-gray-500">订单数</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">¥{factory.rewards}</div>
                  <div className="text-xs text-gray-500">推荐奖励</div>
                </div>
              </div>

              {factory.status === 'demo' && (
                <Button 
                  onClick={() => switchToFactory(factory.id)}
                  disabled={switching}
                  className="w-full"
                >
                  {switching ? '切换中...' : '查看演示数据'}
                </Button>
              )}

              {factory.status === 'current' && (
                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-2">这是您当前的工厂</p>
                  <Button 
                    variant="outline"
                    onClick={() => router.push('/factory/clients')}
                    className="w-full"
                  >
                    管理客户
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">💡 说明</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• 推荐奖励系统已经完全修复并正常工作</li>
          <li>• 您当前的工厂是新建的，还没有客户和订单数据</li>
          <li>• 演示工厂有完整的推荐关系：张三推荐了李四和王五</li>
          <li>• 张三的推荐奖励：¥180（李四¥60 + 王五¥120）</li>
          <li>• 系统支持实时计算和按比例奖励分配</li>
        </ul>
      </div>
    </div>
  )
}
